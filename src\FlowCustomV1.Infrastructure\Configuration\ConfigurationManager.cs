using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.ComponentModel.DataAnnotations;

namespace FlowCustomV1.Infrastructure.Configuration;

/// <summary>
/// 配置管理服务
/// 提供配置验证、环境变量支持、热重载等功能
/// </summary>
public class ConfigurationManagerService : IDisposable
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<ConfigurationManagerService> _logger;
    private readonly Dictionary<string, IDisposable> _changeTokens;
    private readonly Dictionary<string, Action<object>> _changeCallbacks;
    private bool _disposed;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="configuration">配置对象</param>
    /// <param name="logger">日志记录器</param>
    public ConfigurationManagerService(IConfiguration configuration, ILogger<ConfigurationManagerService> logger)
    {
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _changeTokens = new Dictionary<string, IDisposable>();
        _changeCallbacks = new Dictionary<string, Action<object>>();
    }

    /// <summary>
    /// 获取配置值，支持环境变量覆盖
    /// </summary>
    /// <typeparam name="T">配置类型</typeparam>
    /// <param name="sectionName">配置节名称</param>
    /// <returns>配置对象</returns>
    public T GetConfiguration<T>(string sectionName) where T : class, new()
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(sectionName);

        var config = new T();
        var section = _configuration.GetSection(sectionName);
        
        if (section.Exists())
        {
            section.Bind(config);
        }

        // 应用环境变量覆盖
        ApplyEnvironmentVariableOverrides(config, sectionName);

        return config;
    }

    /// <summary>
    /// 验证配置对象
    /// </summary>
    /// <typeparam name="T">配置类型</typeparam>
    /// <param name="config">配置对象</param>
    /// <param name="sectionName">配置节名称</param>
    /// <returns>验证结果</returns>
    public ConfigurationValidationResult ValidateConfiguration<T>(T config, string sectionName) where T : class
    {
        ArgumentNullException.ThrowIfNull(config);
        ArgumentException.ThrowIfNullOrWhiteSpace(sectionName);

        var result = new ConfigurationValidationResult
        {
            SectionName = sectionName,
            IsValid = true
        };

        var validationContext = new ValidationContext(config);
        var validationResults = new List<System.ComponentModel.DataAnnotations.ValidationResult>();

        if (!Validator.TryValidateObject(config, validationContext, validationResults, true))
        {
            result.IsValid = false;
            result.Errors.AddRange(validationResults.Select(vr => vr.ErrorMessage ?? "Unknown validation error"));
            
            foreach (var validationResult in validationResults)
            {
                if (validationResult.MemberNames.Any())
                {
                    result.InvalidProperties.AddRange(validationResult.MemberNames);
                }
            }
        }

        if (!result.IsValid)
        {
            _logger.LogError("Configuration validation failed for section '{SectionName}': {Errors}", 
                sectionName, string.Join(", ", result.Errors));
        }
        else
        {
            _logger.LogDebug("Configuration validation passed for section '{SectionName}'", sectionName);
        }

        return result;
    }

    /// <summary>
    /// 注册配置变更回调
    /// </summary>
    /// <typeparam name="T">配置类型</typeparam>
    /// <param name="sectionName">配置节名称</param>
    /// <param name="callback">变更回调</param>
    public void RegisterChangeCallback<T>(string sectionName, Action<T> callback) where T : class, new()
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(sectionName);
        ArgumentNullException.ThrowIfNull(callback);

        var changeToken = _configuration.GetReloadToken();
        var disposable = changeToken.RegisterChangeCallback(state =>
        {
            try
            {
                var newConfig = GetConfiguration<T>(sectionName);
                var validationResult = ValidateConfiguration(newConfig, sectionName);
                
                if (validationResult.IsValid)
                {
                    callback(newConfig);
                    _logger.LogInformation("Configuration reloaded for section '{SectionName}'", sectionName);
                }
                else
                {
                    _logger.LogWarning("Configuration reload failed validation for section '{SectionName}': {Errors}", 
                        sectionName, string.Join(", ", validationResult.Errors));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during configuration reload for section '{SectionName}'", sectionName);
            }
        }, null);

        _changeTokens[sectionName] = disposable;
        _changeCallbacks[sectionName] = obj => callback((T)obj);
    }

    /// <summary>
    /// 应用环境变量覆盖
    /// </summary>
    /// <typeparam name="T">配置类型</typeparam>
    /// <param name="config">配置对象</param>
    /// <param name="sectionName">配置节名称</param>
    private void ApplyEnvironmentVariableOverrides<T>(T config, string sectionName) where T : class
    {
        var properties = typeof(T).GetProperties();
        
        foreach (var property in properties)
        {
            if (!property.CanWrite) continue;

            // 构建环境变量名：FLOWCUSTOM_{SECTION}_{PROPERTY}
            var envVarName = $"FLOWCUSTOM_{sectionName.ToUpperInvariant()}_{property.Name.ToUpperInvariant()}";
            var envValue = Environment.GetEnvironmentVariable(envVarName);
            
            if (!string.IsNullOrEmpty(envValue))
            {
                try
                {
                    var convertedValue = ConvertEnvironmentValue(envValue, property.PropertyType);
                    property.SetValue(config, convertedValue);
                    
                    _logger.LogDebug("Applied environment variable override: {EnvVar} = {Value}", 
                        envVarName, envValue);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to apply environment variable override: {EnvVar} = {Value}", 
                        envVarName, envValue);
                }
            }
        }
    }

    /// <summary>
    /// 转换环境变量值到目标类型
    /// </summary>
    /// <param name="value">环境变量值</param>
    /// <param name="targetType">目标类型</param>
    /// <returns>转换后的值</returns>
    private object? ConvertEnvironmentValue(string value, Type targetType)
    {
        if (targetType == typeof(string))
            return value;

        if (targetType == typeof(bool) || targetType == typeof(bool?))
            return bool.Parse(value);

        if (targetType == typeof(int) || targetType == typeof(int?))
            return int.Parse(value);

        if (targetType == typeof(long) || targetType == typeof(long?))
            return long.Parse(value);

        if (targetType == typeof(double) || targetType == typeof(double?))
            return double.Parse(value);

        if (targetType == typeof(decimal) || targetType == typeof(decimal?))
            return decimal.Parse(value);

        if (targetType.IsEnum)
            return Enum.Parse(targetType, value, true);

        if (targetType == typeof(List<string>))
            return value.Split(',', StringSplitOptions.RemoveEmptyEntries).ToList();

        // 对于复杂类型，尝试JSON反序列化
        if (targetType.IsClass && targetType != typeof(string))
        {
            return System.Text.Json.JsonSerializer.Deserialize(value, targetType);
        }

        throw new NotSupportedException($"Environment variable conversion not supported for type: {targetType}");
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (_disposed) return;

        foreach (var token in _changeTokens.Values)
        {
            token?.Dispose();
        }
        
        _changeTokens.Clear();
        _changeCallbacks.Clear();
        _disposed = true;
    }
}

/// <summary>
/// 配置验证结果
/// </summary>
public class ConfigurationValidationResult
{
    /// <summary>
    /// 配置节名称
    /// </summary>
    public string SectionName { get; set; } = string.Empty;

    /// <summary>
    /// 是否验证通过
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 验证错误列表
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// 无效属性列表
    /// </summary>
    public List<string> InvalidProperties { get; set; } = new();
}
