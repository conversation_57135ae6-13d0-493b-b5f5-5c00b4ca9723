# FlowCustomV1 Web Frontend 快速开始指南

## 🚀 项目启动

### 方式一：使用启动脚本（推荐）

#### Windows 环境
```powershell
# 进入项目目录
cd src/FlowCustomV1.Web

# 运行启动脚本
.\start.ps1
```

#### Linux/macOS 环境
```bash
# 进入项目目录
cd src/FlowCustomV1.Web

# 添加执行权限
chmod +x start.sh

# 运行启动脚本
./start.sh
```

### 方式二：手动启动

```bash
# 1. 安装依赖
npm install

# 2. 启动开发服务器
npm run dev
```

## 📋 环境要求

- **Node.js**: >= 16.0.0
- **npm**: >= 8.0.0
- **浏览器**: Chrome 90+, Firefox 88+, Safari 14+

## 🔧 可用脚本

```bash
# 开发模式
npm run dev

# 生产构建
npm run build

# 预览构建结果
npm run preview

# 代码检查
npm run lint

# 修复代码问题
npm run lint:fix

# 类型检查
npm run type-check
```

## 🌐 访问地址

- **开发服务器**: http://localhost:3000
- **API 代理**: http://localhost:3000/api -> http://localhost:5000/api

## 📁 项目结构说明

```
src/FlowCustomV1.Web/
├── src/
│   ├── pages/              # 页面组件
│   │   ├── Dashboard/      # 仪表盘 - 系统概览
│   │   ├── Workflow/       # 工作流管理
│   │   ├── Execution/      # 执行管理
│   │   ├── Cluster/        # 集群管理
│   │   ├── Nodes/          # 节点服务
│   │   ├── Monitoring/     # 监控中心
│   │   ├── Plugins/        # 插件管理
│   │   └── System/         # 系统管理
│   ├── components/         # 通用组件
│   ├── services/           # API 服务
│   ├── types/              # 类型定义
│   └── constants/          # 常量配置
├── public/                 # 静态资源
└── 配置文件
```

## 🎯 主要功能模块

### 1. 仪表盘 (`/dashboard`)
- 系统概览统计
- 集群状态监控
- 执行统计图表
- 最近执行记录

### 2. 工作流管理 (`/workflow/*`)
- `/workflow/list` - 工作流列表
- `/workflow/designer` - 可视化设计器
- `/workflow/templates` - 模板库
- `/workflow/import-export` - 导入导出

### 3. 执行管理 (`/execution/*`)
- `/execution/monitor` - 执行监控
- `/execution/history` - 执行历史
- `/execution/schedule` - 定时调度
- `/execution/queue` - 执行队列

### 4. 集群管理 (`/cluster/*`)
- `/cluster/overview` - 集群概览
- `/cluster/nodes` - 节点管理
- `/cluster/topology` - 集群拓扑
- `/cluster/health` - 健康检查

### 5. 节点服务 (`/nodes/*`)
- `/nodes/designer` - Designer 设计器
- `/nodes/validator` - Validator 验证器
- `/nodes/executor` - Executor 执行器
- `/nodes/monitor` - Monitor 监控器
- `/nodes/scheduler` - Scheduler 调度器
- `/nodes/storage` - Storage 存储器

### 6. 监控中心 (`/monitoring/*`)
- `/monitoring/dashboard` - 监控仪表盘
- `/monitoring/metrics` - 性能指标
- `/monitoring/alerts` - 告警管理
- `/monitoring/logs` - 日志中心

### 7. 插件管理 (`/plugins/*`)
- `/plugins/market` - 插件市场
- `/plugins/installed` - 已安装插件
- `/plugins/development` - 插件开发
- `/plugins/natasha` - Natasha 编译器

### 8. 系统管理 (`/system/*`)
- `/system/config` - 系统配置
- `/system/environment` - 环境管理
- `/system/maintenance` - 系统维护
- `/system/about` - 关于系统

## 🔌 API 集成

项目已配置好与后端 API 的集成：

```typescript
// 工作流 API
import { workflowApi } from '@/services/workflow';
const workflows = await workflowApi.getWorkflows();

// 执行 API
import { executionApi } from '@/services/execution';
const execution = await executionApi.startExecution(workflowId);

// 集群 API
import { clusterApi } from '@/services/cluster';
const stats = await clusterApi.getClusterStats();
```

## 🎨 UI 组件使用

项目使用 Ant Design Pro Components：

```tsx
import { ProTable, ProCard } from '@ant-design/pro-components';
import { Button, Space, Tag } from 'antd';

// 使用示例
<ProCard title="标题">
  <ProTable
    columns={columns}
    dataSource={data}
    search={false}
  />
</ProCard>
```

## 🔧 开发提示

### 1. 添加新页面
1. 在 `src/pages/` 下创建页面组件
2. 在 `src/App.tsx` 中添加路由
3. 在 `src/constants/menu.ts` 中添加菜单项

### 2. 添加新 API
1. 在 `src/services/` 下创建 API 服务文件
2. 在 `src/types/api.ts` 中添加类型定义
3. 在页面组件中使用 API

### 3. 样式开发
- 优先使用 WindiCSS 工具类
- 遵循 Ant Design 设计规范
- 使用 `src/styles/global.css` 添加全局样式

## 🐳 Docker 部署

### 开发环境
```bash
docker-compose -f docker-compose.dev.yml up -d
```

### 生产环境
```bash
# 构建镜像
docker build -t flowcustomv1-web:v0.0.1.11 .

# 运行容器
docker run -p 3000:80 flowcustomv1-web:v0.0.1.11

# 或使用 docker-compose
docker-compose up -d
```

## 🔍 故障排除

### 常见问题

1. **端口占用**
   ```bash
   # 检查端口占用
   netstat -ano | findstr :3000
   
   # 修改端口（在 vite.config.ts 中）
   server: { port: 3001 }
   ```

2. **依赖安装失败**
   ```bash
   # 清理缓存
   npm cache clean --force
   
   # 删除 node_modules 重新安装
   rm -rf node_modules package-lock.json
   npm install
   ```

3. **API 连接失败**
   - 检查后端服务是否启动
   - 检查 `vite.config.ts` 中的代理配置
   - 检查防火墙设置

### 获取帮助

- 查看控制台错误信息
- 检查网络请求状态
- 查看项目文档和 README
- 联系开发团队

## 📝 下一步

1. 启动项目并访问 http://localhost:3000
2. 浏览各个功能模块
3. 查看页面结构和组件实现
4. 根据需求开始具体功能开发

祝您开发愉快！🎉
