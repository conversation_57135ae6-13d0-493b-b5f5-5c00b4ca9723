import React from 'react';
import { Card, Row, Col, Button, Tag, Rate, Space, Input, Select, Alert } from 'antd';
import { 
  AppstoreOutlined, 
  DownloadOutlined, 

  SearchOutlined,
  FilterOutlined,
  CodeOutlined,
  BugOutlined
} from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';

const { Search } = Input;
const { Option } = Select;

// 模拟插件数据
const mockPlugins = [
  {
    id: 'plugin-1',
    name: 'HTTP 请求节点',
    description: '支持各种 HTTP 方法的请求节点，包含认证、重试、超时等高级功能',
    version: '2.1.0',
    author: 'FlowCustom Team',
    category: '网络通信',
    rating: 4.8,
    downloads: 1250,
    tags: ['HTTP', 'API', '网络'],
    isInstalled: true,
  },
  {
    id: 'plugin-2',
    name: 'MySQL 数据库节点',
    description: '连接和操作 MySQL 数据库的节点，支持查询、插入、更新、删除操作',
    version: '1.5.2',
    author: 'Database Team',
    category: '数据库',
    rating: 4.6,
    downloads: 980,
    tags: ['MySQL', '数据库', 'SQL'],
    isInstalled: false,
  },
  {
    id: 'plugin-3',
    name: '文件处理节点',
    description: '处理各种文件格式的节点，支持读取、写入、转换等操作',
    version: '3.0.1',
    author: 'File Team',
    category: '文件处理',
    rating: 4.7,
    downloads: 756,
    tags: ['文件', '转换', 'IO'],
    isInstalled: true,
  },
  {
    id: 'plugin-4',
    name: '邮件发送节点',
    description: '发送邮件的节点，支持 SMTP、HTML 邮件、附件等功能',
    version: '1.8.0',
    author: 'Communication Team',
    category: '通信',
    rating: 4.5,
    downloads: 642,
    tags: ['邮件', 'SMTP', '通知'],
    isInstalled: false,
  },
  {
    id: 'plugin-5',
    name: 'JSON 处理节点',
    description: '处理 JSON 数据的节点，支持解析、转换、验证等操作',
    version: '2.3.1',
    author: 'Data Team',
    category: '数据处理',
    rating: 4.9,
    downloads: 1456,
    tags: ['JSON', '数据', '解析'],
    isInstalled: true,
  },
  {
    id: 'plugin-6',
    name: '定时触发器',
    description: '基于 Cron 表达式的定时触发器节点',
    version: '1.2.0',
    author: 'Scheduler Team',
    category: '触发器',
    rating: 4.4,
    downloads: 523,
    tags: ['定时', 'Cron', '触发器'],
    isInstalled: false,
  },
];

const PluginsMarket: React.FC = () => {
  return (
    <div className="page-container">
      <div className="page-header">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="page-title">
              <AppstoreOutlined className="mr-2" />
              插件市场
            </h1>
            <p className="page-description">发现和安装工作流节点插件</p>
          </div>
          <Space>
            <Button icon={<CodeOutlined />}>
              开发插件
            </Button>
            <Button type="primary" icon={<BugOutlined />}>
              Natasha 编译器
            </Button>
          </Space>
        </div>

        <Alert
          message="插件系统基于 Natasha 动态编译"
          description="支持运行时动态加载和卸载插件，提供强大的扩展能力"
          type="success"
          showIcon
          className="mb-6"
        />

        {/* 搜索和筛选 */}
        <div className="flex justify-between items-center mb-6">
          <Space size="large">
            <Search
              placeholder="搜索插件名称或描述"
              allowClear
              style={{ width: 400 }}
              prefix={<SearchOutlined />}
            />
            <Select
              placeholder="选择分类"
              allowClear
              style={{ width: 150 }}
              suffixIcon={<FilterOutlined />}
            >
              <Option value="网络通信">网络通信</Option>
              <Option value="数据库">数据库</Option>
              <Option value="文件处理">文件处理</Option>
              <Option value="通信">通信</Option>
              <Option value="数据处理">数据处理</Option>
              <Option value="触发器">触发器</Option>
            </Select>
            <Select
              placeholder="排序方式"
              defaultValue="downloads"
              style={{ width: 120 }}
            >
              <Option value="downloads">下载量</Option>
              <Option value="rating">评分</Option>
              <Option value="updated">更新时间</Option>
            </Select>
          </Space>
          <div className="text-gray-500">
            共找到 {mockPlugins.length} 个插件
          </div>
        </div>
      </div>

      {/* 插件网格 */}
      <Row gutter={[16, 16]}>
        {mockPlugins.map(plugin => (
          <Col xs={24} sm={12} lg={8} key={plugin.id}>
            <Card
              hoverable
              className="h-full"
              cover={
                <div className="h-32 bg-gradient-to-br from-purple-50 to-blue-100 flex items-center justify-center">
                  <div className="text-4xl text-purple-400">🔌</div>
                </div>
              }
              actions={[
                plugin.isInstalled ? (
                  <Button key="installed" disabled>
                    已安装
                  </Button>
                ) : (
                  <Button 
                    key="install" 
                    type="primary" 
                    icon={<DownloadOutlined />}
                  >
                    安装
                  </Button>
                ),
                <Button key="details" type="text">
                  详情
                </Button>,
                <Button key="demo" type="text">
                  演示
                </Button>,
              ]}
            >
              <Card.Meta
                title={
                  <div className="flex justify-between items-start">
                    <span className="truncate">{plugin.name}</span>
                    {plugin.isInstalled && (
                      <Tag color="green" className="ml-2">已安装</Tag>
                    )}
                  </div>
                }
                description={
                  <div>
                    <p className="text-gray-600 mb-3 line-clamp-2">
                      {plugin.description}
                    </p>
                    
                    <div className="flex justify-between items-center mb-2">
                      <div className="flex items-center">
                        <Rate disabled defaultValue={plugin.rating} allowHalf className="text-xs" />
                        <span className="ml-2 text-sm text-gray-500">
                          {plugin.rating}
                        </span>
                      </div>
                      <div className="text-sm text-gray-500">
                        {plugin.downloads} 下载
                      </div>
                    </div>
                    
                    <div className="flex flex-wrap gap-1 mb-2">
                      {plugin.tags.map(tag => (
                        <Tag key={tag}>{tag}</Tag>
                      ))}
                    </div>
                    
                    <div className="flex justify-between items-center text-xs text-gray-400">
                      <span>v{plugin.version}</span>
                      <span>{plugin.author}</span>
                    </div>
                  </div>
                }
              />
            </Card>
          </Col>
        ))}
      </Row>

      {/* 统计信息 */}
      <Row className="mt-8">
        <Col span={24}>
          <ProCard title="插件统计">
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{mockPlugins.length}</div>
                <div className="text-sm text-gray-500">可用插件</div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {mockPlugins.filter(p => p.isInstalled).length}
                </div>
                <div className="text-sm text-gray-500">已安装</div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">6</div>
                <div className="text-sm text-gray-500">分类数量</div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">
                  {Math.round(mockPlugins.reduce((sum, p) => sum + p.rating, 0) / mockPlugins.length * 10) / 10}
                </div>
                <div className="text-sm text-gray-500">平均评分</div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-cyan-600">
                  {mockPlugins.reduce((sum, p) => sum + p.downloads, 0)}
                </div>
                <div className="text-sm text-gray-500">总下载量</div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-pink-600">16</div>
                <div className="text-sm text-gray-500">内置节点</div>
              </div>
            </div>
          </ProCard>
        </Col>
      </Row>
    </div>
  );
};

export default PluginsMarket;
