using FlowCustomV1.Core.Interfaces;

namespace FlowCustomV1.Core.Models.Plugins;

/// <summary>
/// 编译结果
/// </summary>
public class CompilationResult
{
    /// <summary>
    /// 是否编译成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 节点类型
    /// </summary>
    public string NodeType { get; set; } = string.Empty;

    /// <summary>
    /// 编译后的节点执行器
    /// </summary>
    public INodeExecutor? Executor { get; set; }

    /// <summary>
    /// 编译错误信息
    /// </summary>
    public List<CompilationError> Errors { get; set; } = new();

    /// <summary>
    /// 编译警告信息
    /// </summary>
    public List<CompilationWarning> Warnings { get; set; } = new();

    /// <summary>
    /// 编译时间（毫秒）
    /// </summary>
    public long CompilationTimeMs { get; set; }

    /// <summary>
    /// 编译开始时间
    /// </summary>
    public DateTime StartedAt { get; set; }

    /// <summary>
    /// 编译完成时间
    /// </summary>
    public DateTime CompletedAt { get; set; }

    /// <summary>
    /// 源代码哈希值
    /// </summary>
    public string SourceCodeHash { get; set; } = string.Empty;

    /// <summary>
    /// 编译器版本
    /// </summary>
    public string CompilerVersion { get; set; } = string.Empty;

    /// <summary>
    /// 生成的程序集信息
    /// </summary>
    public AssemblyInfo? AssemblyInfo { get; set; }
}

/// <summary>
/// 编译错误
/// </summary>
public class CompilationError
{
    /// <summary>
    /// 错误代码
    /// </summary>
    public string Code { get; set; } = string.Empty;

    /// <summary>
    /// 错误消息
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 行号
    /// </summary>
    public int Line { get; set; }

    /// <summary>
    /// 列号
    /// </summary>
    public int Column { get; set; }

    /// <summary>
    /// 文件名
    /// </summary>
    public string FileName { get; set; } = string.Empty;

    /// <summary>
    /// 严重程度
    /// </summary>
    public CompilationSeverity Severity { get; set; }
}

/// <summary>
/// 编译警告
/// </summary>
public class CompilationWarning
{
    /// <summary>
    /// 警告代码
    /// </summary>
    public string Code { get; set; } = string.Empty;

    /// <summary>
    /// 警告消息
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 行号
    /// </summary>
    public int Line { get; set; }

    /// <summary>
    /// 列号
    /// </summary>
    public int Column { get; set; }

    /// <summary>
    /// 文件名
    /// </summary>
    public string FileName { get; set; } = string.Empty;
}

/// <summary>
/// 编译严重程度
/// </summary>
public enum CompilationSeverity
{
    /// <summary>
    /// 信息
    /// </summary>
    Info,

    /// <summary>
    /// 警告
    /// </summary>
    Warning,

    /// <summary>
    /// 错误
    /// </summary>
    Error,

    /// <summary>
    /// 致命错误
    /// </summary>
    Fatal
}

/// <summary>
/// 程序集信息
/// </summary>
public class AssemblyInfo
{
    /// <summary>
    /// 程序集名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 程序集版本
    /// </summary>
    public string Version { get; set; } = string.Empty;

    /// <summary>
    /// 程序集位置
    /// </summary>
    public string Location { get; set; } = string.Empty;

    /// <summary>
    /// 程序集大小（字节）
    /// </summary>
    public long Size { get; set; }

    /// <summary>
    /// 程序集哈希值
    /// </summary>
    public string Hash { get; set; } = string.Empty;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; }
}
