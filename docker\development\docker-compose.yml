name: flowcustom-dev

services:
  # NATS单节点 - 开发环境 (默认端口)
  nats-server:
    image: nats:2.11.8-alpine
    container_name: nats-dev-server
    ports:
      - "4222:4222"
      - "8222:8222"
    volumes:
      - ./nats/nats-1.conf:/nats-server.conf:ro
      - nats_dev_data:/tmp/jetstream
    command: ["-c", "/nats-server.conf"]
    networks:
      - flowcustom-dev

  # MySQL数据库 - 开发环境 (默认端口)
  mysql:
    image: mysql:8.0
    container_name: mysql-dev
    environment:
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_DATABASE: flowcustom_dev
      MYSQL_USER: flowcustom
      MYSQL_PASSWORD: FlowCustom@2025
    ports:
      - "3306:3306"
    volumes:
      - mysql_dev_data:/var/lib/mysql
      - ./mysql/init:/docker-entrypoint-initdb.d
    networks:
      - flowcustom-dev

  # API节点 - 开发环境 (默认端口)
  api-node:
    build:
      context: ../../
      dockerfile: docker/development/Dockerfile.api
    container_name: flowcustom-dev-api-node
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:5000
    ports:
      - "5000:5000"
    networks:
      - flowcustom-dev
    depends_on:
      - nats-server
      - mysql

volumes:
  mysql_dev_data:
  nats_dev_data:

networks:
  flowcustom-dev:
    driver: bridge
