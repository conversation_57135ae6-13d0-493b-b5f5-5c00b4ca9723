#!/usr/bin/env python3
"""
FlowCustomV1 v0.0.1.7 专业化节点服务测试
测试Designer、<PERSON>ida<PERSON>、Executor节点的专业化功能
"""

import asyncio
import json
import time
import argparse
import logging
from datetime import datetime
from typing import Dict, List, Any
import aiohttp

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SpecializedNodeTester:
    def __init__(self):
        self.nodes = {
            "designer": {
                "beijing": "http://designer-node-beijing:5000",
                "shanghai": "http://designer-node-shanghai:5000"
            },
            "validator": {
                "beijing": "http://validator-node-beijing:5000", 
                "shanghai": "http://validator-node-shanghai:5000"
            },
            "executor": {
                "beijing": "http://executor-node-beijing:5000",
                "shanghai": "http://executor-node-shanghai:5000"
            }
        }
        
        self.test_results = {
            "test_suite": "Specialized Node Services Tests",
            "start_time": datetime.utcnow().isoformat(),
            "tests": [],
            "summary": {
                "total": 0,
                "passed": 0,
                "failed": 0,
                "errors": []
            }
        }

    async def run_test(self, test_name: str, test_func):
        """运行单个测试"""
        logger.info(f"🧪 运行测试: {test_name}")
        test_result = {
            "name": test_name,
            "start_time": datetime.utcnow().isoformat(),
            "status": "running",
            "duration": 0,
            "details": {}
        }
        
        start_time = time.time()
        try:
            details = await test_func()
            test_result["status"] = "passed"
            test_result["details"] = details
            self.test_results["summary"]["passed"] += 1
            logger.info(f"✅ {test_name} 通过")
        except Exception as e:
            test_result["status"] = "failed"
            test_result["error"] = str(e)
            self.test_results["summary"]["failed"] += 1
            self.test_results["summary"]["errors"].append(f"{test_name}: {str(e)}")
            logger.error(f"❌ {test_name} 失败: {str(e)}")
        finally:
            test_result["duration"] = time.time() - start_time
            test_result["end_time"] = datetime.utcnow().isoformat()
            self.test_results["tests"].append(test_result)
            self.test_results["summary"]["total"] += 1

    async def test_node_health_checks(self) -> Dict[str, Any]:
        """测试所有节点的健康检查"""
        results = {}
        
        async with aiohttp.ClientSession() as session:
            for node_type, locations in self.nodes.items():
                results[node_type] = {}
                for location, url in locations.items():
                    try:
                        async with session.get(f"{url}/health", timeout=10) as response:
                            if response.status == 200:
                                health_data = await response.json()
                                results[node_type][location] = {
                                    "status": "healthy",
                                    "response_time": response.headers.get("X-Response-Time", "unknown"),
                                    "details": health_data
                                }
                            else:
                                results[node_type][location] = {
                                    "status": "unhealthy",
                                    "http_status": response.status
                                }
                    except Exception as e:
                        results[node_type][location] = {
                            "status": "error",
                            "error": str(e)
                        }
        
        return results

    async def test_designer_services(self) -> Dict[str, Any]:
        """测试Designer节点服务"""
        results = {}
        
        # 测试工作流设计API
        test_workflow = {
            "workflowId": "test-designer-workflow",
            "name": "测试设计器工作流",
            "description": "用于测试Designer节点功能的工作流",
            "nodes": [
                {
                    "nodeId": "start",
                    "nodeType": "Start",
                    "name": "开始节点",
                    "position": {"x": 100, "y": 100}
                },
                {
                    "nodeId": "task1", 
                    "nodeType": "Task",
                    "name": "任务节点",
                    "position": {"x": 300, "y": 100},
                    "configuration": {
                        "taskType": "data_processing",
                        "timeout": 30
                    }
                },
                {
                    "nodeId": "end",
                    "nodeType": "End", 
                    "name": "结束节点",
                    "position": {"x": 500, "y": 100}
                }
            ],
            "connections": [
                {"sourceNodeId": "start", "targetNodeId": "task1"},
                {"sourceNodeId": "task1", "targetNodeId": "end"}
            ]
        }
        
        async with aiohttp.ClientSession() as session:
            for location, url in self.nodes["designer"].items():
                try:
                    # 测试创建工作流
                    async with session.post(
                        f"{url}/api/designer/workflows",
                        json=test_workflow,
                        timeout=30
                    ) as response:
                        if response.status == 201:
                            workflow_data = await response.json()
                            results[f"designer_{location}_create"] = {
                                "status": "success",
                                "workflow_id": workflow_data.get("workflowId")
                            }
                            
                            # 测试获取工作流
                            workflow_id = workflow_data.get("workflowId")
                            if workflow_id:
                                async with session.get(
                                    f"{url}/api/designer/workflows/{workflow_id}",
                                    timeout=10
                                ) as get_response:
                                    if get_response.status == 200:
                                        results[f"designer_{location}_get"] = {"status": "success"}
                                    else:
                                        results[f"designer_{location}_get"] = {
                                            "status": "failed",
                                            "http_status": get_response.status
                                        }
                        else:
                            results[f"designer_{location}_create"] = {
                                "status": "failed",
                                "http_status": response.status
                            }
                            
                except Exception as e:
                    results[f"designer_{location}"] = {
                        "status": "error",
                        "error": str(e)
                    }
        
        return results

    async def test_validator_services(self) -> Dict[str, Any]:
        """测试Validator节点服务"""
        results = {}
        
        # 测试工作流验证
        test_workflow = {
            "workflowId": "test-validator-workflow",
            "name": "测试验证器工作流",
            "nodes": [
                {"nodeId": "start", "nodeType": "Start"},
                {"nodeId": "task1", "nodeType": "Task"},
                {"nodeId": "end", "nodeType": "End"}
            ],
            "connections": [
                {"sourceNodeId": "start", "targetNodeId": "task1"},
                {"sourceNodeId": "task1", "targetNodeId": "end"}
            ]
        }
        
        # 无效工作流（用于测试验证失败）
        invalid_workflow = {
            "workflowId": "test-invalid-workflow",
            "name": "无效工作流",
            "nodes": [
                {"nodeId": "start", "nodeType": "Start"},
                {"nodeId": "end", "nodeType": "End"}
                # 缺少连接，应该验证失败
            ],
            "connections": []
        }
        
        async with aiohttp.ClientSession() as session:
            for location, url in self.nodes["validator"].items():
                try:
                    # 测试有效工作流验证
                    async with session.post(
                        f"{url}/api/validator/validate",
                        json=test_workflow,
                        timeout=30
                    ) as response:
                        if response.status == 200:
                            validation_result = await response.json()
                            results[f"validator_{location}_valid"] = {
                                "status": "success",
                                "is_valid": validation_result.get("isValid", False),
                                "errors": validation_result.get("errors", [])
                            }
                        else:
                            results[f"validator_{location}_valid"] = {
                                "status": "failed",
                                "http_status": response.status
                            }
                    
                    # 测试无效工作流验证
                    async with session.post(
                        f"{url}/api/validator/validate",
                        json=invalid_workflow,
                        timeout=30
                    ) as response:
                        if response.status == 200:
                            validation_result = await response.json()
                            results[f"validator_{location}_invalid"] = {
                                "status": "success",
                                "is_valid": validation_result.get("isValid", True),  # 应该是False
                                "errors": validation_result.get("errors", [])
                            }
                        else:
                            results[f"validator_{location}_invalid"] = {
                                "status": "failed", 
                                "http_status": response.status
                            }
                            
                except Exception as e:
                    results[f"validator_{location}"] = {
                        "status": "error",
                        "error": str(e)
                    }
        
        return results

    async def test_executor_services(self) -> Dict[str, Any]:
        """测试Executor节点服务"""
        results = {}
        
        # 测试工作流执行
        test_workflow = {
            "workflowId": "test-executor-workflow",
            "name": "测试执行器工作流",
            "nodes": [
                {"nodeId": "start", "nodeType": "Start"},
                {
                    "nodeId": "task1",
                    "nodeType": "Task",
                    "configuration": {
                        "taskType": "simple_task",
                        "timeout": 10
                    }
                },
                {"nodeId": "end", "nodeType": "End"}
            ],
            "connections": [
                {"sourceNodeId": "start", "targetNodeId": "task1"},
                {"sourceNodeId": "task1", "targetNodeId": "end"}
            ]
        }
        
        async with aiohttp.ClientSession() as session:
            for location, url in self.nodes["executor"].items():
                try:
                    # 测试工作流执行
                    execution_request = {
                        "workflowDefinition": test_workflow,
                        "inputData": {"test_input": "hello_world"}
                    }
                    
                    async with session.post(
                        f"{url}/api/executor/execute",
                        json=execution_request,
                        timeout=60
                    ) as response:
                        if response.status == 202:  # 异步执行，返回202
                            execution_result = await response.json()
                            execution_id = execution_result.get("executionId")
                            
                            results[f"executor_{location}_execute"] = {
                                "status": "success",
                                "execution_id": execution_id
                            }
                            
                            # 测试执行状态查询
                            if execution_id:
                                await asyncio.sleep(2)  # 等待执行开始
                                async with session.get(
                                    f"{url}/api/executor/executions/{execution_id}",
                                    timeout=10
                                ) as status_response:
                                    if status_response.status == 200:
                                        status_data = await status_response.json()
                                        results[f"executor_{location}_status"] = {
                                            "status": "success",
                                            "execution_status": status_data.get("status"),
                                            "details": status_data
                                        }
                                    else:
                                        results[f"executor_{location}_status"] = {
                                            "status": "failed",
                                            "http_status": status_response.status
                                        }
                        else:
                            results[f"executor_{location}_execute"] = {
                                "status": "failed",
                                "http_status": response.status
                            }
                            
                    # 测试执行容量查询
                    async with session.get(
                        f"{url}/api/executor/capacity",
                        timeout=10
                    ) as capacity_response:
                        if capacity_response.status == 200:
                            capacity_data = await capacity_response.json()
                            results[f"executor_{location}_capacity"] = {
                                "status": "success",
                                "capacity": capacity_data
                            }
                        else:
                            results[f"executor_{location}_capacity"] = {
                                "status": "failed",
                                "http_status": capacity_response.status
                            }
                            
                except Exception as e:
                    results[f"executor_{location}"] = {
                        "status": "error",
                        "error": str(e)
                    }
        
        return results

    async def test_cross_node_communication(self) -> Dict[str, Any]:
        """测试跨节点通信"""
        results = {}
        
        # 这个测试需要实际的跨节点消息传递
        # 由于复杂性，这里只做基础的连通性测试
        async with aiohttp.ClientSession() as session:
            # 测试节点间的基础连通性
            for node_type, locations in self.nodes.items():
                results[node_type] = {}
                for location, url in locations.items():
                    try:
                        async with session.get(f"{url}/api/cluster/nodes", timeout=10) as response:
                            if response.status == 200:
                                nodes_data = await response.json()
                                results[node_type][location] = {
                                    "status": "success",
                                    "discovered_nodes": len(nodes_data.get("nodes", [])),
                                    "cluster_status": nodes_data.get("status", "unknown")
                                }
                            else:
                                results[node_type][location] = {
                                    "status": "failed",
                                    "http_status": response.status
                                }
                    except Exception as e:
                        results[node_type][location] = {
                            "status": "error",
                            "error": str(e)
                        }
        
        return results

    async def run_all_tests(self):
        """运行所有测试"""
        logger.info("🚀 开始专业化节点服务测试套件")
        
        # 定义测试列表
        tests = [
            ("节点健康检查测试", self.test_node_health_checks),
            ("Designer服务测试", self.test_designer_services),
            ("Validator服务测试", self.test_validator_services),
            ("Executor服务测试", self.test_executor_services),
            ("跨节点通信测试", self.test_cross_node_communication)
        ]
        
        # 运行所有测试
        for test_name, test_func in tests:
            await self.run_test(test_name, test_func)
        
        # 完成测试
        self.test_results["end_time"] = datetime.utcnow().isoformat()
        self.test_results["total_duration"] = sum(test["duration"] for test in self.test_results["tests"])
        
        # 输出摘要
        summary = self.test_results["summary"]
        logger.info(f"📊 测试完成: {summary['passed']}/{summary['total']} 通过")
        
        if summary["failed"] > 0:
            logger.error("❌ 失败的测试:")
            for error in summary["errors"]:
                logger.error(f"  - {error}")
        
        return self.test_results

def main():
    parser = argparse.ArgumentParser(description="FlowCustomV1 专业化节点服务测试")
    parser.add_argument("--output", "-o", help="输出结果文件路径")
    args = parser.parse_args()
    
    async def run_tests():
        tester = SpecializedNodeTester()
        results = await tester.run_all_tests()
        
        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            logger.info(f"测试结果已保存到: {args.output}")
        
        return results["summary"]["failed"] == 0
    
    # 运行测试
    success = asyncio.run(run_tests())
    exit(0 if success else 1)

if __name__ == "__main__":
    main()
