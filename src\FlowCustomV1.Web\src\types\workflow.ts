// 基于后端的17种内置节点类型
export type NodeType =
  // 基础控制节点
  | 'Start' | 'End' | 'Task'
  // 触发器节点
  | 'TimerTrigger' | 'EventTrigger' | 'WebhookTrigger'
  // 动作节点
  | 'HttpRequest' | 'DataProcessor' | 'NotificationSender'
  // 控制流节点
  | 'IfCondition' | 'ForLoop' | 'ParallelExecution'
  // 数据转换节点
  | 'DataMapper' | 'DataFilter'
  // 外部服务节点
  | 'MySqlDatabase' | 'NatsMessage' | 'RestApiCall';

// 节点分类
export interface NodeCategory {
  id: string;
  name: string;
  description: string;
  icon: string;
  color: string;
  nodes: NodeDefinition[];
}

// 节点定义
export interface NodeDefinition {
  type: NodeType;
  name: string;
  displayName: string;
  description: string;
  icon: string;
  color: string;
  category: string;
  version: string;
  author: string;
  tags: string[];
  inputs: NodePort[];
  outputs: NodePort[];
  properties: NodeProperty[];
}

// 节点端口
export interface NodePort {
  id: string;
  name: string;
  type: 'data' | 'control';
  dataType?: string;
  required?: boolean;
  description?: string;
}

// 节点属性
export interface NodeProperty {
  id: string;
  name: string;
  type: 'string' | 'number' | 'boolean' | 'select' | 'textarea' | 'json';
  defaultValue?: any;
  required?: boolean;
  description?: string;
  options?: { label: string; value: any }[];
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
    message?: string;
  };
}

// 工作流节点类型（独立于画布库）
export interface WorkflowNode {
  id: string;
  type: NodeType;
  position: {
    x: number;
    y: number;
  };
  data: {
    label: string;
    description?: string;
    properties?: Record<string, any>;
    inputs?: NodePort[];
    outputs?: NodePort[];
    status?: 'idle' | 'running' | 'success' | 'error';
    error?: string;
    hasValidationErrors?: boolean;
  };
}

// 工作流连接类型（独立于画布库）
export interface WorkflowEdge {
  id: string;
  source: string;
  target: string;
  sourceHandle?: string;
  targetHandle?: string;
  data?: {
    label?: string;
    condition?: string;
    animated?: boolean;
  };
}

// 工作流画布数据
export interface WorkflowCanvasData {
  nodes: WorkflowNode[];
  edges: WorkflowEdge[];
  viewport: {
    x: number;
    y: number;
    zoom: number;
  };
}

// 节点执行状态
export interface NodeExecutionStatus {
  nodeId: string;
  status: 'idle' | 'running' | 'success' | 'error';
  startTime?: string;
  endTime?: string;
  duration?: number;
  error?: string;
  outputData?: Record<string, any>;
}

// 工作流执行状态
export interface WorkflowExecutionStatus {
  executionId: string;
  workflowId: string;
  status: 'idle' | 'running' | 'success' | 'error' | 'cancelled';
  startTime?: string;
  endTime?: string;
  duration?: number;
  currentNodeId?: string;
  nodeStatuses: Record<string, NodeExecutionStatus>;
  error?: string;
}
