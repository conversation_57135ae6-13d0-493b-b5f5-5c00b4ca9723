using FlowCustomV1.Core.Models.Workflow;
using Xunit;

namespace FlowCustomV1.Core.Tests.Models;

/// <summary>
/// NodeDefinition 单元测试
/// </summary>
public class NodeDefinitionTests
{
    /// <summary>
    /// 测试节点定义创建
    /// </summary>
    [Fact]
    public void NodeDefinition_Create_SetsDefaultValues()
    {
        // Act
        var nodeDefinition = new NodeDefinition();

        // Assert
        Assert.NotNull(nodeDefinition.NodeType);
        Assert.NotNull(nodeDefinition.DisplayName);
        Assert.NotNull(nodeDefinition.Description);
        Assert.NotNull(nodeDefinition.Version);
        Assert.NotNull(nodeDefinition.Icon);
        Assert.NotNull(nodeDefinition.Color);
        Assert.NotNull(nodeDefinition.InputParameters);
        Assert.NotNull(nodeDefinition.OutputParameters);
        Assert.NotNull(nodeDefinition.ConfigurationParameters);
        Assert.NotNull(nodeDefinition.ResourceRequirements);
        Assert.NotNull(nodeDefinition.ExecutorType);
        Assert.NotNull(nodeDefinition.ExecutorAssembly);
        Assert.NotNull(nodeDefinition.Tags);
        Assert.NotNull(nodeDefinition.Metadata);
        
        Assert.Equal("1.0.0", nodeDefinition.Version);
        Assert.Equal("#007ACC", nodeDefinition.Color);
        Assert.Equal(NodeTypeCategory.Process, nodeDefinition.Category);
        Assert.True(nodeDefinition.SupportsAsync);
        Assert.False(nodeDefinition.IsStateful);
    }

    /// <summary>
    /// 测试节点定义验证 - 有效定义
    /// </summary>
    [Fact]
    public void IsValid_ValidDefinition_ReturnsTrue()
    {
        // Arrange
        var nodeDefinition = new NodeDefinition
        {
            NodeType = "TestNode",
            DisplayName = "Test Node"
        };

        // Act
        var isValid = nodeDefinition.IsValid();

        // Assert
        Assert.True(isValid);
    }

    /// <summary>
    /// 测试节点定义验证 - 无效定义（缺少NodeType）
    /// </summary>
    [Fact]
    public void IsValid_MissingNodeType_ReturnsFalse()
    {
        // Arrange
        var nodeDefinition = new NodeDefinition
        {
            NodeType = "", // Empty NodeType
            DisplayName = "Test Node"
        };

        // Act
        var isValid = nodeDefinition.IsValid();

        // Assert
        Assert.False(isValid);
    }

    /// <summary>
    /// 测试节点定义验证 - 无效定义（缺少DisplayName）
    /// </summary>
    [Fact]
    public void IsValid_MissingDisplayName_ReturnsFalse()
    {
        // Arrange
        var nodeDefinition = new NodeDefinition
        {
            NodeType = "TestNode",
            DisplayName = "" // Empty DisplayName
        };

        // Act
        var isValid = nodeDefinition.IsValid();

        // Assert
        Assert.False(isValid);
    }

    /// <summary>
    /// 测试节点定义验证 - 重复参数名称
    /// </summary>
    [Fact]
    public void IsValid_DuplicateParameterNames_ReturnsFalse()
    {
        // Arrange
        var nodeDefinition = new NodeDefinition
        {
            NodeType = "TestNode",
            DisplayName = "Test Node"
        };

        // Add duplicate parameter names across different parameter types
        nodeDefinition.InputParameters.Add(new NodeParameterDefinition { Name = "param1" });
        nodeDefinition.OutputParameters.Add(new NodeParameterDefinition { Name = "param1" }); // Duplicate

        // Act
        var isValid = nodeDefinition.IsValid();

        // Assert
        Assert.False(isValid);
    }

    /// <summary>
    /// 测试节点定义验证 - 唯一参数名称
    /// </summary>
    [Fact]
    public void IsValid_UniqueParameterNames_ReturnsTrue()
    {
        // Arrange
        var nodeDefinition = new NodeDefinition
        {
            NodeType = "TestNode",
            DisplayName = "Test Node"
        };

        // Add unique parameter names
        nodeDefinition.InputParameters.Add(new NodeParameterDefinition { Name = "input1" });
        nodeDefinition.OutputParameters.Add(new NodeParameterDefinition { Name = "output1" });
        nodeDefinition.ConfigurationParameters.Add(new NodeParameterDefinition { Name = "config1" });

        // Act
        var isValid = nodeDefinition.IsValid();

        // Assert
        Assert.True(isValid);
    }

    /// <summary>
    /// 测试节点定义克隆
    /// </summary>
    [Fact]
    public void Clone_CreatesDeepCopy()
    {
        // Arrange
        var original = new NodeDefinition
        {
            NodeType = "TestNode",
            DisplayName = "Test Node",
            Description = "Test Description",
            Version = "2.0.0",
            Category = NodeTypeCategory.Integration,
            Icon = "test-icon",
            Color = "#FF0000",
            SupportsAsync = false,
            IsStateful = true,
            ExecutorType = "TestExecutor",
            ExecutorAssembly = "TestAssembly"
        };

        original.InputParameters.Add(new NodeParameterDefinition
        {
            Name = "input1",
            DisplayName = "Input 1",
            DataType = ParameterDataType.String,
            IsRequired = true
        });

        original.Tags.Add("test-tag");
        original.Metadata["key"] = "value";

        // Act
        var clone = original.Clone();

        // Assert
        Assert.NotSame(original, clone);
        Assert.Equal(original.NodeType, clone.NodeType);
        Assert.Equal(original.DisplayName, clone.DisplayName);
        Assert.Equal(original.Description, clone.Description);
        Assert.Equal(original.Version, clone.Version);
        Assert.Equal(original.Category, clone.Category);
        Assert.Equal(original.Icon, clone.Icon);
        Assert.Equal(original.Color, clone.Color);
        Assert.Equal(original.SupportsAsync, clone.SupportsAsync);
        Assert.Equal(original.IsStateful, clone.IsStateful);
        Assert.Equal(original.ExecutorType, clone.ExecutorType);
        Assert.Equal(original.ExecutorAssembly, clone.ExecutorAssembly);

        // Verify deep copy of collections
        Assert.NotSame(original.InputParameters, clone.InputParameters);
        Assert.Equal(original.InputParameters.Count, clone.InputParameters.Count);
        Assert.NotSame(original.Tags, clone.Tags);
        Assert.Equal(original.Tags.Count, clone.Tags.Count);
        Assert.NotSame(original.Metadata, clone.Metadata);
        Assert.Equal(original.Metadata.Count, clone.Metadata.Count);

        // Verify parameter deep copy
        Assert.NotSame(original.InputParameters[0], clone.InputParameters[0]);
        Assert.Equal(original.InputParameters[0].Name, clone.InputParameters[0].Name);
    }

    /// <summary>
    /// 测试ToString方法
    /// </summary>
    [Fact]
    public void ToString_ReturnsFormattedString()
    {
        // Arrange
        var nodeDefinition = new NodeDefinition
        {
            NodeType = "TestNode",
            DisplayName = "Test Node",
            Version = "1.5.0",
            Category = NodeTypeCategory.Integration
        };

        // Act
        var result = nodeDefinition.ToString();

        // Assert
        Assert.Equal("NodeDefinition[TestNode] Test Node v1.5.0 - Integration", result);
    }

    /// <summary>
    /// 测试不同节点类型分类
    /// </summary>
    [Theory]
    [InlineData(NodeTypeCategory.Trigger)]
    [InlineData(NodeTypeCategory.Process)]
    [InlineData(NodeTypeCategory.Control)]
    [InlineData(NodeTypeCategory.Output)]
    [InlineData(NodeTypeCategory.Integration)]
    [InlineData(NodeTypeCategory.Custom)]
    public void Category_AllValues_CanBeSet(NodeTypeCategory category)
    {
        // Arrange
        var nodeDefinition = new NodeDefinition();

        // Act
        nodeDefinition.Category = category;

        // Assert
        Assert.Equal(category, nodeDefinition.Category);
    }

    /// <summary>
    /// 测试资源需求设置
    /// </summary>
    [Fact]
    public void ResourceRequirements_CanBeConfigured()
    {
        // Arrange
        var nodeDefinition = new NodeDefinition();
        var resourceRequirements = new NodeResourceRequirements
        {
            RequiredCpuCores = 2.0,
            RequiredMemoryMb = 1024,
            RequiredDiskMb = 500,
            RequiredNetworkMbps = 10
        };

        resourceRequirements.RequiredTags.Add("gpu");
        resourceRequirements.Metadata["special"] = "requirement";

        // Act
        nodeDefinition.ResourceRequirements = resourceRequirements;

        // Assert
        Assert.Equal(2.0, nodeDefinition.ResourceRequirements.RequiredCpuCores);
        Assert.Equal(1024, nodeDefinition.ResourceRequirements.RequiredMemoryMb);
        Assert.Equal(500, nodeDefinition.ResourceRequirements.RequiredDiskMb);
        Assert.Equal(10, nodeDefinition.ResourceRequirements.RequiredNetworkMbps);
        Assert.Contains("gpu", nodeDefinition.ResourceRequirements.RequiredTags);
        Assert.Equal("requirement", nodeDefinition.ResourceRequirements.Metadata["special"]);
    }
}
