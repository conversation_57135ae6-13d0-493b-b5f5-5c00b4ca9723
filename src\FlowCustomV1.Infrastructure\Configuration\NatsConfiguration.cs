using System.ComponentModel.DataAnnotations;
using FlowCustomV1.Core.Interfaces.Configuration;

namespace FlowCustomV1.Infrastructure.Configuration;

/// <summary>
/// NATS连接配置
/// </summary>
public class NatsConfiguration : IMessagingConfiguration
{
    /// <summary>
    /// 配置节名称
    /// </summary>
    public const string SectionName = "Nats";

    /// <summary>
    /// NATS服务器地址列表
    /// </summary>
    [Required]
    public List<string> Servers { get; set; } = new();

    /// <summary>
    /// 连接名称
    /// </summary>
    public string ConnectionName { get; set; } = "FlowCustomV1";

    /// <summary>
    /// 用户名（可选）
    /// </summary>
    public string? Username { get; set; }

    /// <summary>
    /// 密码（可选）
    /// </summary>
    public string? Password { get; set; }

    /// <summary>
    /// 连接超时时间（秒）
    /// </summary>
    [Range(1, 300)]
    public int ConnectionTimeoutSeconds { get; set; } = 30;

    /// <summary>
    /// 重连间隔时间（秒）
    /// </summary>
    [Range(1, 60)]
    public int ReconnectIntervalSeconds { get; set; } = 5;

    /// <summary>
    /// 最大重连次数
    /// </summary>
    [Range(0, 100)]
    public int MaxReconnectAttempts { get; set; } = 10;

    /// <summary>
    /// 是否启用自动重连
    /// </summary>
    public bool EnableAutoReconnect { get; set; } = true;

    /// <summary>
    /// Ping间隔时间（秒）
    /// </summary>
    [Range(10, 300)]
    public int PingIntervalSeconds { get; set; } = 120;

    /// <summary>
    /// 最大Ping未响应次数
    /// </summary>
    [Range(1, 10)]
    public int MaxPingsOutstanding { get; set; } = 2;

    /// <summary>
    /// 是否启用详细日志
    /// </summary>
    public bool EnableVerboseLogging { get; set; } = false;

    /// <summary>
    /// JetStream配置
    /// </summary>
    public JetStreamConfiguration JetStream { get; set; } = new();

    /// <summary>
    /// 连接池配置
    /// </summary>
    public ConnectionPoolConfiguration ConnectionPool { get; set; } = new();

    /// <summary>
    /// 消息序列化配置
    /// </summary>
    public SerializationConfiguration Serialization { get; set; } = new();
}

/// <summary>
/// JetStream配置
/// </summary>
public class JetStreamConfiguration : IStreamingConfiguration
{
    /// <summary>
    /// 是否启用JetStream
    /// </summary>
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// JetStream域名
    /// </summary>
    public string? Domain { get; set; }

    /// <summary>
    /// API前缀
    /// </summary>
    public string ApiPrefix { get; set; } = "$JS.API";

    /// <summary>
    /// 默认流配置
    /// </summary>
    public StreamConfiguration DefaultStream { get; set; } = new();

    /// <summary>
    /// 默认流配置（接口实现）
    /// </summary>
    IStreamConfiguration IStreamingConfiguration.DefaultStream => DefaultStream;
}

/// <summary>
/// 流配置
/// </summary>
public class StreamConfiguration : IStreamConfiguration
{
    /// <summary>
    /// 流名称
    /// </summary>
    public string Name { get; set; } = "FLOWCUSTOM";

    /// <summary>
    /// 流主题
    /// </summary>
    public List<string> Subjects { get; set; } = new() { "flowcustom.>" };

    /// <summary>
    /// 流主题（只读列表）
    /// </summary>
    IReadOnlyList<string> IStreamConfiguration.Subjects => Subjects.AsReadOnly();

    /// <summary>
    /// 存储类型（file/memory）
    /// </summary>
    public string Storage { get; set; } = "file";

    /// <summary>
    /// 最大消息数
    /// </summary>
    public long MaxMessages { get; set; } = 1000000;

    /// <summary>
    /// 最大字节数
    /// </summary>
    public long MaxBytes { get; set; } = 1024 * 1024 * 1024; // 1GB

    /// <summary>
    /// 消息最大保留时间（秒）
    /// </summary>
    public long MaxAgeSeconds { get; set; } = 24 * 60 * 60; // 24小时

    /// <summary>
    /// 副本数
    /// </summary>
    [Range(1, 5)]
    public int Replicas { get; set; } = 3;
}

/// <summary>
/// 连接池配置
/// </summary>
public class ConnectionPoolConfiguration : IConnectionPoolConfiguration
{
    /// <summary>
    /// 是否启用连接池
    /// </summary>
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// 最小连接数
    /// </summary>
    [Range(1, 100)]
    public int MinConnections { get; set; } = 2;

    /// <summary>
    /// 最大连接数
    /// </summary>
    [Range(1, 1000)]
    public int MaxConnections { get; set; } = 10;

    /// <summary>
    /// 连接空闲超时时间（秒）
    /// </summary>
    [Range(30, 3600)]
    public int IdleTimeoutSeconds { get; set; } = 300;

    /// <summary>
    /// 连接获取超时时间（秒）
    /// </summary>
    [Range(1, 60)]
    public int AcquireTimeoutSeconds { get; set; } = 30;

    /// <summary>
    /// 连接健康检查间隔（秒）
    /// </summary>
    [Range(10, 300)]
    public int HealthCheckIntervalSeconds { get; set; } = 60;
}

/// <summary>
/// 序列化配置
/// </summary>
public class SerializationConfiguration : ISerializationConfiguration
{
    /// <summary>
    /// 序列化类型（json/messagepack/protobuf）
    /// </summary>
    public string Type { get; set; } = "json";

    /// <summary>
    /// 是否启用压缩
    /// </summary>
    public bool EnableCompression { get; set; } = false;

    /// <summary>
    /// 压缩类型（gzip/deflate）
    /// </summary>
    public string CompressionType { get; set; } = "gzip";

    /// <summary>
    /// JSON序列化选项
    /// </summary>
    public JsonSerializationOptions Json { get; set; } = new();
}

/// <summary>
/// JSON序列化选项
/// </summary>
public class JsonSerializationOptions
{
    /// <summary>
    /// 是否使用驼峰命名
    /// </summary>
    public bool UseCamelCase { get; set; } = true;

    /// <summary>
    /// 是否忽略null值
    /// </summary>
    public bool IgnoreNullValues { get; set; } = true;

    /// <summary>
    /// 是否格式化输出
    /// </summary>
    public bool WriteIndented { get; set; } = false;

    /// <summary>
    /// 是否允许注释
    /// </summary>
    public bool AllowComments { get; set; } = false;

    /// <summary>
    /// 是否允许尾随逗号
    /// </summary>
    public bool AllowTrailingCommas { get; set; } = false;
}
