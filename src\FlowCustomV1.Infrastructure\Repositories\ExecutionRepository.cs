using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using FlowCustomV1.Core.Interfaces.Repositories;
using FlowCustomV1.Core.Models.Workflow;
using FlowCustomV1.Core.Models.Executor;
using FlowCustomV1.Core.Models.Common;
using FlowCustomV1.Infrastructure.Data;
using FlowCustomV1.Infrastructure.Mappers;

namespace FlowCustomV1.Infrastructure.Repositories;

/// <summary>
/// 执行历史仓储实现（重构后统一版本）
/// 提供工作流实例和节点执行记录的持久化和查询功能
/// 实现统一的IExecutionRepository接口
/// </summary>
public class ExecutionRepository : IExecutionRepository
{
    private readonly IDbContextFactory<WorkflowDbContext> _dbContextFactory;
    private readonly ILogger<ExecutionRepository> _logger;

    private static readonly JsonSerializerOptions JsonOptions = new()
    {
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        WriteIndented = false
    };

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="dbContextFactory">数据库上下文工厂</param>
    /// <param name="logger">日志记录器</param>
    public ExecutionRepository(IDbContextFactory<WorkflowDbContext> dbContextFactory, ILogger<ExecutionRepository> logger)
    {
        _dbContextFactory = dbContextFactory;
        _logger = logger;
    }

    /// <summary>
    /// 执行数据库操作的辅助方法
    /// </summary>
    /// <typeparam name="T">返回类型</typeparam>
    /// <param name="operation">数据库操作</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    private async Task<T> ExecuteWithDbContextAsync<T>(Func<WorkflowDbContext, Task<T>> operation, CancellationToken cancellationToken = default)
    {
        await using var dbContext = await _dbContextFactory.CreateDbContextAsync(cancellationToken);
        return await operation(dbContext);
    }

    /// <summary>
    /// 保存工作流实例
    /// </summary>
    /// <param name="instance">工作流实例数据</param>
    /// <param name="inputData">输入数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>实例ID</returns>
    public async Task<string> SaveWorkflowInstanceAsync(WorkflowExecutionResult instance, Dictionary<string, object>? inputData = null, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("保存工作流实例: {InstanceId}", instance.ExecutionId);

            // 使用独立的DbContext避免并发问题
            await using var dbContext = await _dbContextFactory.CreateDbContextAsync(cancellationToken);

            var entity = instance.ToEntity();

            // 设置输入数据
            if (inputData != null && inputData.Any())
            {
                entity.InputData = JsonSerializer.Serialize(inputData, JsonOptions);
            }

            await dbContext.WorkflowInstances.AddAsync(entity, cancellationToken);
            var result = await dbContext.SaveChangesAsync(cancellationToken);

            if (result > 0)
            {
                _logger.LogInformation("工作流实例保存成功: {InstanceId}", instance.ExecutionId);
                return instance.ExecutionId;
            }

            _logger.LogWarning("工作流实例保存失败: {InstanceId}", instance.ExecutionId);
            return string.Empty;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存工作流实例失败: {InstanceId}", instance.ExecutionId);
            return string.Empty;
        }
    }

    /// <summary>
    /// 更新工作流实例
    /// </summary>
    /// <param name="instance">工作流实例数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>更新是否成功</returns>
    public async Task<bool> UpdateWorkflowInstanceAsync(WorkflowExecutionResult instance, CancellationToken cancellationToken = default)
    {
        return await ExecuteWithDbContextAsync(async dbContext =>
        {
            _logger.LogDebug("更新工作流实例: {InstanceId}", instance.ExecutionId);

            var existingEntity = await dbContext.WorkflowInstances
                .FirstOrDefaultAsync(w => w.InstanceId == instance.ExecutionId, cancellationToken);

            if (existingEntity == null)
            {
                _logger.LogWarning("要更新的工作流实例不存在: {InstanceId}", instance.ExecutionId);
                return false;
            }

            // 更新实体属性
            existingEntity.State = instance.State;
            existingEntity.IsSuccess = instance.IsSuccess;
            existingEntity.CompletedAt = instance.CompletedAt;
            existingEntity.OutputData = instance.OutputData.Any() ?
                JsonSerializer.Serialize(instance.OutputData, JsonOptions) : null;
            existingEntity.ErrorMessage = instance.ErrorMessage;
            existingEntity.Message = instance.Message;
            existingEntity.Stats = JsonSerializer.Serialize(instance.Stats, JsonOptions);
            existingEntity.Metadata = instance.Metadata.Any() ?
                JsonSerializer.Serialize(instance.Metadata, JsonOptions) : null;
            existingEntity.LastUpdatedAt = DateTime.UtcNow;

            var result = await dbContext.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("工作流实例更新成功: {InstanceId}", instance.ExecutionId);
            return result > 0;
        }, cancellationToken);
    }

    /// <summary>
    /// 根据实例ID获取工作流实例
    /// </summary>
    /// <param name="instanceId">实例ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>工作流实例</returns>
    public async Task<WorkflowExecutionResult?> GetWorkflowInstanceAsync(string instanceId, CancellationToken cancellationToken = default)
    {
        return await ExecuteWithDbContextAsync(async dbContext =>
        {
            _logger.LogDebug("获取工作流实例: {InstanceId}", instanceId);

            var entity = await dbContext.WorkflowInstances
                .FirstOrDefaultAsync(w => w.InstanceId == instanceId, cancellationToken);

            if (entity == null)
            {
                _logger.LogWarning("工作流实例不存在: {InstanceId}", instanceId);
                return null;
            }

            return entity.ToDomain();
        }, cancellationToken);
    }

    /// <summary>
    /// 保存节点执行记录
    /// </summary>
    /// <param name="execution">节点执行记录</param>
    /// <param name="instanceId">工作流实例ID</param>
    /// <param name="nodeType">节点类型</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>保存是否成功</returns>
    public async Task<bool> SaveNodeExecutionAsync(NodeExecutionResult execution, string instanceId, string nodeType, CancellationToken cancellationToken = default)
    {
        return await ExecuteWithDbContextAsync(async dbContext =>
        {
            _logger.LogDebug("保存节点执行记录: {NodeId} in {InstanceId}", execution.NodeId, instanceId);

            var entity = execution.ToEntity(instanceId);
            entity.NodeType = nodeType;

            await dbContext.NodeExecutions.AddAsync(entity, cancellationToken);
            var result = await dbContext.SaveChangesAsync(cancellationToken);

            if (result > 0)
            {
                _logger.LogInformation("节点执行记录保存成功: {NodeId} in {InstanceId}", execution.NodeId, instanceId);
                return true;
            }

            _logger.LogWarning("节点执行记录保存失败: {NodeId} in {InstanceId}", execution.NodeId, instanceId);
            return false;
        }, cancellationToken);
    }

    /// <summary>
    /// 更新节点执行记录
    /// </summary>
    /// <param name="execution">节点执行记录</param>
    /// <param name="instanceId">工作流实例ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>更新是否成功</returns>
    public async Task<bool> UpdateNodeExecutionAsync(NodeExecutionResult execution, string instanceId, CancellationToken cancellationToken = default)
    {
        return await ExecuteWithDbContextAsync(async dbContext =>
        {
            _logger.LogDebug("更新节点执行记录: {NodeId} in {InstanceId}", execution.NodeId, instanceId);

            var existingEntity = await dbContext.NodeExecutions
                .FirstOrDefaultAsync(n => n.InstanceId == instanceId && n.NodeId == execution.NodeId, cancellationToken);

            if (existingEntity == null)
            {
                _logger.LogWarning("要更新的节点执行记录不存在: {NodeId} in {InstanceId}", execution.NodeId, instanceId);
                return false;
            }

            // 更新实体属性
            existingEntity.State = execution.State;
            existingEntity.IsSuccess = execution.IsSuccess;
            existingEntity.CompletedAt = execution.CompletedAt;
            existingEntity.OutputData = execution.OutputData.Any() ?
                JsonSerializer.Serialize(execution.OutputData, JsonOptions) : null;
            existingEntity.ErrorMessage = execution.ErrorMessage;
            existingEntity.RetryCount = execution.RetryCount;
            existingEntity.ExecutionLogs = execution.ExecutionLogs.Any() ?
                JsonSerializer.Serialize(execution.ExecutionLogs, JsonOptions) : null;
            existingEntity.PerformanceMetrics = execution.PerformanceMetrics.Any() ?
                JsonSerializer.Serialize(execution.PerformanceMetrics, JsonOptions) : null;
            existingEntity.Metadata = execution.Metadata.Any() ?
                JsonSerializer.Serialize(execution.Metadata, JsonOptions) : null;
            existingEntity.LastUpdatedAt = DateTime.UtcNow;

            var result = await dbContext.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("节点执行记录更新成功: {NodeId} in {InstanceId}", execution.NodeId, instanceId);
            return result > 0;
        }, cancellationToken);
    }

    /// <summary>
    /// 获取工作流实例的所有节点执行记录
    /// </summary>
    /// <param name="instanceId">工作流实例ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>节点执行记录列表</returns>
    public async Task<IEnumerable<NodeExecutionResult>> GetNodeExecutionsAsync(string instanceId, CancellationToken cancellationToken = default)
    {
        return await ExecuteWithDbContextAsync(async dbContext =>
        {
            _logger.LogDebug("获取节点执行记录: {InstanceId}", instanceId);

            var entities = await dbContext.NodeExecutions
                .Where(n => n.InstanceId == instanceId)
                .OrderBy(n => n.StartedAt)
                .ToListAsync(cancellationToken);

            return entities.Select(e => e.ToDomain());
        }, cancellationToken);
    }

    /// <summary>
    /// 获取指定节点的执行记录
    /// </summary>
    /// <param name="instanceId">工作流实例ID</param>
    /// <param name="nodeId">节点ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>节点执行记录</returns>
    public async Task<NodeExecutionResult?> GetNodeExecutionAsync(string instanceId, string nodeId, CancellationToken cancellationToken = default)
    {
        return await ExecuteWithDbContextAsync(async dbContext =>
        {
            _logger.LogDebug("获取节点执行记录: {NodeId} in {InstanceId}", nodeId, instanceId);

            var entity = await dbContext.NodeExecutions
                .FirstOrDefaultAsync(n => n.InstanceId == instanceId && n.NodeId == nodeId, cancellationToken);

            return entity?.ToDomain();
        }, cancellationToken);
    }

    /// <summary>
    /// 获取工作流的执行历史
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="pageIndex">页索引</param>
    /// <param name="pageSize">页大小</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>执行历史分页结果</returns>
    public async Task<PagedResult<ExecutionRecord>> GetExecutionHistoryAsync(string workflowId, int pageIndex, int pageSize, CancellationToken cancellationToken = default)
    {
        return await ExecuteWithDbContextAsync(async dbContext =>
        {
            _logger.LogDebug("获取执行历史: {WorkflowId}, 页索引: {PageIndex}, 页大小: {PageSize}", workflowId, pageIndex, pageSize);

            var query = dbContext.WorkflowInstances
                .Where(w => w.WorkflowId == workflowId);

            var totalCount = await query.CountAsync(cancellationToken);

            var skip = pageIndex * pageSize;
            var entities = await query
                .OrderByDescending(w => w.StartedAt)
                .Skip(skip)
                .Take(pageSize)
                .ToListAsync(cancellationToken);

            var records = entities.Select(e => new ExecutionRecord
            {
                ExecutionId = e.InstanceId,
                WorkflowId = e.WorkflowId,
                WorkflowName = e.WorkflowName,
                State = e.State,
                ExecutorNodeId = e.ExecutorNodeId,
                StartedAt = e.StartedAt,
                CompletedAt = e.CompletedAt,
                ErrorMessage = e.ErrorMessage,
                InputDataSize = e.InputData?.Length ?? 0,
                OutputDataSize = e.OutputData?.Length ?? 0,
                CreatedAt = e.CreatedAt
            }).ToList();

            return new PagedResult<ExecutionRecord>
            {
                Items = records,
                TotalCount = totalCount,
                PageIndex = pageIndex,
                PageSize = pageSize
            };
        }, cancellationToken);
    }

    /// <summary>
    /// 获取执行历史（返回WorkflowExecutionResult的分页结果）
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="pageIndex">页索引</param>
    /// <param name="pageSize">页大小</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>执行历史分页结果</returns>
    public async Task<PagedResult<WorkflowExecutionResult>> GetExecutionHistoryResultsAsync(string workflowId, int pageIndex, int pageSize, CancellationToken cancellationToken = default)
    {
        return await ExecuteWithDbContextAsync(async dbContext =>
        {
            _logger.LogDebug("获取执行历史结果: {WorkflowId}, 页索引: {PageIndex}, 页大小: {PageSize}", workflowId, pageIndex, pageSize);

            var skip = pageIndex * pageSize;

            var query = dbContext.WorkflowInstances.Where(w => w.WorkflowId == workflowId);

            var totalCount = await query.CountAsync(cancellationToken);

            var entities = await query
                .OrderByDescending(w => w.StartedAt)
                .Skip(skip)
                .Take(pageSize)
                .ToListAsync(cancellationToken);

            var results = entities.Select(e => e.ToDomain()).ToList();

            return new PagedResult<WorkflowExecutionResult>
            {
                Items = results,
                TotalCount = totalCount,
                PageIndex = pageIndex,
                PageSize = pageSize
            };
        }, cancellationToken);
    }

    /// <summary>
    /// 获取执行历史统计信息
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>统计信息</returns>
    public async Task<FlowCustomV1.Core.Models.Executor.ExecutionStatistics> GetExecutionStatisticsAsync(string workflowId, DateTime? startDate = null, DateTime? endDate = null, CancellationToken cancellationToken = default)
    {
        return await ExecuteWithDbContextAsync(async dbContext =>
        {
            _logger.LogDebug("获取执行统计信息: {WorkflowId}", workflowId);

            var query = dbContext.WorkflowInstances.Where(w => w.WorkflowId == workflowId);

            if (startDate.HasValue)
            {
                query = query.Where(w => w.StartedAt >= startDate.Value);
            }

            if (endDate.HasValue)
            {
                query = query.Where(w => w.StartedAt <= endDate.Value);
            }

            var instances = await query.ToListAsync(cancellationToken);

            var totalExecutions = instances.Count;
            var successfulExecutions = instances.Count(i => i.IsSuccess);
            var failedExecutions = totalExecutions - successfulExecutions;

            var completedInstances = instances.Where(i => i.CompletedAt.HasValue).ToList();
            var executionTimes = completedInstances
                .Select(i => (i.CompletedAt!.Value - i.StartedAt).TotalMilliseconds)
                .ToList();

            var avgExecutionTime = executionTimes.Any() ? executionTimes.Average() : 0;
            var maxExecutionTime = executionTimes.Any() ? executionTimes.Max() : 0;
            var minExecutionTime = executionTimes.Any() ? executionTimes.Min() : 0;

            return new ExecutionStatistics
            {
                TotalExecutions = totalExecutions,
                SuccessfulExecutions = successfulExecutions,
                FailedExecutions = failedExecutions,
                AverageExecutionTimeMs = avgExecutionTime,
                MaxExecutionTimeMs = (long)maxExecutionTime,
                MinExecutionTimeMs = (long)minExecutionTime,
                StartDate = startDate,
                EndDate = endDate
            };
        }, cancellationToken);
    }

    /// <summary>
    /// 删除工作流实例及其相关数据
    /// </summary>
    /// <param name="instanceId">实例ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>删除是否成功</returns>
    public async Task<bool> DeleteWorkflowInstanceAsync(string instanceId, CancellationToken cancellationToken = default)
    {
        return await ExecuteWithDbContextAsync(async dbContext =>
        {
            _logger.LogDebug("删除工作流实例: {InstanceId}", instanceId);

            var instance = await dbContext.WorkflowInstances
                .Include(w => w.NodeExecutions)
                .FirstOrDefaultAsync(w => w.InstanceId == instanceId, cancellationToken);

            if (instance == null)
            {
                _logger.LogWarning("要删除的工作流实例不存在: {InstanceId}", instanceId);
                return false;
            }

            // 删除相关的节点执行记录
            dbContext.NodeExecutions.RemoveRange(instance.NodeExecutions);

            // 删除工作流实例
            dbContext.WorkflowInstances.Remove(instance);

            var result = await dbContext.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("工作流实例删除成功: {InstanceId}", instanceId);
            return result > 0;
        }, cancellationToken);
    }

    /// <summary>
    /// 清理过期的执行记录
    /// </summary>
    /// <param name="retentionDays">保留天数</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>清理的记录数</returns>
    public async Task<int> CleanupExpiredRecordsAsync(int retentionDays = 90, CancellationToken cancellationToken = default)
    {
        return await ExecuteWithDbContextAsync(async dbContext =>
        {
            _logger.LogInformation("开始清理过期执行记录，保留天数: {RetentionDays}", retentionDays);

            var cutoffDate = DateTime.UtcNow.AddDays(-retentionDays);

            var expiredInstances = await dbContext.WorkflowInstances
                .Include(w => w.NodeExecutions)
                .Where(w => w.StartedAt < cutoffDate)
                .ToListAsync(cancellationToken);

            if (!expiredInstances.Any())
            {
                _logger.LogInformation("没有找到过期的执行记录");
                return 0;
            }

            // 删除过期的节点执行记录
            var expiredNodeExecutions = expiredInstances.SelectMany(i => i.NodeExecutions);
            dbContext.NodeExecutions.RemoveRange(expiredNodeExecutions);

            // 删除过期的工作流实例
            dbContext.WorkflowInstances.RemoveRange(expiredInstances);

            var result = await dbContext.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("清理过期执行记录完成，删除了 {Count} 个实例", expiredInstances.Count);
            return expiredInstances.Count;
        }, cancellationToken);
    }

    /// <summary>
    /// 搜索执行记录
    /// </summary>
    /// <param name="criteria">搜索条件</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>搜索结果</returns>
    public async Task<IEnumerable<WorkflowExecutionResult>> SearchExecutionsAsync(ExecutionSearchCriteria criteria, CancellationToken cancellationToken = default)
    {
        return await ExecuteWithDbContextAsync(async dbContext =>
        {
            _logger.LogDebug("搜索执行记录: {Keyword}", criteria.Keyword);

            var query = dbContext.WorkflowInstances.AsQueryable();

            // 工作流ID过滤
            if (!string.IsNullOrEmpty(criteria.WorkflowId))
            {
                query = query.Where(w => w.WorkflowId == criteria.WorkflowId);
            }

            // 状态过滤
            if (criteria.State.HasValue)
            {
                query = query.Where(w => w.State == criteria.State.Value);
            }

            // 成功状态过滤
            if (criteria.IsSuccess.HasValue)
            {
                query = query.Where(w => w.IsSuccess == criteria.IsSuccess.Value);
            }

            // 开始时间范围过滤
            if (criteria.StartedAfter.HasValue)
            {
                query = query.Where(w => w.StartedAt >= criteria.StartedAfter.Value);
            }

            if (criteria.StartedBefore.HasValue)
            {
                query = query.Where(w => w.StartedAt <= criteria.StartedBefore.Value);
            }

            // 完成时间范围过滤
            if (criteria.CompletedAfter.HasValue)
            {
                query = query.Where(w => w.CompletedAt >= criteria.CompletedAfter.Value);
            }

            if (criteria.CompletedBefore.HasValue)
            {
                query = query.Where(w => w.CompletedAt <= criteria.CompletedBefore.Value);
            }

            // 关键词搜索
            if (!string.IsNullOrEmpty(criteria.Keyword))
            {
                var keyword = criteria.Keyword.ToLower();
                query = query.Where(w =>
                    (w.ErrorMessage != null && w.ErrorMessage.ToLower().Contains(keyword)) ||
                    (w.Message != null && w.Message.ToLower().Contains(keyword)));
            }

            // 排序
            query = criteria.SortBy.ToLower() switch
            {
                "completedat" => criteria.SortDescending ?
                    query.OrderByDescending(w => w.CompletedAt) :
                    query.OrderBy(w => w.CompletedAt),
                "state" => criteria.SortDescending ?
                    query.OrderByDescending(w => w.State) :
                    query.OrderBy(w => w.State),
                "issuccess" => criteria.SortDescending ?
                    query.OrderByDescending(w => w.IsSuccess) :
                    query.OrderBy(w => w.IsSuccess),
                _ => criteria.SortDescending ?
                    query.OrderByDescending(w => w.StartedAt) :
                    query.OrderBy(w => w.StartedAt)
            };

            // 分页
            var skip = (criteria.PageNumber - 1) * criteria.PageSize;
            query = query.Skip(skip).Take(criteria.PageSize);

            var entities = await query.ToListAsync(cancellationToken);
            return entities.Select(e => e.ToDomain());
        }, cancellationToken);
    }

    // ========== 以下是统一接口中缺失的方法实现 ==========

    /// <summary>
    /// 保存执行结果
    /// </summary>
    /// <param name="executionResult">执行结果</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>保存任务</returns>
    public async Task SaveExecutionResultAsync(WorkflowExecutionResult executionResult, CancellationToken cancellationToken = default)
    {
        await ExecuteWithDbContextAsync(async dbContext =>
        {
            _logger.LogDebug("保存执行结果: {ExecutionId}", executionResult.ExecutionId);

            var entity = executionResult.ToEntity();

            var existingEntity = await dbContext.WorkflowInstances
                .FirstOrDefaultAsync(w => w.InstanceId == executionResult.ExecutionId, cancellationToken);

            if (existingEntity != null)
            {
                // 更新现有记录
                existingEntity.State = executionResult.State;
                existingEntity.IsSuccess = executionResult.IsSuccess;
                existingEntity.ErrorMessage = executionResult.ErrorMessage;
                existingEntity.CompletedAt = executionResult.CompletedAt;
                existingEntity.OutputData = JsonSerializer.Serialize(executionResult.OutputData, JsonOptions);
                existingEntity.LastModifiedAt = DateTime.UtcNow;
            }
            else
            {
                // 创建新记录
                await dbContext.WorkflowInstances.AddAsync(entity, cancellationToken);
            }

            await dbContext.SaveChangesAsync(cancellationToken);
            return true;
        }, cancellationToken);
    }

    /// <summary>
    /// 获取执行结果
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>执行结果</returns>
    public async Task<WorkflowExecutionResult?> GetExecutionResultAsync(string executionId, CancellationToken cancellationToken = default)
    {
        return await ExecuteWithDbContextAsync(async dbContext =>
        {
            _logger.LogDebug("获取执行结果: {ExecutionId}", executionId);

            var entity = await dbContext.WorkflowInstances
                .FirstOrDefaultAsync(w => w.InstanceId == executionId, cancellationToken);

            return entity?.ToDomain();
        }, cancellationToken);
    }

    /// <summary>
    /// 获取正在运行的执行列表
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>正在运行的执行列表</returns>
    public async Task<IReadOnlyList<ExecutionRecord>> GetRunningExecutionsAsync(CancellationToken cancellationToken = default)
    {
        return await ExecuteWithDbContextAsync(async dbContext =>
        {
            _logger.LogDebug("获取正在运行的执行列表");

            var runningStates = new[] { WorkflowExecutionState.Running, WorkflowExecutionState.Waiting };

            var entities = await dbContext.WorkflowInstances
                .Where(w => runningStates.Contains(w.State))
                .OrderBy(w => w.StartedAt)
                .ToListAsync(cancellationToken);

            return entities.Select(e => new ExecutionRecord
            {
                ExecutionId = e.InstanceId,
                WorkflowId = e.WorkflowId,
                WorkflowName = e.WorkflowName,
                State = e.State,
                ExecutorNodeId = e.ExecutorNodeId,
                StartedAt = e.StartedAt,
                CompletedAt = e.CompletedAt,
                ErrorMessage = e.ErrorMessage,
                InputDataSize = e.InputData?.Length ?? 0,
                OutputDataSize = e.OutputData?.Length ?? 0,
                CreatedAt = e.CreatedAt
            }).ToList().AsReadOnly();
        }, cancellationToken);
    }

    /// <summary>
    /// 保存执行状态
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="state">执行状态</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>保存任务</returns>
    public async Task SaveExecutionStateAsync(string executionId, WorkflowExecutionState state, CancellationToken cancellationToken = default)
    {
        await ExecuteWithDbContextAsync(async dbContext =>
        {
            _logger.LogDebug("保存执行状态: {ExecutionId} -> {State}", executionId, state);

            var entity = await dbContext.WorkflowInstances
                .FirstOrDefaultAsync(w => w.InstanceId == executionId, cancellationToken);

            if (entity != null)
            {
                entity.State = state;
                entity.LastModifiedAt = DateTime.UtcNow;

                if (state == WorkflowExecutionState.Completed ||
                    state == WorkflowExecutionState.Failed ||
                    state == WorkflowExecutionState.Cancelled)
                {
                    entity.CompletedAt = DateTime.UtcNow;
                }

                await dbContext.SaveChangesAsync(cancellationToken);
            }
            else
            {
                _logger.LogWarning("执行状态保存失败，未找到执行记录: {ExecutionId}", executionId);
            }

            return true;
        }, cancellationToken);
    }

    /// <summary>
    /// 获取执行状态
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>执行状态</returns>
    public async Task<WorkflowExecutionState?> GetExecutionStateAsync(string executionId, CancellationToken cancellationToken = default)
    {
        return await ExecuteWithDbContextAsync(async dbContext =>
        {
            _logger.LogDebug("获取执行状态: {ExecutionId}", executionId);

            var entity = await dbContext.WorkflowInstances
                .FirstOrDefaultAsync(w => w.InstanceId == executionId, cancellationToken);

            return entity?.State;
        }, cancellationToken);
    }

    /// <summary>
    /// 保存执行上下文
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="context">执行上下文</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>保存任务</returns>
    public async Task SaveExecutionContextAsync(string executionId, ExecutionContextSnapshot context, CancellationToken cancellationToken = default)
    {
        await ExecuteWithDbContextAsync(async dbContext =>
        {
            _logger.LogDebug("保存执行上下文: {ExecutionId}", executionId);

            var entity = await dbContext.WorkflowInstances
                .FirstOrDefaultAsync(w => w.InstanceId == executionId, cancellationToken);

            if (entity != null)
            {
                // 将执行上下文序列化并保存到InputData字段（临时方案）
                var contextJson = JsonSerializer.Serialize(context, JsonOptions);
                entity.InputData = contextJson;
                entity.LastModifiedAt = DateTime.UtcNow;

                await dbContext.SaveChangesAsync(cancellationToken);
            }
            else
            {
                _logger.LogWarning("执行上下文保存失败，未找到执行记录: {ExecutionId}", executionId);
            }

            return true;
        }, cancellationToken);
    }

    /// <summary>
    /// 获取执行上下文
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>执行上下文</returns>
    public async Task<ExecutionContextSnapshot?> GetExecutionContextAsync(string executionId, CancellationToken cancellationToken = default)
    {
        return await ExecuteWithDbContextAsync(async dbContext =>
        {
            _logger.LogDebug("获取执行上下文: {ExecutionId}", executionId);

            var entity = await dbContext.WorkflowInstances
                .FirstOrDefaultAsync(w => w.InstanceId == executionId, cancellationToken);

            if (entity?.InputData != null)
            {
                try
                {
                    return JsonSerializer.Deserialize<ExecutionContextSnapshot>(entity.InputData, JsonOptions);
                }
                catch (JsonException ex)
                {
                    _logger.LogWarning(ex, "执行上下文反序列化失败: {ExecutionId}", executionId);
                }
            }

            return null;
        }, cancellationToken);
    }

    /// <summary>
    /// 清理过期的执行记录
    /// </summary>
    /// <param name="expirationTime">过期时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>清理的记录数</returns>
    public async Task<int> CleanupExpiredExecutionsAsync(TimeSpan expirationTime, CancellationToken cancellationToken = default)
    {
        return await ExecuteWithDbContextAsync(async dbContext =>
        {
            _logger.LogInformation("开始清理过期执行记录，过期时间: {ExpirationTime}", expirationTime);

            var cutoffDate = DateTime.UtcNow.Subtract(expirationTime);

            var expiredInstances = await dbContext.WorkflowInstances
                .Include(w => w.NodeExecutions)
                .Where(w => w.StartedAt < cutoffDate)
                .ToListAsync(cancellationToken);

            if (!expiredInstances.Any())
            {
                _logger.LogInformation("没有找到过期的执行记录");
                return 0;
            }

            // 删除节点执行记录
            var nodeExecutions = expiredInstances.SelectMany(w => w.NodeExecutions).ToList();
            dbContext.NodeExecutions.RemoveRange(nodeExecutions);

            // 删除工作流实例
            dbContext.WorkflowInstances.RemoveRange(expiredInstances);

            var deletedCount = await dbContext.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("清理完成，删除了 {DeletedCount} 条过期执行记录", expiredInstances.Count);
            return expiredInstances.Count;
        }, cancellationToken);
    }

    /// <summary>
    /// 获取执行统计信息（统一接口版本）
    /// </summary>
    /// <param name="workflowId">工作流ID（可选）</param>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>执行统计</returns>
    public async Task<ExecutionStatistics> GetExecutionStatisticsAsync(string? workflowId, DateTime startTime, DateTime endTime, CancellationToken cancellationToken = default)
    {
        // 调用现有的方法实现，注意参数顺序和类型
        return await GetExecutionStatisticsAsync(workflowId ?? string.Empty, (DateTime?)startTime, (DateTime?)endTime, cancellationToken);
    }



    /// <summary>
    /// 删除单个执行记录
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>删除任务</returns>
    public async Task DeleteExecutionAsync(string executionId, CancellationToken cancellationToken = default)
    {
        await ExecuteWithDbContextAsync(async dbContext =>
        {
            _logger.LogDebug("删除执行记录: {ExecutionId}", executionId);

            var instance = await dbContext.WorkflowInstances
                .Include(w => w.NodeExecutions)
                .FirstOrDefaultAsync(w => w.InstanceId == executionId, cancellationToken);

            if (instance != null)
            {
                // 删除节点执行记录
                dbContext.NodeExecutions.RemoveRange(instance.NodeExecutions);

                // 删除工作流实例
                dbContext.WorkflowInstances.Remove(instance);

                await dbContext.SaveChangesAsync(cancellationToken);
            }

            return true;
        }, cancellationToken);
    }

    /// <summary>
    /// 批量删除执行记录
    /// </summary>
    /// <param name="executionIds">执行ID列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>删除任务</returns>
    public async Task DeleteExecutionsAsync(IEnumerable<string> executionIds, CancellationToken cancellationToken = default)
    {
        await ExecuteWithDbContextAsync(async dbContext =>
        {
            var idList = executionIds.ToList();
            _logger.LogDebug("批量删除执行记录: {Count} 条", idList.Count);

            var instances = await dbContext.WorkflowInstances
                .Include(w => w.NodeExecutions)
                .Where(w => idList.Contains(w.InstanceId))
                .ToListAsync(cancellationToken);

            if (instances.Any())
            {
                // 删除节点执行记录
                var nodeExecutions = instances.SelectMany(w => w.NodeExecutions).ToList();
                dbContext.NodeExecutions.RemoveRange(nodeExecutions);

                // 删除工作流实例
                dbContext.WorkflowInstances.RemoveRange(instances);

                await dbContext.SaveChangesAsync(cancellationToken);
            }

            return true;
        }, cancellationToken);
    }

    /// <summary>
    /// 搜索执行记录（ExecutionSearchQuery版本）
    /// </summary>
    /// <param name="query">查询条件</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>搜索结果</returns>
    public async Task<PagedResult<ExecutionRecord>> SearchExecutionsAsync(ExecutionSearchQuery query, CancellationToken cancellationToken = default)
    {
        return await ExecuteWithDbContextAsync(async dbContext =>
        {
            _logger.LogDebug("搜索执行记录（分页）: {Query}", JsonSerializer.Serialize(query, JsonOptions));

            var dbQuery = dbContext.WorkflowInstances.AsQueryable();

            // 应用过滤条件
            if (!string.IsNullOrEmpty(query.WorkflowId))
            {
                dbQuery = dbQuery.Where(w => w.WorkflowId == query.WorkflowId);
            }

            if (query.States != null && query.States.Any())
            {
                dbQuery = dbQuery.Where(w => query.States.Contains(w.State));
            }

            if (!string.IsNullOrEmpty(query.ExecutorNodeId))
            {
                dbQuery = dbQuery.Where(w => w.ExecutorNodeId == query.ExecutorNodeId);
            }

            if (query.StartedAfter.HasValue)
            {
                dbQuery = dbQuery.Where(w => w.StartedAt >= query.StartedAfter.Value);
            }

            if (query.StartedBefore.HasValue)
            {
                dbQuery = dbQuery.Where(w => w.StartedAt <= query.StartedBefore.Value);
            }

            if (query.CompletedAfter.HasValue)
            {
                dbQuery = dbQuery.Where(w => w.CompletedAt >= query.CompletedAfter.Value);
            }

            if (query.CompletedBefore.HasValue)
            {
                dbQuery = dbQuery.Where(w => w.CompletedAt <= query.CompletedBefore.Value);
            }

            if (query.HasError.HasValue)
            {
                if (query.HasError.Value)
                {
                    dbQuery = dbQuery.Where(w => !string.IsNullOrEmpty(w.ErrorMessage));
                }
                else
                {
                    dbQuery = dbQuery.Where(w => string.IsNullOrEmpty(w.ErrorMessage));
                }
            }

            // 获取总数
            var totalCount = await dbQuery.CountAsync(cancellationToken);

            // 应用排序
            dbQuery = (query.SortBy?.ToLower()) switch
            {
                "completedat" => dbQuery.OrderByDescending(w => w.CompletedAt),
                "state" => dbQuery.OrderBy(w => w.State),
                "workflowid" => dbQuery.OrderBy(w => w.WorkflowId),
                _ => dbQuery.OrderByDescending(w => w.StartedAt)
            };

            // 应用分页
            var skip = query.PageIndex * query.PageSize;
            var entities = await dbQuery.Skip(skip).Take(query.PageSize).ToListAsync(cancellationToken);

            var records = entities.Select(e => new ExecutionRecord
            {
                ExecutionId = e.InstanceId,
                WorkflowId = e.WorkflowId,
                WorkflowName = e.WorkflowName,
                State = e.State,
                ExecutorNodeId = e.ExecutorNodeId,
                StartedAt = e.StartedAt,
                CompletedAt = e.CompletedAt,
                ErrorMessage = e.ErrorMessage,
                InputDataSize = e.InputData?.Length ?? 0,
                OutputDataSize = e.OutputData?.Length ?? 0,
                CreatedAt = e.CreatedAt
            }).ToList();

            return new PagedResult<ExecutionRecord>
            {
                Items = records,
                TotalCount = totalCount,
                PageIndex = query.PageIndex,
                PageSize = query.PageSize
            };
        }, cancellationToken);
    }
}
