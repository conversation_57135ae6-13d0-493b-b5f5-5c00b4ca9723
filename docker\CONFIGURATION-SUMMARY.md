# FlowCustomV1 Docker配置总结

## 📋 重组完成状态

Docker配置已完全重组，每个环境都包含独立的配置文件，便于容器启动时挂载。

### 🎯 新的目录结构

```
docker/
├── development/          # 开发环境 (默认端口)
│   ├── docker-compose.yml
│   ├── start-dev-env.py
│   ├── Dockerfile.* (7个)
│   ├── nats/            # NATS配置文件
│   │   └── nats-1.conf  # 单节点配置
│   └── mysql/           # MySQL配置文件
│       └── init/
│           └── 01-init-database.sql
├── testing/             # 测试环境 (端口+20000)
│   ├── docker-compose.yml
│   ├── start-test-cluster.py
│   ├── Dockerfile.* (8个，包含multi)
│   ├── nats/            # NATS集群配置文件
│   │   ├── nats-1.conf  # 集群节点1
│   │   ├── nats-2.conf  # 集群节点2
│   │   └── nats-3.conf  # 集群节点3
│   └── mysql/           # MySQL配置文件
│       └── init/
│           └── 01-init-database.sql
└── production/          # 生产环境 (端口+10000)
    ├── docker-compose.yml
    ├── start-prod-cluster.py
    ├── Dockerfile.* (7个)
    ├── nats/            # NATS集群配置文件
    │   ├── nats-1.conf  # 集群节点1
    │   ├── nats-2.conf  # 集群节点2
    │   └── nats-3.conf  # 集群节点3
    └── mysql/           # MySQL配置文件
        └── init/
            └── 01-init-database.sql
```

### ✅ 配置文件挂载

每个环境的Docker Compose文件现在使用本地路径挂载配置：

#### NATS配置挂载
```yaml
# 开发环境
volumes:
  - ./nats/nats-1.conf:/nats-server.conf:ro

# 测试/生产环境
volumes:
  - ./nats/nats-1.conf:/nats-server.conf:ro  # 节点1
  - ./nats/nats-2.conf:/nats-server.conf:ro  # 节点2
  - ./nats/nats-3.conf:/nats-server.conf:ro  # 节点3
```

#### MySQL配置挂载
```yaml
volumes:
  - ./mysql/init:/docker-entrypoint-initdb.d
```

### 🎯 端口规划

| 环境 | 端口偏移 | NATS | MySQL | API | 监控 |
|------|---------|------|-------|-----|------|
| **开发环境** | +0 | 4222 | 3306 | 5000 | 8222 |
| **生产环境** | +10000 | 14222-14224 | 13306 | 15000 | 18222-18224 |
| **测试环境** | +20000 | 24222-24224 | 23306 | 25000-25001 | 28222-28224 |

### 🚀 启动方式

每个环境都有独立的启动脚本：

```bash
# 开发环境 (单节点)
python docker/development/start-dev-env.py start

# 测试环境 (8节点集群)
python docker/testing/start-test-cluster.py start

# 生产环境 (7节点集群)
python docker/production/start-prod-cluster.py start
```

### 📊 环境对比

| 特性 | 开发环境 | 测试环境 | 生产环境 |
|------|---------|----------|----------|
| **NATS节点** | 1个单节点 | 3节点集群 | 3节点集群 |
| **应用节点** | 1个API节点 | 8个节点 | 7个节点 |
| **总容器数** | 3个 | 12个 | 11个 |
| **端口范围** | 3306,4222,5000,8222 | 23306,24222-24224,25000-25001,28222-28224 | 13306,14222-14224,15000,18222-18224 |
| **配置复杂度** | 简单 | 中等 | 复杂 |
| **安全级别** | 低 | 中 | 高 |

### 🔧 配置特点

#### 开发环境特点
- **简化配置**: 单节点NATS，便于开发
- **默认端口**: 无端口偏移，便于本地访问
- **详细日志**: Debug级别，完整错误信息
- **快速启动**: 最少容器，启动速度快

#### 测试环境特点
- **完整集群**: 3节点NATS集群，模拟生产
- **端口隔离**: +20000偏移，避免冲突
- **多角色测试**: 包含multi-role节点
- **集成测试**: 支持完整的故障转移测试

#### 生产环境特点
- **高可用**: 3节点NATS集群
- **安全配置**: TLS加密，强认证
- **性能优化**: 生产级资源配置
- **监控完整**: 完整的健康检查

### 🛠️ 管理命令

#### 环境管理
```bash
# 启动环境
python docker/{environment}/start-{env}-{type}.py start

# 检查状态
python docker/{environment}/start-{env}-{type}.py status

# 停止环境
python docker/{environment}/start-{env}-{type}.py stop

# 清理环境
python docker/{environment}/start-{env}-{type}.py cleanup
```

#### 配置验证
```bash
# 验证所有配置
python scripts/validate-docker-config.py

# 重组配置结构 (如需要)
python scripts/reorganize-docker-config.py
```

### ✅ 验证结果

运行 `python scripts/validate-docker-config.py` 的验证结果：

- ✅ **开发环境**: 配置验证通过 (8/8项)
- ✅ **生产环境**: 配置验证通过 (8/8项)  
- ✅ **测试环境**: 配置验证通过 (9/9项)
- ✅ **NATS集群**: 配置验证通过 (所有环境)
- ✅ **端口配置**: 无冲突

### 🎯 使用建议

1. **开发阶段**: 使用开发环境进行日常开发
2. **集成测试**: 使用测试环境进行完整测试
3. **性能测试**: 使用测试环境进行负载测试
4. **生产部署**: 配置生产环境安全参数后部署

### 📝 重组历史

- **v0.0.1.8** (2025-09-07) - Docker配置重组
  - 将NATS和MySQL配置移动到各环境目录
  - 更新Docker Compose文件路径引用
  - 清理旧的集中式配置目录
  - 验证所有环境配置完整性

### 🔍 故障排除

#### 配置文件问题
- 检查配置文件是否存在于正确位置
- 验证Docker Compose文件中的路径引用
- 确认配置文件权限正确

#### 端口冲突
- 使用 `netstat -an | findstr {port}` 检查端口占用
- 停止冲突的服务或容器
- 确认端口偏移配置正确

#### 容器启动失败
- 查看容器日志: `docker logs {container_name}`
- 检查配置文件语法
- 验证依赖服务状态

现在Docker配置已完全重组，结构清晰，每个环境独立，便于管理和部署。
