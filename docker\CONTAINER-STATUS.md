# FlowCustomV1 容器运行状态

## 🚀 三环境容器启动完成

所有三个环境的基础设施容器已成功启动，使用flowcustom-XX项目格式。

### ✅ 开发环境 (flowcustom-dev)

**端口**: 默认端口
**项目名**: flowcustom-dev

| 容器名称 | 镜像 | 状态 | 端口映射 |
|---------|------|------|----------|
| nats-dev-server | nats:2.11.8-alpine | Up 4 minutes | 4222:4222, 8222:8222 |
| mysql-dev | mysql:8.0 | Up 4 minutes | 3306:3306 |

**访问信息**:
- NATS: nats://localhost:4222
- NATS监控: http://localhost:8222
- MySQL: localhost:3306 (用户: flowcustom, 数据库: flowcustom_dev)

### ✅ 测试环境 (flowcustom-test)

**端口**: 默认端口 + 20000
**项目名**: flowcustom-test

| 容器名称 | 镜像 | 状态 | 端口映射 |
|---------|------|------|----------|
| nats-test-server-1 | nats:2.11.8-alpine | Up 46 seconds | 24222:4222, 28222:8222 |
| nats-test-server-2 | nats:2.11.8-alpine | Up 46 seconds | 24223:4222, 28223:8222 |
| nats-test-server-3 | nats:2.11.8-alpine | Up 46 seconds | 24224:4222, 28224:8222 |
| mysql-test | mysql:8.0 | Up 3 minutes | 23306:3306 |

**访问信息**:
- NATS集群: nats://localhost:24222,localhost:24223,localhost:24224
- NATS监控: http://localhost:28222, http://localhost:28223, http://localhost:28224
- MySQL: localhost:23306 (用户: flowcustom, 数据库: flowcustom_test)

### ✅ 生产环境 (flowcustom-prod)

**端口**: 默认端口 + 10000
**项目名**: flowcustom-prod

| 容器名称 | 镜像 | 状态 | 端口映射 |
|---------|------|------|----------|
| nats-prod-server-1 | nats:2.11.8-alpine | Up 22 seconds | 14222:4222, 18222:8222 |
| nats-prod-server-2 | nats:2.11.8-alpine | Up 26 seconds | 14223:4222, 18223:8222 |
| nats-prod-server-3 | nats:2.11.8-alpine | Up 24 seconds | 14224:4222, 18224:8222 |
| mysql-prod | mysql:8.0 | Up 3 minutes | 13306:3306 |

**访问信息**:
- NATS集群: nats://localhost:14222,localhost:14223,localhost:14224
- NATS监控: http://localhost:18222, http://localhost:18223, http://localhost:18224
- MySQL: localhost:13306 (用户: flowcustom, 数据库: flowcustom_prod)

## 📊 端口分配总览

| 环境 | NATS端口 | NATS监控端口 | MySQL端口 | 项目名称 |
|------|---------|-------------|-----------|----------|
| **开发环境** | 4222 | 8222 | 3306 | flowcustom-dev |
| **生产环境** | 14222-14224 | 18222-18224 | 13306 | flowcustom-prod |
| **测试环境** | 24222-24224 | 28222-28224 | 23306 | flowcustom-test |

## 🔧 管理命令

### 查看容器状态
```bash
# 查看所有FlowCustom容器
docker ps --filter name=nats --filter name=mysql

# 查看特定环境
docker ps --filter name=dev     # 开发环境
docker ps --filter name=test    # 测试环境  
docker ps --filter name=prod    # 生产环境
```

### 环境管理
```bash
# 开发环境
docker-compose -f docker/development/docker-compose.yml ps
docker-compose -f docker/development/docker-compose.yml logs

# 测试环境
docker-compose -f docker/testing/docker-compose.yml ps
docker-compose -f docker/testing/docker-compose.yml logs

# 生产环境
docker-compose -f docker/production/docker-compose.yml ps
docker-compose -f docker/production/docker-compose.yml logs
```

### 服务验证
```bash
# 验证NATS连接
nats server info --server=localhost:4222   # 开发环境
nats server info --server=localhost:24222  # 测试环境
nats server info --server=localhost:14222  # 生产环境

# 验证MySQL连接
mysql -h localhost -P 3306 -u flowcustom -p   # 开发环境
mysql -h localhost -P 23306 -u flowcustom -p  # 测试环境
mysql -h localhost -P 13306 -u flowcustom -p  # 生产环境
```

## 🎯 下一步操作

现在基础设施已就绪，可以启动应用节点：

### 启动应用节点
```bash
# 开发环境 - 启动API节点
docker-compose -f docker/development/docker-compose.yml up -d api-node

# 测试环境 - 启动所有应用节点
docker-compose -f docker/testing/docker-compose.yml up -d api-node worker-node designer-node validator-node executor-node monitor-node scheduler-node multi-role-node

# 生产环境 - 启动所有应用节点
docker-compose -f docker/production/docker-compose.yml up -d api-node worker-node designer-node validator-node executor-node monitor-node scheduler-node
```

### 健康检查
```bash
# 检查API健康状态
curl http://localhost:5000/health   # 开发环境
curl http://localhost:25000/health  # 测试环境
curl http://localhost:15000/health  # 生产环境
```

## 📝 配置修复记录

在启动过程中修复了以下配置问题：

1. **NATS日志配置**: 将所有环境的NATS配置中的 `log_file` 注释掉，使用标准输出
2. **项目名称**: 为每个环境的Docker Compose添加了项目名称
   - 开发环境: `name: flowcustom-dev`
   - 测试环境: `name: flowcustom-test`
   - 生产环境: `name: flowcustom-prod`

## ✅ 启动验证

- ✅ **开发环境**: 2/2 基础设施容器运行正常
- ✅ **测试环境**: 4/4 基础设施容器运行正常
- ✅ **生产环境**: 4/4 基础设施容器运行正常
- ✅ **端口隔离**: 三个环境端口完全隔离，无冲突
- ✅ **项目命名**: 使用flowcustom-XX格式，便于管理

现在三个环境的基础设施已完全就绪，可以开始启动应用节点进行完整的集群测试。
