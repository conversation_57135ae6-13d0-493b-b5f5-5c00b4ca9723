using FlowCustomV1.Core.Models.Workflow;

namespace FlowCustomV1.Engine.StateTracking;

/// <summary>
/// 执行状态跟踪器接口
/// 负责跟踪和管理工作流和节点的执行状态
/// </summary>
public interface IExecutionStateTracker
{
    /// <summary>
    /// 跟踪工作流状态变更
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="state">新状态</param>
    /// <param name="reason">状态变更原因</param>
    /// <param name="metadata">状态元数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>跟踪任务</returns>
    Task TrackWorkflowStateAsync(
        string executionId, 
        WorkflowExecutionState state, 
        string? reason = null,
        Dictionary<string, object>? metadata = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 跟踪节点状态变更
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="nodeId">节点ID</param>
    /// <param name="state">新状态</param>
    /// <param name="reason">状态变更原因</param>
    /// <param name="metadata">状态元数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>跟踪任务</returns>
    Task TrackNodeStateAsync(
        string executionId, 
        string nodeId, 
        NodeExecutionState state, 
        string? reason = null,
        Dictionary<string, object>? metadata = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取工作流当前状态
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>工作流状态</returns>
    Task<WorkflowExecutionState> GetWorkflowStateAsync(
        string executionId, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取节点当前状态
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="nodeId">节点ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>节点状态</returns>
    Task<NodeExecutionState> GetNodeStateAsync(
        string executionId, 
        string nodeId, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取工作流的所有节点状态
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>节点状态映射</returns>
    Task<Dictionary<string, NodeExecutionState>> GetAllNodeStatesAsync(
        string executionId, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取工作流状态历史
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>状态历史列表</returns>
    Task<List<WorkflowStateHistoryEntry>> GetWorkflowStateHistoryAsync(
        string executionId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取工作流历史（兼容性方法）
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>状态历史列表</returns>
    Task<List<WorkflowStateHistoryEntry>> GetWorkflowHistoryAsync(
        string executionId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取节点状态历史
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="nodeId">节点ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>状态历史列表</returns>
    Task<List<NodeStateHistoryEntry>> GetNodeStateHistoryAsync(
        string executionId, 
        string nodeId, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 清理指定工作流的状态数据
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>清理任务</returns>
    Task CleanupWorkflowStateAsync(
        string executionId, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 清理过期的状态数据
    /// </summary>
    /// <param name="olderThan">清理早于此时间的数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>清理任务</returns>
    Task CleanupExpiredStatesAsync(
        DateTime olderThan, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 工作流状态变更事件
    /// </summary>
    event EventHandler<WorkflowStateChangedEventArgs>? WorkflowStateChanged;

    /// <summary>
    /// 节点状态变更事件
    /// </summary>
    event EventHandler<NodeStateChangedEventArgs>? NodeStateChanged;
}

/// <summary>
/// 工作流状态变更事件参数
/// </summary>
public class WorkflowStateChangedEventArgs : EventArgs
{
    /// <summary>
    /// 执行ID
    /// </summary>
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 工作流ID
    /// </summary>
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 旧状态
    /// </summary>
    public WorkflowExecutionState OldState { get; set; }

    /// <summary>
    /// 新状态
    /// </summary>
    public WorkflowExecutionState NewState { get; set; }

    /// <summary>
    /// 状态变更时间
    /// </summary>
    public DateTime ChangedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 变更原因
    /// </summary>
    public string? Reason { get; set; }

    /// <summary>
    /// 状态元数据
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// 节点状态变更事件参数
/// </summary>
public class NodeStateChangedEventArgs : EventArgs
{
    /// <summary>
    /// 执行ID
    /// </summary>
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 工作流ID
    /// </summary>
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 节点ID
    /// </summary>
    public string NodeId { get; set; } = string.Empty;

    /// <summary>
    /// 旧状态
    /// </summary>
    public NodeExecutionState OldState { get; set; }

    /// <summary>
    /// 新状态
    /// </summary>
    public NodeExecutionState NewState { get; set; }

    /// <summary>
    /// 状态变更时间
    /// </summary>
    public DateTime ChangedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 变更原因
    /// </summary>
    public string? Reason { get; set; }

    /// <summary>
    /// 状态元数据
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// 工作流状态历史条目
/// </summary>
public class WorkflowStateHistoryEntry
{
    /// <summary>
    /// 执行ID
    /// </summary>
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 工作流ID
    /// </summary>
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 状态
    /// </summary>
    public WorkflowExecutionState State { get; set; }

    /// <summary>
    /// 状态变更时间
    /// </summary>
    public DateTime ChangedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 变更原因
    /// </summary>
    public string? Reason { get; set; }

    /// <summary>
    /// 状态元数据
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// 节点状态历史条目
/// </summary>
public class NodeStateHistoryEntry
{
    /// <summary>
    /// 执行ID
    /// </summary>
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 工作流ID
    /// </summary>
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 节点ID
    /// </summary>
    public string NodeId { get; set; } = string.Empty;

    /// <summary>
    /// 状态
    /// </summary>
    public NodeExecutionState State { get; set; }

    /// <summary>
    /// 状态变更时间
    /// </summary>
    public DateTime ChangedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 变更原因
    /// </summary>
    public string? Reason { get; set; }

    /// <summary>
    /// 状态元数据
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();
}
