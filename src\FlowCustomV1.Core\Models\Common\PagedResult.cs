using System.Text.Json.Serialization;

namespace FlowCustomV1.Core.Models.Common;

/// <summary>
/// 分页结果模型
/// </summary>
/// <typeparam name="T">数据项类型</typeparam>
public class PagedResult<T>
{
    /// <summary>
    /// 数据项列表
    /// </summary>
    [JsonPropertyName("items")]
    public IReadOnlyList<T> Items { get; set; } = new List<T>();

    /// <summary>
    /// 总记录数
    /// </summary>
    [JsonPropertyName("totalCount")]
    public long TotalCount { get; set; }

    /// <summary>
    /// 页索引（从0开始）
    /// </summary>
    [JsonPropertyName("pageIndex")]
    public int PageIndex { get; set; }

    /// <summary>
    /// 页大小
    /// </summary>
    [JsonPropertyName("pageSize")]
    public int PageSize { get; set; }

    /// <summary>
    /// 总页数
    /// </summary>
    [JsonPropertyName("totalPages")]
    public int TotalPages => PageSize > 0 ? (int)Math.Ceiling((double)TotalCount / PageSize) : 0;

    /// <summary>
    /// 是否有上一页
    /// </summary>
    [JsonPropertyName("hasPreviousPage")]
    public bool HasPreviousPage => PageIndex > 0;

    /// <summary>
    /// 是否有下一页
    /// </summary>
    [JsonPropertyName("hasNextPage")]
    public bool HasNextPage => PageIndex < TotalPages - 1;

    /// <summary>
    /// 当前页起始记录索引（从0开始）
    /// </summary>
    [JsonPropertyName("startIndex")]
    public long StartIndex => PageIndex * PageSize;

    /// <summary>
    /// 当前页结束记录索引（从0开始）
    /// </summary>
    [JsonPropertyName("endIndex")]
    public long EndIndex => Math.Min(StartIndex + PageSize - 1, TotalCount - 1);

    /// <summary>
    /// 当前页记录数
    /// </summary>
    [JsonPropertyName("currentPageCount")]
    public int CurrentPageCount => Items.Count;

    /// <summary>
    /// 是否为空结果
    /// </summary>
    [JsonPropertyName("isEmpty")]
    public bool IsEmpty => TotalCount == 0;

    /// <summary>
    /// 创建空的分页结果
    /// </summary>
    /// <param name="pageIndex">页索引</param>
    /// <param name="pageSize">页大小</param>
    /// <returns>空的分页结果</returns>
    public static PagedResult<T> Empty(int pageIndex = 0, int pageSize = 20)
    {
        return new PagedResult<T>
        {
            Items = new List<T>(),
            TotalCount = 0,
            PageIndex = pageIndex,
            PageSize = pageSize
        };
    }

    /// <summary>
    /// 创建分页结果
    /// </summary>
    /// <param name="items">数据项</param>
    /// <param name="totalCount">总记录数</param>
    /// <param name="pageIndex">页索引</param>
    /// <param name="pageSize">页大小</param>
    /// <returns>分页结果</returns>
    public static PagedResult<T> Create(IEnumerable<T> items, long totalCount, int pageIndex, int pageSize)
    {
        return new PagedResult<T>
        {
            Items = items.ToList(),
            TotalCount = totalCount,
            PageIndex = pageIndex,
            PageSize = pageSize
        };
    }

    /// <summary>
    /// 从完整列表创建分页结果
    /// </summary>
    /// <param name="allItems">所有数据项</param>
    /// <param name="pageIndex">页索引</param>
    /// <param name="pageSize">页大小</param>
    /// <returns>分页结果</returns>
    public static PagedResult<T> FromList(IEnumerable<T> allItems, int pageIndex, int pageSize)
    {
        var itemsList = allItems.ToList();
        var totalCount = itemsList.Count;
        var pagedItems = itemsList.Skip(pageIndex * pageSize).Take(pageSize).ToList();

        return new PagedResult<T>
        {
            Items = pagedItems,
            TotalCount = totalCount,
            PageIndex = pageIndex,
            PageSize = pageSize
        };
    }

    /// <summary>
    /// 转换数据项类型
    /// </summary>
    /// <typeparam name="TResult">目标类型</typeparam>
    /// <param name="converter">转换函数</param>
    /// <returns>转换后的分页结果</returns>
    public PagedResult<TResult> Map<TResult>(Func<T, TResult> converter)
    {
        return new PagedResult<TResult>
        {
            Items = Items.Select(converter).ToList(),
            TotalCount = TotalCount,
            PageIndex = PageIndex,
            PageSize = PageSize
        };
    }

    /// <summary>
    /// 异步转换数据项类型
    /// </summary>
    /// <typeparam name="TResult">目标类型</typeparam>
    /// <param name="converter">异步转换函数</param>
    /// <returns>转换后的分页结果</returns>
    public async Task<PagedResult<TResult>> MapAsync<TResult>(Func<T, Task<TResult>> converter)
    {
        var convertedItems = new List<TResult>();
        foreach (var item in Items)
        {
            var convertedItem = await converter(item);
            convertedItems.Add(convertedItem);
        }

        return new PagedResult<TResult>
        {
            Items = convertedItems,
            TotalCount = TotalCount,
            PageIndex = PageIndex,
            PageSize = PageSize
        };
    }
}

/// <summary>
/// 分页查询参数
/// </summary>
public class PagedQuery
{
    /// <summary>
    /// 页索引（从0开始）
    /// </summary>
    [JsonPropertyName("pageIndex")]
    public int PageIndex { get; set; } = 0;

    /// <summary>
    /// 页大小
    /// </summary>
    [JsonPropertyName("pageSize")]
    public int PageSize { get; set; } = 20;

    /// <summary>
    /// 排序字段
    /// </summary>
    [JsonPropertyName("sortBy")]
    public string? SortBy { get; set; }

    /// <summary>
    /// 是否降序排序
    /// </summary>
    [JsonPropertyName("sortDescending")]
    public bool SortDescending { get; set; } = false;

    /// <summary>
    /// 搜索关键词
    /// </summary>
    [JsonPropertyName("searchTerm")]
    public string? SearchTerm { get; set; }

    /// <summary>
    /// 过滤条件
    /// </summary>
    [JsonPropertyName("filters")]
    public Dictionary<string, object> Filters { get; set; } = new();

    /// <summary>
    /// 验证分页参数
    /// </summary>
    public void Validate()
    {
        if (PageIndex < 0)
        {
            PageIndex = 0;
        }

        if (PageSize <= 0)
        {
            PageSize = 20;
        }

        if (PageSize > 1000)
        {
            PageSize = 1000; // 限制最大页大小
        }
    }

    /// <summary>
    /// 计算跳过的记录数
    /// </summary>
    /// <returns>跳过的记录数</returns>
    public int GetSkipCount()
    {
        return PageIndex * PageSize;
    }

    /// <summary>
    /// 获取排序表达式
    /// </summary>
    /// <returns>排序表达式</returns>
    public string GetSortExpression()
    {
        if (string.IsNullOrEmpty(SortBy))
        {
            return string.Empty;
        }

        return SortDescending ? $"{SortBy} DESC" : $"{SortBy} ASC";
    }
}

/// <summary>
/// 分页扩展方法
/// </summary>
public static class PagedResultExtensions
{
    /// <summary>
    /// 将IQueryable转换为分页结果
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    /// <param name="query">查询</param>
    /// <param name="pageIndex">页索引</param>
    /// <param name="pageSize">页大小</param>
    /// <returns>分页结果</returns>
    public static PagedResult<T> ToPagedResult<T>(this IQueryable<T> query, int pageIndex, int pageSize)
    {
        var totalCount = query.Count();
        var items = query.Skip(pageIndex * pageSize).Take(pageSize).ToList();

        return PagedResult<T>.Create(items, totalCount, pageIndex, pageSize);
    }

    /// <summary>
    /// 将IEnumerable转换为分页结果
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    /// <param name="source">数据源</param>
    /// <param name="pageIndex">页索引</param>
    /// <param name="pageSize">页大小</param>
    /// <returns>分页结果</returns>
    public static PagedResult<T> ToPagedResult<T>(this IEnumerable<T> source, int pageIndex, int pageSize)
    {
        return PagedResult<T>.FromList(source, pageIndex, pageSize);
    }

    /// <summary>
    /// 应用分页查询参数
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    /// <param name="query">查询</param>
    /// <param name="pagedQuery">分页查询参数</param>
    /// <returns>分页结果</returns>
    public static PagedResult<T> ApplyPaging<T>(this IQueryable<T> query, PagedQuery pagedQuery)
    {
        pagedQuery.Validate();
        return query.ToPagedResult(pagedQuery.PageIndex, pagedQuery.PageSize);
    }
}
