using FlowCustomV1.Core.Models.Messaging;

namespace FlowCustomV1.Core.Interfaces.Messaging;

/// <summary>
/// NATS消息服务接口
/// 提供基础的NATS消息发布订阅功能
/// </summary>
public interface INatsService : IDisposable
{
    #region 连接管理

    /// <summary>
    /// 连接状态
    /// </summary>
    bool IsConnected { get; }

    /// <summary>
    /// 连接到NATS服务器
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>连接任务</returns>
    Task ConnectAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 断开NATS连接
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>断开连接任务</returns>
    Task DisconnectAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 连接状态变更事件
    /// </summary>
    event EventHandler<ConnectionStatusChangedEventArgs>? ConnectionStatusChanged;

    #endregion

    #region 消息发布

    /// <summary>
    /// 发布消息到指定主题
    /// </summary>
    /// <param name="subject">消息主题</param>
    /// <param name="message">消息对象</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发布任务</returns>
    Task PublishAsync<T>(string subject, T message, CancellationToken cancellationToken = default) where T : class;

    /// <summary>
    /// 发布消息
    /// </summary>
    /// <param name="message">消息对象</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发布任务</returns>
    Task PublishAsync(IMessage message, CancellationToken cancellationToken = default);

    /// <summary>
    /// 发布消息并等待响应（请求-响应模式）
    /// </summary>
    /// <typeparam name="TRequest">请求类型</typeparam>
    /// <typeparam name="TResponse">响应类型</typeparam>
    /// <param name="subject">消息主题</param>
    /// <param name="request">请求消息</param>
    /// <param name="timeout">超时时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>响应消息</returns>
    Task<TResponse?> RequestAsync<TRequest, TResponse>(
        string subject, 
        TRequest request, 
        TimeSpan? timeout = null,
        CancellationToken cancellationToken = default) 
        where TRequest : class 
        where TResponse : class;

    #endregion

    #region 消息订阅

    /// <summary>
    /// 订阅消息主题
    /// </summary>
    /// <typeparam name="T">消息类型</typeparam>
    /// <param name="subject">消息主题</param>
    /// <param name="handler">消息处理器</param>
    /// <param name="queueGroup">队列组名称（可选，用于负载均衡）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>订阅对象</returns>
    Task<IMessageSubscription> SubscribeAsync<T>(
        string subject, 
        Func<T, Task> handler,
        string? queueGroup = null,
        CancellationToken cancellationToken = default) where T : class;

    /// <summary>
    /// 订阅NATS消息
    /// </summary>
    /// <param name="subject">消息主题</param>
    /// <param name="handler">消息处理器</param>
    /// <param name="queueGroup">队列组名称（可选）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>订阅对象</returns>
    Task<IMessageSubscription> SubscribeAsync(
        string subject,
        Func<IMessage, Task> handler,
        string? queueGroup = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 订阅请求消息（用于响应请求-响应模式）
    /// </summary>
    /// <typeparam name="TRequest">请求类型</typeparam>
    /// <typeparam name="TResponse">响应类型</typeparam>
    /// <param name="subject">消息主题</param>
    /// <param name="handler">请求处理器</param>
    /// <param name="queueGroup">队列组名称（可选）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>订阅对象</returns>
    Task<IMessageSubscription> SubscribeRequestAsync<TRequest, TResponse>(
        string subject,
        Func<TRequest, Task<TResponse>> handler,
        string? queueGroup = null,
        CancellationToken cancellationToken = default)
        where TRequest : class
        where TResponse : class;

    #endregion

    #region JetStream支持

    /// <summary>
    /// 是否支持JetStream
    /// </summary>
    bool IsJetStreamEnabled { get; }

    /// <summary>
    /// 发布消息到JetStream
    /// </summary>
    /// <typeparam name="T">消息类型</typeparam>
    /// <param name="subject">消息主题</param>
    /// <param name="message">消息对象</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发布确认</returns>
    Task<string> PublishToStreamAsync<T>(string subject, T message, CancellationToken cancellationToken = default) where T : class;

    /// <summary>
    /// 创建或更新JetStream流
    /// </summary>
    /// <param name="streamName">流名称</param>
    /// <param name="subjects">流主题</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>创建任务</returns>
    Task CreateOrUpdateStreamAsync(string streamName, string[] subjects, CancellationToken cancellationToken = default);

    #endregion
}

/// <summary>
/// 消息订阅接口
/// </summary>
public interface IMessageSubscription : IDisposable
{
    /// <summary>
    /// 订阅ID
    /// </summary>
    string SubscriptionId { get; }

    /// <summary>
    /// 订阅主题
    /// </summary>
    string Subject { get; }

    /// <summary>
    /// 队列组名称
    /// </summary>
    string? QueueGroup { get; }

    /// <summary>
    /// 是否活跃
    /// </summary>
    bool IsActive { get; }

    /// <summary>
    /// 取消订阅
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>取消任务</returns>
    Task UnsubscribeAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// 连接状态变更事件参数
/// </summary>
public class ConnectionStatusChangedEventArgs : EventArgs
{
    /// <summary>
    /// 是否已连接
    /// </summary>
    public bool IsConnected { get; set; }

    /// <summary>
    /// 连接的服务器地址
    /// </summary>
    public string? ServerUrl { get; set; }

    /// <summary>
    /// 状态变更时间
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 错误信息（如果有）
    /// </summary>
    public string? Error { get; set; }
}
