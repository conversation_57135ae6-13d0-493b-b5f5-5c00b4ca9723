using System.ComponentModel.DataAnnotations;

namespace FlowCustomV1.Core.Models.Cluster;

/// <summary>
/// 节点角色配置
/// 定义节点的功能角色配置和运行参数
/// </summary>
public class NodeRoleConfiguration
{
    /// <summary>
    /// 节点功能角色
    /// 支持多角色组合，使用位标志枚举
    /// </summary>
    public NodeRole Roles { get; set; } = NodeRole.All;

    /// <summary>
    /// 是否启用传统模式兼容
    /// 当启用时，节点会同时支持Master-Worker模式
    /// </summary>
    public bool EnableLegacyMode { get; set; } = false;

    /// <summary>
    /// 集群架构模式
    /// 定义集群的整体架构和协作方式
    /// </summary>
    public ClusterArchitectureMode ArchitectureMode { get; set; } = ClusterArchitectureMode.RoleBased;

    /// <summary>
    /// 是否支持动态角色切换
    /// 当启用时，节点可以在运行时动态调整角色
    /// </summary>
    public bool EnableDynamicRoleSwitching { get; set; } = true;

    /// <summary>
    /// 角色优先级配置
    /// 当节点承担多个角色时，定义角色的优先级
    /// </summary>
    public Dictionary<NodeRole, int> RolePriorities { get; set; } = new()
    {
        { NodeRole.Gateway, 10 },      // 网关优先级最高
        { NodeRole.Scheduler, 9 },     // 调度器次之
        { NodeRole.Monitor, 8 },       // 监控器
        { NodeRole.Validator, 7 },     // 验证器
        { NodeRole.Designer, 6 },      // 设计器
        { NodeRole.Executor, 5 },      // 执行器
        { NodeRole.Storage, 4 }        // 存储优先级最低
    };

    /// <summary>
    /// 角色资源限制配置
    /// 定义每个角色的资源使用限制
    /// </summary>
    public Dictionary<NodeRole, RoleResourceLimits> RoleResourceLimits { get; set; } = new();

    /// <summary>
    /// 角色服务配置
    /// 定义每个角色的具体服务配置
    /// </summary>
    public Dictionary<NodeRole, RoleServiceConfiguration> RoleServiceConfigurations { get; set; } = new();

    /// <summary>
    /// 验证配置的有效性
    /// </summary>
    /// <returns>验证结果</returns>
    public ValidationResult Validate()
    {
        var errors = new List<string>();

        // 检查角色配置
        if (Roles == NodeRole.None)
        {
            errors.Add("节点必须至少配置一个功能角色");
        }

        // 检查架构模式兼容性
        if (EnableLegacyMode && !ArchitectureMode.SupportsMasterWorker())
        {
            errors.Add("当前架构模式不支持传统Master-Worker模式");
        }

        // 检查动态切换兼容性
        if (EnableDynamicRoleSwitching && !ArchitectureMode.SupportsDynamicRoleSwitching())
        {
            errors.Add("当前架构模式不支持动态角色切换");
        }

        // 检查角色优先级配置
        var activeRoles = Roles.GetActiveRoles().ToList();
        foreach (var role in activeRoles)
        {
            if (!RolePriorities.ContainsKey(role))
            {
                RolePriorities[role] = 5; // 默认优先级
            }
        }

        return new ValidationResult
        {
            IsValid = errors.Count == 0,
            Errors = errors
        };
    }

    /// <summary>
    /// 获取指定角色的优先级
    /// </summary>
    /// <param name="role">节点角色</param>
    /// <returns>优先级（数值越大优先级越高）</returns>
    public int GetRolePriority(NodeRole role)
    {
        return RolePriorities.TryGetValue(role, out var priority) ? priority : 5;
    }

    /// <summary>
    /// 设置角色优先级
    /// </summary>
    /// <param name="role">节点角色</param>
    /// <param name="priority">优先级</param>
    public void SetRolePriority(NodeRole role, int priority)
    {
        RolePriorities[role] = Math.Max(1, Math.Min(10, priority)); // 限制在1-10范围内
    }

    /// <summary>
    /// 获取按优先级排序的角色列表
    /// </summary>
    /// <returns>按优先级排序的角色列表</returns>
    public List<NodeRole> GetRolesByPriority()
    {
        return Roles.GetActiveRoles()
            .OrderByDescending(role => GetRolePriority(role))
            .ToList();
    }

    /// <summary>
    /// 检查是否支持指定角色
    /// </summary>
    /// <param name="role">目标角色</param>
    /// <returns>是否支持</returns>
    public bool SupportsRole(NodeRole role)
    {
        return Roles.HasRole(role);
    }

    /// <summary>
    /// 添加角色
    /// </summary>
    /// <param name="role">要添加的角色</param>
    public void AddRole(NodeRole role)
    {
        Roles = Roles.AddRole(role);
        
        // 设置默认优先级
        if (!RolePriorities.ContainsKey(role))
        {
            RolePriorities[role] = 5;
        }
    }

    /// <summary>
    /// 移除角色
    /// </summary>
    /// <param name="role">要移除的角色</param>
    public void RemoveRole(NodeRole role)
    {
        Roles = Roles.RemoveRole(role);
        RolePriorities.Remove(role);
        RoleResourceLimits.Remove(role);
        RoleServiceConfigurations.Remove(role);
    }

    /// <summary>
    /// 创建配置的深拷贝
    /// </summary>
    /// <returns>配置的深拷贝</returns>
    public NodeRoleConfiguration Clone()
    {
        return new NodeRoleConfiguration
        {
            Roles = Roles,
            EnableLegacyMode = EnableLegacyMode,
            ArchitectureMode = ArchitectureMode,
            EnableDynamicRoleSwitching = EnableDynamicRoleSwitching,
            RolePriorities = new Dictionary<NodeRole, int>(RolePriorities),
            RoleResourceLimits = new Dictionary<NodeRole, RoleResourceLimits>(RoleResourceLimits),
            RoleServiceConfigurations = new Dictionary<NodeRole, RoleServiceConfiguration>(RoleServiceConfigurations)
        };
    }
}

/// <summary>
/// 角色资源限制配置
/// </summary>
public class RoleResourceLimits
{
    /// <summary>
    /// CPU使用限制（百分比）
    /// </summary>
    [Range(1, 100)]
    public int MaxCpuPercent { get; set; } = 80;

    /// <summary>
    /// 内存使用限制（MB）
    /// </summary>
    [Range(64, int.MaxValue)]
    public int MaxMemoryMb { get; set; } = 1024;

    /// <summary>
    /// 最大并发任务数
    /// </summary>
    [Range(1, 1000)]
    public int MaxConcurrentTasks { get; set; } = 10;

    /// <summary>
    /// 磁盘使用限制（MB）
    /// </summary>
    [Range(100, int.MaxValue)]
    public int MaxDiskMb { get; set; } = 2048;
}

/// <summary>
/// 角色服务配置
/// </summary>
public class RoleServiceConfiguration
{
    /// <summary>
    /// 是否启用该角色的服务
    /// </summary>
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// 服务配置参数
    /// </summary>
    public Dictionary<string, object> Parameters { get; set; } = new();

    /// <summary>
    /// 服务依赖的其他角色
    /// </summary>
    public List<NodeRole> Dependencies { get; set; } = new();
}

/// <summary>
/// 验证结果
/// </summary>
public class ValidationResult
{
    /// <summary>
    /// 是否验证通过
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 验证错误列表
    /// </summary>
    public List<string> Errors { get; set; } = new();
}
