#!/usr/bin/env python3
"""
FlowCustomV1 API快速测试脚本
用于快速验证API服务状态和基本功能
"""

import requests
import json
import sys
from datetime import datetime

def test_api_quick(base_url: str = "http://localhost:5257"):
    """快速测试API基本功能"""
    print(f"🔗 快速测试API: {base_url}")
    print("=" * 50)
    
    session = requests.Session()
    session.headers.update({
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    })
    
    tests_passed = 0
    total_tests = 0
    
    # 1. 健康检查
    total_tests += 1
    try:
        response = session.get(f"{base_url}/api/workflows")
        if response.status_code == 200:
            workflows = response.json()
            print(f"✅ API健康检查: 正常 (发现 {len(workflows)} 个工作流)")
            tests_passed += 1
        else:
            print(f"❌ API健康检查: 失败 (状态码: {response.status_code})")
    except Exception as e:
        print(f"❌ API健康检查: 连接失败 ({str(e)})")
    
    # 2. Swagger文档检查
    total_tests += 1
    try:
        response = session.get(f"{base_url}/swagger/index.html")
        if response.status_code == 200:
            print("✅ Swagger文档: 可访问")
            tests_passed += 1
        else:
            print(f"❌ Swagger文档: 不可访问 (状态码: {response.status_code})")
    except Exception as e:
        print(f"❌ Swagger文档: 访问失败 ({str(e)})")
    
    # 3. 创建测试工作流
    total_tests += 1
    test_workflow = {
        "workflowId": f"quick-test-{int(datetime.now().timestamp())}",
        "name": "快速测试工作流",
        "description": "API快速测试创建的工作流",
        "version": "1.0.0",
        "nodes": [
            {
                "nodeId": "start",
                "nodeType": "Start",
                "name": "开始"
            },
            {
                "nodeId": "end",
                "nodeType": "End",
                "name": "结束"
            }
        ],
        "connections": [
            {
                "connectionId": "conn1",
                "sourceNodeId": "start",
                "targetNodeId": "end"
            }
        ]
    }
    
    try:
        response = session.post(f"{base_url}/api/workflows", json=test_workflow)
        if response.status_code == 201:
            created_workflow = response.json()
            workflow_id = created_workflow.get('workflowId')
            print(f"✅ 创建工作流: 成功 (ID: {workflow_id})")
            tests_passed += 1
            
            # 4. 执行工作流
            total_tests += 1
            try:
                exec_response = session.post(f"{base_url}/api/executions/start/{workflow_id}")
                if exec_response.status_code == 200:
                    exec_result = exec_response.json()
                    print(f"✅ 执行工作流: 成功 (执行ID: {exec_result.get('executionId', 'Unknown')})")
                    tests_passed += 1
                else:
                    print(f"❌ 执行工作流: 失败 (状态码: {exec_response.status_code})")
            except Exception as e:
                print(f"❌ 执行工作流: 失败 ({str(e)})")
            
            # 5. 清理测试数据
            total_tests += 1
            try:
                delete_response = session.delete(f"{base_url}/api/workflows/{workflow_id}")
                if delete_response.status_code == 204:
                    print("✅ 清理测试数据: 成功")
                    tests_passed += 1
                else:
                    print(f"❌ 清理测试数据: 失败 (状态码: {delete_response.status_code})")
            except Exception as e:
                print(f"❌ 清理测试数据: 失败 ({str(e)})")
                
        else:
            print(f"❌ 创建工作流: 失败 (状态码: {response.status_code})")
            error_detail = response.text[:200] if response.text else "无错误详情"
            print(f"   错误详情: {error_detail}")
    except Exception as e:
        print(f"❌ 创建工作流: 失败 ({str(e)})")
    
    # 输出测试结果
    print("=" * 50)
    print(f"📊 快速测试结果: {tests_passed}/{total_tests} 通过")
    success_rate = (tests_passed / total_tests * 100) if total_tests > 0 else 0
    print(f"🎯 成功率: {success_rate:.1f}%")
    
    if tests_passed == total_tests:
        print("🎉 所有测试通过！API服务正常运行")
        return True
    else:
        print("⚠️  部分测试失败，请检查API服务状态")
        return False

if __name__ == "__main__":
    # 检查命令行参数
    base_url = sys.argv[1] if len(sys.argv) > 1 else "http://localhost:5257"
    
    success = test_api_quick(base_url)
    sys.exit(0 if success else 1)
