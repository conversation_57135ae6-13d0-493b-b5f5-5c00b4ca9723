using Microsoft.Extensions.Configuration;
using FlowCustomV1.Core.Interfaces;

namespace FlowCustomV1.Core.Services;

/// <summary>
/// 统一配置管理服务实现
/// 基于Microsoft.Extensions.Configuration的配置服务封装
/// </summary>
public class ConfigurationService : IConfigurationService
{
    private readonly IConfiguration _configuration;
    private readonly IConfigurationRoot? _configurationRoot;

    /// <summary>
    /// 初始化配置服务
    /// </summary>
    /// <param name="configuration">配置对象</param>
    public ConfigurationService(IConfiguration configuration)
    {
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _configurationRoot = configuration as IConfigurationRoot;
    }

    /// <inheritdoc />
    public event EventHandler<ConfigurationChangedEventArgs>? ConfigurationChanged;

    /// <inheritdoc />
    public string? GetValue(string key)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(key);
        return _configuration[key];
    }

    /// <inheritdoc />
    public string GetValue(string key, string defaultValue)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(key);
        ArgumentNullException.ThrowIfNull(defaultValue);
        
        return _configuration[key] ?? defaultValue;
    }

    /// <inheritdoc />
    public T? GetValue<T>(string key)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(key);
        return _configuration.GetValue<T>(key);
    }

    /// <inheritdoc />
    public T GetValue<T>(string key, T defaultValue)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(key);
        return _configuration.GetValue(key, defaultValue);
    }

    /// <inheritdoc />
    public IConfigurationSection? GetSection(string key)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(key);
        var section = _configuration.GetSection(key);
        return section.Exists() ? section : null;
    }

    /// <inheritdoc />
    public T? Bind<T>(string key) where T : class, new()
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(key);
        
        var section = GetSection(key);
        if (section == null)
            return null;

        var instance = new T();
        section.Bind(instance);
        return instance;
    }

    /// <inheritdoc />
    public void Bind(string key, object instance)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(key);
        ArgumentNullException.ThrowIfNull(instance);
        
        var section = GetSection(key);
        section?.Bind(instance);
    }

    /// <inheritdoc />
    public bool Exists(string key)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(key);
        return _configuration.GetSection(key).Exists();
    }

    /// <inheritdoc />
    public IEnumerable<string> GetKeys()
    {
        return GetAllKeys(_configuration.GetChildren());
    }

    /// <inheritdoc />
    public IEnumerable<string> GetKeys(string prefix)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(prefix);
        
        var section = _configuration.GetSection(prefix);
        if (!section.Exists())
            return Enumerable.Empty<string>();

        return GetAllKeys(section.GetChildren()).Select(key => $"{prefix}:{key}");
    }

    /// <inheritdoc />
    public ConfigurationValidationResult ValidateConfiguration(IEnumerable<string> requiredKeys)
    {
        ArgumentNullException.ThrowIfNull(requiredKeys);

        var result = new ConfigurationValidationResult();
        var missingKeys = new List<string>();

        foreach (var key in requiredKeys)
        {
            if (!Exists(key))
            {
                missingKeys.Add(key);
            }
        }

        result.IsValid = missingKeys.Count == 0;

        if (!result.IsValid)
        {
            result.Errors.Add($"Missing required configuration keys: {string.Join(", ", missingKeys)}");
            result.InvalidConfigurations.AddRange(missingKeys);
        }

        return result;
    }

    /// <inheritdoc />
    public void Reload()
    {
        _configurationRoot?.Reload();
        OnConfigurationChanged(new ConfigurationChangedEventArgs
        {
            Key = "*",
            ChangedAt = DateTime.UtcNow
        });
    }

    /// <summary>
    /// 递归获取所有配置键
    /// </summary>
    /// <param name="children">配置子节点</param>
    /// <returns>所有配置键</returns>
    private static IEnumerable<string> GetAllKeys(IEnumerable<IConfigurationSection> children)
    {
        foreach (var child in children)
        {
            yield return child.Key;
            
            foreach (var grandChild in GetAllKeys(child.GetChildren()))
            {
                yield return $"{child.Key}:{grandChild}";
            }
        }
    }

    /// <summary>
    /// 触发配置变更事件
    /// </summary>
    /// <param name="args">事件参数</param>
    protected virtual void OnConfigurationChanged(ConfigurationChangedEventArgs args)
    {
        ConfigurationChanged?.Invoke(this, args);
    }
}
