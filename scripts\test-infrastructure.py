#!/usr/bin/env python3
"""
FlowCustomV1 基础设施测试脚本
测试NATS集群和MySQL是否能正常启动
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def run_command(command, cwd=None, timeout=60):
    """执行命令并返回结果"""
    try:
        result = subprocess.run(
            command, 
            cwd=cwd, 
            capture_output=True, 
            text=True, 
            shell=True,
            encoding='utf-8',
            errors='ignore',
            timeout=timeout
        )
        return result.returncode == 0, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return False, "", f"命令超时 ({timeout}秒)"
    except Exception as e:
        return False, "", str(e)

def test_nats_config():
    """测试NATS配置文件"""
    print("🔍 检查NATS配置文件...")
    
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    nats_config_dir = project_root / "docker" / "nats-cluster" / "config"
    
    config_files = [
        "nats-server-1.conf",
        "nats-server-2.conf", 
        "nats-server-3.conf"
    ]
    
    for config_file in config_files:
        config_path = nats_config_dir / config_file
        if config_path.exists():
            print(f"✅ {config_file} 存在")
        else:
            print(f"❌ {config_file} 不存在")
            return False
    
    return True

def start_nats_only():
    """只启动NATS集群"""
    print("\n🚀 启动NATS集群...")
    
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    cluster_test_dir = project_root / "docker" / "cluster-test"
    
    # 只启动第一个NATS服务器
    success, stdout, stderr = run_command(
        "docker-compose up -d nats-server-1",
        cwd=cluster_test_dir,
        timeout=60
    )
    
    if not success:
        print(f"❌ NATS服务器1启动失败:")
        print(f"stdout: {stdout}")
        print(f"stderr: {stderr}")
        return False
    
    print("✅ NATS服务器1启动成功")
    
    # 等待启动
    time.sleep(5)
    
    # 检查容器状态
    success, stdout, stderr = run_command(
        "docker ps --filter name=nats-test-server-1",
        timeout=10
    )
    
    if success:
        print("📊 NATS容器状态:")
        print(stdout)
    
    return True

def test_nats_connection():
    """测试NATS连接"""
    print("\n🧪 测试NATS连接...")
    
    # 尝试连接NATS
    success, stdout, stderr = run_command(
        "docker exec nats-test-server-1 nats server info",
        timeout=10
    )
    
    if success:
        print("✅ NATS服务器信息:")
        print(stdout)
        return True
    else:
        print(f"❌ NATS连接测试失败: {stderr}")
        return False

def cleanup():
    """清理资源"""
    print("\n🧹 清理资源...")
    
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    cluster_test_dir = project_root / "docker" / "cluster-test"
    
    success, stdout, stderr = run_command(
        "docker-compose down",
        cwd=cluster_test_dir,
        timeout=30
    )
    
    if success:
        print("✅ 清理完成")
    else:
        print(f"⚠️ 清理可能不完整: {stderr}")

def main():
    """主函数"""
    print("🚀 FlowCustomV1 基础设施测试")
    print("=" * 40)
    
    try:
        # 检查NATS配置
        if not test_nats_config():
            return 1
        
        # 启动NATS
        if not start_nats_only():
            return 1
        
        # 测试NATS连接
        if not test_nats_connection():
            print("⚠️ NATS连接测试失败，但容器可能正在启动...")
        
        print("\n🎉 基础设施测试完成!")
        print("💡 NATS服务器访问: localhost:24222")
        
        input("\n按Enter键清理资源...")
        
    finally:
        cleanup()
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
