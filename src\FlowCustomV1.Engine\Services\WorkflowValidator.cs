using FlowCustomV1.Core.Interfaces;
using FlowCustomV1.Core.Models.Workflow;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace FlowCustomV1.Engine.Services;

/// <summary>
/// Provides an implementation for the workflow validator.
/// </summary>
public class WorkflowValidator : IWorkflowValidator
{
    private readonly IValidationService _validationService;
    private readonly ConcurrentDictionary<string, IValidationRule> _customRules = new();

    /// <summary>
    /// Initializes a new instance of the <see cref="WorkflowValidator"/> class.
    /// </summary>
    /// <param name="validationService">The generic validation service for low-level checks.</param>
    public WorkflowValidator(IValidationService validationService)
    {
        _validationService = validationService;
    }

    /// <inheritdoc />
    public Task<WorkflowValidationResult> ValidateWorkflowDefinitionAsync(WorkflowDefinition workflowDefinition, CancellationToken cancellationToken = default)
    {
        var result = new WorkflowValidationResult();

        if (workflowDefinition == null)
        {
            var error = new ValidationError
            {
                Code = "WF_DEF_NULL",
                Message = "Workflow definition cannot be null."
            };
            result.ValidationErrors.Add(error);
            result.IsValid = false;
            return Task.FromResult(result);
        }

        if (string.IsNullOrWhiteSpace(workflowDefinition.WorkflowId))
        {
            var error = new ValidationError
            {
                Code = "WF_ID_NULL",
                Message = "Workflow ID cannot be null or empty.",
                Field = nameof(workflowDefinition.WorkflowId)
            };
            result.ValidationErrors.Add(error);
        }

        if (string.IsNullOrWhiteSpace(workflowDefinition.Name))
        {
            var error = new ValidationError
            {
                Code = "WF_NAME_NULL",
                Message = "Workflow name cannot be null or empty.",
                Field = nameof(workflowDefinition.Name)
            };
            result.ValidationErrors.Add(error);
        }

        if (workflowDefinition.Nodes == null || !workflowDefinition.Nodes.Any())
        {
            var error = new ValidationError
            {
                Code = "WF_NO_NODES",
                Message = "Workflow must have at least one node.",
                Field = nameof(workflowDefinition.Nodes)
            };
            result.ValidationErrors.Add(error);
            result.IsValid = false;
            return Task.FromResult(result); // Stop further validation if there are no nodes
        }

        // Check for exactly one Start node
        var startNodes = workflowDefinition.Nodes.Count(n => n.NodeType == "Start");
        if (startNodes == 0)
        {
            var error = new ValidationError
            {
                Code = "WF_NO_START_NODE",
                Message = "Workflow must have exactly one Start node.",
                Field = "Nodes"
            };
            result.ValidationErrors.Add(error);
        }
        else if (startNodes > 1)
        {
            var error = new ValidationError
            {
                Code = "WF_MULTIPLE_START_NODES",
                Message = "Workflow cannot have more than one Start node.",
                Field = "Nodes"
            };
            result.ValidationErrors.Add(error);
        }

        // Check for at least one End node
        if (!workflowDefinition.Nodes.Any(n => n.NodeType == "End"))
        {
            var error = new ValidationError
            {
                Code = "WF_NO_END_NODE",
                Message = "Workflow must have at least one End node.",
                Field = "Nodes"
            };
            result.ValidationErrors.Add(error);
        }

        // Check for duplicate node IDs
        var duplicateIds = workflowDefinition.Nodes.GroupBy(n => n.NodeId)
                                               .Where(g => g.Count() > 1)
                                               .Select(g => g.Key);
        foreach (var id in duplicateIds)
        {
            var error = new ValidationError
            {
                Code = "WF_DUPLICATE_NODE_ID",
                Message = $"Duplicate node ID found: {id}",
                Field = "NodeId"
            };
            result.ValidationErrors.Add(error);
        }

        // Check for orphaned nodes
        if (workflowDefinition.Connections != null && workflowDefinition.Connections.Any())
        {
            var connectedNodeIds = new HashSet<string>();
            foreach (var connection in workflowDefinition.Connections)
            {
                connectedNodeIds.Add(connection.SourceNodeId);
                connectedNodeIds.Add(connection.TargetNodeId);
            }

            var allNodeIds = new HashSet<string>(workflowDefinition.Nodes.Select(n => n.NodeId));
            var orphanedNodes = allNodeIds.Except(connectedNodeIds);

            foreach (var orphanedNodeId in orphanedNodes)
            {
                // Start and End nodes can be part of a two-node workflow without being orphans
                var node = workflowDefinition.Nodes.First(n => n.NodeId == orphanedNodeId);
                if (workflowDefinition.Nodes.Count > 2 || (node.NodeType != "Start" && node.NodeType != "End"))
                {
                    var error = new ValidationError
                    {
                        Code = "WF_ORPHANED_NODE",
                        Message = $"Node '{orphanedNodeId}' is defined but not used in any connection.",
                        Field = "OrphanedNode",
                        Severity = ValidationSeverity.Warning
                    };
                    result.ValidationErrors.Add(error);
                }
            }
        }

        return Task.FromResult(result);
    }

    /// <inheritdoc />
    public Task<NodeValidationResult> ValidateNodeDefinitionAsync(NodeDefinition nodeDefinition, CancellationToken cancellationToken = default)
    {
        var result = new NodeValidationResult();

        if (nodeDefinition == null)
        {
            var error = new ValidationError
            {
                Code = "ND_DEF_NULL",
                Message = "Node definition cannot be null."
            };
            result.ValidationErrors.Add(error);
            return Task.FromResult(result);
        }

        if (string.IsNullOrWhiteSpace(nodeDefinition.NodeType))
        {
            var error = new ValidationError
            {
                Code = "ND_TYPE_NULL",
                Message = "Node type cannot be null or empty.",
                Field = nameof(nodeDefinition.NodeType)
            };
            result.ValidationErrors.Add(error);
        }

        if (string.IsNullOrWhiteSpace(nodeDefinition.DisplayName))
        {
            var error = new ValidationError
            {
                Code = "ND_NAME_NULL",
                Message = "Display name cannot be null or empty.",
                Field = nameof(nodeDefinition.DisplayName)
            };
            result.ValidationErrors.Add(error);
        }

        // Check for duplicate parameter names
        var allParams = new List<NodeParameterDefinition>();
        if (nodeDefinition.InputParameters != null)
            allParams.AddRange(nodeDefinition.InputParameters);
        if (nodeDefinition.OutputParameters != null)
            allParams.AddRange(nodeDefinition.OutputParameters);
        if (nodeDefinition.ConfigurationParameters != null)
            allParams.AddRange(nodeDefinition.ConfigurationParameters);

        var duplicateParams = allParams.GroupBy(p => p.Name)
                                      .Where(g => g.Count() > 1)
                                      .Select(g => g.Key);

        foreach (var paramName in duplicateParams)
        {
            var error = new ValidationError
            {
                Code = "ND_DUPLICATE_PARAM",
                Message = $"Duplicate parameter name '{paramName}' found in node definition."
            };
            result.ValidationErrors.Add(error);
        }

        return Task.FromResult(result);
    }

    /// <inheritdoc />
    public Task<NodeValidationResult> ValidateWorkflowNodeAsync(WorkflowNode workflowNode, NodeDefinition nodeDefinition, CancellationToken cancellationToken = default)
    {
        var result = new NodeValidationResult();

        if (workflowNode == null)
        {
            var error = new ValidationError
            {
                Code = "NODE_NULL",
                Message = "Workflow node cannot be null."
            };
            result.ValidationErrors.Add(error);
            result.IsValid = false;
            return Task.FromResult(result);
        }

        if (nodeDefinition == null)
        {
            var error = new ValidationError
            {
                Code = "NODE_DEF_NULL",
                Message = "Node definition cannot be null."
            };
            result.ValidationErrors.Add(error);
            result.IsValid = false;
            return Task.FromResult(result);
        }

        // Validate node ID
        if (string.IsNullOrWhiteSpace(workflowNode.NodeId))
        {
            var error = new ValidationError
            {
                Code = "NODE_ID_NULL",
                Message = "Node ID cannot be null or empty.",
                Field = nameof(workflowNode.NodeId)
            };
            result.ValidationErrors.Add(error);
        }

        // Validate node type matches definition
        if (workflowNode.NodeType != nodeDefinition.NodeType)
        {
            var error = new ValidationError
            {
                Code = "NODE_TYPE_MISMATCH",
                Message = $"Node type '{workflowNode.NodeType}' does not match definition type '{nodeDefinition.NodeType}'.",
                Field = nameof(workflowNode.NodeType)
            };
            result.ValidationErrors.Add(error);
        }

        // Validate configuration if present
        if (workflowNode.Configuration != null && nodeDefinition.ConfigurationParameters != null)
        {
            var configValidation = ValidateNodeConfigurationAsync(workflowNode.Configuration, nodeDefinition, cancellationToken);
            if (configValidation != null && configValidation.Result != null)
            {
                result.ValidationErrors.AddRange(configValidation.Result.ValidationErrors);
                result.ValidationWarnings.AddRange(configValidation.Result.ValidationWarnings);
            }
        }

        result.IsValid = !result.ValidationErrors.Any(e => e.Severity == ValidationSeverity.Error);
        return Task.FromResult(result);
    }

    /// <inheritdoc />
    public Task<ConnectionValidationResult> ValidateWorkflowConnectionAsync(WorkflowConnection connection, WorkflowNode sourceNode, WorkflowNode targetNode, CancellationToken cancellationToken = default)
    {
        var result = new ConnectionValidationResult();

        if (connection == null)
        {
            var error = new ValidationError
            {
                Code = "CONN_NULL",
                Message = "Workflow connection cannot be null."
            };
            result.ValidationErrors.Add(error);
            result.IsValid = false;
            return Task.FromResult(result);
        }

        if (sourceNode == null)
        {
            var error = new ValidationError
            {
                Code = "CONN_SRC_NULL",
                Message = "Source node cannot be null.",
                Field = "SourceNode"
            };
            result.ValidationErrors.Add(error);
        }

        if (targetNode == null)
        {
            var error = new ValidationError
            {
                Code = "CONN_TGT_NULL",
                Message = "Target node cannot be null.",
                Field = "TargetNode"
            };
            result.ValidationErrors.Add(error);
        }

        if (sourceNode != null && targetNode != null)
        {
            if (connection.SourceNodeId != sourceNode.NodeId)
            {
                var error = new ValidationError
                {
                    Code = "CONN_SRC_MISMATCH",
                    Message = $"Connection source node ID '{connection.SourceNodeId}' does not match source node ID '{sourceNode.NodeId}'.",
                    Field = "SourceNodeId"
                };
                result.ValidationErrors.Add(error);
            }

            if (connection.TargetNodeId != targetNode.NodeId)
            {
                var error = new ValidationError
                {
                    Code = "CONN_TGT_MISMATCH",
                    Message = $"Connection target node ID '{connection.TargetNodeId}' does not match target node ID '{targetNode.NodeId}'.",
                    Field = "TargetNodeId"
                };
                result.ValidationErrors.Add(error);
            }

            // Check for self-loop connections
            if (connection.SourceNodeId == connection.TargetNodeId)
            {
                var error = new ValidationError
                {
                    Code = "CONN_SELF_LOOP",
                    Message = "Node cannot connect to itself.",
                    Field = "Connection"
                };
                result.ValidationErrors.Add(error);
            }

            // Check for invalid connections (End node as source or Start node as target)
            if (sourceNode.NodeType == "End")
            {
                var error = new ValidationError
                {
                    Code = "CONN_END_SRC",
                    Message = $"An 'End' node cannot be a source node. Node ID: {sourceNode.NodeId}",
                    Field = "SourceNode"
                };
                result.ValidationErrors.Add(error);
            }

            if (targetNode.NodeType == "Start")
            {
                var error = new ValidationError
                {
                    Code = "CONN_START_TGT",
                    Message = $"A 'Start' node cannot be a target node. Node ID: {targetNode.NodeId}",
                    Field = "TargetNode"
                };
                result.ValidationErrors.Add(error);
            }
        }

        result.IsValid = !result.ValidationErrors.Any(e => e.Severity == ValidationSeverity.Error);
        return Task.FromResult(result);
    }

    /// <inheritdoc />
    public Task<ParameterValidationResult> ValidateParametersAsync(Dictionary<string, object> parameters, IEnumerable<WorkflowParameterDefinition> parameterDefinitions, CancellationToken cancellationToken = default)
    {
        return _validationService.ValidateParametersAsync(parameters, parameterDefinitions, cancellationToken);
    }

    /// <inheritdoc />
    public Task<ConfigurationValidationResult> ValidateNodeConfigurationAsync(NodeConfiguration configuration, NodeDefinition nodeDefinition, CancellationToken cancellationToken = default)
    {
        var result = new ConfigurationValidationResult();

        if (configuration == null)
        {
            // Configuration is optional, so this is valid
            result.IsValid = true;
            return Task.FromResult(result);
        }

        if (nodeDefinition == null)
        {
            var error = new ValidationError
            {
                Code = "NODE_DEF_NULL",
                Message = "Node definition cannot be null.",
                Field = "NodeDefinition"
            };
            result.ValidationErrors.Add(error);
            result.IsValid = false;
            return Task.FromResult(result);
        }

        // Validate against definition parameters
        if (nodeDefinition.ConfigurationParameters != null)
        {
            var configParams = new Dictionary<string, object>();
            if (configuration.Parameters != null)
            {
                configParams = configuration.Parameters;
            }

            // Filter out null parameter definitions
            var validParameterDefinitions = nodeDefinition.ConfigurationParameters
                .Where(p => p != null)
                .Select(p => new WorkflowParameterDefinition
                {
                    Name = p.Name ?? string.Empty,
                    DataType = p.DataType, // 直接使用p.DataType，不再使用??操作符
                    IsRequired = p.IsRequired,
                    DefaultValue = p.DefaultValue,
                    ValidationRules = p.ValidationRules ?? new List<ParameterValidationRule>()
                });

            if (validParameterDefinitions.Any())
            {
                var paramValidation = ValidateParametersAsync(
                    configParams,
                    validParameterDefinitions,
                    cancellationToken);

                if (paramValidation != null && paramValidation.Result != null)
                {
                    result.ValidationErrors.AddRange(paramValidation.Result.ValidationErrors);
                    result.ValidationWarnings.AddRange(paramValidation.Result.ValidationWarnings);
                }
            }
        }

        result.IsValid = !result.ValidationErrors.Any(e => e.Severity == ValidationSeverity.Error);
        return Task.FromResult(result);
    }

    /// <inheritdoc />
    public Task<ExecutionValidationResult> ValidateExecutionPreconditionsAsync(WorkflowDefinition workflowDefinition, Dictionary<string, object> inputParameters, CancellationToken cancellationToken = default)
    {
        // 1. First, validate the workflow definition itself.
        var definitionValidationResult = ValidateWorkflowDefinitionAsync(workflowDefinition, cancellationToken);
        if (!definitionValidationResult.Result.IsValid)
        {
            var result = new ExecutionValidationResult
            {
                CanExecute = false
            };
            
            foreach (var error in definitionValidationResult.Result.ValidationErrors)
            {
                result.BlockingReasons.Add($"[Definition Error] {error.Field}: {error.Message}");
            }
            foreach (var warning in definitionValidationResult.Result.ValidationWarnings)
            {
                result.BlockingReasons.Add($"[Definition Warning] {warning.Field}: {warning.Message}");
            }
            return Task.FromResult(result);
        }

        // 2. Validate the input parameters against the workflow's parameter definitions.
        var result2 = new ExecutionValidationResult();
        if (workflowDefinition.InputParameters != null && workflowDefinition.InputParameters.Any())
        {
            var parameterValidationResult = _validationService.ValidateParametersAsync(inputParameters, workflowDefinition.InputParameters, cancellationToken);
            if (!parameterValidationResult.Result.IsValid)
            {
                result2.CanExecute = false;
                foreach (var missingParam in parameterValidationResult.Result.MissingParameters)
                {
                    result2.BlockingReasons.Add($"Missing required input parameter: {missingParam}");
                }
                foreach (var invalidParam in parameterValidationResult.Result.InvalidParameters)
                {
                    result2.BlockingReasons.Add($"Invalid input parameter: {invalidParam}");
                }
            }
        }

        result2.CanExecute = !result2.BlockingReasons.Any();
        return Task.FromResult(result2);
    }

    /// <inheritdoc />
    public Task<CyclicDependencyValidationResult> ValidateCyclicDependenciesAsync(WorkflowDefinition workflowDefinition, CancellationToken cancellationToken = default)
    {
        var result = new CyclicDependencyValidationResult();
        if (workflowDefinition?.Nodes == null || workflowDefinition.Connections == null || !workflowDefinition.Connections.Any())
        {
            result.IsValid = true;
            result.HasCyclicDependency = false;
            return Task.FromResult(result);
        }

        var adjacencyList = new Dictionary<string, List<string>>();
        var allNodeIds = new HashSet<string>(workflowDefinition.Nodes.Select(n => n.NodeId));

        foreach (var node in workflowDefinition.Nodes)
        {
            adjacencyList[node.NodeId] = new List<string>();
        }

        foreach (var connection in workflowDefinition.Connections)
        {
            if (allNodeIds.Contains(connection.SourceNodeId) && allNodeIds.Contains(connection.TargetNodeId))
            {
                adjacencyList[connection.SourceNodeId].Add(connection.TargetNodeId);
            }
        }

        var visiting = new HashSet<string>(); // Nodes currently in the recursion stack (gray set)
        var visited = new HashSet<string>();  // Nodes completely visited (black set)
        var cycles = new List<List<string>>();

        // Helper function to detect cycles using DFS
        bool HasCycle(string nodeId, List<string> path)
        {
            if (visiting.Contains(nodeId))
            {
                // Found a cycle
                var cycleStartIndex = path.IndexOf(nodeId);
                if (cycleStartIndex >= 0)
                {
                    var cycle = new List<string>(path.Skip(cycleStartIndex));
                    cycle.Add(nodeId); // Complete the cycle
                    cycles.Add(cycle);
                }
                return true;
            }

            if (visited.Contains(nodeId))
            {
                return false; // Already processed
            }

            visiting.Add(nodeId);
            path.Add(nodeId);

            foreach (var neighbor in adjacencyList[nodeId])
            {
                if (HasCycle(neighbor, path))
                {
                    return true;
                }
            }

            path.RemoveAt(path.Count - 1);
            visiting.Remove(nodeId);
            visited.Add(nodeId);

            return false;
        }

        bool hasCycle = false;
        foreach (var nodeId in allNodeIds)
        {
            if (!visited.Contains(nodeId))
            {
                var currentPath = new List<string>();
                if (HasCycle(nodeId, currentPath))
                {
                    hasCycle = true;
                }
            }
        }

        result.HasCyclicDependency = hasCycle;
        result.CyclicPaths = cycles;
        result.IsValid = !hasCycle;

        if (hasCycle)
        {
            var error = new ValidationError
            {
                Code = "CYCLIC_DEPENDENCY",
                Message = "Workflow contains cyclic dependencies which are not allowed."
            };
            result.ValidationErrors.Add(error);
        }

        return Task.FromResult(result);
    }

    /// <inheritdoc />
    public void RegisterValidationRule(string ruleName, IValidationRule rule)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(ruleName);
        ArgumentNullException.ThrowIfNull(rule);

        _customRules[ruleName] = rule;
    }

    /// <inheritdoc />
    public bool RemoveValidationRule(string ruleName)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(ruleName);

        return _customRules.TryRemove(ruleName, out _);
    }

    /// <inheritdoc />
    public IReadOnlyDictionary<string, IValidationRule> GetRegisteredValidationRules()
    {
        return _customRules;
    }

    /// <inheritdoc />
    public Task<ResourceValidationResult> ValidateResourceRequirementsAsync(NodeResourceRequirements resourceRequirements, Dictionary<string, object> availableResources, CancellationToken cancellationToken = default)
    {
        var result = new ResourceValidationResult();

        if (resourceRequirements == null)
        {
            var error = new ValidationError
            {
                Code = "RES_REQ_NULL",
                Message = "Resource requirements cannot be null."
            };
            result.ValidationErrors.Add(error);
            result.IsValid = false;
            return Task.FromResult(result);
        }

        // Check CPU requirements
        if (availableResources.TryGetValue("CpuCores", out var cpuObj) && cpuObj is double availableCpu)
        {
            if (resourceRequirements.RequiredCpuCores > availableCpu)
            {
                var error = new ValidationError
                {
                    Code = "INSUFFICIENT_CPU",
                    Message = $"Insufficient CPU cores. Required: {resourceRequirements.RequiredCpuCores}, Available: {availableCpu}"
                };
                result.ValidationErrors.Add(error);
            }
        }

        // Check memory requirements
        if (availableResources.TryGetValue("MemoryMb", out var memObj) && memObj is int availableMemory)
        {
            if (resourceRequirements.RequiredMemoryMb > availableMemory)
            {
                var error = new ValidationError
                {
                    Code = "INSUFFICIENT_MEMORY",
                    Message = $"Insufficient memory. Required: {resourceRequirements.RequiredMemoryMb} MB, Available: {availableMemory} MB"
                };
                result.ValidationErrors.Add(error);
            }
        }

        // Check disk requirements
        if (availableResources.TryGetValue("DiskMb", out var diskObj) && diskObj is int availableDisk)
        {
            if (resourceRequirements.RequiredDiskMb > availableDisk)
            {
                var error = new ValidationError
                {
                    Code = "INSUFFICIENT_DISK",
                    Message = $"Insufficient disk space. Required: {resourceRequirements.RequiredDiskMb} MB, Available: {availableDisk} MB"
                };
                result.ValidationErrors.Add(error);
            }
        }

        // Check required tags
        if (availableResources.TryGetValue("Tags", out var tagsObj) && tagsObj is HashSet<string> availableTags)
        {
            var missingTags = resourceRequirements.RequiredTags.Except(availableTags);
            if (missingTags.Any())
            {
                var error = new ValidationError
                {
                    Code = "MISSING_TAGS",
                    Message = $"Missing required tags: {string.Join(", ", missingTags)}"
                };
                result.ValidationErrors.Add(error);
            }
        }

        result.IsValid = !result.ValidationErrors.Any(e => e.Severity == ValidationSeverity.Error);
        return Task.FromResult(result);
    }
}