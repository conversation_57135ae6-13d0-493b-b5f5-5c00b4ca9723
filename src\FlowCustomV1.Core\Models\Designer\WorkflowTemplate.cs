using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using FlowCustomV1.Core.Models.Workflow;

namespace FlowCustomV1.Core.Models.Designer;

/// <summary>
/// 工作流模板
/// 用于快速创建具有预定义结构的工作流
/// </summary>
public class WorkflowTemplate
{
    /// <summary>
    /// 模板唯一标识符
    /// </summary>
    [Required]
    [JsonPropertyName("templateId")]
    public string TemplateId { get; set; } = string.Empty;

    /// <summary>
    /// 模板名称
    /// </summary>
    [Required]
    [JsonPropertyName("name")]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 模板描述
    /// </summary>
    [JsonPropertyName("description")]
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 模板分类
    /// </summary>
    [JsonPropertyName("category")]
    public string Category { get; set; } = string.Empty;

    /// <summary>
    /// 模板版本
    /// </summary>
    [JsonPropertyName("version")]
    public string Version { get; set; } = "1.0.0";

    /// <summary>
    /// 模板作者
    /// </summary>
    [JsonPropertyName("author")]
    public string Author { get; set; } = string.Empty;

    /// <summary>
    /// 模板图标URL
    /// </summary>
    [JsonPropertyName("iconUrl")]
    public string IconUrl { get; set; } = string.Empty;

    /// <summary>
    /// 默认节点列表
    /// </summary>
    [JsonPropertyName("defaultNodes")]
    public List<WorkflowNode> DefaultNodes { get; set; } = new();

    /// <summary>
    /// 默认连接列表
    /// </summary>
    [JsonPropertyName("defaultConnections")]
    public List<WorkflowConnection> DefaultConnections { get; set; } = new();

    /// <summary>
    /// 默认参数配置
    /// </summary>
    [JsonPropertyName("defaultParameters")]
    public Dictionary<string, object> DefaultParameters { get; set; } = new();

    /// <summary>
    /// 模板标签
    /// </summary>
    [JsonPropertyName("tags")]
    public HashSet<string> Tags { get; set; } = new();

    /// <summary>
    /// 是否为公共模板
    /// </summary>
    [JsonPropertyName("isPublic")]
    public bool IsPublic { get; set; } = false;

    /// <summary>
    /// 是否为系统内置模板
    /// </summary>
    [JsonPropertyName("isBuiltIn")]
    public bool IsBuiltIn { get; set; } = false;

    /// <summary>
    /// 使用次数统计
    /// </summary>
    [JsonPropertyName("usageCount")]
    public int UsageCount { get; set; } = 0;

    /// <summary>
    /// 模板评分
    /// </summary>
    [JsonPropertyName("rating")]
    public double Rating { get; set; } = 0.0;

    /// <summary>
    /// 创建时间
    /// </summary>
    [JsonPropertyName("createdAt")]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 创建者ID
    /// </summary>
    [JsonPropertyName("createdBy")]
    public string CreatedBy { get; set; } = string.Empty;

    /// <summary>
    /// 最后修改时间
    /// </summary>
    [JsonPropertyName("lastModifiedAt")]
    public DateTime LastModifiedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 最后修改者ID
    /// </summary>
    [JsonPropertyName("lastModifiedBy")]
    public string LastModifiedBy { get; set; } = string.Empty;

    /// <summary>
    /// 模板元数据
    /// </summary>
    [JsonPropertyName("metadata")]
    public Dictionary<string, object> Metadata { get; set; } = new();

    /// <summary>
    /// 从模板创建工作流定义
    /// </summary>
    /// <param name="workflowName">工作流名称</param>
    /// <param name="workflowDescription">工作流描述</param>
    /// <returns>工作流定义</returns>
    public WorkflowDefinition CreateWorkflowDefinition(string workflowName, string workflowDescription = "")
    {
        var workflowId = Guid.NewGuid().ToString("N");
        
        return new WorkflowDefinition
        {
            WorkflowId = workflowId,
            Name = workflowName,
            Description = string.IsNullOrEmpty(workflowDescription) ? Description : workflowDescription,
            Version = "1.0.0",
            Author = CreatedBy,
            CreatedAt = DateTime.UtcNow,
            LastModifiedAt = DateTime.UtcNow,
            Nodes = DefaultNodes.Select(node => new WorkflowNode
            {
                NodeId = Guid.NewGuid().ToString("N"),
                NodeType = node.NodeType,
                Name = node.Name,
                Description = node.Description,
                Configuration = new NodeConfiguration
                {
                    Parameters = new Dictionary<string, object>(node.Configuration.Parameters),
                    InputMappings = new Dictionary<string, string>(node.Configuration.InputMappings),
                    OutputMappings = new Dictionary<string, string>(node.Configuration.OutputMappings),
                    ConditionExpression = node.Configuration.ConditionExpression,
                    EnvironmentVariables = new Dictionary<string, string>(node.Configuration.EnvironmentVariables),
                    Metadata = new Dictionary<string, object>(node.Configuration.Metadata)
                },
                Position = node.Position,
                Metadata = new Dictionary<string, object>(node.Metadata)
            }).ToList(),
            Connections = DefaultConnections.Select(conn => new WorkflowConnection
            {
                ConnectionId = Guid.NewGuid().ToString("N"),
                SourceNodeId = conn.SourceNodeId,
                TargetNodeId = conn.TargetNodeId,
                SourcePort = conn.SourcePort,
                TargetPort = conn.TargetPort,
                Name = conn.Name,
                Description = conn.Description,
                Metadata = new Dictionary<string, object>(conn.Metadata)
            }).ToList(),
            InputParameters = new List<WorkflowParameterDefinition>(),
            OutputParameters = new List<WorkflowParameterDefinition>(),
            Configuration = new WorkflowConfiguration(),
            Tags = new HashSet<string>(Tags),
            Metadata = new Dictionary<string, object>(DefaultParameters)
        };
    }

    /// <summary>
    /// 验证模板的有效性
    /// </summary>
    /// <returns>验证结果</returns>
    public TemplateValidationResult Validate()
    {
        var result = new TemplateValidationResult
        {
            IsValid = true,
            Errors = new List<string>(),
            Warnings = new List<string>()
        };

        // 基本验证
        if (string.IsNullOrWhiteSpace(TemplateId))
        {
            result.Errors.Add("模板ID不能为空");
            result.IsValid = false;
        }

        if (string.IsNullOrWhiteSpace(Name))
        {
            result.Errors.Add("模板名称不能为空");
            result.IsValid = false;
        }

        if (string.IsNullOrWhiteSpace(Version))
        {
            result.Errors.Add("模板版本不能为空");
            result.IsValid = false;
        }

        // 节点验证
        if (DefaultNodes.Count == 0)
        {
            result.Warnings.Add("模板没有定义默认节点");
        }

        // 连接验证
        foreach (var connection in DefaultConnections)
        {
            var sourceExists = DefaultNodes.Any(n => n.NodeId == connection.SourceNodeId);
            var targetExists = DefaultNodes.Any(n => n.NodeId == connection.TargetNodeId);

            if (!sourceExists)
            {
                result.Errors.Add($"连接的源节点不存在: {connection.SourceNodeId}");
                result.IsValid = false;
            }

            if (!targetExists)
            {
                result.Errors.Add($"连接的目标节点不存在: {connection.TargetNodeId}");
                result.IsValid = false;
            }
        }

        return result;
    }
}

/// <summary>
/// 模板验证结果
/// </summary>
public class TemplateValidationResult
{
    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 错误列表
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// 警告列表
    /// </summary>
    public List<string> Warnings { get; set; } = new();
}
