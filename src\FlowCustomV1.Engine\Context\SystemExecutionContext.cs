using Microsoft.Extensions.DependencyInjection;

namespace FlowCustomV1.Engine.Context;

/// <summary>
/// 系统级别执行上下文
/// 管理全局共享的系统级配置和服务
/// </summary>
public class SystemExecutionContext
{
    /// <summary>
    /// 系统唯一标识符
    /// </summary>
    public string SystemId { get; set; } = Environment.MachineName;

    /// <summary>
    /// 系统启动时间
    /// </summary>
    public DateTime SystemStartTime { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 全局配置参数
    /// </summary>
    public Dictionary<string, object> GlobalConfiguration { get; set; } = new();

    /// <summary>
    /// 服务提供者
    /// </summary>
    public IServiceProvider ServiceProvider { get; set; } = default!;

    /// <summary>
    /// 系统级取消令牌
    /// </summary>
    public CancellationToken SystemCancellationToken { get; set; } = default;

    /// <summary>
    /// 系统性能指标
    /// </summary>
    public Dictionary<string, double> SystemMetrics { get; set; } = new();

    /// <summary>
    /// 系统运行时间
    /// </summary>
    public TimeSpan SystemUptime => DateTime.UtcNow - SystemStartTime;

    /// <summary>
    /// 获取全局配置参数
    /// </summary>
    /// <typeparam name="T">参数类型</typeparam>
    /// <param name="key">配置键</param>
    /// <param name="defaultValue">默认值</param>
    /// <returns>配置值</returns>
    public T GetGlobalConfiguration<T>(string key, T defaultValue = default!)
    {
        if (GlobalConfiguration.TryGetValue(key, out var value))
        {
            try
            {
                if (value is T directValue)
                    return directValue;

                return (T)Convert.ChangeType(value, typeof(T));
            }
            catch
            {
                return defaultValue;
            }
        }
        return defaultValue;
    }

    /// <summary>
    /// 设置全局配置参数
    /// </summary>
    /// <param name="key">配置键</param>
    /// <param name="value">配置值</param>
    public void SetGlobalConfiguration(string key, object value)
    {
        GlobalConfiguration[key] = value;
    }

    /// <summary>
    /// 获取服务实例
    /// </summary>
    /// <typeparam name="T">服务类型</typeparam>
    /// <returns>服务实例</returns>
    public T GetService<T>() where T : notnull
    {
        return ServiceProvider.GetRequiredService<T>();
    }

    /// <summary>
    /// 尝试获取服务实例
    /// </summary>
    /// <typeparam name="T">服务类型</typeparam>
    /// <returns>服务实例或null</returns>
    public T? GetServiceOrDefault<T>() where T : class
    {
        return ServiceProvider.GetService<T>();
    }

    /// <summary>
    /// 更新系统性能指标
    /// </summary>
    /// <param name="metricName">指标名称</param>
    /// <param name="value">指标值</param>
    public void UpdateSystemMetric(string metricName, double value)
    {
        SystemMetrics[metricName] = value;
    }

    /// <summary>
    /// 获取系统性能指标
    /// </summary>
    /// <param name="metricName">指标名称</param>
    /// <returns>指标值</returns>
    public double GetSystemMetric(string metricName)
    {
        return SystemMetrics.TryGetValue(metricName, out var value) ? value : 0.0;
    }

    /// <summary>
    /// 创建系统上下文的副本
    /// </summary>
    /// <returns>系统上下文副本</returns>
    public SystemExecutionContext Clone()
    {
        return new SystemExecutionContext
        {
            SystemId = SystemId,
            SystemStartTime = SystemStartTime,
            GlobalConfiguration = new Dictionary<string, object>(GlobalConfiguration),
            ServiceProvider = ServiceProvider,
            SystemCancellationToken = SystemCancellationToken,
            SystemMetrics = new Dictionary<string, double>(SystemMetrics)
        };
    }

    /// <summary>
    /// 获取系统上下文的字符串表示
    /// </summary>
    /// <returns>系统上下文字符串</returns>
    public override string ToString()
    {
        return $"SystemContext[{SystemId}] Uptime: {SystemUptime:hh\\:mm\\:ss}, Configs: {GlobalConfiguration.Count}, Metrics: {SystemMetrics.Count}";
    }
}
