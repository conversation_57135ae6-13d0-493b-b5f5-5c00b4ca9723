# NATS Server Configuration for Test Cluster Node 2 - Simplified

# Server identity
server_name: "nats-test-2"

# Network configuration
host: "0.0.0.0"
port: 4222

# HTTP monitoring
http_port: 8222

# Cluster configuration
cluster {
  name: "flowcustom-test-cluster"
  host: "0.0.0.0"
  port: 6222
  
  # Routes to other cluster members
  routes: [
    "nats://nats-1:6222"
    "nats://nats-3:6222"
  ]
}

# JetStream configuration
jetstream {
  store_dir: "/data/jetstream"
}

# Logging configuration
logtime: true
debug: false
trace: false

# Limits
max_connections: 1000
max_control_line: 4096
max_payload: 1048576
max_pending: 67108864
max_subscriptions: 0

# Timeouts
ping_interval: "2m"
ping_max: 2
write_deadline: "10s"
