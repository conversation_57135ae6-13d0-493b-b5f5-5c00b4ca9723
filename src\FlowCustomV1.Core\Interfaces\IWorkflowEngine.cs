using FlowCustomV1.Core.Models.Workflow;
using FlowCustomV1.Core.Models.Cluster;
using ClusterWorkflowExecutionStatus = FlowCustomV1.Core.Models.Cluster.WorkflowExecutionStatus;
using ClusterWorkflowExecutionHistory = FlowCustomV1.Core.Models.Cluster.WorkflowExecutionHistory;
using ClusterWorkflowPerformanceStats = FlowCustomV1.Core.Models.Cluster.WorkflowPerformanceStats;

namespace FlowCustomV1.Core.Interfaces;

/// <summary>
/// 工作流引擎接口
/// 定义工作流的执行、管理和监控功能
/// </summary>
public interface IWorkflowEngine
{
    /// <summary>
    /// 启动工作流引擎
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>启动任务</returns>
    Task StartAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 停止工作流引擎
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>停止任务</returns>
    Task StopAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 执行工作流
    /// </summary>
    /// <param name="workflowDefinition">工作流定义</param>
    /// <param name="inputData">输入数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>工作流执行结果</returns>
    Task<WorkflowExecutionResult> ExecuteWorkflowAsync(
        WorkflowDefinition workflowDefinition,
        Dictionary<string, object>? inputData = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 恢复工作流执行
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>工作流执行结果</returns>
    Task<WorkflowExecutionResult> ResumeWorkflowAsync(
        string executionId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 暂停工作流执行
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>暂停任务</returns>
    Task PauseWorkflowAsync(
        string executionId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 取消工作流执行
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="reason">取消原因</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>取消任务</returns>
    Task CancelWorkflowAsync(
        string executionId,
        string? reason = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取工作流执行状态
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>执行状态</returns>
    Task<ClusterWorkflowExecutionStatus?> GetExecutionStatusAsync(
        string executionId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取所有活跃的工作流执行
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>活跃执行列表</returns>
    Task<IEnumerable<ClusterWorkflowExecutionStatus>> GetActiveExecutionsAsync(
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证工作流定义
    /// </summary>
    /// <param name="workflowDefinition">工作流定义</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    Task<WorkflowValidationResult> ValidateWorkflowAsync(
        WorkflowDefinition workflowDefinition,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取工作流执行历史
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="pageSize">页面大小</param>
    /// <param name="pageNumber">页码</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>执行历史</returns>
    Task<ClusterWorkflowExecutionHistory> GetExecutionHistoryAsync(
        string workflowId,
        int pageSize = 50,
        int pageNumber = 1,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取工作流性能统计
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="timeRange">时间范围</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>性能统计</returns>
    Task<WorkflowPerformanceStats> GetPerformanceStatsAsync(
        string workflowId,
        TimeSpan? timeRange = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 工作流执行状态变更事件
    /// </summary>
    event EventHandler<WorkflowExecutionStatusChangedEventArgs>? ExecutionStatusChanged;

    /// <summary>
    /// 工作流执行完成事件
    /// </summary>
    event EventHandler<WorkflowExecutionCompletedEventArgs>? ExecutionCompleted;

    /// <summary>
    /// 工作流执行错误事件
    /// </summary>
    event EventHandler<WorkflowExecutionErrorEventArgs>? ExecutionError;
}

/// <summary>
/// 工作流执行状态变更事件参数
/// </summary>
public class WorkflowExecutionStatusChangedEventArgs : EventArgs
{
    /// <summary>
    /// 执行ID
    /// </summary>
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 工作流ID
    /// </summary>
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 旧状态
    /// </summary>
    public WorkflowExecutionState OldState { get; set; }

    /// <summary>
    /// 新状态
    /// </summary>
    public WorkflowExecutionState NewState { get; set; }

    /// <summary>
    /// 变更时间
    /// </summary>
    public DateTime ChangedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 变更原因
    /// </summary>
    public string? Reason { get; set; }
}

/// <summary>
/// 工作流执行完成事件参数
/// </summary>
public class WorkflowExecutionCompletedEventArgs : EventArgs
{
    /// <summary>
    /// 执行ID
    /// </summary>
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 工作流ID
    /// </summary>
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 执行结果
    /// </summary>
    public WorkflowExecutionResult Result { get; set; } = new();

    /// <summary>
    /// 完成时间
    /// </summary>
    public DateTime CompletedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 执行时长
    /// </summary>
    public TimeSpan Duration { get; set; }
}

/// <summary>
/// 工作流执行错误事件参数
/// </summary>
public class WorkflowExecutionErrorEventArgs : EventArgs
{
    /// <summary>
    /// 执行ID
    /// </summary>
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 工作流ID
    /// </summary>
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 错误信息
    /// </summary>
    public Exception Error { get; set; } = new Exception();

    /// <summary>
    /// 错误发生时间
    /// </summary>
    public DateTime ErrorAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 错误上下文
    /// </summary>
    public Dictionary<string, object> ErrorContext { get; set; } = new();
}
