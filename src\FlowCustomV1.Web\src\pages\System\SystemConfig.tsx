import React from 'react';
import { Form, Input, Switch, Button, Space, Divider, Alert, Select, InputNumber } from 'antd';
import {
  SettingOutlined,
  SaveOutlined,
  ReloadOutlined,
  DatabaseOutlined,
  CloudServerOutlined,
  SecurityScanOutlined
} from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';
import PageLayout from '@/components/Layout/PageLayout';

const { TextArea } = Input;
const { Option } = Select;

const SystemConfig: React.FC = () => {
  const [form] = Form.useForm();

  const handleSave = () => {
    form.validateFields().then(values => {
      console.log('保存配置:', values);
      // TODO: 实现保存逻辑
    });
  };

  const handleReset = () => {
    form.resetFields();
  };

  return (
    <PageLayout
      title="系统配置"
      description="管理系统全局配置和参数"
      icon={<SettingOutlined />}
      actions={
        <Space>
          <Button icon={<ReloadOutlined />} onClick={handleReset}>
            重置
          </Button>
          <Button type="primary" icon={<SaveOutlined />} onClick={handleSave}>
            保存配置
          </Button>
        </Space>
      }
    >
      <Alert
        message="配置修改提醒"
        description="部分配置修改后需要重启系统才能生效，请谨慎操作"
        type="warning"
        showIcon
        className="layout-card-grid"
      />

      <Form
        form={form}
        layout="vertical"
        initialValues={{
          systemName: 'FlowCustomV1',
          systemDescription: 'FlowCustomV1 工作流自动化系统',
          enableDebugMode: false,
          enableMetrics: true,
          maxConcurrentExecutions: 100,
          executionTimeout: 3600,
          logLevel: 'INFO',
          enableAuditLog: true,
          sessionTimeout: 1800,
          enableCors: true,
          corsOrigins: '*',
          dbConnectionString: 'Server=localhost;Database=FlowCustomV1;',
          dbMaxConnections: 50,
          dbCommandTimeout: 30,
          natsServers: 'nats://localhost:4222',
          natsClusterName: 'flowcustom-cluster',
          enableNatsJetStream: true,
          cacheProvider: 'Redis',
          cacheConnectionString: 'localhost:6379',
          cacheDefaultExpiration: 3600,
        }}
      >
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 基础配置 */}
          <ProCard title={<><SettingOutlined className="mr-2" />基础配置</>}>
            <Form.Item
              label="系统名称"
              name="systemName"
              rules={[{ required: true, message: '请输入系统名称' }]}
            >
              <Input placeholder="请输入系统名称" />
            </Form.Item>

            <Form.Item
              label="系统描述"
              name="systemDescription"
            >
              <TextArea rows={3} placeholder="请输入系统描述" />
            </Form.Item>

            <Form.Item
              label="调试模式"
              name="enableDebugMode"
              valuePropName="checked"
            >
              <Switch checkedChildren="开启" unCheckedChildren="关闭" />
            </Form.Item>

            <Form.Item
              label="性能指标收集"
              name="enableMetrics"
              valuePropName="checked"
            >
              <Switch checkedChildren="开启" unCheckedChildren="关闭" />
            </Form.Item>

            <Form.Item
              label="日志级别"
              name="logLevel"
            >
              <Select>
                <Option value="TRACE">TRACE</Option>
                <Option value="DEBUG">DEBUG</Option>
                <Option value="INFO">INFO</Option>
                <Option value="WARN">WARN</Option>
                <Option value="ERROR">ERROR</Option>
              </Select>
            </Form.Item>
          </ProCard>

          {/* 执行配置 */}
          <ProCard title="执行配置">
            <Form.Item
              label="最大并发执行数"
              name="maxConcurrentExecutions"
              rules={[{ required: true, message: '请输入最大并发执行数' }]}
            >
              <InputNumber min={1} max={1000} style={{ width: '100%' }} />
            </Form.Item>

            <Form.Item
              label="执行超时时间（秒）"
              name="executionTimeout"
              rules={[{ required: true, message: '请输入执行超时时间' }]}
            >
              <InputNumber min={60} max={86400} style={{ width: '100%' }} />
            </Form.Item>

            <Form.Item
              label="审计日志"
              name="enableAuditLog"
              valuePropName="checked"
            >
              <Switch checkedChildren="开启" unCheckedChildren="关闭" />
            </Form.Item>

            <Form.Item
              label="会话超时时间（秒）"
              name="sessionTimeout"
            >
              <InputNumber min={300} max={7200} style={{ width: '100%' }} />
            </Form.Item>
          </ProCard>

          {/* API 配置 */}
          <ProCard title="API 配置">
            <Form.Item
              label="启用 CORS"
              name="enableCors"
              valuePropName="checked"
            >
              <Switch checkedChildren="开启" unCheckedChildren="关闭" />
            </Form.Item>

            <Form.Item
              label="CORS 允许源"
              name="corsOrigins"
            >
              <Input placeholder="例如: https://example.com,https://app.com" />
            </Form.Item>

            <Form.Item
              label="API 限流"
              name="enableRateLimit"
              valuePropName="checked"
            >
              <Switch checkedChildren="开启" unCheckedChildren="关闭" />
            </Form.Item>

            <Form.Item
              label="每分钟请求限制"
              name="rateLimitPerMinute"
            >
              <InputNumber min={10} max={10000} style={{ width: '100%' }} />
            </Form.Item>
          </ProCard>

          {/* 数据库配置 */}
          <ProCard title={<><DatabaseOutlined className="mr-2" />数据库配置</>}>
            <Form.Item
              label="连接字符串"
              name="dbConnectionString"
              rules={[{ required: true, message: '请输入数据库连接字符串' }]}
            >
              <Input.Password placeholder="请输入数据库连接字符串" />
            </Form.Item>

            <Form.Item
              label="最大连接数"
              name="dbMaxConnections"
            >
              <InputNumber min={10} max={200} style={{ width: '100%' }} />
            </Form.Item>

            <Form.Item
              label="命令超时时间（秒）"
              name="dbCommandTimeout"
            >
              <InputNumber min={10} max={300} style={{ width: '100%' }} />
            </Form.Item>

            <Form.Item
              label="启用连接池"
              name="enableConnectionPool"
              valuePropName="checked"
            >
              <Switch checkedChildren="开启" unCheckedChildren="关闭" />
            </Form.Item>
          </ProCard>

          {/* NATS 配置 */}
          <ProCard title={<><CloudServerOutlined className="mr-2" />NATS 配置</>}>
            <Form.Item
              label="NATS 服务器"
              name="natsServers"
              rules={[{ required: true, message: '请输入 NATS 服务器地址' }]}
            >
              <Input placeholder="nats://localhost:4222" />
            </Form.Item>

            <Form.Item
              label="集群名称"
              name="natsClusterName"
            >
              <Input placeholder="请输入集群名称" />
            </Form.Item>

            <Form.Item
              label="启用 JetStream"
              name="enableNatsJetStream"
              valuePropName="checked"
            >
              <Switch checkedChildren="开启" unCheckedChildren="关闭" />
            </Form.Item>

            <Form.Item
              label="连接超时时间（秒）"
              name="natsConnectionTimeout"
            >
              <InputNumber min={5} max={60} style={{ width: '100%' }} />
            </Form.Item>
          </ProCard>

          {/* 缓存配置 */}
          <ProCard title="缓存配置">
            <Form.Item
              label="缓存提供者"
              name="cacheProvider"
            >
              <Select>
                <Option value="Memory">内存缓存</Option>
                <Option value="Redis">Redis</Option>
                <Option value="Memcached">Memcached</Option>
              </Select>
            </Form.Item>

            <Form.Item
              label="连接字符串"
              name="cacheConnectionString"
            >
              <Input placeholder="localhost:6379" />
            </Form.Item>

            <Form.Item
              label="默认过期时间（秒）"
              name="cacheDefaultExpiration"
            >
              <InputNumber min={60} max={86400} style={{ width: '100%' }} />
            </Form.Item>

            <Form.Item
              label="启用分布式缓存"
              name="enableDistributedCache"
              valuePropName="checked"
            >
              <Switch checkedChildren="开启" unCheckedChildren="关闭" />
            </Form.Item>
          </ProCard>

          {/* 安全配置 */}
          <ProCard title={<><SecurityScanOutlined className="mr-2" />安全配置</>}>
            <Form.Item
              label="启用身份验证"
              name="enableAuthentication"
              valuePropName="checked"
            >
              <Switch checkedChildren="开启" unCheckedChildren="关闭" />
            </Form.Item>

            <Form.Item
              label="JWT 密钥"
              name="jwtSecret"
            >
              <Input.Password placeholder="请输入 JWT 密钥" />
            </Form.Item>

            <Form.Item
              label="Token 过期时间（小时）"
              name="tokenExpirationHours"
            >
              <InputNumber min={1} max={168} style={{ width: '100%' }} />
            </Form.Item>

            <Form.Item
              label="启用 HTTPS"
              name="enableHttps"
              valuePropName="checked"
            >
              <Switch checkedChildren="开启" unCheckedChildren="关闭" />
            </Form.Item>
          </ProCard>

          {/* 监控配置 */}
          <ProCard title="监控配置">
            <Form.Item
              label="启用健康检查"
              name="enableHealthCheck"
              valuePropName="checked"
            >
              <Switch checkedChildren="开启" unCheckedChildren="关闭" />
            </Form.Item>

            <Form.Item
              label="指标收集间隔（秒）"
              name="metricsCollectionInterval"
            >
              <InputNumber min={5} max={300} style={{ width: '100%' }} />
            </Form.Item>

            <Form.Item
              label="启用性能分析"
              name="enableProfiling"
              valuePropName="checked"
            >
              <Switch checkedChildren="开启" unCheckedChildren="关闭" />
            </Form.Item>

            <Form.Item
              label="日志保留天数"
              name="logRetentionDays"
            >
              <InputNumber min={1} max={365} style={{ width: '100%' }} />
            </Form.Item>
          </ProCard>
        </div>

        <Divider />

        <div className="text-center">
          <Space size="large">
            <Button size="large" onClick={handleReset}>
              重置配置
            </Button>
            <Button type="primary" size="large" icon={<SaveOutlined />} onClick={handleSave}>
              保存配置
            </Button>
          </Space>
        </div>
      </Form>
    </PageLayout>
  );
};

export default SystemConfig;
