const axios = require('axios');

const API_BASE_URL = 'http://localhost:5000/api';

// 工作流模板数据
const workflowTemplates = [
  {
    name: '用户注册流程',
    description: '处理新用户注册的完整流程，包括验证、创建账户和发送欢迎邮件',
    category: '用户管理',
    tags: ['用户', '注册', '邮件']
  },
  {
    name: '订单处理流程',
    description: '电商订单的完整处理流程，从下单到发货',
    category: '电商',
    tags: ['订单', '支付', '物流']
  },
  {
    name: '数据备份流程',
    description: '定期数据备份和验证流程',
    category: '运维',
    tags: ['备份', '数据库', '定时任务']
  },
  {
    name: '客户服务工单处理',
    description: '客户服务工单的自动分配和处理流程',
    category: '客服',
    tags: ['工单', '客服', '自动化']
  },
  {
    name: '财务报表生成',
    description: '月度财务报表的自动生成和分发流程',
    category: '财务',
    tags: ['报表', '财务', '月度']
  },
  {
    name: '员工入职流程',
    description: '新员工入职的完整流程，包括账户创建、权限分配等',
    category: '人事',
    tags: ['入职', '员工', '权限']
  },
  {
    name: '库存预警流程',
    description: '库存不足时的自动预警和补货流程',
    category: '库存',
    tags: ['库存', '预警', '补货']
  },
  {
    name: '营销活动审批',
    description: '营销活动的多级审批流程',
    category: '营销',
    tags: ['审批', '营销', '活动']
  },
  {
    name: '系统监控告警',
    description: '系统异常监控和告警通知流程',
    category: '监控',
    tags: ['监控', '告警', '系统']
  },
  {
    name: '合同审批流程',
    description: '合同的多级审批和签署流程',
    category: '法务',
    tags: ['合同', '审批', '签署']
  }
];

// 生成随机工作流数据
function generateWorkflow(template, index) {
  const publishStatuses = ['Draft', 'Published', 'Deprecated', 'Archived'];
  const authors = ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十'];
  
  const now = new Date();
  const createdAt = new Date(now.getTime() - Math.random() * 30 * 24 * 60 * 60 * 1000); // 30天内随机时间
  
  return {
    name: `${template.name} v${Math.floor(Math.random() * 5) + 1}.${Math.floor(Math.random() * 10)}`,
    description: template.description,
    version: `${Math.floor(Math.random() * 3) + 1}.${Math.floor(Math.random() * 10)}.${Math.floor(Math.random() * 10)}`,
    author: authors[Math.floor(Math.random() * authors.length)],
    isActive: Math.random() > 0.1, // 90% 概率为活跃
    publishStatus: publishStatuses[Math.floor(Math.random() * publishStatuses.length)],
    createdBy: authors[Math.floor(Math.random() * authors.length)],
    lastModifiedBy: authors[Math.floor(Math.random() * authors.length)],
    tags: template.tags.concat([template.category]),
    nodes: [
      {
        id: 'start',
        type: 'StartNode',
        name: '开始',
        position: { x: 100, y: 100 },
        properties: {}
      },
      {
        id: 'process1',
        type: 'ProcessNode',
        name: '处理步骤1',
        position: { x: 300, y: 100 },
        properties: {
          description: '执行主要业务逻辑'
        }
      },
      {
        id: 'end',
        type: 'EndNode',
        name: '结束',
        position: { x: 500, y: 100 },
        properties: {}
      }
    ],
    connections: [
      {
        id: 'conn1',
        sourceNodeId: 'start',
        targetNodeId: 'process1',
        sourcePort: 'output',
        targetPort: 'input'
      },
      {
        id: 'conn2',
        sourceNodeId: 'process1',
        targetNodeId: 'end',
        sourcePort: 'output',
        targetPort: 'input'
      }
    ],
    inputParameters: [
      {
        name: 'inputData',
        type: 'string',
        description: '输入数据',
        required: true
      }
    ],
    outputParameters: [
      {
        name: 'result',
        type: 'string',
        description: '处理结果'
      }
    ],
    configuration: {
      maxExecutionTime: 300 + Math.floor(Math.random() * 600), // 5-15分钟
      retryCount: Math.floor(Math.random() * 5) + 1,
      enableLogging: Math.random() > 0.2 // 80% 概率启用日志
    }
  };
}

// 创建工作流
async function createWorkflow(workflowData) {
  try {
    const response = await axios.post(`${API_BASE_URL}/workflows`, workflowData, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    return response.data;
  } catch (error) {
    console.error('创建工作流失败:', error.response?.data || error.message);
    return null;
  }
}

// 主函数
async function generateTestWorkflows() {
  console.log('开始生成50个测试工作流...');
  
  let successCount = 0;
  let failCount = 0;
  
  for (let i = 0; i < 50; i++) {
    const template = workflowTemplates[i % workflowTemplates.length];
    const workflowData = generateWorkflow(template, i);
    
    console.log(`正在创建第 ${i + 1} 个工作流: ${workflowData.name}`);
    
    const result = await createWorkflow(workflowData);
    if (result) {
      successCount++;
      console.log(`✅ 成功创建: ${workflowData.name}`);
    } else {
      failCount++;
      console.log(`❌ 创建失败: ${workflowData.name}`);
    }
    
    // 添加小延迟避免请求过快
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  console.log(`\n生成完成！成功: ${successCount}, 失败: ${failCount}`);
}

// 运行脚本
generateTestWorkflows().catch(console.error);
