using FlowCustomV1.Core.Interfaces;

namespace FlowCustomV1.Api.Services;

/// <summary>
/// Manages the lifecycle of the workflow engine as a background service.
/// </summary>
public class WorkflowEngineHostedService : IHostedService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<WorkflowEngineHostedService> _logger;
    private IServiceScope? _serviceScope;
    private IWorkflowEngine? _workflowEngine;

    public WorkflowEngineHostedService(IServiceProvider serviceProvider, ILogger<WorkflowEngineHostedService> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    public async Task StartAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Workflow Engine Hosted Service is starting.");

        // 创建一个长期存在的 scope，在服务生命周期内保持
        _serviceScope = _serviceProvider.CreateScope();
        _workflowEngine = _serviceScope.ServiceProvider.GetRequiredService<IWorkflowEngine>();

        await _workflowEngine.StartAsync(cancellationToken);

        _logger.LogInformation("Workflow Engine Hosted Service started successfully.");
    }

    public async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Workflow Engine Hosted Service is stopping.");

        try
        {
            if (_workflowEngine != null)
            {
                await _workflowEngine.StopAsync(cancellationToken);
            }
        }
        finally
        {
            _serviceScope?.Dispose();
            _serviceScope = null;
            _workflowEngine = null;
        }

        _logger.LogInformation("Workflow Engine Hosted Service stopped successfully.");
    }
}

