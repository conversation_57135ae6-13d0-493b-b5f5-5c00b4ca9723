# 性能测试脚本已移动到 tests 目录

## 📁 新的目录结构

性能测试脚本已从项目根目录移动到 `tests/performance_tests/` 目录中，这样更符合项目的整体测试结构。

### 🎯 移动原因

1. **结构统一**：与其他测试（单元测试、集成测试）保持一致的目录结构
2. **职责清晰**：所有测试相关的文件都在 `tests` 目录下
3. **项目整洁**：避免项目根目录文件过多
4. **标准化**：符合.NET项目的标准测试目录结构

### 📋 目录对比

#### 移动前
```
FlowCustomV1/
├── performance_tests/
│   ├── run_tests.py
│   ├── performance_test.py
│   └── ...
└── tests/
    ├── FlowCustomV1.Api.Tests/
    ├── FlowCustomV1.Core.Tests/
    └── ...
```

#### 移动后
```
FlowCustomV1/
└── tests/
    ├── performance_tests/          # 性能测试脚本
    │   ├── run_tests.py
    │   ├── performance_test.py
    │   └── ...
    ├── FlowCustomV1.Api.Tests/     # API单元测试
    ├── FlowCustomV1.Core.Tests/    # 核心单元测试
    ├── FlowCustomV1.Integration.Tests/  # 集成测试
    └── ...
```

### 🚀 更新后的使用方式

#### 交互式运行
```bash
python tests/performance_tests/run_tests.py
```

#### 命令行运行
```bash
# 快速测试
python tests/performance_tests/run_tests.py quick

# 日常开发测试套件
python tests/performance_tests/run_tests.py daily

# 完整测试套件
python tests/performance_tests/run_tests.py all
```

#### 直接运行单个脚本
```bash
# 快速性能验证
python tests/performance_tests/quick_performance_test.py

# 综合性能测试
python tests/performance_tests/performance_test.py

# 深度性能分析
python tests/performance_tests/performance_analysis.py

# 基础设施压力测试
python tests/performance_tests/infrastructure_stress_test.py

# 极限压力测试
python tests/performance_tests/extreme_stress_test.py
```

### 📊 测试类型分层

现在 `tests` 目录包含了完整的测试体系：

1. **单元测试** (`FlowCustomV1.*.Tests/`)
   - API层单元测试
   - 核心业务逻辑测试
   - 引擎组件测试

2. **集成测试** (`FlowCustomV1.Integration.Tests/`)
   - 数据库集成测试
   - 服务集成测试
   - 端到端测试

3. **性能测试** (`performance_tests/`)
   - API性能测试
   - 基础设施压力测试
   - 系统极限测试

4. **Docker测试** (`docker/`)
   - 容器化环境测试
   - 集群功能测试
   - 分布式场景测试

### ✅ 兼容性说明

- 所有脚本功能保持不变
- 配置文件和报告生成器正常工作
- 测试套件和交互式界面完全兼容
- 性能基准和评级体系不受影响

### 📖 相关文档已更新

- `tests/performance_tests/README.md` - 详细使用指南
- `tests/performance_tests/SCRIPTS_OVERVIEW.md` - 脚本清单
- `docs/测试管理/PERFORMANCE_TEST_SUMMARY.md` - 性能测试总结

---

**移动完成时间**：2025-09-08
**影响范围**：仅路径变更，功能完全保持
**向后兼容**：所有功能正常工作
