#!/usr/bin/env python3
"""
FlowCustomV1 集群状态验证脚本
验证Docker集群中所有节点的健康状态和连通性
"""

import os
import sys
import time
import json
import requests
import logging
from typing import List, Dict, Any
from concurrent.futures import ThreadPoolExecutor, as_completed

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ClusterVerifier:
    def __init__(self):
        self.cluster_endpoints = self._parse_cluster_endpoints()
        self.nats_servers = self._parse_nats_servers()
        self.verification_results = {}
        
    def _parse_cluster_endpoints(self) -> List[str]:
        """解析集群节点端点"""
        endpoints_str = os.getenv('CLUSTER_NODES', '')
        if not endpoints_str:
            logger.error("CLUSTER_NODES environment variable not set")
            sys.exit(1)
            
        endpoints = [f"http://{endpoint}" for endpoint in endpoints_str.split(',')]
        logger.info(f"Cluster endpoints: {endpoints}")
        return endpoints
    
    def _parse_nats_servers(self) -> List[str]:
        """解析NATS服务器端点"""
        servers_str = os.getenv('NATS_SERVERS', '')
        if not servers_str:
            logger.warning("NATS_SERVERS environment variable not set")
            return []
            
        servers = servers_str.split(',')
        logger.info(f"NATS servers: {servers}")
        return servers
    
    def verify_node_health(self, endpoint: str) -> Dict[str, Any]:
        """验证单个节点的健康状态"""
        node_name = endpoint.split('/')[-1].replace(':5000', '')
        result = {
            'endpoint': endpoint,
            'node_name': node_name,
            'healthy': False,
            'ready': False,
            'response_time': None,
            'node_info': None,
            'error': None
        }
        
        try:
            # 检查健康状态
            start_time = time.time()
            health_response = requests.get(f"{endpoint}/health", timeout=10)
            response_time = (time.time() - start_time) * 1000
            
            result['response_time'] = round(response_time, 2)
            
            if health_response.status_code == 200:
                result['healthy'] = True
                health_data = health_response.json()
                logger.info(f"✓ {node_name} is healthy (response: {response_time:.2f}ms)")
                
                # 检查就绪状态
                try:
                    ready_response = requests.get(f"{endpoint}/ready", timeout=5)
                    if ready_response.status_code == 200:
                        result['ready'] = True
                        logger.info(f"✓ {node_name} is ready")
                except:
                    logger.warning(f"⚠ {node_name} ready check failed")
                
                # 获取节点信息
                try:
                    info_response = requests.get(f"{endpoint}/api/node/info", timeout=5)
                    if info_response.status_code == 200:
                        result['node_info'] = info_response.json()
                        logger.info(f"✓ {node_name} node info retrieved")
                except:
                    logger.warning(f"⚠ {node_name} node info not available")
                    
            else:
                result['error'] = f"Health check failed with status {health_response.status_code}"
                logger.error(f"✗ {node_name} health check failed: {result['error']}")
                
        except requests.exceptions.RequestException as e:
            result['error'] = str(e)
            logger.error(f"✗ {node_name} connection failed: {e}")
        except Exception as e:
            result['error'] = f"Unexpected error: {str(e)}"
            logger.error(f"✗ {node_name} verification failed: {e}")
            
        return result
    
    def verify_nats_connectivity(self) -> Dict[str, Any]:
        """验证NATS集群连通性"""
        result = {
            'nats_healthy': False,
            'cluster_formed': False,
            'servers_status': {},
            'error': None
        }
        
        if not self.nats_servers:
            result['error'] = "No NATS servers configured"
            return result
        
        healthy_servers = 0
        
        for server in self.nats_servers:
            server_name = server.replace('nats://', '').replace(':4222', '')
            server_status = {
                'healthy': False,
                'cluster_info': None,
                'error': None
            }
            
            try:
                # 检查NATS服务器监控端点
                monitor_url = f"http://{server_name}:8222/healthz"
                response = requests.get(monitor_url, timeout=5)
                
                if response.status_code == 200:
                    server_status['healthy'] = True
                    healthy_servers += 1
                    logger.info(f"✓ NATS server {server_name} is healthy")
                    
                    # 获取集群信息
                    try:
                        cluster_response = requests.get(f"http://{server_name}:8222/routez", timeout=5)
                        if cluster_response.status_code == 200:
                            server_status['cluster_info'] = cluster_response.json()
                    except:
                        pass
                        
                else:
                    server_status['error'] = f"Health check failed with status {response.status_code}"
                    logger.error(f"✗ NATS server {server_name} health check failed")
                    
            except Exception as e:
                server_status['error'] = str(e)
                logger.error(f"✗ NATS server {server_name} connection failed: {e}")
            
            result['servers_status'][server_name] = server_status
        
        result['nats_healthy'] = healthy_servers > 0
        result['cluster_formed'] = healthy_servers >= 2  # 至少2个节点形成集群
        
        if result['nats_healthy']:
            logger.info(f"✓ NATS cluster status: {healthy_servers}/{len(self.nats_servers)} servers healthy")
        else:
            logger.error("✗ No healthy NATS servers found")
            
        return result
    
    def verify_cluster_connectivity(self) -> Dict[str, Any]:
        """验证集群节点间连通性"""
        result = {
            'connectivity_verified': False,
            'node_discovery': {},
            'error': None
        }
        
        # 从每个健康的节点获取其发现的其他节点
        healthy_nodes = [r for r in self.verification_results.values() if r.get('healthy', False)]
        
        if len(healthy_nodes) < 2:
            result['error'] = "Not enough healthy nodes for connectivity test"
            return result
        
        for node_result in healthy_nodes:
            endpoint = node_result['endpoint']
            node_name = node_result['node_name']
            
            try:
                # 获取节点发现的其他节点
                discovery_response = requests.get(f"{endpoint}/api/cluster/nodes", timeout=10)
                if discovery_response.status_code == 200:
                    discovered_nodes = discovery_response.json()
                    result['node_discovery'][node_name] = {
                        'discovered_count': len(discovered_nodes),
                        'discovered_nodes': [node.get('nodeId', 'unknown') for node in discovered_nodes]
                    }
                    logger.info(f"✓ {node_name} discovered {len(discovered_nodes)} other nodes")
                else:
                    result['node_discovery'][node_name] = {
                        'error': f"Discovery API failed with status {discovery_response.status_code}"
                    }
                    logger.warning(f"⚠ {node_name} node discovery failed")
                    
            except Exception as e:
                result['node_discovery'][node_name] = {
                    'error': str(e)
                }
                logger.error(f"✗ {node_name} connectivity check failed: {e}")
        
        # 验证连通性
        successful_discoveries = sum(1 for info in result['node_discovery'].values() 
                                   if 'discovered_count' in info and info['discovered_count'] > 0)
        
        result['connectivity_verified'] = successful_discoveries >= len(healthy_nodes) // 2
        
        return result
    
    def run_verification(self) -> bool:
        """运行完整的集群验证"""
        logger.info("=" * 60)
        logger.info("FlowCustomV1 Cluster Verification Started")
        logger.info("=" * 60)
        
        # 1. 验证节点健康状态
        logger.info("Phase 1: Verifying node health...")
        with ThreadPoolExecutor(max_workers=10) as executor:
            future_to_endpoint = {
                executor.submit(self.verify_node_health, endpoint): endpoint 
                for endpoint in self.cluster_endpoints
            }
            
            for future in as_completed(future_to_endpoint):
                endpoint = future_to_endpoint[future]
                try:
                    result = future.result()
                    self.verification_results[endpoint] = result
                except Exception as e:
                    logger.error(f"Node verification failed for {endpoint}: {e}")
                    self.verification_results[endpoint] = {
                        'endpoint': endpoint,
                        'healthy': False,
                        'error': str(e)
                    }
        
        # 2. 验证NATS连通性
        logger.info("Phase 2: Verifying NATS connectivity...")
        nats_result = self.verify_nats_connectivity()
        
        # 3. 验证集群连通性
        logger.info("Phase 3: Verifying cluster connectivity...")
        connectivity_result = self.verify_cluster_connectivity()
        
        # 4. 生成验证报告
        self._generate_verification_report(nats_result, connectivity_result)
        
        # 5. 判断验证结果
        healthy_nodes = sum(1 for r in self.verification_results.values() if r.get('healthy', False))
        total_nodes = len(self.cluster_endpoints)
        
        success = (
            healthy_nodes >= total_nodes * 0.8 and  # 至少80%的节点健康
            nats_result.get('nats_healthy', False) and  # NATS集群健康
            connectivity_result.get('connectivity_verified', False)  # 节点间连通性正常
        )
        
        logger.info("=" * 60)
        if success:
            logger.info("✅ Cluster verification PASSED")
            logger.info(f"Healthy nodes: {healthy_nodes}/{total_nodes}")
            logger.info("Cluster is ready for testing!")
        else:
            logger.error("❌ Cluster verification FAILED")
            logger.error(f"Healthy nodes: {healthy_nodes}/{total_nodes}")
            logger.error("Cluster is not ready for testing!")
        logger.info("=" * 60)
        
        return success
    
    def _generate_verification_report(self, nats_result: Dict, connectivity_result: Dict):
        """生成验证报告"""
        report = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'cluster_endpoints': self.cluster_endpoints,
            'nats_servers': self.nats_servers,
            'node_verification': self.verification_results,
            'nats_verification': nats_result,
            'connectivity_verification': connectivity_result
        }
        
        # 保存报告到文件
        report_path = '/app/test-results/cluster-verification-report.json'
        os.makedirs(os.path.dirname(report_path), exist_ok=True)
        
        try:
            with open(report_path, 'w') as f:
                json.dump(report, f, indent=2)
            logger.info(f"Verification report saved to: {report_path}")
        except Exception as e:
            logger.error(f"Failed to save verification report: {e}")

def main():
    """主函数"""
    verifier = ClusterVerifier()
    success = verifier.run_verification()
    
    # 设置退出代码
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
