using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using FlowCustomV1.Infrastructure;

namespace FlowCustomV1.Integration.Tests;

/// <summary>
/// 数据库清理装置，用于在测试前清理数据库
/// </summary>
public class DatabaseCleanupFixture : IDisposable
{
    private readonly IServiceProvider _serviceProvider;
    private readonly IConfiguration _configuration;

    public DatabaseCleanupFixture()
    {
        // 构建MySQL测试配置
        var configBuilder = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.mysql.json", optional: false, reloadOnChange: false);

        _configuration = configBuilder.Build();

        // 构建服务容器
        var services = new ServiceCollection();

        // 添加日志
        services.AddLogging(builder =>
        {
            builder.AddConsole();
            builder.AddDebug();
            builder.SetMinimumLevel(LogLevel.Debug);
        });

        // 添加基础设施
        services.AddInfrastructure(_configuration);

        _serviceProvider = services.BuildServiceProvider();
    }

    /// <summary>
    /// 清理数据库
    /// </summary>
    public async Task CleanupDatabaseAsync()
    {
        using var scope = _serviceProvider.CreateScope();
        var dbInitService = scope.ServiceProvider.GetRequiredService<FlowCustomV1.Infrastructure.Services.IDatabaseInitializationService>();
        
        // 重置数据库
        await dbInitService.ResetDatabaseAsync();
    }

    public void Dispose()
    {
        if (_serviceProvider is IDisposable disposable)
        {
            disposable.Dispose();
        }
    }
}