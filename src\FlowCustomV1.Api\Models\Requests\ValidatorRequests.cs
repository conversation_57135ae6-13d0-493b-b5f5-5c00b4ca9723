using System.ComponentModel.DataAnnotations;
using FlowCustomV1.Core.Models.Workflow;
using FlowCustomV1.Core.Interfaces.Validator;

namespace FlowCustomV1.Api.Models.Requests;

/// <summary>
/// 验证工作流请求
/// </summary>
public class ValidateWorkflowRequest
{
    /// <summary>
    /// 工作流定义
    /// </summary>
    [Required]
    public WorkflowDefinition WorkflowDefinition { get; set; } = null!;

    /// <summary>
    /// 验证选项
    /// </summary>
    public ValidationOptions? Options { get; set; }
}

/// <summary>
/// 验证节点请求
/// </summary>
public class ValidateNodeRequest
{
    /// <summary>
    /// 节点定义
    /// </summary>
    [Required]
    public NodeDefinition NodeDefinition { get; set; } = null!;

    /// <summary>
    /// 验证选项
    /// </summary>
    public ValidationOptions? Options { get; set; }
}

/// <summary>
/// 批量验证请求
/// </summary>
public class ValidateBatchRequest
{
    /// <summary>
    /// 工作流定义列表
    /// </summary>
    [Required]
    [MinLength(1)]
    public List<WorkflowDefinition> WorkflowDefinitions { get; set; } = new();

    /// <summary>
    /// 验证选项
    /// </summary>
    public ValidationOptions? Options { get; set; }

    /// <summary>
    /// 并行度
    /// </summary>
    [Range(1, 10)]
    public int Parallelism { get; set; } = 3;
}

/// <summary>
/// 添加验证规则请求
/// </summary>
public class AddValidationRuleRequest
{
    /// <summary>
    /// 验证规则
    /// </summary>
    [Required]
    public ValidationRule Rule { get; set; } = null!;
}

/// <summary>
/// 更新验证规则请求
/// </summary>
public class UpdateValidationRuleRequest
{
    /// <summary>
    /// 验证规则
    /// </summary>
    [Required]
    public ValidationRule Rule { get; set; } = null!;
}

/// <summary>
/// 设置规则启用状态请求
/// </summary>
public class SetRuleEnabledRequest
{
    /// <summary>
    /// 是否启用
    /// </summary>
    [Required]
    public bool Enabled { get; set; }
}

/// <summary>
/// 验证选项
/// </summary>
public class ValidationOptions
{
    /// <summary>
    /// 是否启用缓存
    /// </summary>
    public bool EnableCache { get; set; } = true;

    /// <summary>
    /// 缓存过期时间（分钟）
    /// </summary>
    [Range(1, 1440)]
    public int CacheExpirationMinutes { get; set; } = 60;

    /// <summary>
    /// 是否进行深度验证
    /// </summary>
    public bool DeepValidation { get; set; } = false;

    /// <summary>
    /// 是否检查循环依赖
    /// </summary>
    public bool CheckCyclicDependency { get; set; } = true;

    /// <summary>
    /// 是否进行性能分析
    /// </summary>
    public bool PerformanceAnalysis { get; set; } = false;

    /// <summary>
    /// 验证超时时间（秒）
    /// </summary>
    [Range(1, 300)]
    public int TimeoutSeconds { get; set; } = 30;

    /// <summary>
    /// 自定义验证规则ID列表
    /// </summary>
    public List<string> CustomRuleIds { get; set; } = new();

    /// <summary>
    /// 排除的验证规则ID列表
    /// </summary>
    public List<string> ExcludedRuleIds { get; set; } = new();
}

/// <summary>
/// 验证器健康状态
/// </summary>
public class ValidatorHealthStatus
{
    /// <summary>
    /// 是否健康
    /// </summary>
    public bool IsHealthy { get; set; }

    /// <summary>
    /// 检查时间戳
    /// </summary>
    public DateTime Timestamp { get; set; }

    /// <summary>
    /// 缓存统计信息
    /// </summary>
    public CacheStatistics? CacheStatistics { get; set; }

    /// <summary>
    /// 活跃规则数量
    /// </summary>
    public int ActiveRuleCount { get; set; }

    /// <summary>
    /// 版本信息
    /// </summary>
    public string Version { get; set; } = string.Empty;

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 详细信息
    /// </summary>
    public Dictionary<string, object> Details { get; set; } = new();
}








