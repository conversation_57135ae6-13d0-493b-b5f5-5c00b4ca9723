using FlowCustomV1.Core.Interfaces.Plugins;
using FlowCustomV1.Core.Models.Plugins;
using FlowCustomV1.Infrastructure.Services.Plugins;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Xunit;
using Xunit.Abstractions;

namespace FlowCustomV1.Infrastructure.Tests.Services.Plugins;

/// <summary>
/// 插件系统集成测试
/// </summary>
public class PluginSystemIntegrationTests : IDisposable
{
    private readonly ITestOutputHelper _output;
    private readonly ServiceProvider _serviceProvider;
    private readonly IPluginManager _pluginManager;
    private readonly IDynamicNodeCompiler _dynamicCompiler;

    public PluginSystemIntegrationTests(ITestOutputHelper output)
    {
        _output = output;

        // 设置服务容器
        var services = new ServiceCollection();
        
        // 添加日志服务
        services.AddLogging(builder =>
        {
            builder.AddConsole();
            builder.SetMinimumLevel(LogLevel.Debug);
        });

        // 添加插件系统服务
        services.AddSingleton<IDynamicNodeCompiler, NatashaCompilerService>();
        services.AddSingleton<IPluginLoader, McMasterPluginLoader>();
        services.AddSingleton<IPluginManager, PluginManager>();

        _serviceProvider = services.BuildServiceProvider();
        _pluginManager = _serviceProvider.GetRequiredService<IPluginManager>();
        _dynamicCompiler = _serviceProvider.GetRequiredService<IDynamicNodeCompiler>();
    }

    /// <summary>
    /// 测试插件管理器初始化
    /// </summary>
    [Fact]
    public async Task InitializeAsync_ShouldSucceed()
    {
        // Act
        await _pluginManager.InitializeAsync();

        // Assert
        var statistics = _pluginManager.GetPluginStatistics();
        Assert.True(statistics.TotalPlugins > 0, "应该有内置插件被注册");
        Assert.True(statistics.BuiltinPlugins > 0, "应该有内置插件");

        _output.WriteLine($"总插件数: {statistics.TotalPlugins}");
        _output.WriteLine($"内置插件数: {statistics.BuiltinPlugins}");
    }

    /// <summary>
    /// 测试获取内置插件执行器
    /// </summary>
    [Theory]
    [InlineData("Start")]
    [InlineData("End")]
    [InlineData("Task")]
    [InlineData("TimerTrigger")]
    [InlineData("EventTrigger")]
    [InlineData("WebhookTrigger")]
    [InlineData("HttpRequest")]
    [InlineData("DataProcessor")]
    [InlineData("NotificationSender")]
    [InlineData("IfCondition")]
    [InlineData("ForLoop")]
    [InlineData("ParallelExecution")]
    [InlineData("DataMapper")]
    [InlineData("DataFilter")]
    [InlineData("MySqlDatabase")]
    [InlineData("NatsMessage")]
    [InlineData("RestApiCall")]
    public async Task GetPluginExecutorAsync_BuiltinPlugin_ShouldReturnExecutor(string nodeType)
    {
        // Arrange
        await _pluginManager.InitializeAsync();

        // Act
        var executor = await _pluginManager.GetPluginExecutorAsync(nodeType);

        // Assert
        Assert.NotNull(executor);
        _output.WriteLine($"成功获取 {nodeType} 节点执行器");
    }

    /// <summary>
    /// 测试动态编译器编译内置插件
    /// </summary>
    [Fact]
    public async Task DynamicCompiler_CompileBuiltinPlugin_ShouldSucceed()
    {
        // Arrange
        await _dynamicCompiler.InitializeAsync();

        var builtinDefinition = new BuiltinPluginDefinition
        {
            NodeType = "TestNode",
            Name = "测试节点",
            DisplayName = "测试",
            Description = "用于测试的节点",
            Version = "1.0.0",
            Author = "Test",
            ExecutionLogic = new ExecutionLogicTemplate
            {
                Type = ExecutionLogicType.Code,
                CodeTemplate = @"
                    result.OutputData[""test""] = true;
                    result.OutputData[""timestamp""] = DateTime.UtcNow;
                    await Task.CompletedTask;
                "
            }
        };

        // Act
        var compilationResult = await _dynamicCompiler.CompileFromBuiltinDefinitionAsync(builtinDefinition);

        // Assert
        Assert.True(compilationResult.IsSuccess, $"编译应该成功: {string.Join("; ", compilationResult.Errors.Select(e => e.Message))}");
        Assert.NotNull(compilationResult.Executor);
        Assert.True(compilationResult.CompilationTimeMs >= 0);

        _output.WriteLine($"编译成功，耗时: {compilationResult.CompilationTimeMs}ms");
    }

    /// <summary>
    /// 测试动态编译器编译JSON配置插件
    /// </summary>
    [Fact]
    public async Task DynamicCompiler_CompileJsonPlugin_ShouldSucceed()
    {
        // Arrange
        await _dynamicCompiler.InitializeAsync();

        var jsonConfig = @"{
            ""nodeType"": ""JsonTestNode"",
            ""name"": ""JSON测试节点"",
            ""logic"": ""简单的JSON配置逻辑""
        }";

        // Act
        var compilationResult = await _dynamicCompiler.CompileFromJsonConfigAsync(jsonConfig, "JsonTestNode");

        // Assert
        Assert.True(compilationResult.IsSuccess, $"编译应该成功: {string.Join("; ", compilationResult.Errors.Select(e => e.Message))}");
        Assert.NotNull(compilationResult.Executor);

        _output.WriteLine($"JSON插件编译成功，耗时: {compilationResult.CompilationTimeMs}ms");
    }

    /// <summary>
    /// 测试插件管理器加载所有插件
    /// </summary>
    [Fact]
    public async Task LoadAllPluginsAsync_ShouldLoadBuiltinPlugins()
    {
        // Arrange
        await _pluginManager.InitializeAsync();

        // Act
        await _pluginManager.LoadAllPluginsAsync();

        // Assert
        var statistics = _pluginManager.GetPluginStatistics();
        Assert.True(statistics.LoadedPlugins > 0, "应该有插件被加载");
        Assert.True(statistics.BuiltinPlugins > 0, "应该有内置插件");

        // 验证内置插件是否已加载
        Assert.True(_pluginManager.IsPluginLoaded("Start"), "Start节点应该已加载");
        Assert.True(_pluginManager.IsPluginLoaded("End"), "End节点应该已加载");
        Assert.True(_pluginManager.IsPluginLoaded("Task"), "Task节点应该已加载");

        _output.WriteLine($"已加载插件数: {statistics.LoadedPlugins}");
        _output.WriteLine($"内置插件数: {statistics.BuiltinPlugins}");
    }

    /// <summary>
    /// 测试获取可用插件信息
    /// </summary>
    [Fact]
    public async Task GetAvailablePluginsAsync_ShouldReturnPluginList()
    {
        // Arrange
        await _pluginManager.InitializeAsync();

        // Act
        var availablePlugins = await _pluginManager.GetAvailablePluginsAsync();

        // Assert
        Assert.NotEmpty(availablePlugins);
        
        var startPlugin = availablePlugins.FirstOrDefault(p => p.NodeType == "Start");
        Assert.NotNull(startPlugin);
        Assert.Equal(PluginType.Builtin, startPlugin.PluginType);

        _output.WriteLine($"可用插件数: {availablePlugins.Count}");
        foreach (var plugin in availablePlugins)
        {
            _output.WriteLine($"- {plugin.NodeType}: {plugin.Name} ({plugin.PluginType})");
        }
    }

    /// <summary>
    /// 测试插件统计信息
    /// </summary>
    [Fact]
    public async Task GetPluginStatistics_ShouldReturnValidStatistics()
    {
        // Arrange
        await _pluginManager.InitializeAsync();
        await _pluginManager.LoadAllPluginsAsync();

        // 使用一些插件来更新统计信息
        await _pluginManager.GetPluginExecutorAsync("Start");
        await _pluginManager.GetPluginExecutorAsync("End");

        // Act
        var statistics = _pluginManager.GetPluginStatistics();

        // Assert
        Assert.True(statistics.TotalPlugins > 0);
        Assert.True(statistics.LoadedPlugins > 0);
        Assert.True(statistics.BuiltinPlugins > 0);
        Assert.True(statistics.TotalUsageCount >= 0);
        Assert.NotNull(statistics.ByType);
        Assert.Contains(PluginType.Builtin, statistics.ByType.Keys);

        _output.WriteLine($"插件统计信息:");
        _output.WriteLine($"- 总插件数: {statistics.TotalPlugins}");
        _output.WriteLine($"- 已加载插件数: {statistics.LoadedPlugins}");
        _output.WriteLine($"- 内置插件数: {statistics.BuiltinPlugins}");
        _output.WriteLine($"- 总使用次数: {statistics.TotalUsageCount}");
    }

    /// <summary>
    /// 测试编译缓存功能
    /// </summary>
    [Fact]
    public async Task CompilationCache_ShouldImprovePerformance()
    {
        // Arrange
        await _dynamicCompiler.InitializeAsync();

        var builtinDefinition = new BuiltinPluginDefinition
        {
            NodeType = "CacheTestNode",
            Name = "缓存测试节点",
            ExecutionLogic = new ExecutionLogicTemplate
            {
                Type = ExecutionLogicType.Code,
                CodeTemplate = "result.OutputData[\"cached\"] = true; await Task.CompletedTask;"
            }
        };

        // Act - 第一次编译
        var firstResult = await _dynamicCompiler.CompileFromBuiltinDefinitionAsync(builtinDefinition);
        var firstTime = firstResult.CompilationTimeMs;

        // Act - 第二次编译（应该使用缓存）
        var secondResult = await _dynamicCompiler.CompileFromBuiltinDefinitionAsync(builtinDefinition);
        var secondTime = secondResult.CompilationTimeMs;

        // Assert
        Assert.True(firstResult.IsSuccess);
        Assert.True(secondResult.IsSuccess);
        Assert.True(secondTime <= firstTime, $"第二次编译时间({secondTime}ms)应该小于等于第一次({firstTime}ms)");

        _output.WriteLine($"第一次编译时间: {firstTime}ms");
        _output.WriteLine($"第二次编译时间: {secondTime}ms");
    }

    /// <summary>
    /// 测试源代码验证功能
    /// </summary>
    [Theory]
    [InlineData("var x = 1;", true)]
    [InlineData("", false)]
    public async Task ValidateSourceCodeAsync_ShouldValidateCorrectly(string sourceCode, bool expectedValid)
    {
        // Arrange
        await _dynamicCompiler.InitializeAsync();

        // Act
        var validationResult = await _dynamicCompiler.ValidateSourceCodeAsync(sourceCode);

        // Assert
        Assert.Equal(expectedValid, validationResult.IsValid);

        _output.WriteLine($"源代码验证结果: {validationResult.IsValid}");
        if (!validationResult.IsValid)
        {
            _output.WriteLine($"错误: {string.Join("; ", validationResult.Errors)}");
        }
    }

    /// <summary>
    /// 测试内置节点库功能完整性
    /// </summary>
    [Fact]
    public async Task BuiltinNodeLibrary_ShouldHaveAllRequiredNodeTypes()
    {
        // Arrange
        await _pluginManager.InitializeAsync();
        await _pluginManager.LoadAllPluginsAsync();

        var expectedNodeTypes = new[]
        {
            // 基础控制节点
            "Start", "End", "Task",

            // 触发器节点
            "TimerTrigger", "EventTrigger", "WebhookTrigger",

            // 动作节点
            "HttpRequest", "DataProcessor", "NotificationSender",

            // 控制流节点
            "IfCondition", "ForLoop", "ParallelExecution",

            // 数据转换节点
            "DataMapper", "DataFilter",

            // 外部服务集成节点
            "MySqlDatabase", "NatsMessage", "RestApiCall"
        };

        // Act
        var availablePlugins = await _pluginManager.GetAvailablePluginsAsync();
        var availableNodeTypes = availablePlugins.Select(p => p.NodeType).ToList();

        // Assert
        foreach (var expectedType in expectedNodeTypes)
        {
            Assert.Contains(expectedType, availableNodeTypes);
            _output.WriteLine($"✅ 找到内置节点: {expectedType}");
        }

        var statistics = _pluginManager.GetPluginStatistics();
        Assert.True(statistics.BuiltinPlugins >= expectedNodeTypes.Length,
            $"内置插件数量应该至少有 {expectedNodeTypes.Length} 个，实际有 {statistics.BuiltinPlugins} 个");

        _output.WriteLine($"内置节点库统计:");
        _output.WriteLine($"- 总插件数: {statistics.TotalPlugins}");
        _output.WriteLine($"- 内置插件数: {statistics.BuiltinPlugins}");
        _output.WriteLine($"- 已加载插件数: {statistics.LoadedPlugins}");
    }

    /// <summary>
    /// 测试触发器节点功能
    /// </summary>
    [Theory]
    [InlineData("TimerTrigger")]
    [InlineData("EventTrigger")]
    [InlineData("WebhookTrigger")]
    public async Task TriggerNodes_ShouldExecuteSuccessfully(string nodeType)
    {
        // Arrange
        await _pluginManager.InitializeAsync();
        var executor = await _pluginManager.GetPluginExecutorAsync(nodeType);
        Assert.NotNull(executor);

        var context = new Core.Models.Workflow.NodeExecutionContext
        {
            WorkflowId = "test-workflow",
            ExecutionId = "test-execution",
            NodeId = "test-node",
            NodeType = nodeType,
            InputData = new System.Collections.Concurrent.ConcurrentDictionary<string, object>(new Dictionary<string, object>
            {
                ["intervalSeconds"] = 60,
                ["eventType"] = "test-event",
                ["webhookUrl"] = "/webhook/test"
            })
        };

        // Act
        var result = await executor.ExecuteAsync(context);

        // Assert
        Assert.NotNull(result);
        Assert.True(result.OutputData.ContainsKey("triggered"));
        Assert.True(result.OutputData.ContainsKey("triggerTime"));

        _output.WriteLine($"{nodeType} 执行成功:");
        _output.WriteLine($"- 触发状态: {result.OutputData.GetValueOrDefault("triggered")}");
        _output.WriteLine($"- 触发时间: {result.OutputData.GetValueOrDefault("triggerTime")}");
    }

    /// <summary>
    /// 测试外部服务集成节点功能
    /// </summary>
    [Theory]
    [InlineData("MySqlDatabase")]
    [InlineData("NatsMessage")]
    [InlineData("RestApiCall")]
    public async Task ExternalServiceNodes_ShouldExecuteSuccessfully(string nodeType)
    {
        // Arrange
        await _pluginManager.InitializeAsync();
        var executor = await _pluginManager.GetPluginExecutorAsync(nodeType);
        Assert.NotNull(executor);

        var context = new Core.Models.Workflow.NodeExecutionContext
        {
            WorkflowId = "test-workflow",
            ExecutionId = "test-execution",
            NodeId = "test-node",
            NodeType = nodeType,
            InputData = new System.Collections.Concurrent.ConcurrentDictionary<string, object>(new Dictionary<string, object>
            {
                // MySQL配置
                ["connectionString"] = "Server=localhost;Database=test;Uid=root;Pwd=password;",
                ["queryType"] = "SELECT",
                ["sqlQuery"] = "SELECT * FROM users LIMIT 10",
                ["parameters"] = new Dictionary<string, object> { ["userId"] = 123 },

                // NATS配置
                ["operation"] = "publish",
                ["subject"] = "test.message",
                ["message"] = "Hello from test",
                ["servers"] = new List<string> { "nats://localhost:4222" },

                // REST API配置
                ["url"] = "https://jsonplaceholder.typicode.com/posts/1",
                ["method"] = "GET",
                ["headers"] = new Dictionary<string, string> { ["User-Agent"] = "FlowCustomV1-Test" },
                ["contentType"] = "application/json",
                ["authentication"] = new Dictionary<string, string> { ["type"] = "bearer", ["token"] = "test-token" }
            })
        };

        // Act
        var result = await executor.ExecuteAsync(context);

        // Assert
        Assert.NotNull(result);
        Assert.True(result.OutputData.ContainsKey("success"));

        _output.WriteLine($"{nodeType} 执行成功:");
        _output.WriteLine($"- 执行状态: {result.OutputData.GetValueOrDefault("success")}");

        // 根据节点类型验证特定输出
        switch (nodeType)
        {
            case "MySqlDatabase":
                Assert.True(result.OutputData.ContainsKey("queryResult"));
                Assert.True(result.OutputData.ContainsKey("affectedRows"));
                _output.WriteLine($"- 查询结果数量: {((List<Dictionary<string, object>>)result.OutputData.GetValueOrDefault("queryResult", new List<Dictionary<string, object>>())).Count}");
                break;

            case "NatsMessage":
                Assert.True(result.OutputData.ContainsKey("operationResult"));
                Assert.True(result.OutputData.ContainsKey("subject"));
                _output.WriteLine($"- NATS操作: {result.OutputData.GetValueOrDefault("operation")}");
                _output.WriteLine($"- 主题: {result.OutputData.GetValueOrDefault("subject")}");
                break;

            case "RestApiCall":
                Assert.True(result.OutputData.ContainsKey("statusCode"));
                Assert.True(result.OutputData.ContainsKey("responseBody"));
                _output.WriteLine($"- HTTP状态码: {result.OutputData.GetValueOrDefault("statusCode")}");
                _output.WriteLine($"- 响应时间: {result.OutputData.GetValueOrDefault("duration")}ms");
                break;
        }
    }

    /// <summary>
    /// 测试MySQL数据库节点的不同查询类型
    /// </summary>
    [Theory]
    [InlineData("SELECT")]
    [InlineData("INSERT")]
    [InlineData("UPDATE")]
    [InlineData("DELETE")]
    public async Task MySqlDatabaseNode_ShouldHandleDifferentQueryTypes(string queryType)
    {
        // Arrange
        await _pluginManager.InitializeAsync();
        var executor = await _pluginManager.GetPluginExecutorAsync("MySqlDatabase");
        Assert.NotNull(executor);

        var context = new Core.Models.Workflow.NodeExecutionContext
        {
            WorkflowId = "test-workflow",
            ExecutionId = "test-execution",
            NodeId = "test-node",
            NodeType = "MySqlDatabase",
            InputData = new System.Collections.Concurrent.ConcurrentDictionary<string, object>(new Dictionary<string, object>
            {
                ["connectionString"] = "Server=localhost;Database=test;Uid=root;Pwd=password;",
                ["queryType"] = queryType,
                ["sqlQuery"] = $"{queryType} query example",
                ["parameters"] = new Dictionary<string, object>()
            })
        };

        // Act
        var result = await executor.ExecuteAsync(context);

        // Assert
        Assert.NotNull(result);
        Assert.True(result.OutputData.ContainsKey("success"));
        Assert.Equal(queryType, result.OutputData.GetValueOrDefault("queryType"));

        _output.WriteLine($"MySQL {queryType} 查询测试:");
        _output.WriteLine($"- 执行状态: {result.OutputData.GetValueOrDefault("success")}");
        _output.WriteLine($"- 查询类型: {result.OutputData.GetValueOrDefault("queryType")}");
    }

    /// <summary>
    /// 测试NATS消息节点的不同操作类型
    /// </summary>
    [Theory]
    [InlineData("publish")]
    [InlineData("subscribe")]
    [InlineData("request")]
    public async Task NatsMessageNode_ShouldHandleDifferentOperations(string operation)
    {
        // Arrange
        await _pluginManager.InitializeAsync();
        var executor = await _pluginManager.GetPluginExecutorAsync("NatsMessage");
        Assert.NotNull(executor);

        var context = new Core.Models.Workflow.NodeExecutionContext
        {
            WorkflowId = "test-workflow",
            ExecutionId = "test-execution",
            NodeId = "test-node",
            NodeType = "NatsMessage",
            InputData = new System.Collections.Concurrent.ConcurrentDictionary<string, object>(new Dictionary<string, object>
            {
                ["operation"] = operation,
                ["subject"] = $"test.{operation}",
                ["message"] = $"Test {operation} message",
                ["servers"] = new List<string> { "nats://localhost:4222" }
            })
        };

        // Act
        var result = await executor.ExecuteAsync(context);

        // Assert
        Assert.NotNull(result);
        Assert.True(result.OutputData.ContainsKey("success"));
        Assert.Equal(operation, result.OutputData.GetValueOrDefault("operation"));

        _output.WriteLine($"NATS {operation} 操作测试:");
        _output.WriteLine($"- 执行状态: {result.OutputData.GetValueOrDefault("success")}");
        _output.WriteLine($"- 操作类型: {result.OutputData.GetValueOrDefault("operation")}");
    }

    /// <summary>
    /// 测试REST API节点的不同HTTP方法
    /// </summary>
    [Theory]
    [InlineData("GET")]
    [InlineData("POST")]
    [InlineData("PUT")]
    [InlineData("DELETE")]
    [InlineData("PATCH")]
    public async Task RestApiCallNode_ShouldHandleDifferentHttpMethods(string method)
    {
        // Arrange
        await _pluginManager.InitializeAsync();
        var executor = await _pluginManager.GetPluginExecutorAsync("RestApiCall");
        Assert.NotNull(executor);

        var context = new Core.Models.Workflow.NodeExecutionContext
        {
            WorkflowId = "test-workflow",
            ExecutionId = "test-execution",
            NodeId = "test-node",
            NodeType = "RestApiCall",
            InputData = new System.Collections.Concurrent.ConcurrentDictionary<string, object>(new Dictionary<string, object>
            {
                ["url"] = "https://httpbin.org/anything",
                ["method"] = method,
                ["headers"] = new Dictionary<string, string> { ["User-Agent"] = "FlowCustomV1-Test" },
                ["body"] = method == "GET" ? "" : "{\"test\": \"data\"}",
                ["contentType"] = "application/json"
            })
        };

        // Act
        var result = await executor.ExecuteAsync(context);

        // Assert
        Assert.NotNull(result);
        Assert.True(result.OutputData.ContainsKey("success"));
        Assert.Equal(method, result.OutputData.GetValueOrDefault("method"));

        _output.WriteLine($"REST API {method} 方法测试:");
        _output.WriteLine($"- 执行状态: {result.OutputData.GetValueOrDefault("success")}");
        _output.WriteLine($"- HTTP方法: {result.OutputData.GetValueOrDefault("method")}");
        _output.WriteLine($"- 状态码: {result.OutputData.GetValueOrDefault("statusCode")}");
    }

    public void Dispose()
    {
        _serviceProvider?.Dispose();
    }
}
