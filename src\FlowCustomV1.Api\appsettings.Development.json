{"Logging": {"LogLevel": {"Default": "Debug", "Microsoft.AspNetCore": "Information", "Microsoft.EntityFrameworkCore": "Information", "FlowCustomV1": "Debug", "System": "Information"}}, "System": {"Network": {"HttpPort": 5000, "HttpsPort": 5001, "BindAddress": "127.0.0.1", "AllowedHosts": ["localhost", "127.0.0.1"], "CorsAllowedOrigins": ["http://localhost:3000", "http://localhost:8080"]}, "Performance": {"MaxConcurrentWorkflows": 50, "MaxConcurrentNodes": 25, "WorkerThreadPoolSize": 4, "IoThreadPoolSize": 2, "MessageQueueSize": 5000, "BatchSize": 50, "MemoryThresholdMb": 1024, "CpuThresholdPercent": 70}, "Timeouts": {"HttpRequestTimeoutSeconds": 60, "DatabaseTimeoutSeconds": 30, "WorkflowExecutionTimeoutMinutes": 30, "NodeExecutionTimeoutSeconds": 180, "MessageProcessingTimeoutSeconds": 15, "HealthCheckTimeoutSeconds": 10, "ShutdownTimeoutSeconds": 30}, "Node": {"NodeId": "flowcustom-dev-api-001", "NodeName": "FlowCustom Development API Node", "IpAddress": "127.0.0.1", "Roles": ["Api", "Worker", "Designer"], "Labels": {"environment": "development", "version": "v0.0.1.8", "developer": "local"}, "MaxMemoryMB": 512, "MaxCpuPercent": 70, "HeartbeatIntervalSeconds": 15, "EnableAutoDiscovery": true}, "Monitoring": {"Enabled": true, "MetricsCollectionIntervalSeconds": 30, "HealthCheckIntervalSeconds": 15, "LogLevel": "Debug", "EnablePerformanceCounters": true, "EnableDistributedTracing": true, "TracingSampleRate": 1, "MetricsRetentionDays": 7}}, "MessagingTopics": {"RootPrefix": "flowcustom-dev"}, "Database": {"Provider": "MySQL", "ConnectionString": "Server=mysql-dev;Port=3306;Database=flowcustom_dev;Uid=flowcustom;Pwd=FlowCustom@2025;"}, "Nats": {"Servers": ["nats://nats-dev-server:4222"], "ConnectionName": "FlowCustomV1-Dev-Api", "Username": "<PERSON><PERSON><PERSON>", "Password": "flowcustom_password", "ConnectionTimeoutSeconds": 30, "ReconnectIntervalSeconds": 2, "MaxReconnectAttempts": 20, "EnableAutoReconnect": true, "PingIntervalSeconds": 60, "MaxPingsOutstanding": 2, "EnableVerboseLogging": true, "JetStream": {"Enabled": true, "Domain": "flowcustom-dev-cluster", "ApiPrefix": "$JS.API", "DefaultStream": {"Name": "FLOWCUSTOM_DEV", "Subjects": ["flowcustom-dev.>"], "Storage": "memory", "MaxMessages": 100000, "MaxBytes": 104857600, "MaxAgeSeconds": 3600, "Replicas": 1}}, "ConnectionPool": {"Enabled": true, "MinConnections": 1, "MaxConnections": 5, "IdleTimeoutSeconds": 180, "AcquireTimeoutSeconds": 5}, "Serialization": {"Type": "json", "EnableCompression": false, "CompressionType": "gzip", "Json": {"UseCamelCase": true, "IgnoreNullValues": true, "WriteIndented": true, "DateTimeFormat": "yyyy-MM-ddTHH:mm:ss.fffZ"}}}, "NodeDiscovery": {"ClusterName": "FlowCustomV1-Dev", "HeartbeatIntervalSeconds": 15, "NodeTimeoutSeconds": 60, "NodeCleanupIntervalSeconds": 30, "DiscoveryTimeoutSeconds": 1, "EnableAutoRegistration": true, "EnableHeartbeat": true, "EnableNodeCleanup": true, "MaxRetryAttempts": 5, "RetryIntervalSeconds": 2, "NodeRole": "Api", "NodeTags": ["api", "worker", "designer", "development", "local"], "CapabilityTags": ["workflow-execution", "api-gateway", "http-api", "workflow-design"], "EnableVerboseLogging": true}}