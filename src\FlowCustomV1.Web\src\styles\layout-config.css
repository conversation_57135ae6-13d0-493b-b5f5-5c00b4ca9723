/**
 * 分层布局配置系统
 *
 * 🎯 全局基础配置 + 页面特定配置的分层架构
 * - 全局配置：绿黄蓝三层框架 + 通用参数
 * - 页面配置：各页面根据内容特点的特定参数
 */

:root {
  /* ========== 🟢 绿色框架 - Page Container ========== */
  --layout-content-padding: 8px;                   /* 内容区域内边距 */
  --layout-container-padding: 8px;                 /* 页面容器内边距 */

  /* ========== 🟡 黄色框架 - Page Header ========== */
  --layout-page-header-margin: 12px;               /* 页面头部下边距 */

  /* ========== 🔵 蓝色框架 - Page Content ========== */
  --layout-element-spacing: 4px;                   /* 元素间距 */

  /* ========== 🎯 ProLayout 基础参数 ========== */
  --layout-header-height: 64px;                    /* ProLayout头部高度 */
  --layout-content-height: calc(100vh - var(--layout-header-height));

  /* ========== 🟠 橙色框架 - 可配置组件容器 ========== */
  --layout-toolbar-height: 60px;                   /* 工具栏高度 */
  --layout-toolbar-margin: 16px;                   /* 工具栏下边距 */
  --layout-stats-container-height: 60px;          /* 统计卡片容器高度 */
  --layout-stats-container-margin: 2px;          /* 统计卡片容器下边距 - 增加与蓝色框间距 */
  --layout-stats-container-padding: 10px;          /* 统计卡片容器内边距 */

  /* ========== 🟣 紫色框架 - Table Container ========== */
  --layout-table-container-padding: 0px;           /* 表格容器内边距 */
  --layout-table-pagination-height: 20px;          /* 分页栏高度 */

  /* ========== 🎨 滚动条样式 ========== */
  --layout-scrollbar-width: 6px;                   /* 滚动条宽度 */
  --layout-scrollbar-color: #c1c1c1;               /* 滚动条颜色 */
  --layout-scrollbar-track-color: #f1f1f1;         /* 滚动条轨道颜色 */
  --layout-scrollbar-hover-color: #a8a8a8;         /* 滚动条悬停颜色 */

  /* ========== 📐 卡片和间距 ========== */
  --layout-card-gutter: 16px;                      /* 卡片间距 */
  --layout-card-margin: 16px;                      /* 卡片下边距 */
  --layout-card-padding: 16px;                     /* 卡片内边距 */
}

/* ========== 📱 响应式配置 ========== */

/* 移动端适配 */
@media (max-width: 768px) {
  :root {
    --layout-content-padding: 4px;
    --layout-container-padding: 4px;
    --layout-page-header-margin: 8px;
  }
}




