using FlowCustomV1.Core.Interfaces.Scheduling;
using FlowCustomV1.Core.Models.Cluster;
using FlowCustomV1.Core.Models.Scheduling;

namespace FlowCustomV1.Infrastructure.Services.Scheduling.Strategies;

/// <summary>
/// 最快响应时间策略
/// 选择平均响应时间最短的节点
/// </summary>
public class FastestResponseStrategy : ILoadBalancingStrategy
{
    private readonly LoadBalancingStrategyStats _stats = new() { StrategyName = "FastestResponse" };

    /// <inheritdoc />
    public string StrategyName => "FastestResponse";

    /// <inheritdoc />
    public string Description => "选择平均响应时间最短的节点";

    /// <inheritdoc />
    public int Weight { get; set; } = 30;

    /// <inheritdoc />
    public bool IsEnabled { get; set; } = true;

    /// <inheritdoc />
    public NodeInfo? SelectNode(
        IReadOnlyList<NodeInfo> candidateNodes,
        TaskRequirements taskRequirements,
        LoadBalancingContext context)
    {
        if (candidateNodes.Count == 0)
            return null;

        var startTime = DateTime.UtcNow;

        try
        {
            // 选择平均响应时间最短的节点
            var selectedNode = candidateNodes
                .OrderBy(n => n.Load.AverageResponseTimeMs)
                .ThenBy(n => n.Load.LoadScore) // 次要排序条件
                .First();

            // 更新统计
            _stats.UsageCount++;
            _stats.LastUsedAt = DateTime.UtcNow;
            _stats.AverageSelectionTimeMs = UpdateAverage(_stats.AverageSelectionTimeMs,
                (DateTime.UtcNow - startTime).TotalMilliseconds, _stats.UsageCount);

            var fitness = CalculateNodeFitness(selectedNode, taskRequirements, context);
            _stats.AverageFitnessScore = UpdateAverage(_stats.AverageFitnessScore, fitness, _stats.UsageCount);

            return selectedNode;
        }
        catch (Exception)
        {
            return candidateNodes.First();
        }
    }

    /// <inheritdoc />
    public double CalculateNodeFitness(
        NodeInfo node,
        TaskRequirements taskRequirements,
        LoadBalancingContext context)
    {
        // 基于响应时间计算适合度
        var responseTime = node.Load.AverageResponseTimeMs;
        
        if (responseTime <= 100) return 100;
        if (responseTime <= 500) return 80;
        if (responseTime <= 1000) return 60;
        if (responseTime <= 2000) return 40;
        if (responseTime <= 5000) return 20;
        return 10;
    }

    /// <inheritdoc />
    public void UpdateState(NodeInfo selectedNode, TaskExecutionResult? taskResult = null)
    {
        if (taskResult != null)
        {
            if (taskResult.IsSuccess)
            {
                _stats.SuccessCount++;
            }
            else
            {
                _stats.FailureCount++;
            }
        }
    }

    /// <inheritdoc />
    public void ResetState()
    {
        _stats.UsageCount = 0;
        _stats.SuccessCount = 0;
        _stats.FailureCount = 0;
        _stats.AverageSelectionTimeMs = 0;
        _stats.AverageFitnessScore = 0;
        _stats.StatsSince = DateTime.UtcNow;
    }

    /// <inheritdoc />
    public LoadBalancingStrategyStats GetStatistics()
    {
        return new LoadBalancingStrategyStats
        {
            StrategyName = _stats.StrategyName,
            UsageCount = _stats.UsageCount,
            SuccessCount = _stats.SuccessCount,
            FailureCount = _stats.FailureCount,
            AverageSelectionTimeMs = _stats.AverageSelectionTimeMs,
            AverageFitnessScore = _stats.AverageFitnessScore,
            LastUsedAt = _stats.LastUsedAt,
            StatsSince = _stats.StatsSince
        };
    }

    /// <summary>
    /// 更新平均值
    /// </summary>
    private double UpdateAverage(double currentAverage, double newValue, long count)
    {
        if (count <= 1) return newValue;
        return (currentAverage * (count - 1) + newValue) / count;
    }
}

/// <summary>
/// 随机选择策略
/// 随机选择一个节点
/// </summary>
public class RandomStrategy : ILoadBalancingStrategy
{
    private readonly Random _random = new();
    private readonly LoadBalancingStrategyStats _stats = new() { StrategyName = "Random" };

    /// <inheritdoc />
    public string StrategyName => "Random";

    /// <inheritdoc />
    public string Description => "随机选择一个节点";

    /// <inheritdoc />
    public int Weight { get; set; } = 10;

    /// <inheritdoc />
    public bool IsEnabled { get; set; } = true;

    /// <inheritdoc />
    public NodeInfo? SelectNode(
        IReadOnlyList<NodeInfo> candidateNodes,
        TaskRequirements taskRequirements,
        LoadBalancingContext context)
    {
        if (candidateNodes.Count == 0)
            return null;

        var startTime = DateTime.UtcNow;

        try
        {
            // 随机选择一个节点
            var selectedNode = candidateNodes[_random.Next(candidateNodes.Count)];

            // 更新统计
            _stats.UsageCount++;
            _stats.LastUsedAt = DateTime.UtcNow;
            _stats.AverageSelectionTimeMs = UpdateAverage(_stats.AverageSelectionTimeMs,
                (DateTime.UtcNow - startTime).TotalMilliseconds, _stats.UsageCount);

            var fitness = CalculateNodeFitness(selectedNode, taskRequirements, context);
            _stats.AverageFitnessScore = UpdateAverage(_stats.AverageFitnessScore, fitness, _stats.UsageCount);

            return selectedNode;
        }
        catch (Exception)
        {
            return candidateNodes.First();
        }
    }

    /// <inheritdoc />
    public double CalculateNodeFitness(
        NodeInfo node,
        TaskRequirements taskRequirements,
        LoadBalancingContext context)
    {
        // 随机策略不基于适合度，所有节点评分相等
        return 50.0;
    }

    /// <inheritdoc />
    public void UpdateState(NodeInfo selectedNode, TaskExecutionResult? taskResult = null)
    {
        if (taskResult != null)
        {
            if (taskResult.IsSuccess)
            {
                _stats.SuccessCount++;
            }
            else
            {
                _stats.FailureCount++;
            }
        }
    }

    /// <inheritdoc />
    public void ResetState()
    {
        _stats.UsageCount = 0;
        _stats.SuccessCount = 0;
        _stats.FailureCount = 0;
        _stats.AverageSelectionTimeMs = 0;
        _stats.AverageFitnessScore = 0;
        _stats.StatsSince = DateTime.UtcNow;
    }

    /// <inheritdoc />
    public LoadBalancingStrategyStats GetStatistics()
    {
        return new LoadBalancingStrategyStats
        {
            StrategyName = _stats.StrategyName,
            UsageCount = _stats.UsageCount,
            SuccessCount = _stats.SuccessCount,
            FailureCount = _stats.FailureCount,
            AverageSelectionTimeMs = _stats.AverageSelectionTimeMs,
            AverageFitnessScore = _stats.AverageFitnessScore,
            LastUsedAt = _stats.LastUsedAt,
            StatsSince = _stats.StatsSince
        };
    }

    /// <summary>
    /// 更新平均值
    /// </summary>
    private double UpdateAverage(double currentAverage, double newValue, long count)
    {
        if (count <= 1) return newValue;
        return (currentAverage * (count - 1) + newValue) / count;
    }
}

/// <summary>
/// 加权轮询策略
/// 根据节点权重进行轮询选择
/// </summary>
public class WeightedRoundRobinStrategy : ILoadBalancingStrategy
{
    private readonly Dictionary<string, NodeWeight> _nodeWeights = new();
    private readonly LoadBalancingStrategyStats _stats = new() { StrategyName = "WeightedRoundRobin" };
    private readonly object _lockObject = new();

    /// <inheritdoc />
    public string StrategyName => "WeightedRoundRobin";

    /// <inheritdoc />
    public string Description => "根据节点权重进行轮询选择";

    /// <inheritdoc />
    public int Weight { get; set; } = 25;

    /// <inheritdoc />
    public bool IsEnabled { get; set; } = true;

    /// <inheritdoc />
    public NodeInfo? SelectNode(
        IReadOnlyList<NodeInfo> candidateNodes,
        TaskRequirements taskRequirements,
        LoadBalancingContext context)
    {
        if (candidateNodes.Count == 0)
            return null;

        var startTime = DateTime.UtcNow;

        try
        {
            NodeInfo selectedNode;
            lock (_lockObject)
            {
                // 更新节点权重
                UpdateNodeWeights(candidateNodes);

                // 使用加权轮询算法选择节点
                selectedNode = SelectByWeightedRoundRobin(candidateNodes);
            }

            // 更新统计
            _stats.UsageCount++;
            _stats.LastUsedAt = DateTime.UtcNow;
            _stats.AverageSelectionTimeMs = UpdateAverage(_stats.AverageSelectionTimeMs,
                (DateTime.UtcNow - startTime).TotalMilliseconds, _stats.UsageCount);

            var fitness = CalculateNodeFitness(selectedNode, taskRequirements, context);
            _stats.AverageFitnessScore = UpdateAverage(_stats.AverageFitnessScore, fitness, _stats.UsageCount);

            return selectedNode;
        }
        catch (Exception)
        {
            return candidateNodes.First();
        }
    }

    /// <inheritdoc />
    public double CalculateNodeFitness(
        NodeInfo node,
        TaskRequirements taskRequirements,
        LoadBalancingContext context)
    {
        // 基于节点性能和负载计算权重
        var performanceScore = node.Capabilities.PerformanceLevel * 10; // 0-100
        var loadScore = Math.Max(0, 100 - node.Load.LoadScore);
        var healthScore = node.Health.IsHealthy ? 100 : node.Health.HealthScore;

        return (performanceScore + loadScore + healthScore) / 3.0;
    }

    /// <inheritdoc />
    public void UpdateState(NodeInfo selectedNode, TaskExecutionResult? taskResult = null)
    {
        if (taskResult != null)
        {
            if (taskResult.IsSuccess)
            {
                _stats.SuccessCount++;
            }
            else
            {
                _stats.FailureCount++;
            }
        }
    }

    /// <inheritdoc />
    public void ResetState()
    {
        lock (_lockObject)
        {
            _nodeWeights.Clear();
            _stats.UsageCount = 0;
            _stats.SuccessCount = 0;
            _stats.FailureCount = 0;
            _stats.AverageSelectionTimeMs = 0;
            _stats.AverageFitnessScore = 0;
            _stats.StatsSince = DateTime.UtcNow;
        }
    }

    /// <inheritdoc />
    public LoadBalancingStrategyStats GetStatistics()
    {
        return new LoadBalancingStrategyStats
        {
            StrategyName = _stats.StrategyName,
            UsageCount = _stats.UsageCount,
            SuccessCount = _stats.SuccessCount,
            FailureCount = _stats.FailureCount,
            AverageSelectionTimeMs = _stats.AverageSelectionTimeMs,
            AverageFitnessScore = _stats.AverageFitnessScore,
            LastUsedAt = _stats.LastUsedAt,
            StatsSince = _stats.StatsSince
        };
    }

    /// <summary>
    /// 更新节点权重
    /// </summary>
    private void UpdateNodeWeights(IReadOnlyList<NodeInfo> nodes)
    {
        foreach (var node in nodes)
        {
            if (!_nodeWeights.ContainsKey(node.NodeId))
            {
                _nodeWeights[node.NodeId] = new NodeWeight
                {
                    NodeId = node.NodeId,
                    Weight = CalculateNodeWeight(node),
                    CurrentWeight = 0
                };
            }
            else
            {
                // 动态更新权重
                _nodeWeights[node.NodeId].Weight = CalculateNodeWeight(node);
            }
        }
    }

    /// <summary>
    /// 计算节点权重
    /// </summary>
    private int CalculateNodeWeight(NodeInfo node)
    {
        var performanceWeight = node.Capabilities.PerformanceLevel;
        var healthWeight = node.Health.IsHealthy ? 10 : (int)(node.Health.HealthScore / 10);
        var loadWeight = Math.Max(1, 10 - (int)(node.Load.LoadScore / 10));

        return Math.Max(1, performanceWeight + healthWeight + loadWeight);
    }

    /// <summary>
    /// 使用加权轮询算法选择节点
    /// </summary>
    private NodeInfo SelectByWeightedRoundRobin(IReadOnlyList<NodeInfo> nodes)
    {
        var maxWeight = _nodeWeights.Values.Max(w => w.Weight);
        var totalWeight = _nodeWeights.Values.Sum(w => w.Weight);

        // 更新当前权重
        foreach (var weight in _nodeWeights.Values)
        {
            weight.CurrentWeight += weight.Weight;
        }

        // 选择当前权重最高的节点
        var selectedWeight = _nodeWeights.Values.OrderByDescending(w => w.CurrentWeight).First();
        selectedWeight.CurrentWeight -= totalWeight;

        return nodes.First(n => n.NodeId == selectedWeight.NodeId);
    }

    /// <summary>
    /// 更新平均值
    /// </summary>
    private double UpdateAverage(double currentAverage, double newValue, long count)
    {
        if (count <= 1) return newValue;
        return (currentAverage * (count - 1) + newValue) / count;
    }

    /// <summary>
    /// 节点权重信息
    /// </summary>
    private class NodeWeight
    {
        public string NodeId { get; set; } = string.Empty;
        public int Weight { get; set; }
        public int CurrentWeight { get; set; }
    }
}
