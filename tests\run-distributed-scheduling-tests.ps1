# FlowCustomV1 分布式任务调度测试运行脚本
# 用于运行 v0.0.1.7 版本的所有测试用例

param(
    [string]$TestCategory = "All",
    [string]$OutputFormat = "detailed",
    [switch]$GenerateReport,
    [switch]$RunPerformanceTests,
    [switch]$RunResilienceTests,
    [string]$LogLevel = "Information"
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# 测试配置
$TestProjectPath = "FlowCustomV1.Tests"
$ReportPath = "TestResults"
$TestResultsFile = "distributed-scheduling-test-results.xml"
$CoverageFile = "coverage.xml"

Write-Host "=== FlowCustomV1 分布式任务调度测试套件 v0.0.1.7 ===" -ForegroundColor Green
Write-Host "开始时间: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor Gray
Write-Host ""

# 创建测试结果目录
if (!(Test-Path $ReportPath)) {
    New-Item -ItemType Directory -Path $ReportPath -Force | Out-Null
    Write-Host "✓ 创建测试结果目录: $ReportPath" -ForegroundColor Green
}

# 构建测试项目
Write-Host "--- 构建测试项目 ---" -ForegroundColor Yellow
try {
    dotnet build $TestProjectPath --configuration Release --no-restore
    if ($LASTEXITCODE -ne 0) {
        throw "构建失败"
    }
    Write-Host "✓ 测试项目构建成功" -ForegroundColor Green
}
catch {
    Write-Host "✗ 测试项目构建失败: $_" -ForegroundColor Red
    exit 1
}

# 定义测试类别
$TestCategories = @{
    "Unit" = @(
        "FlowCustomV1.Tests.Scheduling.TaskDistributionServiceTests",
        "FlowCustomV1.Tests.Scheduling.LoadBalancingStrategyTests",
        "FlowCustomV1.Tests.Scheduling.TaskExecutionTrackerTests"
    )
    "Integration" = @(
        "FlowCustomV1.Tests.Integration.DistributedTaskSchedulingIntegrationTests"
    )
    "Performance" = @(
        "FlowCustomV1.Tests.Performance.TaskSchedulingBenchmarkTests"
    )
    "Resilience" = @(
        "FlowCustomV1.Tests.Resilience.TaskSchedulingResilienceTests"
    )
    "Suite" = @(
        "FlowCustomV1.Tests.TestSuites.DistributedTaskSchedulingTestSuite"
    )
}

# 运行测试的函数
function Run-TestCategory {
    param(
        [string]$CategoryName,
        [array]$TestClasses
    )
    
    Write-Host "--- 运行 $CategoryName 测试 ---" -ForegroundColor Yellow
    
    $categoryResults = @{
        Total = 0
        Passed = 0
        Failed = 0
        Skipped = 0
        Duration = 0
    }
    
    foreach ($testClass in $TestClasses) {
        Write-Host "运行: $testClass" -ForegroundColor Cyan
        
        $testArgs = @(
            "test"
            $TestProjectPath
            "--filter", "FullyQualifiedName~$testClass"
            "--logger", "trx;LogFileName=$CategoryName-$($testClass.Split('.')[-1]).trx"
            "--results-directory", $ReportPath
            "--verbosity", $LogLevel.ToLower()
            "--no-build"
            "--configuration", "Release"
        )
        
        if ($GenerateReport) {
            $testArgs += "--collect", "XPlat Code Coverage"
        }
        
        try {
            $startTime = Get-Date
            & dotnet @testArgs
            $endTime = Get-Date
            $duration = ($endTime - $startTime).TotalSeconds
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "  ✓ $testClass 通过 ($([math]::Round($duration, 2))s)" -ForegroundColor Green
                $categoryResults.Passed++
            } else {
                Write-Host "  ✗ $testClass 失败 ($([math]::Round($duration, 2))s)" -ForegroundColor Red
                $categoryResults.Failed++
            }
            
            $categoryResults.Total++
            $categoryResults.Duration += $duration
        }
        catch {
            Write-Host "  ✗ $testClass 执行异常: $_" -ForegroundColor Red
            $categoryResults.Failed++
            $categoryResults.Total++
        }
    }
    
    Write-Host ""
    Write-Host "$CategoryName 测试结果:" -ForegroundColor White
    Write-Host "  总计: $($categoryResults.Total)" -ForegroundColor Gray
    Write-Host "  通过: $($categoryResults.Passed)" -ForegroundColor Green
    Write-Host "  失败: $($categoryResults.Failed)" -ForegroundColor Red
    Write-Host "  跳过: $($categoryResults.Skipped)" -ForegroundColor Yellow
    Write-Host "  耗时: $([math]::Round($categoryResults.Duration, 2))s" -ForegroundColor Gray
    Write-Host ""
    
    return $categoryResults
}

# 主测试执行逻辑
$overallResults = @{
    Total = 0
    Passed = 0
    Failed = 0
    Skipped = 0
    Duration = 0
    Categories = @{}
}

$startTime = Get-Date

try {
    # 根据参数决定运行哪些测试
    $categoriesToRun = @()
    
    switch ($TestCategory.ToLower()) {
        "all" { 
            $categoriesToRun = @("Unit", "Integration", "Suite")
            if ($RunPerformanceTests) { $categoriesToRun += "Performance" }
            if ($RunResilienceTests) { $categoriesToRun += "Resilience" }
        }
        "unit" { $categoriesToRun = @("Unit") }
        "integration" { $categoriesToRun = @("Integration") }
        "performance" { $categoriesToRun = @("Performance") }
        "resilience" { $categoriesToRun = @("Resilience") }
        "suite" { $categoriesToRun = @("Suite") }
        default { 
            Write-Host "未知的测试类别: $TestCategory" -ForegroundColor Red
            Write-Host "支持的类别: All, Unit, Integration, Performance, Resilience, Suite" -ForegroundColor Yellow
            exit 1
        }
    }
    
    # 运行选定的测试类别
    foreach ($category in $categoriesToRun) {
        if ($TestCategories.ContainsKey($category)) {
            $categoryResult = Run-TestCategory -CategoryName $category -TestClasses $TestCategories[$category]
            
            $overallResults.Total += $categoryResult.Total
            $overallResults.Passed += $categoryResult.Passed
            $overallResults.Failed += $categoryResult.Failed
            $overallResults.Skipped += $categoryResult.Skipped
            $overallResults.Duration += $categoryResult.Duration
            $overallResults.Categories[$category] = $categoryResult
        }
    }
}
catch {
    Write-Host "测试执行过程中发生错误: $_" -ForegroundColor Red
    exit 1
}

$endTime = Get-Date
$totalDuration = ($endTime - $startTime).TotalSeconds

# 生成测试报告
if ($GenerateReport) {
    Write-Host "--- 生成测试报告 ---" -ForegroundColor Yellow
    
    $reportContent = @"
# FlowCustomV1 分布式任务调度测试报告 v0.0.1.7

## 测试概览
- **开始时间**: $($startTime.ToString('yyyy-MM-dd HH:mm:ss'))
- **结束时间**: $($endTime.ToString('yyyy-MM-dd HH:mm:ss'))
- **总耗时**: $([math]::Round($totalDuration, 2)) 秒
- **测试类别**: $($categoriesToRun -join ', ')

## 测试结果
- **总测试数**: $($overallResults.Total)
- **通过**: $($overallResults.Passed)
- **失败**: $($overallResults.Failed)
- **跳过**: $($overallResults.Skipped)
- **成功率**: $([math]::Round(($overallResults.Passed / [math]::Max($overallResults.Total, 1)) * 100, 2))%

## 分类结果
"@

    foreach ($category in $overallResults.Categories.Keys) {
        $categoryResult = $overallResults.Categories[$category]
        $successRate = if ($categoryResult.Total -gt 0) { 
            [math]::Round(($categoryResult.Passed / $categoryResult.Total) * 100, 2) 
        } else { 0 }
        
        $reportContent += @"

### $category 测试
- 总计: $($categoryResult.Total)
- 通过: $($categoryResult.Passed)
- 失败: $($categoryResult.Failed)
- 成功率: $successRate%
- 耗时: $([math]::Round($categoryResult.Duration, 2))s
"@
    }
    
    $reportContent += @"

## 测试环境
- **.NET 版本**: $(dotnet --version)
- **操作系统**: $([System.Environment]::OSVersion.VersionString)
- **机器名**: $([System.Environment]::MachineName)
- **处理器数**: $([System.Environment]::ProcessorCount)

## 版本信息
- **FlowCustomV1 版本**: v0.0.1.7
- **测试套件版本**: v0.0.1.7
- **生成时间**: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
"@
    
    $reportPath = Join-Path $ReportPath "test-report.md"
    $reportContent | Out-File -FilePath $reportPath -Encoding UTF8
    Write-Host "✓ 测试报告已生成: $reportPath" -ForegroundColor Green
}

# 输出最终结果
Write-Host "=== 测试执行完成 ===" -ForegroundColor Green
Write-Host "结束时间: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor Gray
Write-Host "总耗时: $([math]::Round($totalDuration, 2)) 秒" -ForegroundColor Gray
Write-Host ""

Write-Host "最终结果:" -ForegroundColor White
Write-Host "  总测试数: $($overallResults.Total)" -ForegroundColor Gray
Write-Host "  通过: $($overallResults.Passed)" -ForegroundColor Green
Write-Host "  失败: $($overallResults.Failed)" -ForegroundColor Red
Write-Host "  跳过: $($overallResults.Skipped)" -ForegroundColor Yellow

$successRate = if ($overallResults.Total -gt 0) { 
    [math]::Round(($overallResults.Passed / $overallResults.Total) * 100, 2) 
} else { 0 }
Write-Host "  成功率: $successRate%" -ForegroundColor $(if ($successRate -eq 100) { "Green" } elseif ($successRate -ge 80) { "Yellow" } else { "Red" })

# 设置退出代码
if ($overallResults.Failed -eq 0) {
    Write-Host ""
    Write-Host "🎉 所有测试通过！" -ForegroundColor Green
    exit 0
} else {
    Write-Host ""
    Write-Host "❌ 有测试失败，请检查测试结果" -ForegroundColor Red
    exit 1
}
