using FlowCustomV1.Core.Interfaces;
using FlowCustomV1.Core.Models.Cluster;
using FlowCustomV1.Core.Models.Messages;
using System.Collections.Concurrent;

namespace FlowCustomV1.Core.Services;

/// <summary>
/// 集群服务实现
/// 提供完整的集群节点管理、发现和通信功能
/// </summary>
public class ClusterService : IClusterService, IDisposable
{
    private readonly ILoggingService _loggingService;
    private readonly ConcurrentDictionary<string, NodeInfo> _nodes;
    private readonly ConcurrentDictionary<string, DateTime> _lastHeartbeats;
    private readonly Timer? _heartbeatTimer;
    private readonly Timer? _healthCheckTimer;
    private bool _isInitialized = false;
    private bool _isStarted = false;
    private readonly object _lockObject = new object();

    /// <summary>
    /// 初始化集群服务
    /// </summary>
    /// <param name="loggingService">日志服务</param>
    public ClusterService(ILoggingService loggingService)
    {
        _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
        _nodes = new ConcurrentDictionary<string, NodeInfo>();
        _lastHeartbeats = new ConcurrentDictionary<string, DateTime>();

        // 创建定时器（但不启动）
        _heartbeatTimer = new Timer(SendPeriodicHeartbeat, null, Timeout.Infinite, Timeout.Infinite);
        _healthCheckTimer = new Timer(PerformHealthChecks, null, Timeout.Infinite, Timeout.Infinite);
    }

    /// <inheritdoc />
    public NodeInfo CurrentNode { get; private set; } = new();

    /// <inheritdoc />
    public bool IsInitialized => _isInitialized;

    /// <inheritdoc />
    public string ClusterName { get; private set; } = string.Empty;

    /// <inheritdoc />
    public event EventHandler<NodeJoinedEventArgs>? NodeJoined;

    /// <inheritdoc />
    public event EventHandler<NodeLeftEventArgs>? NodeLeft;

    /// <inheritdoc />
    public event EventHandler<NodeStatusChangedEventArgs>? NodeStatusChanged;

    /// <inheritdoc />
    public event EventHandler<MessageReceivedEventArgs>? MessageReceived;

    /// <inheritdoc />
    public async Task InitializeAsync(string clusterName, NodeInfo nodeInfo, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(clusterName);
        ArgumentNullException.ThrowIfNull(nodeInfo);

        if (_isInitialized)
        {
            _loggingService.LogWarning("Cluster service is already initialized");
            return;
        }

        _loggingService.LogInformation("Initializing cluster service for cluster '{ClusterName}' with node {NodeId}",
            clusterName, nodeInfo.NodeId);

        ClusterName = clusterName;
        CurrentNode = nodeInfo.Clone();
        CurrentNode.ClusterName = clusterName;
        CurrentNode.Timestamps.CreatedAt = DateTime.UtcNow;
        CurrentNode.Timestamps.LastActiveAt = DateTime.UtcNow;

        // 注册当前节点
        await RegisterNodeAsync(CurrentNode, cancellationToken);

        _isInitialized = true;
        _loggingService.LogInformation("Cluster service initialized successfully");
    }

    /// <inheritdoc />
    public async Task StartAsync(CancellationToken cancellationToken = default)
    {
        if (!_isInitialized)
        {
            throw new InvalidOperationException("Cluster service must be initialized before starting");
        }

        if (_isStarted)
        {
            _loggingService.LogWarning("Cluster service is already started");
            return;
        }

        _loggingService.LogInformation("Starting cluster service");

        lock (_lockObject)
        {
            if (_isStarted) return;

            // 启动定时器
            _heartbeatTimer?.Change(TimeSpan.Zero, TimeSpan.FromSeconds(30)); // 每30秒发送心跳
            _healthCheckTimer?.Change(TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(5)); // 每5分钟健康检查

            _isStarted = true;
        }

        // 标记当前节点为启动状态
        CurrentNode.Timestamps.MarkStarted();
        CurrentNode.Status = NodeStatus.Healthy;

        _loggingService.LogInformation("Cluster service started successfully");
        await Task.CompletedTask;
    }

    /// <inheritdoc />
    public async Task StopAsync(CancellationToken cancellationToken = default)
    {
        if (!_isStarted)
        {
            _loggingService.LogWarning("Cluster service is not started");
            return;
        }

        _loggingService.LogInformation("Stopping cluster service");

        lock (_lockObject)
        {
            if (!_isStarted) return;

            // 停止定时器
            _heartbeatTimer?.Change(Timeout.Infinite, Timeout.Infinite);
            _healthCheckTimer?.Change(Timeout.Infinite, Timeout.Infinite);

            _isStarted = false;
        }

        // 标记当前节点为停止状态
        CurrentNode.Timestamps.MarkStopped();
        CurrentNode.Status = NodeStatus.Offline;

        // 注销当前节点
        await UnregisterNodeAsync(CurrentNode.NodeId, cancellationToken);

        _loggingService.LogInformation("Cluster service stopped successfully");
    }

    /// <inheritdoc />
    public Task RegisterNodeAsync(NodeInfo nodeInfo, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(nodeInfo);

        _loggingService.LogInformation("Registering node {NodeId} ({NodeName}) to cluster",
            nodeInfo.NodeId, nodeInfo.NodeName);

        // 更新节点信息
        nodeInfo.Timestamps.CreatedAt = DateTime.UtcNow;
        nodeInfo.Timestamps.LastActiveAt = DateTime.UtcNow;
        nodeInfo.Status = NodeStatus.Healthy;

        // 添加到节点集合
        _nodes.AddOrUpdate(nodeInfo.NodeId, nodeInfo, (key, existing) =>
        {
            _loggingService.LogInformation("Updating existing node {NodeId}", key);
            return nodeInfo;
        });

        // 更新心跳时间
        _lastHeartbeats.AddOrUpdate(nodeInfo.NodeId, DateTime.UtcNow, (key, existing) => DateTime.UtcNow);

        // 触发节点加入事件
        NodeJoined?.Invoke(this, new NodeJoinedEventArgs
        {
            NodeInfo = nodeInfo.Clone(),
            JoinedAt = DateTime.UtcNow
        });

        _loggingService.LogInformation("Node {NodeId} registered successfully", nodeInfo.NodeId);
        return Task.CompletedTask;
    }

    /// <inheritdoc />
    public Task UnregisterNodeAsync(string nodeId, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(nodeId);

        _loggingService.LogInformation("Unregistering node {NodeId} from cluster", nodeId);

        if (_nodes.TryRemove(nodeId, out var nodeInfo))
        {
            _lastHeartbeats.TryRemove(nodeId, out _);

            // 触发节点离开事件
            NodeLeft?.Invoke(this, new NodeLeftEventArgs
            {
                NodeId = nodeId,
                Reason = "Unregistered",
                LeftAt = DateTime.UtcNow
            });

            _loggingService.LogInformation("Node {NodeId} unregistered successfully", nodeId);
        }
        else
        {
            _loggingService.LogWarning("Node {NodeId} not found for unregistration", nodeId);
        }

        return Task.CompletedTask;
    }

    /// <inheritdoc />
    public Task<IEnumerable<NodeInfo>> DiscoverNodesAsync(NodeDiscoveryQuery? query = null, CancellationToken cancellationToken = default)
    {
        _loggingService.LogInformation("Discovering nodes with query: {Query}", query?.ToString() ?? "null");

        var allNodes = _nodes.Values.ToList();

        if (query == null)
        {
            return Task.FromResult<IEnumerable<NodeInfo>>(allNodes);
        }

        var result = allNodes.AsEnumerable();

        // 按节点模式过滤
        if (query.NodeMode.HasValue)
        {
            result = result.Where(n => n.Mode == query.NodeMode.Value);
        }

        // 按节点状态过滤
        if (query.NodeStatus.HasValue)
        {
            result = result.Where(n => n.Status == query.NodeStatus.Value);
        }

        // 按性能等级过滤
        if (query.MinPerformanceLevel.HasValue)
        {
            result = result.Where(n => n.Capabilities.PerformanceLevel >= query.MinPerformanceLevel.Value);
        }

        // 按最大负载评分过滤
        if (query.MaxLoadScore.HasValue)
        {
            result = result.Where(n => n.Load.LoadScore <= query.MaxLoadScore.Value);
        }

        // 按必需的能力标签过滤
        if (query.RequiredCapabilityTags.Any())
        {
            result = result.Where(n => query.RequiredCapabilityTags.All(tag => n.Capabilities.Tags.Contains(tag)));
        }

        // 按支持的执行器类型过滤
        if (query.SupportedExecutorTypes.Any())
        {
            result = result.Where(n => query.SupportedExecutorTypes.Any(type => n.Capabilities.SupportedExecutorTypes.Contains(type)));
        }

        // 按集群名称过滤
        if (!string.IsNullOrWhiteSpace(query.ClusterNameFilter))
        {
            result = result.Where(n => n.ClusterName.Contains(query.ClusterNameFilter, StringComparison.OrdinalIgnoreCase));
        }

        var finalResult = result.ToList();
        _loggingService.LogDebug("Discovery found {Count} nodes matching criteria", finalResult.Count);

        return Task.FromResult<IEnumerable<NodeInfo>>(finalResult);
    }

    /// <inheritdoc />
    public Task<IEnumerable<NodeInfo>> GetAllNodesAsync(CancellationToken cancellationToken = default)
    {
        var nodes = _nodes.Values.ToList();
        _loggingService.LogDebug("Retrieved {Count} nodes from cluster", nodes.Count);
        return Task.FromResult<IEnumerable<NodeInfo>>(nodes);
    }

    /// <inheritdoc />
    public Task<NodeInfo?> GetNodeAsync(string nodeId, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(nodeId);

        var node = _nodes.TryGetValue(nodeId, out var nodeInfo) ? nodeInfo : null;
        _loggingService.LogDebug("Retrieved node {NodeId}: {Found}", nodeId, node != null ? "Found" : "Not Found");

        return Task.FromResult(node);
    }

    /// <inheritdoc />
    public Task UpdateNodeAsync(NodeInfo nodeInfo, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(nodeInfo);

        _loggingService.LogDebug("Updating node {NodeId}", nodeInfo.NodeId);

        if (_nodes.TryGetValue(nodeInfo.NodeId, out var existingNode))
        {
            var oldStatus = existingNode.Status;

            // 更新节点信息
            nodeInfo.Timestamps.LastActiveAt = DateTime.UtcNow;
            _nodes.TryUpdate(nodeInfo.NodeId, nodeInfo, existingNode);
            _lastHeartbeats.AddOrUpdate(nodeInfo.NodeId, DateTime.UtcNow, (key, existing) => DateTime.UtcNow);

            // 如果状态发生变化，触发事件
            if (oldStatus != nodeInfo.Status)
            {
                NodeStatusChanged?.Invoke(this, new NodeStatusChangedEventArgs
                {
                    NodeId = nodeInfo.NodeId,
                    OldStatus = oldStatus,
                    NewStatus = nodeInfo.Status,
                    ChangedAt = DateTime.UtcNow
                });
            }

            _loggingService.LogDebug("Node {NodeId} updated successfully", nodeInfo.NodeId);
        }
        else
        {
            _loggingService.LogWarning("Node {NodeId} not found for update", nodeInfo.NodeId);
        }

        return Task.CompletedTask;
    }

    /// <inheritdoc />
    public Task<NodeInfo?> SelectBestNodeAsync(NodeExecutionRequirements? requirements = null, CancellationToken cancellationToken = default)
    {
        _loggingService.LogInformation("Selecting best node for execution with requirements: {Requirements}",
            requirements?.ToString() ?? "none");

        var availableNodes = _nodes.Values
            .Where(n => n.Status == NodeStatus.Healthy && n.Mode.CanExecuteTasks())
            .ToList();

        if (!availableNodes.Any())
        {
            _loggingService.LogWarning("No available nodes found for execution");
            return Task.FromResult<NodeInfo?>(null);
        }

        if (requirements == null)
        {
            // 如果没有特殊要求，选择负载最低的节点
            var bestNode = availableNodes
                .OrderBy(n => n.Load.LoadScore)
                .ThenByDescending(n => n.Capabilities.PerformanceLevel)
                .First();

            _loggingService.LogInformation("Selected node {NodeId} (load: {LoadScore})",
                bestNode.NodeId, bestNode.Load.LoadScore);
            return Task.FromResult<NodeInfo?>(bestNode);
        }

        // 根据要求过滤节点
        var suitableNodes = availableNodes.Where(node =>
        {
            // 检查执行器类型
            if (!string.IsNullOrWhiteSpace(requirements.RequiredExecutorType) &&
                !node.Capabilities.SupportedExecutorTypes.Contains(requirements.RequiredExecutorType))
            {
                return false;
            }

            // 检查CPU要求
            if (requirements.MinCpuCores.HasValue && node.Capabilities.CpuCores < requirements.MinCpuCores.Value)
            {
                return false;
            }

            // 检查内存要求
            if (requirements.MinMemoryMb.HasValue && node.Capabilities.MemoryMb < requirements.MinMemoryMb.Value)
            {
                return false;
            }

            // 检查磁盘空间要求
            if (requirements.MinDiskSpaceMb.HasValue && node.Capabilities.DiskSpaceMb < requirements.MinDiskSpaceMb.Value)
            {
                return false;
            }

            // 检查必需标签
            if (requirements.RequiredTags.Any() && !requirements.RequiredTags.All(tag => node.Capabilities.Tags.Contains(tag)))
            {
                return false;
            }

            // 检查节点是否可以接受新任务
            if (!node.Load.CanAcceptNewTask())
            {
                return false;
            }

            return true;
        }).ToList();

        if (!suitableNodes.Any())
        {
            _loggingService.LogWarning("No suitable nodes found matching requirements");
            return Task.FromResult<NodeInfo?>(null);
        }

        // 计算节点评分并选择最佳节点
        var scoredNodes = suitableNodes.Select(node =>
        {
            var score = CalculateNodeScore(node, requirements);
            return new { Node = node, Score = score };
        }).OrderByDescending(x => x.Score).ToList();

        var selectedNode = scoredNodes.First().Node;
        _loggingService.LogInformation("Selected node {NodeId} with score {Score}",
            selectedNode.NodeId, scoredNodes.First().Score);

        return Task.FromResult<NodeInfo?>(selectedNode);
    }

    /// <summary>
    /// 计算节点评分
    /// </summary>
    /// <param name="node">节点信息</param>
    /// <param name="requirements">执行要求</param>
    /// <returns>节点评分</returns>
    private double CalculateNodeScore(NodeInfo node, NodeExecutionRequirements requirements)
    {
        var score = 0.0;

        // 性能等级权重 (30%)
        score += node.Capabilities.PerformanceLevel * 10.0;

        // 负载评分权重 (40%) - 负载越低评分越高
        score += (100 - node.Load.LoadScore) * 0.4;

        // 可用资源权重 (20%)
        var cpuAvailability = (100 - node.Load.CpuUsagePercentage) * 0.1;
        var memoryAvailability = (100 - node.Load.MemoryUsagePercentage) * 0.1;
        score += cpuAvailability + memoryAvailability;

        // 任务容量权重 (10%)
        var taskCapacityRatio = node.Load.MaxTaskCapacity > 0 ?
            (double)(node.Load.MaxTaskCapacity - node.Load.ActiveTaskCount) / node.Load.MaxTaskCapacity : 0;
        score += taskCapacityRatio * 10.0;

        // 应用优先级权重
        foreach (var weight in requirements.PriorityWeights)
        {
            if (node.Capabilities.Tags.Contains(weight.Key))
            {
                score *= weight.Value;
            }
        }

        return score;
    }

    /// <inheritdoc />
    public async Task SendHeartbeatAsync(CancellationToken cancellationToken = default)
    {
        if (!_isStarted)
        {
            _loggingService.LogDebug("Cluster service not started, skipping heartbeat");
            return;
        }

        _loggingService.LogDebug("Sending heartbeat for node {NodeId}", CurrentNode.NodeId);

        // 更新当前节点的最后心跳时间
        CurrentNode.Timestamps.LastActiveAt = DateTime.UtcNow;
        CurrentNode.Timestamps.UpdateLastActive();

        // 创建心跳消息
        var heartbeatMessage = NodeHeartbeatMessage.Create(CurrentNode);
        heartbeatMessage.SequenceNumber = DateTime.UtcNow.Ticks;

        // 更新节点信息
        await UpdateNodeAsync(CurrentNode, cancellationToken);

        // 广播心跳消息
        await BroadcastMessageAsync(heartbeatMessage, cancellationToken);

        _loggingService.LogDebug("Heartbeat sent successfully");
    }

    /// <inheritdoc />
    public Task<ClusterHealthCheckResult> CheckNodeHealthAsync(string nodeId, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(nodeId);

        _loggingService.LogDebug("Performing health check for node {NodeId}", nodeId);

        var result = new ClusterHealthCheckResult
        {
            NodeId = nodeId,
            CheckTime = DateTime.UtcNow,
            IsHealthy = true,
            HealthScore = 100.0
        };

        if (!_nodes.TryGetValue(nodeId, out var nodeInfo))
        {
            result.IsHealthy = false;
            result.HealthScore = 0;
            result.Issues.Add("Node not found in cluster");
            return Task.FromResult(result);
        }

        // 检查节点状态
        if (nodeInfo.Status != NodeStatus.Healthy)
        {
            result.Issues.Add($"Node status is {nodeInfo.Status}");
            result.HealthScore -= 40;
        }

        // 检查最后心跳时间
        if (_lastHeartbeats.TryGetValue(nodeId, out var lastHeartbeat))
        {
            var timeSinceHeartbeat = DateTime.UtcNow - lastHeartbeat;
            if (timeSinceHeartbeat > TimeSpan.FromMinutes(2))
            {
                result.Issues.Add($"No heartbeat for {timeSinceHeartbeat.TotalMinutes:F1} minutes");
                result.HealthScore -= 30;
            }
        }
        else
        {
            result.Issues.Add("No heartbeat recorded");
            result.HealthScore -= 50;
        }

        // 检查负载状态
        if (nodeInfo.Load.IsOverloaded())
        {
            result.Issues.Add($"Node is overloaded (load score: {nodeInfo.Load.LoadScore:F1})");
            result.HealthScore -= 20;
        }

        // 检查健康状态
        if (!nodeInfo.Health.IsHealthy)
        {
            result.Issues.Add($"Node health check failed (score: {nodeInfo.Health.HealthScore:F1})");
            result.HealthScore -= 30;
        }

        result.HealthScore = Math.Max(result.HealthScore, 0);
        result.IsHealthy = result.HealthScore >= 70;

        _loggingService.LogDebug("Health check completed for node {NodeId}: {IsHealthy} (score: {HealthScore})",
            nodeId, result.IsHealthy, result.HealthScore);

        return Task.FromResult(result);
    }
    /// <inheritdoc />
    public Task<ClusterStats> GetClusterStatsAsync(CancellationToken cancellationToken = default)
    {
        _loggingService.LogDebug("Calculating cluster statistics");

        var allNodes = _nodes.Values.ToList();
        var onlineNodes = allNodes.Where(n => n.Status == NodeStatus.Healthy).ToList();

        var stats = new ClusterStats
        {
            ClusterName = ClusterName,
            TotalNodes = allNodes.Count,
            OnlineNodes = onlineNodes.Count,
            OfflineNodes = allNodes.Count - onlineNodes.Count,
            AverageLoadScore = onlineNodes.Any() ? onlineNodes.Average(n => n.Load.LoadScore) : 0,
            ClusterHealthScore = CalculateClusterHealthScore(allNodes),
            TotalCpuCores = allNodes.Sum(n => n.Capabilities.CpuCores),
            TotalMemoryMB = allNodes.Sum(n => n.Capabilities.MemoryMb),
            UsedCpuCores = allNodes.Sum(n => (int)(n.Capabilities.CpuCores * n.Load.CpuUsagePercentage / 100)),
            UsedMemoryMB = allNodes.Sum(n => (long)(n.Capabilities.MemoryMb * n.Load.MemoryUsagePercentage / 100)),
            ActiveTasks = allNodes.Sum(n => n.Load.ActiveTaskCount),
            QueuedTasks = allNodes.Sum(n => n.Load.QueuedTaskCount),
            StatisticsTime = DateTime.UtcNow,
            LastUpdatedAt = DateTime.UtcNow
        };

        // 按模式分组统计
        foreach (var group in allNodes.GroupBy(n => n.Mode))
        {
            stats.NodesByMode[group.Key] = group.Count();
        }

        // 按状态分组统计
        foreach (var group in allNodes.GroupBy(n => n.Status))
        {
            stats.NodesByStatus[group.Key] = group.Count();
        }

        _loggingService.LogDebug("Cluster statistics calculated: {TotalNodes} total, {OnlineNodes} online",
            stats.TotalNodes, stats.OnlineNodes);

        return Task.FromResult(stats);
    }

    /// <summary>
    /// 计算集群健康评分
    /// </summary>
    /// <param name="nodes">节点列表</param>
    /// <returns>集群健康评分</returns>
    private double CalculateClusterHealthScore(List<NodeInfo> nodes)
    {
        if (!nodes.Any()) return 0;

        var healthyNodes = nodes.Count(n => n.Status == NodeStatus.Healthy);
        var healthRatio = (double)healthyNodes / nodes.Count;

        var avgLoadScore = nodes.Where(n => n.Status == NodeStatus.Healthy)
                               .DefaultIfEmpty()
                               .Average(n => n?.Load.LoadScore ?? 100);

        var loadHealthScore = Math.Max(0, 100 - avgLoadScore);

        return (healthRatio * 70) + (loadHealthScore * 0.3);
    }

    /// <inheritdoc />
    public Task BroadcastMessageAsync(ClusterMessage message, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(message);

        _loggingService.LogDebug("Broadcasting message {MessageType} to all nodes", message.MessageType);

        // 设置发送者信息
        message.SenderId = CurrentNode.NodeId;
        message.CreatedAt = DateTime.UtcNow;

        // 在实际实现中，这里应该通过消息队列或网络发送消息
        // 目前只是记录日志
        _loggingService.LogInformation("Message {MessageId} of type {MessageType} broadcasted to {NodeCount} nodes",
            message.MessageId, message.MessageType, _nodes.Count);

        // 触发消息接收事件（模拟其他节点接收到消息）
        MessageReceived?.Invoke(this, new MessageReceivedEventArgs
        {
            Message = message,
            SenderId = message.SenderId,
            ReceivedAt = DateTime.UtcNow
        });

        return Task.CompletedTask;
    }

    /// <inheritdoc />
    public Task SendMessageToNodeAsync(string nodeId, ClusterMessage message, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(nodeId);
        ArgumentNullException.ThrowIfNull(message);

        _loggingService.LogDebug("Sending message {MessageType} to node {NodeId}", message.MessageType, nodeId);

        if (!_nodes.ContainsKey(nodeId))
        {
            _loggingService.LogWarning("Target node {NodeId} not found in cluster", nodeId);
            throw new InvalidOperationException($"Node {nodeId} not found in cluster");
        }

        // 设置发送者和目标信息
        message.SenderId = CurrentNode.NodeId;
        message.TargetId = nodeId;
        message.CreatedAt = DateTime.UtcNow;

        // 在实际实现中，这里应该通过消息队列或网络发送消息
        // 目前只是记录日志
        _loggingService.LogInformation("Message {MessageId} of type {MessageType} sent to node {NodeId}",
            message.MessageId, message.MessageType, nodeId);

        return Task.CompletedTask;
    }

    /// <summary>
    /// 定期发送心跳
    /// </summary>
    /// <param name="state">状态对象</param>
    private async void SendPeriodicHeartbeat(object? state)
    {
        try
        {
            await SendHeartbeatAsync();
        }
        catch (Exception ex)
        {
            _loggingService.LogError(ex, "Error sending periodic heartbeat");
        }
    }

    /// <summary>
    /// 执行健康检查
    /// </summary>
    /// <param name="state">状态对象</param>
    private async void PerformHealthChecks(object? state)
    {
        try
        {
            _loggingService.LogDebug("Performing periodic health checks");

            var nodesToCheck = _nodes.Keys.ToList();
            var unhealthyNodes = new List<string>();

            foreach (var nodeId in nodesToCheck)
            {
                var healthResult = await CheckNodeHealthAsync(nodeId);
                if (!healthResult.IsHealthy)
                {
                    unhealthyNodes.Add(nodeId);
                    _loggingService.LogWarning("Node {NodeId} failed health check: {Issues}",
                        nodeId, string.Join(", ", healthResult.Issues));
                }
            }

            // 清理长时间未响应的节点
            await CleanupStaleNodes();

            _loggingService.LogDebug("Health checks completed. {UnhealthyCount} unhealthy nodes found",
                unhealthyNodes.Count);
        }
        catch (Exception ex)
        {
            _loggingService.LogError(ex, "Error performing health checks");
        }
    }

    /// <summary>
    /// 清理过期节点
    /// </summary>
    private async Task CleanupStaleNodes()
    {
        var staleThreshold = DateTime.UtcNow.AddMinutes(-5); // 5分钟无心跳视为过期
        var staleNodes = new List<string>();

        foreach (var kvp in _lastHeartbeats)
        {
            if (kvp.Value < staleThreshold)
            {
                staleNodes.Add(kvp.Key);
            }
        }

        foreach (var nodeId in staleNodes)
        {
            _loggingService.LogInformation("Removing stale node {NodeId}", nodeId);
            await UnregisterNodeAsync(nodeId);
        }
    }

    /// <summary>
    /// 创建当前节点信息
    /// </summary>
    /// <returns>当前节点信息</returns>
    private static NodeInfo CreateCurrentNodeInfo()
    {
        var nodeId = Environment.MachineName + "-" + Guid.NewGuid().ToString("N")[..8];

        return new NodeInfo
        {
            NodeId = nodeId,
            NodeName = Environment.MachineName,
            Mode = NodeMode.Worker,
            Status = NodeStatus.Healthy,
            Network = new NetworkInfo
            {
                IpAddress = "127.0.0.1",
                HttpPort = 8080,
                NatsPort = 4222,
                ManagementPort = 8081,
                HostName = Environment.MachineName
            },
            Capabilities = new NodeCapabilities
            {
                CpuCores = Environment.ProcessorCount,
                MemoryMb = 4096, // 默认4GB
                DiskSpaceMb = 10240, // 默认10GB
                MaxConcurrentExecutions = Environment.ProcessorCount * 2,
                PerformanceLevel = 5
            },
            Load = new NodeLoad
            {
                MaxTaskCapacity = Environment.ProcessorCount * 2
            },
            Health = new HealthStatus(),
            Timestamps = new Timestamps
            {
                CreatedAt = DateTime.UtcNow,
                LastActiveAt = DateTime.UtcNow
            }
        };
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    /// <param name="disposing">是否正在释放</param>
    protected virtual void Dispose(bool disposing)
    {
        if (disposing)
        {
            _heartbeatTimer?.Dispose();
            _healthCheckTimer?.Dispose();
        }
    }
}