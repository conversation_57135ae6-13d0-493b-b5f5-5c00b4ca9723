# FlowCustomV1 v0.0.1.7 应用节点测试环境
# 包含实际的FlowCustomV1应用服务测试

services:
  # ===========================================
  # 基础设施层 (继承简化配置)
  # ===========================================
  nats:
    image: nats:2.11.8-alpine
    container_name: flowcustom-app-test-nats
    ports:
      - "24222:4222"
      - "28222:8222"
    command: ["-js", "-m", "8222"]
    networks:
      - flowcustom-app-test
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8222/healthz"]
      interval: 10s
      timeout: 5s
      retries: 3

  mysql:
    image: mysql:8.0
    container_name: flowcustom-app-test-mysql
    environment:
      MYSQL_ROOT_PASSWORD: AppTestPassword123!
      MYSQL_DATABASE: flowcustom_app_test
      MYSQL_USER: flowcustom
      MYSQL_PASSWORD: AppTestPassword123!
    ports:
      - "23308:3306"
    volumes:
      - mysql-app-test-data:/var/lib/mysql
      - ./mysql-config/init-full-test.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - flowcustom-app-test
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-pAppTestPassword123!"]
      interval: 10s
      timeout: 5s
      retries: 5

  # ===========================================
  # 应用层 - FlowCustomV1 API服务
  # ===========================================
  flowcustom-api:
    build:
      context: ../../
      dockerfile: src/FlowCustomV1.Api/Dockerfile
    container_name: flowcustom-app-test-api
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:5000
      - ConnectionStrings__DefaultConnection=Server=mysql;Database=flowcustom_app_test;Uid=flowcustom;Pwd=AppTestPassword123!;
      - NATS__Servers__0=nats://nats:4222
      - NodeConfiguration__NodeId=api-test-node
      - NodeConfiguration__NodeName=API Test Node
      - NodeConfiguration__NodeRole=Master
      - NodeConfiguration__Region=Test
      - NodeConfiguration__DataCenter=TestDC
      - Logging__LogLevel__Default=Information
      - Logging__LogLevel__Microsoft.AspNetCore=Warning
    ports:
      - "25000:5000"
    depends_on:
      nats:
        condition: service_healthy
      mysql:
        condition: service_healthy
    networks:
      - flowcustom-app-test
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 15s
      timeout: 10s
      retries: 3
      start_period: 30s

  # ===========================================
  # 应用测试协调器
  # ===========================================
  app-test-coordinator:
    image: python:3.11-slim
    container_name: flowcustom-app-test-coordinator
    working_dir: /app
    environment:
      - TEST_ENVIRONMENT=AppTest
      - NATS_SERVERS=nats://nats:4222
      - MYSQL_CONNECTION=Server=mysql;Database=flowcustom_app_test;Uid=flowcustom;Pwd=AppTestPassword123!;
      - API_BASE_URL=http://flowcustom-api:5000
      - TEST_TIMEOUT_MINUTES=30
    volumes:
      - ./test-scripts:/app/test-scripts
      - ./test-results:/app/test-results
    command: >
      bash -c "
        pip install requests aiohttp asyncio nats-py mysql-connector-python &&
        echo '🚀 开始FlowCustomV1应用节点测试...' &&
        python /app/test-scripts/test_application_nodes.py
      "
    depends_on:
      flowcustom-api:
        condition: service_healthy
    networks:
      - flowcustom-app-test
    profiles:
      - test

networks:
  flowcustom-app-test:
    driver: bridge

volumes:
  mysql-app-test-data:
    driver: local
