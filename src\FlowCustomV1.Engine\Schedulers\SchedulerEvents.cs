using FlowCustomV1.Core.Models.Workflow;

namespace FlowCustomV1.Engine.Schedulers;

/// <summary>
/// 节点调度请求
/// </summary>
public class NodeScheduleRequest
{
    /// <summary>
    /// 请求唯一标识符
    /// </summary>
    public string RequestId { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 节点执行上下文
    /// </summary>
    public NodeExecutionContext NodeContext { get; set; } = default!;

    /// <summary>
    /// 调度优先级
    /// </summary>
    public int Priority { get; set; } = 0;

    /// <summary>
    /// 请求创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 调度延迟时间
    /// </summary>
    public TimeSpan Delay { get; set; } = TimeSpan.Zero;

    /// <summary>
    /// 是否为重试请求
    /// </summary>
    public bool IsRetry { get; set; } = false;

    /// <summary>
    /// 重试次数
    /// </summary>
    public int RetryCount { get; set; } = 0;

    /// <summary>
    /// 请求元数据
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();

    /// <summary>
    /// 获取调度请求的字符串表示
    /// </summary>
    /// <returns>调度请求字符串</returns>
    public override string ToString()
    {
        return $"ScheduleRequest[{RequestId}] Node: {NodeContext.NodeId}, Priority: {Priority}, Delay: {Delay.TotalMilliseconds}ms";
    }
}

/// <summary>
/// 节点调度完成事件参数
/// </summary>
public class NodeScheduledEventArgs : EventArgs
{
    /// <summary>
    /// 调度请求
    /// </summary>
    public NodeScheduleRequest Request { get; set; } = default!;

    /// <summary>
    /// 调度时间
    /// </summary>
    public DateTime ScheduledAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 调度器ID
    /// </summary>
    public string SchedulerId { get; set; } = string.Empty;

    /// <summary>
    /// 调度延迟（从请求创建到实际调度的时间）
    /// </summary>
    public TimeSpan SchedulingDelay => ScheduledAt - Request.CreatedAt;
}

/// <summary>
/// 节点执行完成事件参数
/// </summary>
public class NodeCompletedEventArgs : EventArgs
{
    /// <summary>
    /// 节点执行上下文
    /// </summary>
    public NodeExecutionContext NodeContext { get; set; } = default!;

    /// <summary>
    /// 节点执行结果
    /// </summary>
    public NodeExecutionResult Result { get; set; } = default!;

    /// <summary>
    /// 完成时间
    /// </summary>
    public DateTime CompletedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 执行时长
    /// </summary>
    public TimeSpan ExecutionDuration { get; set; }

    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess => Result.IsSuccess;

    /// <summary>
    /// 错误信息（如果有）
    /// </summary>
    public Exception? Error => Result.Exception;
}

/// <summary>
/// 节点执行错误事件参数
/// </summary>
public class NodeExecutionErrorEventArgs : EventArgs
{
    /// <summary>
    /// 节点执行上下文
    /// </summary>
    public NodeExecutionContext NodeContext { get; set; } = default!;

    /// <summary>
    /// 错误信息
    /// </summary>
    public Exception Error { get; set; } = default!;

    /// <summary>
    /// 错误发生时间
    /// </summary>
    public DateTime ErrorAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 是否可以重试
    /// </summary>
    public bool CanRetry { get; set; } = false;

    /// <summary>
    /// 重试次数
    /// </summary>
    public int RetryCount { get; set; } = 0;

    /// <summary>
    /// 错误上下文
    /// </summary>
    public Dictionary<string, object> ErrorContext { get; set; } = new();
}

/// <summary>
/// 调度器状态变更事件参数
/// </summary>
public class SchedulerStateChangedEventArgs : EventArgs
{
    /// <summary>
    /// 调度器ID
    /// </summary>
    public string SchedulerId { get; set; } = string.Empty;

    /// <summary>
    /// 旧状态
    /// </summary>
    public SchedulerState OldState { get; set; }

    /// <summary>
    /// 新状态
    /// </summary>
    public SchedulerState NewState { get; set; }

    /// <summary>
    /// 状态变更时间
    /// </summary>
    public DateTime ChangedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 变更原因
    /// </summary>
    public string? Reason { get; set; }
}

/// <summary>
/// 调度器状态枚举
/// </summary>
public enum SchedulerState
{
    /// <summary>
    /// 未启动
    /// </summary>
    NotStarted = 0,

    /// <summary>
    /// 启动中
    /// </summary>
    Starting = 1,

    /// <summary>
    /// 运行中
    /// </summary>
    Running = 2,

    /// <summary>
    /// 暂停中
    /// </summary>
    Pausing = 3,

    /// <summary>
    /// 已暂停
    /// </summary>
    Paused = 4,

    /// <summary>
    /// 停止中
    /// </summary>
    Stopping = 5,

    /// <summary>
    /// 已停止
    /// </summary>
    Stopped = 6,

    /// <summary>
    /// 错误状态
    /// </summary>
    Error = 7
}

/// <summary>
/// 调度器统计信息
/// </summary>
public class SchedulerStatistics
{
    /// <summary>
    /// 总调度请求数
    /// </summary>
    public long TotalRequests { get; set; } = 0;

    /// <summary>
    /// 成功执行的节点数
    /// </summary>
    public long SuccessfulExecutions { get; set; } = 0;

    /// <summary>
    /// 失败执行的节点数
    /// </summary>
    public long FailedExecutions { get; set; } = 0;

    /// <summary>
    /// 当前队列中的请求数
    /// </summary>
    public int QueuedRequests { get; set; } = 0;

    /// <summary>
    /// 当前正在执行的节点数
    /// </summary>
    public int ActiveExecutions { get; set; } = 0;

    /// <summary>
    /// 平均执行时间（毫秒）
    /// </summary>
    public double AverageExecutionTimeMs { get; set; } = 0;

    /// <summary>
    /// 最大执行时间（毫秒）
    /// </summary>
    public double MaxExecutionTimeMs { get; set; } = 0;

    /// <summary>
    /// 最小执行时间（毫秒）
    /// </summary>
    public double MinExecutionTimeMs { get; set; } = 0;

    /// <summary>
    /// 调度器启动时间
    /// </summary>
    public DateTime StartedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 调度器运行时间
    /// </summary>
    public TimeSpan Uptime => DateTime.UtcNow - StartedAt;

    /// <summary>
    /// 成功率
    /// </summary>
    public double SuccessRate => TotalRequests > 0 ? (double)SuccessfulExecutions / TotalRequests * 100 : 0;

    /// <summary>
    /// 吞吐量（每秒处理的请求数）
    /// </summary>
    public double Throughput => Uptime.TotalSeconds > 0 ? TotalRequests / Uptime.TotalSeconds : 0;
}
