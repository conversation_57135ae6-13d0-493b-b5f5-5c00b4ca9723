using System.Text.Json.Serialization;
using FlowCustomV1.Core.Interfaces.Messaging;
using FlowCustomV1.Core.Models.Designer;

namespace FlowCustomV1.Core.Models.Messages;

/// <summary>
/// 协作消息基类
/// </summary>
public abstract class CollaborationMessageBase : IMessage
{
    /// <inheritdoc />
    [JsonPropertyName("messageId")]
    public string MessageId { get; set; } = Guid.NewGuid().ToString();

    /// <inheritdoc />
    [JsonPropertyName("messageType")]
    public abstract string MessageType { get; }

    /// <inheritdoc />
    [JsonPropertyName("senderId")]
    public string SenderId { get; set; } = string.Empty;

    /// <inheritdoc />
    [JsonPropertyName("targetId")]
    public string? TargetId { get; set; }

    /// <inheritdoc />
    [JsonPropertyName("createdAt")]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <inheritdoc />
    [JsonPropertyName("expiresAt")]
    public DateTime? ExpiresAt { get; set; }

    /// <inheritdoc />
    [JsonPropertyName("priority")]
    public MessagePriority Priority { get; set; } = MessagePriority.Normal;

    /// <inheritdoc />
    [JsonPropertyName("payload")]
    public object? Payload { get; set; }

    /// <inheritdoc />
    [JsonPropertyName("metadata")]
    public IReadOnlyDictionary<string, object> Metadata { get; set; } = new Dictionary<string, object>();

    /// <summary>
    /// 会话ID
    /// </summary>
    [JsonPropertyName("sessionId")]
    public string SessionId { get; set; } = string.Empty;

    /// <summary>
    /// 时间戳
    /// </summary>
    [JsonPropertyName("timestamp")]
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 工作流ID
    /// </summary>
    [JsonPropertyName("workflowId")]
    public string WorkflowId { get; set; } = string.Empty;
}

/// <summary>
/// 协作会话消息
/// </summary>
public class CollaborationSessionMessage : CollaborationMessageBase
{
    /// <inheritdoc />
    public override string MessageType => "collaboration.session";

    /// <summary>
    /// 会话动作
    /// </summary>
    [JsonPropertyName("action")]
    public SessionAction Action { get; set; }

    /// <summary>
    /// 会话信息
    /// </summary>
    [JsonPropertyName("session")]
    public CollaborationSession? Session { get; set; }

    /// <summary>
    /// 原因（用于结束会话等）
    /// </summary>
    [JsonPropertyName("reason")]
    public string Reason { get; set; } = string.Empty;
}

/// <summary>
/// 协作事件消息
/// </summary>
public class CollaborationMessage : CollaborationMessageBase
{
    /// <inheritdoc />
    public override string MessageType => "collaboration.event";

    /// <summary>
    /// 协作动作
    /// </summary>
    [JsonPropertyName("action")]
    public CollaborationAction Action { get; set; }

    /// <summary>
    /// 协作者信息
    /// </summary>
    [JsonPropertyName("collaborator")]
    public CollaboratorInfo? Collaborator { get; set; }

    /// <summary>
    /// 设计操作（设计变更时）
    /// </summary>
    [JsonPropertyName("operation")]
    public DesignOperation? Operation { get; set; }
}

/// <summary>
/// 协作操作消息
/// </summary>
public class CollaborationOperationMessage : CollaborationMessageBase
{
    /// <inheritdoc />
    public override string MessageType => "collaboration.operation";

    /// <summary>
    /// 设计操作
    /// </summary>
    [JsonPropertyName("operation")]
    public DesignOperation Operation { get; set; } = new();

    /// <summary>
    /// 操作序列号
    /// </summary>
    [JsonPropertyName("sequenceNumber")]
    public long SequenceNumber { get; set; }

    /// <summary>
    /// 是否需要确认
    /// </summary>
    [JsonPropertyName("requiresAck")]
    public bool RequiresAck { get; set; } = false;
}

/// <summary>
/// 协作状态消息
/// </summary>
public class CollaborationStatusMessage : CollaborationMessageBase
{
    /// <inheritdoc />
    public override string MessageType => "collaboration.status";

    /// <summary>
    /// 协作者ID
    /// </summary>
    [JsonPropertyName("collaboratorId")]
    public string CollaboratorId { get; set; } = string.Empty;

    /// <summary>
    /// 新状态
    /// </summary>
    [JsonPropertyName("status")]
    public CollaboratorStatus Status { get; set; }

    /// <summary>
    /// 之前的状态
    /// </summary>
    [JsonPropertyName("previousStatus")]
    public CollaboratorStatus PreviousStatus { get; set; }
}

/// <summary>
/// 协作光标消息
/// </summary>
public class CollaborationCursorMessage : CollaborationMessageBase
{
    /// <inheritdoc />
    public override string MessageType => "collaboration.cursor";

    /// <summary>
    /// 协作者ID
    /// </summary>
    [JsonPropertyName("collaboratorId")]
    public string CollaboratorId { get; set; } = string.Empty;

    /// <summary>
    /// 光标位置
    /// </summary>
    [JsonPropertyName("position")]
    public CursorPosition Position { get; set; } = new();
}

/// <summary>
/// 协作选择消息
/// </summary>
public class CollaborationSelectionMessage : CollaborationMessageBase
{
    /// <inheritdoc />
    public override string MessageType => "collaboration.selection";

    /// <summary>
    /// 协作者ID
    /// </summary>
    [JsonPropertyName("collaboratorId")]
    public string CollaboratorId { get; set; } = string.Empty;

    /// <summary>
    /// 选择的对象ID
    /// </summary>
    [JsonPropertyName("selection")]
    public string Selection { get; set; } = string.Empty;

    /// <summary>
    /// 之前的选择
    /// </summary>
    [JsonPropertyName("previousSelection")]
    public string PreviousSelection { get; set; } = string.Empty;
}

/// <summary>
/// 协作冲突消息
/// </summary>
public class CollaborationConflictMessage : CollaborationMessageBase
{
    /// <inheritdoc />
    public override string MessageType => "collaboration.conflict";

    /// <summary>
    /// 冲突检测结果
    /// </summary>
    [JsonPropertyName("conflictResult")]
    public ConflictDetectionResult ConflictResult { get; set; } = new();
}

/// <summary>
/// 协作聊天消息
/// </summary>
public class CollaborationChatMessage : CollaborationMessageBase
{
    /// <inheritdoc />
    public override string MessageType => "collaboration.chat";

    /// <summary>
    /// 发送者ID
    /// </summary>
    [JsonPropertyName("senderId")]
    public new string SenderId { get; set; } = string.Empty;

    /// <summary>
    /// 发送者名称
    /// </summary>
    [JsonPropertyName("senderName")]
    public string SenderName { get; set; } = string.Empty;

    /// <summary>
    /// 消息内容
    /// </summary>
    [JsonPropertyName("message")]
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 消息类型
    /// </summary>
    [JsonPropertyName("chatMessageType")]
    public ChatMessageType ChatMessageType { get; set; } = ChatMessageType.Text;

    /// <summary>
    /// 是否为私聊消息
    /// </summary>
    [JsonPropertyName("isPrivate")]
    public bool IsPrivate { get; set; } = false;

    /// <summary>
    /// 私聊目标协作者ID
    /// </summary>
    [JsonPropertyName("targetCollaboratorId")]
    public string? TargetCollaboratorId { get; set; }
}

/// <summary>
/// 协作心跳消息
/// </summary>
public class CollaborationHeartbeatMessage : CollaborationMessageBase
{
    /// <inheritdoc />
    public override string MessageType => "collaboration.heartbeat";

    /// <summary>
    /// 节点ID
    /// </summary>
    [JsonPropertyName("nodeId")]
    public string NodeId { get; set; } = string.Empty;

    /// <summary>
    /// 节点状态
    /// </summary>
    [JsonPropertyName("nodeStatus")]
    public string NodeStatus { get; set; } = "healthy";

    /// <summary>
    /// 活跃会话数量
    /// </summary>
    [JsonPropertyName("activeSessionCount")]
    public int ActiveSessionCount { get; set; } = 0;

    /// <summary>
    /// 总协作者数量
    /// </summary>
    [JsonPropertyName("totalCollaborators")]
    public int TotalCollaborators { get; set; } = 0;
}

/// <summary>
/// 会话动作枚举
/// </summary>
public enum SessionAction
{
    /// <summary>
    /// 创建
    /// </summary>
    Created,

    /// <summary>
    /// 更新
    /// </summary>
    Updated,

    /// <summary>
    /// 暂停
    /// </summary>
    Paused,

    /// <summary>
    /// 恢复
    /// </summary>
    Resumed,

    /// <summary>
    /// 结束
    /// </summary>
    Ended,

    /// <summary>
    /// 过期
    /// </summary>
    Expired
}
