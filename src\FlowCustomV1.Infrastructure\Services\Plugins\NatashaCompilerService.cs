using FlowCustomV1.Core.Interfaces;
using FlowCustomV1.Core.Interfaces.Plugins;
using FlowCustomV1.Core.Models.Plugins;
using FlowCustomV1.Core.Models.Workflow;
using Microsoft.Extensions.Logging;
using Natasha.CSharp;
using System.Collections.Concurrent;
using System.Diagnostics;
using System.Reflection;
using System.Security.Cryptography;
using System.Text;

namespace FlowCustomV1.Infrastructure.Services.Plugins;

/// <summary>
/// Natasha动态编译服务
/// 基于Natasha实现运行时C#代码编译
/// </summary>
public class NatashaCompilerService : IDynamicNodeCompiler
{
    private readonly ILogger<NatashaCompilerService> _logger;
    private readonly ConcurrentDictionary<string, INodeExecutor> _compilationCache;
    private readonly ConcurrentDictionary<string, CompilationStatistics> _nodeTypeStatistics;
    private readonly CompilationStatistics _globalStatistics;
    private bool _isInitialized;

    public NatashaCompilerService(ILogger<NatashaCompilerService> logger)
    {
        _logger = logger;
        _compilationCache = new ConcurrentDictionary<string, INodeExecutor>();
        _nodeTypeStatistics = new ConcurrentDictionary<string, CompilationStatistics>();
        _globalStatistics = new CompilationStatistics();
    }

    /// <summary>
    /// 编译完成事件
    /// </summary>
    public event EventHandler<CompilationCompletedEventArgs>? CompilationCompleted;

    /// <summary>
    /// 编译错误事件
    /// </summary>
    public event EventHandler<CompilationErrorEventArgs>? CompilationError;

    /// <summary>
    /// 初始化编译器
    /// </summary>
    public async Task InitializeAsync(CancellationToken cancellationToken = default)
    {
        if (_isInitialized)
            return;

        try
        {
            _logger.LogInformation("正在初始化Natasha编译器...");

            // 初始化Natasha编译环境
            await Task.Run(() =>
            {
                // 预热Natasha编译器 - 这是必需的
                NatashaManagement.Preheating();

                _logger.LogInformation("Natasha编译器初始化完成");
            }, cancellationToken);

            _isInitialized = true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "初始化Natasha编译器失败");
            throw;
        }
    }

    /// <summary>
    /// 编译节点执行器代码
    /// </summary>
    public async Task<CompilationResult> CompileNodeExecutorAsync(
        string sourceCode, 
        string nodeType, 
        CancellationToken cancellationToken = default)
    {
        if (!_isInitialized)
            await InitializeAsync(cancellationToken);

        var stopwatch = Stopwatch.StartNew();
        var result = new CompilationResult
        {
            NodeType = nodeType,
            StartedAt = DateTime.UtcNow,
            SourceCodeHash = ComputeHash(sourceCode)
        };

        try
        {
            _logger.LogDebug("开始编译节点执行器: {NodeType}", nodeType);

            // 检查缓存
            var cacheKey = $"{nodeType}_{result.SourceCodeHash}";
            if (_compilationCache.TryGetValue(cacheKey, out var cachedExecutor))
            {
                _logger.LogDebug("使用缓存的执行器: {NodeType}", nodeType);
                result.IsSuccess = true;
                result.Executor = cachedExecutor;
                result.CompilationTimeMs = 0; // 缓存命中，编译时间为0
                
                UpdateStatistics(nodeType, true, 0, true);
                return result;
            }

            // 动态编译
            var executor = await CompileExecutorFromSourceAsync(sourceCode, nodeType, cancellationToken);
            
            if (executor != null)
            {
                result.IsSuccess = true;
                result.Executor = executor;
                
                // 缓存编译结果
                _compilationCache.TryAdd(cacheKey, executor);
                
                _logger.LogInformation("节点执行器编译成功: {NodeType}, 耗时: {ElapsedMs}ms", 
                    nodeType, stopwatch.ElapsedMilliseconds);
            }
            else
            {
                result.IsSuccess = false;
                result.Errors.Add(new CompilationError
                {
                    Code = "COMPILE_FAILED",
                    Message = "编译失败，未能生成执行器",
                    Severity = CompilationSeverity.Error
                });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "编译节点执行器时发生异常: {NodeType}", nodeType);
            
            result.IsSuccess = false;
            result.Errors.Add(new CompilationError
            {
                Code = "COMPILE_EXCEPTION",
                Message = ex.Message,
                Severity = CompilationSeverity.Fatal
            });

            // 触发编译错误事件
            CompilationError?.Invoke(this, new CompilationErrorEventArgs
            {
                NodeType = nodeType,
                Errors = result.Errors,
                SourceCode = sourceCode
            });
        }
        finally
        {
            stopwatch.Stop();
            result.CompilationTimeMs = stopwatch.ElapsedMilliseconds;
            result.CompletedAt = DateTime.UtcNow;
            result.CompilerVersion = GetCompilerVersion();

            // 更新统计信息
            UpdateStatistics(nodeType, result.IsSuccess, result.CompilationTimeMs, false);

            // 触发编译完成事件
            CompilationCompleted?.Invoke(this, new CompilationCompletedEventArgs
            {
                Result = result
            });
        }

        return result;
    }

    /// <summary>
    /// 从JSON配置编译节点执行器
    /// </summary>
    public async Task<CompilationResult> CompileFromJsonConfigAsync(
        string jsonConfig, 
        string nodeType, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("从JSON配置编译节点执行器: {NodeType}", nodeType);

            // 解析JSON配置并生成C#代码
            var sourceCode = GenerateSourceCodeFromJsonConfig(jsonConfig, nodeType);
            
            return await CompileNodeExecutorAsync(sourceCode, nodeType, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "从JSON配置编译节点执行器失败: {NodeType}", nodeType);
            
            return new CompilationResult
            {
                IsSuccess = false,
                NodeType = nodeType,
                StartedAt = DateTime.UtcNow,
                CompletedAt = DateTime.UtcNow,
                Errors = new List<CompilationError>
                {
                    new CompilationError
                    {
                        Code = "JSON_COMPILE_FAILED",
                        Message = ex.Message,
                        Severity = CompilationSeverity.Error
                    }
                }
            };
        }
    }

    /// <summary>
    /// 从内置插件定义编译节点执行器
    /// </summary>
    public async Task<CompilationResult> CompileFromBuiltinDefinitionAsync(
        BuiltinPluginDefinition pluginDefinition, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("从内置插件定义编译节点执行器: {NodeType}", pluginDefinition.NodeType);

            // 根据插件定义生成C#代码
            var sourceCode = GenerateSourceCodeFromBuiltinDefinition(pluginDefinition);
            
            return await CompileNodeExecutorAsync(sourceCode, pluginDefinition.NodeType, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "从内置插件定义编译节点执行器失败: {NodeType}", pluginDefinition.NodeType);
            
            return new CompilationResult
            {
                IsSuccess = false,
                NodeType = pluginDefinition.NodeType,
                StartedAt = DateTime.UtcNow,
                CompletedAt = DateTime.UtcNow,
                Errors = new List<CompilationError>
                {
                    new CompilationError
                    {
                        Code = "BUILTIN_COMPILE_FAILED",
                        Message = ex.Message,
                        Severity = CompilationSeverity.Error
                    }
                }
            };
        }
    }

    /// <summary>
    /// 获取编译缓存中的执行器
    /// </summary>
    public INodeExecutor? GetCachedExecutor(string nodeType)
    {
        var cacheKeys = _compilationCache.Keys.Where(k => k.StartsWith($"{nodeType}_")).ToList();
        if (cacheKeys.Any())
        {
            return _compilationCache[cacheKeys.First()];
        }
        return null;
    }

    /// <summary>
    /// 清除编译缓存
    /// </summary>
    public async Task ClearCacheAsync(string? nodeType = null)
    {
        await Task.Run(() =>
        {
            if (string.IsNullOrEmpty(nodeType))
            {
                _compilationCache.Clear();
                _logger.LogInformation("已清除所有编译缓存");
            }
            else
            {
                var keysToRemove = _compilationCache.Keys.Where(k => k.StartsWith($"{nodeType}_")).ToList();
                foreach (var key in keysToRemove)
                {
                    _compilationCache.TryRemove(key, out _);
                }
                _logger.LogInformation("已清除节点类型 {NodeType} 的编译缓存", nodeType);
            }
        });
    }

    /// <summary>
    /// 预编译常用节点
    /// </summary>
    public async Task PrecompileNodesAsync(IEnumerable<string> nodeTypes, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("开始预编译常用节点...");

        var tasks = nodeTypes.Select(async nodeType =>
        {
            try
            {
                // 获取内置节点定义并预编译
                var builtinDefinition = GetBuiltinNodeDefinition(nodeType);
                if (builtinDefinition != null)
                {
                    await CompileFromBuiltinDefinitionAsync(builtinDefinition, cancellationToken);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "预编译节点失败: {NodeType}", nodeType);
            }
        });

        await Task.WhenAll(tasks);
        _logger.LogInformation("预编译完成");
    }

    /// <summary>
    /// 获取编译统计信息
    /// </summary>
    public CompilationStatistics GetCompilationStatistics()
    {
        _globalStatistics.LastUpdated = DateTime.UtcNow;
        _globalStatistics.ByNodeType = new Dictionary<string, NodeTypeCompilationStatistics>(
            _nodeTypeStatistics.ToDictionary(
                kvp => kvp.Key,
                kvp => new NodeTypeCompilationStatistics
                {
                    NodeType = kvp.Key,
                    CompilationCount = kvp.Value.TotalCompilations,
                    SuccessCount = kvp.Value.SuccessfulCompilations,
                    FailureCount = kvp.Value.FailedCompilations,
                    AverageCompilationTimeMs = kvp.Value.AverageCompilationTimeMs,
                    SuccessRate = kvp.Value.SuccessRate
                }));

        return _globalStatistics;
    }

    /// <summary>
    /// 验证源代码语法
    /// </summary>
    public async Task<ValidationResult> ValidateSourceCodeAsync(string sourceCode, CancellationToken cancellationToken = default)
    {
        var result = new ValidationResult();

        try
        {
            await Task.Run(() =>
            {
                // 使用Natasha进行语法验证
                // 这里可以实现更详细的语法检查逻辑
                if (string.IsNullOrWhiteSpace(sourceCode))
                {
                    result.IsValid = false;
                    result.Errors.Add("源代码不能为空");
                }
                else
                {
                    result.IsValid = true;
                }
            }, cancellationToken);
        }
        catch (Exception ex)
        {
            result.IsValid = false;
            result.Errors.Add($"验证源代码时发生异常: {ex.Message}");
        }

        return result;
    }

    #region 私有方法

    /// <summary>
    /// 从源代码编译执行器
    /// </summary>
    private async Task<INodeExecutor?> CompileExecutorFromSourceAsync(string sourceCode, string nodeType, CancellationToken cancellationToken)
    {
        return await Task.Run(() =>
        {
            try
            {
                // 使用Natasha动态编译
                var assemblyBuilder = new AssemblyCSharpBuilder();
                assemblyBuilder.Add(sourceCode);

                // 添加必要的引用
                assemblyBuilder.Domain.LoadPlugin(typeof(INodeExecutor).Assembly.Location);
                assemblyBuilder.Domain.LoadPlugin(typeof(Task).Assembly.Location);
                assemblyBuilder.Domain.LoadPlugin(typeof(CancellationToken).Assembly.Location);

                var assembly = assemblyBuilder.GetAssembly();

                // 查找实现了INodeExecutor接口的类型
                var executorType = assembly.GetTypes()
                    .FirstOrDefault(t => typeof(INodeExecutor).IsAssignableFrom(t) && !t.IsInterface && !t.IsAbstract);

                if (executorType != null)
                {
                    return Activator.CreateInstance(executorType) as INodeExecutor;
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "使用Natasha编译源代码失败: {NodeType}", nodeType);
                return null;
            }
        }, cancellationToken);
    }

    /// <summary>
    /// 从JSON配置生成源代码
    /// </summary>
    private string GenerateSourceCodeFromJsonConfig(string jsonConfig, string nodeType)
    {
        // 这里实现从JSON配置生成C#代码的逻辑
        // 暂时返回一个简单的模板
        return $@"
using FlowCustomV1.Core.Interfaces;
using FlowCustomV1.Core.Models.Workflow;
using System.Threading;
using System.Threading.Tasks;

public class {nodeType}Executor : INodeExecutor
{{
    public async Task<NodeExecutionResult> ExecuteAsync(NodeExecutionContext context, CancellationToken cancellationToken = default)
    {{
        // JSON配置驱动的执行逻辑
        // 配置: {jsonConfig}

        return new NodeExecutionResult
        {{
            IsSuccess = true,
            NodeId = context.Node.Id,
            ExecutionTime = TimeSpan.FromMilliseconds(100),
            OutputData = new Dictionary<string, object>()
        }};
    }}
}}";
    }

    /// <summary>
    /// 从内置插件定义生成源代码
    /// </summary>
    private string GenerateSourceCodeFromBuiltinDefinition(BuiltinPluginDefinition pluginDefinition)
    {
        var className = $"{pluginDefinition.NodeType}Executor";
        var executionLogic = GenerateExecutionLogic(pluginDefinition.ExecutionLogic);

        return $@"
using FlowCustomV1.Core.Interfaces;
using FlowCustomV1.Core.Models.Workflow;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

public class {className} : INodeExecutor
{{
    public async Task<NodeExecutionResult> ExecuteAsync(NodeExecutionContext context, CancellationToken cancellationToken = default)
    {{
        var startTime = DateTime.UtcNow;
        var result = new NodeExecutionResult
        {{
            NodeId = context.Node.Id,
            StartedAt = startTime
        }};

        try
        {{
            {executionLogic}

            result.IsSuccess = true;
            result.CompletedAt = DateTime.UtcNow;
            result.ExecutionTime = result.CompletedAt - result.StartedAt;
        }}
        catch (Exception ex)
        {{
            result.IsSuccess = false;
            result.ErrorMessage = ex.Message;
            result.Exception = ex;
            result.CompletedAt = DateTime.UtcNow;
            result.ExecutionTime = result.CompletedAt - result.StartedAt;
        }}

        return result;
    }}
}}";
    }

    /// <summary>
    /// 生成执行逻辑代码
    /// </summary>
    private string GenerateExecutionLogic(ExecutionLogicTemplate template)
    {
        return template.Type switch
        {
            ExecutionLogicType.Code => template.CodeTemplate ?? "// 无执行逻辑",
            ExecutionLogicType.Expression => $"result.OutputData[\"result\"] = {template.ExpressionTemplate ?? "true"};",
            ExecutionLogicType.Configuration => "// 配置驱动的执行逻辑",
            ExecutionLogicType.Script => "// 脚本执行逻辑",
            _ => "// 默认执行逻辑"
        };
    }

    /// <summary>
    /// 获取内置节点定义
    /// </summary>
    private BuiltinPluginDefinition? GetBuiltinNodeDefinition(string nodeType)
    {
        // 这里返回内置节点的定义
        // 暂时返回一个简单的定义
        return nodeType switch
        {
            "Start" => new BuiltinPluginDefinition
            {
                NodeType = "Start",
                Name = "开始节点",
                DisplayName = "开始",
                Description = "工作流开始节点",
                ExecutionLogic = new ExecutionLogicTemplate
                {
                    Type = ExecutionLogicType.Code,
                    CodeTemplate = @"
                        result.OutputData[""started""] = true;
                        result.OutputData[""startTime""] = DateTime.UtcNow;
                    "
                }
            },
            "End" => new BuiltinPluginDefinition
            {
                NodeType = "End",
                Name = "结束节点",
                DisplayName = "结束",
                Description = "工作流结束节点",
                ExecutionLogic = new ExecutionLogicTemplate
                {
                    Type = ExecutionLogicType.Code,
                    CodeTemplate = @"
                        result.OutputData[""completed""] = true;
                        result.OutputData[""endTime""] = DateTime.UtcNow;
                    "
                }
            },
            "Task" => new BuiltinPluginDefinition
            {
                NodeType = "Task",
                Name = "任务节点",
                DisplayName = "任务",
                Description = "通用任务节点",
                ExecutionLogic = new ExecutionLogicTemplate
                {
                    Type = ExecutionLogicType.Code,
                    CodeTemplate = @"
                        // 执行任务逻辑
                        await Task.Delay(100, cancellationToken);
                        result.OutputData[""taskCompleted""] = true;
                    "
                }
            },
            _ => null
        };
    }

    /// <summary>
    /// 计算字符串哈希值
    /// </summary>
    private string ComputeHash(string input)
    {
        using var sha256 = SHA256.Create();
        var hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(input));
        return Convert.ToHexString(hashBytes);
    }

    /// <summary>
    /// 获取编译器版本
    /// </summary>
    private string GetCompilerVersion()
    {
        return Assembly.GetAssembly(typeof(AssemblyCSharpBuilder))?.GetName().Version?.ToString() ?? "Unknown";
    }

    /// <summary>
    /// 更新统计信息
    /// </summary>
    private void UpdateStatistics(string nodeType, bool isSuccess, long compilationTimeMs, bool isCacheHit)
    {
        // 更新全局统计
        _globalStatistics.TotalCompilations++;
        if (isSuccess)
            _globalStatistics.SuccessfulCompilations++;
        else
            _globalStatistics.FailedCompilations++;

        if (isCacheHit)
            _globalStatistics.CacheHits++;
        else
            _globalStatistics.CacheMisses++;

        // 更新平均编译时间
        var totalTime = _globalStatistics.AverageCompilationTimeMs * (_globalStatistics.TotalCompilations - 1) + compilationTimeMs;
        _globalStatistics.AverageCompilationTimeMs = totalTime / _globalStatistics.TotalCompilations;

        // 更新成功率和缓存命中率
        _globalStatistics.SuccessRate = (double)_globalStatistics.SuccessfulCompilations / _globalStatistics.TotalCompilations * 100;
        _globalStatistics.CacheHitRate = (double)_globalStatistics.CacheHits / (_globalStatistics.CacheHits + _globalStatistics.CacheMisses) * 100;

        // 更新节点类型统计
        var nodeStats = _nodeTypeStatistics.GetOrAdd(nodeType, _ => new CompilationStatistics());
        nodeStats.TotalCompilations++;
        if (isSuccess)
            nodeStats.SuccessfulCompilations++;
        else
            nodeStats.FailedCompilations++;

        nodeStats.SuccessRate = (double)nodeStats.SuccessfulCompilations / nodeStats.TotalCompilations * 100;

        var nodeTypeTotalTime = nodeStats.AverageCompilationTimeMs * (nodeStats.TotalCompilations - 1) + compilationTimeMs;
        nodeStats.AverageCompilationTimeMs = nodeTypeTotalTime / nodeStats.TotalCompilations;
    }

    #endregion
}
