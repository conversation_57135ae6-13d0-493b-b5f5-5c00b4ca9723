using FlowCustomV1.Core.Models.Cluster;

namespace FlowCustomV1.Core.Models.Messages;

/// <summary>
/// 节点心跳消息
/// 用于节点向集群报告自身状态和健康信息
/// </summary>
public class NodeHeartbeatMessage : ClusterMessage
{
    /// <summary>
    /// 消息类型标识符
    /// </summary>
    public override string MessageType => "node.heartbeat";

    /// <summary>
    /// 节点当前状态
    /// </summary>
    public NodeStatus NodeStatus { get; set; } = NodeStatus.Unknown;

    /// <summary>
    /// 节点负载信息
    /// </summary>
    public NodeLoad LoadInfo { get; set; } = new();

    /// <summary>
    /// 节点健康状态
    /// </summary>
    public HealthStatus HealthStatus { get; set; } = new();

    /// <summary>
    /// 节点信息 (向后兼容)
    /// </summary>
    public NodeInfo? NodeInfo { get; set; }

    /// <summary>
    /// 健康信息 (向后兼容)
    /// </summary>
    public HealthStatus? HealthInfo 
    { 
        get => HealthStatus; 
        set => HealthStatus = value ?? new(); 
    }

    /// <summary>
    /// 心跳序列号
    /// </summary>
    public long SequenceNumber { get; set; } = 0;

    /// <summary>
    /// 上次心跳时间
    /// </summary>
    public DateTime LastHeartbeatAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 节点正常运行时间（秒）
    /// </summary>
    public long UptimeSeconds { get; set; } = 0;

    /// <summary>
    /// 当前活跃任务数
    /// </summary>
    public int ActiveTaskCount { get; set; } = 0;

    /// <summary>
    /// 队列中的任务数
    /// </summary>
    public int QueuedTaskCount { get; set; } = 0;

    /// <summary>
    /// 已完成任务数
    /// </summary>
    public long CompletedTaskCount { get; set; } = 0;

    /// <summary>
    /// 失败任务数
    /// </summary>
    public long FailedTaskCount { get; set; } = 0;

    /// <summary>
    /// 节点能力信息
    /// </summary>
    public NodeCapabilities? Capabilities { get; set; }

    /// <summary>
    /// 网络信息
    /// </summary>
    public NetworkInfo? NetworkInfo { get; set; }

    /// <summary>
    /// 性能指标
    /// </summary>
    public Dictionary<string, double> PerformanceMetrics { get; set; } = new();

    /// <summary>
    /// 创建心跳消息
    /// </summary>
    /// <param name="nodeInfo">节点信息</param>
    /// <returns>心跳消息</returns>
    public static NodeHeartbeatMessage Create(NodeInfo nodeInfo)
    {
        return new NodeHeartbeatMessage
        {
            SenderId = nodeInfo.NodeId,
            NodeStatus = nodeInfo.Status,
            LoadInfo = nodeInfo.Load.Clone(),
            HealthStatus = nodeInfo.Health.Clone(),
            NodeInfo = nodeInfo,
            Capabilities = nodeInfo.Capabilities.Clone(),
            NetworkInfo = nodeInfo.Network.Clone(),
            LastHeartbeatAt = DateTime.UtcNow,
            UptimeSeconds = (long)(DateTime.UtcNow - nodeInfo.Timestamps.CreatedAt).TotalSeconds
        };
    }

    /// <summary>
    /// 添加性能指标
    /// </summary>
    /// <param name="metricName">指标名称</param>
    /// <param name="value">指标值</param>
    public void AddPerformanceMetric(string metricName, double value)
    {
        PerformanceMetrics[metricName] = value;
    }

    /// <summary>
    /// 获取性能指标
    /// </summary>
    /// <param name="metricName">指标名称</param>
    /// <returns>指标值</returns>
    public double? GetPerformanceMetric(string metricName)
    {
        return PerformanceMetrics.TryGetValue(metricName, out var value) ? value : null;
    }

    /// <summary>
    /// 验证心跳消息的有效性
    /// </summary>
    /// <returns>验证结果</returns>
    public override bool IsValid()
    {
        return base.IsValid() &&
               NodeStatus != NodeStatus.Unknown &&
               LoadInfo != null &&
               HealthStatus != null &&
               SequenceNumber >= 0 &&
               UptimeSeconds >= 0;
    }

    /// <summary>
    /// 获取心跳消息的详细描述
    /// </summary>
    /// <returns>详细描述</returns>
    public string GetDetailedDescription()
    {
        return $"Heartbeat from {SenderId}: Status={NodeStatus}, " +
               $"ActiveTasks={ActiveTaskCount}, Uptime={UptimeSeconds}s, " +
               $"Sequence={SequenceNumber}";
    }

    /// <summary>
    /// 获取心跳消息的字符串表示
    /// </summary>
    /// <returns>字符串表示</returns>
    public override string ToString()
    {
        return $"NodeHeartbeat[{SenderId}] Status={NodeStatus} Seq={SequenceNumber} " +
               $"Tasks={ActiveTaskCount} at {CreatedAt:HH:mm:ss}";
    }
}
