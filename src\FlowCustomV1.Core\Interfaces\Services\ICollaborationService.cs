using FlowCustomV1.Core.Models.Designer;

namespace FlowCustomV1.Core.Interfaces.Services;

/// <summary>
/// 协作服务接口
/// 提供多用户实时协作设计和冲突解决功能
/// </summary>
public interface ICollaborationService
{
    #region 协作会话管理

    /// <summary>
    /// 创建协作会话
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="sessionInfo">会话信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>会话ID</returns>
    Task<string> CreateCollaborationSessionAsync(string workflowId, CollaborationSessionInfo sessionInfo, CancellationToken cancellationToken = default);

    /// <summary>
    /// 加入协作会话
    /// </summary>
    /// <param name="sessionId">会话ID</param>
    /// <param name="collaborator">协作者信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>加入结果</returns>
    Task<bool> JoinSessionAsync(string sessionId, CollaboratorInfo collaborator, CancellationToken cancellationToken = default);

    /// <summary>
    /// 离开协作会话
    /// </summary>
    /// <param name="sessionId">会话ID</param>
    /// <param name="collaboratorId">协作者ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>离开结果</returns>
    Task<bool> LeaveSessionAsync(string sessionId, string collaboratorId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取协作会话信息
    /// </summary>
    /// <param name="sessionId">会话ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>会话信息</returns>
    Task<CollaborationSession?> GetSessionAsync(string sessionId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取工作流的活跃协作会话
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>协作会话列表</returns>
    Task<IReadOnlyList<CollaborationSession>> GetActiveSessionsAsync(string workflowId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 结束协作会话
    /// </summary>
    /// <param name="sessionId">会话ID</param>
    /// <param name="reason">结束原因</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>结束结果</returns>
    Task<bool> EndSessionAsync(string sessionId, string reason = "", CancellationToken cancellationToken = default);

    #endregion

    #region 实时协作

    /// <summary>
    /// 广播设计操作
    /// </summary>
    /// <param name="sessionId">会话ID</param>
    /// <param name="operation">设计操作</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>广播任务</returns>
    Task BroadcastOperationAsync(string sessionId, DesignOperation operation, CancellationToken cancellationToken = default);

    /// <summary>
    /// 更新协作者状态
    /// </summary>
    /// <param name="sessionId">会话ID</param>
    /// <param name="collaboratorId">协作者ID</param>
    /// <param name="status">新状态</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>更新结果</returns>
    Task<bool> UpdateCollaboratorStatusAsync(string sessionId, string collaboratorId, CollaboratorStatus status, CancellationToken cancellationToken = default);

    /// <summary>
    /// 更新协作者光标位置
    /// </summary>
    /// <param name="sessionId">会话ID</param>
    /// <param name="collaboratorId">协作者ID</param>
    /// <param name="position">光标位置</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>更新结果</returns>
    Task<bool> UpdateCursorPositionAsync(string sessionId, string collaboratorId, CursorPosition position, CancellationToken cancellationToken = default);

    /// <summary>
    /// 更新协作者选择
    /// </summary>
    /// <param name="sessionId">会话ID</param>
    /// <param name="collaboratorId">协作者ID</param>
    /// <param name="selection">选择的对象ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>更新结果</returns>
    Task<bool> UpdateSelectionAsync(string sessionId, string collaboratorId, string selection, CancellationToken cancellationToken = default);

    #endregion

    #region 冲突检测和解决

    /// <summary>
    /// 检测操作冲突
    /// </summary>
    /// <param name="sessionId">会话ID</param>
    /// <param name="operation">设计操作</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>冲突检测结果</returns>
    Task<ConflictDetectionResult> DetectConflictAsync(string sessionId, DesignOperation operation, CancellationToken cancellationToken = default);

    /// <summary>
    /// 解决设计冲突
    /// </summary>
    /// <param name="sessionId">会话ID</param>
    /// <param name="conflictId">冲突ID</param>
    /// <param name="resolution">解决方案</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>解决结果</returns>
    Task<bool> ResolveConflictAsync(string sessionId, string conflictId, ConflictResolution resolution, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取待解决的冲突列表
    /// </summary>
    /// <param name="sessionId">会话ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>冲突列表</returns>
    Task<IReadOnlyList<DesignConflict>> GetPendingConflictsAsync(string sessionId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 应用操作变更
    /// </summary>
    /// <param name="sessionId">会话ID</param>
    /// <param name="operations">操作列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>应用结果</returns>
    Task<OperationApplicationResult> ApplyOperationsAsync(string sessionId, IReadOnlyList<DesignOperation> operations, CancellationToken cancellationToken = default);

    #endregion

    #region 协作历史和审计

    /// <summary>
    /// 获取协作历史
    /// </summary>
    /// <param name="sessionId">会话ID</param>
    /// <param name="query">查询条件</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>协作历史</returns>
    Task<IReadOnlyList<CollaborationHistoryEntry>> GetCollaborationHistoryAsync(string sessionId, CollaborationHistoryQuery? query = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 记录协作活动
    /// </summary>
    /// <param name="sessionId">会话ID</param>
    /// <param name="activity">活动信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>记录结果</returns>
    Task<bool> LogActivityAsync(string sessionId, CollaborationActivity activity, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取协作统计信息
    /// </summary>
    /// <param name="sessionId">会话ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>统计信息</returns>
    Task<CollaborationStatistics> GetStatisticsAsync(string sessionId, CancellationToken cancellationToken = default);

    #endregion

    #region 权限管理

    /// <summary>
    /// 检查协作者权限
    /// </summary>
    /// <param name="sessionId">会话ID</param>
    /// <param name="collaboratorId">协作者ID</param>
    /// <param name="permission">权限名称</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否有权限</returns>
    Task<bool> CheckPermissionAsync(string sessionId, string collaboratorId, string permission, CancellationToken cancellationToken = default);

    /// <summary>
    /// 更新协作者权限
    /// </summary>
    /// <param name="sessionId">会话ID</param>
    /// <param name="collaboratorId">协作者ID</param>
    /// <param name="permissions">权限列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>更新结果</returns>
    Task<bool> UpdatePermissionsAsync(string sessionId, string collaboratorId, HashSet<string> permissions, CancellationToken cancellationToken = default);

    /// <summary>
    /// 更新协作者角色
    /// </summary>
    /// <param name="sessionId">会话ID</param>
    /// <param name="collaboratorId">协作者ID</param>
    /// <param name="role">新角色</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>更新结果</returns>
    Task<bool> UpdateRoleAsync(string sessionId, string collaboratorId, CollaboratorRole role, CancellationToken cancellationToken = default);

    #endregion

    #region 事件

    /// <summary>
    /// 协作者加入事件
    /// </summary>
    event EventHandler<CollaboratorJoinedEventArgs>? CollaboratorJoined;

    /// <summary>
    /// 协作者离开事件
    /// </summary>
    event EventHandler<CollaboratorLeftEventArgs>? CollaboratorLeft;

    /// <summary>
    /// 设计操作事件
    /// </summary>
    event EventHandler<DesignOperationEventArgs>? DesignOperationReceived;

    /// <summary>
    /// 冲突检测事件
    /// </summary>
    event EventHandler<ConflictDetectedEventArgs>? ConflictDetected;

    /// <summary>
    /// 冲突解决事件
    /// </summary>
    event EventHandler<ConflictResolvedEventArgs>? ConflictResolved;

    /// <summary>
    /// 协作会话状态变更事件
    /// </summary>
    event EventHandler<SessionStatusChangedEventArgs>? SessionStatusChanged;

    #endregion
}
