using FlowCustomV1.Core.Models.Cluster;

namespace FlowCustomV1.Core.Models.Scheduling;

/// <summary>
/// 任务重试计划事件参数
/// </summary>
public class TaskRetryPlannedEventArgs : EventArgs
{
    /// <summary>
    /// 任务ID
    /// </summary>
    public string TaskId { get; set; } = string.Empty;

    /// <summary>
    /// 重试计划
    /// </summary>
    public TaskRetryPlan RetryPlan { get; set; } = null!;

    /// <summary>
    /// 失败信息
    /// </summary>
    public TaskFailureInfo FailureInfo { get; set; } = null!;

    /// <summary>
    /// 计划创建时间
    /// </summary>
    public DateTime PlannedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 使用的重试策略
    /// </summary>
    public string RetryStrategy { get; set; } = string.Empty;

    /// <summary>
    /// 附加信息
    /// </summary>
    public Dictionary<string, object> AdditionalInfo { get; set; } = new();
}

/// <summary>
/// 任务重试开始事件参数
/// </summary>
public class TaskRetryStartedEventArgs : EventArgs
{
    /// <summary>
    /// 任务ID
    /// </summary>
    public string TaskId { get; set; } = string.Empty;

    /// <summary>
    /// 重试计划ID
    /// </summary>
    public string RetryPlanId { get; set; } = string.Empty;

    /// <summary>
    /// 当前重试次数
    /// </summary>
    public int CurrentRetryCount { get; set; }

    /// <summary>
    /// 最大重试次数
    /// </summary>
    public int MaxRetries { get; set; }

    /// <summary>
    /// 执行节点ID
    /// </summary>
    public string ExecutorNodeId { get; set; } = string.Empty;

    /// <summary>
    /// 重试开始时间
    /// </summary>
    public DateTime StartedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 重试原因
    /// </summary>
    public string RetryReason { get; set; } = string.Empty;

    /// <summary>
    /// 使用的重试策略
    /// </summary>
    public string RetryStrategy { get; set; } = string.Empty;

    /// <summary>
    /// 附加信息
    /// </summary>
    public Dictionary<string, object> AdditionalInfo { get; set; } = new();
}

/// <summary>
/// 任务重试完成事件参数
/// </summary>
public class TaskRetryCompletedEventArgs : EventArgs
{
    /// <summary>
    /// 任务ID
    /// </summary>
    public string TaskId { get; set; } = string.Empty;

    /// <summary>
    /// 重试计划ID
    /// </summary>
    public string RetryPlanId { get; set; } = string.Empty;

    /// <summary>
    /// 重试次数
    /// </summary>
    public int RetryCount { get; set; }

    /// <summary>
    /// 执行节点ID
    /// </summary>
    public string ExecutorNodeId { get; set; } = string.Empty;

    /// <summary>
    /// 重试完成时间
    /// </summary>
    public DateTime CompletedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 重试结果
    /// </summary>
    public TaskRetryExecutionResult RetryResult { get; set; } = null!;

    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 总重试时间（毫秒）
    /// </summary>
    public long TotalRetryTimeMs { get; set; }

    /// <summary>
    /// 附加信息
    /// </summary>
    public Dictionary<string, object> AdditionalInfo { get; set; } = new();
}

/// <summary>
/// 任务重试失败事件参数
/// </summary>
public class TaskRetryFailedEventArgs : EventArgs
{
    /// <summary>
    /// 任务ID
    /// </summary>
    public string TaskId { get; set; } = string.Empty;

    /// <summary>
    /// 重试计划ID
    /// </summary>
    public string RetryPlanId { get; set; } = string.Empty;

    /// <summary>
    /// 重试次数
    /// </summary>
    public int RetryCount { get; set; }

    /// <summary>
    /// 执行节点ID
    /// </summary>
    public string ExecutorNodeId { get; set; } = string.Empty;

    /// <summary>
    /// 重试失败时间
    /// </summary>
    public DateTime FailedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 失败信息
    /// </summary>
    public TaskFailureInfo FailureInfo { get; set; } = null!;

    /// <summary>
    /// 错误消息
    /// </summary>
    public string ErrorMessage { get; set; } = string.Empty;

    /// <summary>
    /// 是否为最后一次重试
    /// </summary>
    public bool IsLastRetry { get; set; }

    /// <summary>
    /// 是否需要升级处理
    /// </summary>
    public bool RequiresEscalation { get; set; }

    /// <summary>
    /// 附加信息
    /// </summary>
    public Dictionary<string, object> AdditionalInfo { get; set; } = new();
}

/// <summary>
/// 任务重试取消事件参数
/// </summary>
public class TaskRetryCancelledEventArgs : EventArgs
{
    /// <summary>
    /// 任务ID
    /// </summary>
    public string TaskId { get; set; } = string.Empty;

    /// <summary>
    /// 重试计划ID
    /// </summary>
    public string RetryPlanId { get; set; } = string.Empty;

    /// <summary>
    /// 取消时的重试次数
    /// </summary>
    public int RetryCount { get; set; }

    /// <summary>
    /// 取消时间
    /// </summary>
    public DateTime CancelledAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 取消原因
    /// </summary>
    public string CancellationReason { get; set; } = string.Empty;

    /// <summary>
    /// 取消者ID
    /// </summary>
    public string CancelledBy { get; set; } = string.Empty;

    /// <summary>
    /// 是否为用户主动取消
    /// </summary>
    public bool IsUserCancellation { get; set; }

    /// <summary>
    /// 附加信息
    /// </summary>
    public Dictionary<string, object> AdditionalInfo { get; set; } = new();
}

/// <summary>
/// 故障恢复执行事件参数
/// </summary>
public class RecoveryExecutedEventArgs : EventArgs
{
    /// <summary>
    /// 任务ID
    /// </summary>
    public string TaskId { get; set; } = string.Empty;

    /// <summary>
    /// 恢复操作ID
    /// </summary>
    public string RecoveryId { get; set; } = string.Empty;

    /// <summary>
    /// 恢复类型
    /// </summary>
    public string RecoveryType { get; set; } = string.Empty;

    /// <summary>
    /// 执行时间
    /// </summary>
    public DateTime ExecutedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 恢复结果
    /// </summary>
    public RecoveryExecutionResult RecoveryResult { get; set; } = null!;

    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 恢复建议
    /// </summary>
    public RecoveryRecommendations? Recommendations { get; set; }

    /// <summary>
    /// 附加信息
    /// </summary>
    public Dictionary<string, object> AdditionalInfo { get; set; } = new();
}
