using FlowCustomV1.Core.Interfaces.Messaging;
using FlowCustomV1.Core.Interfaces.Services;
using FlowCustomV1.Core.Models.Designer;
using FlowCustomV1.Core.Models.Messages;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;

namespace FlowCustomV1.Infrastructure.Services.Designer;

/// <summary>
/// 协作服务实现
/// 提供多用户实时协作设计和冲突解决功能
/// </summary>
public class CollaborationService : ICollaborationService, IDisposable
{
    private readonly INatsService _natsService;
    private readonly ILogger<CollaborationService> _logger;
    
    // 协作会话存储
    private readonly ConcurrentDictionary<string, CollaborationSession> _sessions = new();
    private readonly ConcurrentDictionary<string, List<CollaborationHistoryEntry>> _sessionHistory = new();
    private readonly ConcurrentDictionary<string, List<DesignConflict>> _sessionConflicts = new();
    private readonly ConcurrentDictionary<string, CollaborationStatistics> _sessionStatistics = new();
    
    // 实时状态跟踪
    private readonly ConcurrentDictionary<string, DateTime> _lastActivity = new();
    private readonly Timer _cleanupTimer;
    
    private bool _disposed = false;

    /// <summary>
    /// 构造函数
    /// </summary>
    public CollaborationService(
        INatsService natsService,
        ILogger<CollaborationService> logger)
    {
        _natsService = natsService ?? throw new ArgumentNullException(nameof(natsService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        // 启动清理定时器（每5分钟清理一次过期会话）
        _cleanupTimer = new Timer(CleanupExpiredSessions, null, TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(5));
        
        // 初始化NATS订阅
        _ = Task.Run(InitializeNatsSubscriptionsAsync);
    }

    #region 协作会话管理

    /// <inheritdoc />
    public async Task<string> CreateCollaborationSessionAsync(string workflowId, CollaborationSessionInfo sessionInfo, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(workflowId);
        ArgumentNullException.ThrowIfNull(sessionInfo);

        try
        {
            _logger.LogInformation("Creating collaboration session for workflow {WorkflowId}", workflowId);

            var sessionId = Guid.NewGuid().ToString("N");
            var session = new CollaborationSession
            {
                SessionId = sessionId,
                WorkflowId = workflowId,
                Name = sessionInfo.Name,
                Description = sessionInfo.Description,
                CreatedBy = sessionInfo.CreatedBy,
                CreatedAt = DateTime.UtcNow,
                Status = SessionStatus.Active,
                MaxCollaborators = sessionInfo.MaxCollaborators,
                Settings = sessionInfo.Settings,
                LastActivityAt = DateTime.UtcNow
            };

            _sessions.TryAdd(sessionId, session);
            _sessionHistory.TryAdd(sessionId, new List<CollaborationHistoryEntry>());
            _sessionConflicts.TryAdd(sessionId, new List<DesignConflict>());
            _sessionStatistics.TryAdd(sessionId, new CollaborationStatistics { SessionId = sessionId });

            // 记录会话创建活动
            await LogActivityAsync(sessionId, new CollaborationActivity
            {
                CollaboratorId = sessionInfo.CreatedBy,
                CollaboratorName = "Session Creator",
                ActivityType = CollaborationActivityType.JoinSession,
                Description = $"Created collaboration session '{sessionInfo.Name}'"
            }, cancellationToken);

            // 触发会话状态变更事件
            OnSessionStatusChanged(new SessionStatusChangedEventArgs
            {
                SessionId = sessionId,
                PreviousStatus = SessionStatus.Active, // 新会话
                NewStatus = SessionStatus.Active,
                ChangedAt = DateTime.UtcNow,
                Reason = "Session created",
                TriggeredBy = sessionInfo.CreatedBy
            });

            _logger.LogInformation("Collaboration session {SessionId} created successfully for workflow {WorkflowId}", sessionId, workflowId);
            return sessionId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create collaboration session for workflow {WorkflowId}", workflowId);
            return string.Empty;
        }
    }

    /// <inheritdoc />
    public async Task<bool> JoinSessionAsync(string sessionId, CollaboratorInfo collaborator, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(sessionId);
        ArgumentNullException.ThrowIfNull(collaborator);

        try
        {
            _logger.LogInformation("Collaborator {CollaboratorId} joining session {SessionId}", collaborator.CollaboratorId, sessionId);

            if (!_sessions.TryGetValue(sessionId, out var session))
            {
                _logger.LogWarning("Session {SessionId} not found", sessionId);
                return false;
            }

            if (session.Status != SessionStatus.Active)
            {
                _logger.LogWarning("Session {SessionId} is not active (status: {Status})", sessionId, session.Status);
                return false;
            }

            if (session.Collaborators.Count >= session.MaxCollaborators)
            {
                _logger.LogWarning("Session {SessionId} has reached maximum collaborators limit ({MaxCollaborators})", sessionId, session.MaxCollaborators);
                return false;
            }

            // 检查协作者是否已在会话中
            var existingCollaborator = session.Collaborators.FirstOrDefault(c => c.CollaboratorId == collaborator.CollaboratorId);
            if (existingCollaborator != null)
            {
                // 更新现有协作者信息
                existingCollaborator.LastActiveAt = DateTime.UtcNow;
                existingCollaborator.Status = CollaboratorStatus.Online;
            }
            else
            {
                // 添加新协作者
                collaborator.JoinedAt = DateTime.UtcNow;
                collaborator.LastActiveAt = DateTime.UtcNow;
                collaborator.Status = CollaboratorStatus.Online;
                session.Collaborators.Add(collaborator);
            }

            session.LastActivityAt = DateTime.UtcNow;
            _lastActivity.TryAdd(collaborator.CollaboratorId, DateTime.UtcNow);

            // 记录加入活动
            await LogActivityAsync(sessionId, new CollaborationActivity
            {
                CollaboratorId = collaborator.CollaboratorId,
                CollaboratorName = collaborator.Name,
                ActivityType = CollaborationActivityType.JoinSession,
                Description = $"{collaborator.Name} joined the collaboration session"
            }, cancellationToken);

            // 更新统计信息
            if (_sessionStatistics.TryGetValue(sessionId, out var stats))
            {
                stats.TotalCollaborators = Math.Max(stats.TotalCollaborators, session.Collaborators.Count);
                stats.ActiveCollaborators = session.Collaborators.Count(c => c.Status == CollaboratorStatus.Online);
            }

            // 触发协作者加入事件
            OnCollaboratorJoined(new CollaboratorJoinedEventArgs
            {
                SessionId = sessionId,
                Collaborator = collaborator,
                JoinedAt = DateTime.UtcNow,
                TotalCollaborators = session.Collaborators.Count
            });

            _logger.LogInformation("Collaborator {CollaboratorId} joined session {SessionId} successfully", collaborator.CollaboratorId, sessionId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to join session {SessionId} for collaborator {CollaboratorId}", sessionId, collaborator.CollaboratorId);
            return false;
        }
    }

    /// <inheritdoc />
    public async Task<bool> LeaveSessionAsync(string sessionId, string collaboratorId, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(sessionId);
        ArgumentException.ThrowIfNullOrWhiteSpace(collaboratorId);

        try
        {
            _logger.LogInformation("Collaborator {CollaboratorId} leaving session {SessionId}", collaboratorId, sessionId);

            if (!_sessions.TryGetValue(sessionId, out var session))
            {
                _logger.LogWarning("Session {SessionId} not found", sessionId);
                return false;
            }

            var collaborator = session.Collaborators.FirstOrDefault(c => c.CollaboratorId == collaboratorId);
            if (collaborator == null)
            {
                _logger.LogWarning("Collaborator {CollaboratorId} not found in session {SessionId}", collaboratorId, sessionId);
                return false;
            }

            // 移除协作者
            session.Collaborators.Remove(collaborator);
            session.LastActivityAt = DateTime.UtcNow;
            _lastActivity.TryRemove(collaboratorId, out _);

            // 记录离开活动
            await LogActivityAsync(sessionId, new CollaborationActivity
            {
                CollaboratorId = collaboratorId,
                CollaboratorName = collaborator.Name,
                ActivityType = CollaborationActivityType.LeaveSession,
                Description = $"{collaborator.Name} left the collaboration session"
            }, cancellationToken);

            // 更新统计信息
            if (_sessionStatistics.TryGetValue(sessionId, out var stats))
            {
                stats.ActiveCollaborators = session.Collaborators.Count(c => c.Status == CollaboratorStatus.Online);
            }

            // 触发协作者离开事件
            OnCollaboratorLeft(new CollaboratorLeftEventArgs
            {
                SessionId = sessionId,
                CollaboratorId = collaboratorId,
                CollaboratorName = collaborator.Name,
                LeftAt = DateTime.UtcNow,
                RemainingCollaborators = session.Collaborators.Count
            });

            // 如果没有协作者了，考虑结束会话
            if (session.Collaborators.Count == 0 && session.Settings.AutoSaveIntervalSeconds > 0)
            {
                await EndSessionAsync(sessionId, "No active collaborators", cancellationToken);
            }

            _logger.LogInformation("Collaborator {CollaboratorId} left session {SessionId} successfully", collaboratorId, sessionId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to leave session {SessionId} for collaborator {CollaboratorId}", sessionId, collaboratorId);
            return false;
        }
    }

    /// <inheritdoc />
    public Task<CollaborationSession?> GetSessionAsync(string sessionId, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(sessionId);

        try
        {
            if (_sessions.TryGetValue(sessionId, out var session))
            {
                // 返回会话的副本以避免外部修改
                return Task.FromResult<CollaborationSession?>(CloneSession(session));
            }

            return Task.FromResult<CollaborationSession?>(null);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get session {SessionId}", sessionId);
            return Task.FromResult<CollaborationSession?>(null);
        }
    }

    /// <inheritdoc />
    public Task<IReadOnlyList<CollaborationSession>> GetActiveSessionsAsync(string workflowId, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(workflowId);

        try
        {
            var activeSessions = _sessions.Values
                .Where(s => s.WorkflowId == workflowId && s.Status == SessionStatus.Active)
                .Select(CloneSession)
                .OrderByDescending(s => s.LastActivityAt)
                .ToList();

            return Task.FromResult<IReadOnlyList<CollaborationSession>>(activeSessions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get active sessions for workflow {WorkflowId}", workflowId);
            return Task.FromResult<IReadOnlyList<CollaborationSession>>(new List<CollaborationSession>());
        }
    }

    /// <inheritdoc />
    public async Task<bool> EndSessionAsync(string sessionId, string reason = "", CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(sessionId);

        try
        {
            _logger.LogInformation("Ending session {SessionId} with reason: {Reason}", sessionId, reason);

            if (!_sessions.TryGetValue(sessionId, out var session))
            {
                _logger.LogWarning("Session {SessionId} not found", sessionId);
                return false;
            }

            var previousStatus = session.Status;
            session.Status = SessionStatus.Ended;
            session.EndedAt = DateTime.UtcNow;

            // 记录会话结束活动
            await LogActivityAsync(sessionId, new CollaborationActivity
            {
                CollaboratorId = "system",
                CollaboratorName = "System",
                ActivityType = CollaborationActivityType.SessionEnded,
                Description = $"Session ended: {reason}"
            }, cancellationToken);

            // 触发会话状态变更事件
            OnSessionStatusChanged(new SessionStatusChangedEventArgs
            {
                SessionId = sessionId,
                PreviousStatus = previousStatus,
                NewStatus = SessionStatus.Ended,
                ChangedAt = DateTime.UtcNow,
                Reason = reason,
                TriggeredBy = "system"
            });

            _logger.LogInformation("Session {SessionId} ended successfully", sessionId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to end session {SessionId}", sessionId);
            return false;
        }
    }

    #endregion

    #region 实时协作

    /// <inheritdoc />
    public async Task BroadcastOperationAsync(string sessionId, DesignOperation operation, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(sessionId);
        ArgumentNullException.ThrowIfNull(operation);

        try
        {
            _logger.LogDebug("Broadcasting operation {OperationType} in session {SessionId}", operation.Type, sessionId);

            if (!_sessions.TryGetValue(sessionId, out var session))
            {
                _logger.LogWarning("Session {SessionId} not found", sessionId);
                return;
            }

            // 检测冲突
            var conflictResult = await DetectConflictAsync(sessionId, operation, cancellationToken);
            if (conflictResult.HasConflict)
            {
                _logger.LogWarning("Conflict detected for operation {OperationId} in session {SessionId}", operation.OperationId, sessionId);

                // 触发冲突检测事件
                OnConflictDetected(new ConflictDetectedEventArgs
                {
                    ConflictId = conflictResult.ConflictId,
                    WorkflowId = session.WorkflowId,
                    ConflictType = conflictResult.ConflictType,
                    ConflictingCollaborators = conflictResult.InvolvedCollaborators.Select(id =>
                        session.Collaborators.FirstOrDefault(c => c.CollaboratorId == id)).Where(c => c != null).ToList()!,
                    ConflictingOperations = conflictResult.ConflictingOperations,
                    DetectedAt = DateTime.UtcNow,
                    Description = conflictResult.Description
                });

                return;
            }

            // 更新会话活动时间
            session.LastActivityAt = DateTime.UtcNow;
            _lastActivity.TryAdd(operation.CollaboratorId, DateTime.UtcNow);

            // 记录操作活动
            await LogActivityAsync(sessionId, new CollaborationActivity
            {
                CollaboratorId = operation.CollaboratorId,
                CollaboratorName = session.Collaborators.FirstOrDefault(c => c.CollaboratorId == operation.CollaboratorId)?.Name ?? "Unknown",
                ActivityType = CollaborationActivityType.DesignOperation,
                Description = $"Performed {operation.Type} operation on {operation.TargetType}",
                Operation = operation
            }, cancellationToken);

            // 更新统计信息
            if (_sessionStatistics.TryGetValue(sessionId, out var stats))
            {
                stats.TotalOperations++;
                if (stats.OperationsByCollaborator.ContainsKey(operation.CollaboratorId))
                    stats.OperationsByCollaborator[operation.CollaboratorId]++;
                else
                    stats.OperationsByCollaborator[operation.CollaboratorId] = 1;

                var operationType = operation.Type.ToString();
                if (stats.OperationsByType.ContainsKey(operationType))
                    stats.OperationsByType[operationType]++;
                else
                    stats.OperationsByType[operationType] = 1;
            }

            // 触发设计操作事件
            OnDesignOperationReceived(new DesignOperationEventArgs
            {
                SessionId = sessionId,
                Operation = operation,
                SourceCollaborator = session.Collaborators.FirstOrDefault(c => c.CollaboratorId == operation.CollaboratorId),
                ReceivedAt = DateTime.UtcNow,
                ShouldBroadcast = operation.RequiresSync
            });

            _logger.LogDebug("Operation {OperationId} broadcasted successfully in session {SessionId}", operation.OperationId, sessionId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to broadcast operation in session {SessionId}", sessionId);
            throw;
        }
    }

    /// <inheritdoc />
    public async Task<bool> UpdateCollaboratorStatusAsync(string sessionId, string collaboratorId, CollaboratorStatus status, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(sessionId);
        ArgumentException.ThrowIfNullOrWhiteSpace(collaboratorId);

        try
        {
            if (!_sessions.TryGetValue(sessionId, out var session))
            {
                return false;
            }

            var collaborator = session.Collaborators.FirstOrDefault(c => c.CollaboratorId == collaboratorId);
            if (collaborator == null)
            {
                return false;
            }

            var previousStatus = collaborator.Status;
            collaborator.Status = status;
            collaborator.LastActiveAt = DateTime.UtcNow;
            session.LastActivityAt = DateTime.UtcNow;

            _lastActivity.TryAdd(collaboratorId, DateTime.UtcNow);

            // 记录状态更新活动
            await LogActivityAsync(sessionId, new CollaborationActivity
            {
                CollaboratorId = collaboratorId,
                CollaboratorName = collaborator.Name,
                ActivityType = CollaborationActivityType.StatusUpdate,
                Description = $"Status changed from {previousStatus} to {status}"
            }, cancellationToken);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update collaborator status in session {SessionId}", sessionId);
            return false;
        }
    }

    /// <inheritdoc />
    public Task<bool> UpdateCursorPositionAsync(string sessionId, string collaboratorId, CursorPosition position, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(sessionId);
        ArgumentException.ThrowIfNullOrWhiteSpace(collaboratorId);
        ArgumentNullException.ThrowIfNull(position);

        try
        {
            if (!_sessions.TryGetValue(sessionId, out var session))
            {
                return Task.FromResult(false);
            }

            var collaborator = session.Collaborators.FirstOrDefault(c => c.CollaboratorId == collaboratorId);
            if (collaborator == null)
            {
                return Task.FromResult(false);
            }

            collaborator.CursorPosition = position;
            collaborator.LastActiveAt = DateTime.UtcNow;
            session.LastActivityAt = DateTime.UtcNow;

            _lastActivity.TryAdd(collaboratorId, DateTime.UtcNow);

            return Task.FromResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update cursor position in session {SessionId}", sessionId);
            return Task.FromResult(false);
        }
    }

    /// <inheritdoc />
    public async Task<bool> UpdateSelectionAsync(string sessionId, string collaboratorId, string selection, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(sessionId);
        ArgumentException.ThrowIfNullOrWhiteSpace(collaboratorId);

        try
        {
            if (!_sessions.TryGetValue(sessionId, out var session))
            {
                return false;
            }

            var collaborator = session.Collaborators.FirstOrDefault(c => c.CollaboratorId == collaboratorId);
            if (collaborator == null)
            {
                return false;
            }

            collaborator.CurrentSelection = selection;
            collaborator.LastActiveAt = DateTime.UtcNow;
            session.LastActivityAt = DateTime.UtcNow;

            _lastActivity.TryAdd(collaboratorId, DateTime.UtcNow);

            // 记录选择变更活动
            await LogActivityAsync(sessionId, new CollaborationActivity
            {
                CollaboratorId = collaboratorId,
                CollaboratorName = collaborator.Name,
                ActivityType = CollaborationActivityType.SelectionChange,
                Description = $"Selection changed to: {selection}",
                Data = new Dictionary<string, object> { { "selection", selection } }
            }, cancellationToken);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update selection in session {SessionId}", sessionId);
            return false;
        }
    }

    #endregion

    #region 冲突检测和解决

    /// <inheritdoc />
    public Task<ConflictDetectionResult> DetectConflictAsync(string sessionId, DesignOperation operation, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(sessionId);
        ArgumentNullException.ThrowIfNull(operation);

        try
        {
            if (!_sessions.TryGetValue(sessionId, out var session))
            {
                return Task.FromResult(new ConflictDetectionResult { HasConflict = false });
            }

            if (!session.Settings.EnableConflictDetection)
            {
                return Task.FromResult(new ConflictDetectionResult { HasConflict = false });
            }

            var result = new ConflictDetectionResult
            {
                HasConflict = false,
                DetectedAt = DateTime.UtcNow
            };

            // 检查并发编辑冲突
            var conflictingCollaborators = session.Collaborators
                .Where(c => c.CollaboratorId != operation.CollaboratorId &&
                           c.CurrentSelection == operation.TargetId &&
                           DateTime.UtcNow - c.LastActiveAt < TimeSpan.FromSeconds(30))
                .ToList();

            if (conflictingCollaborators.Any())
            {
                result.HasConflict = true;
                result.ConflictType = ConflictType.ConcurrentEdit;
                result.ConflictId = Guid.NewGuid().ToString("N");
                result.Description = $"Multiple collaborators are editing the same object: {operation.TargetId}";
                result.InvolvedCollaborators = conflictingCollaborators.Select(c => c.CollaboratorId).ToList();
                result.ConflictingOperations = new List<DesignOperation> { operation };

                // 创建冲突记录
                var conflict = new DesignConflict
                {
                    ConflictId = result.ConflictId,
                    SessionId = sessionId,
                    ConflictType = result.ConflictType,
                    Status = ConflictStatus.Pending,
                    Description = result.Description,
                    InvolvedCollaborators = conflictingCollaborators,
                    ConflictingOperations = result.ConflictingOperations,
                    CreatedAt = DateTime.UtcNow
                };

                if (_sessionConflicts.TryGetValue(sessionId, out var conflicts))
                {
                    conflicts.Add(conflict);
                }

                // 更新统计信息
                if (_sessionStatistics.TryGetValue(sessionId, out var stats))
                {
                    stats.TotalConflicts++;
                }

                // 生成解决建议
                result.SuggestedResolutions = GenerateConflictResolutionSuggestions(conflict);
            }

            return Task.FromResult(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to detect conflict in session {SessionId}", sessionId);
            return Task.FromResult(new ConflictDetectionResult { HasConflict = false });
        }
    }

    /// <inheritdoc />
    public async Task<bool> ResolveConflictAsync(string sessionId, string conflictId, ConflictResolution resolution, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(sessionId);
        ArgumentException.ThrowIfNullOrWhiteSpace(conflictId);
        ArgumentNullException.ThrowIfNull(resolution);

        try
        {
            _logger.LogInformation("Resolving conflict {ConflictId} in session {SessionId}", conflictId, sessionId);

            if (!_sessionConflicts.TryGetValue(sessionId, out var conflicts))
            {
                return false;
            }

            var conflict = conflicts.FirstOrDefault(c => c.ConflictId == conflictId);
            if (conflict == null)
            {
                _logger.LogWarning("Conflict {ConflictId} not found in session {SessionId}", conflictId, sessionId);
                return false;
            }

            if (conflict.Status != ConflictStatus.Pending)
            {
                _logger.LogWarning("Conflict {ConflictId} is not in pending status (current: {Status})", conflictId, conflict.Status);
                return false;
            }

            // 应用解决方案
            conflict.Status = ConflictStatus.Resolved;
            conflict.ResolvedAt = DateTime.UtcNow;
            conflict.Resolution = resolution;

            // 记录冲突解决活动
            await LogActivityAsync(sessionId, new CollaborationActivity
            {
                CollaboratorId = resolution.ResolvedBy,
                CollaboratorName = "Conflict Resolver",
                ActivityType = CollaborationActivityType.ConflictResolved,
                Description = $"Resolved conflict using {resolution.Strategy} strategy: {resolution.Description}",
                Data = new Dictionary<string, object>
                {
                    { "conflictId", conflictId },
                    { "strategy", resolution.Strategy.ToString() }
                }
            }, cancellationToken);

            // 更新统计信息
            if (_sessionStatistics.TryGetValue(sessionId, out var stats))
            {
                stats.ResolvedConflicts++;
            }

            // 触发冲突解决事件
            OnConflictResolved(new ConflictResolvedEventArgs
            {
                SessionId = sessionId,
                ConflictId = conflictId,
                Resolution = resolution,
                ResolvedBy = resolution.ResolvedBy,
                ResolvedAt = DateTime.UtcNow,
                ResolutionTimeMs = (long)(DateTime.UtcNow - conflict.CreatedAt).TotalMilliseconds
            });

            _logger.LogInformation("Conflict {ConflictId} resolved successfully in session {SessionId}", conflictId, sessionId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to resolve conflict {ConflictId} in session {SessionId}", conflictId, sessionId);
            return false;
        }
    }

    /// <inheritdoc />
    public Task<IReadOnlyList<DesignConflict>> GetPendingConflictsAsync(string sessionId, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(sessionId);

        try
        {
            if (_sessionConflicts.TryGetValue(sessionId, out var conflicts))
            {
                var result = conflicts
                    .Where(c => c.Status == ConflictStatus.Pending)
                    .OrderByDescending(c => c.CreatedAt)
                    .ToList();
                return Task.FromResult<IReadOnlyList<DesignConflict>>(result);
            }

            return Task.FromResult<IReadOnlyList<DesignConflict>>(new List<DesignConflict>());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get pending conflicts for session {SessionId}", sessionId);
            return Task.FromResult<IReadOnlyList<DesignConflict>>(new List<DesignConflict>());
        }
    }

    /// <inheritdoc />
    public async Task<OperationApplicationResult> ApplyOperationsAsync(string sessionId, IReadOnlyList<DesignOperation> operations, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(sessionId);
        ArgumentNullException.ThrowIfNull(operations);

        var result = new OperationApplicationResult
        {
            AppliedAt = DateTime.UtcNow
        };

        try
        {
            _logger.LogInformation("Applying {OperationCount} operations in session {SessionId}", operations.Count, sessionId);

            if (!_sessions.TryGetValue(sessionId, out var session))
            {
                result.Success = false;
                result.Errors.Add("Session not found");
                return result;
            }

            foreach (var operation in operations)
            {
                try
                {
                    // 检测冲突
                    var conflictResult = await DetectConflictAsync(sessionId, operation, cancellationToken);
                    if (conflictResult.HasConflict)
                    {
                        result.SkippedCount++;
                        result.Warnings.Add($"Operation {operation.OperationId} skipped due to conflict");
                        continue;
                    }

                    // 应用操作（这里是简化实现，实际应该调用工作流引擎）
                    await BroadcastOperationAsync(sessionId, operation, cancellationToken);
                    result.AppliedCount++;
                }
                catch (Exception ex)
                {
                    result.FailedCount++;
                    result.Errors.Add($"Failed to apply operation {operation.OperationId}: {ex.Message}");
                    _logger.LogError(ex, "Failed to apply operation {OperationId}", operation.OperationId);
                }
            }

            result.Success = result.FailedCount == 0;
            _logger.LogInformation("Applied {AppliedCount}/{TotalCount} operations successfully in session {SessionId}",
                result.AppliedCount, operations.Count, sessionId);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to apply operations in session {SessionId}", sessionId);
            result.Success = false;
            result.Errors.Add($"Operation application failed: {ex.Message}");
            return result;
        }
    }

    #endregion

    #region 协作历史和审计

    /// <inheritdoc />
    public Task<IReadOnlyList<CollaborationHistoryEntry>> GetCollaborationHistoryAsync(string sessionId, CollaborationHistoryQuery? query = null, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(sessionId);

        try
        {
            if (!_sessionHistory.TryGetValue(sessionId, out var history))
            {
                return Task.FromResult<IReadOnlyList<CollaborationHistoryEntry>>(new List<CollaborationHistoryEntry>());
            }

            var filteredHistory = history.AsEnumerable();

            // 应用查询过滤
            if (query != null)
            {
                if (!string.IsNullOrWhiteSpace(query.CollaboratorId))
                {
                    filteredHistory = filteredHistory.Where(h => h.CollaboratorId == query.CollaboratorId);
                }

                if (query.ActivityTypes?.Count > 0)
                {
                    filteredHistory = filteredHistory.Where(h => query.ActivityTypes.Contains(h.ActivityType));
                }

                if (query.StartTime.HasValue)
                {
                    filteredHistory = filteredHistory.Where(h => h.Timestamp >= query.StartTime.Value);
                }

                if (query.EndTime.HasValue)
                {
                    filteredHistory = filteredHistory.Where(h => h.Timestamp <= query.EndTime.Value);
                }

                // 排序
                filteredHistory = query.SortDirection == SortDirection.Ascending
                    ? filteredHistory.OrderBy(h => h.Timestamp)
                    : filteredHistory.OrderByDescending(h => h.Timestamp);

                // 分页
                filteredHistory = filteredHistory.Skip((query.PageNumber - 1) * query.PageSize).Take(query.PageSize);
            }
            else
            {
                filteredHistory = filteredHistory.OrderByDescending(h => h.Timestamp);
            }

            return Task.FromResult<IReadOnlyList<CollaborationHistoryEntry>>(filteredHistory.ToList());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get collaboration history for session {SessionId}", sessionId);
            return Task.FromResult<IReadOnlyList<CollaborationHistoryEntry>>(new List<CollaborationHistoryEntry>());
        }
    }

    /// <inheritdoc />
    public Task<bool> LogActivityAsync(string sessionId, CollaborationActivity activity, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(sessionId);
        ArgumentNullException.ThrowIfNull(activity);

        try
        {
            if (!_sessions.TryGetValue(sessionId, out var session))
            {
                return Task.FromResult(false);
            }

            if (!session.Settings.RecordHistory)
            {
                return Task.FromResult(true); // 不记录历史，但返回成功
            }

            var historyEntry = new CollaborationHistoryEntry
            {
                EntryId = activity.ActivityId,
                SessionId = sessionId,
                CollaboratorId = activity.CollaboratorId,
                CollaboratorName = activity.CollaboratorName,
                ActivityType = activity.ActivityType,
                Description = activity.Description,
                Operation = activity.Operation,
                Timestamp = activity.Timestamp,
                Data = activity.Data,
                IpAddress = activity.ClientInfo?.IpAddress ?? "",
                UserAgent = activity.ClientInfo?.UserAgent ?? ""
            };

            if (_sessionHistory.TryGetValue(sessionId, out var history))
            {
                history.Add(historyEntry);

                // 清理过期历史记录
                var cutoffDate = DateTime.UtcNow.AddDays(-session.Settings.HistoryRetentionDays);
                var expiredEntries = history.Where(h => h.Timestamp < cutoffDate).ToList();
                foreach (var expired in expiredEntries)
                {
                    history.Remove(expired);
                }
            }

            return Task.FromResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to log activity in session {SessionId}", sessionId);
            return Task.FromResult(false);
        }
    }

    /// <inheritdoc />
    public Task<CollaborationStatistics> GetStatisticsAsync(string sessionId, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(sessionId);

        try
        {
            if (_sessionStatistics.TryGetValue(sessionId, out var stats))
            {
                // 更新实时统计
                if (_sessions.TryGetValue(sessionId, out var session))
                {
                    stats.ActiveCollaborators = session.Collaborators.Count(c => c.Status == CollaboratorStatus.Online);
                    stats.SessionDurationMinutes = (DateTime.UtcNow - session.CreatedAt).TotalMinutes;

                    // 计算协作效率评分（简化算法）
                    if (stats.TotalOperations > 0 && stats.TotalConflicts >= 0)
                    {
                        var conflictRate = (double)stats.TotalConflicts / stats.TotalOperations;
                        var resolutionRate = stats.TotalConflicts > 0 ? (double)stats.ResolvedConflicts / stats.TotalConflicts : 1.0;
                        stats.CollaborationEfficiencyScore = Math.Max(0, Math.Min(100, (1 - conflictRate) * resolutionRate * 100));
                    }
                }

                stats.GeneratedAt = DateTime.UtcNow;
                return Task.FromResult(stats);
            }

            return Task.FromResult(new CollaborationStatistics { SessionId = sessionId });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get statistics for session {SessionId}", sessionId);
            return Task.FromResult(new CollaborationStatistics { SessionId = sessionId });
        }
    }

    #endregion

    #region 权限管理

    /// <inheritdoc />
    public Task<bool> CheckPermissionAsync(string sessionId, string collaboratorId, string permission, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(sessionId);
        ArgumentException.ThrowIfNullOrWhiteSpace(collaboratorId);
        ArgumentException.ThrowIfNullOrWhiteSpace(permission);

        try
        {
            if (!_sessions.TryGetValue(sessionId, out var session))
            {
                return Task.FromResult(false);
            }

            var collaborator = session.Collaborators.FirstOrDefault(c => c.CollaboratorId == collaboratorId);
            if (collaborator == null)
            {
                return Task.FromResult(false);
            }

            // 检查显式权限
            if (collaborator.Permissions.Contains(permission))
            {
                return Task.FromResult(true);
            }

            // 检查角色权限
            var result = collaborator.Role switch
            {
                CollaboratorRole.Owner => true, // 所有者有所有权限
                CollaboratorRole.Admin => permission != "delete_session", // 管理员除了删除会话外有所有权限
                CollaboratorRole.Editor => permission is "edit" or "comment" or "view", // 编辑者可以编辑、评论和查看
                CollaboratorRole.Viewer => permission == "view", // 观察者只能查看
                _ => false
            };
            return Task.FromResult(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to check permission in session {SessionId}", sessionId);
            return Task.FromResult(false);
        }
    }

    /// <inheritdoc />
    public async Task<bool> UpdatePermissionsAsync(string sessionId, string collaboratorId, HashSet<string> permissions, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(sessionId);
        ArgumentException.ThrowIfNullOrWhiteSpace(collaboratorId);
        ArgumentNullException.ThrowIfNull(permissions);

        try
        {
            if (!_sessions.TryGetValue(sessionId, out var session))
            {
                return false;
            }

            var collaborator = session.Collaborators.FirstOrDefault(c => c.CollaboratorId == collaboratorId);
            if (collaborator == null)
            {
                return false;
            }

            var previousPermissions = new HashSet<string>(collaborator.Permissions);
            collaborator.Permissions = permissions;

            // 记录权限变更活动
            await LogActivityAsync(sessionId, new CollaborationActivity
            {
                CollaboratorId = "system", // TODO: 从上下文获取操作者ID
                CollaboratorName = "System",
                ActivityType = CollaborationActivityType.PermissionChange,
                Description = $"Updated permissions for {collaborator.Name}",
                Data = new Dictionary<string, object>
                {
                    { "targetCollaborator", collaboratorId },
                    { "previousPermissions", previousPermissions.ToArray() },
                    { "newPermissions", permissions.ToArray() }
                }
            }, cancellationToken);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update permissions in session {SessionId}", sessionId);
            return false;
        }
    }

    /// <inheritdoc />
    public async Task<bool> UpdateRoleAsync(string sessionId, string collaboratorId, CollaboratorRole role, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(sessionId);
        ArgumentException.ThrowIfNullOrWhiteSpace(collaboratorId);

        try
        {
            if (!_sessions.TryGetValue(sessionId, out var session))
            {
                return false;
            }

            var collaborator = session.Collaborators.FirstOrDefault(c => c.CollaboratorId == collaboratorId);
            if (collaborator == null)
            {
                return false;
            }

            var previousRole = collaborator.Role;
            collaborator.Role = role;

            // 记录角色变更活动
            await LogActivityAsync(sessionId, new CollaborationActivity
            {
                CollaboratorId = "system", // TODO: 从上下文获取操作者ID
                CollaboratorName = "System",
                ActivityType = CollaborationActivityType.RoleChange,
                Description = $"Role changed from {previousRole} to {role} for {collaborator.Name}",
                Data = new Dictionary<string, object>
                {
                    { "targetCollaborator", collaboratorId },
                    { "previousRole", previousRole.ToString() },
                    { "newRole", role.ToString() }
                }
            }, cancellationToken);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update role in session {SessionId}", sessionId);
            return false;
        }
    }

    #endregion

    #region 事件

    /// <inheritdoc />
    public event EventHandler<CollaboratorJoinedEventArgs>? CollaboratorJoined;

    /// <inheritdoc />
    public event EventHandler<CollaboratorLeftEventArgs>? CollaboratorLeft;

    /// <inheritdoc />
    public event EventHandler<DesignOperationEventArgs>? DesignOperationReceived;

    /// <inheritdoc />
    public event EventHandler<ConflictDetectedEventArgs>? ConflictDetected;

    /// <inheritdoc />
    public event EventHandler<ConflictResolvedEventArgs>? ConflictResolved;

    /// <inheritdoc />
    public event EventHandler<SessionStatusChangedEventArgs>? SessionStatusChanged;

    /// <summary>
    /// 触发协作者加入事件
    /// </summary>
    protected virtual void OnCollaboratorJoined(CollaboratorJoinedEventArgs e)
    {
        CollaboratorJoined?.Invoke(this, e);
    }

    /// <summary>
    /// 触发协作者离开事件
    /// </summary>
    protected virtual void OnCollaboratorLeft(CollaboratorLeftEventArgs e)
    {
        CollaboratorLeft?.Invoke(this, e);
    }

    /// <summary>
    /// 触发设计操作事件
    /// </summary>
    protected virtual void OnDesignOperationReceived(DesignOperationEventArgs e)
    {
        DesignOperationReceived?.Invoke(this, e);
    }

    /// <summary>
    /// 触发冲突检测事件
    /// </summary>
    protected virtual void OnConflictDetected(ConflictDetectedEventArgs e)
    {
        ConflictDetected?.Invoke(this, e);
    }

    /// <summary>
    /// 触发冲突解决事件
    /// </summary>
    protected virtual void OnConflictResolved(ConflictResolvedEventArgs e)
    {
        ConflictResolved?.Invoke(this, e);
    }

    /// <summary>
    /// 触发会话状态变更事件
    /// </summary>
    protected virtual void OnSessionStatusChanged(SessionStatusChangedEventArgs e)
    {
        SessionStatusChanged?.Invoke(this, e);
    }

    #endregion

    #region 私有辅助方法

    /// <summary>
    /// 初始化NATS订阅
    /// </summary>
    private async Task InitializeNatsSubscriptionsAsync()
    {
        try
        {
            _logger.LogInformation("Initializing NATS subscriptions for CollaborationService");

            // 确保NATS连接
            if (!_natsService.IsConnected)
            {
                await _natsService.ConnectAsync();
            }

            // 订阅协作消息
            await _natsService.SubscribeAsync<CollaborationMessage>(
                "flowcustom.collaboration.*",
                OnCollaborationMessageReceived);

            _logger.LogInformation("NATS subscriptions initialized successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize NATS subscriptions");
        }
    }

    /// <summary>
    /// 处理协作消息
    /// </summary>
    private async Task OnCollaborationMessageReceived(CollaborationMessage message)
    {
        try
        {
            _logger.LogDebug("Received collaboration message for session {SessionId}", message.SessionId);

            switch (message.Action)
            {
                case CollaborationAction.Join:
                    if (message.Collaborator != null)
                    {
                        await JoinSessionAsync(message.SessionId, message.Collaborator);
                    }
                    break;

                case CollaborationAction.Leave:
                    if (message.Collaborator != null)
                    {
                        await LeaveSessionAsync(message.SessionId, message.Collaborator.CollaboratorId);
                    }
                    break;

                case CollaborationAction.DesignChange:
                    if (message.Operation != null)
                    {
                        await BroadcastOperationAsync(message.SessionId, message.Operation);
                    }
                    break;

                case CollaborationAction.StatusUpdate:
                    if (message.Collaborator != null)
                    {
                        await UpdateCollaboratorStatusAsync(message.SessionId, message.Collaborator.CollaboratorId, message.Collaborator.Status);
                    }
                    break;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to handle collaboration message");
        }
    }

    /// <summary>
    /// 克隆会话对象
    /// </summary>
    private static CollaborationSession CloneSession(CollaborationSession session)
    {
        return new CollaborationSession
        {
            SessionId = session.SessionId,
            WorkflowId = session.WorkflowId,
            Name = session.Name,
            Description = session.Description,
            CreatedBy = session.CreatedBy,
            CreatedAt = session.CreatedAt,
            Status = session.Status,
            MaxCollaborators = session.MaxCollaborators,
            Collaborators = session.Collaborators.Select(c => new CollaboratorInfo
            {
                CollaboratorId = c.CollaboratorId,
                Name = c.Name,
                Email = c.Email,
                AvatarUrl = c.AvatarUrl,
                JoinedAt = c.JoinedAt,
                LastActiveAt = c.LastActiveAt,
                Role = c.Role,
                CurrentSelection = c.CurrentSelection,
                CursorPosition = c.CursorPosition,
                Status = c.Status,
                Permissions = new HashSet<string>(c.Permissions),
                Color = c.Color
            }).ToList(),
            Settings = new CollaborationSettings
            {
                AllowAnonymous = session.Settings.AllowAnonymous,
                EnableRealTimeSync = session.Settings.EnableRealTimeSync,
                EnableConflictDetection = session.Settings.EnableConflictDetection,
                AutoSaveIntervalSeconds = session.Settings.AutoSaveIntervalSeconds,
                CollaboratorTimeoutMinutes = session.Settings.CollaboratorTimeoutMinutes,
                RecordHistory = session.Settings.RecordHistory,
                HistoryRetentionDays = session.Settings.HistoryRetentionDays,
                EnableVoiceChat = session.Settings.EnableVoiceChat,
                EnableTextChat = session.Settings.EnableTextChat
            },
            LastActivityAt = session.LastActivityAt,
            EndedAt = session.EndedAt,
            Metadata = new Dictionary<string, object>(session.Metadata)
        };
    }

    /// <summary>
    /// 生成冲突解决建议
    /// </summary>
    private static List<ConflictResolutionSuggestion> GenerateConflictResolutionSuggestions(DesignConflict conflict)
    {
        var suggestions = new List<ConflictResolutionSuggestion>();

        switch (conflict.ConflictType)
        {
            case ConflictType.ConcurrentEdit:
                suggestions.Add(new ConflictResolutionSuggestion
                {
                    Strategy = ConflictResolutionStrategy.AcceptCurrent,
                    Description = "Keep the current version and discard conflicting changes",
                    Priority = 1,
                    IsAutomatic = false,
                    ExpectedOutcome = "Current version is preserved"
                });

                suggestions.Add(new ConflictResolutionSuggestion
                {
                    Strategy = ConflictResolutionStrategy.AcceptIncoming,
                    Description = "Accept the incoming changes and overwrite current version",
                    Priority = 2,
                    IsAutomatic = false,
                    ExpectedOutcome = "Incoming changes are applied"
                });

                suggestions.Add(new ConflictResolutionSuggestion
                {
                    Strategy = ConflictResolutionStrategy.ManualMerge,
                    Description = "Manually merge the conflicting changes",
                    Priority = 3,
                    IsAutomatic = false,
                    ExpectedOutcome = "Best of both versions combined"
                });
                break;

            case ConflictType.DeleteWhileEditing:
                suggestions.Add(new ConflictResolutionSuggestion
                {
                    Strategy = ConflictResolutionStrategy.AcceptCurrent,
                    Description = "Cancel the delete operation and keep editing",
                    Priority = 1,
                    IsAutomatic = false,
                    ExpectedOutcome = "Object remains and can be edited"
                });
                break;

            default:
                suggestions.Add(new ConflictResolutionSuggestion
                {
                    Strategy = ConflictResolutionStrategy.ManualMerge,
                    Description = "Manually resolve the conflict",
                    Priority = 1,
                    IsAutomatic = false,
                    ExpectedOutcome = "Conflict resolved based on user decision"
                });
                break;
        }

        return suggestions;
    }

    /// <summary>
    /// 清理过期会话
    /// </summary>
    private void CleanupExpiredSessions(object? state)
    {
        try
        {
            var expiredSessions = new List<string>();
            var cutoffTime = DateTime.UtcNow.AddMinutes(-30); // 30分钟无活动视为过期

            foreach (var kvp in _sessions)
            {
                var session = kvp.Value;
                if (session.Status == SessionStatus.Active && session.LastActivityAt < cutoffTime)
                {
                    expiredSessions.Add(kvp.Key);
                }
            }

            foreach (var sessionId in expiredSessions)
            {
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await EndSessionAsync(sessionId, "Session expired due to inactivity");
                        _logger.LogInformation("Expired session {SessionId} cleaned up", sessionId);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Failed to cleanup expired session {SessionId}", sessionId);
                    }
                });
            }

            if (expiredSessions.Count > 0)
            {
                _logger.LogInformation("Cleaned up {Count} expired sessions", expiredSessions.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to cleanup expired sessions");
        }
    }

    #endregion

    #region IDisposable实现

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed && disposing)
        {
            try
            {
                // 停止清理定时器
                _cleanupTimer?.Dispose();

                // 结束所有活跃会话
                var activeSessions = _sessions.Values.Where(s => s.Status == SessionStatus.Active).ToList();
                foreach (var session in activeSessions)
                {
                    try
                    {
                        _ = EndSessionAsync(session.SessionId, "Service shutting down");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Failed to end session {SessionId} during disposal", session.SessionId);
                    }
                }

                // 清理存储
                _sessions.Clear();
                _sessionHistory.Clear();
                _sessionConflicts.Clear();
                _sessionStatistics.Clear();
                _lastActivity.Clear();

                _logger.LogInformation("CollaborationService disposed successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while disposing CollaborationService");
            }
            finally
            {
                _disposed = true;
            }
        }
    }

    /// <summary>
    /// 析构函数
    /// </summary>
    ~CollaborationService()
    {
        Dispose(false);
    }

    #endregion
}
