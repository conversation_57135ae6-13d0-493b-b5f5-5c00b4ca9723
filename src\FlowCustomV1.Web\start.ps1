# FlowCustomV1 Web Frontend 启动脚本
# PowerShell 脚本用于 Windows 环境

Write-Host "=== FlowCustomV1 Web Frontend 启动脚本 ===" -ForegroundColor Green
Write-Host "版本: v0.0.1.11" -ForegroundColor Cyan
Write-Host ""

# 检查 Node.js 版本
Write-Host "检查 Node.js 环境..." -ForegroundColor Yellow
try {
    $nodeVersion = node --version
    Write-Host "Node.js 版本: $nodeVersion" -ForegroundColor Green
    
    # 检查版本是否满足要求 (>= 16.0.0)
    $versionNumber = $nodeVersion -replace 'v', ''
    $majorVersion = [int]($versionNumber.Split('.')[0])
    
    if ($majorVersion -lt 16) {
        Write-Host "错误: Node.js 版本过低，需要 >= 16.0.0" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "错误: 未找到 Node.js，请先安装 Node.js >= 16.0.0" -ForegroundColor Red
    exit 1
}

# 检查 npm 版本
try {
    $npmVersion = npm --version
    Write-Host "npm 版本: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "错误: 未找到 npm" -ForegroundColor Red
    exit 1
}

Write-Host ""

# 检查是否存在 node_modules
if (!(Test-Path "node_modules")) {
    Write-Host "未找到 node_modules，开始安装依赖..." -ForegroundColor Yellow
    Write-Host "这可能需要几分钟时间，请耐心等待..." -ForegroundColor Cyan
    
    npm install
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "依赖安装失败！" -ForegroundColor Red
        exit 1
    }
    
    Write-Host "依赖安装完成！" -ForegroundColor Green
} else {
    Write-Host "检查依赖更新..." -ForegroundColor Yellow
    npm install
}

Write-Host ""

# 检查后端 API 连接
Write-Host "检查后端 API 连接..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:5000/api/health" -Method GET -TimeoutSec 5 -ErrorAction Stop
    Write-Host "后端 API 连接正常" -ForegroundColor Green
} catch {
    Write-Host "警告: 无法连接到后端 API (http://localhost:5000)" -ForegroundColor Yellow
    Write-Host "请确保后端服务已启动，或检查 vite.config.ts 中的代理配置" -ForegroundColor Yellow
}

Write-Host ""

# 启动开发服务器
Write-Host "启动开发服务器..." -ForegroundColor Green
Write-Host "访问地址: http://localhost:3000" -ForegroundColor Cyan
Write-Host "按 Ctrl+C 停止服务器" -ForegroundColor Yellow
Write-Host ""

npm run dev
