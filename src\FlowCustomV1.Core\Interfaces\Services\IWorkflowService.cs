using FlowCustomV1.Core.Models.Workflow;
using FlowCustomV1.Core.Models.Common;
using FlowCustomV1.Core.Models.Cluster;

namespace FlowCustomV1.Core.Interfaces.Services;

/// <summary>
/// 工作流服务接口
/// 提供工作流的业务逻辑操作
/// </summary>
public interface IWorkflowService
{
    #region 工作流管理

    /// <summary>
    /// 创建工作流
    /// </summary>
    /// <param name="workflowDefinition">工作流定义</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>创建结果</returns>
    Task<string> CreateWorkflowAsync(WorkflowDefinition workflowDefinition, CancellationToken cancellationToken = default);

    /// <summary>
    /// 更新工作流
    /// </summary>
    /// <param name="workflowDefinition">工作流定义</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>更新结果</returns>
    Task<bool> UpdateWorkflowAsync(WorkflowDefinition workflowDefinition, CancellationToken cancellationToken = default);

    /// <summary>
    /// 删除工作流
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>删除结果</returns>
    Task<bool> DeleteWorkflowAsync(string workflowId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取工作流
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>工作流定义</returns>
    Task<WorkflowDefinition?> GetWorkflowAsync(string workflowId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取工作流列表
    /// </summary>
    /// <param name="pageIndex">页索引</param>
    /// <param name="pageSize">页大小</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>工作流列表</returns>
    Task<PagedResult<WorkflowDefinition>> GetWorkflowsAsync(int pageIndex = 0, int pageSize = 20, CancellationToken cancellationToken = default);

    /// <summary>
    /// 搜索工作流
    /// </summary>
    /// <param name="searchTerm">搜索词</param>
    /// <param name="pageIndex">页索引</param>
    /// <param name="pageSize">页大小</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>工作流列表</returns>
    Task<PagedResult<WorkflowDefinition>> SearchWorkflowsAsync(string searchTerm, int pageIndex = 0, int pageSize = 20, CancellationToken cancellationToken = default);

    #endregion

    #region 工作流执行

    /// <summary>
    /// 执行工作流
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="parameters">执行参数</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>执行结果</returns>
    Task<WorkflowExecutionResult> ExecuteWorkflowAsync(string workflowId, Dictionary<string, object>? parameters = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 停止工作流执行
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>停止结果</returns>
    Task<bool> StopWorkflowExecutionAsync(string executionId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 暂停工作流执行
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>暂停结果</returns>
    Task<bool> PauseWorkflowExecutionAsync(string executionId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 恢复工作流执行
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>恢复结果</returns>
    Task<bool> ResumeWorkflowExecutionAsync(string executionId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取工作流执行状态
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>执行状态</returns>
    Task<WorkflowExecutionStatus?> GetExecutionStatusAsync(string executionId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取工作流执行历史
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="pageIndex">页索引</param>
    /// <param name="pageSize">页大小</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>执行历史</returns>
    Task<PagedResult<WorkflowExecutionResult>> GetExecutionHistoryAsync(string workflowId, int pageIndex = 0, int pageSize = 20, CancellationToken cancellationToken = default);

    #endregion

    #region 工作流验证

    /// <summary>
    /// 验证工作流定义
    /// </summary>
    /// <param name="workflowDefinition">工作流定义</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    Task<WorkflowValidationResult> ValidateWorkflowAsync(WorkflowDefinition workflowDefinition, CancellationToken cancellationToken = default);

    /// <summary>
    /// 检查工作流是否可以执行
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>检查结果</returns>
    Task<bool> CanExecuteWorkflowAsync(string workflowId, CancellationToken cancellationToken = default);

    #endregion

    #region 工作流版本管理

    /// <summary>
    /// 创建工作流版本
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="version">版本号</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>版本创建结果</returns>
    Task<bool> CreateWorkflowVersionAsync(string workflowId, string version, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取工作流版本列表
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>版本列表</returns>
    Task<IReadOnlyList<string>> GetWorkflowVersionsAsync(string workflowId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取指定版本的工作流
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="version">版本号</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>工作流定义</returns>
    Task<WorkflowDefinition?> GetWorkflowVersionAsync(string workflowId, string version, CancellationToken cancellationToken = default);

    #endregion
}
