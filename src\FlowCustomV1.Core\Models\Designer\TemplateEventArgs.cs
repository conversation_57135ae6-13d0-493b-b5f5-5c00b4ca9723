using System.Text.Json.Serialization;

namespace FlowCustomV1.Core.Models.Designer;

/// <summary>
/// 模板创建事件参数
/// </summary>
public class TemplateCreatedEventArgs : EventArgs
{
    /// <summary>
    /// 模板信息
    /// </summary>
    [JsonPropertyName("template")]
    public WorkflowTemplate Template { get; set; } = new();

    /// <summary>
    /// 创建者ID
    /// </summary>
    [JsonPropertyName("createdBy")]
    public string CreatedBy { get; set; } = string.Empty;

    /// <summary>
    /// 创建时间
    /// </summary>
    [JsonPropertyName("createdAt")]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 是否为系统内置模板
    /// </summary>
    [JsonPropertyName("isBuiltIn")]
    public bool IsBuiltIn { get; set; } = false;
}

/// <summary>
/// 模板更新事件参数
/// </summary>
public class TemplateUpdatedEventArgs : EventArgs
{
    /// <summary>
    /// 模板ID
    /// </summary>
    [JsonPropertyName("templateId")]
    public string TemplateId { get; set; } = string.Empty;

    /// <summary>
    /// 更新后的模板信息
    /// </summary>
    [JsonPropertyName("template")]
    public WorkflowTemplate Template { get; set; } = new();

    /// <summary>
    /// 更新前的模板信息
    /// </summary>
    [JsonPropertyName("previousTemplate")]
    public WorkflowTemplate? PreviousTemplate { get; set; }

    /// <summary>
    /// 更新者ID
    /// </summary>
    [JsonPropertyName("updatedBy")]
    public string UpdatedBy { get; set; } = string.Empty;

    /// <summary>
    /// 更新时间
    /// </summary>
    [JsonPropertyName("updatedAt")]
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 变更摘要
    /// </summary>
    [JsonPropertyName("changes")]
    public List<string> Changes { get; set; } = new();
}

/// <summary>
/// 模板删除事件参数
/// </summary>
public class TemplateDeletedEventArgs : EventArgs
{
    /// <summary>
    /// 模板ID
    /// </summary>
    [JsonPropertyName("templateId")]
    public string TemplateId { get; set; } = string.Empty;

    /// <summary>
    /// 删除前的模板信息
    /// </summary>
    [JsonPropertyName("deletedTemplate")]
    public WorkflowTemplate DeletedTemplate { get; set; } = new();

    /// <summary>
    /// 删除者ID
    /// </summary>
    [JsonPropertyName("deletedBy")]
    public string DeletedBy { get; set; } = string.Empty;

    /// <summary>
    /// 删除时间
    /// </summary>
    [JsonPropertyName("deletedAt")]
    public DateTime DeletedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 删除原因
    /// </summary>
    [JsonPropertyName("reason")]
    public string Reason { get; set; } = string.Empty;

    /// <summary>
    /// 是否为软删除
    /// </summary>
    [JsonPropertyName("isSoftDelete")]
    public bool IsSoftDelete { get; set; } = true;
}

/// <summary>
/// 模板发布事件参数
/// </summary>
public class TemplatePublishedEventArgs : EventArgs
{
    /// <summary>
    /// 模板ID
    /// </summary>
    [JsonPropertyName("templateId")]
    public string TemplateId { get; set; } = string.Empty;

    /// <summary>
    /// 模板信息
    /// </summary>
    [JsonPropertyName("template")]
    public WorkflowTemplate Template { get; set; } = new();

    /// <summary>
    /// 发布者ID
    /// </summary>
    [JsonPropertyName("publishedBy")]
    public string PublishedBy { get; set; } = string.Empty;

    /// <summary>
    /// 发布时间
    /// </summary>
    [JsonPropertyName("publishedAt")]
    public DateTime PublishedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 发布类型
    /// </summary>
    [JsonPropertyName("publishType")]
    public TemplatePublishType PublishType { get; set; } = TemplatePublishType.Public;

    /// <summary>
    /// 发布说明
    /// </summary>
    [JsonPropertyName("publishNotes")]
    public string PublishNotes { get; set; } = string.Empty;
}

/// <summary>
/// 模板使用事件参数
/// </summary>
public class TemplateUsedEventArgs : EventArgs
{
    /// <summary>
    /// 模板ID
    /// </summary>
    [JsonPropertyName("templateId")]
    public string TemplateId { get; set; } = string.Empty;

    /// <summary>
    /// 模板名称
    /// </summary>
    [JsonPropertyName("templateName")]
    public string TemplateName { get; set; } = string.Empty;

    /// <summary>
    /// 使用者ID
    /// </summary>
    [JsonPropertyName("userId")]
    public string UserId { get; set; } = string.Empty;

    /// <summary>
    /// 使用者名称
    /// </summary>
    [JsonPropertyName("userName")]
    public string UserName { get; set; } = string.Empty;

    /// <summary>
    /// 使用时间
    /// </summary>
    [JsonPropertyName("usedAt")]
    public DateTime UsedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 创建的工作流ID
    /// </summary>
    [JsonPropertyName("createdWorkflowId")]
    public string CreatedWorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 使用上下文信息
    /// </summary>
    [JsonPropertyName("context")]
    public Dictionary<string, object> Context { get; set; } = new();
}

/// <summary>
/// 模板版本创建事件参数
/// </summary>
public class TemplateVersionCreatedEventArgs : EventArgs
{
    /// <summary>
    /// 模板ID
    /// </summary>
    [JsonPropertyName("templateId")]
    public string TemplateId { get; set; } = string.Empty;

    /// <summary>
    /// 版本信息
    /// </summary>
    [JsonPropertyName("version")]
    public TemplateVersion Version { get; set; } = new();

    /// <summary>
    /// 创建者ID
    /// </summary>
    [JsonPropertyName("createdBy")]
    public string CreatedBy { get; set; } = string.Empty;

    /// <summary>
    /// 创建时间
    /// </summary>
    [JsonPropertyName("createdAt")]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 是否为自动创建
    /// </summary>
    [JsonPropertyName("isAutoCreated")]
    public bool IsAutoCreated { get; set; } = false;
}

/// <summary>
/// 模板评分事件参数
/// </summary>
public class TemplateRatedEventArgs : EventArgs
{
    /// <summary>
    /// 模板ID
    /// </summary>
    [JsonPropertyName("templateId")]
    public string TemplateId { get; set; } = string.Empty;

    /// <summary>
    /// 评分者ID
    /// </summary>
    [JsonPropertyName("userId")]
    public string UserId { get; set; } = string.Empty;

    /// <summary>
    /// 评分（1-5）
    /// </summary>
    [JsonPropertyName("rating")]
    public int Rating { get; set; } = 0;

    /// <summary>
    /// 评价内容
    /// </summary>
    [JsonPropertyName("comment")]
    public string Comment { get; set; } = string.Empty;

    /// <summary>
    /// 评分时间
    /// </summary>
    [JsonPropertyName("ratedAt")]
    public DateTime RatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 之前的评分（如果是更新评分）
    /// </summary>
    [JsonPropertyName("previousRating")]
    public int? PreviousRating { get; set; }
}

/// <summary>
/// 模板发布类型枚举
/// </summary>
public enum TemplatePublishType
{
    /// <summary>
    /// 公共发布
    /// </summary>
    Public,

    /// <summary>
    /// 组织内发布
    /// </summary>
    Organization,

    /// <summary>
    /// 团队内发布
    /// </summary>
    Team,

    /// <summary>
    /// 私有发布
    /// </summary>
    Private
}
