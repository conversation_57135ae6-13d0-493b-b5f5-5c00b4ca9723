using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FlowCustomV1.Core.Interfaces;
using FlowCustomV1.Core.Interfaces.Repositories;
using FlowCustomV1.Core.Models.Cluster;
using FlowCustomV1.Core.Models.Workflow;
using FlowCustomV1.Engine.Context;
using FlowCustomV1.Engine.ErrorHandling;
using FlowCustomV1.Engine.Executors;
using FlowCustomV1.Engine.Schedulers;
using FlowCustomV1.Engine.StateTracking;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace FlowCustomV1.Engine.Services;

/// <summary>
/// 工作流引擎服务实现
/// 负责协调整个工作流的执行过程
/// </summary>
public class WorkflowEngineService : IWorkflowEngine
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<WorkflowEngineService> _logger;
    private readonly INodeScheduler _nodeScheduler;
    private readonly IExecutionStateTracker _stateTracker;
    private readonly IErrorHandler _errorHandler;
    private readonly SystemExecutionContext _systemContext;
    private readonly IExecutionRepository? _executionRepository;
    private readonly IWorkflowRepository? _workflowRepository;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="serviceProvider">服务提供者</param>
    /// <param name="logger">日志记录器</param>
    /// <param name="nodeScheduler">节点调度器</param>
    /// <param name="stateTracker">状态跟踪器</param>
    /// <param name="errorHandler">错误处理器</param>
    /// <param name="executionRepository">执行仓储（可选）</param>
    /// <param name="workflowRepository">工作流仓储（可选）</param>
    public WorkflowEngineService(
        IServiceProvider serviceProvider,
        ILogger<WorkflowEngineService> logger,
        INodeScheduler nodeScheduler,
        IExecutionStateTracker stateTracker,
        IErrorHandler errorHandler,
        IExecutionRepository? executionRepository = null,
        IWorkflowRepository? workflowRepository = null)
    {
        _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _nodeScheduler = nodeScheduler ?? throw new ArgumentNullException(nameof(nodeScheduler));
        _stateTracker = stateTracker ?? throw new ArgumentNullException(nameof(stateTracker));
        _errorHandler = errorHandler ?? throw new ArgumentNullException(nameof(errorHandler));

        // 直接注入可选的数据持久化服务
        _executionRepository = executionRepository;
        _workflowRepository = workflowRepository;

        // 创建系统执行上下文
        _systemContext = new SystemExecutionContext
        {
            ServiceProvider = _serviceProvider,
            SystemStartTime = DateTime.UtcNow
        };

        // 订阅调度器事件
        _nodeScheduler.NodeCompleted += OnNodeCompleted;
        _nodeScheduler.NodeExecutionError += OnNodeExecutionError;

        var persistenceStatus = _executionRepository != null ? "enabled" : "disabled";
        _logger.LogInformation("WorkflowEngineService initialized with persistence {PersistenceStatus}", persistenceStatus);
    }

    /// <summary>
    /// 启动工作流引擎
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>启动任务</returns>
    public async Task StartAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting workflow engine");
            
            // 启动节点调度器
            await _nodeScheduler.StartAsync(cancellationToken).ConfigureAwait(false);

            _logger.LogInformation("Workflow engine started successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start workflow engine");
            throw;
        }
    }

    /// <summary>
    /// 停止工作流引擎
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>停止任务</returns>
    public async Task StopAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Stopping workflow engine");

            // 停止节点调度器
            await _nodeScheduler.StopAsync(cancellationToken).ConfigureAwait(false);
            
            _logger.LogInformation("Workflow engine stopped successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to stop workflow engine");
            throw;
        }
    }

    /// <summary>
    /// 执行工作流
    /// </summary>
    /// <param name="workflowDefinition">工作流定义</param>
    /// <param name="inputData">输入数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>执行结果</returns>
    public async Task<WorkflowExecutionResult> ExecuteWorkflowAsync(
        WorkflowDefinition workflowDefinition, 
        Dictionary<string, object>? inputData = null, 
        CancellationToken cancellationToken = default)
    {
        if (workflowDefinition == null)
            throw new ArgumentNullException(nameof(workflowDefinition));

        var executionId = Guid.NewGuid().ToString();
        _logger.LogInformation("Starting workflow execution: {WorkflowId} ({ExecutionId})", 
            workflowDefinition.WorkflowId, executionId);

        try
        {
            // 创建工作流执行上下文
            var workflowContext = await CreateWorkflowContextAsync(
                workflowDefinition, inputData, executionId, cancellationToken);

            // 跟踪工作流状态
            await _stateTracker.TrackWorkflowStateAsync(
                executionId, WorkflowExecutionState.Running, "Workflow execution started",
                cancellationToken: cancellationToken);

            // 保存工作流实例到数据库
            if (_executionRepository != null)
            {
                var initialResult = new WorkflowExecutionResult
                {
                    ExecutionId = executionId,
                    WorkflowId = workflowDefinition.WorkflowId,
                    State = WorkflowExecutionState.Running,
                    IsSuccess = false,
                    StartedAt = DateTime.UtcNow,
                    OutputData = new Dictionary<string, object>(),
                    NodeResults = new List<NodeExecutionResult>(),
                    Stats = new WorkflowExecutionStats(),
                    Metadata = new Dictionary<string, object>()
                };

                await _executionRepository.SaveWorkflowInstanceAsync(initialResult, inputData, cancellationToken);
                _logger.LogDebug("Workflow instance saved to database: {ExecutionId}", executionId);
            }

            // 在后台启动工作流执行，不阻塞当前线程
            _ = ExecuteWorkflowInternalAsync(workflowContext, cancellationToken);

            // 立即返回，表示工作流已成功启动
            return new WorkflowExecutionResult
            {
                ExecutionId = executionId,
                WorkflowId = workflowDefinition.WorkflowId,
                State = WorkflowExecutionState.Running,
                IsSuccess = true,
                StartedAt = DateTime.UtcNow,
                Message = "Workflow execution started successfully."
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Workflow execution failed: {WorkflowId} ({ExecutionId})", 
                workflowDefinition.WorkflowId, executionId);

            // 跟踪失败状态
            await _stateTracker.TrackWorkflowStateAsync(
                executionId, WorkflowExecutionState.Failed, $"Workflow execution failed: {ex.Message}", 
                cancellationToken: cancellationToken);

            // 处理工作流级别的错误
            var workflowContext = new EngineWorkflowContext
            {
                ExecutionId = executionId,
                WorkflowId = workflowDefinition.WorkflowId,
                Definition = workflowDefinition,
                SystemContext = _systemContext
            };

            await _errorHandler.HandleWorkflowErrorAsync(workflowContext, ex, cancellationToken);

            return new WorkflowExecutionResult
            {
                ExecutionId = executionId,
                WorkflowId = workflowDefinition.WorkflowId,
                State = WorkflowExecutionState.Failed,
                IsSuccess = false,
                StartedAt = DateTime.UtcNow,
                CompletedAt = DateTime.UtcNow,
                OutputData = new Dictionary<string, object>(),
                NodeResults = new List<NodeExecutionResult>(),
                Stats = new WorkflowExecutionStats(),
                Metadata = new Dictionary<string, object>
                {
                    ["Error"] = ex.Message,
                    ["StackTrace"] = ex.StackTrace ?? string.Empty
                }
            };
        }
    }

    /// <summary>
    /// 暂停工作流执行
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>暂停任务</returns>
    public async Task PauseWorkflowAsync(string executionId, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(executionId))
            throw new ArgumentException("ExecutionId cannot be null or empty", nameof(executionId));

        _logger.LogInformation("Pausing workflow execution: {ExecutionId}", executionId);

        try
        {
            // 通过调度器暂停工作流执行
            await _nodeScheduler.PauseAsync(cancellationToken).ConfigureAwait(false);

            // 更新状态跟踪
            await _stateTracker.TrackWorkflowStateAsync(
                executionId,
                WorkflowExecutionState.Paused,
                "Workflow execution paused by user request",
                cancellationToken: cancellationToken).ConfigureAwait(false);

            _logger.LogInformation("Workflow execution paused: {ExecutionId}", executionId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to pause workflow execution: {ExecutionId}", executionId);
            throw;
        }
    }

    /// <summary>
    /// 取消工作流执行
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="reason">取消原因</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>取消任务</returns>
    public async Task CancelWorkflowAsync(string executionId, string? reason = null, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(executionId))
            throw new ArgumentException("ExecutionId cannot be null or empty", nameof(executionId));

        _logger.LogInformation("Cancelling workflow execution: {ExecutionId}, Reason: {Reason}", executionId, reason ?? "No reason provided");

        try
        {
            // 取消调度器中的相关执行
            await _nodeScheduler.CancelWorkflowExecutionAsync(executionId, cancellationToken);

            // 跟踪取消状态
            await _stateTracker.TrackWorkflowStateAsync(
                executionId, WorkflowExecutionState.Cancelled, "Workflow execution cancelled", 
                cancellationToken: cancellationToken);

            _logger.LogInformation("Workflow execution cancelled: {ExecutionId}", executionId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to cancel workflow execution: {ExecutionId}", executionId);
            throw;
        }
    }

    /// <summary>
    /// 获取工作流执行状态
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>执行状态</returns>
    public async Task<WorkflowExecutionStatus?> GetExecutionStatusAsync(
        string executionId,
        CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(executionId))
            throw new ArgumentException("ExecutionId cannot be null or empty", nameof(executionId));

        try
        {
            var state = await _stateTracker.GetWorkflowStateAsync(executionId, cancellationToken).ConfigureAwait(false);
            var history = await _stateTracker.GetWorkflowHistoryAsync(executionId, cancellationToken).ConfigureAwait(false);

            if (state == WorkflowExecutionState.NotStarted)
                return null;

            var latestEntry = history.OrderByDescending(h => h.ChangedAt).FirstOrDefault();

            return new WorkflowExecutionStatus
            {
                ExecutionId = executionId,
                WorkflowId = latestEntry?.WorkflowId ?? "unknown",
                State = state,
                StartedAt = history.Where(h => h.State == WorkflowExecutionState.Running)
                                  .OrderBy(h => h.ChangedAt)
                                  .FirstOrDefault()?.ChangedAt ?? DateTime.UtcNow,
                CompletedAt = state.IsCompleted() ? latestEntry?.ChangedAt : null,
                LastUpdatedAt = latestEntry?.ChangedAt ?? DateTime.UtcNow,
                NodeStates = new Dictionary<string, NodeExecutionState>(),
                Metadata = new Dictionary<string, object>
                {
                    ["StateHistoryCount"] = history.Count,
                    ["LastStateChange"] = latestEntry?.Reason ?? "Unknown"
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get execution status for: {ExecutionId}", executionId);
            return null;
        }
    }

    /// <summary>
    /// 获取工作流执行历史
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>执行历史</returns>
    public async Task<List<WorkflowStateHistoryEntry>> GetWorkflowHistoryAsync(
        string executionId, 
        CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(executionId))
            throw new ArgumentException("ExecutionId cannot be null or empty", nameof(executionId));

        return await _stateTracker.GetWorkflowStateHistoryAsync(executionId, cancellationToken);
    }

    /// <summary>
    /// 创建工作流执行上下文
    /// </summary>
    /// <param name="workflowDefinition">工作流定义</param>
    /// <param name="inputData">输入数据</param>
    /// <param name="executionId">执行ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>工作流执行上下文</returns>
    private async Task<EngineWorkflowContext> CreateWorkflowContextAsync(
        WorkflowDefinition workflowDefinition,
        Dictionary<string, object>? inputData,
        string executionId,
        CancellationToken cancellationToken)
    {
        var context = new EngineWorkflowContext
        {
            ExecutionId = executionId,
            WorkflowId = workflowDefinition.WorkflowId,
            Definition = workflowDefinition.Clone(),
            State = WorkflowExecutionState.NotStarted,
            SystemContext = _systemContext,
            WorkflowCancellationToken = cancellationToken,
            InputData = new ConcurrentDictionary<string, object>(inputData ?? new Dictionary<string, object>()),
            StartedAt = DateTime.UtcNow
        };

        // 初始化工作流数据
        foreach (var kvp in context.InputData)
        {
            context.WorkflowData[kvp.Key] = kvp.Value;
        }

        // 初始化节点状态
        foreach (var node in workflowDefinition.Nodes)
        {
            context.NodeStates[node.NodeId] = NodeExecutionState.NotStarted;
        }

        _logger.LogDebug("Workflow context created: {ExecutionId} with {NodeCount} nodes",
            executionId, workflowDefinition.Nodes.Count);

        return await Task.FromResult(context);
    }

    /// <summary>
    /// 内部工作流执行逻辑
    /// </summary>
    /// <param name="workflowContext">工作流执行上下文</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>执行任务</returns>
    private async Task ExecuteWorkflowInternalAsync(
        EngineWorkflowContext workflowContext,
        CancellationToken cancellationToken)
    {
        _logger.LogDebug("Starting internal workflow execution: {ExecutionId}", workflowContext.ExecutionId);

        try
        {
            // 查找起始节点
            var startNodes = FindStartNodes(workflowContext.Definition);
            if (startNodes.Count == 0)
            {
                throw new InvalidOperationException("No start nodes found in workflow definition");
            }

            _logger.LogDebug("Found {StartNodeCount} start nodes", startNodes.Count);

            // 调度起始节点执行
            foreach (var startNode in startNodes)
            {
                await ScheduleNodeExecutionAsync(workflowContext, startNode, cancellationToken);
            }

            // 等待工作流完成
            await WaitForWorkflowCompletionAsync(workflowContext, cancellationToken);

            _logger.LogDebug("Internal workflow execution completed: {ExecutionId}", workflowContext.ExecutionId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Internal workflow execution failed: {ExecutionId}", workflowContext.ExecutionId);
            workflowContext.State = WorkflowExecutionState.Failed;
            workflowContext.CompletedAt = DateTime.UtcNow;
            throw;
        }
    }

    /// <summary>
    /// 查找起始节点
    /// </summary>
    /// <param name="workflowDefinition">工作流定义</param>
    /// <returns>起始节点列表</returns>
    private List<WorkflowNode> FindStartNodes(WorkflowDefinition workflowDefinition)
    {
        // 查找没有输入连接的节点，或者明确标记为Start类型的节点
        var nodesWithInputs = workflowDefinition.Connections
            .Select(c => c.TargetNodeId)
            .ToHashSet();

        var startNodes = workflowDefinition.Nodes
            .Where(n => n.NodeType.Equals("Start", StringComparison.OrdinalIgnoreCase) ||
                       !nodesWithInputs.Contains(n.NodeId))
            .ToList();

        return startNodes;
    }

    /// <summary>
    /// 调度节点执行
    /// </summary>
    /// <param name="workflowContext">工作流执行上下文</param>
    /// <param name="node">要执行的节点</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>调度任务</returns>
    private async Task ScheduleNodeExecutionAsync(
        EngineWorkflowContext workflowContext,
        WorkflowNode node,
        CancellationToken cancellationToken)
    {
        _logger.LogDebug("Scheduling node execution: {NodeId} ({NodeType})", node.NodeId, node.NodeType);

        try
        {
            // 创建增强节点执行上下文
            var nodeContext = CreateNodeExecutionContext(workflowContext, node);

            // 创建调度请求
            var scheduleRequest = new NodeScheduleRequest
            {
                NodeContext = nodeContext,
                Priority = node.Priority,
                CreatedAt = DateTime.UtcNow
            };

            // 提交到调度器
            await _nodeScheduler.ScheduleNodeAsync(scheduleRequest, cancellationToken);

            _logger.LogDebug("Node scheduled successfully: {NodeId}", node.NodeId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to schedule node execution: {NodeId}", node.NodeId);

            // 标记节点为失败
            workflowContext.UpdateNodeState(node.NodeId, NodeExecutionState.Failed);

            // 跟踪节点状态
            await _stateTracker.TrackNodeStateAsync(
                workflowContext.ExecutionId, node.NodeId, NodeExecutionState.Failed,
                $"Failed to schedule: {ex.Message}", cancellationToken: cancellationToken);
        }
    }

    /// <summary>
    /// 创建节点执行上下文
    /// </summary>
    /// <param name="workflowContext">工作流执行上下文</param>
    /// <param name="node">工作流节点</param>
    /// <returns>节点执行上下文</returns>
    private NodeExecutionContext CreateNodeExecutionContext(
        EngineWorkflowContext workflowContext,
        WorkflowNode node)
    {
        var nodeContext = new NodeExecutionContext
        {
            ExecutionId = workflowContext.ExecutionId,
            WorkflowId = workflowContext.WorkflowId,
            NodeId = node.NodeId,
            NodeType = node.NodeType,
            Configuration = node.Configuration.Clone(),
            CancellationToken = workflowContext.WorkflowCancellationToken,
            StartedAt = DateTime.UtcNow,
            WorkflowExecutionContext = workflowContext, // 设置完整的工作流执行上下文
            EnableDetailedLogging = true
        };

        // 从工作流数据中复制相关的输入数据
        foreach (var kvp in workflowContext.WorkflowData)
        {
            nodeContext.InputData[kvp.Key] = kvp.Value;
        }

        return nodeContext;
    }

    /// <summary>
    /// 等待工作流完成
    /// </summary>
    /// <param name="workflowContext">工作流执行上下文</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>等待任务</returns>
    private async Task WaitForWorkflowCompletionAsync(
        EngineWorkflowContext workflowContext,
        CancellationToken cancellationToken)
    {
        _logger.LogDebug("Waiting for workflow completion: {ExecutionId}", workflowContext.ExecutionId);

        var timeout = TimeSpan.FromMinutes(30); // 默认30分钟超时
        var startTime = DateTime.UtcNow;
        var checkInterval = TimeSpan.FromSeconds(1);

        while (!cancellationToken.IsCancellationRequested &&
               DateTime.UtcNow - startTime < timeout)
        {
            // 检查工作流是否完成
            if (IsWorkflowCompleted(workflowContext))
            {
                _logger.LogDebug("Workflow completed: {ExecutionId} with state: {State}",
                    workflowContext.ExecutionId, workflowContext.State);

                // 注意：不要在这里强制设置状态，IsWorkflowCompleted方法已经正确设置了状态
                // workflowContext.State 和 workflowContext.CompletedAt 已经在IsWorkflowCompleted中设置

                // 更新数据库中的工作流实例状态
                if (_executionRepository != null)
                {
                    try
                    {
                        var finalResult = CreateWorkflowExecutionResult(workflowContext);
                        await _executionRepository.UpdateWorkflowInstanceAsync(finalResult, cancellationToken);
                        _logger.LogDebug("Workflow instance status updated in database: {ExecutionId} with state: {State}",
                            workflowContext.ExecutionId, workflowContext.State);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Failed to update workflow instance status in database: {ExecutionId}", workflowContext.ExecutionId);
                    }
                }

                return;
            }

            // 等待一段时间后再检查
            await Task.Delay(checkInterval, cancellationToken);
        }

        // 超时或取消
        if (cancellationToken.IsCancellationRequested)
        {
            workflowContext.State = WorkflowExecutionState.Cancelled;
            _logger.LogInformation("Workflow execution cancelled: {ExecutionId}", workflowContext.ExecutionId);
        }
        else
        {
            workflowContext.State = WorkflowExecutionState.Failed;
            _logger.LogWarning("Workflow execution timed out: {ExecutionId}", workflowContext.ExecutionId);
        }

        workflowContext.CompletedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// 检查工作流是否完成
    /// </summary>
    /// <param name="workflowContext">工作流执行上下文</param>
    /// <returns>是否完成</returns>
    private bool IsWorkflowCompleted(EngineWorkflowContext workflowContext)
    {
        // 如果工作流已经标记为完成状态，直接返回
        if (workflowContext.IsCompleted)
        {
            return true;
        }

        // 检查是否所有节点都已执行完成
        var totalNodes = workflowContext.Definition.Nodes.Count;
        var completedOrFailedNodes = workflowContext.NodeStates.Values
            .Count(state => state == NodeExecutionState.Completed ||
                           state == NodeExecutionState.Failed ||
                           state == NodeExecutionState.Skipped);

        // 如果所有节点都已处理完成
        if (completedOrFailedNodes == totalNodes)
        {
            // 检查是否有End节点，如果有，确保End节点已完成
            var endNodes = workflowContext.Definition.Nodes
                .Where(n => n.NodeType.Equals("End", StringComparison.OrdinalIgnoreCase))
                .ToList();

            if (endNodes.Count > 0)
            {
                var endNodeCompleted = endNodes.Any(n =>
                    workflowContext.GetNodeState(n.NodeId) == NodeExecutionState.Completed);

                if (endNodeCompleted)
                {
                    workflowContext.State = WorkflowExecutionState.Completed;
                    workflowContext.CompletedAt = DateTime.UtcNow;
                    return true;
                }
            }
            else
            {
                // 没有End节点，检查是否有失败的节点
                var hasFailedNodes = workflowContext.NodeStates.Values
                    .Any(state => state == NodeExecutionState.Failed);

                workflowContext.State = hasFailedNodes ?
                    WorkflowExecutionState.Failed :
                    WorkflowExecutionState.Completed;
                workflowContext.CompletedAt = DateTime.UtcNow;
                return true;
            }
        }

        return false;
    }

    /// <summary>
    /// 处理节点完成事件
    /// </summary>
    /// <param name="sender">事件发送者</param>
    /// <param name="e">事件参数</param>
    private void OnNodeCompleted(object? sender, NodeCompletedEventArgs e)
    {
        // 使用 Task.Run 来避免 async void
        _ = Task.Run(async () =>
        {
            try
            {
                _logger.LogDebug("Node completed: {NodeId} - {State}", e.NodeContext.NodeId, e.Result.State);

                // 更新WorkflowExecutionContext中的节点状态
                var workflowContext = e.NodeContext.WorkflowExecutionContext as EngineWorkflowContext;
                if (workflowContext != null)
                {
                    workflowContext.SetNodeResult(e.NodeContext.NodeId, e.Result);
                    _logger.LogDebug("Updated WorkflowExecutionContext: node {NodeId} state set to {State}",
                        e.NodeContext.NodeId, e.Result.State);
                }

                // 跟踪节点状态
                await _stateTracker.TrackNodeStateAsync(
                    e.NodeContext.ExecutionId,
                    e.NodeContext.NodeId,
                    e.Result.State,
                    $"Node execution completed: {(e.IsSuccess ? "Success" : "Failed")}");

                // 保存节点执行记录到数据库
                if (_executionRepository != null && workflowContext != null)
                {
                    try
                    {
                        var nodeType = GetNodeType(e.NodeContext.NodeId, workflowContext.Definition);
                        await _executionRepository.SaveNodeExecutionAsync(
                            e.Result, workflowContext.ExecutionId, nodeType, CancellationToken.None);
                        _logger.LogDebug("Node execution record saved to database: {NodeId}", e.NodeContext.NodeId);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Failed to save node execution record: {NodeId}", e.NodeContext.NodeId);
                    }
                }

                // 如果节点成功完成，调度后续节点
                if (e.IsSuccess)
                {
                    await ScheduleNextNodesAsync(e.NodeContext, CancellationToken.None);
                }
                else
                {
                    _logger.LogWarning("Node failed: {NodeId} - {ErrorMessage}",
                        e.NodeContext.NodeId, e.Result.ErrorMessage);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling node completed event for node {NodeId}", e.NodeContext.NodeId);
            }
        });
    }

    /// <summary>
    /// 处理节点执行错误事件
    /// </summary>
    /// <param name="sender">事件发送者</param>
    /// <param name="e">事件参数</param>
    private void OnNodeExecutionError(object? sender, NodeExecutionErrorEventArgs e)
    {
        // 使用 Task.Run 来避免 async void
        _ = Task.Run(async () =>
        {
            try
            {
                _logger.LogError(e.Error, "Node execution error: {NodeId}", e.NodeContext.NodeId);

                // 跟踪节点错误状态
                await _stateTracker.TrackNodeStateAsync(
                    e.NodeContext.ExecutionId,
                    e.NodeContext.NodeId,
                    NodeExecutionState.Failed,
                    $"Node execution error: {e.Error.Message}");

                // 如果可以重试，可以在这里处理重试逻辑
                if (e.CanRetry && e.RetryCount < 3) // 简单的重试逻辑
                {
                    _logger.LogInformation("Retrying node execution: {NodeId} (Attempt {RetryCount})",
                        e.NodeContext.NodeId, e.RetryCount + 1);

                    // 这里可以重新调度节点执行
                    // await ScheduleNodeExecutionAsync(e.NodeContext.WorkflowContext, node, CancellationToken.None);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling node execution error event for node {NodeId}", e.NodeContext.NodeId);
            }
        });
    }

    /// <summary>
    /// 调度后续节点执行
    /// </summary>
    /// <param name="completedNodeContext">已完成的节点上下文</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>调度任务</returns>
    private async Task ScheduleNextNodesAsync(
        NodeExecutionContext completedNodeContext,
        CancellationToken cancellationToken)
    {
        var workflowContext = completedNodeContext.WorkflowExecutionContext as EngineWorkflowContext
            ?? throw new InvalidOperationException("WorkflowExecutionContext is not available");
        var completedNodeId = completedNodeContext.NodeId;

        _logger.LogDebug("Scheduling next nodes after {NodeId}", completedNodeId);

        // 查找从当前节点出发的连接
        var outgoingConnections = workflowContext.Definition.Connections
            .Where(c => c.SourceNodeId == completedNodeId)
            .ToList();

        foreach (var connection in outgoingConnections)
        {
            var nextNode = workflowContext.Definition.Nodes
                .FirstOrDefault(n => n.NodeId == connection.TargetNodeId);

            if (nextNode == null)
            {
                _logger.LogWarning("Target node not found: {TargetNodeId}", connection.TargetNodeId);
                continue;
            }

            // 检查目标节点的前置条件是否满足
            if (await ArePrerequisitesSatisfiedAsync(workflowContext, nextNode, cancellationToken))
            {
                // 检查节点是否已经在执行或已完成
                var nodeState = workflowContext.GetNodeState(nextNode.NodeId);
                if (nodeState == NodeExecutionState.NotStarted)
                {
                    await ScheduleNodeExecutionAsync(workflowContext, nextNode, cancellationToken);
                }
                else
                {
                    _logger.LogDebug("Node {NodeId} is already in state {State}, skipping",
                        nextNode.NodeId, nodeState);
                }
            }
            else
            {
                _logger.LogDebug("Prerequisites not satisfied for node {NodeId}", nextNode.NodeId);
            }
        }
    }

    /// <summary>
    /// 检查节点的前置条件是否满足
    /// </summary>
    /// <param name="workflowContext">工作流执行上下文</param>
    /// <param name="node">要检查的节点</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>前置条件是否满足</returns>
    private async Task<bool> ArePrerequisitesSatisfiedAsync(
        EngineWorkflowContext workflowContext,
        WorkflowNode node,
        CancellationToken cancellationToken)
    {
        _logger.LogDebug("Checking prerequisites for node {NodeId}", node.NodeId);

        // 查找指向当前节点的所有连接
        var incomingConnections = workflowContext.Definition.Connections
            .Where(c => c.TargetNodeId == node.NodeId)
            .ToList();

        _logger.LogDebug("Node {NodeId} has {Count} incoming connections", node.NodeId, incomingConnections.Count);

        // 如果没有输入连接，说明是起始节点，前置条件满足
        if (incomingConnections.Count == 0)
        {
            _logger.LogDebug("Node {NodeId} has no prerequisites (start node)", node.NodeId);
            return true;
        }

        // 检查所有前置节点是否都已成功完成
        foreach (var connection in incomingConnections)
        {
            var sourceNodeState = workflowContext.GetNodeState(connection.SourceNodeId);
            _logger.LogDebug("Prerequisite node {SourceNodeId} state: {State}", connection.SourceNodeId, sourceNodeState);

            // 前置节点必须已完成
            if (sourceNodeState != NodeExecutionState.Completed)
            {
                _logger.LogDebug("Prerequisites not satisfied: node {SourceNodeId} is in state {State}, expected Completed",
                    connection.SourceNodeId, sourceNodeState);
                return false;
            }
        }

        _logger.LogDebug("All prerequisites satisfied for node {NodeId}", node.NodeId);
        return await Task.FromResult(true);
    }

    /// <summary>
    /// 获取工作流执行状态（兼容方法）
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>执行状态</returns>
    public async Task<WorkflowExecutionState> GetWorkflowStateAsync(
        string executionId,
        CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(executionId))
            throw new ArgumentException("ExecutionId cannot be null or empty", nameof(executionId));

        return await _stateTracker.GetWorkflowStateAsync(executionId, cancellationToken).ConfigureAwait(false);
    }

    /// <summary>
    /// 恢复工作流执行
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>工作流执行结果</returns>
    public async Task<WorkflowExecutionResult> ResumeWorkflowAsync(string executionId, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(executionId))
            throw new ArgumentException("ExecutionId cannot be null or empty", nameof(executionId));

        _logger.LogInformation("Resuming workflow execution: {ExecutionId}", executionId);

        try
        {
            // 通过调度器恢复工作流执行
            await _nodeScheduler.ResumeAsync(cancellationToken).ConfigureAwait(false);

            // 更新状态跟踪
            await _stateTracker.TrackWorkflowStateAsync(
                executionId,
                WorkflowExecutionState.Running,
                "Workflow execution resumed by user request",
                cancellationToken: cancellationToken).ConfigureAwait(false);

            _logger.LogInformation("Workflow execution resumed: {ExecutionId}", executionId);

            // 返回恢复结果
            return new WorkflowExecutionResult
            {
                ExecutionId = executionId,
                State = WorkflowExecutionState.Running,
                IsSuccess = true,
                StartedAt = DateTime.UtcNow,
                Message = "Workflow execution resumed successfully"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to resume workflow execution: {ExecutionId}", executionId);

            return new WorkflowExecutionResult
            {
                ExecutionId = executionId,
                State = WorkflowExecutionState.Failed,
                IsSuccess = false,
                ErrorMessage = ex.Message,
                Exception = ex,
                CompletedAt = DateTime.UtcNow
            };
        }
    }



    /// <summary>
    /// 获取所有活跃的工作流执行
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>活跃执行列表</returns>
    public async Task<IEnumerable<WorkflowExecutionStatus>> GetActiveExecutionsAsync(
        CancellationToken cancellationToken = default)
    {
        try
        {
            // 这里应该从状态跟踪器获取所有活跃的执行
            // 目前返回空列表，实际实现需要扩展状态跟踪器
            return await Task.FromResult(Enumerable.Empty<WorkflowExecutionStatus>());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get active executions");
            return Enumerable.Empty<WorkflowExecutionStatus>();
        }
    }

    /// <summary>
    /// 验证工作流定义
    /// </summary>
    /// <param name="workflowDefinition">工作流定义</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    public async Task<WorkflowValidationResult> ValidateWorkflowAsync(
        WorkflowDefinition workflowDefinition,
        CancellationToken cancellationToken = default)
    {
        if (workflowDefinition == null)
            throw new ArgumentNullException(nameof(workflowDefinition));

        var result = new WorkflowValidationResult
        {
            IsValid = true,
            WorkflowId = workflowDefinition.WorkflowId,
            ValidationErrors = new List<ValidationError>(),
            ValidationWarnings = new List<ValidationWarning>(),
            ValidatedAt = DateTime.UtcNow
        };

        try
        {
            // 基本验证
            if (string.IsNullOrEmpty(workflowDefinition.WorkflowId))
            {
                result.ValidationErrors.Add(new ValidationError
                {
                    Code = "MISSING_WORKFLOW_ID",
                    Message = "Workflow ID is required",
                    Severity = ValidationSeverity.Error
                });
            }

            if (workflowDefinition.Nodes == null || workflowDefinition.Nodes.Count == 0)
            {
                result.ValidationErrors.Add(new ValidationError
                {
                    Code = "NO_NODES",
                    Message = "Workflow must contain at least one node",
                    Severity = ValidationSeverity.Error
                });
            }

            // 检查是否有起始节点
            var startNodes = FindStartNodes(workflowDefinition);
            if (startNodes.Count == 0)
            {
                result.ValidationErrors.Add(new ValidationError
                {
                    Code = "NO_START_NODE",
                    Message = "Workflow must have at least one start node",
                    Severity = ValidationSeverity.Error
                });
            }

            result.IsValid = result.ValidationErrors.Count == 0;
            return await Task.FromResult(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating workflow: {WorkflowId}", workflowDefinition.WorkflowId);
            result.IsValid = false;
            result.ValidationErrors.Add(new ValidationError
            {
                Code = "VALIDATION_EXCEPTION",
                Message = $"Validation failed with exception: {ex.Message}",
                Severity = ValidationSeverity.Error
            });
            return result;
        }
    }

    /// <summary>
    /// 获取工作流执行历史
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="pageSize">页面大小</param>
    /// <param name="pageNumber">页码</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>执行历史</returns>
    public async Task<WorkflowExecutionHistory> GetExecutionHistoryAsync(
        string workflowId,
        int pageSize = 50,
        int pageNumber = 1,
        CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(workflowId))
            throw new ArgumentException("WorkflowId cannot be null or empty", nameof(workflowId));

        try
        {
            // 目前返回空的历史记录，实际实现需要扩展状态跟踪器
            return await Task.FromResult(new WorkflowExecutionHistory
            {
                WorkflowId = workflowId,
                TotalCount = 0,
                PageSize = pageSize,
                PageNumber = pageNumber,
                Executions = new List<WorkflowExecutionRecord>()
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get execution history for workflow: {WorkflowId}", workflowId);
            throw;
        }
    }

    /// <summary>
    /// 获取工作流性能统计
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="timeRange">时间范围</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>性能统计</returns>
    public async Task<WorkflowPerformanceStats> GetPerformanceStatsAsync(
        string workflowId,
        TimeSpan? timeRange = null,
        CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(workflowId))
            throw new ArgumentException("WorkflowId cannot be null or empty", nameof(workflowId));

        try
        {
            // 目前返回空的统计信息，实际实现需要收集性能数据
            return await Task.FromResult(new WorkflowPerformanceStats
            {
                WorkflowId = workflowId,
                TimeRange = timeRange.HasValue
                    ? new TimeRange
                    {
                        StartTime = DateTime.UtcNow.Subtract(timeRange.Value),
                        EndTime = DateTime.UtcNow
                    }
                    : new TimeRange
                    {
                        StartTime = DateTime.UtcNow.AddDays(-7),
                        EndTime = DateTime.UtcNow
                    },
                TotalExecutions = 0,
                SuccessfulExecutions = 0,
                FailedExecutions = 0,
                AverageExecutionTime = 0,
                MinExecutionTime = 0,
                MaxExecutionTime = 0,
                ThroughputPerHour = 0,
                ErrorRate = 0,
                GeneratedAt = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get performance stats for workflow: {WorkflowId}", workflowId);
            throw;
        }
    }

    /// <summary>
    /// 工作流执行状态变更事件
    /// </summary>
#pragma warning disable CS0067 // 事件从不使用
    public event EventHandler<WorkflowExecutionStatusChangedEventArgs>? ExecutionStatusChanged;

    /// <summary>
    /// 工作流执行完成事件
    /// </summary>
    public event EventHandler<WorkflowExecutionCompletedEventArgs>? ExecutionCompleted;

    /// <summary>
    /// 工作流执行错误事件
    /// </summary>
    public event EventHandler<WorkflowExecutionErrorEventArgs>? ExecutionError;
#pragma warning restore CS0067

    /// <summary>
    /// 获取节点类型
    /// </summary>
    /// <param name="nodeId">节点ID</param>
    /// <param name="definition">工作流定义</param>
    /// <returns>节点类型</returns>
    private static string GetNodeType(string nodeId, WorkflowDefinition definition)
    {
        var node = definition.Nodes.FirstOrDefault(n => n.NodeId == nodeId);
        return node?.NodeType ?? "Unknown";
    }

    /// <summary>
    /// 从工作流执行上下文创建工作流执行结果
    /// </summary>
    /// <param name="workflowContext">工作流执行上下文</param>
    /// <returns>工作流执行结果</returns>
    private WorkflowExecutionResult CreateWorkflowExecutionResult(EngineWorkflowContext workflowContext)
    {
        var nodeResults = new List<NodeExecutionResult>();

        // 收集所有节点的执行结果
        foreach (var node in workflowContext.Definition.Nodes)
        {
            var nodeState = workflowContext.NodeStates.GetValueOrDefault(node.NodeId, NodeExecutionState.NotStarted);
            nodeResults.Add(new NodeExecutionResult
            {
                NodeId = node.NodeId,
                ExecutionId = workflowContext.ExecutionId,
                State = nodeState,
                StartedAt = DateTime.UtcNow, // 这里应该从实际的执行记录中获取
                CompletedAt = nodeState == NodeExecutionState.Completed ? DateTime.UtcNow : null,
                IsSuccess = nodeState == NodeExecutionState.Completed,
                OutputData = new Dictionary<string, object>(),
                ErrorMessage = nodeState == NodeExecutionState.Failed ? "Node execution failed" : null
            });
        }

        return new WorkflowExecutionResult
        {
            ExecutionId = workflowContext.ExecutionId,
            WorkflowId = workflowContext.Definition.WorkflowId,
            State = workflowContext.State,
            IsSuccess = workflowContext.State == WorkflowExecutionState.Completed,
            StartedAt = workflowContext.StartedAt,
            CompletedAt = workflowContext.CompletedAt,
            OutputData = workflowContext.OutputData?.ToDictionary(kvp => kvp.Key, kvp => kvp.Value) ?? new Dictionary<string, object>(),
            NodeResults = nodeResults,
            Stats = new WorkflowExecutionStats
            {
                TotalNodes = workflowContext.Definition.Nodes.Count,
                ExecutedNodes = nodeResults.Count(n => n.State != NodeExecutionState.NotStarted),
                SuccessfulNodes = nodeResults.Count(n => n.State == NodeExecutionState.Completed),
                FailedNodes = nodeResults.Count(n => n.State == NodeExecutionState.Failed),
                SkippedNodes = nodeResults.Count(n => n.State == NodeExecutionState.Skipped),
                AverageExecutionTimeMs = workflowContext.CompletedAt.HasValue
                    ? (workflowContext.CompletedAt.Value - workflowContext.StartedAt).TotalMilliseconds
                    : 0
            },
            Metadata = new Dictionary<string, object>()
        };
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        try
        {
            // 取消订阅事件
            _nodeScheduler.NodeCompleted -= OnNodeCompleted;
            _nodeScheduler.NodeExecutionError -= OnNodeExecutionError;

            // 释放调度器资源
            _nodeScheduler?.Dispose();

            _logger.LogInformation("WorkflowEngineService disposed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error disposing WorkflowEngineService");
        }
    }
}
