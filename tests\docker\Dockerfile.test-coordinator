# FlowCustomV1 测试协调器 Docker 镜像
# 负责执行分布式集群测试用例

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS base
WORKDIR /app

# 安装必要的工具
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    jq \
    python3 \
    python3-pip \
    && rm -rf /var/lib/apt/lists/*

# 安装Python测试工具
RUN pip3 install requests pytest pytest-html pytest-json-report

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# 复制测试项目文件
COPY ["tests/FlowCustomV1.Tests/FlowCustomV1.Tests.csproj", "tests/FlowCustomV1.Tests/"]
COPY ["src/FlowCustomV1.Api/FlowCustomV1.Api.csproj", "src/FlowCustomV1.Api/"]
COPY ["src/FlowCustomV1.Infrastructure/FlowCustomV1.Infrastructure.csproj", "src/FlowCustomV1.Infrastructure/"]
COPY ["src/FlowCustomV1.Engine/FlowCustomV1.Engine.csproj", "src/FlowCustomV1.Engine/"]
COPY ["src/FlowCustomV1.Core/FlowCustomV1.Core.csproj", "src/FlowCustomV1.Core/"]

# 还原测试项目依赖
RUN dotnet restore "tests/FlowCustomV1.Tests/FlowCustomV1.Tests.csproj"

# 复制所有源代码
COPY . .

# 构建测试项目
WORKDIR "/src/tests/FlowCustomV1.Tests"
RUN dotnet build "FlowCustomV1.Tests.csproj" -c Release -o /app/build

FROM base AS final
WORKDIR /app

# 复制构建的测试项目
COPY --from=build /app/build .
COPY --from=build /src/tests/FlowCustomV1.Tests /app/tests

# 复制测试脚本和配置
COPY ["tests/docker/test-scripts/", "./test-scripts/"]
COPY ["tests/docker/config/", "./config/"]

# 设置权限
RUN chmod +x ./test-scripts/*.sh
RUN chmod +x ./test-scripts/*.py

# 创建测试结果目录
RUN mkdir -p /app/test-results /app/logs

# 环境变量
ENV DOTNET_ENVIRONMENT=Testing
ENV TEST_ENVIRONMENT=Docker
ENV PYTHONPATH=/app/test-scripts

# 启动脚本
COPY ["tests/docker/scripts/start-test-coordinator.sh", "./start-test-coordinator.sh"]
RUN chmod +x ./start-test-coordinator.sh

ENTRYPOINT ["./start-test-coordinator.sh"]
