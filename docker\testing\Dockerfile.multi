# FlowCustomV1 多角色节点 - 测试环境
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
WORKDIR /app
EXPOSE 5000

# 测试环境标签
LABEL environment="testing"
LABEL component="multi-role"
LABEL roles="A<PERSON>,Worker,Designer"
LABEL version="v0.0.1.8"

FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

# 复制项目文件
COPY ["src/FlowCustomV1.Api/FlowCustomV1.Api.csproj", "src/FlowCustomV1.Api/"]
COPY ["src/FlowCustomV1.Core/FlowCustomV1.Core.csproj", "src/FlowCustomV1.Core/"]
COPY ["src/FlowCustomV1.Infrastructure/FlowCustomV1.Infrastructure.csproj", "src/FlowCustomV1.Infrastructure/"]

# 还原依赖
RUN dotnet restore "src/FlowCustomV1.Api/FlowCustomV1.Api.csproj"

# 复制所有源代码
COPY . .

# 构建项目
WORKDIR "/src/src/FlowCustomV1.Api"
RUN dotnet build "FlowCustomV1.Api.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "FlowCustomV1.Api.csproj" -c Release -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .

# 测试环境特定配置
ENV ASPNETCORE_ENVIRONMENT=Testing
ENV ASPNETCORE_URLS=http://+:5000

# 多角色节点特定配置
ENV NODE_ROLES=Api,Worker,Designer

# 测试环境设置
ENV ASPNETCORE_LOGGING__LOGLEVEL__DEFAULT=Information

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:5000/health || exit 1

ENTRYPOINT ["dotnet", "FlowCustomV1.Api.dll", "--environment", "Testing", "--roles", "Api,Worker,Designer"]
