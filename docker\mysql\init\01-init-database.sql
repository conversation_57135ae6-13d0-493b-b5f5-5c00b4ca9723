-- FlowCustomV1 MySQL初始化脚本
-- 通用数据库初始化，适用于所有环境

-- 设置字符集和排序规则
SET NAMES utf8mb4;
SET CHARACTER SET utf8mb4;

-- 创建flowcustom用户（如果不存在）
-- 注意：密码和数据库名由环境变量控制
CREATE USER IF NOT EXISTS 'flowcustom'@'%' IDENTIFIED BY 'FlowCustom@2025';

-- 授权用户访问当前数据库（数据库名由MYSQL_DATABASE环境变量指定）
GRANT ALL PRIVILEGES ON *.* TO 'flowcustom'@'%';
GRANT ALL PRIVILEGES ON *.* TO 'root'@'%';

-- 刷新权限
FLUSH PRIVILEGES;

-- 显示配置信息
SELECT 'MySQL Configuration:' AS info;
SELECT @@version AS 'MySQL Version';
SELECT @@lower_case_table_names AS 'lower_case_table_names';
SELECT @@character_set_server AS 'character_set_server';
SELECT @@collation_server AS 'collation_server';

-- 显示当前数据库
SELECT 'Current Database:' AS info;
SELECT DATABASE() AS 'current_database';
SHOW DATABASES;
