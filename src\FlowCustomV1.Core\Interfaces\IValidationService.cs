using FlowCustomV1.Core.Models.Workflow;

namespace FlowCustomV1.Core.Interfaces;

/// <summary>
/// 验证服务接口
/// 提供通用的数据验证功能
/// </summary>
public interface IValidationService
{
    /// <summary>
    /// 验证对象
    /// </summary>
    /// <typeparam name="T">对象类型</typeparam>
    /// <param name="obj">要验证的对象</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    Task<ValidationResult> ValidateAsync<T>(T obj, CancellationToken cancellationToken = default) where T : class;

    /// <summary>
    /// 验证对象集合
    /// </summary>
    /// <typeparam name="T">对象类型</typeparam>
    /// <param name="objects">要验证的对象集合</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果列表</returns>
    Task<IEnumerable<ValidationResult>> ValidateCollectionAsync<T>(IEnumerable<T> objects, CancellationToken cancellationToken = default) where T : class;

    /// <summary>
    /// 验证属性值
    /// </summary>
    /// <param name="value">属性值</param>
    /// <param name="propertyName">属性名称</param>
    /// <param name="validationRules">验证规则</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    Task<PropertyValidationResult> ValidatePropertyAsync(object? value, string propertyName, IEnumerable<ParameterValidationRule> validationRules, CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证参数字典
    /// </summary>
    /// <param name="parameters">参数字典</param>
    /// <param name="parameterDefinitions">参数定义</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    Task<ParameterValidationResult> ValidateParametersAsync(Dictionary<string, object> parameters, IEnumerable<WorkflowParameterDefinition> parameterDefinitions, CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证JSON字符串
    /// </summary>
    /// <param name="json">JSON字符串</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    Task<JsonValidationResult> ValidateJsonAsync(string json, CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证XML字符串
    /// </summary>
    /// <param name="xml">XML字符串</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    Task<XmlValidationResult> ValidateXmlAsync(string xml, CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证邮箱地址
    /// </summary>
    /// <param name="email">邮箱地址</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    Task<ValidationResult> ValidateEmailAsync(string email, CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证URL
    /// </summary>
    /// <param name="url">URL</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    Task<ValidationResult> ValidateUrlAsync(string url, CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证正则表达式
    /// </summary>
    /// <param name="pattern">正则表达式模式</param>
    /// <param name="input">输入字符串</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    Task<ValidationResult> ValidateRegexAsync(string pattern, string input, CancellationToken cancellationToken = default);

    /// <summary>
    /// 注册自定义验证器
    /// </summary>
    /// <typeparam name="T">对象类型</typeparam>
    /// <param name="validator">验证器</param>
    void RegisterValidator<T>(IValidator<T> validator) where T : class;

    /// <summary>
    /// 移除自定义验证器
    /// </summary>
    /// <typeparam name="T">对象类型</typeparam>
    /// <returns>是否成功移除</returns>
    bool RemoveValidator<T>() where T : class;

    /// <summary>
    /// 获取已注册的验证器
    /// </summary>
    /// <typeparam name="T">对象类型</typeparam>
    /// <returns>验证器</returns>
    IValidator<T>? GetValidator<T>() where T : class;

    /// <summary>
    /// 获取所有已注册的验证器类型
    /// </summary>
    /// <returns>验证器类型列表</returns>
    IEnumerable<Type> GetRegisteredValidatorTypes();
}

/// <summary>
/// 验证器接口
/// </summary>
/// <typeparam name="T">要验证的对象类型</typeparam>
public interface IValidator<in T> where T : class
{
    /// <summary>
    /// 验证对象
    /// </summary>
    /// <param name="obj">要验证的对象</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    Task<ValidationResult> ValidateAsync(T obj, CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证器名称
    /// </summary>
    string ValidatorName { get; }

    /// <summary>
    /// 验证器版本
    /// </summary>
    string Version { get; }
}

/// <summary>
/// 属性验证结果
/// </summary>
public class PropertyValidationResult : ValidationResult
{
    /// <summary>
    /// 属性名称
    /// </summary>
    public string PropertyName { get; set; } = string.Empty;

    /// <summary>
    /// 属性值
    /// </summary>
    public object? PropertyValue { get; set; }
}

/// <summary>
/// JSON验证结果
/// </summary>
public class JsonValidationResult : ValidationResult
{
    /// <summary>
    /// 是否为有效JSON
    /// </summary>
    public bool IsValidJson { get; set; } = true;

    /// <summary>
    /// JSON解析错误
    /// </summary>
    public string? ParseError { get; set; }

    /// <summary>
    /// JSON对象
    /// </summary>
    public object? ParsedObject { get; set; }
}

/// <summary>
/// XML验证结果
/// </summary>
public class XmlValidationResult : ValidationResult
{
    /// <summary>
    /// 是否为有效XML
    /// </summary>
    public bool IsValidXml { get; set; } = true;

    /// <summary>
    /// XML解析错误
    /// </summary>
    public string? ParseError { get; set; }

    /// <summary>
    /// XML文档
    /// </summary>
    public object? ParsedDocument { get; set; }
}
