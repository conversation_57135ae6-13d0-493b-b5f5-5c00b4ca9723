using FlowCustomV1.Core.Models.Workflow;
using FlowCustomV1.Core.Models.Executor;

namespace FlowCustomV1.Core.Interfaces.UseCases;

/// <summary>
/// 执行工作流用例接口
/// 定义工作流执行的业务契约，不包含技术实现细节
/// </summary>
public interface IExecuteWorkflowUseCase
{
    /// <summary>
    /// 执行工作流
    /// </summary>
    /// <param name="request">执行请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>执行响应</returns>
    Task<ExecuteWorkflowResponse> ExecuteAsync(ExecuteWorkflowRequest request, CancellationToken cancellationToken = default);
}

/// <summary>
/// 执行工作流请求
/// </summary>
public class ExecuteWorkflowRequest
{
    /// <summary>
    /// 工作流ID
    /// </summary>
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 工作流定义（可选，如果提供则直接使用）
    /// </summary>
    public WorkflowDefinition? WorkflowDefinition { get; set; }

    /// <summary>
    /// 输入数据
    /// </summary>
    public Dictionary<string, object> InputData { get; set; } = new();

    /// <summary>
    /// 执行选项
    /// </summary>
    public ExecutionOptions Options { get; set; } = new();

    /// <summary>
    /// 请求者ID
    /// </summary>
    public string RequesterId { get; set; } = string.Empty;
}

/// <summary>
/// 执行工作流响应
/// </summary>
public class ExecuteWorkflowResponse
{
    /// <summary>
    /// 执行ID
    /// </summary>
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 是否成功启动
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 执行器节点ID
    /// </summary>
    public string ExecutorNodeId { get; set; } = string.Empty;

    /// <summary>
    /// 预估执行时间（毫秒）
    /// </summary>
    public long EstimatedDurationMs { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime StartedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 执行选项
/// </summary>
public class ExecutionOptions
{
    /// <summary>
    /// 超时时间（毫秒）
    /// </summary>
    public long TimeoutMs { get; set; } = 300000; // 5分钟默认超时

    /// <summary>
    /// 最大重试次数
    /// </summary>
    public int MaxRetries { get; set; } = 3;

    /// <summary>
    /// 是否允许迁移
    /// </summary>
    public bool AllowMigration { get; set; } = true;

    /// <summary>
    /// 是否持久化结果
    /// </summary>
    public bool PersistResult { get; set; } = true;

    /// <summary>
    /// 执行优先级
    /// </summary>
    public ExecutionPriority Priority { get; set; } = ExecutionPriority.Normal;

    /// <summary>
    /// 执行标签
    /// </summary>
    public List<string> Tags { get; set; } = new();

    /// <summary>
    /// 执行元数据
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();
}
