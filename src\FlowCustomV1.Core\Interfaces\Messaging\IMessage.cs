namespace FlowCustomV1.Core.Interfaces.Messaging;

/// <summary>
/// 消息基础接口
/// 定义消息系统的抽象契约，不依赖具体的消息中间件实现
/// </summary>
public interface IMessage
{
    /// <summary>
    /// 消息唯一标识
    /// </summary>
    string MessageId { get; }

    /// <summary>
    /// 消息类型
    /// </summary>
    string MessageType { get; }

    /// <summary>
    /// 发送者标识
    /// </summary>
    string SenderId { get; }

    /// <summary>
    /// 目标标识（可选，用于点对点消息）
    /// </summary>
    string? TargetId { get; }

    /// <summary>
    /// 消息创建时间
    /// </summary>
    DateTime CreatedAt { get; }

    /// <summary>
    /// 消息过期时间（可选）
    /// </summary>
    DateTime? ExpiresAt { get; }

    /// <summary>
    /// 消息优先级
    /// </summary>
    MessagePriority Priority { get; }

    /// <summary>
    /// 消息内容
    /// </summary>
    object? Payload { get; }

    /// <summary>
    /// 消息元数据
    /// </summary>
    IReadOnlyDictionary<string, object> Metadata { get; }
}

/// <summary>
/// 泛型消息接口
/// </summary>
/// <typeparam name="T">消息内容类型</typeparam>
public interface IMessage<T> : IMessage
{
    /// <summary>
    /// 强类型消息内容
    /// </summary>
    new T? Payload { get; }
}

/// <summary>
/// 消息优先级枚举
/// </summary>
public enum MessagePriority
{
    /// <summary>
    /// 低优先级
    /// </summary>
    Low = 0,

    /// <summary>
    /// 普通优先级
    /// </summary>
    Normal = 1,

    /// <summary>
    /// 高优先级
    /// </summary>
    High = 2,

    /// <summary>
    /// 紧急优先级
    /// </summary>
    Critical = 3
}

/// <summary>
/// 消息构建器接口
/// </summary>
public interface IMessageBuilder
{
    /// <summary>
    /// 设置消息类型
    /// </summary>
    /// <param name="messageType">消息类型</param>
    /// <returns>消息构建器</returns>
    IMessageBuilder WithType(string messageType);

    /// <summary>
    /// 设置发送者
    /// </summary>
    /// <param name="senderId">发送者标识</param>
    /// <returns>消息构建器</returns>
    IMessageBuilder WithSender(string senderId);

    /// <summary>
    /// 设置目标
    /// </summary>
    /// <param name="targetId">目标标识</param>
    /// <returns>消息构建器</returns>
    IMessageBuilder WithTarget(string targetId);

    /// <summary>
    /// 设置优先级
    /// </summary>
    /// <param name="priority">消息优先级</param>
    /// <returns>消息构建器</returns>
    IMessageBuilder WithPriority(MessagePriority priority);

    /// <summary>
    /// 设置过期时间
    /// </summary>
    /// <param name="expiresAt">过期时间</param>
    /// <returns>消息构建器</returns>
    IMessageBuilder WithExpiration(DateTime expiresAt);

    /// <summary>
    /// 设置消息内容
    /// </summary>
    /// <param name="payload">消息内容</param>
    /// <returns>消息构建器</returns>
    IMessageBuilder WithPayload(object payload);

    /// <summary>
    /// 添加元数据
    /// </summary>
    /// <param name="key">元数据键</param>
    /// <param name="value">元数据值</param>
    /// <returns>消息构建器</returns>
    IMessageBuilder WithMetadata(string key, object value);

    /// <summary>
    /// 构建消息
    /// </summary>
    /// <returns>消息实例</returns>
    IMessage Build();

    /// <summary>
    /// 构建强类型消息
    /// </summary>
    /// <typeparam name="T">消息内容类型</typeparam>
    /// <returns>强类型消息实例</returns>
    IMessage<T> Build<T>();
}
