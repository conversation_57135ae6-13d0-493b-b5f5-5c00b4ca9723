#!/usr/bin/env python3
"""
FlowCustomV1 集群测试启动脚本
支持启动完整的7角色+1多角色节点集群测试环境
"""

import subprocess
import sys
import time
import json
import requests
from typing import List, Dict, Any

def run_command(command: List[str], cwd: str = None) -> bool:
    """执行命令并返回是否成功"""
    try:
        result = subprocess.run(command, cwd=cwd, capture_output=True, text=True)
        if result.returncode != 0:
            print(f"❌ 命令执行失败: {' '.join(command)}")
            print(f"错误输出: {result.stderr}")
            return False
        return True
    except Exception as e:
        print(f"❌ 命令执行异常: {e}")
        return False

def check_service_health(url: str, timeout: int = 30) -> bool:
    """检查服务健康状态"""
    for i in range(timeout):
        try:
            response = requests.get(f"{url}/health", timeout=5)
            if response.status_code == 200:
                return True
        except:
            pass
        time.sleep(1)
    return False

def start_infrastructure():
    """启动基础设施服务 (NATS + MySQL)"""
    print("🚀 启动基础设施服务...")
    
    # 启动NATS集群和MySQL
    if not run_command(["docker-compose", "up", "-d", "nats-server-1", "nats-server-2", "nats-server-3", "mysql"], cwd="."):
        return False
    
    print("⏳ 等待基础设施服务启动...")
    time.sleep(10)
    
    # 检查NATS集群状态
    print("🔍 检查NATS集群状态...")
    for i, port in enumerate([24222, 24223, 24224], 1):
        try:
            result = subprocess.run(["docker", "exec", f"nats-test-server-{i}", "nats", "server", "info"],
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ NATS服务器 {port} 运行正常")
            else:
                print(f"⚠️ NATS服务器 {port} 状态检查失败")
        except:
            print(f"⚠️ 无法检查NATS服务器 {port} 状态")
    
    # 检查MySQL状态
    print("🔍 检查MySQL状态...")
    try:
        result = subprocess.run(["docker", "exec", "mysql-test", "mysqladmin", "ping", "-h", "localhost"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ MySQL数据库运行正常")
        else:
            print("⚠️ MySQL数据库状态检查失败")
    except:
        print("⚠️ 无法检查MySQL数据库状态")
    
    return True

def start_application_nodes():
    """启动应用节点"""
    print("🚀 启动应用节点...")
    
    # 定义节点启动顺序 (基础设施优先，然后是核心服务)
    node_order = [
        "api-node",           # API网关节点
        "scheduler-node",     # 调度节点
        "executor-node",      # 执行节点
        "worker-node",        # 工作节点
        "designer-node",      # 设计节点
        "validator-node",     # 验证节点
        "monitor-node",       # 监控节点
        "multi-role-node"     # 多角色节点
    ]
    
    for node in node_order:
        print(f"🔄 启动 {node}...")
        if not run_command(["docker-compose", "up", "-d", node], cwd="."):
            print(f"❌ {node} 启动失败")
            return False
        
        # 等待节点启动
        time.sleep(5)
        
        # 检查容器状态
        result = subprocess.run(["docker", "ps", "--filter", f"name=flowcustom-{node.replace('-node', '')}", "--format", "{{.Status}}"], 
                              capture_output=True, text=True)
        if "Up" in result.stdout:
            print(f"✅ {node} 启动成功")
        else:
            print(f"⚠️ {node} 状态异常: {result.stdout.strip()}")
    
    return True

def check_cluster_status():
    """检查集群状态"""
    print("🔍 检查集群状态...")
    
    # 检查所有容器状态
    result = subprocess.run(["docker", "ps", "--filter", "name=flowcustom", "--format", "table {{.Names}}\t{{.Status}}\t{{.Ports}}"], 
                          capture_output=True, text=True)
    print("📊 集群节点状态:")
    print(result.stdout)
    
    # 检查API节点健康状态
    print("🔍 检查API节点健康状态...")
    api_endpoints = [
        "http://localhost:25000",  # API节点
        "http://localhost:25001"   # 多角色节点
    ]

    for endpoint in api_endpoints:
        if check_service_health(endpoint):
            print(f"✅ {endpoint} 健康检查通过")
        else:
            print(f"⚠️ {endpoint} 健康检查失败")

    # 显示集群访问信息
    print("\n🌐 集群访问信息:")
    print("API节点 (单角色):     http://localhost:25000")
    print("多角色节点:          http://localhost:25001")
    print("NATS监控:           http://localhost:28222")
    print("MySQL数据库:        localhost:23306")
    
    return True

def stop_cluster():
    """停止集群"""
    print("🛑 停止集群...")
    return run_command(["docker-compose", "down"], cwd=".")

def cleanup_cluster():
    """清理集群资源"""
    print("🧹 清理集群资源...")
    run_command(["docker-compose", "down", "-v", "--remove-orphans"], cwd=".")
    run_command(["docker", "system", "prune", "-f"], cwd=".")
    return True

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python start-cluster-test.py [start|stop|status|cleanup]")
        sys.exit(1)
    
    command = sys.argv[1].lower()
    
    if command == "start":
        print("🚀 启动FlowCustomV1集群测试环境...")
        print("📋 集群配置: 7个单角色节点 + 1个多角色节点")
        
        if not start_infrastructure():
            print("❌ 基础设施启动失败")
            sys.exit(1)
        
        if not start_application_nodes():
            print("❌ 应用节点启动失败")
            sys.exit(1)
        
        if not check_cluster_status():
            print("❌ 集群状态检查失败")
            sys.exit(1)
        
        print("✅ 集群测试环境启动完成!")
        print("💡 使用 'python start-cluster-test.py status' 检查集群状态")
        print("💡 使用 'python start-cluster-test.py stop' 停止集群")
    
    elif command == "stop":
        if stop_cluster():
            print("✅ 集群已停止")
        else:
            print("❌ 集群停止失败")
    
    elif command == "status":
        check_cluster_status()
    
    elif command == "cleanup":
        if cleanup_cluster():
            print("✅ 集群资源清理完成")
        else:
            print("❌ 集群资源清理失败")
    
    else:
        print("❌ 未知命令，支持的命令: start, stop, status, cleanup")
        sys.exit(1)

if __name__ == "__main__":
    main()
