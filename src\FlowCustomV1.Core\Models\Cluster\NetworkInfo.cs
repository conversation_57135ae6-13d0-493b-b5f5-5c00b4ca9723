using System.ComponentModel.DataAnnotations;
using System.Net;

namespace FlowCustomV1.Core.Models.Cluster;

/// <summary>
/// 网络信息模型
/// 包含节点的网络通信相关信息
/// </summary>
public class NetworkInfo
{
    /// <summary>
    /// 节点IP地址
    /// </summary>
    [Required]
    public string IpAddress { get; set; } = string.Empty;

    /// <summary>
    /// HTTP API端口
    /// </summary>
    [Range(1, 65535)]
    public int HttpPort { get; set; } = 8080;

    /// <summary>
    /// NATS通信端口
    /// </summary>
    [Range(1, 65535)]
    public int NatsPort { get; set; } = 4222;

    /// <summary>
    /// 管理端口
    /// </summary>
    [Range(1, 65535)]
    public int ManagementPort { get; set; } = 8081;

    /// <summary>
    /// 网络延迟（毫秒）
    /// </summary>
    public double LatencyMs { get; set; } = 0;

    /// <summary>
    /// 网络延迟（LatencyMs的别名，保持向后兼容）
    /// </summary>
    public double Latency
    {
        get => LatencyMs;
        set => LatencyMs = value;
    }

    /// <summary>
    /// 网络带宽（Mbps）
    /// </summary>
    public double BandwidthMbps { get; set; } = 0;

    /// <summary>
    /// 是否启用SSL/TLS
    /// </summary>
    public bool SslEnabled { get; set; } = false;

    /// <summary>
    /// SSL证书路径
    /// </summary>
    public string? SslCertificatePath { get; set; }

    /// <summary>
    /// SSL证书密码
    /// </summary>
    public string? SslCertificatePassword { get; set; }

    /// <summary>
    /// 主机名
    /// </summary>
    public string HostName { get; set; } = string.Empty;

    /// <summary>
    /// 外部访问地址
    /// </summary>
    public string? ExternalAddress { get; set; }

    /// <summary>
    /// 网络接口名称
    /// </summary>
    public string? NetworkInterface { get; set; }

    /// <summary>
    /// 网络质量评分 (0-100)
    /// </summary>
    public double NetworkQualityScore { get; set; } = 100.0;

    /// <summary>
    /// 连接状态
    /// </summary>
    public NetworkConnectionState ConnectionState { get; set; } = NetworkConnectionState.Unknown;

    /// <summary>
    /// 最后连接测试时间
    /// </summary>
    public DateTime LastConnectionTest { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 网络统计信息
    /// </summary>
    public NetworkStatistics Statistics { get; set; } = new();

    /// <summary>
    /// 网络配置元数据
    /// </summary>
    public Dictionary<string, object> NetworkMetadata { get; set; } = new();

    /// <summary>
    /// 获取完整的HTTP URL
    /// </summary>
    /// <returns>HTTP URL</returns>
    public string GetHttpUrl()
    {
        var protocol = SslEnabled ? "https" : "http";
        var address = !string.IsNullOrWhiteSpace(ExternalAddress) ? ExternalAddress : IpAddress;
        return $"{protocol}://{address}:{HttpPort}";
    }

    /// <summary>
    /// 获取完整的管理URL
    /// </summary>
    /// <returns>管理URL</returns>
    public string GetManagementUrl()
    {
        var protocol = SslEnabled ? "https" : "http";
        var address = !string.IsNullOrWhiteSpace(ExternalAddress) ? ExternalAddress : IpAddress;
        return $"{protocol}://{address}:{ManagementPort}";
    }

    /// <summary>
    /// 获取NATS连接字符串
    /// </summary>
    /// <returns>NATS连接字符串</returns>
    public string GetNatsConnectionString()
    {
        var address = !string.IsNullOrWhiteSpace(ExternalAddress) ? ExternalAddress : IpAddress;
        return $"nats://{address}:{NatsPort}";
    }

    /// <summary>
    /// 验证IP地址格式
    /// </summary>
    /// <returns>是否有效</returns>
    public bool IsValidIpAddress()
    {
        return IPAddress.TryParse(IpAddress, out _);
    }

    /// <summary>
    /// 验证网络配置的有效性
    /// </summary>
    /// <returns>验证结果</returns>
    public bool IsValid()
    {
        return !string.IsNullOrWhiteSpace(IpAddress) &&
               IsValidIpAddress() &&
               HttpPort > 0 && HttpPort <= 65535 &&
               NatsPort > 0 && NatsPort <= 65535 &&
               ManagementPort > 0 && ManagementPort <= 65535 &&
               !string.IsNullOrWhiteSpace(HostName);
    }

    /// <summary>
    /// 创建网络信息的深拷贝
    /// </summary>
    /// <returns>网络信息的深拷贝</returns>
    public NetworkInfo Clone()
    {
        return new NetworkInfo
        {
            IpAddress = IpAddress,
            HttpPort = HttpPort,
            NatsPort = NatsPort,
            ManagementPort = ManagementPort,
            LatencyMs = LatencyMs,
            BandwidthMbps = BandwidthMbps,
            SslEnabled = SslEnabled,
            SslCertificatePath = SslCertificatePath,
            SslCertificatePassword = SslCertificatePassword,
            HostName = HostName,
            ExternalAddress = ExternalAddress,
            NetworkInterface = NetworkInterface,
            NetworkQualityScore = NetworkQualityScore,
            ConnectionState = ConnectionState,
            LastConnectionTest = LastConnectionTest,
            Statistics = Statistics.Clone(),
            NetworkMetadata = new Dictionary<string, object>(NetworkMetadata)
        };
    }

    /// <summary>
    /// 获取网络信息的简要描述
    /// </summary>
    /// <returns>网络信息简要描述</returns>
    public override string ToString()
    {
        return $"Network[{IpAddress}:{HttpPort}] {HostName} - {ConnectionState}";
    }
}

/// <summary>
/// 网络连接状态
/// </summary>
public enum NetworkConnectionState
{
    /// <summary>
    /// 未知状态
    /// </summary>
    Unknown = 0,

    /// <summary>
    /// 已连接
    /// </summary>
    Connected = 1,

    /// <summary>
    /// 连接中
    /// </summary>
    Connecting = 2,

    /// <summary>
    /// 已断开
    /// </summary>
    Disconnected = 3,

    /// <summary>
    /// 连接失败
    /// </summary>
    Failed = 4,

    /// <summary>
    /// 超时
    /// </summary>
    Timeout = 5
}

/// <summary>
/// 网络统计信息
/// </summary>
public class NetworkStatistics
{
    /// <summary>
    /// 发送字节数
    /// </summary>
    public long BytesSent { get; set; } = 0;

    /// <summary>
    /// 接收字节数
    /// </summary>
    public long BytesReceived { get; set; } = 0;

    /// <summary>
    /// 发送包数
    /// </summary>
    public long PacketsSent { get; set; } = 0;

    /// <summary>
    /// 接收包数
    /// </summary>
    public long PacketsReceived { get; set; } = 0;

    /// <summary>
    /// 连接错误数
    /// </summary>
    public long ConnectionErrors { get; set; } = 0;

    /// <summary>
    /// 平均延迟（毫秒）
    /// </summary>
    public double AverageLatencyMs { get; set; } = 0;

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 创建网络统计信息的深拷贝
    /// </summary>
    /// <returns>网络统计信息的深拷贝</returns>
    public NetworkStatistics Clone()
    {
        return new NetworkStatistics
        {
            BytesSent = BytesSent,
            BytesReceived = BytesReceived,
            PacketsSent = PacketsSent,
            PacketsReceived = PacketsReceived,
            ConnectionErrors = ConnectionErrors,
            AverageLatencyMs = AverageLatencyMs,
            LastUpdated = LastUpdated
        };
    }
}
