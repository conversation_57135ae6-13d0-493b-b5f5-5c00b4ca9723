namespace FlowCustomV1.Infrastructure.Health;

/// <summary>
/// 数据库健康状态
/// </summary>
public class DatabaseHealthStatus
{
    /// <summary>
    /// 是否健康
    /// </summary>
    public bool IsHealthy { get; set; }

    /// <summary>
    /// 数据库是否存在
    /// </summary>
    public bool DatabaseExists { get; set; }

    /// <summary>
    /// 表是否存在
    /// </summary>
    public bool TablesExist { get; set; }

    /// <summary>
    /// 表结构是否有效
    /// </summary>
    public bool SchemaIsValid { get; set; }

    /// <summary>
    /// 是否可以连接
    /// </summary>
    public bool CanConnect { get; set; }

    /// <summary>
    /// 待处理的迁移数量
    /// </summary>
    public int PendingMigrationsCount { get; set; }

    /// <summary>
    /// 检查时间
    /// </summary>
    public DateTime CheckedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 响应时间（毫秒）
    /// </summary>
    public double ResponseTimeMs { get; set; }

    /// <summary>
    /// 问题列表
    /// </summary>
    public List<string> Issues { get; set; } = new();

    /// <summary>
    /// 建议列表
    /// </summary>
    public List<string> Recommendations { get; set; } = new();

    /// <summary>
    /// 详细信息
    /// </summary>
    public Dictionary<string, object> Details { get; set; } = new();

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 异常详情
    /// </summary>
    public Exception? Exception { get; set; }

    /// <summary>
    /// 添加问题
    /// </summary>
    /// <param name="issue">问题描述</param>
    public void AddIssue(string issue)
    {
        Issues.Add(issue);
        IsHealthy = false;
    }

    /// <summary>
    /// 添加建议
    /// </summary>
    /// <param name="recommendation">建议描述</param>
    public void AddRecommendation(string recommendation)
    {
        Recommendations.Add(recommendation);
    }

    /// <summary>
    /// 添加详细信息
    /// </summary>
    /// <param name="key">键</param>
    /// <param name="value">值</param>
    public void AddDetail(string key, object value)
    {
        Details[key] = value;
    }

    /// <summary>
    /// 设置错误
    /// </summary>
    /// <param name="message">错误消息</param>
    /// <param name="exception">异常</param>
    public void SetError(string message, Exception? exception = null)
    {
        ErrorMessage = message;
        Exception = exception;
        IsHealthy = false;
        AddIssue(message);
    }

    /// <summary>
    /// 获取健康状态摘要
    /// </summary>
    /// <returns>状态摘要</returns>
    public string GetSummary()
    {
        if (IsHealthy)
        {
            return $"数据库健康 - 响应时间: {ResponseTimeMs:F1}ms";
        }

        var issueCount = Issues.Count;
        var issueText = issueCount == 1 ? Issues[0] : $"{issueCount}个问题";
        return $"数据库异常 - {issueText}";
    }

    /// <summary>
    /// 转换为字符串表示
    /// </summary>
    /// <returns>字符串表示</returns>
    public override string ToString()
    {
        return GetSummary();
    }
}
