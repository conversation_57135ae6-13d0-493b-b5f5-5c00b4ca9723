using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using FlowCustomV1.Core.Models.Executor;
using FlowCustomV1.Core.Interfaces.Scheduling;

namespace FlowCustomV1.Core.Models.Scheduling;

/// <summary>
/// 任务执行信息
/// </summary>
public class TaskExecution
{
    /// <summary>
    /// 任务ID
    /// </summary>
    [Required]
    public string TaskId { get; set; } = string.Empty;

    /// <summary>
    /// 工作流ID
    /// </summary>
    [Required]
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 执行ID
    /// </summary>
    [Required]
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 节点ID（工作流中的节点）
    /// </summary>
    [Required]
    public string NodeId { get; set; } = string.Empty;

    /// <summary>
    /// 执行节点ID（集群中的物理节点）
    /// </summary>
    [Required]
    public string ExecutorNodeId { get; set; } = string.Empty;

    /// <summary>
    /// 任务类型
    /// </summary>
    [Required]
    public string TaskType { get; set; } = string.Empty;

    /// <summary>
    /// 任务名称
    /// </summary>
    public string TaskName { get; set; } = string.Empty;

    /// <summary>
    /// 任务描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 任务优先级
    /// </summary>
    [Range(1, 10)]
    public int Priority { get; set; } = 5;

    /// <summary>
    /// 输入数据
    /// </summary>
    public Dictionary<string, object> InputData { get; set; } = new();

    /// <summary>
    /// 任务配置
    /// </summary>
    public Dictionary<string, object> Configuration { get; set; } = new();

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 计划开始时间
    /// </summary>
    public DateTime? ScheduledStartTime { get; set; }

    /// <summary>
    /// 预期执行时间（毫秒）
    /// </summary>
    public long EstimatedExecutionTimeMs { get; set; }

    /// <summary>
    /// 超时时间（毫秒）
    /// </summary>
    public long TimeoutMs { get; set; } = 300000; // 5分钟默认超时

    /// <summary>
    /// 任务标签
    /// </summary>
    public Dictionary<string, string> Labels { get; set; } = new();

    /// <summary>
    /// 任务元数据
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();

    /// <summary>
    /// 依赖的任务ID列表
    /// </summary>
    public List<string> Dependencies { get; set; } = new();

    /// <summary>
    /// 重试配置
    /// </summary>
    public TaskRetryConfig RetryConfig { get; set; } = new();
}

/// <summary>
/// 任务重试配置
/// </summary>
public class TaskRetryConfig
{
    /// <summary>
    /// 最大重试次数
    /// </summary>
    [Range(0, 10)]
    public int MaxRetries { get; set; } = 3;

    /// <summary>
    /// 重试延迟（毫秒）
    /// </summary>
    [Range(0, 300000)]
    public int RetryDelayMs { get; set; } = 1000;

    /// <summary>
    /// 重试延迟倍数
    /// </summary>
    [Range(1.0, 10.0)]
    public double RetryDelayMultiplier { get; set; } = 2.0;

    /// <summary>
    /// 最大重试延迟（毫秒）
    /// </summary>
    [Range(1000, 3600000)]
    public int MaxRetryDelayMs { get; set; } = 60000;

    /// <summary>
    /// 启用指数退避
    /// </summary>
    public bool EnableExponentialBackoff { get; set; } = true;

    /// <summary>
    /// 可重试的错误类型
    /// </summary>
    public List<string> RetryableErrorTypes { get; set; } = new();
}

/// <summary>
/// 任务执行状态枚举
/// </summary>
public enum TaskExecutionStatus
{
    /// <summary>
    /// 等待中
    /// </summary>
    Pending,

    /// <summary>
    /// 已排队
    /// </summary>
    Queued,

    /// <summary>
    /// 正在初始化
    /// </summary>
    Initializing,

    /// <summary>
    /// 正在运行
    /// </summary>
    Running,

    /// <summary>
    /// 已暂停
    /// </summary>
    Paused,

    /// <summary>
    /// 正在取消
    /// </summary>
    Cancelling,

    /// <summary>
    /// 已取消
    /// </summary>
    Cancelled,

    /// <summary>
    /// 已完成
    /// </summary>
    Completed,

    /// <summary>
    /// 已失败
    /// </summary>
    Failed,

    /// <summary>
    /// 超时
    /// </summary>
    TimedOut,

    /// <summary>
    /// 正在重试
    /// </summary>
    Retrying,

    /// <summary>
    /// 已跳过
    /// </summary>
    Skipped
}

/// <summary>
/// 任务执行状态
/// </summary>
public class TaskExecutionState
{
    /// <summary>
    /// 任务ID
    /// </summary>
    public string TaskId { get; set; } = string.Empty;

    /// <summary>
    /// 工作流ID
    /// </summary>
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 执行ID
    /// </summary>
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 执行节点ID
    /// </summary>
    public string ExecutorNodeId { get; set; } = string.Empty;

    /// <summary>
    /// 节点ID（ExecutorNodeId的别名，保持向后兼容）
    /// </summary>
    public string NodeId
    {
        get => ExecutorNodeId;
        set => ExecutorNodeId = value;
    }

    /// <summary>
    /// 当前状态
    /// </summary>
    public TaskExecutionStatus Status { get; set; } = TaskExecutionStatus.Pending;

    /// <summary>
    /// 上一个状态
    /// </summary>
    public TaskExecutionStatus? PreviousStatus { get; set; }

    /// <summary>
    /// 状态变更时间
    /// </summary>
    public DateTime StatusChangedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 任务进度
    /// </summary>
    public TaskProgress Progress { get; set; } = new();

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime? StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }

    /// <summary>
    /// 执行时长（毫秒）
    /// </summary>
    public long? ExecutionTimeMs => EndTime.HasValue && StartTime.HasValue 
        ? (long)(EndTime.Value - StartTime.Value).TotalMilliseconds 
        : null;

    /// <summary>
    /// 重试次数
    /// </summary>
    public int RetryCount { get; set; } = 0;

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 错误详情
    /// </summary>
    public string? ErrorDetails { get; set; }

    /// <summary>
    /// 输出数据
    /// </summary>
    public Dictionary<string, object> OutputData { get; set; } = new();

    /// <summary>
    /// 执行日志
    /// </summary>
    public List<TaskExecutionLog> Logs { get; set; } = new();

    /// <summary>
    /// 资源使用情况
    /// </summary>
    public FlowCustomV1.Core.Models.Executor.ResourceUsage? ResourceUsage { get; set; }

    /// <summary>
    /// 性能指标
    /// </summary>
    public TaskPerformanceMetrics? PerformanceMetrics { get; set; }

    /// <summary>
    /// 状态历史
    /// </summary>
    public List<TaskStatusHistory> StatusHistory { get; set; } = new();

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 扩展属性
    /// </summary>
    public Dictionary<string, object> ExtendedProperties { get; set; } = new();
}

/// <summary>
/// 任务进度信息
/// </summary>
public class TaskProgress
{
    /// <summary>
    /// 进度百分比（0-100）
    /// </summary>
    [Range(0, 100)]
    public double Percentage { get; set; } = 0;

    /// <summary>
    /// 完成百分比（Percentage的别名，保持向后兼容）
    /// </summary>
    [Range(0, 100)]
    public double PercentComplete
    {
        get => Percentage;
        set => Percentage = value;
    }

    /// <summary>
    /// 当前步骤
    /// </summary>
    public string CurrentStep { get; set; } = string.Empty;

    /// <summary>
    /// 总步骤数
    /// </summary>
    public int TotalSteps { get; set; } = 0;

    /// <summary>
    /// 已完成步骤数
    /// </summary>
    public int CompletedSteps { get; set; } = 0;

    /// <summary>
    /// 进度消息
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 预计剩余时间（毫秒）
    /// </summary>
    public long? EstimatedRemainingTimeMs { get; set; }

    /// <summary>
    /// 处理的项目数
    /// </summary>
    public long ProcessedItems { get; set; } = 0;

    /// <summary>
    /// 总项目数
    /// </summary>
    public long TotalItems { get; set; } = 0;

    /// <summary>
    /// 处理速度（项目/秒）
    /// </summary>
    public double ProcessingRate { get; set; } = 0;

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 详细进度信息
    /// </summary>
    public Dictionary<string, object> Details { get; set; } = new();
}

/// <summary>
/// 任务执行日志
/// </summary>
public class TaskExecutionLog
{
    /// <summary>
    /// 日志ID
    /// </summary>
    public string LogId { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 时间戳
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 日志级别
    /// </summary>
    public LogLevel Level { get; set; } = LogLevel.Information;

    /// <summary>
    /// 日志消息
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 日志类别
    /// </summary>
    public string Category { get; set; } = string.Empty;

    /// <summary>
    /// 异常信息
    /// </summary>
    public string? Exception { get; set; }

    /// <summary>
    /// 上下文数据
    /// </summary>
    public Dictionary<string, object> Context { get; set; } = new();
}

/// <summary>
/// 日志级别枚举
/// </summary>
public enum LogLevel
{
    /// <summary>
    /// 跟踪
    /// </summary>
    Trace,

    /// <summary>
    /// 调试
    /// </summary>
    Debug,

    /// <summary>
    /// 信息
    /// </summary>
    Information,

    /// <summary>
    /// 警告
    /// </summary>
    Warning,

    /// <summary>
    /// 错误
    /// </summary>
    Error,

    /// <summary>
    /// 严重错误
    /// </summary>
    Critical
}

/// <summary>
/// 任务性能指标
/// </summary>
public class TaskPerformanceMetrics
{
    /// <summary>
    /// 启动时间（毫秒）
    /// </summary>
    public long StartupTimeMs { get; set; }

    /// <summary>
    /// 初始化时间（毫秒）
    /// </summary>
    public long InitializationTimeMs { get; set; }

    /// <summary>
    /// 执行时间（毫秒）
    /// </summary>
    public long ExecutionTimeMs { get; set; }

    /// <summary>
    /// 清理时间（毫秒）
    /// </summary>
    public long CleanupTimeMs { get; set; }

    /// <summary>
    /// 总时间（毫秒）
    /// </summary>
    public long TotalTimeMs { get; set; }

    /// <summary>
    /// 吞吐量（操作/秒）
    /// </summary>
    public double ThroughputPerSecond { get; set; }

    /// <summary>
    /// 内存使用峰值（MB）
    /// </summary>
    public long PeakMemoryUsageMB { get; set; }

    /// <summary>
    /// CPU使用率峰值（%）
    /// </summary>
    public double PeakCpuUsagePercent { get; set; }

    /// <summary>
    /// 磁盘I/O总量（MB）
    /// </summary>
    public long TotalDiskIOMB { get; set; }

    /// <summary>
    /// 网络I/O总量（MB）
    /// </summary>
    public long TotalNetworkIOMB { get; set; }

    /// <summary>
    /// 缓存命中率（%）
    /// </summary>
    public double CacheHitRatePercent { get; set; }

    /// <summary>
    /// 错误率（%）
    /// </summary>
    public double ErrorRatePercent { get; set; }
}

/// <summary>
/// 任务状态历史
/// </summary>
public class TaskStatusHistory
{
    /// <summary>
    /// 状态
    /// </summary>
    public TaskExecutionStatus Status { get; set; }

    /// <summary>
    /// 变更时间
    /// </summary>
    public DateTime ChangedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 变更原因
    /// </summary>
    public string Reason { get; set; } = string.Empty;

    /// <summary>
    /// 持续时间（毫秒）
    /// </summary>
    public long? DurationMs { get; set; }

    /// <summary>
    /// 附加信息
    /// </summary>
    public Dictionary<string, object> AdditionalInfo { get; set; } = new();
}

/// <summary>
/// 任务跟踪结果
/// </summary>
public class TaskTrackingResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 任务ID
    /// </summary>
    public string TaskId { get; set; } = string.Empty;

    /// <summary>
    /// 跟踪ID
    /// </summary>
    public string TrackingId { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 开始跟踪时间
    /// </summary>
    public DateTime StartTime { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 跟踪配置
    /// </summary>
    public Dictionary<string, object> TrackingConfig { get; set; } = new();
}

/// <summary>
/// 任务状态更新结果
/// </summary>
public class TaskStatusUpdateResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 任务ID
    /// </summary>
    public string TaskId { get; set; } = string.Empty;

    /// <summary>
    /// 旧状态
    /// </summary>
    public TaskExecutionStatus? OldStatus { get; set; }

    /// <summary>
    /// 新状态
    /// </summary>
    public TaskExecutionStatus NewStatus { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdateTime { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// 任务跟踪停止结果
/// </summary>
public class TaskTrackingStopResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 任务ID
    /// </summary>
    public string TaskId { get; set; } = string.Empty;

    /// <summary>
    /// 跟踪ID
    /// </summary>
    public string TrackingId { get; set; } = string.Empty;

    /// <summary>
    /// 停止时间
    /// </summary>
    public DateTime StopTime { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 总跟踪时间（毫秒）
    /// </summary>
    public long TotalTrackingTimeMs { get; set; }

    /// <summary>
    /// 最终状态
    /// </summary>
    public TaskExecutionStatus FinalStatus { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 跟踪摘要
    /// </summary>
    public Dictionary<string, object> TrackingSummary { get; set; } = new();
}

/// <summary>
/// 任务操作结果
/// </summary>
public class TaskOperationResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 任务ID
    /// </summary>
    public string TaskId { get; set; } = string.Empty;

    /// <summary>
    /// 操作类型
    /// </summary>
    public string OperationType { get; set; } = string.Empty;

    /// <summary>
    /// 操作时间
    /// </summary>
    public DateTime OperationTime { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 执行时间（OperationTime的别名，保持向后兼容）
    /// </summary>
    public DateTime ExecutedAt
    {
        get => OperationTime;
        set => OperationTime = value;
    }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 操作详情
    /// </summary>
    public Dictionary<string, object> Details { get; set; } = new();
}

/// <summary>
/// 任务搜索条件
/// </summary>
public class TaskSearchCriteria
{
    /// <summary>
    /// 任务ID列表
    /// </summary>
    public List<string> TaskIds { get; set; } = new();

    /// <summary>
    /// 单个任务ID（TaskIds的便捷属性，保持向后兼容）
    /// </summary>
    public string? TaskId
    {
        get => TaskIds.FirstOrDefault();
        set
        {
            TaskIds.Clear();
            if (!string.IsNullOrEmpty(value))
                TaskIds.Add(value);
        }
    }

    /// <summary>
    /// 工作流ID列表
    /// </summary>
    public List<string> WorkflowIds { get; set; } = new();

    /// <summary>
    /// 单个工作流ID（WorkflowIds的便捷属性，保持向后兼容）
    /// </summary>
    public string? WorkflowId
    {
        get => WorkflowIds.FirstOrDefault();
        set
        {
            WorkflowIds.Clear();
            if (!string.IsNullOrEmpty(value))
                WorkflowIds.Add(value);
        }
    }

    /// <summary>
    /// 执行节点ID列表
    /// </summary>
    public List<string> ExecutorNodeIds { get; set; } = new();

    /// <summary>
    /// 单个节点ID（ExecutorNodeIds的便捷属性，保持向后兼容）
    /// </summary>
    public string? NodeId
    {
        get => ExecutorNodeIds.FirstOrDefault();
        set
        {
            ExecutorNodeIds.Clear();
            if (!string.IsNullOrEmpty(value))
                ExecutorNodeIds.Add(value);
        }
    }

    /// <summary>
    /// 任务状态列表
    /// </summary>
    public List<TaskExecutionStatus> Statuses { get; set; } = new();

    /// <summary>
    /// 单个任务状态（Statuses的便捷属性，保持向后兼容）
    /// </summary>
    public TaskExecutionStatus? Status
    {
        get => Statuses.FirstOrDefault();
        set
        {
            Statuses.Clear();
            if (value.HasValue)
                Statuses.Add(value.Value);
        }
    }

    /// <summary>
    /// 任务类型列表
    /// </summary>
    public List<string> TaskTypes { get; set; } = new();

    /// <summary>
    /// 优先级范围
    /// </summary>
    public (int Min, int Max)? PriorityRange { get; set; }

    /// <summary>
    /// 创建时间范围
    /// </summary>
    public (DateTime Start, DateTime End)? CreatedTimeRange { get; set; }

    /// <summary>
    /// 开始时间范围
    /// </summary>
    public (DateTime Start, DateTime End)? StartTimeRange { get; set; }

    /// <summary>
    /// 开始时间范围起始（StartTimeRange的便捷属性，保持向后兼容）
    /// </summary>
    public DateTime? StartTimeFrom
    {
        get => StartTimeRange?.Start;
        set
        {
            if (value.HasValue)
            {
                var end = StartTimeRange?.End ?? DateTime.MaxValue;
                StartTimeRange = (value.Value, end);
            }
            else
            {
                StartTimeRange = null;
            }
        }
    }

    /// <summary>
    /// 开始时间范围结束（StartTimeRange的便捷属性，保持向后兼容）
    /// </summary>
    public DateTime? StartTimeTo
    {
        get => StartTimeRange?.End;
        set
        {
            if (value.HasValue)
            {
                var start = StartTimeRange?.Start ?? DateTime.MinValue;
                StartTimeRange = (start, value.Value);
            }
            else
            {
                StartTimeRange = null;
            }
        }
    }

    /// <summary>
    /// 结束时间范围
    /// </summary>
    public (DateTime Start, DateTime End)? EndTimeRange { get; set; }

    /// <summary>
    /// 执行时间范围（毫秒）
    /// </summary>
    public (long Min, long Max)? ExecutionTimeRange { get; set; }

    /// <summary>
    /// 标签过滤器
    /// </summary>
    public Dictionary<string, string> LabelFilters { get; set; } = new();

    /// <summary>
    /// 关键字搜索
    /// </summary>
    public string? Keywords { get; set; }

    /// <summary>
    /// 排序字段
    /// </summary>
    public string SortBy { get; set; } = "CreatedAt";

    /// <summary>
    /// 排序方向
    /// </summary>
    public SortDirection SortDirection { get; set; } = SortDirection.Descending;

    /// <summary>
    /// 是否降序排序（SortDirection的便捷属性，保持向后兼容）
    /// </summary>
    public bool SortDescending
    {
        get => SortDirection == SortDirection.Descending;
        set => SortDirection = value ? SortDirection.Descending : SortDirection.Ascending;
    }

    /// <summary>
    /// 页码（从1开始）
    /// </summary>
    [Range(1, int.MaxValue)]
    public int PageNumber { get; set; } = 1;

    /// <summary>
    /// 页大小
    /// </summary>
    [Range(1, 1000)]
    public int PageSize { get; set; } = 50;

    /// <summary>
    /// 是否包含日志
    /// </summary>
    public bool IncludeLogs { get; set; } = false;

    /// <summary>
    /// 是否包含性能指标
    /// </summary>
    public bool IncludePerformanceMetrics { get; set; } = false;
}

/// <summary>
/// 排序方向枚举
/// </summary>
public enum SortDirection
{
    /// <summary>
    /// 升序
    /// </summary>
    Ascending,

    /// <summary>
    /// 降序
    /// </summary>
    Descending
}

/// <summary>
/// 任务搜索结果
/// </summary>
public class TaskSearchResult
{
    /// <summary>
    /// 搜索条件
    /// </summary>
    public TaskSearchCriteria Criteria { get; set; } = null!;

    /// <summary>
    /// 总记录数
    /// </summary>
    public long TotalCount { get; set; }

    /// <summary>
    /// 总页数
    /// </summary>
    public int TotalPages => (int)Math.Ceiling((double)TotalCount / Criteria.PageSize);

    /// <summary>
    /// 当前页码
    /// </summary>
    public int CurrentPage => Criteria.PageNumber;

    /// <summary>
    /// 是否有下一页
    /// </summary>
    public bool HasNextPage => CurrentPage < TotalPages;

    /// <summary>
    /// 是否有上一页
    /// </summary>
    public bool HasPreviousPage => CurrentPage > 1;

    /// <summary>
    /// 搜索结果
    /// </summary>
    public List<TaskExecutionState> Results { get; set; } = new();

    /// <summary>
    /// 搜索耗时（毫秒）
    /// </summary>
    public long SearchTimeMs { get; set; }

    /// <summary>
    /// 搜索时间
    /// </summary>
    public DateTime SearchTime { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 聚合统计
    /// </summary>
    public Dictionary<string, object> Aggregations { get; set; } = new();
}
