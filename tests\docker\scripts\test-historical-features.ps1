# FlowCustomV1 历史功能测试脚本 (简化版)
# 版本: v0.0.1.8

$TestResults = @()
$StartTime = Get-Date

function Write-TestLog {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $color = switch ($Level) {
        "ERROR" { "Red" }
        "WARN" { "Yellow" }
        "SUCCESS" { "Green" }
        "SECTION" { "Cyan" }
        default { "White" }
    }
    Write-Host "[$timestamp] [$Level] $Message" -ForegroundColor $color
}

function Add-TestResult {
    param(
        [string]$TestName,
        [bool]$Passed,
        [string]$Details = "",
        [string]$Version = ""
    )
    
    $script:TestResults += [PSCustomObject]@{
        TestName = $TestName
        Version = $Version
        Passed = $Passed
        Details = $Details
        Timestamp = Get-Date
    }
    
    $status = if ($Passed) { "PASS" } else { "FAIL" }
    $versionInfo = if ($Version) { " [$Version]" } else { "" }
    Write-TestLog "$status - $TestName$versionInfo $(if($Details) { "($Details)" })" $(if($Passed) { "SUCCESS" } else { "ERROR" })
}

function Test-ApiEndpoint {
    param([string]$Url, [string]$Method = "GET", [string]$Body = $null)
    
    try {
        if ($Body) {
            $response = Invoke-WebRequest -Uri $Url -Method $Method -Body $Body -ContentType "application/json" -TimeoutSec 10 -UseBasicParsing
        } else {
            $response = Invoke-WebRequest -Uri $Url -Method $Method -TimeoutSec 10 -UseBasicParsing
        }
        return @{
            Success = $response.StatusCode -eq 200
            StatusCode = $response.StatusCode
            Content = $response.Content
        }
    }
    catch {
        return @{
            Success = $false
            StatusCode = 0
            Content = $_.Exception.Message
        }
    }
}

function Test-ContainerLogs {
    param([string]$ContainerName, [string]$Pattern)
    
    try {
        $logs = docker logs $ContainerName 2>&1 | Select-String $Pattern
        return $logs.Count -gt 0
    }
    catch {
        return $false
    }
}

Write-TestLog "Starting FlowCustomV1 Historical Features Test" "SECTION"

try {
    # 设置工作目录
    $testDir = Split-Path -Parent $PSScriptRoot
    Set-Location $testDir

    # 检查Docker环境
    Write-TestLog "Checking Docker environment..." "INFO"
    $containers = docker ps --format "{{.Names}}" | Where-Object { $_ -match "flowcustom-test" }
    Write-TestLog "Found $($containers.Count) FlowCustom containers running" "INFO"

    # ==================== v0.0.0.10 - RESTful API 基础功能测试 ====================
    Write-TestLog "Testing v0.0.0.10 - RESTful API Basic Functions" "SECTION"
    
    # 测试基础API端点
    $workflowsTest = Test-ApiEndpoint "http://localhost:5001/api/workflows"
    Add-TestResult "GET /api/workflows" $workflowsTest.Success $workflowsTest.StatusCode "v0.0.0.10"
    
    # 测试工作流创建
    $createWorkflowBody = '{"name":"Test Workflow","description":"Test workflow","definition":{"nodes":[],"connections":[]}}'
    $createTest = Test-ApiEndpoint "http://localhost:5001/api/workflows" "POST" $createWorkflowBody
    Add-TestResult "POST /api/workflows" $createTest.Success $createTest.StatusCode "v0.0.0.10"
    
    # 测试工作流验证
    $validateTest = Test-ApiEndpoint "http://localhost:5001/api/workflows/validate" "POST" $createWorkflowBody
    Add-TestResult "POST /api/workflows/validate" $validateTest.Success $validateTest.StatusCode "v0.0.0.10"

    # ==================== v0.0.1.1 - NATS消息路由功能测试 ====================
    Write-TestLog "Testing v0.0.1.1 - NATS Message Routing Functions" "SECTION"
    
    # 测试NATS健康状态
    $natsHealthy = Test-ApiEndpoint "http://localhost:8222/healthz"
    Add-TestResult "NATS Server Health" $natsHealthy.Success $natsHealthy.Content "v0.0.1.1"
    
    # 测试NATS连接信息
    $natsConnections = Test-ApiEndpoint "http://localhost:8222/connz"
    Add-TestResult "NATS Connections" $natsConnections.Success "Connection info" "v0.0.1.1"
    
    # 检查应用日志中的NATS消息
    $masterNatsLogs = Test-ContainerLogs "flowcustom-test-master" "NATS"
    Add-TestResult "Master Node NATS Logs" $masterNatsLogs "NATS logs found" "v0.0.1.1"
    
    $workerNatsLogs = Test-ContainerLogs "flowcustom-test-worker" "NATS"
    Add-TestResult "Worker Node NATS Logs" $workerNatsLogs "NATS logs found" "v0.0.1.1"
    
    # 测试消息发布功能
    $masterMessageLogs = Test-ContainerLogs "flowcustom-test-master" "Message.*publish"
    Add-TestResult "Message Publishing" $masterMessageLogs "Message publishing logs" "v0.0.1.1"

    # ==================== v0.0.1.3 - 节点服务发现功能测试 ====================
    Write-TestLog "Testing v0.0.1.3 - Node Discovery Functions" "SECTION"
    
    # 测试集群API
    $clusterTest = Test-ApiEndpoint "http://localhost:5001/api/cluster/status"
    Add-TestResult "Cluster Status API" $clusterTest.Success $clusterTest.StatusCode "v0.0.1.3"
    
    # 测试节点列表API
    $nodesTest = Test-ApiEndpoint "http://localhost:5001/api/nodes"
    Add-TestResult "Nodes List API" $nodesTest.Success $nodesTest.StatusCode "v0.0.1.3"
    
    # 检查节点注册日志
    $masterRegisterLogs = Test-ContainerLogs "flowcustom-test-master" "register"
    Add-TestResult "Master Node Registration" $masterRegisterLogs "Registration logs" "v0.0.1.3"
    
    $workerRegisterLogs = Test-ContainerLogs "flowcustom-test-worker" "register"
    Add-TestResult "Worker Node Registration" $workerRegisterLogs "Registration logs" "v0.0.1.3"
    
    # 检查心跳机制
    $masterHeartbeatLogs = Test-ContainerLogs "flowcustom-test-master" "heartbeat"
    Add-TestResult "Master Node Heartbeat" $masterHeartbeatLogs "Heartbeat logs" "v0.0.1.3"
    
    $workerHeartbeatLogs = Test-ContainerLogs "flowcustom-test-worker" "heartbeat"
    Add-TestResult "Worker Node Heartbeat" $workerHeartbeatLogs "Heartbeat logs" "v0.0.1.3"

    # ==================== v0.0.1.4 - Designer节点服务功能测试 ====================
    Write-TestLog "Testing v0.0.1.4 - Designer Node Functions" "SECTION"
    
    # 测试工作流设计器API
    $designerTest = Test-ApiEndpoint "http://localhost:5001/api/designer/workflows"
    Add-TestResult "Designer Workflows API" $designerTest.Success $designerTest.StatusCode "v0.0.1.4"
    
    # 测试模板管理API
    $templatesTest = Test-ApiEndpoint "http://localhost:5001/api/designer/templates"
    Add-TestResult "Template Management API" $templatesTest.Success $templatesTest.StatusCode "v0.0.1.4"
    
    # 测试协作API
    $collaborationTest = Test-ApiEndpoint "http://localhost:5001/api/collaboration/sessions"
    Add-TestResult "Collaboration API" $collaborationTest.Success $collaborationTest.StatusCode "v0.0.1.4"

    # ==================== v0.0.1.5 - Validator节点服务功能测试 ====================
    Write-TestLog "Testing v0.0.1.5 - Validator Node Functions" "SECTION"
    
    # 测试验证器API
    $validatorBody = '{"workflowId":"test"}'
    $validatorTest = Test-ApiEndpoint "http://localhost:5001/api/validator/workflows/validate" "POST" $validatorBody
    Add-TestResult "Validator API" $validatorTest.Success $validatorTest.StatusCode "v0.0.1.5"
    
    # 测试验证缓存API
    $cacheTest = Test-ApiEndpoint "http://localhost:5001/api/validator/cache/stats"
    Add-TestResult "Validation Cache API" $cacheTest.Success $cacheTest.StatusCode "v0.0.1.5"
    
    # 测试验证规则API
    $rulesTest = Test-ApiEndpoint "http://localhost:5001/api/validator/rules"
    Add-TestResult "Validation Rules API" $rulesTest.Success $rulesTest.StatusCode "v0.0.1.5"

    # ==================== v0.0.1.6 - Executor节点服务功能测试 ====================
    Write-TestLog "Testing v0.0.1.6 - Executor Node Functions" "SECTION"
    
    # 测试执行器容量API
    $capacityTest = Test-ApiEndpoint "http://localhost:5001/api/executor/capacity"
    Add-TestResult "Executor Capacity API" $capacityTest.Success $capacityTest.StatusCode "v0.0.1.6"
    
    # 测试执行状态API
    $executionStatusTest = Test-ApiEndpoint "http://localhost:5001/api/executor/executions"
    Add-TestResult "Execution Status API" $executionStatusTest.Success $executionStatusTest.StatusCode "v0.0.1.6"

    # ==================== v0.0.1.7 - 分布式任务调度系统测试 ====================
    Write-TestLog "Testing v0.0.1.7 - Distributed Task Scheduling System" "SECTION"
    
    # 测试任务分发API
    $taskBody = '{"taskType":"test"}'
    $taskDistributionTest = Test-ApiEndpoint "http://localhost:5001/api/tasks/distribute" "POST" $taskBody
    Add-TestResult "Task Distribution API" $taskDistributionTest.Success $taskDistributionTest.StatusCode "v0.0.1.7"
    
    # 测试负载均衡API
    $loadBalancingTest = Test-ApiEndpoint "http://localhost:5001/api/tasks/balance"
    Add-TestResult "Load Balancing API" $loadBalancingTest.Success $loadBalancingTest.StatusCode "v0.0.1.7"
    
    # 测试任务跟踪API
    $taskTrackingTest = Test-ApiEndpoint "http://localhost:5001/api/tasks/tracking"
    Add-TestResult "Task Tracking API" $taskTrackingTest.Success $taskTrackingTest.StatusCode "v0.0.1.7"

    # ==================== 系统集成测试 ====================
    Write-TestLog "Testing System Integration" "SECTION"
    
    # 测试完整的工作流执行流程
    if ($createTest.Success) {
        try {
            $workflowResponse = $createTest.Content | ConvertFrom-Json
            if ($workflowResponse.id) {
                $workflowId = $workflowResponse.id
                
                # 启动工作流执行
                $executeTest = Test-ApiEndpoint "http://localhost:5001/api/executions/start/$workflowId" "POST" "{}"
                Add-TestResult "Workflow Execution Start" $executeTest.Success $executeTest.StatusCode "Integration"
                
                if ($executeTest.Success) {
                    # 等待执行完成
                    Start-Sleep -Seconds 3
                    
                    # 检查执行历史
                    $historyTest = Test-ApiEndpoint "http://localhost:5001/api/executions/workflow/$workflowId"
                    Add-TestResult "Workflow Execution History" $historyTest.Success $historyTest.StatusCode "Integration"
                }
            }
        }
        catch {
            Add-TestResult "End-to-End Workflow Test" $false $_.Exception.Message "Integration"
        }
    }

}
catch {
    Write-TestLog "Critical error: $($_.Exception.Message)" "ERROR"
}
finally {
    # 生成测试报告
    $EndTime = Get-Date
    $TotalDuration = ($EndTime - $StartTime).TotalSeconds
    
    # 统计结果
    $TotalTests = $TestResults.Count
    $PassedTests = ($TestResults | Where-Object { $_.Passed }).Count
    $FailedTests = $TotalTests - $PassedTests
    $SuccessRate = if ($TotalTests -gt 0) { [math]::Round(($PassedTests / $TotalTests) * 100, 2) } else { 0 }
    
    # 按版本统计
    $VersionStats = $TestResults | Group-Object Version | ForEach-Object {
        $versionPassed = ($_.Group | Where-Object { $_.Passed }).Count
        $versionTotal = $_.Group.Count
        $versionRate = if ($versionTotal -gt 0) { [math]::Round(($versionPassed / $versionTotal) * 100, 2) } else { 0 }
        
        [PSCustomObject]@{
            Version = $_.Name
            Total = $versionTotal
            Passed = $versionPassed
            Failed = $versionTotal - $versionPassed
            SuccessRate = $versionRate
        }
    }
    
    # 输出测试报告
    Write-Host "`n" + "="*80 -ForegroundColor Cyan
    Write-Host "FlowCustomV1 Historical Features Test Report" -ForegroundColor Cyan
    Write-Host "="*80 -ForegroundColor Cyan
    Write-Host "Test Duration: $([math]::Round($TotalDuration, 2)) seconds" -ForegroundColor White
    Write-Host "Total Tests: $TotalTests" -ForegroundColor White
    Write-Host "Passed Tests: $PassedTests" -ForegroundColor Green
    Write-Host "Failed Tests: $FailedTests" -ForegroundColor Red
    Write-Host "Overall Success Rate: $SuccessRate%" -ForegroundColor $(if($SuccessRate -ge 60) { "Green" } else { "Red" })
    Write-Host ""
    
    # 版本统计
    Write-Host "Results by Version:" -ForegroundColor Yellow
    Write-Host "-"*80 -ForegroundColor Yellow
    foreach ($stat in $VersionStats | Sort-Object Version) {
        $color = if ($stat.SuccessRate -ge 50) { "Green" } elseif ($stat.SuccessRate -ge 25) { "Yellow" } else { "Red" }
        Write-Host "$($stat.Version): $($stat.Passed)/$($stat.Total) ($($stat.SuccessRate)%)" -ForegroundColor $color
    }
    Write-Host ""
    
    # 详细结果
    Write-Host "Detailed Test Results:" -ForegroundColor Yellow
    Write-Host "-"*80 -ForegroundColor Yellow
    
    foreach ($result in $TestResults | Sort-Object Version, TestName) {
        $status = if ($result.Passed) { "PASS" } else { "FAIL" }
        $versionInfo = if ($result.Version) { "[$($result.Version)]" } else { "[System]" }
        Write-Host "$status $versionInfo $($result.TestName)" -ForegroundColor $(if($result.Passed) { "Green" } else { "Red" })
        if ($result.Details -and -not $result.Passed) {
            Write-Host "    Details: $($result.Details)" -ForegroundColor Gray
        }
    }
    
    Write-Host "`n" + "="*80 -ForegroundColor Cyan
    
    # 返回退出码
    if ($SuccessRate -ge 60) {
        Write-TestLog "Historical features testing completed successfully!" "SUCCESS"
        exit 0
    } else {
        Write-TestLog "Historical features testing completed with issues" "WARN"
        exit 1
    }
}
