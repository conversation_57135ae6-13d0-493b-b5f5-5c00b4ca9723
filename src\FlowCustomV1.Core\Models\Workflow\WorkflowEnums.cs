namespace FlowCustomV1.Core.Models.Workflow;

/// <summary>
/// 工作流执行状态
/// </summary>
public enum WorkflowExecutionState
{
    /// <summary>
    /// 未开始
    /// </summary>
    NotStarted = 0,

    /// <summary>
    /// 运行中
    /// </summary>
    Running = 1,

    /// <summary>
    /// 已暂停
    /// </summary>
    Paused = 2,

    /// <summary>
    /// 已完成
    /// </summary>
    Completed = 3,

    /// <summary>
    /// 已失败
    /// </summary>
    Failed = 4,

    /// <summary>
    /// 已取消
    /// </summary>
    Cancelled = 5,

    /// <summary>
    /// 超时
    /// </summary>
    Timeout = 6,

    /// <summary>
    /// 等待中
    /// </summary>
    Waiting = 7
}

/// <summary>
/// 节点执行状态
/// </summary>
public enum NodeExecutionState
{
    /// <summary>
    /// 未开始
    /// </summary>
    NotStarted = 0,

    /// <summary>
    /// 等待中
    /// </summary>
    Waiting = 1,

    /// <summary>
    /// 运行中
    /// </summary>
    Running = 2,

    /// <summary>
    /// 已完成
    /// </summary>
    Completed = 3,

    /// <summary>
    /// 已失败
    /// </summary>
    Failed = 4,

    /// <summary>
    /// 已跳过
    /// </summary>
    Skipped = 5,

    /// <summary>
    /// 已取消
    /// </summary>
    Cancelled = 6,

    /// <summary>
    /// 超时
    /// </summary>
    Timeout = 7,

    /// <summary>
    /// 重试中
    /// </summary>
    Retrying = 8
}

/// <summary>
/// 参数数据类型
/// </summary>
public enum ParameterDataType
{
    /// <summary>
    /// 字符串
    /// </summary>
    String = 0,

    /// <summary>
    /// 整数
    /// </summary>
    Integer = 1,

    /// <summary>
    /// 浮点数
    /// </summary>
    Double = 2,

    /// <summary>
    /// 布尔值
    /// </summary>
    Boolean = 3,

    /// <summary>
    /// 日期时间
    /// </summary>
    DateTime = 4,

    /// <summary>
    /// 对象
    /// </summary>
    Object = 5,

    /// <summary>
    /// 数组
    /// </summary>
    Array = 6,

    /// <summary>
    /// 文件
    /// </summary>
    File = 7,

    /// <summary>
    /// JSON
    /// </summary>
    Json = 8,

    /// <summary>
    /// XML
    /// </summary>
    Xml = 9
}

/// <summary>
/// 连接条件类型
/// </summary>
public enum ConnectionConditionType
{
    /// <summary>
    /// 无条件（总是执行）
    /// </summary>
    Always = 0,

    /// <summary>
    /// 成功时执行
    /// </summary>
    OnSuccess = 1,

    /// <summary>
    /// 失败时执行
    /// </summary>
    OnFailure = 2,

    /// <summary>
    /// 条件表达式
    /// </summary>
    Expression = 3,

    /// <summary>
    /// 自定义条件
    /// </summary>
    Custom = 4
}

/// <summary>
/// 节点类型分类
/// </summary>
public enum NodeTypeCategory
{
    /// <summary>
    /// 触发节点
    /// </summary>
    Trigger = 0,

    /// <summary>
    /// 处理节点
    /// </summary>
    Process = 1,

    /// <summary>
    /// 控制节点
    /// </summary>
    Control = 2,

    /// <summary>
    /// 输出节点
    /// </summary>
    Output = 3,

    /// <summary>
    /// 集成节点
    /// </summary>
    Integration = 4,

    /// <summary>
    /// 自定义节点
    /// </summary>
    Custom = 5
}

/// <summary>
/// 重试策略类型
/// </summary>
public enum RetryStrategyType
{
    /// <summary>
    /// 不重试
    /// </summary>
    None = 0,

    /// <summary>
    /// 固定间隔
    /// </summary>
    FixedInterval = 1,

    /// <summary>
    /// 指数退避
    /// </summary>
    ExponentialBackoff = 2,

    /// <summary>
    /// 线性退避
    /// </summary>
    LinearBackoff = 3,

    /// <summary>
    /// 自定义策略
    /// </summary>
    Custom = 4
}

/// <summary>
/// 工作流优先级
/// </summary>
public enum WorkflowPriority
{
    /// <summary>
    /// 低优先级
    /// </summary>
    Low = 0,

    /// <summary>
    /// 普通优先级
    /// </summary>
    Normal = 1,

    /// <summary>
    /// 高优先级
    /// </summary>
    High = 2,

    /// <summary>
    /// 紧急优先级
    /// </summary>
    Critical = 3
}

/// <summary>
/// 执行模式
/// </summary>
public enum ExecutionMode
{
    /// <summary>
    /// 同步执行
    /// </summary>
    Synchronous = 0,

    /// <summary>
    /// 异步执行
    /// </summary>
    Asynchronous = 1,

    /// <summary>
    /// 并行执行
    /// </summary>
    Parallel = 2,

    /// <summary>
    /// 顺序执行
    /// </summary>
    Sequential = 3
}

/// <summary>
/// 工作流发布状态
/// </summary>
public enum PublishStatus
{
    /// <summary>
    /// 草稿
    /// </summary>
    Draft = 0,

    /// <summary>
    /// 已发布
    /// </summary>
    Published = 1,

    /// <summary>
    /// 已弃用
    /// </summary>
    Deprecated = 2,

    /// <summary>
    /// 已归档
    /// </summary>
    Archived = 3
}

/// <summary>
/// WorkflowExecutionState扩展方法
/// </summary>
public static class WorkflowExecutionStateExtensions
{
    /// <summary>
    /// 判断工作流是否已完成（包括成功、失败、取消、超时）
    /// </summary>
    /// <param name="state">工作流执行状态</param>
    /// <returns>是否已完成</returns>
    public static bool IsCompleted(this WorkflowExecutionState state)
    {
        return state == WorkflowExecutionState.Completed ||
               state == WorkflowExecutionState.Failed ||
               state == WorkflowExecutionState.Cancelled ||
               state == WorkflowExecutionState.Timeout;
    }

    /// <summary>
    /// 判断工作流是否正在运行
    /// </summary>
    /// <param name="state">工作流执行状态</param>
    /// <returns>是否正在运行</returns>
    public static bool IsRunning(this WorkflowExecutionState state)
    {
        return state == WorkflowExecutionState.Running;
    }

    /// <summary>
    /// 判断工作流是否成功完成
    /// </summary>
    /// <param name="state">工作流执行状态</param>
    /// <returns>是否成功完成</returns>
    public static bool IsSuccessful(this WorkflowExecutionState state)
    {
        return state == WorkflowExecutionState.Completed;
    }
}
