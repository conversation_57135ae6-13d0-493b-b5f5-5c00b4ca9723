using FlowCustomV1.Core.Interfaces.Executor;
using FlowCustomV1.Core.Models.Executor;

namespace FlowCustomV1.Api.Services;

/// <summary>
/// 工作流执行器后台服务
/// 负责启动和管理工作流执行器服务的生命周期
/// </summary>
public class ExecutorHostedService : BackgroundService
{
    private readonly ILogger<ExecutorHostedService> _logger;
    private readonly IServiceProvider _serviceProvider;
    private IWorkflowExecutorService? _executorService;
    private IServiceScope? _serviceScope;

    /// <summary>
    /// 初始化工作流执行器后台服务
    /// </summary>
    public ExecutorHostedService(
        ILogger<ExecutorHostedService> logger,
        IServiceProvider serviceProvider)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
    }

    /// <summary>
    /// 启动后台服务
    /// </summary>
    public override async Task StartAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Starting ExecutorHostedService...");

        try
        {
            // 创建一个长期存在的 scope，在服务生命周期内保持
            _serviceScope = _serviceProvider.CreateScope();
            _executorService = _serviceScope.ServiceProvider.GetRequiredService<IWorkflowExecutorService>();

            // 订阅事件
            SubscribeToEvents();

            // 启动工作流执行器服务
            await _executorService.StartAsync(cancellationToken);

            _logger.LogInformation("ExecutorHostedService started successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start ExecutorHostedService");
            _serviceScope?.Dispose();
            _serviceScope = null;
            throw;
        }

        await base.StartAsync(cancellationToken);
    }

    /// <summary>
    /// 停止后台服务
    /// </summary>
    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Stopping ExecutorHostedService...");

        try
        {
            if (_executorService != null)
            {
                // 取消订阅事件
                UnsubscribeFromEvents();

                // 停止工作流执行器服务
                await _executorService.StopAsync(cancellationToken);
            }

            _logger.LogInformation("ExecutorHostedService stopped successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while stopping ExecutorHostedService");
        }
        finally
        {
            _serviceScope?.Dispose();
            _serviceScope = null;
            _executorService = null;
        }

        await base.StopAsync(cancellationToken);
    }

    /// <summary>
    /// 执行后台任务
    /// </summary>
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogDebug("ExecutorHostedService is running");

        try
        {
            // 保持服务运行，直到取消
            while (!stoppingToken.IsCancellationRequested)
            {
                // 定期检查服务状态
                await CheckServiceHealthAsync(stoppingToken);

                // 等待一段时间再次检查
                await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
            }
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("ExecutorHostedService execution was cancelled");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ExecutorHostedService execution");
        }
    }

    /// <summary>
    /// 订阅事件
    /// </summary>
    private void SubscribeToEvents()
    {
        if (_executorService != null)
        {
            _executorService.ExecutionStarted += OnExecutionStarted;
            _executorService.ExecutionCompleted += OnExecutionCompleted;
            _executorService.ExecutionFailed += OnExecutionFailed;
            _executorService.ExecutionStatusChanged += OnExecutionStatusChanged;
        }
    }

    /// <summary>
    /// 取消订阅事件
    /// </summary>
    private void UnsubscribeFromEvents()
    {
        if (_executorService != null)
        {
            _executorService.ExecutionStarted -= OnExecutionStarted;
            _executorService.ExecutionCompleted -= OnExecutionCompleted;
            _executorService.ExecutionFailed -= OnExecutionFailed;
            _executorService.ExecutionStatusChanged -= OnExecutionStatusChanged;
        }
    }

    /// <summary>
    /// 检查服务健康状态
    /// </summary>
    private async Task CheckServiceHealthAsync(CancellationToken cancellationToken)
    {
        try
        {
            if (_executorService != null)
            {
                var status = _executorService.GetStatus();
                _logger.LogDebug("Executor service status: {Status}", status);

                // 如果服务状态异常，记录警告
                if (status == ExecutorServiceStatus.Error)
                {
                    _logger.LogWarning("Executor service is in error state");
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking executor service health");
        }

        await Task.CompletedTask;
    }

    #region 事件处理

    /// <summary>
    /// 处理执行开始事件
    /// </summary>
    private void OnExecutionStarted(object? sender, ExecutionStartedEventArgs e)
    {
        _logger.LogInformation("Workflow execution started: {ExecutionId} for workflow {WorkflowId}", 
            e.ExecutionId, e.WorkflowId);
    }

    /// <summary>
    /// 处理执行完成事件
    /// </summary>
    private void OnExecutionCompleted(object? sender, ExecutionCompletedEventArgs e)
    {
        _logger.LogInformation("Workflow execution completed: {ExecutionId} with success: {IsSuccess}",
            e.ExecutionId, e.IsSuccess);
    }

    /// <summary>
    /// 处理执行失败事件
    /// </summary>
    private void OnExecutionFailed(object? sender, ExecutionFailedEventArgs e)
    {
        _logger.LogError("Workflow execution failed: {ExecutionId} with error: {Error}", 
            e.ExecutionId, e.ErrorMessage);
    }

    /// <summary>
    /// 处理执行状态变更事件
    /// </summary>
    private void OnExecutionStatusChanged(object? sender, ExecutionStatusChangedEventArgs e)
    {
        _logger.LogInformation("Workflow execution status changed: {ExecutionId} from {OldState} to {NewState}",
            e.ExecutionId, e.OldState, e.NewState);
    }

    #endregion
}
