using FlowCustomV1.Core.Interfaces.Executor;
using FlowCustomV1.Core.Models.Executor;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;

namespace FlowCustomV1.Infrastructure.Services.Executor;

/// <summary>
/// 执行容量管理器实现
/// 负责管理节点的执行容量和资源分配
/// </summary>
public class ExecutionCapacityManager : IExecutionCapacityManager
{
    private readonly ILogger<ExecutionCapacityManager> _logger;
    
    // 容量配置
    private CapacityLimits _capacityLimits = new();
    
    // 资源预留
    private readonly ConcurrentDictionary<string, ResourceReservation> _resourceReservations = new();
    
    // 资源使用历史
    private readonly ConcurrentQueue<ResourceUsageSnapshot> _usageHistory = new();
    private readonly object _historyLock = new();
    
    // 性能监控（简化实现）
    private readonly DateTime _startTime = DateTime.UtcNow;
    
    // 统计信息
    private long _totalReservations = 0;
    private long _successfulReservations = 0;
    private long _failedReservations = 0;

    public ExecutionCapacityManager(ILogger<ExecutionCapacityManager> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        
        // 简化实现，不使用性能计数器
        _logger.LogInformation("ExecutionCapacityManager initialized with simplified performance monitoring");
    }

    /// <inheritdoc />
    public ExecutionCapacity GetCurrentCapacity()
    {
        try
        {
            var currentExecutions = _resourceReservations.Count;
            var cpuUsage = GetCpuUsage();
            var memoryUsage = GetMemoryUsage();
            var diskUsage = GetDiskUsage();
            var networkUsage = GetNetworkUsage();
            
            // 计算负载评分（0-100，越低越好）
            var loadScore = CalculateLoadScore(cpuUsage, memoryUsage, diskUsage, networkUsage, currentExecutions);

            return new ExecutionCapacity
            {
                NodeId = Environment.MachineName,
                MaxConcurrentExecutions = _capacityLimits.MaxConcurrentExecutions,
                CurrentExecutions = currentExecutions,
                CpuUsagePercent = cpuUsage,
                MemoryUsagePercent = memoryUsage,
                DiskUsagePercent = diskUsage,
                NetworkUsagePercent = networkUsage,
                LoadScore = loadScore,
                LastUpdated = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get current capacity");
            throw;
        }
    }

    /// <inheritdoc />
    public bool CanAcceptExecution(ResourceUsage estimatedResourceUsage)
    {
        ArgumentNullException.ThrowIfNull(estimatedResourceUsage);

        try
        {
            var currentCapacity = GetCurrentCapacity();
            
            // 检查并发执行数限制
            if (currentCapacity.CurrentExecutions >= _capacityLimits.MaxConcurrentExecutions)
            {
                _logger.LogDebug("Cannot accept execution: max concurrent executions reached ({Current}/{Max})",
                    currentCapacity.CurrentExecutions, _capacityLimits.MaxConcurrentExecutions);
                return false;
            }

            // 检查CPU使用率
            var projectedCpuUsage = currentCapacity.CpuUsagePercent + (estimatedResourceUsage.CpuCores * 10);
            if (projectedCpuUsage > _capacityLimits.MaxCpuUsagePercent)
            {
                _logger.LogDebug("Cannot accept execution: CPU usage would exceed limit ({Projected}% > {Limit}%)",
                    projectedCpuUsage, _capacityLimits.MaxCpuUsagePercent);
                return false;
            }

            // 检查内存使用率
            var projectedMemoryUsage = currentCapacity.MemoryUsagePercent + (estimatedResourceUsage.MemoryMB / 1024.0 * 10);
            if (projectedMemoryUsage > _capacityLimits.MaxMemoryUsagePercent)
            {
                _logger.LogDebug("Cannot accept execution: Memory usage would exceed limit ({Projected}% > {Limit}%)",
                    projectedMemoryUsage, _capacityLimits.MaxMemoryUsagePercent);
                return false;
            }

            // 检查负载评分
            if (currentCapacity.LoadScore > _capacityLimits.MaxLoadScore)
            {
                _logger.LogDebug("Cannot accept execution: Load score exceeds limit ({Current} > {Limit})",
                    currentCapacity.LoadScore, _capacityLimits.MaxLoadScore);
                return false;
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to check if can accept execution");
            return false;
        }
    }

    /// <inheritdoc />
    public bool ReserveResources(string executionId, ResourceUsage resourceUsage)
    {
        ArgumentException.ThrowIfNullOrEmpty(executionId);
        ArgumentNullException.ThrowIfNull(resourceUsage);

        try
        {
            Interlocked.Increment(ref _totalReservations);

            if (!CanAcceptExecution(resourceUsage))
            {
                Interlocked.Increment(ref _failedReservations);
                return false;
            }

            var reservation = new ResourceReservation
            {
                ExecutionId = executionId,
                ResourceUsage = resourceUsage,
                ReservedAt = DateTime.UtcNow
            };

            if (_resourceReservations.TryAdd(executionId, reservation))
            {
                Interlocked.Increment(ref _successfulReservations);
                _logger.LogDebug("Resources reserved for execution {ExecutionId}: CPU={CpuCores}, Memory={MemoryMB}MB",
                    executionId, resourceUsage.CpuCores, resourceUsage.MemoryMB);
                return true;
            }

            Interlocked.Increment(ref _failedReservations);
            _logger.LogWarning("Failed to reserve resources for execution {ExecutionId}: already exists", executionId);
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to reserve resources for execution {ExecutionId}", executionId);
            Interlocked.Increment(ref _failedReservations);
            return false;
        }
    }

    /// <inheritdoc />
    public bool ReleaseResources(string executionId)
    {
        ArgumentException.ThrowIfNullOrEmpty(executionId);

        try
        {
            if (_resourceReservations.TryRemove(executionId, out var reservation))
            {
                _logger.LogDebug("Resources released for execution {ExecutionId}: CPU={CpuCores}, Memory={MemoryMB}MB",
                    executionId, reservation.ResourceUsage.CpuCores, reservation.ResourceUsage.MemoryMB);
                return true;
            }

            _logger.LogDebug("No resources found to release for execution {ExecutionId}", executionId);
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to release resources for execution {ExecutionId}", executionId);
            return false;
        }
    }

    /// <inheritdoc />
    public void UpdateResourceUsage(string executionId, ResourceUsage actualUsage)
    {
        ArgumentException.ThrowIfNullOrEmpty(executionId);
        ArgumentNullException.ThrowIfNull(actualUsage);

        try
        {
            if (_resourceReservations.TryGetValue(executionId, out var reservation))
            {
                reservation.ActualUsage = actualUsage;
                reservation.UpdatedAt = DateTime.UtcNow;
                
                _logger.LogDebug("Resource usage updated for execution {ExecutionId}", executionId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update resource usage for execution {ExecutionId}", executionId);
        }
    }

    /// <inheritdoc />
    public NodeLoadInfo GetNodeLoadInfo()
    {
        try
        {
            var capacity = GetCurrentCapacity();
            var statistics = GetResourceUsageStatistics();

            return new NodeLoadInfo
            {
                NodeId = Environment.MachineName,
                NodeName = Environment.MachineName,
                NodeRole = "Executor",
                CpuCores = Environment.ProcessorCount,
                TotalMemoryMb = GetTotalMemoryMb(),
                AvailableMemoryMb = GetAvailableMemoryMb(),
                TotalDiskMb = GetTotalDiskMb(),
                AvailableDiskMb = GetAvailableDiskMb(),
                NetworkBandwidthMbps = 1000, // 假设1Gbps网络
                CurrentExecutions = capacity.CurrentExecutions,
                MaxExecutions = capacity.MaxConcurrentExecutions,
                AverageExecutionTimeMs = statistics.UsageHistory.Count > 0 ? 
                    statistics.UsageHistory.Average(h => h.CurrentExecutions * 1000) : 0,
                SuccessfulExecutions = _successfulReservations,
                FailedExecutions = _failedReservations,
                HealthStatus = capacity.CanAcceptExecution ? NodeHealthStatus.Healthy : NodeHealthStatus.Warning,
                LastHeartbeat = DateTime.UtcNow,
                Tags = new List<string> { "executor", "workflow" },
                Capabilities = new List<string> { "workflow-execution", "task-processing" }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get node load info");
            throw;
        }
    }

    /// <inheritdoc />
    public ResourceUsageStatistics GetResourceUsageStatistics()
    {
        try
        {
            var now = DateTime.UtcNow;
            var startTime = now.AddHours(-1); // 最近1小时的统计

            lock (_historyLock)
            {
                var recentHistory = _usageHistory.Where(h => h.Timestamp >= startTime).ToList();
                
                if (recentHistory.Count == 0)
                {
                    return new ResourceUsageStatistics
                    {
                        StartTime = startTime,
                        EndTime = now,
                        UsageHistory = new List<ResourceUsageSnapshot>()
                    };
                }

                return new ResourceUsageStatistics
                {
                    StartTime = startTime,
                    EndTime = now,
                    AverageCpuUsage = recentHistory.Average(h => h.CpuUsage),
                    PeakCpuUsage = recentHistory.Max(h => h.CpuUsage),
                    AverageMemoryUsage = recentHistory.Average(h => h.MemoryUsage),
                    PeakMemoryUsage = recentHistory.Max(h => h.MemoryUsage),
                    AverageDiskUsage = recentHistory.Average(h => h.DiskUsage),
                    PeakDiskUsage = recentHistory.Max(h => h.DiskUsage),
                    AverageNetworkUsage = recentHistory.Average(h => h.NetworkUsage),
                    PeakNetworkUsage = recentHistory.Max(h => h.NetworkUsage),
                    UsageHistory = recentHistory
                };
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get resource usage statistics");
            throw;
        }
    }

    /// <inheritdoc />
    public void SetCapacityLimits(CapacityLimits limits)
    {
        ArgumentNullException.ThrowIfNull(limits);
        
        _capacityLimits = limits;
        _logger.LogInformation("Capacity limits updated: MaxExecutions={MaxExecutions}, MaxCpu={MaxCpu}%, MaxMemory={MaxMemory}%",
            limits.MaxConcurrentExecutions, limits.MaxCpuUsagePercent, limits.MaxMemoryUsagePercent);
    }

    /// <inheritdoc />
    public CapacityLimits GetCapacityLimits()
    {
        return _capacityLimits;
    }

    /// <inheritdoc />
    public int CleanupExpiredReservations(TimeSpan expirationTime)
    {
        try
        {
            var expiredCount = 0;
            var cutoffTime = DateTime.UtcNow - expirationTime;
            var expiredReservations = new List<string>();

            foreach (var kvp in _resourceReservations)
            {
                if (kvp.Value.ReservedAt < cutoffTime)
                {
                    expiredReservations.Add(kvp.Key);
                }
            }

            foreach (var executionId in expiredReservations)
            {
                if (_resourceReservations.TryRemove(executionId, out _))
                {
                    expiredCount++;
                }
            }

            if (expiredCount > 0)
            {
                _logger.LogInformation("Cleaned up {ExpiredCount} expired resource reservations", expiredCount);
            }

            return expiredCount;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to cleanup expired reservations");
            return 0;
        }
    }

    #region 私有方法

    /// <summary>
    /// 获取CPU使用率
    /// </summary>
    private double GetCpuUsage()
    {
        try
        {
            // 简化的CPU使用率估算
            var baseUsage = Math.Min(_resourceReservations.Count * 10.0, 80.0);
            var timeBasedVariation = Math.Sin(DateTime.UtcNow.TimeOfDay.TotalMinutes / 10) * 5; // 模拟变化
            return Math.Max(0, Math.Min(100, baseUsage + timeBasedVariation));
        }
        catch
        {
            return 0.0;
        }
    }

    /// <summary>
    /// 获取内存使用率
    /// </summary>
    private double GetMemoryUsage()
    {
        try
        {
            // 简化的内存使用率估算
            var baseUsage = Math.Min(_resourceReservations.Count * 5.0, 70.0);
            var gcMemory = GC.GetTotalMemory(false) / 1024 / 1024; // MB
            var memoryFactor = Math.Min(gcMemory / 1024.0 * 10, 20); // 基于GC内存的因子
            return Math.Max(0, Math.Min(100, baseUsage + memoryFactor));
        }
        catch
        {
            return 0.0;
        }
    }

    /// <summary>
    /// 获取磁盘使用率
    /// </summary>
    private double GetDiskUsage()
    {
        try
        {
            var drives = DriveInfo.GetDrives().Where(d => d.IsReady && d.DriveType == DriveType.Fixed);
            if (drives.Any())
            {
                var totalSpace = drives.Sum(d => d.TotalSize);
                var freeSpace = drives.Sum(d => d.AvailableFreeSpace);
                return (double)(totalSpace - freeSpace) / totalSpace * 100;
            }

            return 50.0; // 默认值
        }
        catch
        {
            return 50.0;
        }
    }

    /// <summary>
    /// 获取网络使用率
    /// </summary>
    private double GetNetworkUsage()
    {
        // 简化实现，实际项目中可以使用网络性能计数器
        return Math.Min(_resourceReservations.Count * 2.0, 30.0);
    }

    /// <summary>
    /// 计算负载评分
    /// </summary>
    private double CalculateLoadScore(double cpuUsage, double memoryUsage, double diskUsage, double networkUsage, int currentExecutions)
    {
        // 加权平均计算负载评分
        var weights = new { Cpu = 0.4, Memory = 0.3, Disk = 0.2, Network = 0.1 };
        var executionFactor = Math.Min(currentExecutions / (double)_capacityLimits.MaxConcurrentExecutions * 100, 100);

        return (cpuUsage * weights.Cpu +
                memoryUsage * weights.Memory +
                diskUsage * weights.Disk +
                networkUsage * weights.Network +
                executionFactor * 0.2) / 1.2;
    }

    /// <summary>
    /// 获取总内存大小（MB）
    /// </summary>
    private long GetTotalMemoryMb()
    {
        try
        {
            var totalMemory = GC.GetTotalMemory(false);
            return totalMemory / 1024 / 1024 * 4; // 估算为实际使用的4倍
        }
        catch
        {
            return 8192; // 默认8GB
        }
    }

    /// <summary>
    /// 获取可用内存大小（MB）
    /// </summary>
    private long GetAvailableMemoryMb()
    {
        try
        {
            // 基于GC内存的简化估算
            var gcMemory = GC.GetTotalMemory(false) / 1024 / 1024; // MB
            var totalMemory = GetTotalMemoryMb();
            var estimatedUsed = Math.Max(gcMemory * 4, totalMemory * 0.3); // 估算已使用内存
            return Math.Max(1024, totalMemory - (long)estimatedUsed); // 至少保留1GB可用
        }
        catch
        {
            return 4096; // 默认4GB
        }
    }

    /// <summary>
    /// 获取总磁盘空间（MB）
    /// </summary>
    private long GetTotalDiskMb()
    {
        try
        {
            var drives = DriveInfo.GetDrives().Where(d => d.IsReady && d.DriveType == DriveType.Fixed);
            return drives.Sum(d => d.TotalSize) / 1024 / 1024;
        }
        catch
        {
            return 500000; // 默认500GB
        }
    }

    /// <summary>
    /// 获取可用磁盘空间（MB）
    /// </summary>
    private long GetAvailableDiskMb()
    {
        try
        {
            var drives = DriveInfo.GetDrives().Where(d => d.IsReady && d.DriveType == DriveType.Fixed);
            return drives.Sum(d => d.AvailableFreeSpace) / 1024 / 1024;
        }
        catch
        {
            return 250000; // 默认250GB
        }
    }

    #endregion
}

/// <summary>
/// 资源预留信息
/// </summary>
internal class ResourceReservation
{
    /// <summary>
    /// 执行ID
    /// </summary>
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 预留的资源使用量
    /// </summary>
    public ResourceUsage ResourceUsage { get; set; } = new();

    /// <summary>
    /// 实际资源使用量
    /// </summary>
    public ResourceUsage? ActualUsage { get; set; }

    /// <summary>
    /// 预留时间
    /// </summary>
    public DateTime ReservedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime? UpdatedAt { get; set; }
}
