#!/usr/bin/env python3
"""
FlowCustomV1 极限压力测试
测试NATS和MySQL在极限负载下的表现
"""

import requests
import time
import threading
import concurrent.futures
import statistics
import json
from datetime import datetime
import mysql.connector
import random
import string

# 测试配置
NATS_MONITOR_URL = "http://localhost:8222"
MYSQL_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'flowcustom',
    'password': 'FlowCustom@2025',
    'database': 'flowcustom_dev'
}

class ExtremeStressTest:
    def __init__(self):
        self.results = {}
        
    def test_nats_extreme_load(self):
        """NATS极限负载测试"""
        print("\n🔥 NATS极限负载测试")
        print("=" * 50)
        
        # 测试不同极限并发级别
        extreme_levels = [100, 200, 500, 1000]
        
        for concurrent_users in extreme_levels:
            print(f"\n🚀 极限并发测试: {concurrent_users} 用户")
            
            def make_extreme_requests(thread_id, requests_count=10):
                times = []
                success_count = 0
                error_count = 0
                
                for i in range(requests_count):
                    try:
                        start_time = time.time()
                        response = requests.get(f"{NATS_MONITOR_URL}/varz", timeout=10)
                        end_time = time.time()
                        
                        if response.status_code == 200:
                            success_count += 1
                            times.append((end_time - start_time) * 1000)
                        else:
                            error_count += 1
                            
                    except Exception as e:
                        error_count += 1
                        
                return times, success_count, error_count
            
            start_time = time.time()
            
            try:
                with concurrent.futures.ThreadPoolExecutor(max_workers=concurrent_users) as executor:
                    futures = [executor.submit(make_extreme_requests, i, 5) for i in range(concurrent_users)]
                    results = []
                    
                    # 设置超时时间
                    for future in concurrent.futures.as_completed(futures, timeout=60):
                        try:
                            result = future.result()
                            results.append(result)
                        except Exception as e:
                            print(f"    线程执行失败: {str(e)}")
                            
            except concurrent.futures.TimeoutError:
                print(f"    ⚠️  超时：{concurrent_users} 并发用户测试超时")
                continue
            except Exception as e:
                print(f"    ❌ 测试失败: {str(e)}")
                continue
                
            end_time = time.time()
            total_duration = end_time - start_time
            
            # 汇总结果
            all_times = []
            total_success = 0
            total_errors = 0
            
            for times, success, errors in results:
                all_times.extend(times)
                total_success += success
                total_errors += errors
                
            total_requests = concurrent_users * 5
            
            if all_times:
                avg_time = statistics.mean(all_times)
                max_time = max(all_times)
                min_time = min(all_times)
                throughput = total_success / total_duration if total_duration > 0 else 0
                
                print(f"    总耗时: {total_duration:.2f}秒")
                print(f"    成功请求: {total_success}/{total_requests}")
                print(f"    失败请求: {total_errors}")
                print(f"    成功率: {total_success/total_requests*100:.1f}%")
                print(f"    吞吐量: {throughput:.2f} 请求/秒")
                print(f"    平均响应时间: {avg_time:.2f}ms")
                print(f"    最快/最慢: {min_time:.2f}ms / {max_time:.2f}ms")
                
                # 性能评级
                if throughput > 200:
                    grade = "🟢 优秀"
                elif throughput > 100:
                    grade = "🟡 良好"
                elif throughput > 50:
                    grade = "🟠 一般"
                else:
                    grade = "🔴 较差"
                    
                print(f"    性能评级: {grade}")
                
                self.results[f"nats_extreme_{concurrent_users}"] = {
                    "throughput": throughput,
                    "success_rate": total_success/total_requests,
                    "avg_time": avg_time,
                    "max_time": max_time,
                    "total_duration": total_duration,
                    "grade": grade
                }
            else:
                print(f"    ❌ 所有请求都失败了")
                
    def test_mysql_extreme_load(self):
        """MySQL极限负载测试"""
        print("\n🔥 MySQL极限负载测试")
        print("=" * 50)
        
        # 测试不同极限并发级别
        extreme_levels = [50, 100, 200, 500]
        
        for concurrent_connections in extreme_levels:
            print(f"\n🚀 极限并发测试: {concurrent_connections} 连接")
            
            def make_extreme_queries(thread_id, queries_count=10):
                times = []
                success_count = 0
                error_count = 0
                
                try:
                    conn = mysql.connector.connect(**MYSQL_CONFIG, connection_timeout=10)
                    cursor = conn.cursor()
                    
                    for i in range(queries_count):
                        try:
                            start_time = time.time()
                            cursor.execute("SELECT DATABASE(), CONNECTION_ID(), NOW(), RAND()")
                            results = cursor.fetchall()
                            end_time = time.time()
                            
                            success_count += 1
                            times.append((end_time - start_time) * 1000)
                            
                        except Exception as e:
                            error_count += 1
                            
                    cursor.close()
                    conn.close()
                    
                except Exception as e:
                    error_count += queries_count
                    
                return times, success_count, error_count
            
            start_time = time.time()
            
            try:
                with concurrent.futures.ThreadPoolExecutor(max_workers=concurrent_connections) as executor:
                    futures = [executor.submit(make_extreme_queries, i, 5) for i in range(concurrent_connections)]
                    results = []
                    
                    # 设置超时时间
                    for future in concurrent.futures.as_completed(futures, timeout=120):
                        try:
                            result = future.result()
                            results.append(result)
                        except Exception as e:
                            print(f"    连接执行失败: {str(e)}")
                            
            except concurrent.futures.TimeoutError:
                print(f"    ⚠️  超时：{concurrent_connections} 并发连接测试超时")
                continue
            except Exception as e:
                print(f"    ❌ 测试失败: {str(e)}")
                continue
                
            end_time = time.time()
            total_duration = end_time - start_time
            
            # 汇总结果
            all_times = []
            total_success = 0
            total_errors = 0
            
            for times, success, errors in results:
                all_times.extend(times)
                total_success += success
                total_errors += errors
                
            total_queries = concurrent_connections * 5
            
            if all_times:
                avg_time = statistics.mean(all_times)
                max_time = max(all_times)
                min_time = min(all_times)
                throughput = total_success / total_duration if total_duration > 0 else 0
                
                print(f"    总耗时: {total_duration:.2f}秒")
                print(f"    成功查询: {total_success}/{total_queries}")
                print(f"    失败查询: {total_errors}")
                print(f"    成功率: {total_success/total_queries*100:.1f}%")
                print(f"    吞吐量: {throughput:.2f} 查询/秒")
                print(f"    平均查询时间: {avg_time:.2f}ms")
                print(f"    最快/最慢: {min_time:.2f}ms / {max_time:.2f}ms")
                
                # 性能评级
                if throughput > 1000:
                    grade = "🟢 优秀"
                elif throughput > 500:
                    grade = "🟡 良好"
                elif throughput > 200:
                    grade = "🟠 一般"
                else:
                    grade = "🔴 较差"
                    
                print(f"    性能评级: {grade}")
                
                self.results[f"mysql_extreme_{concurrent_connections}"] = {
                    "throughput": throughput,
                    "success_rate": total_success/total_queries,
                    "avg_time": avg_time,
                    "max_time": max_time,
                    "total_duration": total_duration,
                    "grade": grade
                }
            else:
                print(f"    ❌ 所有查询都失败了")
                
    def test_system_breaking_point(self):
        """测试系统崩溃点"""
        print("\n🔥 系统崩溃点测试")
        print("=" * 50)
        
        print("⚠️  警告：此测试可能会导致系统暂时不可用")
        print("正在寻找系统的性能极限...")
        
        # 逐步增加负载直到系统崩溃
        current_load = 100
        max_successful_load = 0
        
        while current_load <= 2000:
            print(f"\n🎯 测试负载级别: {current_load}")
            
            def stress_test_request(thread_id):
                try:
                    response = requests.get(f"{NATS_MONITOR_URL}/varz", timeout=5)
                    return response.status_code == 200
                except:
                    return False
            
            start_time = time.time()
            success_count = 0
            
            try:
                with concurrent.futures.ThreadPoolExecutor(max_workers=current_load) as executor:
                    futures = [executor.submit(stress_test_request, i) for i in range(current_load)]
                    
                    for future in concurrent.futures.as_completed(futures, timeout=30):
                        try:
                            if future.result():
                                success_count += 1
                        except:
                            pass
                            
            except concurrent.futures.TimeoutError:
                print(f"    ❌ 负载 {current_load}: 超时失败")
                break
            except Exception as e:
                print(f"    ❌ 负载 {current_load}: 系统错误 - {str(e)}")
                break
                
            end_time = time.time()
            duration = end_time - start_time
            success_rate = success_count / current_load
            
            print(f"    成功率: {success_rate*100:.1f}% ({success_count}/{current_load})")
            print(f"    耗时: {duration:.2f}秒")
            
            if success_rate >= 0.8:  # 80%成功率认为是可接受的
                max_successful_load = current_load
                print(f"    ✅ 负载 {current_load}: 通过测试")
                current_load += 200
            else:
                print(f"    ❌ 负载 {current_load}: 系统开始不稳定")
                break
                
        print(f"\n🎯 系统性能极限分析:")
        print(f"  最大稳定负载: {max_successful_load} 并发请求")
        print(f"  崩溃点: {current_load} 并发请求")
        
        self.results["system_breaking_point"] = {
            "max_stable_load": max_successful_load,
            "breaking_point": current_load
        }
        
    def generate_extreme_report(self):
        """生成极限压力测试报告"""
        print("\n" + "=" * 60)
        print("🔥 FlowCustomV1 极限压力测试报告")
        print("=" * 60)
        print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 运行所有极限测试
        self.test_nats_extreme_load()
        self.test_mysql_extreme_load()
        self.test_system_breaking_point()
        
        # 生成总结
        self.print_extreme_summary()
        
    def print_extreme_summary(self):
        """打印极限测试总结"""
        print("\n" + "=" * 60)
        print("🎯 极限压力测试总结")
        print("=" * 60)
        
        print("\n🔸 NATS极限性能:")
        nats_best_throughput = 0
        nats_best_config = ""
        
        for key, data in self.results.items():
            if key.startswith("nats_extreme_"):
                concurrent_level = key.split("_")[-1]
                throughput = data['throughput']
                grade = data['grade']
                
                print(f"  {concurrent_level} 并发: {throughput:.2f} req/s {grade}")
                
                if throughput > nats_best_throughput:
                    nats_best_throughput = throughput
                    nats_best_config = concurrent_level
                    
        print(f"  🏆 NATS最佳性能: {nats_best_throughput:.2f} req/s ({nats_best_config} 并发)")
        
        print("\n🔸 MySQL极限性能:")
        mysql_best_throughput = 0
        mysql_best_config = ""
        
        for key, data in self.results.items():
            if key.startswith("mysql_extreme_"):
                concurrent_level = key.split("_")[-1]
                throughput = data['throughput']
                grade = data['grade']
                
                print(f"  {concurrent_level} 并发: {throughput:.2f} queries/s {grade}")
                
                if throughput > mysql_best_throughput:
                    mysql_best_throughput = throughput
                    mysql_best_config = concurrent_level
                    
        print(f"  🏆 MySQL最佳性能: {mysql_best_throughput:.2f} queries/s ({mysql_best_config} 并发)")
        
        if "system_breaking_point" in self.results:
            bp_data = self.results["system_breaking_point"]
            print(f"\n🔸 系统极限:")
            print(f"  最大稳定负载: {bp_data['max_stable_load']} 并发请求")
            print(f"  系统崩溃点: {bp_data['breaking_point']} 并发请求")
        
        print(f"\n测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    print("⚠️  警告：这是极限压力测试，可能会影响系统性能")
    print("确认要继续吗？(y/N): ", end="")
    
    # 在自动化环境中直接运行
    test = ExtremeStressTest()
    test.generate_extreme_report()
