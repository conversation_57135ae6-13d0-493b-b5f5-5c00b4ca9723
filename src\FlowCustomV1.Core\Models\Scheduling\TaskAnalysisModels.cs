using System.ComponentModel.DataAnnotations;

namespace FlowCustomV1.Core.Models.Scheduling;

/// <summary>
/// 失败模式分析结果
/// </summary>
public class FailurePatternAnalysis
{
    /// <summary>
    /// 分析ID
    /// </summary>
    public string AnalysisId { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 任务ID
    /// </summary>
    public string TaskId { get; set; } = string.Empty;

    /// <summary>
    /// 分析时间
    /// </summary>
    public DateTime AnalyzedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 失败模式类型
    /// </summary>
    public FailurePatternType PatternType { get; set; }

    /// <summary>
    /// 失败频率
    /// </summary>
    public double FailureFrequency { get; set; }

    /// <summary>
    /// 失败趋势
    /// </summary>
    public FailureTrend Trend { get; set; }

    /// <summary>
    /// 相关失败任务
    /// </summary>
    public List<string> RelatedFailedTasks { get; set; } = new();

    /// <summary>
    /// 根本原因分析
    /// </summary>
    public string RootCauseAnalysis { get; set; } = string.Empty;

    /// <summary>
    /// 建议措施
    /// </summary>
    public List<string> RecommendedActions { get; set; } = new();

    /// <summary>
    /// 置信度（0-1）
    /// </summary>
    public double ConfidenceLevel { get; set; }

    /// <summary>
    /// 分析详情
    /// </summary>
    public Dictionary<string, object> AnalysisDetails { get; set; } = new();
}

/// <summary>
/// 恢复建议
/// </summary>
public class RecoveryRecommendations
{
    /// <summary>
    /// 建议ID
    /// </summary>
    public string RecommendationId { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 任务ID
    /// </summary>
    public string TaskId { get; set; } = string.Empty;

    /// <summary>
    /// 生成时间
    /// </summary>
    public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 推荐的恢复策略
    /// </summary>
    public List<RecoveryStrategy> RecommendedStrategies { get; set; } = new();

    /// <summary>
    /// 优先级排序的建议
    /// </summary>
    public List<RecoveryAction> PrioritizedActions { get; set; } = new();

    /// <summary>
    /// 预期成功率
    /// </summary>
    public double ExpectedSuccessRate { get; set; }

    /// <summary>
    /// 预计恢复时间（分钟）
    /// </summary>
    public int EstimatedRecoveryTimeMinutes { get; set; }

    /// <summary>
    /// 风险评估
    /// </summary>
    public RiskAssessment RiskAssessment { get; set; } = null!;

    /// <summary>
    /// 建议详情
    /// </summary>
    public Dictionary<string, object> Details { get; set; } = new();
}

/// <summary>
/// 恢复执行结果
/// </summary>
public class RecoveryExecutionResult
{
    /// <summary>
    /// 执行ID
    /// </summary>
    public string ExecutionId { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 任务ID
    /// </summary>
    public string TaskId { get; set; } = string.Empty;

    /// <summary>
    /// 恢复策略
    /// </summary>
    public string RecoveryStrategy { get; set; } = string.Empty;

    /// <summary>
    /// 执行时间
    /// </summary>
    public DateTime ExecutedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 执行耗时（毫秒）
    /// </summary>
    public long ExecutionTimeMs { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 恢复后状态
    /// </summary>
    public TaskExecutionStatus PostRecoveryStatus { get; set; }

    /// <summary>
    /// 执行详情
    /// </summary>
    public Dictionary<string, object> ExecutionDetails { get; set; } = new();
}

/// <summary>
/// 重试统计信息
/// </summary>
public class RetryStatistics
{
    /// <summary>
    /// 统计时间范围开始
    /// </summary>
    public DateTime PeriodStart { get; set; }

    /// <summary>
    /// 统计时间范围结束
    /// </summary>
    public DateTime PeriodEnd { get; set; }

    /// <summary>
    /// 总重试次数
    /// </summary>
    public int TotalRetries { get; set; }

    /// <summary>
    /// 成功重试次数
    /// </summary>
    public int SuccessfulRetries { get; set; }

    /// <summary>
    /// 失败重试次数
    /// </summary>
    public int FailedRetries { get; set; }

    /// <summary>
    /// 重试成功率
    /// </summary>
    public double RetrySuccessRate { get; set; }

    /// <summary>
    /// 平均重试次数
    /// </summary>
    public double AverageRetryCount { get; set; }

    /// <summary>
    /// 平均重试间隔（毫秒）
    /// </summary>
    public long AverageRetryIntervalMs { get; set; }

    /// <summary>
    /// 按失败类型分组的统计
    /// </summary>
    public Dictionary<FailureType, int> FailureTypeStatistics { get; set; } = new();

    /// <summary>
    /// 按重试策略分组的统计
    /// </summary>
    public Dictionary<string, RetryStrategyStatistics> StrategyStatistics { get; set; } = new();
}

/// <summary>
/// 失败模式统计
/// </summary>
public class FailurePatternStatistics
{
    /// <summary>
    /// 统计时间范围
    /// </summary>
    public TimeSpan StatisticsPeriod { get; set; }

    /// <summary>
    /// 总失败次数
    /// </summary>
    public int TotalFailures { get; set; }

    /// <summary>
    /// 按模式类型分组的统计
    /// </summary>
    public Dictionary<FailurePatternType, int> PatternTypeStatistics { get; set; } = new();

    /// <summary>
    /// 最常见的失败模式
    /// </summary>
    public FailurePatternType MostCommonPattern { get; set; }

    /// <summary>
    /// 失败趋势
    /// </summary>
    public FailureTrend OverallTrend { get; set; }

    /// <summary>
    /// 平均失败间隔（分钟）
    /// </summary>
    public double AverageFailureIntervalMinutes { get; set; }

    /// <summary>
    /// 详细统计信息
    /// </summary>
    public Dictionary<string, object> DetailedStatistics { get; set; } = new();
}

/// <summary>
/// 恢复效果统计
/// </summary>
public class RecoveryEffectivenessStatistics
{
    /// <summary>
    /// 统计时间范围
    /// </summary>
    public TimeSpan StatisticsPeriod { get; set; }

    /// <summary>
    /// 总恢复尝试次数
    /// </summary>
    public int TotalRecoveryAttempts { get; set; }

    /// <summary>
    /// 成功恢复次数
    /// </summary>
    public int SuccessfulRecoveries { get; set; }

    /// <summary>
    /// 恢复成功率
    /// </summary>
    public double RecoverySuccessRate { get; set; }

    /// <summary>
    /// 平均恢复时间（分钟）
    /// </summary>
    public double AverageRecoveryTimeMinutes { get; set; }

    /// <summary>
    /// 按恢复策略分组的效果统计
    /// </summary>
    public Dictionary<string, RecoveryStrategyEffectiveness> StrategyEffectiveness { get; set; } = new();

    /// <summary>
    /// 最有效的恢复策略
    /// </summary>
    public string MostEffectiveStrategy { get; set; } = string.Empty;

    /// <summary>
    /// 详细统计信息
    /// </summary>
    public Dictionary<string, object> DetailedStatistics { get; set; } = new();
}

/// <summary>
/// 配置更新结果
/// </summary>
public class ConfigUpdateResult
{
    /// <summary>
    /// 更新ID
    /// </summary>
    public string UpdateId { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 更新的配置项
    /// </summary>
    public Dictionary<string, object> UpdatedConfigurations { get; set; } = new();

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 影响的组件
    /// </summary>
    public List<string> AffectedComponents { get; set; } = new();

    /// <summary>
    /// 更新详情
    /// </summary>
    public Dictionary<string, object> UpdateDetails { get; set; } = new();
}

/// <summary>
/// 健康检查结果
/// </summary>
public class HealthCheckResult
{
    /// <summary>
    /// 检查ID
    /// </summary>
    public string CheckId { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 检查时间
    /// </summary>
    public DateTime CheckedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 是否健康
    /// </summary>
    public bool IsHealthy { get; set; }

    /// <summary>
    /// 健康状态
    /// </summary>
    public HealthStatus Status { get; set; }

    /// <summary>
    /// 检查项目结果
    /// </summary>
    public Dictionary<string, HealthCheckItemResult> CheckResults { get; set; } = new();

    /// <summary>
    /// 总体评分（0-100）
    /// </summary>
    public int OverallScore { get; set; }

    /// <summary>
    /// 警告信息
    /// </summary>
    public List<string> Warnings { get; set; } = new();

    /// <summary>
    /// 错误信息
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// 建议措施
    /// </summary>
    public List<string> Recommendations { get; set; } = new();
}

/// <summary>
/// 清理结果
/// </summary>
public class CleanupResult
{
    /// <summary>
    /// 清理ID
    /// </summary>
    public string CleanupId { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 清理时间
    /// </summary>
    public DateTime CleanedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 清理的项目数量
    /// </summary>
    public int CleanedItemsCount { get; set; }

    /// <summary>
    /// 释放的资源大小（字节）
    /// </summary>
    public long ReleasedResourcesBytes { get; set; }

    /// <summary>
    /// 清理耗时（毫秒）
    /// </summary>
    public long CleanupTimeMs { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 清理详情
    /// </summary>
    public Dictionary<string, object> CleanupDetails { get; set; } = new();
}

/// <summary>
/// 失败模式类型
/// </summary>
public enum FailurePatternType
{
    /// <summary>
    /// 随机失败
    /// </summary>
    Random,

    /// <summary>
    /// 周期性失败
    /// </summary>
    Periodic,

    /// <summary>
    /// 连续失败
    /// </summary>
    Consecutive,

    /// <summary>
    /// 级联失败
    /// </summary>
    Cascading,

    /// <summary>
    /// 资源相关失败
    /// </summary>
    ResourceRelated,

    /// <summary>
    /// 时间相关失败
    /// </summary>
    TimeRelated
}

/// <summary>
/// 失败趋势
/// </summary>
public enum FailureTrend
{
    /// <summary>
    /// 稳定
    /// </summary>
    Stable,

    /// <summary>
    /// 上升
    /// </summary>
    Increasing,

    /// <summary>
    /// 下降
    /// </summary>
    Decreasing,

    /// <summary>
    /// 波动
    /// </summary>
    Fluctuating
}

/// <summary>
/// 健康状态
/// </summary>
public enum HealthStatus
{
    /// <summary>
    /// 健康
    /// </summary>
    Healthy,

    /// <summary>
    /// 警告
    /// </summary>
    Warning,

    /// <summary>
    /// 不健康
    /// </summary>
    Unhealthy,

    /// <summary>
    /// 严重
    /// </summary>
    Critical
}

/// <summary>
/// 恢复策略
/// </summary>
public class RecoveryStrategy
{
    /// <summary>
    /// 策略名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 策略描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 优先级
    /// </summary>
    public int Priority { get; set; }

    /// <summary>
    /// 预期成功率
    /// </summary>
    public double ExpectedSuccessRate { get; set; }

    /// <summary>
    /// 预计执行时间（分钟）
    /// </summary>
    public int EstimatedExecutionTimeMinutes { get; set; }

    /// <summary>
    /// 风险级别
    /// </summary>
    public RiskLevel RiskLevel { get; set; }
}

/// <summary>
/// 恢复操作
/// </summary>
public class RecoveryAction
{
    /// <summary>
    /// 操作名称
    /// </summary>
    public string ActionName { get; set; } = string.Empty;

    /// <summary>
    /// 操作描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 优先级
    /// </summary>
    public int Priority { get; set; }

    /// <summary>
    /// 操作参数
    /// </summary>
    public Dictionary<string, object> Parameters { get; set; } = new();
}

/// <summary>
/// 风险评估
/// </summary>
public class RiskAssessment
{
    /// <summary>
    /// 风险级别
    /// </summary>
    public RiskLevel RiskLevel { get; set; }

    /// <summary>
    /// 风险描述
    /// </summary>
    public string RiskDescription { get; set; } = string.Empty;

    /// <summary>
    /// 缓解措施
    /// </summary>
    public List<string> MitigationMeasures { get; set; } = new();
}

/// <summary>
/// 风险级别
/// </summary>
public enum RiskLevel
{
    /// <summary>
    /// 低风险
    /// </summary>
    Low,

    /// <summary>
    /// 中等风险
    /// </summary>
    Medium,

    /// <summary>
    /// 高风险
    /// </summary>
    High,

    /// <summary>
    /// 极高风险
    /// </summary>
    Critical
}

/// <summary>
/// 重试策略统计
/// </summary>
public class RetryStrategyStatistics
{
    /// <summary>
    /// 策略名称
    /// </summary>
    public string StrategyName { get; set; } = string.Empty;

    /// <summary>
    /// 使用次数
    /// </summary>
    public int UsageCount { get; set; }

    /// <summary>
    /// 成功次数
    /// </summary>
    public int SuccessCount { get; set; }

    /// <summary>
    /// 成功率
    /// </summary>
    public double SuccessRate { get; set; }

    /// <summary>
    /// 平均执行时间（毫秒）
    /// </summary>
    public long AverageExecutionTimeMs { get; set; }
}

/// <summary>
/// 恢复策略效果
/// </summary>
public class RecoveryStrategyEffectiveness
{
    /// <summary>
    /// 策略名称
    /// </summary>
    public string StrategyName { get; set; } = string.Empty;

    /// <summary>
    /// 使用次数
    /// </summary>
    public int UsageCount { get; set; }

    /// <summary>
    /// 成功次数
    /// </summary>
    public int SuccessCount { get; set; }

    /// <summary>
    /// 成功率
    /// </summary>
    public double SuccessRate { get; set; }

    /// <summary>
    /// 平均恢复时间（分钟）
    /// </summary>
    public double AverageRecoveryTimeMinutes { get; set; }
}

/// <summary>
/// 健康检查项目结果
/// </summary>
public class HealthCheckItemResult
{
    /// <summary>
    /// 检查项目名称
    /// </summary>
    public string ItemName { get; set; } = string.Empty;

    /// <summary>
    /// 是否通过
    /// </summary>
    public bool IsPassed { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public HealthStatus Status { get; set; }

    /// <summary>
    /// 消息
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 详细信息
    /// </summary>
    public Dictionary<string, object> Details { get; set; } = new();
}

/// <summary>
/// 重试建议
/// </summary>
public class RetryRecommendation
{
    /// <summary>
    /// 建议ID
    /// </summary>
    public string RecommendationId { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 任务ID
    /// </summary>
    public string TaskId { get; set; } = string.Empty;

    /// <summary>
    /// 是否建议重试
    /// </summary>
    public bool ShouldRetry { get; set; }

    /// <summary>
    /// 建议的重试策略
    /// </summary>
    public string RecommendedStrategy { get; set; } = string.Empty;

    /// <summary>
    /// 建议的重试延迟（毫秒）
    /// </summary>
    public long RecommendedDelayMs { get; set; }

    /// <summary>
    /// 建议的最大重试次数
    /// </summary>
    public int RecommendedMaxRetries { get; set; }

    /// <summary>
    /// 建议原因
    /// </summary>
    public string Reason { get; set; } = string.Empty;

    /// <summary>
    /// 置信度（0-1）
    /// </summary>
    public double Confidence { get; set; }

    /// <summary>
    /// 预期成功率
    /// </summary>
    public double ExpectedSuccessRate { get; set; }

    /// <summary>
    /// 风险评估
    /// </summary>
    public RiskLevel RiskLevel { get; set; }

    /// <summary>
    /// 建议的配置修改
    /// </summary>
    public Dictionary<string, object> ConfigurationChanges { get; set; } = new();

    /// <summary>
    /// 排除的节点
    /// </summary>
    public List<string> ExcludedNodes { get; set; } = new();

    /// <summary>
    /// 首选的节点
    /// </summary>
    public List<string> PreferredNodes { get; set; } = new();

    /// <summary>
    /// 生成时间
    /// </summary>
    public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 附加信息
    /// </summary>
    public Dictionary<string, object> AdditionalInfo { get; set; } = new();
}

/// <summary>
/// 任务跟踪摘要
/// </summary>
public class TaskTrackingSummary
{
    /// <summary>
    /// 任务ID
    /// </summary>
    public string TaskId { get; set; } = string.Empty;

    /// <summary>
    /// 任务状态
    /// </summary>
    public TaskExecutionStatus Status { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }

    /// <summary>
    /// 总跟踪时间（毫秒）
    /// </summary>
    public long TotalTrackingTime { get; set; }

    /// <summary>
    /// 节点ID
    /// </summary>
    public string NodeId { get; set; } = string.Empty;

    /// <summary>
    /// 进度信息
    /// </summary>
    public TaskProgress? Progress { get; set; }
}

/// <summary>
/// 任务搜索聚合信息
/// </summary>
public class TaskSearchAggregations
{
    /// <summary>
    /// 按状态统计
    /// </summary>
    public Dictionary<TaskExecutionStatus, int> StatusCounts { get; set; } = new();

    /// <summary>
    /// 按节点统计
    /// </summary>
    public Dictionary<string, int> NodeCounts { get; set; } = new();

    /// <summary>
    /// 按工作流统计
    /// </summary>
    public Dictionary<string, int> WorkflowCounts { get; set; } = new();
}



/// <summary>
/// 节点状态
/// </summary>
public enum NodeStatus
{
    /// <summary>
    /// 在线
    /// </summary>
    Online,

    /// <summary>
    /// 离线
    /// </summary>
    Offline,

    /// <summary>
    /// 繁忙
    /// </summary>
    Busy,

    /// <summary>
    /// 维护中
    /// </summary>
    Maintenance,

    /// <summary>
    /// 错误
    /// </summary>
    Error
}


