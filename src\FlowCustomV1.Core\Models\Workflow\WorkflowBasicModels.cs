using System.Collections.Concurrent;
using System.ComponentModel.DataAnnotations;

namespace FlowCustomV1.Core.Models.Workflow;

/// <summary>
/// 工作流参数定义
/// </summary>
public class WorkflowParameterDefinition
{
    /// <summary>
    /// 参数名称
    /// </summary>
    [Required]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 参数显示名称
    /// </summary>
    public string DisplayName { get; set; } = string.Empty;

    /// <summary>
    /// 参数描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 参数数据类型
    /// </summary>
    public ParameterDataType DataType { get; set; } = ParameterDataType.String;

    /// <summary>
    /// 是否必需
    /// </summary>
    public bool IsRequired { get; set; } = false;

    /// <summary>
    /// 默认值
    /// </summary>
    public object? DefaultValue { get; set; }

    /// <summary>
    /// 验证规则
    /// </summary>
    public List<ParameterValidationRule> ValidationRules { get; set; } = new();

    /// <summary>
    /// 参数元数据
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();

    /// <summary>
    /// 创建参数定义的深拷贝
    /// </summary>
    /// <returns>参数定义的深拷贝</returns>
    public WorkflowParameterDefinition Clone()
    {
        return new WorkflowParameterDefinition
        {
            Name = Name,
            DisplayName = DisplayName,
            Description = Description,
            DataType = DataType,
            IsRequired = IsRequired,
            DefaultValue = DefaultValue,
            ValidationRules = ValidationRules.Select(r => r.Clone()).ToList(),
            Metadata = new Dictionary<string, object>(Metadata)
        };
    }
}

/// <summary>
/// 工作流配置
/// </summary>
public class WorkflowConfiguration
{
    /// <summary>
    /// 工作流优先级
    /// </summary>
    public WorkflowPriority Priority { get; set; } = WorkflowPriority.Normal;

    /// <summary>
    /// 执行模式
    /// </summary>
    public ExecutionMode ExecutionMode { get; set; } = ExecutionMode.Sequential;

    /// <summary>
    /// 超时时间（分钟）
    /// </summary>
    [Range(1, int.MaxValue)]
    public int TimeoutMinutes { get; set; } = 60;

    /// <summary>
    /// 最大并发节点数
    /// </summary>
    [Range(1, int.MaxValue)]
    public int MaxConcurrentNodes { get; set; } = 10;

    /// <summary>
    /// 是否启用自动重试
    /// </summary>
    public bool EnableAutoRetry { get; set; } = false;

    /// <summary>
    /// 全局重试策略
    /// </summary>
    public NodeRetryStrategy GlobalRetryStrategy { get; set; } = new();

    /// <summary>
    /// 错误处理策略
    /// </summary>
    public string ErrorHandlingStrategy { get; set; } = "StopOnError";

    /// <summary>
    /// 是否启用详细日志
    /// </summary>
    public bool EnableDetailedLogging { get; set; } = false;

    /// <summary>
    /// 配置参数
    /// </summary>
    public Dictionary<string, object> Parameters { get; set; } = new();

    /// <summary>
    /// 创建配置的深拷贝
    /// </summary>
    /// <returns>配置的深拷贝</returns>
    public WorkflowConfiguration Clone()
    {
        return new WorkflowConfiguration
        {
            Priority = Priority,
            ExecutionMode = ExecutionMode,
            TimeoutMinutes = TimeoutMinutes,
            MaxConcurrentNodes = MaxConcurrentNodes,
            EnableAutoRetry = EnableAutoRetry,
            GlobalRetryStrategy = GlobalRetryStrategy.Clone(),
            ErrorHandlingStrategy = ErrorHandlingStrategy,
            EnableDetailedLogging = EnableDetailedLogging,
            Parameters = new Dictionary<string, object>(Parameters)
        };
    }
}

/// <summary>
/// 节点参数定义
/// </summary>
public class NodeParameterDefinition
{
    /// <summary>
    /// 参数名称
    /// </summary>
    [Required]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 参数显示名称
    /// </summary>
    public string DisplayName { get; set; } = string.Empty;

    /// <summary>
    /// 参数描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 参数数据类型
    /// </summary>
    public ParameterDataType DataType { get; set; } = ParameterDataType.String;

    /// <summary>
    /// 是否必需
    /// </summary>
    public bool IsRequired { get; set; } = false;

    /// <summary>
    /// 默认值
    /// </summary>
    public object? DefaultValue { get; set; }

    /// <summary>
    /// 验证规则
    /// </summary>
    public List<ParameterValidationRule> ValidationRules { get; set; } = new();

    /// <summary>
    /// 参数分组
    /// </summary>
    public string Group { get; set; } = "General";

    /// <summary>
    /// 显示顺序
    /// </summary>
    public int DisplayOrder { get; set; } = 0;

    /// <summary>
    /// 参数元数据
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();

    /// <summary>
    /// 创建节点参数定义的深拷贝
    /// </summary>
    /// <returns>节点参数定义的深拷贝</returns>
    public NodeParameterDefinition Clone()
    {
        return new NodeParameterDefinition
        {
            Name = Name,
            DisplayName = DisplayName,
            Description = Description,
            DataType = DataType,
            IsRequired = IsRequired,
            DefaultValue = DefaultValue,
            ValidationRules = ValidationRules.Select(r => r.Clone()).ToList(),
            Group = Group,
            DisplayOrder = DisplayOrder,
            Metadata = new Dictionary<string, object>(Metadata)
        };
    }
}

/// <summary>
/// 参数验证规则
/// </summary>
public class ParameterValidationRule
{
    /// <summary>
    /// 规则类型
    /// </summary>
    public string RuleType { get; set; } = string.Empty;

    /// <summary>
    /// 规则值
    /// </summary>
    public object? RuleValue { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string ErrorMessage { get; set; } = string.Empty;

    /// <summary>
    /// 创建验证规则的深拷贝
    /// </summary>
    /// <returns>验证规则的深拷贝</returns>
    public ParameterValidationRule Clone()
    {
        return new ParameterValidationRule
        {
            RuleType = RuleType,
            RuleValue = RuleValue,
            ErrorMessage = ErrorMessage
        };
    }
}

/// <summary>
/// 节点定义
/// 描述可用的节点类型及其配置
/// </summary>
public class NodeDefinition
{
    /// <summary>
    /// 节点类型标识符
    /// </summary>
    [Required]
    public string NodeType { get; set; } = string.Empty;

    /// <summary>
    /// 显示名称
    /// </summary>
    [Required]
    public string DisplayName { get; set; } = string.Empty;

    /// <summary>
    /// 描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 版本
    /// </summary>
    public string Version { get; set; } = "1.0.0";

    /// <summary>
    /// 节点类型分类
    /// </summary>
    public NodeTypeCategory Category { get; set; } = NodeTypeCategory.Process;

    /// <summary>
    /// 图标
    /// </summary>
    public string Icon { get; set; } = string.Empty;

    /// <summary>
    /// 颜色
    /// </summary>
    public string Color { get; set; } = "#007ACC";

    /// <summary>
    /// 是否支持异步执行
    /// </summary>
    public bool SupportsAsync { get; set; } = true;

    /// <summary>
    /// 是否有状态
    /// </summary>
    public bool IsStateful { get; set; } = false;

    /// <summary>
    /// 输入参数定义
    /// </summary>
    public List<NodeParameterDefinition> InputParameters { get; set; } = new();

    /// <summary>
    /// 输出参数定义
    /// </summary>
    public List<NodeParameterDefinition> OutputParameters { get; set; } = new();

    /// <summary>
    /// 配置参数定义
    /// </summary>
    public List<NodeParameterDefinition> ConfigurationParameters { get; set; } = new();

    /// <summary>
    /// 资源需求
    /// </summary>
    public NodeResourceRequirements ResourceRequirements { get; set; } = new();

    /// <summary>
    /// 执行器类型（用于反射创建执行器）
    /// </summary>
    public string ExecutorType { get; set; } = string.Empty;

    /// <summary>
    /// 执行器程序集
    /// </summary>
    public string ExecutorAssembly { get; set; } = string.Empty;

    /// <summary>
    /// 标签
    /// </summary>
    public HashSet<string> Tags { get; set; } = new();

    /// <summary>
    /// 元数据
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 验证节点定义的有效性
    /// </summary>
    /// <returns>是否有效</returns>
    public bool IsValid()
    {
        if (string.IsNullOrWhiteSpace(NodeType) || string.IsNullOrWhiteSpace(DisplayName))
            return false;

        // 验证参数定义的唯一性
        var allParams = InputParameters.Concat(OutputParameters).Concat(ConfigurationParameters);
        var paramNames = allParams.Select(p => p.Name).ToList();
        return paramNames.Count == paramNames.Distinct().Count();
    }

    /// <summary>
    /// 创建节点定义的深拷贝
    /// </summary>
    /// <returns>节点定义的深拷贝</returns>
    public NodeDefinition Clone()
    {
        return new NodeDefinition
        {
            NodeType = NodeType,
            DisplayName = DisplayName,
            Description = Description,
            Version = Version,
            Category = Category,
            Icon = Icon,
            Color = Color,
            SupportsAsync = SupportsAsync,
            IsStateful = IsStateful,
            InputParameters = InputParameters.Select(p => p.Clone()).ToList(),
            OutputParameters = OutputParameters.Select(p => p.Clone()).ToList(),
            ConfigurationParameters = ConfigurationParameters.Select(p => p.Clone()).ToList(),
            ResourceRequirements = ResourceRequirements.Clone(),
            ExecutorType = ExecutorType,
            ExecutorAssembly = ExecutorAssembly,
            Tags = new HashSet<string>(Tags),
            Metadata = new Dictionary<string, object>(Metadata)
        };
    }

    /// <summary>
    /// 获取节点定义的字符串表示
    /// </summary>
    /// <returns>节点定义字符串</returns>
    public override string ToString()
    {
        return $"NodeDefinition[{NodeType}] {DisplayName} v{Version} - {Category}";
    }
}

/// <summary>
/// 节点执行上下文
/// </summary>
public class NodeExecutionContext
{
    /// <summary>
    /// 执行ID
    /// </summary>
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 工作流ID
    /// </summary>
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 节点ID
    /// </summary>
    public string NodeId { get; set; } = string.Empty;

    /// <summary>
    /// 节点类型
    /// </summary>
    public string NodeType { get; set; } = string.Empty;

    /// <summary>
    /// 输入数据（线程安全）
    /// </summary>
    public ConcurrentDictionary<string, object> InputData { get; set; } = new();

    /// <summary>
    /// 工作流上下文数据（线程安全）
    /// </summary>
    public ConcurrentDictionary<string, object> WorkflowContext { get; set; } = new();

    /// <summary>
    /// 工作流执行上下文引用（用于Engine层访问完整的工作流状态）
    /// </summary>
    public object? WorkflowExecutionContext { get; set; }

    /// <summary>
    /// 节点配置
    /// </summary>
    public NodeConfiguration Configuration { get; set; } = new();

    /// <summary>
    /// 取消令牌
    /// </summary>
    public CancellationToken CancellationToken { get; set; } = default;

    /// <summary>
    /// 执行开始时间
    /// </summary>
    public DateTime StartedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 重试次数
    /// </summary>
    public int RetryCount { get; set; } = 0;

    /// <summary>
    /// 上下文元数据（线程安全）
    /// </summary>
    public ConcurrentDictionary<string, object> Metadata { get; set; } = new();

    /// <summary>
    /// 执行日志列表（线程安全）
    /// </summary>
    public ConcurrentBag<string> ExecutionLog { get; set; } = new();

    /// <summary>
    /// 节点执行性能指标（线程安全）
    /// </summary>
    public ConcurrentDictionary<string, double> PerformanceMetrics { get; set; } = new();

    /// <summary>
    /// 当前执行进度百分比 (0-100)
    /// </summary>
    public int ProgressPercentage { get; set; } = 0;

    /// <summary>
    /// 当前执行步骤描述
    /// </summary>
    public string CurrentStep { get; set; } = string.Empty;

    /// <summary>
    /// 节点执行状态
    /// </summary>
    public NodeExecutionState State { get; set; } = NodeExecutionState.NotStarted;

    /// <summary>
    /// 是否启用详细日志记录
    /// </summary>
    public bool EnableDetailedLogging { get; set; } = false;

    /// <summary>
    /// 节点执行的前置节点结果（线程安全）
    /// </summary>
    public ConcurrentDictionary<string, NodeExecutionResult> PreviousNodeResults { get; set; } = new();

    /// <summary>
    /// 节点执行的临时数据（线程安全）
    /// </summary>
    public ConcurrentDictionary<string, object> TemporaryData { get; set; } = new();

    /// <summary>
    /// 添加执行日志（线程安全）
    /// </summary>
    /// <param name="message">日志消息</param>
    /// <param name="level">日志级别</param>
    public void AddLog(string message, string level = "INFO")
    {
        var timestamp = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss.fff");
        var logEntry = $"[{timestamp}] [{level}] {message}";
        ExecutionLog.Add(logEntry);
    }

    /// <summary>
    /// 更新执行进度
    /// </summary>
    /// <param name="percentage">进度百分比</param>
    /// <param name="step">当前步骤</param>
    public void UpdateProgress(int percentage, string step)
    {
        ProgressPercentage = Math.Max(0, Math.Min(100, percentage));
        CurrentStep = step ?? string.Empty;
        AddLog($"Progress: {ProgressPercentage}% - {CurrentStep}");
    }

    /// <summary>
    /// 更新节点状态
    /// </summary>
    /// <param name="state">新状态</param>
    /// <param name="reason">状态变更原因</param>
    public void UpdateState(NodeExecutionState state, string? reason = null)
    {
        var oldState = State;
        State = state;
        AddLog($"State changed from {oldState} to {state}: {reason ?? "No reason provided"}");
    }

    /// <summary>
    /// 添加性能指标
    /// </summary>
    /// <param name="metricName">指标名称</param>
    /// <param name="value">指标值</param>
    public void AddPerformanceMetric(string metricName, double value)
    {
        PerformanceMetrics.AddOrUpdate(metricName, value, (key, oldValue) => value);
    }

    /// <summary>
    /// 增加性能指标计数
    /// </summary>
    /// <param name="metricName">指标名称</param>
    /// <param name="increment">增量</param>
    public void IncrementPerformanceMetric(string metricName, double increment = 1.0)
    {
        PerformanceMetrics.AddOrUpdate(metricName, increment, (key, oldValue) => oldValue + increment);
    }

    /// <summary>
    /// 记录性能指标（别名方法，用于兼容性）
    /// </summary>
    /// <param name="metricName">指标名称</param>
    /// <param name="value">指标值</param>
    public void RecordMetric(string metricName, double value)
    {
        AddPerformanceMetric(metricName, value);
    }

    /// <summary>
    /// 获取性能指标
    /// </summary>
    /// <param name="metricName">指标名称</param>
    /// <returns>指标值，如果不存在则返回0</returns>
    public double GetMetric(string metricName)
    {
        return PerformanceMetrics.TryGetValue(metricName, out var value) ? value : 0.0;
    }

    /// <summary>
    /// 设置临时数据
    /// </summary>
    /// <param name="key">键</param>
    /// <param name="value">值</param>
    public void SetTemporaryData(string key, object value)
    {
        TemporaryData[key] = value;
    }

    /// <summary>
    /// 获取临时数据
    /// </summary>
    /// <param name="key">键</param>
    /// <returns>值，如果不存在则返回null</returns>
    public object? GetTemporaryData(string key)
    {
        return TemporaryData.TryGetValue(key, out var value) ? value : null;
    }

    /// <summary>
    /// 最后发生的错误（用于兼容性）
    /// </summary>
    public Exception? LastError { get; set; }
}

/// <summary>
/// 验证错误
/// </summary>
public class ValidationError
{
    /// <summary>
    /// 错误代码
    /// </summary>
    public string Code { get; set; } = string.Empty;

    /// <summary>
    /// 错误消息
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 错误严重性
    /// </summary>
    public ValidationSeverity Severity { get; set; } = ValidationSeverity.Error;

    /// <summary>
    /// 错误字段
    /// </summary>
    public string? Field { get; set; }

    /// <summary>
    /// 错误值
    /// </summary>
    public object? Value { get; set; }
}

/// <summary>
/// 验证警告
/// </summary>
public class ValidationWarning
{
    /// <summary>
    /// 警告代码
    /// </summary>
    public string Code { get; set; } = string.Empty;

    /// <summary>
    /// 警告消息
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 警告字段
    /// </summary>
    public string? Field { get; set; }

    /// <summary>
    /// 警告值
    /// </summary>
    public object? Value { get; set; }
}

/// <summary>
/// 验证严重性
/// </summary>
public enum ValidationSeverity
{
    /// <summary>
    /// 信息
    /// </summary>
    Info,

    /// <summary>
    /// 警告
    /// </summary>
    Warning,

    /// <summary>
    /// 错误
    /// </summary>
    Error,

    /// <summary>
    /// 严重错误
    /// </summary>
    Critical
}

/// <summary>
/// 节点资源需求
/// </summary>
public class NodeResourceRequirements
{
    /// <summary>
    /// 所需CPU核心数
    /// </summary>
    public double RequiredCpuCores { get; set; } = 0.5;

    /// <summary>
    /// 所需内存（MB）
    /// </summary>
    public int RequiredMemoryMb { get; set; } = 256;

    /// <summary>
    /// 所需磁盘空间（MB）
    /// </summary>
    public int RequiredDiskMb { get; set; } = 100;

    /// <summary>
    /// 所需网络带宽（Mbps）
    /// </summary>
    public int RequiredNetworkMbps { get; set; } = 1;

    /// <summary>
    /// 特殊要求标签
    /// </summary>
    public HashSet<string> RequiredTags { get; set; } = new();

    /// <summary>
    /// 资源需求元数据
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();

    /// <summary>
    /// 预估执行时间
    /// </summary>
    public TimeSpan EstimatedExecutionTime { get; set; } = TimeSpan.FromMilliseconds(100);

    /// <summary>
    /// 所需服务列表
    /// </summary>
    public List<string> RequiredServices { get; set; } = new();

    // 兼容性属性（别名）
    /// <summary>
    /// CPU核心数（兼容性属性）
    /// </summary>
    public double CpuCores
    {
        get => RequiredCpuCores;
        set => RequiredCpuCores = value;
    }





    /// <summary>
    /// 网络带宽Mbps（兼容性属性）
    /// </summary>
    public int NetworkBandwidthMbps
    {
        get => RequiredNetworkMbps;
        set => RequiredNetworkMbps = value;
    }

    /// <summary>
    /// 创建资源需求的深拷贝
    /// </summary>
    /// <returns>资源需求的深拷贝</returns>
    public NodeResourceRequirements Clone()
    {
        return new NodeResourceRequirements
        {
            RequiredCpuCores = RequiredCpuCores,
            RequiredMemoryMb = RequiredMemoryMb,
            RequiredDiskMb = RequiredDiskMb,
            RequiredNetworkMbps = RequiredNetworkMbps,
            RequiredTags = new HashSet<string>(RequiredTags),
            Metadata = new Dictionary<string, object>(Metadata)
        };
    }
}
