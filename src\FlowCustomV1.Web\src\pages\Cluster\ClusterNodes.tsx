import React, { useEffect, useState } from 'react';
import { Tag, Button, Space, Progress, Modal, message, Tooltip } from 'antd';
import {
  ReloadOutlined,
  SettingOutlined,
  PoweroffOutlined,
  RedoOutlined,
  EyeOutlined,
  MonitorOutlined,
  NodeIndexOutlined,
} from '@ant-design/icons';
import { ProTable, ProColumns } from '@ant-design/pro-components';
import { clusterApi } from '@/services/cluster';
import type { NodeInfo } from '@/types/api';
import PageLayout from '@/components/Layout/PageLayout';

// ========== 页面布局配置 ==========
const PAGE_LAYOUT_CONFIG = {
  // 表格滚动高度 - 有统计卡片容器 + 工具栏的页面
  tableScrollY: 'calc(100vh - 520px)'
};

const ClusterNodes: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [nodes, setNodes] = useState<NodeInfo[]>([]);

  // 加载节点数据
  const loadNodes = async () => {
    try {
      setLoading(true);
      const nodeList = await clusterApi.getAllNodes();
      setNodes(nodeList);
    } catch (error) {
      console.error('加载节点数据失败:', error);
      message.error('加载节点数据失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadNodes();
    // 设置定时刷新
    const interval = setInterval(loadNodes, 10000);
    return () => clearInterval(interval);
  }, []);

  // 获取节点状态标签
  const getNodeStatusTag = (status: string) => {
    const statusMap = {
      Online: { color: 'success', text: '在线' },
      Offline: { color: 'error', text: '离线' },
      Busy: { color: 'warning', text: '繁忙' },
      Maintenance: { color: 'default', text: '维护中' },
    };
    const config = statusMap[status as keyof typeof statusMap] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 获取节点类型标签
  const getNodeTypeTag = (type: string) => {
    const typeMap = {
      Master: { color: 'blue', text: 'Master' },
      Worker: { color: 'green', text: 'Worker' },
      Hybrid: { color: 'purple', text: 'Hybrid' },
    };
    const config = typeMap[type as keyof typeof typeMap] || { color: 'default', text: type };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 获取角色标签
  const getRoleTags = (roles: string[]) => {
    const roleColors = {
      Api: 'blue',
      Designer: 'green',
      Validator: 'orange',
      Executor: 'purple',
      Monitor: 'cyan',
      Scheduler: 'magenta',
      Storage: 'red',
    };
    
    return (
      <div className="flex flex-wrap gap-1">
        {roles.map(role => (
          <Tag key={role} color={roleColors[role as keyof typeof roleColors] || 'default'}>
            {role}
          </Tag>
        ))}
      </div>
    );
  };

  // 重启节点
  const handleRestart = (nodeId: string, nodeName: string) => {
    Modal.confirm({
      title: '重启节点',
      content: `确定要重启节点 "${nodeName}" 吗？`,
      onOk: async () => {
        try {
          await clusterApi.restartNode(nodeId);
          message.success('节点重启指令已发送');
          loadNodes();
        } catch (error) {
          message.error('重启节点失败');
        }
      },
    });
  };

  // 关闭节点
  const handleShutdown = (nodeId: string, nodeName: string) => {
    Modal.confirm({
      title: '关闭节点',
      content: `确定要关闭节点 "${nodeName}" 吗？此操作将停止节点上的所有服务。`,
      okType: 'danger',
      onOk: async () => {
        try {
          await clusterApi.shutdownNode(nodeId);
          message.success('节点关闭指令已发送');
          loadNodes();
        } catch (error) {
          message.error('关闭节点失败');
        }
      },
    });
  };

  // 查看节点详情
  const handleViewDetails = (node: NodeInfo) => {
    Modal.info({
      title: `节点详情 - ${node.nodeName}`,
      width: 800,
      content: (
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <strong>节点ID:</strong> {node.nodeId}
            </div>
            <div>
              <strong>节点名称:</strong> {node.nodeName}
            </div>
            <div>
              <strong>节点类型:</strong> {getNodeTypeTag(node.nodeType)}
            </div>
            <div>
              <strong>状态:</strong> {getNodeStatusTag(node.status)}
            </div>
            <div>
              <strong>地址:</strong> {node.address}:{node.port}
            </div>
            <div>
              <strong>版本:</strong> {node.version}
            </div>
            <div>
              <strong>启动时间:</strong> {new Date(node.startedAt).toLocaleString()}
            </div>
            <div>
              <strong>最后心跳:</strong> {new Date(node.lastHeartbeat).toLocaleString()}
            </div>
          </div>
          
          <div>
            <strong>角色:</strong>
            <div className="mt-2">{getRoleTags(node.roles)}</div>
          </div>
          
          <div>
            <strong>能力:</strong>
            <div className="mt-2 flex flex-wrap gap-1">
              {node.capabilities.map(cap => (
                <Tag key={cap}>{cap}</Tag>
              ))}
            </div>
          </div>
          
          <div>
            <strong>标签:</strong>
            <div className="mt-2 flex flex-wrap gap-1">
              {Object.entries(node.tags).map(([key, value]) => (
                <Tag key={key}>{key}: {value}</Tag>
              ))}
            </div>
          </div>
        </div>
      ),
    });
  };

  // 表格列定义
  const columns: ProColumns<NodeInfo>[] = [
    {
      title: '节点名称',
      dataIndex: 'nodeName',
      key: 'nodeName',
      width: 150,
      render: (text, record) => (
        <div>
          <div className="font-medium">{text}</div>
          <div className="text-xs text-gray-500">{record.nodeId.substring(0, 8)}...</div>
        </div>
      ),
    },
    {
      title: '类型',
      dataIndex: 'nodeType',
      key: 'nodeType',
      width: 100,
      render: (_, record) => getNodeTypeTag(record.nodeType),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (_, record) => getNodeStatusTag(record.status),
    },
    {
      title: '地址',
      key: 'address',
      width: 150,
      render: (_, record) => `${record.address}:${record.port}`,
    },
    {
      title: '角色',
      dataIndex: 'roles',
      key: 'roles',
      width: 200,
      render: (_, record) => getRoleTags(record.roles),
    },
    {
      title: 'CPU',
      key: 'cpu',
      width: 80,
      render: (_, record) => (
        <Progress 
          type="circle" 
          size={40} 
          percent={Math.round(record.loadInfo.cpuUsage)} 
          format={(percent) => `${percent}%`}
        />
      ),
    },
    {
      title: '内存',
      key: 'memory',
      width: 80,
      render: (_, record) => (
        <Progress 
          type="circle" 
          size={40} 
          percent={Math.round(record.loadInfo.memoryUsage)} 
          format={(percent) => `${percent}%`}
        />
      ),
    },
    {
      title: '执行任务',
      key: 'executions',
      width: 100,
      render: (_, record) => (
        <div className="text-center">
          <div className="font-semibold">
            {record.loadInfo.activeExecutions}/{record.loadInfo.maxExecutions}
          </div>
          <Progress 
            percent={Math.round((record.loadInfo.activeExecutions / record.loadInfo.maxExecutions) * 100)}
            showInfo={false}
            size="small"
          />
        </div>
      ),
    },
    {
      title: '版本',
      dataIndex: 'version',
      key: 'version',
      width: 100,
      render: (version) => <Tag color="blue">{version}</Tag>,
    },
    {
      title: '最后心跳',
      dataIndex: 'lastHeartbeat',
      key: 'lastHeartbeat',
      width: 160,
      render: (_, record) => {
        const heartbeatTime = new Date(record.lastHeartbeat);
        const now = new Date();
        const diff = now.getTime() - heartbeatTime.getTime();
        const isRecent = diff < 30000; // 30秒内
        
        return (
          <div className={isRecent ? 'text-green-600' : 'text-red-600'}>
            {heartbeatTime.toLocaleString()}
          </div>
        );
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button 
              type="text" 
              size="small" 
              icon={<EyeOutlined />}
              onClick={() => handleViewDetails(record)}
            />
          </Tooltip>
          <Tooltip title="监控指标">
            <Button 
              type="text" 
              size="small" 
              icon={<MonitorOutlined />}
              onClick={() => window.open(`/monitoring/node/${record.nodeId}`, '_blank')}
            />
          </Tooltip>
          <Tooltip title="重启节点">
            <Button
              type="text"
              size="small"
              icon={<RedoOutlined />}
              onClick={() => handleRestart(record.nodeId, record.nodeName)}
              disabled={record.status === 'Offline'}
            />
          </Tooltip>
          <Tooltip title="关闭节点">
            <Button 
              type="text" 
              size="small" 
              danger
              icon={<PoweroffOutlined />}
              onClick={() => handleShutdown(record.nodeId, record.nodeName)}
              disabled={record.status === 'Offline'}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <PageLayout
      title="节点管理"
      description="管理和监控集群中的所有节点"
      icon={<NodeIndexOutlined />}
      actions={
        <Space>
          <Button
            icon={<SettingOutlined />}
            onClick={() => window.open('/cluster/config', '_blank')}
          >
            集群配置
          </Button>
          <Button
            type="primary"
            icon={<ReloadOutlined />}
            loading={loading}
            onClick={loadNodes}
          >
            刷新
          </Button>
        </Space>
      }
    >
      {/* 节点表格 */}
      <div className="workflow-table-container">
        <ProTable<NodeInfo>
        columns={columns}
        dataSource={nodes}
        rowKey="nodeId"
        loading={loading}
        pagination={{
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => 
            `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`,
        }}
          scroll={{
            x: 1400,
            y: PAGE_LAYOUT_CONFIG.tableScrollY
          }}
          search={false}
          toolBarRender={false}
          options={false}
          rowClassName={(record) => {
            if (record.status === 'Offline') return 'bg-red-50';
            if (record.status === 'Busy') return 'bg-yellow-50';
            return '';
          }}
        />
      </div>
    </PageLayout>
  );
};

export default ClusterNodes;
