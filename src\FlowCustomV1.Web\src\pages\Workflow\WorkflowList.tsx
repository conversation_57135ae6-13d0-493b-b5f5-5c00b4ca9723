import React, { useEffect, useState } from 'react';
import {
  Button,
  Table,
  Space,
  Tag,
  Modal,
  message,
  Popconfirm,
  Input,
  Select,
  DatePicker,
  Tooltip
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  PlayCircleOutlined,
  CopyOutlined,
  SettingOutlined,
  ExportOutlined,
  ImportOutlined,
  ReloadOutlined,
  EyeOutlined,
  CloudUploadOutlined,
  CloudDownloadOutlined,
  HistoryOutlined
} from '@ant-design/icons';
import { ProTable, ProColumns } from '@ant-design/pro-components';
import { useNavigate } from 'react-router-dom';
import { workflowApi, executionApi } from '@/services/workflow';
import type { WorkflowDefinition, PublishWorkflowRequest } from '@/types/api';
import { components, getMergeColumns } from '@/utils/ResizableTableUtil';
import LayoutConfigPanel from '@/components/Layout/LayoutConfigPanel';

// ========== 页面布局配置 ==========
const PAGE_LAYOUT_CONFIG = {
  // 🎯 页面类型标识
  pageType: 'simple-list',

  // 🟠 统计卡片容器配置
  hasStatsContainer: false,
  statsContainerHeight: '0px',
  statsContainerMargin: '0px',
  statsContainerPadding: '0px',

  // 🟠 工具栏配置
  hasToolbar: true,
  toolbarHeight: 'var(--layout-toolbar-height)',
  toolbarMargin: 'var(--layout-toolbar-margin)',
  toolbarComplexity: 'high', // 复杂工具栏（搜索+筛选+操作按钮）

  // 🟣 表格配置
  tablePaginationHeight: '20px', // 测试自定义分页栏高度（设置为60px便于观察效果）
  tableHasSelection: true, // 支持行选择

  // 动态计算表格滚动高度，考虑分页栏高度变化
  get tableScrollY() {
    const baseOffset = 300; // 基础偏移量（页面头部 + 工具栏 + 边距）
    const paginationHeight = parseInt(this.tablePaginationHeight) || 20;
    const totalOffset = baseOffset + paginationHeight;
    return `calc(100vh - ${totalOffset}px)`;
  },

  // 📐 页面特定间距调整
  customSpacing: {
    extraOffset: 0, // 无额外偏移
    compactMode: false
  }
};
import PageLayout from '@/components/Layout/PageLayout';

const { Search } = Input;
const { Option } = Select;
const { RangePicker } = DatePicker;

const WorkflowList: React.FC = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [workflows, setWorkflows] = useState<WorkflowDefinition[]>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [publishModalVisible, setPublishModalVisible] = useState(false);
  const [publishingWorkflow, setPublishingWorkflow] = useState<WorkflowDefinition | null>(null);
  const [publishHistoryVisible, setPublishHistoryVisible] = useState(false);
  const [publishHistory, setPublishHistory] = useState<any[]>([]);

  // 布局配置面板状态
  const [layoutConfigVisible, setLayoutConfigVisible] = useState(false);

  // 应用页面特定的布局配置
  useEffect(() => {
    // 应用自定义分页栏高度
    if (PAGE_LAYOUT_CONFIG.tablePaginationHeight !== 'var(--layout-table-pagination-height)') {
      document.documentElement.style.setProperty(
        '--layout-table-pagination-height',
        PAGE_LAYOUT_CONFIG.tablePaginationHeight
      );
      console.log(`🎨 工作流列表页面应用自定义分页栏高度: ${PAGE_LAYOUT_CONFIG.tablePaginationHeight}`);
      console.log(`📐 动态计算的表格滚动高度: ${PAGE_LAYOUT_CONFIG.tableScrollY}`);
    }

    // 清理函数：组件卸载时恢复默认值
    return () => {
      document.documentElement.style.setProperty(
        '--layout-table-pagination-height',
        '20px' // 恢复默认值
      );
    };
  }, []);

  // 搜索和分页状态
  const [searchName, setSearchName] = useState<string>('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [total, setTotal] = useState(0);

  // 监控total值变化
  useEffect(() => {
    console.log('Total值发生变化:', total);
  }, [total]);

  // 列宽状态


  // 加载工作流列表
  const loadWorkflows = async (page = currentPage, size = pageSize, name = searchName, status = statusFilter) => {
    try {
      setLoading(true);
      const params: any = {
        page,
        pageSize: size
      };

      if (name) {
        params.search = name;
      }

      if (status) {
        params.status = status;
      }

      const response = await workflowApi.getWorkflows(params);

      console.log('API响应数据:', response);
      console.log('工作流数据数组:', response.data);
      console.log('数据数组长度:', response.data?.length);
      console.log('总数:', response.totalCount);
      console.log('分页信息:', {
        totalCount: response.totalCount,
        page: response.page,
        pageSize: response.pageSize,
        totalPages: response.totalPages
      });

      setWorkflows(response.data || []);
      setTotal(response.totalCount || 0);
      setCurrentPage(page);
      setPageSize(size);

      console.log('设置后的workflows状态:', response.data || []);
      console.log('设置后的total状态:', response.totalCount || 0);
    } catch (error) {
      console.error('加载工作流列表失败:', error);
      console.error('错误详情:', (error as any).response?.data);
      console.error('请求状态:', (error as any).response?.status);
      message.error(`加载工作流列表失败: ${(error as Error).message}`);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadWorkflows();
  }, []);



  // 删除工作流
  const handleDelete = async (id: string) => {
    try {
      await workflowApi.deleteWorkflow(id);
      message.success('删除成功');
      loadWorkflows();
    } catch (error) {
      message.error('删除失败');
    }
  };

  // 复制工作流
  const handleDuplicate = async (id: string, name: string) => {
    try {
      await workflowApi.copyWorkflow(id, { name: `${name} - 副本` });
      message.success('复制成功');
      loadWorkflows();
    } catch (error) {
      message.error('复制失败');
    }
  };

  // 启动执行
  const handleExecute = async (workflowId: string) => {
    try {
      const result = await executionApi.startExecution(workflowId);
      message.success('工作流已启动执行');
      navigate(`/execution/monitor?id=${result.executionId}`);
    } catch (error) {
      message.error('启动执行失败');
    }
  };

  // 导出工作流
  const handleExport = async (id: string, name: string) => {
    try {
      const blob = await workflowApi.exportWorkflow(id);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${name}.json`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      message.success('导出成功');
    } catch (error) {
      message.error('导出失败');
    }
  };

  // 批量删除
  const handleBatchDelete = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要删除的工作流');
      return;
    }

    Modal.confirm({
      title: '确认删除',
      content: `确定要删除选中的 ${selectedRowKeys.length} 个工作流吗？`,
      onOk: async () => {
        try {
          await Promise.all(selectedRowKeys.map(id => workflowApi.deleteWorkflow(id)));
          message.success('批量删除成功');
          setSelectedRowKeys([]);
          loadWorkflows();
        } catch (error) {
          message.error('批量删除失败');
        }
      },
    });
  };

  // 发布工作流
  const handlePublish = (workflow: WorkflowDefinition) => {
    setPublishingWorkflow(workflow);
    setPublishModalVisible(true);
  };

  // 确认发布
  const handleConfirmPublish = async (values: any) => {
    if (!publishingWorkflow) return;

    try {
      const publishRequest: PublishWorkflowRequest = {
        workflowId: publishingWorkflow.workflowId,
        version: values.version,
        releaseNotes: values.releaseNotes,
        publishToEnvironments: values.environments || ['production'],
      };

      await workflowApi.publishWorkflow(publishRequest);
      message.success('工作流发布成功');
      setPublishModalVisible(false);
      setPublishingWorkflow(null);
      loadWorkflows();
    } catch (error) {
      message.error('发布失败');
    }
  };

  // 取消发布
  const handleUnpublish = async (workflowId: string) => {
    Modal.confirm({
      title: '确认取消发布',
      content: '取消发布后，该工作流将不再可用于执行。确定要继续吗？',
      onOk: async () => {
        try {
          await workflowApi.unpublishWorkflow(workflowId);
          message.success('已取消发布');
          loadWorkflows();
        } catch (error) {
          message.error('取消发布失败');
        }
      },
    });
  };

  // 查看发布历史
  const handleViewPublishHistory = async (workflowId: string) => {
    try {
      const history = await workflowApi.getPublishHistory(workflowId);
      setPublishHistory(history);
      setPublishHistoryVisible(true);
    } catch (error) {
      message.error('获取发布历史失败');
    }
  };

  // 表格列定义状态
  const [columns, setColumns] = useState<ProColumns<WorkflowDefinition>[]>([
    {
      title: '工作流名称',
      dataIndex: 'name',
      key: 'name',
      width: 300,
      ellipsis: true,
      render: (text, record) => (
        <div>
          <div className="font-medium">{text}</div>
          {record.description && (
            <div className="text-gray-500 text-sm">{record.description}</div>
          )}
        </div>
      ),
    },
    {
      title: '版本',
      dataIndex: 'version',
      key: 'version',
      width: 120,
      render: (version) => <Tag color="blue">{version}</Tag>,
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      key: 'isActive',
      width: 100,
      render: (isActive) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '发布状态',
      key: 'publishStatus',
      width: 120,
      render: (_, record) => {
        const getPublishStatusTag = (status?: string, isPublished?: boolean) => {
          if (isPublished) {
            return <Tag color="success" icon={<CloudUploadOutlined />}>已发布</Tag>;
          }
          switch (status) {
            case 'Published':
              return <Tag color="success" icon={<CloudUploadOutlined />}>已发布</Tag>;
            case 'Deprecated':
              return <Tag color="warning">已弃用</Tag>;
            case 'Archived':
              return <Tag color="default">已归档</Tag>;
            default:
              return <Tag color="default">草稿</Tag>;
          }
        };
        return getPublishStatusTag(record.publishStatus, record.isPublished);
      },
    },
    {
      title: '节点数',
      key: 'nodeCount',
      width: 100,
      render: (_, record) => record.nodes?.length || 0,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 180,
      render: (_, record) => new Date(record.createdAt).toLocaleString(),
    },
    {
      title: '最后修改',
      dataIndex: 'lastModifiedAt',
      key: 'lastModifiedAt',
      width: 180,
      render: (_, record) => new Date(record.lastModifiedAt).toLocaleString(),
    },
    {
      title: '标签',
      dataIndex: 'tags',
      key: 'tags',
      width: 150,
      render: (_, record: WorkflowDefinition) => (
        <div>
          {record.tags?.slice(0, 2).map(tag => (
            <Tag key={tag}>{tag}</Tag>
          ))}
          {(record.tags?.length || 0) > 2 && (
            <Tooltip title={record.tags?.slice(2).join(', ') || ''}>
              <Tag>+{(record.tags?.length || 0) - 2}</Tag>
            </Tooltip>
          )}
        </div>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => navigate(`/workflow/detail/${record.workflowId}`)}
            />
          </Tooltip>
          <Tooltip title="编辑信息">
            <Button
              type="text"
              size="small"
              icon={<EditOutlined />}
              onClick={() => navigate(`/workflow/edit/${record.workflowId}`)}
            />
          </Tooltip>
          <Tooltip title="设计器">
            <Button
              type="text"
              size="small"
              icon={<SettingOutlined />}
              onClick={() => navigate(`/workflow/designer/${record.workflowId}`)}
            />
          </Tooltip>
          <Tooltip title="执行">
            <Button 
              type="text" 
              size="small" 
              icon={<PlayCircleOutlined />}
              onClick={() => handleExecute(record.workflowId)}
            />
          </Tooltip>
          <Tooltip title="复制">
            <Button 
              type="text" 
              size="small" 
              icon={<CopyOutlined />}
              onClick={() => handleDuplicate(record.workflowId, record.name)}
            />
          </Tooltip>
          <Tooltip title="导出">
            <Button
              type="text"
              size="small"
              icon={<ExportOutlined />}
              onClick={() => handleExport(record.workflowId, record.name)}
            />
          </Tooltip>
          {record.isPublished ? (
            <Tooltip title="取消发布">
              <Button
                type="text"
                size="small"
                icon={<CloudDownloadOutlined />}
                onClick={() => handleUnpublish(record.workflowId)}
              />
            </Tooltip>
          ) : (
            <Tooltip title="发布">
              <Button
                type="text"
                size="small"
                icon={<CloudUploadOutlined />}
                onClick={() => handlePublish(record)}
              />
            </Tooltip>
          )}
          <Tooltip title="发布历史">
            <Button
              type="text"
              size="small"
              icon={<HistoryOutlined />}
              onClick={() => handleViewPublishHistory(record.workflowId)}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这个工作流吗？"
            onConfirm={() => handleDelete(record.workflowId)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button 
                type="text" 
                size="small" 
                danger 
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ]);

  // 使用可调整列宽的功能
  const mergeColumns = getMergeColumns(columns, setColumns);

  // 行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (keys: React.Key[]) => setSelectedRowKeys(keys as string[]),
  };

  return (
    <PageLayout
      title="工作流管理"
      description="管理和维护系统中的所有工作流"
      actions={
        <Space>
          <Button
            icon={<ImportOutlined />}
            onClick={() => {
              // TODO: 实现导入功能
              message.info('导入功能开发中');
            }}
          >
            导入
          </Button>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => navigate('/workflow/create')}
          >
            新建工作流
          </Button>
        </Space>
      }
    >

      {/* 工具栏 */}
      <div className="toolbar">
        <div className="toolbar-left">
          <Space>
            <Search
              placeholder="搜索工作流名称"
              allowClear
              style={{ width: 300 }}
              value={searchName}
              onChange={(e) => setSearchName(e.target.value)}
              onSearch={(value) => {
                setSearchName(value);
                loadWorkflows(1, pageSize, value, statusFilter);
              }}
            />
            <Select
              placeholder="状态筛选"
              allowClear
              style={{ width: 120 }}
              value={statusFilter}
              onChange={(value) => {
                setStatusFilter(value || '');
                loadWorkflows(1, pageSize, searchName, value || '');
              }}
            >
              <Option value="Draft">草稿</Option>
              <Option value="Published">已发布</Option>
              <Option value="Deprecated">已弃用</Option>
              <Option value="Archived">已归档</Option>
            </Select>
            <RangePicker
              placeholder={['开始日期', '结束日期']}
              onChange={(dates) => {
                // TODO: 实现日期筛选
                console.log('日期筛选:', dates);
              }}
            />
          </Space>
        </div>
        <div className="toolbar-right">
          <Space>
            {selectedRowKeys.length > 0 && (
              <Button 
                danger 
                onClick={handleBatchDelete}
              >
                批量删除 ({selectedRowKeys.length})
              </Button>
            )}
            <Button
              icon={<ReloadOutlined />}
              onClick={() => loadWorkflows(currentPage, pageSize, searchName, statusFilter)}
              loading={loading}
            >
              刷新
            </Button>
            <Button
              icon={<SettingOutlined />}
              onClick={() => setLayoutConfigVisible(true)}
              title="布局配置"
            >
              布局
            </Button>
          </Space>
        </div>
      </div>

      {/* 工作流表格 */}
      <div
        className="workflow-table-container"
        style={{
          flex: 1,
          /* 移除固定高度，让表格占满剩余空间 */
          border: '1px solid #f0f0f0',
          borderRadius: '6px',
          overflow: 'hidden', /* 改为hidden，让ProTable内部处理滚动 */
          display: 'flex',
          flexDirection: 'column'
        }}
      >
        <ProTable<WorkflowDefinition>
        columns={mergeColumns}
        dataSource={workflows}
        rowKey="workflowId"
        loading={loading}
        rowSelection={rowSelection}
        components={components}
        pagination={{
          current: currentPage,
          pageSize: pageSize,
          total: total,
          showSizeChanger: true,
          showQuickJumper: true,
          pageSizeOptions: ['10', '20', '50', '100'],
          showTotal: (total, range) =>
            `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`,
          onChange: (page, size) => {
            loadWorkflows(page, size, searchName, statusFilter);
          },
          onShowSizeChange: (_, size) => {
            loadWorkflows(1, size, searchName, statusFilter);
          }
        }}
        scroll={{
          x: 'max-content',
          y: PAGE_LAYOUT_CONFIG.tableScrollY
        }}
        style={{
          height: '100%',
          display: 'flex',
          flexDirection: 'column'
        }}
        tableStyle={{
          flex: 1,
          overflow: 'auto'
        }}
        search={false}
        toolBarRender={false}
        options={false}
        columnsState={{
          persistenceKey: 'workflow-list-columns',
          persistenceType: 'localStorage',
        }}
        size="small"
      />
      </div>

      {/* 发布工作流模态框 */}
      <Modal
        title="发布工作流"
        open={publishModalVisible}
        onCancel={() => {
          setPublishModalVisible(false);
          setPublishingWorkflow(null);
        }}
        footer={null}
        width={600}
      >
        {publishingWorkflow && (
          <div>
            <div className="mb-4">
              <h4 className="text-lg font-medium">{publishingWorkflow.name}</h4>
              <p className="text-gray-500">{publishingWorkflow.description}</p>
              <div className="mt-2">
                <Tag color="blue">版本: {publishingWorkflow.version}</Tag>
                <Tag color={publishingWorkflow.isActive ? 'green' : 'red'}>
                  {publishingWorkflow.isActive ? '启用' : '禁用'}
                </Tag>
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">发布版本</label>
                <Input
                  placeholder="输入发布版本号，如 v1.0.0"
                  defaultValue={`v${publishingWorkflow.version}`}
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">发布环境</label>
                <Select
                  mode="multiple"
                  placeholder="选择发布环境"
                  defaultValue={['production']}
                  style={{ width: '100%' }}
                >
                  <Option value="development">开发环境</Option>
                  <Option value="testing">测试环境</Option>
                  <Option value="staging">预发布环境</Option>
                  <Option value="production">生产环境</Option>
                </Select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">发布说明</label>
                <Input.TextArea
                  rows={4}
                  placeholder="输入本次发布的更新内容和说明..."
                />
              </div>

              <div className="flex justify-end space-x-2 pt-4">
                <Button onClick={() => setPublishModalVisible(false)}>
                  取消
                </Button>
                <Button
                  type="primary"
                  icon={<CloudUploadOutlined />}
                  onClick={() => handleConfirmPublish({
                    version: `v${publishingWorkflow.version}`,
                    environments: ['production'],
                    releaseNotes: ''
                  })}
                >
                  确认发布
                </Button>
              </div>
            </div>
          </div>
        )}
      </Modal>

      {/* 发布历史模态框 */}
      <Modal
        title="发布历史"
        open={publishHistoryVisible}
        onCancel={() => setPublishHistoryVisible(false)}
        footer={null}
        width={800}
      >
        <Table
          dataSource={publishHistory}
          rowKey="version"
          pagination={false}
          columns={[
            {
              title: '版本',
              dataIndex: 'version',
              key: 'version',
              render: (version) => <Tag color="blue">{version}</Tag>,
            },
            {
              title: '发布时间',
              dataIndex: 'publishedAt',
              key: 'publishedAt',
              render: (time) => new Date(time).toLocaleString(),
            },
            {
              title: '发布人',
              dataIndex: 'publishedBy',
              key: 'publishedBy',
            },
            {
              title: '发布环境',
              dataIndex: 'environments',
              key: 'environments',
              render: (environments: string[]) => (
                <div>
                  {environments.map(env => (
                    <Tag key={env}>{env}</Tag>
                  ))}
                </div>
              ),
            },
            {
              title: '发布说明',
              dataIndex: 'releaseNotes',
              key: 'releaseNotes',
              ellipsis: true,
            },
          ]}
        />
      </Modal>

      {/* 布局配置面板 */}
      <LayoutConfigPanel
        visible={layoutConfigVisible}
        onClose={() => setLayoutConfigVisible(false)}
      />
    </PageLayout>
  );
};

export default WorkflowList;
