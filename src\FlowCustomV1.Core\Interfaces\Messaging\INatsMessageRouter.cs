using FlowCustomV1.Core.Models.Messaging;

namespace FlowCustomV1.Core.Interfaces.Messaging;

/// <summary>
/// NATS消息路由服务接口
/// 提供智能消息路由和负载均衡功能
/// </summary>
public interface INatsMessageRouter
{
    #region 路由规则管理

    /// <summary>
    /// 注册路由规则
    /// </summary>
    /// <param name="pattern">主题模式</param>
    /// <param name="targetRole">目标节点角色</param>
    /// <param name="routingStrategy">路由策略</param>
    void RegisterRoute(string pattern, string targetRole, RoutingStrategy routingStrategy = RoutingStrategy.RoundRobin);

    /// <summary>
    /// 移除路由规则
    /// </summary>
    /// <param name="pattern">主题模式</param>
    void UnregisterRoute(string pattern);

    /// <summary>
    /// 获取所有路由规则
    /// </summary>
    /// <returns>路由规则列表</returns>
    IReadOnlyList<RouteRule> GetRoutes();

    #endregion

    #region 消息路由

    /// <summary>
    /// 路由消息到最佳节点
    /// </summary>
    /// <param name="message">消息对象</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>路由任务</returns>
    Task RouteMessageAsync(IMessage message, CancellationToken cancellationToken = default);

    /// <summary>
    /// 路由消息到指定角色的节点
    /// </summary>
    /// <param name="message">消息对象</param>
    /// <param name="targetRole">目标角色</param>
    /// <param name="strategy">路由策略</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>路由任务</returns>
    Task RouteToRoleAsync(IMessage message, string targetRole, RoutingStrategy strategy = RoutingStrategy.RoundRobin, CancellationToken cancellationToken = default);

    /// <summary>
    /// 广播消息到所有节点
    /// </summary>
    /// <param name="message">消息对象</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>广播任务</returns>
    Task BroadcastAsync(IMessage message, CancellationToken cancellationToken = default);

    /// <summary>
    /// 广播消息到指定角色的所有节点
    /// </summary>
    /// <param name="message">消息对象</param>
    /// <param name="targetRole">目标角色</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>广播任务</returns>
    Task BroadcastToRoleAsync(IMessage message, string targetRole, CancellationToken cancellationToken = default);

    /// <summary>
    /// 发布消息到指定主题
    /// </summary>
    /// <param name="subject">主题</param>
    /// <param name="message">消息对象</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发布任务</returns>
    Task PublishAsync<T>(string subject, T message, CancellationToken cancellationToken = default) where T : class;

    #endregion

    #region 节点管理

    /// <summary>
    /// 注册节点
    /// </summary>
    /// <param name="nodeInfo">节点信息</param>
    void RegisterNode(NodeInfo nodeInfo);

    /// <summary>
    /// 注销节点
    /// </summary>
    /// <param name="nodeId">节点ID</param>
    void UnregisterNode(string nodeId);

    /// <summary>
    /// 更新节点负载信息
    /// </summary>
    /// <param name="nodeId">节点ID</param>
    /// <param name="loadInfo">负载信息</param>
    void UpdateNodeLoad(string nodeId, NodeLoadInfo loadInfo);

    /// <summary>
    /// 获取指定角色的可用节点
    /// </summary>
    /// <param name="role">节点角色</param>
    /// <returns>节点列表</returns>
    IReadOnlyList<NodeInfo> GetAvailableNodes(string role);

    /// <summary>
    /// 获取所有节点
    /// </summary>
    /// <returns>节点列表</returns>
    IReadOnlyList<NodeInfo> GetAllNodes();

    #endregion

    #region 事件

    /// <summary>
    /// 节点状态变更事件
    /// </summary>
    event EventHandler<NodeStatusChangedEventArgs>? NodeStatusChanged;

    /// <summary>
    /// 路由失败事件
    /// </summary>
    event EventHandler<RoutingFailedEventArgs>? RoutingFailed;

    #endregion
}

/// <summary>
/// 路由规则
/// </summary>
public class RouteRule
{
    /// <summary>
    /// 主题模式
    /// </summary>
    public string Pattern { get; set; } = string.Empty;

    /// <summary>
    /// 目标角色
    /// </summary>
    public string TargetRole { get; set; } = string.Empty;

    /// <summary>
    /// 路由策略
    /// </summary>
    public RoutingStrategy Strategy { get; set; } = RoutingStrategy.RoundRobin;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool Enabled { get; set; } = true;
}

/// <summary>
/// 路由策略枚举
/// </summary>
public enum RoutingStrategy
{
    /// <summary>
    /// 轮询
    /// </summary>
    RoundRobin,

    /// <summary>
    /// 最少连接
    /// </summary>
    LeastConnections,

    /// <summary>
    /// 最低负载
    /// </summary>
    LeastLoad,

    /// <summary>
    /// 随机
    /// </summary>
    Random,

    /// <summary>
    /// 加权轮询
    /// </summary>
    WeightedRoundRobin,

    /// <summary>
    /// 最快响应
    /// </summary>
    FastestResponse
}

/// <summary>
/// 节点信息
/// </summary>
public class NodeInfo
{
    /// <summary>
    /// 节点ID
    /// </summary>
    public string NodeId { get; set; } = string.Empty;

    /// <summary>
    /// 节点角色
    /// </summary>
    public string Role { get; set; } = string.Empty;

    /// <summary>
    /// 节点状态
    /// </summary>
    public string Status { get; set; } = "Online";

    /// <summary>
    /// 节点地址
    /// </summary>
    public string Address { get; set; } = string.Empty;

    /// <summary>
    /// 负载信息
    /// </summary>
    public NodeLoadInfo LoadInfo { get; set; } = new();

    /// <summary>
    /// 节点能力
    /// </summary>
    public List<string> Capabilities { get; set; } = new();

    /// <summary>
    /// 权重
    /// </summary>
    public int Weight { get; set; } = 1;

    /// <summary>
    /// 最后心跳时间
    /// </summary>
    public DateTime LastHeartbeat { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 注册时间
    /// </summary>
    public DateTime RegisteredAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 是否在线
    /// </summary>
    public bool IsOnline => Status == "Online" && 
                           DateTime.UtcNow - LastHeartbeat < TimeSpan.FromMinutes(2);
}

/// <summary>
/// 节点状态变更事件参数
/// </summary>
public class NodeStatusChangedEventArgs : EventArgs
{
    /// <summary>
    /// 节点ID
    /// </summary>
    public string NodeId { get; set; } = string.Empty;

    /// <summary>
    /// 旧状态
    /// </summary>
    public string OldStatus { get; set; } = string.Empty;

    /// <summary>
    /// 新状态
    /// </summary>
    public string NewStatus { get; set; } = string.Empty;

    /// <summary>
    /// 变更时间
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 路由失败事件参数
/// </summary>
public class RoutingFailedEventArgs : EventArgs
{
    /// <summary>
    /// 消息ID
    /// </summary>
    public string MessageId { get; set; } = string.Empty;

    /// <summary>
    /// 目标主题
    /// </summary>
    public string Subject { get; set; } = string.Empty;

    /// <summary>
    /// 目标角色
    /// </summary>
    public string? TargetRole { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string Error { get; set; } = string.Empty;

    /// <summary>
    /// 失败时间
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}
