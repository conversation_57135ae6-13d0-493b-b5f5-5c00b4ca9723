# NATS Server Configuration - Production Environment
# Node 2 of 3 in FlowCustomV1 Production Cluster

# Server Identity
server_name: "flowcustom-prod-nats-2"

# Network Configuration - Production ports
port: 4222
http_port: 8222

# Logging Configuration - Production environment (minimal)
# log_file: "/var/log/nats/nats-server.log"  # 使用标准输出
logtime: true
debug: false
trace: false
log_size_limit: 100MB
max_traced_msg_len: 0

# JetStream Configuration
jetstream {
    # Storage directory
    store_dir: "/data/jetstream"
    
    # Memory and file storage limits
    max_memory_store: 1GB
    max_file_store: 10GB
    
    # Domain for this JetStream cluster
    domain: "flowcustom-cluster"
}

# Cluster Configuration
cluster {
    name: "flowcustom-nats-cluster"
    host: "0.0.0.0"
    port: 6222
    
    # Routes to other cluster members
    routes: [
        "nats://nats-1:6222",
        "nats://nats-2:6222", 
        "nats://nats-3:6222"
    ]
}

# WebSocket Configuration for Frontend
websocket {
    host: "0.0.0.0"
    port: 8080
    no_tls: true
    
    # CORS settings for web clients
    same_origin: false
    allowed_origins: ["*"]
}

# System Account for JetStream management
system_account: "$SYS"

# Accounts Configuration
accounts {
    # System account
    $SYS {
        users: [
            {
                user: "sys"
                password: "sys_password"
            }
        ]
    }
    
    # FlowCustom application account
    FLOWCUSTOM {
        users: [
            {
                user: "flowcustom"
                password: "flowcustom_password"
            }
        ]
        
        # JetStream configuration for this account
        jetstream: {
            max_mem: 512MB
            max_file: 5GB
            max_streams: 100
            max_consumers: 1000
        }
    }
}

# Default account mapping
no_auth_user: "flowcustom"

# Performance Tuning
max_connections: 64K
max_subscriptions: 0
max_payload: 1MB
max_pending: 64MB

# Write deadline for slow consumers
write_deadline: "10s"

# Ping interval and max outstanding pings
ping_interval: "2m"
ping_max: 2

# Slow consumer threshold
max_control_line: 4KB
