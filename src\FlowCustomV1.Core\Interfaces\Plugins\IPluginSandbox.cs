using FlowCustomV1.Core.Models.Plugins;
using FlowCustomV1.Core.Models.Workflow;

namespace FlowCustomV1.Core.Interfaces.Plugins;

/// <summary>
/// 插件沙箱接口
/// 提供插件执行隔离和安全控制
/// </summary>
public interface IPluginSandbox
{
    /// <summary>
    /// 初始化沙箱
    /// </summary>
    /// <param name="sandboxConfig">沙箱配置</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>初始化任务</returns>
    Task InitializeAsync(SandboxConfiguration sandboxConfig, CancellationToken cancellationToken = default);

    /// <summary>
    /// 在沙箱中执行插件
    /// </summary>
    /// <param name="executor">节点执行器</param>
    /// <param name="context">执行上下文</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>执行结果</returns>
    Task<SandboxExecutionResult> ExecuteInSandboxAsync(
        INodeExecutor executor,
        NodeExecutionContext context,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 创建隔离的执行环境
    /// </summary>
    /// <param name="pluginInfo">插件信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>执行环境</returns>
    Task<IsolatedExecutionEnvironment> CreateIsolatedEnvironmentAsync(
        PluginInfo pluginInfo,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 销毁执行环境
    /// </summary>
    /// <param name="environment">执行环境</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>销毁任务</returns>
    Task DestroyEnvironmentAsync(IsolatedExecutionEnvironment environment, CancellationToken cancellationToken = default);

    /// <summary>
    /// 设置资源限制
    /// </summary>
    /// <param name="nodeType">节点类型</param>
    /// <param name="limits">资源限制</param>
    /// <returns>设置任务</returns>
    Task SetResourceLimitsAsync(string nodeType, ResourceLimits limits);

    /// <summary>
    /// 获取资源使用情况
    /// </summary>
    /// <param name="nodeType">节点类型</param>
    /// <returns>资源使用情况</returns>
    Task<ResourceUsage> GetResourceUsageAsync(string nodeType);

    /// <summary>
    /// 检查权限
    /// </summary>
    /// <param name="nodeType">节点类型</param>
    /// <param name="permission">权限</param>
    /// <returns>是否有权限</returns>
    bool CheckPermission(string nodeType, string permission);

    /// <summary>
    /// 设置权限策略
    /// </summary>
    /// <param name="nodeType">节点类型</param>
    /// <param name="policy">权限策略</param>
    /// <returns>设置任务</returns>
    Task SetPermissionPolicyAsync(string nodeType, PermissionPolicy policy);

    /// <summary>
    /// 监控插件执行
    /// </summary>
    /// <param name="nodeType">节点类型</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>监控任务</returns>
    Task MonitorPluginExecutionAsync(string nodeType, CancellationToken cancellationToken = default);

    /// <summary>
    /// 终止插件执行
    /// </summary>
    /// <param name="nodeType">节点类型</param>
    /// <param name="reason">终止原因</param>
    /// <returns>终止任务</returns>
    Task TerminatePluginExecutionAsync(string nodeType, string reason);

    /// <summary>
    /// 获取沙箱统计信息
    /// </summary>
    /// <returns>沙箱统计信息</returns>
    SandboxStatistics GetSandboxStatistics();

    /// <summary>
    /// 资源超限事件
    /// </summary>
    event EventHandler<ResourceLimitExceededEventArgs> ResourceLimitExceeded;

    /// <summary>
    /// 权限违规事件
    /// </summary>
    event EventHandler<PermissionViolationEventArgs> PermissionViolation;

    /// <summary>
    /// 执行超时事件
    /// </summary>
    event EventHandler<ExecutionTimeoutEventArgs> ExecutionTimeout;
}
