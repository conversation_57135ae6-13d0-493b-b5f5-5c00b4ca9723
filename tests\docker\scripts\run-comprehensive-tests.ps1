# FlowCustomV1 Docker环境综合测试脚本
# 版本: v0.0.1.8
# 日期: 2025-09-07

param(
    [switch]$SkipBuild,
    [switch]$Verbose,
    [int]$TimeoutSeconds = 300
)

# 测试结果记录
$TestResults = @()
$StartTime = Get-Date

# 日志函数
function Write-TestLog {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $color = switch ($Level) {
        "ERROR" { "Red" }
        "WARN" { "Yellow" }
        "SUCCESS" { "Green" }
        default { "White" }
    }
    Write-Host "[$timestamp] [$Level] $Message" -ForegroundColor $color
}

function Add-TestResult {
    param(
        [string]$TestName,
        [bool]$Passed,
        [string]$Details = "",
        [double]$Duration = 0
    )
    
    $script:TestResults += [PSCustomObject]@{
        TestName = $TestName
        Passed = $Passed
        Details = $Details
        Duration = $Duration
        Timestamp = Get-Date
    }
    
    $status = if ($Passed) { "✅ PASS" } else { "❌ FAIL" }
    Write-TestLog "$status - $TestName $(if($Details) { "($Details)" })" $(if($Passed) { "SUCCESS" } else { "ERROR" })
}

function Test-ServiceHealth {
    param([string]$Url, [string]$ServiceName, [int]$TimeoutSec = 10)
    
    try {
        $response = Invoke-WebRequest -Uri $Url -TimeoutSec $TimeoutSec -UseBasicParsing
        return $response.StatusCode -eq 200
    }
    catch {
        Write-TestLog "Service $ServiceName health check failed: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Test-ContainerStatus {
    param([string]$ContainerName)
    
    try {
        $status = docker inspect --format='{{.State.Status}}' $ContainerName 2>$null
        return $status -eq "running"
    }
    catch {
        return $false
    }
}

function Wait-ForContainer {
    param([string]$ContainerName, [int]$TimeoutSec = 60)
    
    $elapsed = 0
    while ($elapsed -lt $TimeoutSec) {
        if (Test-ContainerStatus $ContainerName) {
            return $true
        }
        Start-Sleep -Seconds 2
        $elapsed += 2
    }
    return $false
}

# 主测试流程
Write-TestLog "Starting FlowCustomV1 Docker Environment Comprehensive Tests" "INFO"
Write-TestLog "Test Parameters: SkipBuild=$SkipBuild, Verbose=$Verbose, Timeout=$TimeoutSeconds" "INFO"

try {
    # 设置工作目录
    $testDir = Split-Path -Parent $PSScriptRoot
    Set-Location $testDir
    Write-TestLog "工作目录: $testDir" "INFO"

    # 测试1: 环境清理
    Write-TestLog "执行测试1: 环境清理" "INFO"
    $testStart = Get-Date
    
    try {
        docker-compose -f docker-compose.simple.yml down -v 2>$null
        Add-TestResult "环境清理" $true "旧环境已清理" ((Get-Date) - $testStart).TotalSeconds
    }
    catch {
        Add-TestResult "环境清理" $false $_.Exception.Message ((Get-Date) - $testStart).TotalSeconds
    }

    # 测试2: 镜像构建
    if (-not $SkipBuild) {
        Write-TestLog "执行测试2: 镜像构建" "INFO"
        $testStart = Get-Date
        
        try {
            $buildOutput = docker-compose -f docker-compose.simple.yml build 2>&1
            if ($LASTEXITCODE -eq 0) {
                Add-TestResult "镜像构建" $true "构建成功" ((Get-Date) - $testStart).TotalSeconds
            } else {
                Add-TestResult "镜像构建" $false "构建失败: $buildOutput" ((Get-Date) - $testStart).TotalSeconds
                throw "镜像构建失败"
            }
        }
        catch {
            Add-TestResult "镜像构建" $false $_.Exception.Message ((Get-Date) - $testStart).TotalSeconds
            throw
        }
    }

    # 测试3: 基础设施启动
    Write-TestLog "执行测试3: 基础设施启动" "INFO"
    $testStart = Get-Date
    
    try {
        docker-compose -f docker-compose.simple.yml up -d nats mysql
        
        # 等待基础设施就绪
        Start-Sleep -Seconds 30
        
        $natsHealthy = Test-ContainerStatus "flowcustom-test-nats"
        $mysqlHealthy = Test-ContainerStatus "flowcustom-test-mysql"
        
        if ($natsHealthy -and $mysqlHealthy) {
            Add-TestResult "基础设施启动" $true "NATS和MySQL启动成功" ((Get-Date) - $testStart).TotalSeconds
        } else {
            Add-TestResult "基础设施启动" $false "NATS:$natsHealthy, MySQL:$mysqlHealthy" ((Get-Date) - $testStart).TotalSeconds
        }
    }
    catch {
        Add-TestResult "基础设施启动" $false $_.Exception.Message ((Get-Date) - $testStart).TotalSeconds
    }

    # 测试4: NATS服务验证
    Write-TestLog "执行测试4: NATS服务验证" "INFO"
    $testStart = Get-Date
    
    $natsHealthy = Test-ServiceHealth "http://localhost:8222/healthz" "NATS"
    Add-TestResult "NATS服务验证" $natsHealthy $(if($natsHealthy) { "健康检查通过" } else { "健康检查失败" }) ((Get-Date) - $testStart).TotalSeconds

    # 测试5: MySQL服务验证
    Write-TestLog "执行测试5: MySQL服务验证" "INFO"
    $testStart = Get-Date
    
    try {
        $mysqlTest = docker exec flowcustom-test-mysql mysql -uflowcustom -pTestPassword123! -e "SELECT 1;" 2>$null
        $mysqlHealthy = $LASTEXITCODE -eq 0
        Add-TestResult "MySQL服务验证" $mysqlHealthy $(if($mysqlHealthy) { "数据库连接成功" } else { "数据库连接失败" }) ((Get-Date) - $testStart).TotalSeconds
    }
    catch {
        Add-TestResult "MySQL服务验证" $false $_.Exception.Message ((Get-Date) - $testStart).TotalSeconds
    }

    # 测试6: 应用节点启动
    Write-TestLog "执行测试6: 应用节点启动" "INFO"
    $testStart = Get-Date
    
    try {
        docker-compose -f docker-compose.simple.yml up -d master-node worker-node
        
        # 等待应用节点启动
        Start-Sleep -Seconds 45
        
        $masterHealthy = Test-ContainerStatus "flowcustom-test-master"
        $workerHealthy = Test-ContainerStatus "flowcustom-test-worker"
        
        if ($masterHealthy -and $workerHealthy) {
            Add-TestResult "应用节点启动" $true "Master和Worker节点启动成功" ((Get-Date) - $testStart).TotalSeconds
        } else {
            Add-TestResult "应用节点启动" $false "Master:$masterHealthy, Worker:$workerHealthy" ((Get-Date) - $testStart).TotalSeconds
        }
    }
    catch {
        Add-TestResult "应用节点启动" $false $_.Exception.Message ((Get-Date) - $testStart).TotalSeconds
    }

    # 测试7: 配置验证
    Write-TestLog "执行测试7: 配置验证" "INFO"
    $testStart = Get-Date
    
    try {
        $masterEnv = docker exec flowcustom-test-master env | Select-String "Nats__Servers__0"
        $workerEnv = docker exec flowcustom-test-worker env | Select-String "Nats__Servers__0"
        
        $configValid = ($masterEnv -match "nats://nats:4222") -and ($workerEnv -match "nats://nats:4222")
        Add-TestResult "配置验证" $configValid $(if($configValid) { "环境变量配置正确" } else { "环境变量配置错误" }) ((Get-Date) - $testStart).TotalSeconds
    }
    catch {
        Add-TestResult "配置验证" $false $_.Exception.Message ((Get-Date) - $testStart).TotalSeconds
    }

    # 测试8: 应用启动验证
    Write-TestLog "执行测试8: 应用启动验证" "INFO"
    $testStart = Get-Date
    
    try {
        $masterLogs = docker logs flowcustom-test-master 2>&1 | Select-String "Application started"
        $workerLogs = docker logs flowcustom-test-worker 2>&1 | Select-String "Application started"
        
        $appStarted = ($masterLogs.Count -gt 0) -and ($workerLogs.Count -gt 0)
        Add-TestResult "应用启动验证" $appStarted $(if($appStarted) { "应用成功启动" } else { "应用启动失败" }) ((Get-Date) - $testStart).TotalSeconds
    }
    catch {
        Add-TestResult "应用启动验证" $false $_.Exception.Message ((Get-Date) - $testStart).TotalSeconds
    }

    # 测试9: 节点通信验证
    Write-TestLog "执行测试9: 节点通信验证" "INFO"
    $testStart = Get-Date
    
    try {
        # 等待节点通信建立
        Start-Sleep -Seconds 15
        
        $masterHeartbeat = docker logs flowcustom-test-master 2>&1 | Select-String "heartbeat" | Select-Object -Last 1
        $workerHeartbeat = docker logs flowcustom-test-worker 2>&1 | Select-String "heartbeat" | Select-Object -Last 1
        
        $communicationWorking = ($masterHeartbeat -ne $null) -and ($workerHeartbeat -ne $null)
        Add-TestResult "节点通信验证" $communicationWorking $(if($communicationWorking) { "节点心跳正常" } else { "节点心跳异常" }) ((Get-Date) - $testStart).TotalSeconds
    }
    catch {
        Add-TestResult "节点通信验证" $false $_.Exception.Message ((Get-Date) - $testStart).TotalSeconds
    }

    # 测试10: API功能验证
    Write-TestLog "执行测试10: API功能验证" "INFO"
    $testStart = Get-Date
    
    # 等待API就绪
    Start-Sleep -Seconds 10
    
    try {
        # 尝试多个可能的健康检查端点
        $endpoints = @("/health", "/ready", "/live", "/api/health")
        $apiWorking = $false
        $workingEndpoint = ""
        
        foreach ($endpoint in $endpoints) {
            try {
                $response = Invoke-WebRequest -Uri "http://localhost:5001$endpoint" -TimeoutSec 5 -UseBasicParsing -ErrorAction SilentlyContinue
                if ($response.StatusCode -eq 200) {
                    $apiWorking = $true
                    $workingEndpoint = $endpoint
                    break
                }
            }
            catch {
                # 继续尝试下一个端点
            }
        }
        
        Add-TestResult "API功能验证" $apiWorking $(if($apiWorking) { "API端点 $workingEndpoint 响应正常" } else { "所有API端点无响应" }) ((Get-Date) - $testStart).TotalSeconds
    }
    catch {
        Add-TestResult "API功能验证" $false $_.Exception.Message ((Get-Date) - $testStart).TotalSeconds
    }

    # 测试11: 容器资源使用
    Write-TestLog "执行测试11: 容器资源使用" "INFO"
    $testStart = Get-Date
    
    try {
        $stats = docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}" 2>$null
        $resourcesOk = $stats -ne $null
        Add-TestResult "容器资源使用" $resourcesOk $(if($resourcesOk) { "资源使用正常" } else { "无法获取资源信息" }) ((Get-Date) - $testStart).TotalSeconds
    }
    catch {
        Add-TestResult "容器资源使用" $false $_.Exception.Message ((Get-Date) - $testStart).TotalSeconds
    }

    # 测试12: 故障恢复测试
    Write-TestLog "执行测试12: 故障恢复测试" "INFO"
    $testStart = Get-Date
    
    try {
        # 重启Worker节点
        docker restart flowcustom-test-worker
        Start-Sleep -Seconds 30
        
        $workerRecovered = Test-ContainerStatus "flowcustom-test-worker"
        Add-TestResult "故障恢复测试" $workerRecovered $(if($workerRecovered) { "Worker节点重启成功" } else { "Worker节点重启失败" }) ((Get-Date) - $testStart).TotalSeconds
    }
    catch {
        Add-TestResult "故障恢复测试" $false $_.Exception.Message ((Get-Date) - $testStart).TotalSeconds
    }

}
catch {
    Write-TestLog "测试执行过程中发生严重错误: $($_.Exception.Message)" "ERROR"
}
finally {
    # 生成测试报告
    $EndTime = Get-Date
    $TotalDuration = ($EndTime - $StartTime).TotalSeconds
    
    Write-TestLog "测试完成，生成报告..." "INFO"
    
    # 统计结果
    $TotalTests = $TestResults.Count
    $PassedTests = ($TestResults | Where-Object { $_.Passed }).Count
    $FailedTests = $TotalTests - $PassedTests
    $SuccessRate = if ($TotalTests -gt 0) { [math]::Round(($PassedTests / $TotalTests) * 100, 2) } else { 0 }
    
    # 输出测试报告
    Write-Host "`n" + "="*80 -ForegroundColor Cyan
    Write-Host "FlowCustomV1 Docker环境综合测试报告" -ForegroundColor Cyan
    Write-Host "="*80 -ForegroundColor Cyan
    Write-Host "测试时间: $StartTime - $EndTime" -ForegroundColor White
    Write-Host "总耗时: $([math]::Round($TotalDuration, 2))秒" -ForegroundColor White
    Write-Host "总测试数: $TotalTests" -ForegroundColor White
    Write-Host "通过测试: $PassedTests" -ForegroundColor Green
    Write-Host "失败测试: $FailedTests" -ForegroundColor Red
    Write-Host "成功率: $SuccessRate%" -ForegroundColor $(if($SuccessRate -ge 80) { "Green" } else { "Red" })
    Write-Host ""
    
    # 详细结果
    Write-Host "详细测试结果:" -ForegroundColor Yellow
    Write-Host "-"*80 -ForegroundColor Yellow
    
    foreach ($result in $TestResults) {
        $status = if ($result.Passed) { "✅ PASS" } else { "❌ FAIL" }
        $duration = [math]::Round($result.Duration, 2)
        Write-Host "$status $($result.TestName) (${duration}s)" -ForegroundColor $(if($result.Passed) { "Green" } else { "Red" })
        if ($result.Details) {
            Write-Host "    详情: $($result.Details)" -ForegroundColor Gray
        }
    }
    
    # 失败测试汇总
    $failedTests = $TestResults | Where-Object { -not $_.Passed }
    if ($failedTests.Count -gt 0) {
        Write-Host "`n失败测试汇总:" -ForegroundColor Red
        Write-Host "-"*80 -ForegroundColor Red
        foreach ($failed in $failedTests) {
            Write-Host "❌ $($failed.TestName): $($failed.Details)" -ForegroundColor Red
        }
    }
    
    Write-Host "`n" + "="*80 -ForegroundColor Cyan
    
    # 保存测试报告到文件
    $reportPath = "test-report-$(Get-Date -Format 'yyyyMMdd-HHmmss').json"
    $TestResults | ConvertTo-Json -Depth 3 | Out-File $reportPath
    Write-TestLog "测试报告已保存到: $reportPath" "INFO"
    
    # 返回退出码
    if ($FailedTests -eq 0) {
        Write-TestLog "所有测试通过！" "SUCCESS"
        exit 0
    } else {
        Write-TestLog "$FailedTests 个测试失败" "ERROR"
        exit 1
    }
}
