#!/usr/bin/env python3
"""
FlowCustomV1 性能测试套件主入口
统一管理和执行所有性能测试脚本
"""

import sys
import os
import subprocess
import time
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class PerformanceTestSuite:
    def __init__(self):
        self.test_scripts = {
            "quick": {
                "file": "quick_performance_test.py",
                "name": "快速性能验证",
                "description": "快速验证系统基本性能",
                "duration": "约30秒"
            },
            "comprehensive": {
                "file": "performance_test.py", 
                "name": "综合性能测试",
                "description": "全面测试API性能表现",
                "duration": "约2-3分钟"
            },
            "analysis": {
                "file": "performance_analysis.py",
                "name": "深度性能分析", 
                "description": "深入分析特定性能问题",
                "duration": "约1-2分钟"
            },
            "infrastructure": {
                "file": "infrastructure_stress_test.py",
                "name": "基础设施压力测试",
                "description": "测试NATS和MySQL性能",
                "duration": "约3-5分钟"
            },
            "extreme": {
                "file": "extreme_stress_test.py",
                "name": "极限压力测试",
                "description": "测试系统极限负载能力",
                "duration": "约5-10分钟"
            }
        }
        
    def show_menu(self):
        """显示测试菜单"""
        print("🎯 FlowCustomV1 性能测试套件")
        print("=" * 50)
        print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        print("📋 可用测试:")
        for key, test in self.test_scripts.items():
            print(f"  {key:12} - {test['name']}")
            print(f"               {test['description']}")
            print(f"               执行时间: {test['duration']}")
            print()
            
        print("🔧 特殊选项:")
        print("  all         - 执行所有测试（按推荐顺序）")
        print("  daily       - 日常开发测试套件")
        print("  diagnosis   - 性能问题诊断套件")
        print("  capacity    - 系统容量评估套件")
        print("  help        - 显示详细帮助")
        print("  exit        - 退出")
        print()
        
    def run_script(self, script_name):
        """执行指定的测试脚本"""
        if script_name not in self.test_scripts:
            print(f"❌ 未知的测试脚本: {script_name}")
            return False
            
        test_info = self.test_scripts[script_name]
        script_path = os.path.join(os.path.dirname(__file__), test_info["file"])
        
        if not os.path.exists(script_path):
            print(f"❌ 测试脚本不存在: {script_path}")
            return False
            
        print(f"\n🚀 执行测试: {test_info['name']}")
        print(f"📝 描述: {test_info['description']}")
        print(f"⏱️  预计时间: {test_info['duration']}")
        print("-" * 50)
        
        try:
            start_time = time.time()
            result = subprocess.run([sys.executable, script_path], 
                                  capture_output=False, 
                                  text=True,
                                  cwd=os.path.dirname(os.path.dirname(__file__)))
            end_time = time.time()
            
            duration = end_time - start_time
            
            if result.returncode == 0:
                print(f"\n✅ 测试完成: {test_info['name']}")
                print(f"⏱️  实际耗时: {duration:.1f}秒")
                return True
            else:
                print(f"\n❌ 测试失败: {test_info['name']}")
                print(f"⏱️  耗时: {duration:.1f}秒")
                print(f"🔍 返回码: {result.returncode}")
                return False
                
        except Exception as e:
            print(f"\n❌ 执行测试时发生错误: {str(e)}")
            return False
            
    def run_test_suite(self, suite_name):
        """执行测试套件"""
        suites = {
            "all": ["quick", "comprehensive", "analysis", "infrastructure", "extreme"],
            "daily": ["quick", "comprehensive"],
            "diagnosis": ["analysis", "comprehensive"],
            "capacity": ["infrastructure", "extreme"]
        }
        
        if suite_name not in suites:
            print(f"❌ 未知的测试套件: {suite_name}")
            return
            
        tests = suites[suite_name]
        print(f"\n🎯 执行测试套件: {suite_name}")
        print(f"📋 包含测试: {', '.join([self.test_scripts[t]['name'] for t in tests])}")
        
        if suite_name == "extreme":
            print("\n⚠️  警告: 极限测试可能影响系统性能")
            response = input("确认继续? (y/N): ")
            if response.lower() != 'y':
                print("❌ 测试已取消")
                return
                
        total_start = time.time()
        success_count = 0
        
        for i, test in enumerate(tests, 1):
            print(f"\n📊 进度: {i}/{len(tests)}")
            if self.run_script(test):
                success_count += 1
            else:
                print(f"⚠️  测试 {test} 失败，继续执行下一个测试...")
                
            # 测试间隔
            if i < len(tests):
                print("\n⏳ 等待5秒后继续下一个测试...")
                time.sleep(5)
                
        total_end = time.time()
        total_duration = total_end - total_start
        
        print(f"\n" + "=" * 50)
        print(f"🎯 测试套件完成: {suite_name}")
        print(f"📊 成功/总数: {success_count}/{len(tests)}")
        print(f"⏱️  总耗时: {total_duration/60:.1f}分钟")
        print(f"✅ 成功率: {success_count/len(tests)*100:.1f}%")
        
    def show_help(self):
        """显示详细帮助"""
        print("\n📖 FlowCustomV1 性能测试套件帮助")
        print("=" * 50)
        
        print("\n🎯 测试脚本详细说明:")
        for key, test in self.test_scripts.items():
            print(f"\n{key} - {test['name']}")
            print(f"  文件: {test['file']}")
            print(f"  描述: {test['description']}")
            print(f"  时间: {test['duration']}")
            
        print("\n🔧 测试套件说明:")
        print("  all     - 完整测试流程，适合全面评估")
        print("  daily   - 日常开发使用，快速验证基本性能")
        print("  diagnosis - 性能问题诊断，深入分析性能瓶颈")
        print("  capacity  - 容量规划评估，了解系统极限")
        
        print("\n⚠️  注意事项:")
        print("  - 确保FlowCustomV1系统正在运行")
        print("  - 极限测试可能影响系统稳定性")
        print("  - 建议在专用测试环境中运行")
        print("  - 压力测试期间避免其他重要操作")
        
    def run_interactive(self):
        """交互式运行"""
        while True:
            self.show_menu()
            
            choice = input("请选择测试 (输入名称或选项): ").strip().lower()
            
            if choice == "exit":
                print("👋 退出性能测试套件")
                break
            elif choice == "help":
                self.show_help()
                input("\n按回车键继续...")
            elif choice in ["all", "daily", "diagnosis", "capacity"]:
                self.run_test_suite(choice)
                input("\n按回车键继续...")
            elif choice in self.test_scripts:
                self.run_script(choice)
                input("\n按回车键继续...")
            else:
                print(f"❌ 无效选择: {choice}")
                input("按回车键继续...")
                
def main():
    """主函数"""
    suite = PerformanceTestSuite()
    
    if len(sys.argv) > 1:
        # 命令行模式
        command = sys.argv[1].lower()
        if command in suite.test_scripts:
            suite.run_script(command)
        elif command in ["all", "daily", "diagnosis", "capacity"]:
            suite.run_test_suite(command)
        elif command == "help":
            suite.show_help()
        else:
            print(f"❌ 无效命令: {command}")
            print("使用 'python run_tests.py help' 查看帮助")
    else:
        # 交互式模式
        suite.run_interactive()

if __name__ == "__main__":
    main()
