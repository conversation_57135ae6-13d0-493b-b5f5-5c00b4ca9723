using Microsoft.Extensions.Configuration;
using FlowCustomV1.Core.Models.Workflow;

namespace FlowCustomV1.Core.Interfaces;

/// <summary>
/// 统一配置管理服务接口
/// 提供配置读取、验证和热更新功能
/// </summary>
public interface IConfigurationService
{
    /// <summary>
    /// 获取配置值
    /// </summary>
    /// <param name="key">配置键</param>
    /// <returns>配置值，如果不存在返回null</returns>
    string? GetValue(string key);

    /// <summary>
    /// 获取配置值（带默认值）
    /// </summary>
    /// <param name="key">配置键</param>
    /// <param name="defaultValue">默认值</param>
    /// <returns>配置值，如果不存在返回默认值</returns>
    string GetValue(string key, string defaultValue);

    /// <summary>
    /// 获取强类型配置值
    /// </summary>
    /// <typeparam name="T">配置值类型</typeparam>
    /// <param name="key">配置键</param>
    /// <returns>配置值，如果不存在或转换失败返回类型默认值</returns>
    T? GetValue<T>(string key);

    /// <summary>
    /// 获取强类型配置值（带默认值）
    /// </summary>
    /// <typeparam name="T">配置值类型</typeparam>
    /// <param name="key">配置键</param>
    /// <param name="defaultValue">默认值</param>
    /// <returns>配置值，如果不存在或转换失败返回默认值</returns>
    T GetValue<T>(string key, T defaultValue);

    /// <summary>
    /// 获取配置节
    /// </summary>
    /// <param name="key">配置节键</param>
    /// <returns>配置节，如果不存在返回null</returns>
    IConfigurationSection? GetSection(string key);

    /// <summary>
    /// 绑定配置到对象
    /// </summary>
    /// <typeparam name="T">目标对象类型</typeparam>
    /// <param name="key">配置节键</param>
    /// <returns>绑定后的对象实例</returns>
    T? Bind<T>(string key) where T : class, new();

    /// <summary>
    /// 绑定配置到现有对象
    /// </summary>
    /// <param name="key">配置节键</param>
    /// <param name="instance">目标对象实例</param>
    void Bind(string key, object instance);

    /// <summary>
    /// 检查配置键是否存在
    /// </summary>
    /// <param name="key">配置键</param>
    /// <returns>如果存在返回true，否则返回false</returns>
    bool Exists(string key);

    /// <summary>
    /// 获取所有配置键
    /// </summary>
    /// <returns>配置键集合</returns>
    IEnumerable<string> GetKeys();

    /// <summary>
    /// 获取指定前缀的所有配置键
    /// </summary>
    /// <param name="prefix">键前缀</param>
    /// <returns>匹配前缀的配置键集合</returns>
    IEnumerable<string> GetKeys(string prefix);

    /// <summary>
    /// 验证配置完整性
    /// </summary>
    /// <param name="requiredKeys">必需的配置键</param>
    /// <returns>验证结果，包含缺失的键信息</returns>
    ConfigurationValidationResult ValidateConfiguration(IEnumerable<string> requiredKeys);

    /// <summary>
    /// 重新加载配置
    /// </summary>
    void Reload();

    /// <summary>
    /// 配置变更事件
    /// </summary>
    event EventHandler<ConfigurationChangedEventArgs>? ConfigurationChanged;
}



/// <summary>
/// 配置变更事件参数
/// </summary>
public class ConfigurationChangedEventArgs : EventArgs
{
    /// <summary>
    /// 变更的配置键
    /// </summary>
    public string Key { get; set; } = string.Empty;

    /// <summary>
    /// 旧值
    /// </summary>
    public string? OldValue { get; set; }

    /// <summary>
    /// 新值
    /// </summary>
    public string? NewValue { get; set; }

    /// <summary>
    /// 变更时间
    /// </summary>
    public DateTime ChangedAt { get; set; } = DateTime.UtcNow;
}
