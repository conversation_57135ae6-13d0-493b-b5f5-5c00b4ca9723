#!/usr/bin/env python3
"""
FlowCustomV1 MySQL环境管理脚本
管理开发、测试、生产三个独立的MySQL容器
"""

import subprocess
import sys
import time
from pathlib import Path

def run_command(command, cwd=None, timeout=60):
    """执行命令并返回结果"""
    try:
        result = subprocess.run(
            command, 
            cwd=cwd, 
            capture_output=True, 
            text=True, 
            shell=True,
            encoding='utf-8',
            errors='ignore',
            timeout=timeout
        )
        return result.returncode == 0, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return False, "", f"命令超时 ({timeout}秒)"
    except Exception as e:
        return False, "", str(e)

def start_mysql_environment(environment):
    """启动指定环境的MySQL"""
    print(f"🚀 启动 {environment.upper()} 环境MySQL...")
    
    script_dir = Path(__file__).parent
    compose_file = script_dir / f"{environment}.yml"
    
    if not compose_file.exists():
        print(f"❌ 配置文件不存在: {compose_file}")
        return False
    
    success, stdout, stderr = run_command(
        f"docker-compose -f {compose_file} up -d",
        cwd=script_dir,
        timeout=120
    )
    
    if not success:
        print(f"❌ {environment.upper()} MySQL启动失败:")
        print(f"stderr: {stderr}")
        return False
    
    print(f"✅ {environment.upper()} MySQL启动成功")
    
    # 等待MySQL启动
    print("⏳ 等待MySQL初始化...")
    time.sleep(10)
    
    return True

def stop_mysql_environment(environment):
    """停止指定环境的MySQL"""
    print(f"🛑 停止 {environment.upper()} 环境MySQL...")
    
    script_dir = Path(__file__).parent
    compose_file = script_dir / f"{environment}.yml"
    
    success, stdout, stderr = run_command(
        f"docker-compose -f {compose_file} down",
        cwd=script_dir,
        timeout=30
    )
    
    if success:
        print(f"✅ {environment.upper()} MySQL已停止")
    else:
        print(f"⚠️ {environment.upper()} MySQL停止可能不完整: {stderr}")
    
    return success

def cleanup_mysql_environment(environment):
    """清理指定环境的MySQL"""
    print(f"🧹 清理 {environment.upper()} 环境MySQL...")
    
    script_dir = Path(__file__).parent
    compose_file = script_dir / f"{environment}.yml"
    
    success, stdout, stderr = run_command(
        f"docker-compose -f {compose_file} down -v",
        cwd=script_dir,
        timeout=30
    )
    
    if success:
        print(f"✅ {environment.upper()} MySQL清理完成")
    else:
        print(f"⚠️ {environment.upper()} MySQL清理可能不完整: {stderr}")
    
    return success

def check_mysql_status(environment):
    """检查指定环境MySQL状态"""
    print(f"🔍 检查 {environment.upper()} 环境MySQL状态...")
    
    container_name = f"mysql-{environment}"
    
    # 检查容器状态
    success, stdout, stderr = run_command(
        f"docker ps --filter name={container_name}",
        timeout=10
    )
    
    if success and container_name in stdout:
        print(f"✅ {environment.upper()} MySQL容器运行中")
        
        # 检查MySQL服务
        success, stdout, stderr = run_command(
            f"docker exec {container_name} mysqladmin ping -h localhost",
            timeout=10
        )
        
        if success:
            print(f"✅ {environment.upper()} MySQL服务正常")
        else:
            print(f"⚠️ {environment.upper()} MySQL服务异常")
        
        # 显示连接信息
        port_map = {"development": 3306, "testing": 23306, "production": 13306}
        port = port_map.get(environment, 3306)
        db_name = f"flowcustom_{environment[:4]}"  # dev, test, prod
        
        print(f"📊 {environment.upper()} MySQL连接信息:")
        print(f"   主机: localhost")
        print(f"   端口: {port}")
        print(f"   数据库: {db_name}")
        print(f"   用户: flowcustom")
        print(f"   连接命令: mysql -h localhost -P {port} -u flowcustom -p")
        
    else:
        print(f"❌ {environment.upper()} MySQL容器未运行")

def start_all_environments():
    """启动所有环境的MySQL"""
    print("🚀 启动所有MySQL环境...")
    
    environments = ["development", "testing", "production"]
    success_count = 0
    
    for env in environments:
        if start_mysql_environment(env):
            success_count += 1
        time.sleep(2)  # 错开启动时间
    
    print(f"\n📊 启动结果: {success_count}/{len(environments)} 个环境启动成功")
    
    if success_count == len(environments):
        print("\n🌐 所有MySQL环境访问信息:")
        print("开发环境: mysql -h localhost -P 3306 -u flowcustom -p")
        print("测试环境: mysql -h localhost -P 23306 -u flowcustom -p")
        print("生产环境: mysql -h localhost -P 13306 -u flowcustom -p")

def stop_all_environments():
    """停止所有环境的MySQL"""
    print("🛑 停止所有MySQL环境...")
    
    environments = ["development", "testing", "production"]
    for env in environments:
        stop_mysql_environment(env)

def cleanup_all_environments():
    """清理所有环境的MySQL"""
    print("🧹 清理所有MySQL环境...")
    
    environments = ["development", "testing", "production"]
    for env in environments:
        cleanup_mysql_environment(env)

def check_all_status():
    """检查所有环境状态"""
    print("🔍 检查所有MySQL环境状态...")
    
    environments = ["development", "testing", "production"]
    for env in environments:
        check_mysql_status(env)
        print()

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python manage-mysql.py [command] [environment]")
        print("\n命令:")
        print("  start [env]    - 启动指定环境MySQL (或all启动所有)")
        print("  stop [env]     - 停止指定环境MySQL (或all停止所有)")
        print("  status [env]   - 检查指定环境状态 (或all检查所有)")
        print("  cleanup [env]  - 清理指定环境MySQL (或all清理所有)")
        print("\n环境:")
        print("  development   - 开发环境 (端口3306)")
        print("  testing       - 测试环境 (端口23306)")
        print("  production    - 生产环境 (端口13306)")
        print("  all           - 所有环境")
        sys.exit(1)
    
    command = sys.argv[1].lower()
    environment = sys.argv[2].lower() if len(sys.argv) > 2 else "all"
    
    valid_environments = ["development", "testing", "production", "all"]
    if environment not in valid_environments:
        print(f"❌ 无效环境: {environment}")
        print(f"支持的环境: {', '.join(valid_environments)}")
        sys.exit(1)
    
    if command == "start":
        if environment == "all":
            start_all_environments()
        else:
            start_mysql_environment(environment)
    
    elif command == "stop":
        if environment == "all":
            stop_all_environments()
        else:
            stop_mysql_environment(environment)
    
    elif command == "status":
        if environment == "all":
            check_all_status()
        else:
            check_mysql_status(environment)
    
    elif command == "cleanup":
        if environment == "all":
            cleanup_all_environments()
        else:
            cleanup_mysql_environment(environment)
    
    else:
        print(f"❌ 未知命令: {command}")
        print("支持的命令: start, stop, status, cleanup")
        sys.exit(1)

if __name__ == "__main__":
    main()
