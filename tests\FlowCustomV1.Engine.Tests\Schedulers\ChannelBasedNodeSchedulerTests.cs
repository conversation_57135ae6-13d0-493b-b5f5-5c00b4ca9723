using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using FlowCustomV1.Engine.Schedulers;

namespace FlowCustomV1.Engine.Tests.Schedulers;

/// <summary>
/// ChannelBasedNodeScheduler 测试类
/// </summary>
public class ChannelBasedNodeSchedulerTests : TestBase
{
    [Fact]
    public void Scheduler_ShouldBeCreatedSuccessfully()
    {
        // Arrange
        var logger = ServiceProvider.GetRequiredService<ILogger<ChannelBasedNodeScheduler>>();

        // Act
        var scheduler = new ChannelBasedNodeScheduler(ServiceProvider, logger);

        // Assert
        scheduler.Should().NotBeNull();
        scheduler.State.Should().Be(SchedulerState.NotStarted);
    }

    [Fact]
    public void SchedulerId_ShouldBeGenerated()
    {
        // Arrange
        var logger = ServiceProvider.GetRequiredService<ILogger<ChannelBasedNodeScheduler>>();
        var scheduler = new ChannelBasedNodeScheduler(ServiceProvider, logger);

        // Act
        var schedulerId = scheduler.SchedulerId;

        // Assert
        schedulerId.Should().NotBeNullOrEmpty();
    }
}