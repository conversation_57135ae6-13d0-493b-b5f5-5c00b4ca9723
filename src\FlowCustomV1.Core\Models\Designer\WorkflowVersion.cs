using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using FlowCustomV1.Core.Models.Workflow;

namespace FlowCustomV1.Core.Models.Designer;

/// <summary>
/// 工作流版本信息
/// 用于工作流的版本控制和历史管理
/// </summary>
public class WorkflowVersion
{
    /// <summary>
    /// 版本唯一标识符
    /// </summary>
    [Required]
    [JsonPropertyName("versionId")]
    public string VersionId { get; set; } = string.Empty;

    /// <summary>
    /// 工作流ID
    /// </summary>
    [Required]
    [JsonPropertyName("workflowId")]
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 版本号
    /// </summary>
    [Required]
    [JsonPropertyName("version")]
    public string Version { get; set; } = string.Empty;

    /// <summary>
    /// 版本描述
    /// </summary>
    [JsonPropertyName("description")]
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 版本类型
    /// </summary>
    [JsonPropertyName("versionType")]
    public VersionType VersionType { get; set; } = VersionType.Minor;

    /// <summary>
    /// 工作流定义快照
    /// </summary>
    [JsonPropertyName("definition")]
    public WorkflowDefinition Definition { get; set; } = new();

    /// <summary>
    /// 是否为活跃版本
    /// </summary>
    [JsonPropertyName("isActive")]
    public bool IsActive { get; set; } = false;

    /// <summary>
    /// 是否为发布版本
    /// </summary>
    [JsonPropertyName("isReleased")]
    public bool IsReleased { get; set; } = false;

    /// <summary>
    /// 父版本ID
    /// </summary>
    [JsonPropertyName("parentVersionId")]
    public string? ParentVersionId { get; set; }

    /// <summary>
    /// 变更摘要
    /// </summary>
    [JsonPropertyName("changeSummary")]
    public List<VersionChange> ChangeSummary { get; set; } = new();

    /// <summary>
    /// 创建时间
    /// </summary>
    [JsonPropertyName("createdAt")]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 创建者ID
    /// </summary>
    [JsonPropertyName("createdBy")]
    public string CreatedBy { get; set; } = string.Empty;

    /// <summary>
    /// 创建者名称
    /// </summary>
    [JsonPropertyName("createdByName")]
    public string CreatedByName { get; set; } = string.Empty;

    /// <summary>
    /// 版本标签
    /// </summary>
    [JsonPropertyName("tags")]
    public HashSet<string> Tags { get; set; } = new();

    /// <summary>
    /// 版本元数据
    /// </summary>
    [JsonPropertyName("metadata")]
    public Dictionary<string, object> Metadata { get; set; } = new();

    /// <summary>
    /// 版本大小（字节）
    /// </summary>
    [JsonPropertyName("size")]
    public long Size { get; set; } = 0;

    /// <summary>
    /// 校验和
    /// </summary>
    [JsonPropertyName("checksum")]
    public string Checksum { get; set; } = string.Empty;
}

/// <summary>
/// 版本类型枚举
/// </summary>
public enum VersionType
{
    /// <summary>
    /// 主版本（重大变更）
    /// </summary>
    Major,

    /// <summary>
    /// 次版本（功能增加）
    /// </summary>
    Minor,

    /// <summary>
    /// 补丁版本（错误修复）
    /// </summary>
    Patch,

    /// <summary>
    /// 预发布版本
    /// </summary>
    PreRelease,

    /// <summary>
    /// 快照版本
    /// </summary>
    Snapshot
}

/// <summary>
/// 版本变更记录
/// </summary>
public class VersionChange
{
    /// <summary>
    /// 变更类型
    /// </summary>
    [JsonPropertyName("changeType")]
    public ChangeType ChangeType { get; set; }

    /// <summary>
    /// 变更对象类型
    /// </summary>
    [JsonPropertyName("objectType")]
    public string ObjectType { get; set; } = string.Empty;

    /// <summary>
    /// 变更对象ID
    /// </summary>
    [JsonPropertyName("objectId")]
    public string ObjectId { get; set; } = string.Empty;

    /// <summary>
    /// 变更描述
    /// </summary>
    [JsonPropertyName("description")]
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 变更前的值
    /// </summary>
    [JsonPropertyName("oldValue")]
    public object? OldValue { get; set; }

    /// <summary>
    /// 变更后的值
    /// </summary>
    [JsonPropertyName("newValue")]
    public object? NewValue { get; set; }

    /// <summary>
    /// 变更时间
    /// </summary>
    [JsonPropertyName("timestamp")]
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 变更类型枚举
/// </summary>
public enum ChangeType
{
    /// <summary>
    /// 添加
    /// </summary>
    Added,

    /// <summary>
    /// 修改
    /// </summary>
    Modified,

    /// <summary>
    /// 删除
    /// </summary>
    Deleted,

    /// <summary>
    /// 移动
    /// </summary>
    Moved,

    /// <summary>
    /// 重命名
    /// </summary>
    Renamed
}

/// <summary>
/// 工作流版本信息（用于创建版本）
/// </summary>
public class WorkflowVersionInfo
{
    /// <summary>
    /// 版本号
    /// </summary>
    [Required]
    [JsonPropertyName("version")]
    public string Version { get; set; } = string.Empty;

    /// <summary>
    /// 版本描述
    /// </summary>
    [JsonPropertyName("description")]
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 版本类型
    /// </summary>
    [JsonPropertyName("versionType")]
    public VersionType VersionType { get; set; } = VersionType.Minor;

    /// <summary>
    /// 是否设为活跃版本
    /// </summary>
    [JsonPropertyName("setAsActive")]
    public bool SetAsActive { get; set; } = false;

    /// <summary>
    /// 是否立即发布
    /// </summary>
    [JsonPropertyName("releaseImmediately")]
    public bool ReleaseImmediately { get; set; } = false;

    /// <summary>
    /// 版本标签
    /// </summary>
    [JsonPropertyName("tags")]
    public HashSet<string> Tags { get; set; } = new();

    /// <summary>
    /// 创建者ID
    /// </summary>
    [JsonPropertyName("createdBy")]
    public string CreatedBy { get; set; } = string.Empty;

    /// <summary>
    /// 创建者名称
    /// </summary>
    [JsonPropertyName("createdByName")]
    public string CreatedByName { get; set; } = string.Empty;
}

/// <summary>
/// 工作流查询条件
/// </summary>
public class WorkflowQuery
{
    /// <summary>
    /// 工作流名称（模糊匹配）
    /// </summary>
    [JsonPropertyName("name")]
    public string? Name { get; set; }

    /// <summary>
    /// 作者ID
    /// </summary>
    [JsonPropertyName("author")]
    public string? Author { get; set; }

    /// <summary>
    /// 标签过滤
    /// </summary>
    [JsonPropertyName("tags")]
    public List<string>? Tags { get; set; }

    /// <summary>
    /// 创建时间范围（开始）
    /// </summary>
    [JsonPropertyName("createdAfter")]
    public DateTime? CreatedAfter { get; set; }

    /// <summary>
    /// 创建时间范围（结束）
    /// </summary>
    [JsonPropertyName("createdBefore")]
    public DateTime? CreatedBefore { get; set; }

    /// <summary>
    /// 分页大小
    /// </summary>
    [JsonPropertyName("pageSize")]
    public int PageSize { get; set; } = 20;

    /// <summary>
    /// 页码（从1开始）
    /// </summary>
    [JsonPropertyName("pageNumber")]
    public int PageNumber { get; set; } = 1;

    /// <summary>
    /// 排序字段
    /// </summary>
    [JsonPropertyName("sortBy")]
    public string SortBy { get; set; } = "CreatedAt";

    /// <summary>
    /// 排序方向
    /// </summary>
    [JsonPropertyName("sortDirection")]
    public SortDirection SortDirection { get; set; } = SortDirection.Descending;
}

/// <summary>
/// 排序方向枚举
/// </summary>
public enum SortDirection
{
    /// <summary>
    /// 升序
    /// </summary>
    Ascending,

    /// <summary>
    /// 降序
    /// </summary>
    Descending
}
