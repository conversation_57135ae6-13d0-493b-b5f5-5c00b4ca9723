{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "FlowCustomV1": "Debug"}, "Console": {"IncludeScopes": true, "TimestampFormat": "yyyy-MM-dd HH:mm:ss.fff "}}, "TaskDistribution": {"MaxCandidateNodes": 50, "MaxConcurrentDistributions": 20, "MinBalanceScore": 0.7, "MaxRebalancingOperations": 5, "NodeSelectionTimeoutMs": 2000, "TaskDistributionTimeoutMs": 10000, "AutoRebalancingEnabled": true, "RebalancingThreshold": 0.3, "RebalancingIntervalSeconds": 300, "EnablePerformanceMonitoring": true, "EnableDetailedLogging": true, "StatisticsRetentionDays": 7, "PredictionHistoryWindowSize": 1000, "HealthCheckWeight": 0.3, "LoadWeight": 0.3, "PerformanceWeight": 0.25, "GeographyWeight": 0.15, "NodeBlacklist": [], "NodeWhitelist": []}, "TaskTracking": {"CleanupIntervalMinutes": 30, "StatisticsUpdateIntervalSeconds": 15, "MaxRecentCompletedTasks": 500, "AutoCleanupOnCompletion": false, "TaskStateRetentionDays": 3, "EnableDetailedLogging": true, "EnablePerformanceMonitoring": true, "MaxConcurrentTrackedTasks": 2000, "TimeoutCheckIntervalSeconds": 30, "EnableTimeoutCheck": true, "DefaultTaskTimeoutMs": 300000, "EnableProgressTracking": true, "ProgressUpdateMinIntervalMs": 500, "EnableResourceMonitoring": true, "ResourceMonitoringIntervalSeconds": 15, "EnableEventPublishing": true, "EventTopicPrefix": "test.task.tracking", "BatchOperationSize": 50, "EnableDependencyTracking": true, "MaxDependencyDepth": 5, "EnableRetryTracking": true, "MaxRetryHistoryCount": 5, "EnableExecutionLogging": true, "MaxExecutionLogCount": 100, "LogLevelFilters": ["Information", "Warning", "Error", "Critical"], "EnableStatusHistory": true, "MaxStatusHistoryCount": 20, "EnablePerformanceAnalysis": true, "PerformanceAnalysisSamplingRate": 0.2, "EnableTaskAlerting": false, "ExecutionTimeAlertThresholdMs": 600000, "FailureRateAlertThreshold": 0.2, "AlertCheckIntervalSeconds": 60, "EnableMetricsExport": false, "MetricsExportIntervalSeconds": 30, "MetricsRetentionDays": 7, "EnableDistributedTracing": false, "TracingSamplingRate": 0.1, "TracingServiceName": "FlowCustomV1.TaskTracking.Test", "EnableHealthCheck": true, "HealthCheckIntervalSeconds": 30, "HealthCheckTimeoutMs": 3000}, "NATS": {"Servers": ["nats://localhost:4222"], "ConnectionName": "FlowCustomV1-Test", "MaxReconnectAttempts": 5, "ReconnectWaitMs": 2000, "PingIntervalMs": 30000, "MaxPingsOut": 3, "EnableJetStream": false, "JetStreamDomain": "test", "EnableTLS": false, "EnableAuth": false, "Username": "", "Password": "", "Token": "", "CredentialsFile": "", "EnableVerbose": false, "EnablePedantic": false, "DrainTimeoutMs": 5000, "FlushTimeoutMs": 5000}, "TestSettings": {"Environment": "Testing", "EnableMockServices": true, "MockNodeCount": 10, "MockTaskCount": 100, "TestDataPath": "TestData", "EnableTestLogging": true, "TestLogLevel": "Debug", "TestTimeoutMs": 30000, "EnablePerformanceTesting": true, "PerformanceTestIterations": 1000, "EnableResilienceTesting": true, "ResilienceTestScenarios": ["NodeFailure", "NetworkPartition", "HighLoad", "ResourceExhaustion"], "EnableIntegrationTesting": true, "IntegrationTestEndpoints": ["http://localhost:5000"], "TestDatabaseConnection": "Data Source=:memory:", "EnableTestReporting": true, "TestReportFormat": "xml,json,html", "TestReportPath": "TestResults"}, "BenchmarkSettings": {"WarmupIterations": 10, "MeasurementIterations": 100, "MaxDegreeOfParallelism": 4, "MemoryDiagnoser": true, "EnableGcDiagnoser": true, "EnableThreadingDiagnoser": true, "ExportFormat": "json,html", "ExportPath": "BenchmarkResults", "Categories": ["TaskDistribution", "LoadBalancing", "TaskTracking", "NodeSelection"]}, "MockConfiguration": {"EnableMockNATS": true, "MockNATSPort": 14222, "EnableMockNodes": true, "MockNodeStartPort": 15000, "MockNodeCount": 5, "MockNodeLatencyMs": 50, "MockTaskExecutionTimeMs": 1000, "MockFailureRate": 0.05, "EnableMockMetrics": true, "MockMetricsUpdateIntervalMs": 1000}}