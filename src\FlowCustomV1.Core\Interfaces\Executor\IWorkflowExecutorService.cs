using FlowCustomV1.Core.Models.Workflow;
using FlowCustomV1.Core.Models.Executor;
using FlowCustomV1.Core.Models.Common;

namespace FlowCustomV1.Core.Interfaces.Executor;

/// <summary>
/// 工作流执行器服务接口
/// 提供分布式工作流执行功能，支持任务调度、状态同步和结果持久化
/// </summary>
public interface IWorkflowExecutorService
{
    #region 执行管理

    /// <summary>
    /// 执行工作流
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="inputData">输入数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>执行ID</returns>
    Task<string> ExecuteWorkflowAsync(string workflowId, Dictionary<string, object>? inputData = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 执行工作流（使用工作流定义）
    /// </summary>
    /// <param name="workflowDefinition">工作流定义</param>
    /// <param name="inputData">输入数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>执行ID</returns>
    Task<string> ExecuteWorkflowAsync(WorkflowDefinition workflowDefinition, Dictionary<string, object>? inputData = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取执行结果
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>执行结果</returns>
    Task<WorkflowExecutionResult?> GetExecutionResultAsync(string executionId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 取消工作流执行
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>取消结果</returns>
    Task<bool> CancelExecutionAsync(string executionId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 暂停工作流执行
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>暂停结果</returns>
    Task<bool> PauseExecutionAsync(string executionId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 恢复工作流执行
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>恢复结果</returns>
    Task<bool> ResumeExecutionAsync(string executionId, CancellationToken cancellationToken = default);

    #endregion

    #region 执行查询

    /// <summary>
    /// 获取正在运行的执行列表
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>执行信息列表</returns>
    Task<IReadOnlyList<ExecutionInfo>> GetRunningExecutionsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取工作流的执行历史
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="pageIndex">页索引</param>
    /// <param name="pageSize">页大小</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>执行历史</returns>
    Task<PagedResult<ExecutionInfo>> GetExecutionHistoryAsync(string workflowId, int pageIndex = 0, int pageSize = 20, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取执行统计信息
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>执行统计</returns>
    Task<ExecutionStatistics> GetExecutionStatisticsAsync(CancellationToken cancellationToken = default);

    #endregion

    #region 资源管理

    /// <summary>
    /// 获取执行容量信息
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>执行容量</returns>
    Task<ExecutionCapacity> GetExecutionCapacityAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 更新节点负载信息
    /// </summary>
    /// <param name="loadInfo">负载信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>更新任务</returns>
    Task UpdateNodeLoadAsync(NodeLoadInfo loadInfo, CancellationToken cancellationToken = default);

    #endregion

    #region 分布式执行

    /// <summary>
    /// 迁移执行到其他节点
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="targetNodeId">目标节点ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>迁移结果</returns>
    Task<bool> MigrateExecutionAsync(string executionId, string targetNodeId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 接收迁移的执行
    /// </summary>
    /// <param name="executionContext">执行上下文</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>接收结果</returns>
    Task<bool> ReceiveMigratedExecutionAsync(ExecutionMigrationContext executionContext, CancellationToken cancellationToken = default);

    #endregion

    #region 服务生命周期

    /// <summary>
    /// 启动执行器服务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>启动任务</returns>
    Task StartAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 停止执行器服务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>停止任务</returns>
    Task StopAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取服务状态
    /// </summary>
    /// <returns>服务状态</returns>
    ExecutorServiceStatus GetStatus();

    #endregion

    #region 事件

    /// <summary>
    /// 执行开始事件
    /// </summary>
    event EventHandler<ExecutionStartedEventArgs>? ExecutionStarted;

    /// <summary>
    /// 执行完成事件
    /// </summary>
    event EventHandler<ExecutionCompletedEventArgs>? ExecutionCompleted;

    /// <summary>
    /// 执行失败事件
    /// </summary>
    event EventHandler<ExecutionFailedEventArgs>? ExecutionFailed;

    /// <summary>
    /// 执行状态变更事件
    /// </summary>
    event EventHandler<ExecutionStatusChangedEventArgs>? ExecutionStatusChanged;

    #endregion
}
