# PowerShell script to fix async methods that don't use await
# This script removes 'async' keyword and wraps return values with Task.FromResult()

$files = @(
    "src\FlowCustomV1.Infrastructure\Services\Designer\TemplateManagementService.cs",
    "src\FlowCustomV1.Infrastructure\Services\Designer\WorkflowDesignerService.cs",
    "src\FlowCustomV1.Infrastructure\Services\Designer\CollaborationService.cs",
    "src\FlowCustomV1.Infrastructure\Services\Designer\NatsCollaborationService.cs"
)

foreach ($file in $files) {
    if (Test-Path $file) {
        Write-Host "Processing $file..."
        
        $content = Get-Content $file -Raw
        
        # Pattern to match async methods that don't use await
        # This is a simple pattern - we'll need to manually verify each change
        
        # Remove 'async' keyword from method signatures
        $content = $content -replace 'public async Task<([^>]+)>', 'public Task<$1>'
        $content = $content -replace 'private async Task<([^>]+)>', 'private Task<$1>'
        $content = $content -replace 'protected async Task<([^>]+)>', 'protected Task<$1>'
        
        # Fix simple return statements
        $content = $content -replace 'return ([^;]+);(\s*})', 'return Task.FromResult($1);$2'
        
        Set-Content $file $content -Encoding UTF8
        Write-Host "Fixed $file"
    }
}

Write-Host "Done! Please review the changes manually."
