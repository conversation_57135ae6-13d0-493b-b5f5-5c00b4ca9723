#!/usr/bin/env python3
"""
FlowCustomV1 Docker集群真实测试脚本
启动Docker集群并进行真实的功能测试
"""

import os
import sys
import subprocess
import time
import requests
import json
from pathlib import Path

def run_command(command, cwd=None, capture_output=True, timeout=60):
    """执行命令并返回结果"""
    try:
        result = subprocess.run(
            command, 
            cwd=cwd, 
            capture_output=capture_output, 
            text=True, 
            shell=True,
            encoding='utf-8',
            errors='ignore',
            timeout=timeout
        )
        return result.returncode == 0, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return False, "", f"命令超时 ({timeout}秒)"
    except Exception as e:
        return False, "", str(e)

def check_docker_available():
    """检查Docker是否可用"""
    print("🔍 检查Docker环境...")
    
    success, stdout, stderr = run_command("docker --version")
    if not success:
        print("❌ Docker未安装或不可用")
        return False
    
    print(f"✅ Docker版本: {stdout.strip()}")
    
    success, stdout, stderr = run_command("docker-compose --version")
    if not success:
        print("❌ Docker Compose未安装或不可用")
        return False
    
    print(f"✅ Docker Compose版本: {stdout.strip()}")
    return True

def build_docker_images(cluster_test_dir):
    """构建Docker镜像"""
    print("\n🔨 构建Docker镜像...")
    
    # 构建API节点镜像
    print("🔄 构建API节点镜像...")
    success, stdout, stderr = run_command(
        "docker build -f Dockerfile.api -t flowcustom-api ../../",
        cwd=cluster_test_dir,
        timeout=300
    )
    
    if not success:
        print(f"❌ API节点镜像构建失败: {stderr}")
        return False
    
    print("✅ API节点镜像构建成功")
    return True

def start_infrastructure(cluster_test_dir):
    """启动基础设施服务"""
    print("\n🚀 启动基础设施服务...")
    
    # 启动NATS集群和MySQL
    success, stdout, stderr = run_command(
        "docker-compose up -d nats-server-1 nats-server-2 nats-server-3 mysql",
        cwd=cluster_test_dir,
        timeout=120
    )
    
    if not success:
        print(f"❌ 基础设施启动失败: {stderr}")
        return False
    
    print("✅ 基础设施服务启动成功")
    
    # 等待服务启动
    print("⏳ 等待服务初始化...")
    time.sleep(15)
    
    return True

def check_infrastructure_health(cluster_test_dir):
    """检查基础设施健康状态"""
    print("\n🔍 检查基础设施健康状态...")
    
    # 检查容器状态
    success, stdout, stderr = run_command(
        "docker-compose ps",
        cwd=cluster_test_dir
    )
    
    if not success:
        print(f"❌ 无法获取容器状态: {stderr}")
        return False
    
    print("📊 容器状态:")
    print(stdout)
    
    # 检查NATS服务器
    nats_healthy = True
    for i in range(1, 4):
        container_name = f"nats-test-server-{i}"
        success, stdout, stderr = run_command(
            f"docker exec {container_name} nats server info",
            timeout=10
        )
        
        if success:
            print(f"✅ NATS服务器 {i} 健康")
        else:
            print(f"⚠️ NATS服务器 {i} 状态异常")
            nats_healthy = False
    
    # 检查MySQL
    success, stdout, stderr = run_command(
        "docker exec mysql-test mysqladmin ping -h localhost",
        timeout=10
    )
    
    if success:
        print("✅ MySQL数据库健康")
    else:
        print("⚠️ MySQL数据库状态异常")
        return False
    
    return nats_healthy

def start_application_nodes(cluster_test_dir):
    """启动应用节点"""
    print("\n🚀 启动应用节点...")
    
    # 只启动API节点和多角色节点进行测试
    nodes = ["api-node", "multi-role-node"]
    
    for node in nodes:
        print(f"🔄 启动 {node}...")
        success, stdout, stderr = run_command(
            f"docker-compose up -d {node}",
            cwd=cluster_test_dir,
            timeout=120
        )
        
        if not success:
            print(f"❌ {node} 启动失败: {stderr}")
            return False
        
        print(f"✅ {node} 启动成功")
        time.sleep(10)  # 等待节点启动
    
    return True

def test_api_endpoints():
    """测试API端点"""
    print("\n🧪 测试API端点...")
    
    endpoints = [
        ("API节点", "http://localhost:25000"),
        ("多角色节点", "http://localhost:25001")
    ]
    
    for name, url in endpoints:
        print(f"🔄 测试 {name} ({url})...")
        
        # 等待服务启动
        max_retries = 30
        for i in range(max_retries):
            try:
                response = requests.get(f"{url}/health", timeout=5)
                if response.status_code == 200:
                    print(f"✅ {name} 健康检查通过")
                    break
                else:
                    print(f"⚠️ {name} 返回状态码: {response.status_code}")
            except requests.exceptions.RequestException as e:
                if i == max_retries - 1:
                    print(f"❌ {name} 健康检查失败: {e}")
                    return False
                time.sleep(2)
        else:
            print(f"❌ {name} 健康检查超时")
            return False
    
    return True

def test_cluster_functionality(cluster_test_dir):
    """测试集群功能"""
    print("\n🧪 测试集群功能...")
    
    # 检查节点注册
    try:
        response = requests.get("http://localhost:25000/api/cluster/nodes", timeout=10)
        if response.status_code == 200:
            nodes = response.json()
            print(f"✅ 集群节点发现: 发现 {len(nodes)} 个节点")
            for node in nodes:
                print(f"   - 节点: {node.get('nodeId', 'Unknown')} ({node.get('roles', [])})")
        else:
            print(f"⚠️ 节点发现API返回状态码: {response.status_code}")
    except Exception as e:
        print(f"⚠️ 节点发现测试失败: {e}")
    
    # 检查系统状态
    try:
        response = requests.get("http://localhost:25000/api/system/status", timeout=10)
        if response.status_code == 200:
            status = response.json()
            print("✅ 系统状态检查通过")
            print(f"   - 环境: {status.get('environment', 'Unknown')}")
            print(f"   - 版本: {status.get('version', 'Unknown')}")
        else:
            print(f"⚠️ 系统状态API返回状态码: {response.status_code}")
    except Exception as e:
        print(f"⚠️ 系统状态测试失败: {e}")
    
    return True

def cleanup_cluster(cluster_test_dir):
    """清理集群"""
    print("\n🧹 清理集群...")
    
    success, stdout, stderr = run_command(
        "docker-compose down -v",
        cwd=cluster_test_dir,
        timeout=60
    )
    
    if success:
        print("✅ 集群清理完成")
    else:
        print(f"⚠️ 集群清理可能不完整: {stderr}")
    
    return success

def main():
    """主函数"""
    print("🚀 FlowCustomV1 Docker集群真实测试")
    print("=" * 50)
    
    # 设置路径
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    cluster_test_dir = project_root / "docker" / "cluster-test"
    
    if not cluster_test_dir.exists():
        print(f"❌ 集群测试目录不存在: {cluster_test_dir}")
        return 1
    
    print(f"📁 项目根目录: {project_root}")
    print(f"📁 集群测试目录: {cluster_test_dir}")
    
    try:
        # 检查Docker环境
        if not check_docker_available():
            return 1
        
        # 构建镜像
        if not build_docker_images(cluster_test_dir):
            return 1
        
        # 启动基础设施
        if not start_infrastructure(cluster_test_dir):
            return 1
        
        # 检查基础设施健康状态
        if not check_infrastructure_health(cluster_test_dir):
            print("⚠️ 基础设施健康检查失败，但继续测试...")
        
        # 启动应用节点
        if not start_application_nodes(cluster_test_dir):
            return 1
        
        # 测试API端点
        if not test_api_endpoints():
            print("⚠️ API端点测试失败，但继续其他测试...")
        
        # 测试集群功能
        test_cluster_functionality(cluster_test_dir)
        
        print("\n🎉 Docker集群测试完成!")
        print("\n🌐 集群访问信息:")
        print("API节点:      http://localhost:25000")
        print("多角色节点:    http://localhost:25001")
        print("NATS监控:     http://localhost:28222")
        print("MySQL数据库:  localhost:23306")
        
        print("\n💡 手动测试建议:")
        print("1. 访问 http://localhost:25000/health 检查API健康状态")
        print("2. 访问 http://localhost:25000/api/cluster/nodes 查看集群节点")
        print("3. 访问 http://localhost:25000/api/system/status 查看系统状态")
        
        input("\n按Enter键清理集群...")
        
    finally:
        # 清理集群
        cleanup_cluster(cluster_test_dir)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
