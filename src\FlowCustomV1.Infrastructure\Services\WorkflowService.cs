using Microsoft.Extensions.Logging;
using FlowCustomV1.Core.Interfaces;
using FlowCustomV1.Core.Interfaces.Services;
using FlowCustomV1.Core.Interfaces.Executor;
using FlowCustomV1.Core.Interfaces.Validator;
using FlowCustomV1.Core.Models.Workflow;
using FlowCustomV1.Core.Models.Common;
using FlowCustomV1.Core.Models.Cluster;

namespace FlowCustomV1.Infrastructure.Services;

/// <summary>
/// 工作流服务实现
/// 提供工作流的业务逻辑操作，协调各个子服务
/// </summary>
public class WorkflowService : IWorkflowService
{
    private readonly ILogger<WorkflowService> _logger;
    private readonly IWorkflowRepository _workflowRepository;
    private readonly IWorkflowEngine _workflowEngine;
    private readonly IWorkflowValidatorService? _validatorService;
    private readonly IWorkflowExecutorService? _executorService;

    public WorkflowService(
        ILogger<WorkflowService> logger,
        IWorkflowRepository workflowRepository,
        IWorkflowEngine workflowEngine,
        IWorkflowValidatorService? validatorService = null,
        IWorkflowExecutorService? executorService = null)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _workflowRepository = workflowRepository ?? throw new ArgumentNullException(nameof(workflowRepository));
        _workflowEngine = workflowEngine ?? throw new ArgumentNullException(nameof(workflowEngine));
        _validatorService = validatorService;
        _executorService = executorService;
    }

    #region 工作流管理

    public async Task<string> CreateWorkflowAsync(WorkflowDefinition workflowDefinition, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(workflowDefinition);

        try
        {
            _logger.LogInformation("Creating workflow {WorkflowId}", workflowDefinition.WorkflowId);

            // 验证工作流定义
            if (_validatorService != null)
            {
                var validationResult = await _validatorService.ValidateWorkflowAsync(workflowDefinition, cancellationToken);
                if (!validationResult.IsValid)
                {
                    throw new InvalidOperationException($"Workflow validation failed: {string.Join(", ", validationResult.Errors)}");
                }
            }

            // 保存工作流定义
            var success = await _workflowRepository.SaveWorkflowDefinitionAsync(workflowDefinition, cancellationToken);
            if (!success)
            {
                throw new InvalidOperationException("Failed to save workflow definition");
            }

            _logger.LogInformation("Successfully created workflow {WorkflowId}", workflowDefinition.WorkflowId);
            return workflowDefinition.WorkflowId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create workflow {WorkflowId}", workflowDefinition.WorkflowId);
            throw;
        }
    }

    public async Task<bool> UpdateWorkflowAsync(WorkflowDefinition workflowDefinition, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(workflowDefinition);

        try
        {
            _logger.LogInformation("Updating workflow {WorkflowId}", workflowDefinition.WorkflowId);

            // 验证工作流定义
            if (_validatorService != null)
            {
                var validationResult = await _validatorService.ValidateWorkflowAsync(workflowDefinition, cancellationToken);
                if (!validationResult.IsValid)
                {
                    _logger.LogWarning("Workflow validation failed for {WorkflowId}: {Errors}", 
                        workflowDefinition.WorkflowId, string.Join(", ", validationResult.Errors));
                    return false;
                }
            }

            // 更新时间戳
            workflowDefinition.LastModifiedAt = DateTime.UtcNow;

            // 保存更新
            var success = await _workflowRepository.SaveWorkflowDefinitionAsync(workflowDefinition, cancellationToken);
            
            if (success)
            {
                _logger.LogInformation("Successfully updated workflow {WorkflowId}", workflowDefinition.WorkflowId);
            }
            else
            {
                _logger.LogWarning("Failed to update workflow {WorkflowId}", workflowDefinition.WorkflowId);
            }

            return success;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update workflow {WorkflowId}", workflowDefinition.WorkflowId);
            return false;
        }
    }

    public async Task<bool> DeleteWorkflowAsync(string workflowId, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(workflowId);

        try
        {
            _logger.LogInformation("Deleting workflow {WorkflowId}", workflowId);

            var success = await _workflowRepository.DeleteWorkflowDefinitionAsync(workflowId, cancellationToken);
            
            if (success)
            {
                _logger.LogInformation("Successfully deleted workflow {WorkflowId}", workflowId);
            }
            else
            {
                _logger.LogWarning("Failed to delete workflow {WorkflowId}", workflowId);
            }

            return success;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete workflow {WorkflowId}", workflowId);
            return false;
        }
    }

    public async Task<WorkflowDefinition?> GetWorkflowAsync(string workflowId, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(workflowId);

        try
        {
            return await _workflowRepository.GetWorkflowDefinitionAsync(workflowId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get workflow {WorkflowId}", workflowId);
            return null;
        }
    }

    public async Task<PagedResult<WorkflowDefinition>> GetWorkflowsAsync(int pageIndex = 0, int pageSize = 20, CancellationToken cancellationToken = default)
    {
        try
        {
            var workflows = await _workflowRepository.GetWorkflowDefinitionsAsync(pageIndex, pageSize, cancellationToken);
            var totalCount = await _workflowRepository.GetWorkflowCountAsync(cancellationToken);

            return new PagedResult<WorkflowDefinition>
            {
                Items = workflows,
                TotalCount = totalCount,
                PageIndex = pageIndex,
                PageSize = pageSize
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get workflows");
            return new PagedResult<WorkflowDefinition>
            {
                Items = [],
                TotalCount = 0,
                PageIndex = pageIndex,
                PageSize = pageSize
            };
        }
    }

    public async Task<PagedResult<WorkflowDefinition>> SearchWorkflowsAsync(string searchTerm, int pageIndex = 0, int pageSize = 20, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(searchTerm);

        try
        {
            var workflows = await _workflowRepository.SearchWorkflowDefinitionsAsync(searchTerm, pageIndex, pageSize, cancellationToken);
            var totalCount = await _workflowRepository.GetWorkflowCountBySearchAsync(searchTerm, cancellationToken);

            return new PagedResult<WorkflowDefinition>
            {
                Items = workflows,
                TotalCount = totalCount,
                PageIndex = pageIndex,
                PageSize = pageSize
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to search workflows with term {SearchTerm}", searchTerm);
            return new PagedResult<WorkflowDefinition>
            {
                Items = [],
                TotalCount = 0,
                PageIndex = pageIndex,
                PageSize = pageSize
            };
        }
    }

    #endregion

    #region 工作流执行

    public async Task<WorkflowExecutionResult> ExecuteWorkflowAsync(string workflowId, Dictionary<string, object>? parameters = null, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(workflowId);

        try
        {
            _logger.LogInformation("Executing workflow {WorkflowId}", workflowId);

            // 获取工作流定义
            var workflowDefinition = await GetWorkflowAsync(workflowId, cancellationToken);
            if (workflowDefinition == null)
            {
                throw new InvalidOperationException($"Workflow {workflowId} not found");
            }

            // 使用工作流引擎执行
            return await _workflowEngine.ExecuteWorkflowAsync(workflowDefinition, parameters, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to execute workflow {WorkflowId}", workflowId);
            throw;
        }
    }

    public async Task<bool> StopWorkflowExecutionAsync(string executionId, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(executionId);

        try
        {
            _logger.LogInformation("Stopping workflow execution {ExecutionId}", executionId);

            // 使用工作流引擎停止执行
            await _workflowEngine.CancelWorkflowAsync(executionId, "Stopped by user request", cancellationToken);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to stop workflow execution {ExecutionId}", executionId);
            return false;
        }
    }

    public async Task<bool> PauseWorkflowExecutionAsync(string executionId, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(executionId);

        try
        {
            _logger.LogInformation("Pausing workflow execution {ExecutionId}", executionId);

            // 使用工作流引擎暂停执行
            await _workflowEngine.PauseWorkflowAsync(executionId, cancellationToken);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to pause workflow execution {ExecutionId}", executionId);
            return false;
        }
    }

    public async Task<bool> ResumeWorkflowExecutionAsync(string executionId, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(executionId);

        try
        {
            _logger.LogInformation("Resuming workflow execution {ExecutionId}", executionId);

            // 使用工作流引擎恢复执行
            await _workflowEngine.ResumeWorkflowAsync(executionId, cancellationToken);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to resume workflow execution {ExecutionId}", executionId);
            return false;
        }
    }

    public async Task<WorkflowExecutionStatus?> GetExecutionStatusAsync(string executionId, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(executionId);

        try
        {
            return await _workflowEngine.GetExecutionStatusAsync(executionId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get execution status for {ExecutionId}", executionId);
            return null;
        }
    }

    public async Task<PagedResult<WorkflowExecutionResult>> GetExecutionHistoryAsync(string workflowId, int pageIndex = 0, int pageSize = 20, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(workflowId);

        try
        {
            // 这里需要从执行历史仓储获取数据
            // 目前返回空结果，实际实现需要扩展
            return new PagedResult<WorkflowExecutionResult>
            {
                Items = [],
                TotalCount = 0,
                PageIndex = pageIndex,
                PageSize = pageSize
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get execution history for workflow {WorkflowId}", workflowId);
            return new PagedResult<WorkflowExecutionResult>
            {
                Items = [],
                TotalCount = 0,
                PageIndex = pageIndex,
                PageSize = pageSize
            };
        }
    }

    #endregion

    #region 工作流验证

    public async Task<WorkflowValidationResult> ValidateWorkflowAsync(WorkflowDefinition workflowDefinition, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(workflowDefinition);

        if (_validatorService != null)
        {
            return await _validatorService.ValidateWorkflowAsync(workflowDefinition, cancellationToken);
        }

        // 如果没有验证服务，使用基础验证
        return workflowDefinition.Validate();
    }

    public async Task<bool> CanExecuteWorkflowAsync(string workflowId, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(workflowId);

        try
        {
            var workflowDefinition = await GetWorkflowAsync(workflowId, cancellationToken);
            if (workflowDefinition == null)
            {
                return false;
            }

            var validationResult = await ValidateWorkflowAsync(workflowDefinition, cancellationToken);
            return validationResult.IsValid;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to check if workflow {WorkflowId} can be executed", workflowId);
            return false;
        }
    }

    #endregion

    #region 工作流版本管理

    public async Task<bool> CreateWorkflowVersionAsync(string workflowId, string version, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(workflowId);
        ArgumentException.ThrowIfNullOrWhiteSpace(version);

        try
        {
            _logger.LogInformation("Creating version {Version} for workflow {WorkflowId}", version, workflowId);

            // 获取当前工作流定义
            var workflowDefinition = await GetWorkflowAsync(workflowId, cancellationToken);
            if (workflowDefinition == null)
            {
                return false;
            }

            // 创建新版本
            var versionedWorkflow = workflowDefinition.Clone();
            versionedWorkflow.Version = version;
            versionedWorkflow.LastModifiedAt = DateTime.UtcNow;

            // 保存版本
            var success = await _workflowRepository.SaveWorkflowDefinitionAsync(versionedWorkflow, cancellationToken);
            
            if (success)
            {
                _logger.LogInformation("Successfully created version {Version} for workflow {WorkflowId}", version, workflowId);
            }

            return success;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create version {Version} for workflow {WorkflowId}", version, workflowId);
            return false;
        }
    }

    public async Task<IReadOnlyList<string>> GetWorkflowVersionsAsync(string workflowId, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(workflowId);

        try
        {
            return await _workflowRepository.GetWorkflowVersionsAsync(workflowId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get versions for workflow {WorkflowId}", workflowId);
            return new List<string>();
        }
    }

    public async Task<WorkflowDefinition?> GetWorkflowVersionAsync(string workflowId, string version, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(workflowId);
        ArgumentException.ThrowIfNullOrWhiteSpace(version);

        try
        {
            return await _workflowRepository.GetWorkflowDefinitionByVersionAsync(workflowId, version, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get version {Version} of workflow {WorkflowId}", version, workflowId);
            return null;
        }
    }

    #endregion
}
