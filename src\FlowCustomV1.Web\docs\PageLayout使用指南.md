# PageLayout 统一页面布局组件使用指南

## 📋 概述

`PageLayout` 是一个标准化的页面布局组件，用于统一所有功能页面的布局样式。它提供了一致的页面结构，包括页面标题、描述、操作按钮和内容区域。

## 🎯 设计目标

- **统一性** - 所有功能页面使用相同的布局结构
- **一致性** - 标题、描述、操作按钮的位置和样式保持一致
- **可维护性** - 布局样式集中管理，易于维护和更新
- **灵活性** - 支持自定义样式和内容结构

## 📦 组件属性

| 属性 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| `title` | `string` | ✅ | - | 页面标题 |
| `description` | `string` | ❌ | - | 页面描述 |
| `icon` | `React.ReactNode` | ❌ | - | 页面标题图标 |
| `actions` | `React.ReactNode` | ❌ | - | 页面右侧操作按钮 |
| `children` | `React.ReactNode` | ✅ | - | 页面内容 |
| `showHeaderDivider` | `boolean` | ❌ | `true` | 是否显示页面头部分隔线 |
| `containerClassName` | `string` | ❌ | `''` | 自定义页面容器样式 |
| `headerClassName` | `string` | ❌ | `''` | 自定义页面头部样式 |

## 🚀 基本使用

### 1. 导入组件

```tsx
import PageLayout from '@/components/Layout/PageLayout';
import { Button, Space } from 'antd';
import { PlusOutlined, ImportOutlined } from '@ant-design/icons';
```

### 2. 基本用法

```tsx
const MyPage: React.FC = () => {
  return (
    <PageLayout
      title="页面标题"
      description="页面描述信息"
    >
      {/* 页面具体内容 */}
      <div>页面内容</div>
    </PageLayout>
  );
};
```

### 3. 带图标和操作按钮

```tsx
const WorkflowList: React.FC = () => {
  return (
    <PageLayout
      title="工作流管理"
      description="管理和维护系统中的所有工作流"
      icon={<WorkflowOutlined />}
      actions={
        <Space>
          <Button icon={<ImportOutlined />}>导入</Button>
          <Button type="primary" icon={<PlusOutlined />}>
            新建工作流
          </Button>
        </Space>
      }
    >
      {/* 工作流列表内容 */}
      <ProTable {...tableProps} />
    </PageLayout>
  );
};
```

## 📝 使用示例

### 示例1：简单页面

```tsx
import React from 'react';
import PageLayout from '@/components/Layout/PageLayout';
import { SettingOutlined } from '@ant-design/icons';

const SystemConfig: React.FC = () => {
  return (
    <PageLayout
      title="系统配置"
      description="管理系统全局配置和参数"
      icon={<SettingOutlined />}
    >
      <div>
        {/* 配置表单内容 */}
      </div>
    </PageLayout>
  );
};
```

### 示例2：带工具栏的页面

```tsx
import React from 'react';
import PageLayout from '@/components/Layout/PageLayout';
import { Button, Space, Input, Select } from 'antd';
import { ReloadOutlined, FilterOutlined } from '@ant-design/icons';

const ExecutionMonitor: React.FC = () => {
  return (
    <PageLayout
      title="执行监控"
      description="实时监控工作流执行状态"
      actions={
        <Button type="primary" icon={<ReloadOutlined />}>
          刷新
        </Button>
      }
    >
      {/* 工具栏 */}
      <div className="toolbar">
        <div className="toolbar-left">
          <Space>
            <Input.Search placeholder="搜索..." />
            <Select placeholder="状态筛选" />
          </Space>
        </div>
      </div>

      {/* 监控内容 */}
      <div>
        {/* 执行列表 */}
      </div>
    </PageLayout>
  );
};
```

### 示例3：自定义样式

```tsx
import React from 'react';
import PageLayout from '@/components/Layout/PageLayout';

const CustomPage: React.FC = () => {
  return (
    <PageLayout
      title="自定义页面"
      description="展示自定义样式的页面"
      showHeaderDivider={false}
      containerClassName="custom-container"
      headerClassName="custom-header"
    >
      <div>
        {/* 自定义内容 */}
      </div>
    </PageLayout>
  );
};
```

## 🎨 样式类说明

### 全局样式类

- `.page-container` - 页面主容器（白色背景、圆角、内边距、阴影）
- `.page-header` - 页面头部区域（底部边框分隔）
- `.page-title` - 页面标题（20px字体、500字重）
- `.page-description` - 页面描述（14px字体、浅灰色）
- `.page-content` - 页面内容区域（flex布局）

### 工具栏样式类

- `.toolbar` - 工具栏容器（左右分布）
- `.toolbar-left` - 工具栏左侧区域
- `.toolbar-right` - 工具栏右侧区域

## 🔧 迁移指南

### 从旧布局迁移到PageLayout

**迁移前：**
```tsx
return (
  <div className="page-container">
    <div className="page-header">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="page-title">页面标题</h1>
          <p className="page-description">页面描述</p>
        </div>
        <Space>
          <Button>操作按钮</Button>
        </Space>
      </div>
    </div>
    {/* 页面内容 */}
  </div>
);
```

**迁移后：**
```tsx
return (
  <PageLayout
    title="页面标题"
    description="页面描述"
    actions={<Button>操作按钮</Button>}
  >
    {/* 页面内容 */}
  </PageLayout>
);
```

## ✅ 最佳实践

1. **统一使用PageLayout** - 所有功能页面都应该使用PageLayout组件
2. **合理使用图标** - 页面标题可以添加相关的图标，增强视觉识别
3. **操作按钮分组** - 使用Space组件对多个操作按钮进行分组
4. **描述信息简洁** - 页面描述应该简洁明了，说明页面的主要功能
5. **内容结构清晰** - 页面内容应该有清晰的层次结构

## 🚫 注意事项

1. **不要嵌套PageLayout** - 一个页面只使用一个PageLayout组件
2. **避免过多操作按钮** - 页面头部的操作按钮不宜过多，建议不超过3个
3. **保持样式一致** - 不要随意覆盖PageLayout的默认样式
4. **响应式考虑** - 确保页面在不同屏幕尺寸下都能正常显示
