@FlowCustomV1.Api_HostAddress = http://localhost:5257

### 获取所有工作流
GET {{FlowCustomV1.Api_HostAddress}}/api/workflows/
Accept: application/json

### 根据ID获取工作流
GET {{FlowCustomV1.Api_HostAddress}}/api/workflows/1
Accept: application/json

### 创建工作流
POST {{FlowCustomV1.Api_HostAddress}}/api/workflows/
Content-Type: application/json

{
  "id": "1",
  "name": "Sample Workflow",
  "description": "A sample workflow for testing",
  "nodes": [],
  "connections": []
}

### 更新工作流
PUT {{FlowCustomV1.Api_HostAddress}}/api/workflows/1
Content-Type: application/json

{
  "id": "1",
  "name": "Updated Sample Workflow",
  "description": "An updated sample workflow for testing",
  "nodes": [],
  "connections": []
}

### 删除工作流
DELETE {{FlowCustomV1.Api_HostAddress}}/api/workflows/1

### 启动工作流执行
POST {{FlowCustomV1.Api_HostAddress}}/api/executions/start/1

### 获取执行实例
GET {{FlowCustomV1.Api_HostAddress}}/api/executions/1

### 获取工作流的所有执行实例
GET {{FlowCustomV1.Api_HostAddress}}/api/executions/workflow/1