using FlowCustomV1.Core.Models.Workflow;
using FlowCustomV1.Core.Interfaces;
using FlowCustomV1.Core.Interfaces.Repositories;

using Microsoft.AspNetCore.Mvc;

namespace FlowCustomV1.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
public class ExecutionsController(IWorkflowEngine workflowEngineService, IExecutionRepository executionRepository, IWorkflowRepository workflowRepository) : ControllerBase
{

    // POST: api/executions/start/{workflowId}
    [HttpPost("start/{workflowId}")]
    public async Task<ActionResult<WorkflowExecutionResult>> StartExecution(string workflowId)
    {
        // 首先获取工作流定义
        var workflowDefinition = await workflowRepository.GetWorkflowDefinitionAsync(workflowId);
        if (workflowDefinition == null)
        {
            return NotFound("Workflow not found");
        }

        // 执行工作流
        var result = await workflowEngineService.ExecuteWorkflowAsync(workflowDefinition);
        return Ok(result);
    }

    // GET: api/executions/{executionId}
    [HttpGet("{executionId}")]
    public async Task<ActionResult<WorkflowExecutionResult>> GetExecution(string executionId)
    {
        var execution = await executionRepository.GetWorkflowInstanceAsync(executionId);
        if (execution == null)
        {
            return NotFound();
        }
        return Ok(execution);
    }

    // GET: api/executions/workflow/{workflowId}
    [HttpGet("workflow/{workflowId}")]
    public async Task<ActionResult<IEnumerable<WorkflowExecutionResult>>> GetExecutionsByWorkflow(string workflowId, [FromQuery] int pageIndex = 0, [FromQuery] int pageSize = 50)
    {
        var executions = await executionRepository.GetExecutionHistoryAsync(workflowId, pageIndex, pageSize);
        return Ok(executions);
    }
}