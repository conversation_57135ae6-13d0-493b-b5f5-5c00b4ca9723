import api from './api';
import type {
  WorkflowExecution,
  ExecutionStatus,
  PagedResponse,
  ExecutionStats
} from '@/types/api';

export interface ExecutionQueryParams {
  pageIndex?: number;
  pageSize?: number;
  status?: ExecutionStatus;
  workflowId?: string;
  startTime?: string;
  endTime?: string;
}

export const executionApi = {
  // 获取所有执行记录
  getAllExecutions: async (params: ExecutionQueryParams = {}): Promise<PagedResponse<WorkflowExecution>> => {
    const response = await api.get('/api/executions', { params });
    return response.data;
  },

  // 获取执行记录详情
  getExecutionById: async (id: string): Promise<WorkflowExecution> => {
    const response = await api.get(`/api/executions/${id}`);
    return response.data;
  },

  // 获取执行统计
  getExecutionStats: async (): Promise<ExecutionStats> => {
    const response = await api.get('/api/executions/stats');
    return response.data;
  },

  // 启动工作流执行
  startExecution: async (workflowId: string, parameters?: Record<string, any>): Promise<WorkflowExecution> => {
    const response = await api.post('/api/executions/start', {
      workflowId,
      parameters
    });
    return response.data;
  },

  // 执行工作流（别名方法，用于兼容）
  executeWorkflow: async (workflowId: string, parameters?: Record<string, any>): Promise<WorkflowExecution> => {
    const response = await api.post('/api/executions/start', {
      workflowId,
      parameters
    });
    return response.data;
  },

  // 根据工作流ID获取执行记录
  getExecutionsByWorkflow: async (workflowId: string, params: { pageSize?: number } = {}): Promise<PagedResponse<WorkflowExecution>> => {
    const response = await api.get('/api/executions', {
      params: {
        workflowId,
        pageSize: params.pageSize || 10
      }
    });
    return response.data;
  },

  // 暂停执行
  pauseExecution: async (executionId: string): Promise<void> => {
    await api.post(`/api/executions/${executionId}/pause`);
  },

  // 恢复执行
  resumeExecution: async (executionId: string): Promise<void> => {
    await api.post(`/api/executions/${executionId}/resume`);
  },

  // 停止执行
  stopExecution: async (executionId: string): Promise<void> => {
    await api.post(`/api/executions/${executionId}/stop`);
  },

  // 重试执行
  retryExecution: async (executionId: string): Promise<WorkflowExecution> => {
    const response = await api.post(`/api/executions/${executionId}/retry`);
    return response.data;
  },

  // 获取执行日志
  getExecutionLogs: async (executionId: string): Promise<string[]> => {
    const response = await api.get(`/api/executions/${executionId}/logs`);
    return response.data;
  },

  // 删除执行记录
  deleteExecution: async (executionId: string): Promise<void> => {
    await api.delete(`/api/executions/${executionId}`);
  },

  // 批量删除执行记录
  batchDeleteExecutions: async (executionIds: string[]): Promise<void> => {
    await api.post('/api/executions/batch-delete', { executionIds });
  },

  // 获取执行进度
  getExecutionProgress: async (executionId: string): Promise<{
    currentStep: number;
    totalSteps: number;
    progress: number;
    currentNodeId?: string;
    currentNodeName?: string;
  }> => {
    const response = await api.get(`/api/executions/${executionId}/progress`);
    return response.data;
  },

  // 获取执行结果
  getExecutionResult: async (executionId: string): Promise<{
    success: boolean;
    result?: any;
    error?: string;
    outputData?: Record<string, any>;
  }> => {
    const response = await api.get(`/api/executions/${executionId}/result`);
    return response.data;
  }
};

export default executionApi;
