using FlowCustomV1.Core.Models.Executor;
using FlowCustomV1.Core.Interfaces.Scheduling;

namespace FlowCustomV1.Core.Models.Scheduling;

/// <summary>
/// 任务状态变更事件参数
/// </summary>
public class TaskStatusChangedEventArgs : EventArgs
{
    /// <summary>
    /// 任务ID
    /// </summary>
    public string TaskId { get; set; } = string.Empty;

    /// <summary>
    /// 旧状态
    /// </summary>
    public TaskExecutionStatus? OldStatus { get; set; }

    /// <summary>
    /// 新状态
    /// </summary>
    public TaskExecutionStatus NewStatus { get; set; }

    /// <summary>
    /// 任务状态
    /// </summary>
    public TaskExecutionState TaskState { get; set; } = null!;

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdateTime { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 状态变更原因
    /// </summary>
    public string Reason { get; set; } = string.Empty;

    /// <summary>
    /// 执行节点ID
    /// </summary>
    public string ExecutorNodeId { get; set; } = string.Empty;

    /// <summary>
    /// 工作流ID
    /// </summary>
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 执行ID
    /// </summary>
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 状态持续时间（毫秒）
    /// </summary>
    public long? StatusDurationMs { get; set; }

    /// <summary>
    /// 附加信息
    /// </summary>
    public Dictionary<string, object> AdditionalInfo { get; set; } = new();
}

/// <summary>
/// 任务进度更新事件参数
/// </summary>
public class TaskProgressUpdatedEventArgs : EventArgs
{
    /// <summary>
    /// 任务ID
    /// </summary>
    public string TaskId { get; set; } = string.Empty;

    /// <summary>
    /// 进度信息
    /// </summary>
    public TaskProgress Progress { get; set; } = null!;

    /// <summary>
    /// 任务状态
    /// </summary>
    public TaskExecutionState TaskState { get; set; } = null!;

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdateTime { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 执行节点ID
    /// </summary>
    public string ExecutorNodeId { get; set; } = string.Empty;

    /// <summary>
    /// 工作流ID
    /// </summary>
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 执行ID
    /// </summary>
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 进度变化量
    /// </summary>
    public double ProgressDelta { get; set; }

    /// <summary>
    /// 预计剩余时间（毫秒）
    /// </summary>
    public long? EstimatedRemainingTimeMs { get; set; }

    /// <summary>
    /// 处理速度（项目/秒）
    /// </summary>
    public double ProcessingRate { get; set; }

    /// <summary>
    /// 附加信息
    /// </summary>
    public Dictionary<string, object> AdditionalInfo { get; set; } = new();
}

/// <summary>
/// 任务执行完成事件参数
/// </summary>
public class TaskExecutionCompletedEventArgs : EventArgs
{
    /// <summary>
    /// 任务ID
    /// </summary>
    public string TaskId { get; set; } = string.Empty;

    /// <summary>
    /// 任务状态
    /// </summary>
    public TaskExecutionState TaskState { get; set; } = null!;

    /// <summary>
    /// 执行结果
    /// </summary>
    public TaskExecutionResult ExecutionResult { get; set; } = null!;

    /// <summary>
    /// 完成时间
    /// </summary>
    public DateTime CompletedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 执行节点ID
    /// </summary>
    public string ExecutorNodeId { get; set; } = string.Empty;

    /// <summary>
    /// 工作流ID
    /// </summary>
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 执行ID
    /// </summary>
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 总执行时间（毫秒）
    /// </summary>
    public long TotalExecutionTimeMs { get; set; }

    /// <summary>
    /// 重试次数
    /// </summary>
    public int RetryCount { get; set; }

    /// <summary>
    /// 输出数据大小（字节）
    /// </summary>
    public long OutputDataSize { get; set; }

    /// <summary>
    /// 资源使用情况
    /// </summary>
    public FlowCustomV1.Core.Models.Executor.ResourceUsage? ResourceUsage { get; set; }

    /// <summary>
    /// 性能指标
    /// </summary>
    public TaskPerformanceMetrics? PerformanceMetrics { get; set; }

    /// <summary>
    /// 附加信息
    /// </summary>
    public Dictionary<string, object> AdditionalInfo { get; set; } = new();
}

/// <summary>
/// 任务执行失败事件参数
/// </summary>
public class TaskExecutionFailedEventArgs : EventArgs
{
    /// <summary>
    /// 任务ID
    /// </summary>
    public string TaskId { get; set; } = string.Empty;

    /// <summary>
    /// 任务状态
    /// </summary>
    public TaskExecutionState TaskState { get; set; } = null!;

    /// <summary>
    /// 执行结果
    /// </summary>
    public TaskExecutionResult ExecutionResult { get; set; } = null!;

    /// <summary>
    /// 失败时间
    /// </summary>
    public DateTime FailedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 执行节点ID
    /// </summary>
    public string ExecutorNodeId { get; set; } = string.Empty;

    /// <summary>
    /// 工作流ID
    /// </summary>
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 执行ID
    /// </summary>
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 错误消息
    /// </summary>
    public string ErrorMessage { get; set; } = string.Empty;

    /// <summary>
    /// 错误详情
    /// </summary>
    public string? ErrorDetails { get; set; }

    /// <summary>
    /// 错误类型
    /// </summary>
    public string? ErrorType { get; set; }

    /// <summary>
    /// 错误代码
    /// </summary>
    public string? ErrorCode { get; set; }

    /// <summary>
    /// 是否可重试
    /// </summary>
    public bool IsRetryable { get; set; }

    /// <summary>
    /// 重试次数
    /// </summary>
    public int RetryCount { get; set; }

    /// <summary>
    /// 最大重试次数
    /// </summary>
    public int MaxRetries { get; set; }

    /// <summary>
    /// 失败前的执行时间（毫秒）
    /// </summary>
    public long ExecutionTimeBeforeFailure { get; set; }

    /// <summary>
    /// 资源使用情况
    /// </summary>
    public FlowCustomV1.Core.Models.Executor.ResourceUsage? ResourceUsage { get; set; }

    /// <summary>
    /// 堆栈跟踪
    /// </summary>
    public string? StackTrace { get; set; }

    /// <summary>
    /// 附加信息
    /// </summary>
    public Dictionary<string, object> AdditionalInfo { get; set; } = new();
}

/// <summary>
/// 任务超时事件参数
/// </summary>
public class TaskTimeoutEventArgs : EventArgs
{
    /// <summary>
    /// 任务ID
    /// </summary>
    public string TaskId { get; set; } = string.Empty;

    /// <summary>
    /// 任务状态
    /// </summary>
    public TaskExecutionState TaskState { get; set; } = null!;

    /// <summary>
    /// 超时时间
    /// </summary>
    public DateTime TimeoutAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 执行节点ID
    /// </summary>
    public string ExecutorNodeId { get; set; } = string.Empty;

    /// <summary>
    /// 工作流ID
    /// </summary>
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 执行ID
    /// </summary>
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 超时阈值（毫秒）
    /// </summary>
    public long TimeoutThresholdMs { get; set; }

    /// <summary>
    /// 实际执行时间（毫秒）
    /// </summary>
    public long ActualExecutionTimeMs { get; set; }

    /// <summary>
    /// 超时类型
    /// </summary>
    public TimeoutType TimeoutType { get; set; }

    /// <summary>
    /// 超时原因
    /// </summary>
    public string TimeoutReason { get; set; } = string.Empty;

    /// <summary>
    /// 是否强制终止
    /// </summary>
    public bool IsForceTerminated { get; set; }

    /// <summary>
    /// 附加信息
    /// </summary>
    public Dictionary<string, object> AdditionalInfo { get; set; } = new();
}

/// <summary>
/// 超时类型枚举
/// </summary>
public enum TimeoutType
{
    /// <summary>
    /// 执行超时
    /// </summary>
    ExecutionTimeout,

    /// <summary>
    /// 初始化超时
    /// </summary>
    InitializationTimeout,

    /// <summary>
    /// 响应超时
    /// </summary>
    ResponseTimeout,

    /// <summary>
    /// 清理超时
    /// </summary>
    CleanupTimeout
}

/// <summary>
/// 任务重试事件参数
/// </summary>
public class TaskRetryEventArgs : EventArgs
{
    /// <summary>
    /// 任务ID
    /// </summary>
    public string TaskId { get; set; } = string.Empty;

    /// <summary>
    /// 任务状态
    /// </summary>
    public TaskExecutionState TaskState { get; set; } = null!;

    /// <summary>
    /// 重试时间
    /// </summary>
    public DateTime RetryAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 执行节点ID
    /// </summary>
    public string ExecutorNodeId { get; set; } = string.Empty;

    /// <summary>
    /// 工作流ID
    /// </summary>
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 执行ID
    /// </summary>
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 当前重试次数
    /// </summary>
    public int CurrentRetryCount { get; set; }

    /// <summary>
    /// 最大重试次数
    /// </summary>
    public int MaxRetries { get; set; }

    /// <summary>
    /// 重试原因
    /// </summary>
    public string RetryReason { get; set; } = string.Empty;

    /// <summary>
    /// 上次失败的错误信息
    /// </summary>
    public string? LastFailureError { get; set; }

    /// <summary>
    /// 重试延迟（毫秒）
    /// </summary>
    public long RetryDelayMs { get; set; }

    /// <summary>
    /// 是否为最后一次重试
    /// </summary>
    public bool IsLastRetry { get; set; }

    /// <summary>
    /// 重试策略
    /// </summary>
    public string RetryStrategy { get; set; } = string.Empty;

    /// <summary>
    /// 附加信息
    /// </summary>
    public Dictionary<string, object> AdditionalInfo { get; set; } = new();
}
