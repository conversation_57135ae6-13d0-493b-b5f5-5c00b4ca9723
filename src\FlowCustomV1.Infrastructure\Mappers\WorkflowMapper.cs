using System.Text.Json;
using FlowCustomV1.Core.Models.Workflow;
using FlowCustomV1.Infrastructure.Entities;

namespace FlowCustomV1.Infrastructure.Mappers;

/// <summary>
/// 工作流模型映射器
/// 负责领域模型和实体模型之间的转换
/// </summary>
public static class WorkflowMapper
{
    private static readonly JsonSerializerOptions JsonOptions = new()
    {
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        WriteIndented = false
    };

    /// <summary>
    /// 将工作流定义转换为实体
    /// </summary>
    /// <param name="definition">工作流定义</param>
    /// <returns>工作流定义实体</returns>
    public static WorkflowDefinitionEntity ToEntity(this WorkflowDefinition definition)
    {
        return new WorkflowDefinitionEntity
        {
            WorkflowId = definition.WorkflowId,
            Name = definition.Name,
            Description = definition.Description,
            Version = definition.Version,
            Author = definition.Author,
            DefinitionJson = JsonSerializer.Serialize(definition, JsonOptions),
            CreatedAt = definition.CreatedAt,
            LastModifiedAt = definition.LastModifiedAt,
            IsActive = true,
            Tags = definition.Tags.Any() ? JsonSerializer.Serialize(definition.Tags, JsonOptions) : null,
            Metadata = definition.Metadata.Any() ? JsonSerializer.Serialize(definition.Metadata, JsonOptions) : null
        };
    }

    /// <summary>
    /// 将工作流定义实体转换为领域模型
    /// </summary>
    /// <param name="entity">工作流定义实体</param>
    /// <returns>工作流定义</returns>
    public static WorkflowDefinition ToDomain(this WorkflowDefinitionEntity entity)
    {
        var definition = JsonSerializer.Deserialize<WorkflowDefinition>(entity.DefinitionJson, JsonOptions)
                        ?? new WorkflowDefinition();

        // 确保基本属性正确
        definition.WorkflowId = entity.WorkflowId;
        definition.Name = entity.Name;
        definition.Description = entity.Description ?? string.Empty;
        definition.Version = entity.Version;
        definition.Author = entity.Author ?? string.Empty;
        definition.CreatedAt = entity.CreatedAt;
        definition.LastModifiedAt = entity.LastModifiedAt;

        // 反序列化标签和元数据
        if (!string.IsNullOrEmpty(entity.Tags))
        {
            try
            {
                var tags = JsonSerializer.Deserialize<HashSet<string>>(entity.Tags, JsonOptions);
                definition.Tags = tags ?? new HashSet<string>();
            }
            catch
            {
                definition.Tags = new HashSet<string>();
            }
        }

        if (!string.IsNullOrEmpty(entity.Metadata))
        {
            try
            {
                var metadata = JsonSerializer.Deserialize<Dictionary<string, object>>(entity.Metadata, JsonOptions);
                definition.Metadata = metadata ?? new Dictionary<string, object>();
            }
            catch
            {
                definition.Metadata = new Dictionary<string, object>();
            }
        }

        return definition;
    }

    /// <summary>
    /// 将工作流执行结果转换为实例实体
    /// </summary>
    /// <param name="result">工作流执行结果</param>
    /// <returns>工作流实例实体</returns>
    public static WorkflowInstanceEntity ToEntity(this WorkflowExecutionResult result)
    {
        return new WorkflowInstanceEntity
        {
            InstanceId = result.ExecutionId,
            WorkflowId = result.WorkflowId,
            State = result.State,
            IsSuccess = result.IsSuccess,
            StartedAt = result.StartedAt,
            CompletedAt = result.CompletedAt,
            InputData = null, // 需要从外部传入
            OutputData = result.OutputData.Any() ? JsonSerializer.Serialize(result.OutputData, JsonOptions) : null,
            ErrorMessage = result.ErrorMessage,
            Message = result.Message,
            Stats = JsonSerializer.Serialize(result.Stats, JsonOptions),
            Metadata = result.Metadata.Any() ? JsonSerializer.Serialize(result.Metadata, JsonOptions) : null,
            CreatedAt = result.StartedAt,
            LastUpdatedAt = result.CompletedAt ?? DateTime.UtcNow
        };
    }

    /// <summary>
    /// 将工作流实例实体转换为执行结果
    /// </summary>
    /// <param name="entity">工作流实例实体</param>
    /// <returns>工作流执行结果</returns>
    public static WorkflowExecutionResult ToDomain(this WorkflowInstanceEntity entity)
    {
        var result = new WorkflowExecutionResult
        {
            ExecutionId = entity.InstanceId,
            WorkflowId = entity.WorkflowId,
            State = entity.State,
            IsSuccess = entity.IsSuccess,
            StartedAt = entity.StartedAt,
            CompletedAt = entity.CompletedAt,
            ErrorMessage = entity.ErrorMessage,
            Message = entity.Message
        };

        // 反序列化输出数据
        if (!string.IsNullOrEmpty(entity.OutputData))
        {
            try
            {
                var outputData = JsonSerializer.Deserialize<Dictionary<string, object>>(entity.OutputData, JsonOptions);
                result.OutputData = outputData ?? new Dictionary<string, object>();
            }
            catch
            {
                result.OutputData = new Dictionary<string, object>();
            }
        }

        // 反序列化统计信息
        if (!string.IsNullOrEmpty(entity.Stats))
        {
            try
            {
                var stats = JsonSerializer.Deserialize<WorkflowExecutionStats>(entity.Stats, JsonOptions);
                result.Stats = stats ?? new WorkflowExecutionStats();
            }
            catch
            {
                result.Stats = new WorkflowExecutionStats();
            }
        }

        // 反序列化元数据
        if (!string.IsNullOrEmpty(entity.Metadata))
        {
            try
            {
                var metadata = JsonSerializer.Deserialize<Dictionary<string, object>>(entity.Metadata, JsonOptions);
                result.Metadata = metadata ?? new Dictionary<string, object>();
            }
            catch
            {
                result.Metadata = new Dictionary<string, object>();
            }
        }

        return result;
    }

    /// <summary>
    /// 将节点执行结果转换为实体
    /// </summary>
    /// <param name="result">节点执行结果</param>
    /// <param name="instanceId">工作流实例ID</param>
    /// <returns>节点执行实体</returns>
    public static NodeExecutionEntity ToEntity(this NodeExecutionResult result, string instanceId)
    {
        return new NodeExecutionEntity
        {
            ExecutionId = result.ExecutionId,
            InstanceId = instanceId,
            NodeId = result.NodeId,
            NodeType = "Unknown", // 需要从外部传入
            State = result.State,
            IsSuccess = result.IsSuccess,
            StartedAt = result.StartedAt,
            CompletedAt = result.CompletedAt,
            InputData = result.InputData.Any() ? JsonSerializer.Serialize(result.InputData, JsonOptions) : null,
            OutputData = result.OutputData.Any() ? JsonSerializer.Serialize(result.OutputData, JsonOptions) : null,
            ErrorMessage = result.ErrorMessage,
            RetryCount = result.RetryCount,
            ExecutionLogs = result.ExecutionLogs.Any() ? JsonSerializer.Serialize(result.ExecutionLogs, JsonOptions) : null,
            PerformanceMetrics = result.PerformanceMetrics.Any() ? JsonSerializer.Serialize(result.PerformanceMetrics, JsonOptions) : null,
            Metadata = result.Metadata.Any() ? JsonSerializer.Serialize(result.Metadata, JsonOptions) : null,
            CreatedAt = result.StartedAt,
            LastUpdatedAt = result.CompletedAt ?? DateTime.UtcNow
        };
    }

    /// <summary>
    /// 将节点执行实体转换为结果
    /// </summary>
    /// <param name="entity">节点执行实体</param>
    /// <returns>节点执行结果</returns>
    public static NodeExecutionResult ToDomain(this NodeExecutionEntity entity)
    {
        var result = new NodeExecutionResult
        {
            NodeId = entity.NodeId,
            ExecutionId = entity.ExecutionId,
            State = entity.State,
            IsSuccess = entity.IsSuccess,
            StartedAt = entity.StartedAt,
            CompletedAt = entity.CompletedAt,
            ErrorMessage = entity.ErrorMessage,
            RetryCount = entity.RetryCount
        };

        // 反序列化各种JSON数据
        if (!string.IsNullOrEmpty(entity.InputData))
        {
            try
            {
                var inputData = JsonSerializer.Deserialize<Dictionary<string, object>>(entity.InputData, JsonOptions);
                result.InputData = inputData ?? new Dictionary<string, object>();
            }
            catch
            {
                result.InputData = new Dictionary<string, object>();
            }
        }

        if (!string.IsNullOrEmpty(entity.OutputData))
        {
            try
            {
                var outputData = JsonSerializer.Deserialize<Dictionary<string, object>>(entity.OutputData, JsonOptions);
                result.OutputData = outputData ?? new Dictionary<string, object>();
            }
            catch
            {
                result.OutputData = new Dictionary<string, object>();
            }
        }

        if (!string.IsNullOrEmpty(entity.ExecutionLogs))
        {
            try
            {
                var logs = JsonSerializer.Deserialize<List<string>>(entity.ExecutionLogs, JsonOptions);
                result.ExecutionLogs = logs ?? new List<string>();
            }
            catch
            {
                result.ExecutionLogs = new List<string>();
            }
        }

        if (!string.IsNullOrEmpty(entity.PerformanceMetrics))
        {
            try
            {
                var metrics = JsonSerializer.Deserialize<Dictionary<string, double>>(entity.PerformanceMetrics, JsonOptions);
                result.PerformanceMetrics = metrics ?? new Dictionary<string, double>();
            }
            catch
            {
                result.PerformanceMetrics = new Dictionary<string, double>();
            }
        }

        if (!string.IsNullOrEmpty(entity.Metadata))
        {
            try
            {
                var metadata = JsonSerializer.Deserialize<Dictionary<string, object>>(entity.Metadata, JsonOptions);
                result.Metadata = metadata ?? new Dictionary<string, object>();
            }
            catch
            {
                result.Metadata = new Dictionary<string, object>();
            }
        }

        return result;
    }

    /// <summary>
    /// 将工作流定义实体转换为模型（ToDomain的别名）
    /// </summary>
    /// <param name="entity">工作流定义实体</param>
    /// <returns>工作流定义</returns>
    public static WorkflowDefinition ToModel(WorkflowDefinitionEntity entity)
    {
        return entity.ToDomain();
    }
}
