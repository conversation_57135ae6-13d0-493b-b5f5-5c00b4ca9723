# FlowCustomV1 Docker环境手动验证清单

## 📋 快速验证步骤

### 1. 容器状态检查 ✅
```bash
docker ps
```
**预期结果**：
- [ ] flowcustom-test-nats: Up (healthy)
- [ ] flowcustom-test-mysql: Up (healthy)  
- [ ] flowcustom-test-master: Up
- [ ] flowcustom-test-worker: Up

### 2. 基础服务健康检查 ✅
```bash
# NATS健康检查
curl http://localhost:8222/healthz

# MySQL连接测试
docker exec flowcustom-test-mysql mysql -uflowcustom -pTestPassword123! -e "SELECT 1;"
```
**预期结果**：
- [ ] NATS返回: `{"status":"ok"}`
- [ ] MySQL返回: `1`

### 3. 应用启动验证 ✅
```bash
# 检查Master节点日志
docker logs flowcustom-test-master | findstr "Application started"

# 检查Worker节点日志  
docker logs flowcustom-test-worker | findstr "Application started"
```
**预期结果**：
- [ ] Master节点显示 "Application started"
- [ ] Worker节点显示 "Application started"

### 4. 配置验证 ✅
```bash
# 检查Master节点配置
docker exec flowcustom-test-master env | findstr "Nats__Servers__0"

# 检查Worker节点配置
docker exec flowcustom-test-worker env | findstr "Nats__Servers__0"
```
**预期结果**：
- [ ] 两个节点都显示: `Nats__Servers__0=nats://nats:4222`

### 5. 节点通信验证 ✅
```bash
# 检查Master节点心跳
docker logs flowcustom-test-master | findstr "heartbeat" | Select-Object -Last 3

# 检查Worker节点心跳
docker logs flowcustom-test-worker | findstr "heartbeat" | Select-Object -Last 3
```
**预期结果**：
- [ ] Master节点有心跳日志
- [ ] Worker节点有心跳日志
- [ ] 心跳时间戳是最近的（几秒内）

### 6. 消息传递验证 ✅
```bash
# 检查消息发布日志
docker logs flowcustom-test-master | findstr "Message published successfully" | Select-Object -Last 2
docker logs flowcustom-test-worker | findstr "Message published successfully" | Select-Object -Last 2
```
**预期结果**：
- [ ] Master节点有消息发布成功日志
- [ ] Worker节点有消息发布成功日志

---

## 🔧 故障排查命令

### 如果容器未启动
```bash
# 查看容器日志
docker logs [container_name]

# 重新启动
docker-compose -f docker-compose.simple.yml restart [service_name]
```

### 如果应用无法连接
```bash
# 检查网络连接
docker exec flowcustom-test-master nc -zv nats 4222
docker exec flowcustom-test-master nc -zv mysql 3306

# 检查环境变量
docker exec flowcustom-test-master env | grep -E "(Nats|Database)"
```

### 如果节点无法通信
```bash
# 检查NATS连接
docker logs flowcustom-test-master | findstr "NATS"
docker logs flowcustom-test-worker | findstr "NATS"

# 检查节点注册
docker logs flowcustom-test-master | findstr "register"
docker logs flowcustom-test-worker | findstr "register"
```

---

## ✅ 验证完成确认

**基础设施验证**：
- [ ] NATS服务正常运行
- [ ] MySQL服务正常运行
- [ ] 网络连接正常

**应用验证**：
- [ ] Master节点正常启动
- [ ] Worker节点正常启动
- [ ] 配置正确加载

**通信验证**：
- [ ] 节点发现正常
- [ ] 心跳机制正常
- [ ] 消息传递正常

**配置体系验证**：
- [ ] 无硬编码配置
- [ ] 环境变量正确
- [ ] 配置文件正确

---

## 🎯 最终确认

如果以上所有检查项都通过，则：

**✅ FlowCustomV1 v0.0.1.8 Docker测试环境验证成功！**

- 配置体系重构完全成功
- Docker环境稳定可靠
- 分布式节点通信正常
- 系统质量达到预期目标

**可以进行下一阶段开发工作。**
