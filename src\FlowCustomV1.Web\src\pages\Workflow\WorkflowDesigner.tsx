import React, { useEffect, useRef, useState } from 'react';
import { Graph } from '@antv/x6';
import { register } from '@antv/x6-react-shape';
import { Stencil } from '@antv/x6-plugin-stencil';
import { MiniMap } from '@antv/x6-plugin-minimap';
import { Selection } from '@antv/x6-plugin-selection';
import { Snapline } from '@antv/x6-plugin-snapline';
import { Keyboard } from '@antv/x6-plugin-keyboard';
import { Clipboard } from '@antv/x6-plugin-clipboard';
import { History } from '@antv/x6-plugin-history';
import { Scroller } from '@antv/x6-plugin-scroller';
import { Button, Space, message, Card, Divider } from 'antd';
import {
  UndoOutlined,
  RedoOutlined,
  CopyOutlined,
  ScissorOutlined,
  DeleteOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
  SaveOutlined,
  PlayCircleOutlined,
  SettingOutlined,
  CompressOutlined,
  MenuOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  ApartmentOutlined,
} from '@ant-design/icons';
import PageLayout from '@/components/Layout/PageLayout';
import { BaseNode, CircleNode, DiamondNode, NodeData } from '@/components/Workflow/WorkflowNodes';

// ========== React节点组件定义 ==========
// 工作流节点组件 - 用于Stencil侧边栏
const StencilWorkflowNode = (props: any) => {
  const { node } = props;
  const data = node?.getData() as NodeData;

  return <BaseNode node={node} data={data} />;
};

// 注册React节点组件
register({
  shape: 'stencil-workflow-node',
  width: 160,
  height: 36,
  component: StencilWorkflowNode,
  ports: {
    groups: {
      top: {
        position: 'top',
        attrs: {
          circle: {
            r: 4,
            magnet: true,
            stroke: '#C2C8D5',
            strokeWidth: 1,
            fill: '#fff',
          },
        },
      },
      bottom: {
        position: 'bottom',
        attrs: {
          circle: {
            r: 4,
            magnet: true,
            stroke: '#C2C8D5',
            strokeWidth: 1,
            fill: '#fff',
          },
        },
      },
    },
  },
});

// ========== 页面布局配置 ==========
const PAGE_LAYOUT_CONFIG = {
  // 🎯 页面类型标识
  pageType: 'workflow-designer',

  // 🟠 工具栏配置
  hasToolbar: true,
  toolbarHeight: 'var(--layout-toolbar-height)',
  toolbarMargin: 'var(--layout-toolbar-margin)',
  toolbarComplexity: 'high', // 复杂工具栏

  // 🔵 设计器内容配置
  designerHeight: 'calc(100vh - 280px)', // 设计器高度

  // 📐 页面特定间距调整
  customSpacing: {
    extraOffset: 0,
    compactMode: false
  }
};

// 🗺️ 小地图配置
const MINIMAP_CONFIG = {
  // 基础配置
  scalable: true,
  minScale: 0.01,
  maxScale: 16,

  // 尝试强制设置小地图的显示区域
  enabled: true,

  // 样式配置
  graphOptions: {
    async: true,
    // 移除getCellView，让小地图使用默认渲染
    // getCellView(cell: any) {
    //   // 简化小地图中的节点显示
    //   if (cell.isNode()) {
    //     return null; // 使用默认渲染
    //   }
    // },
  },
};

// 🏗️ 布局配置
const LAYOUT_CONFIG = {
  // 左侧容器配置
  leftPanel: {
    width: 'w-64', // 左侧面板宽度
    flexShrink: 'flex-shrink-0', // 不允许收缩
  },

  // 右侧容器配置
  rightPanel: {
    width: 'w-55', // 右侧面板宽度
    flexShrink: 'flex-shrink-0', // 不允许收缩
    spacing: 'mb-4', // Card之间的间距

    // 快捷键Card配置
    shortcutCard: {
      height: '200px', // 固定高度
      flexShrink: 0, // 不允许收缩
    },

    // 小地图Card配置
    minimapCard: {
      height: '250px', // 固定合适高度
      minHeight: '200px', // 最小高度
    },
  },

  // Card标题栏统一样式
  cardHeaderStyle: {
    fontSize: '14px',
    fontWeight: 500,
    minHeight: '40px',
    padding: '8px 16px'
  },

  // Card内容区统一样式
  cardBodyStyle: {
    padding: '8px 12px',
    height: 'auto'
  },

  // 小地图Card特殊body样式
  minimapCardBodyStyle: {
    padding: '8px', // 适当的padding
    height: 'calc(100% - 40px)', // 减去header高度
    overflow: 'hidden',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center'
  },
};

// 全局标记，避免重复注册节点
let nodesRegistered = false;

const WorkflowDesigner: React.FC = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const stencilContainerRef = useRef<HTMLDivElement>(null);
  const minimapContainerRef = useRef<HTMLDivElement>(null);
  const graphRef = useRef<Graph | null>(null);
  const stencilRef = useRef<Stencil | null>(null);
  const minimapRef = useRef<MiniMap | null>(null);
  const [canUndo, setCanUndo] = useState(false);
  const [canRedo, setCanRedo] = useState(false);
  const [stencilVisible, setStencilVisible] = useState(true);
  const [minimapVisible, setMinimapVisible] = useState(true);

  // 注册自定义节点形状
  const registerCustomNodes = () => {
    // 检查是否已经注册过，避免重复注册
    if (nodesRegistered) {
      return;
    }

    // 注册开始节点 - 圆形
    register({
      shape: 'start-node',
      width: 60,
      height: 60,
      component: CircleNode,
      ports: {
        groups: {
          out: {
            position: 'bottom',
            attrs: {
              circle: {
                r: 4,
                magnet: true,
                stroke: '#52c41a',
                strokeWidth: 2,
                fill: '#fff',
                style: { visibility: 'hidden' },
              },
            },
          },
        },
        items: [{ group: 'out' }],
      },
    });

    // 注册结束节点 - 圆形
    register({
      shape: 'end-node',
      width: 60,
      height: 60,
      component: CircleNode,
      ports: {
        groups: {
          in: {
            position: 'top',
            attrs: {
              circle: {
                r: 4,
                magnet: true,
                stroke: '#ff4d4f',
                strokeWidth: 2,
                fill: '#fff',
                style: { visibility: 'hidden' },
              },
            },
          },
        },
        items: [{ group: 'in' }],
      },
    });

    // 注册任务节点 - 矩形
    register({
      shape: 'task-node',
      width: 140,
      height: 40,
      component: BaseNode,
      ports: {
        groups: {
          in: {
            position: 'left',
            attrs: {
              circle: {
                r: 4,
                magnet: true,
                stroke: '#1890ff',
                strokeWidth: 2,
                fill: '#fff',
                style: { visibility: 'hidden' },
              },
            },
          },
          out: {
            position: 'right',
            attrs: {
              circle: {
                r: 4,
                magnet: true,
                stroke: '#1890ff',
                strokeWidth: 2,
                fill: '#fff',
                style: { visibility: 'hidden' },
              },
            },
          },
        },
        items: [{ group: 'in' }, { group: 'out' }],
      },
    });

    // 注册决策节点 - 菱形
    register({
      shape: 'decision-node',
      width: 80,
      height: 80,
      component: DiamondNode,
      ports: {
        groups: {
          in: {
            position: 'top',
            attrs: {
              circle: {
                r: 4,
                magnet: true,
                stroke: '#fa541c',
                strokeWidth: 2,
                fill: '#fff',
                style: { visibility: 'hidden' },
              },
            },
          },
          out: {
            position: 'bottom',
            attrs: {
              circle: {
                r: 4,
                magnet: true,
                stroke: '#fa541c',
                strokeWidth: 2,
                fill: '#fff',
                style: { visibility: 'hidden' },
              },
            },
          },
        },
        items: [{ group: 'in' }, { group: 'out' }],
      },
    });

    // 标记节点已注册
    nodesRegistered = true;
  };

  // 创建节点的辅助函数 - 统一使用长方形样式
  const createNode = (type: string, label: string, x: number, y: number) => {
    const nodeConfig: any = {
      x,
      y,
      width: 180, // 增加宽度以适应列表布局
      height: 50, // 增加高度以适应更多内容
      data: {
        label,
        type: type.toLowerCase(),
        description: getNodeDescription(type),
      },
    };

    // 所有节点都使用统一的长方形样式
    switch (type) {
      case 'Start':
        return { ...nodeConfig, shape: 'task-node', data: { ...nodeConfig.data, type: 'start' } };
      case 'End':
        return { ...nodeConfig, shape: 'task-node', data: { ...nodeConfig.data, type: 'end' } };
      case 'Task':
        return { ...nodeConfig, shape: 'task-node', data: { ...nodeConfig.data, type: 'task' } };
      case 'TimerTrigger':
        return { ...nodeConfig, shape: 'task-node', data: { ...nodeConfig.data, type: 'timer' } };
      case 'HttpRequest':
        return { ...nodeConfig, shape: 'task-node', data: { ...nodeConfig.data, type: 'http' } };
      case 'DataProcessor':
        return { ...nodeConfig, shape: 'task-node', data: { ...nodeConfig.data, type: 'database' } };
      case 'Decision':
        return { ...nodeConfig, shape: 'task-node', data: { ...nodeConfig.data, type: 'decision' } };
      default:
        return { ...nodeConfig, shape: 'task-node', data: { ...nodeConfig.data, type: 'task' } };
    }
  };

  // 获取节点描述
  const getNodeDescription = (type: string): string => {
    switch (type) {
      // 基础控制节点 (3种)
      case 'Start': return '流程开始';
      case 'End': return '流程结束';
      case 'Task': return '执行任务';

      // 触发器节点 (3种)
      case 'TimerTrigger': return '定时触发';
      case 'EventTrigger': return '事件触发';
      case 'WebhookTrigger': return 'Webhook触发';

      // 动作节点 (3种)
      case 'HttpRequest': return 'HTTP请求';
      case 'DataProcessor': return '数据处理';
      case 'NotificationSender': return '通知发送';

      // 控制流节点 (3种)
      case 'IfCondition': return '条件判断';
      case 'ForLoop': return '循环执行';
      case 'ParallelExecution': return '并行执行';

      // 数据转换节点 (2种)
      case 'DataMapper': return '数据映射';
      case 'DataFilter': return '数据过滤';

      // 外部服务节点 (3种)
      case 'MySqlDatabase': return 'MySQL数据库';
      case 'NatsMessage': return 'NATS消息';
      case 'RestApiCall': return 'REST API调用';

      default: return '通用任务';
    }
  };

  // 创建Stencil专用的简化节点 - 使用SVG渲染
  // 创建Stencil专用节点 - 使用React组件
  const createStencilNode = (type: string, label: string) => {
    return {
      shape: 'stencil-workflow-node',
      width: 160,
      height: 40,
      data: {
        label,
        type: type.toLowerCase(),
        description: getNodeDescription(type),
        status: 'default',
      },
    };
  };

  useEffect(() => {
    if (!containerRef.current || !stencilContainerRef.current || !minimapContainerRef.current) {
      return;
    }

    // 注册自定义节点
    registerCustomNodes();

    // 创建图形实例
    const graph: Graph = new Graph({
      container: containerRef.current,
      autoResize: true,
      background: {
        color: '#F2F7FA',
      },
      grid: {
        size: 10,
        visible: true,
        type: 'doubleMesh',
        args: [
          {
            color: '#E7E8EA',
            thickness: 1,
          },
          {
            color: '#CBCED3',
            thickness: 1,
            factor: 4,
          },
        ],
      },
      // 禁用默认的平移和缩放，使用Scroller插件
      panning: false,
      mousewheel: false,
      connecting: {
        router: 'manhattan',
        connector: {
          name: 'rounded',
          args: {
            radius: 8,
          },
        },
        anchor: 'center',
        connectionPoint: 'anchor',
        allowBlank: false,
        snap: {
          radius: 20,
        },
        createEdge() {
          return graph.createEdge({
            attrs: {
              line: {
                stroke: '#1890ff',
                strokeWidth: 2,
                targetMarker: {
                  name: 'block',
                  width: 6,
                  height: 4,
                },
              },
            },
          });
        },
        validateConnection({ targetMagnet }) {
          return !!targetMagnet;
        },
      },
      highlighting: {
        magnetAdsorbed: {
          name: 'stroke',
          args: {
            attrs: {
              fill: '#5F95FF',
              stroke: '#5F95FF',
            },
          },
        },
      },
    });

    // 使用插件
    // 1. Scroller - 画布滚动功能
    graph.use(new Scroller({
      enabled: true,
      pannable: true,
      modifiers: ['shift', 'ctrl'],
    }));

    // 2. Selection - 框选功能
    graph.use(new Selection({
      enabled: true,
      multiple: true,
      rubberband: true,
      movable: true,
      showNodeSelectionBox: true,
      showEdgeSelectionBox: true,
    }));

    // 3. Snapline - 对齐线功能
    graph.use(new Snapline({
      enabled: true,
      sharp: true,
    }));

    // 4. Keyboard - 快捷键功能
    graph.use(new Keyboard({
      enabled: true,
      global: true,
    }));

    // 5. Clipboard - 复制粘贴功能
    graph.use(new Clipboard({
      enabled: true,
      useLocalStorage: true,
    }));

    // 6. History - 撤销重做功能
    graph.use(new History({
      enabled: true,
      beforeAddCommand: (_event, args) => {
        if (args && 'key' in args && args.key === 'tools') {
          return false;
        }
        return true;
      },
    }));

    // 7. MiniMap - 小地图功能将在单独的useEffect中初始化
    // 这里先不初始化MiniMap，避免容器引用问题

    // 定义节点数据
    const nodeGroups = [
      { name: 'basic', title: '基础控制', nodes: ['Start', 'End', 'Task'] },
      { name: 'trigger', title: '触发器', nodes: ['TimerTrigger', 'EventTrigger', 'WebhookTrigger'] },
      { name: 'action', title: '动作节点', nodes: ['HttpRequest', 'DataProcessor', 'NotificationSender'] },
      { name: 'control', title: '控制流', nodes: ['IfCondition', 'ForLoop', 'ParallelExecution'] },
      { name: 'data', title: '数据转换', nodes: ['DataMapper', 'DataFilter'] },
      { name: 'external', title: '外部服务', nodes: ['MySqlDatabase', 'NatsMessage', 'RestApiCall'] },
    ];

    // 计算每个分组的高度 (节点高度50px + 间距10px)
    const calculateGroupHeight = (nodeCount: number) => Math.max(nodeCount * 60 + 20, 80);

    // 创建侧边栏 - 使用动态配置
    const stencil = new Stencil({
      title: '组件库',
      target: graph,
      stencilGraphWidth: 180,
      stencilGraphHeight: 180,
      collapsable: true,
      groups: nodeGroups.map(group => ({
        title: `${group.title} (${group.nodes.length})`,
        name: group.name,
        collapsed: false,
        graphHeight: calculateGroupHeight(group.nodes.length),
      })),
      // 使用X6默认布局配置
      layoutOptions: {
        columns: 1,
        columnWidth: 160,
        rowHeight: 50,
        dx: 10,
        dy: 10,
        marginX: 10,
        marginY: 10,
        center: false,
        resizeToFit: false
      },
    });

    stencilContainerRef.current.appendChild(stencil.container);
    stencilRef.current = stencil;

    // 创建节点标签映射
    const nodeLabels: Record<string, string> = {
      'Start': '开始', 'End': '结束', 'Task': '任务',
      'TimerTrigger': '定时器', 'EventTrigger': '事件触发', 'WebhookTrigger': 'Webhook',
      'HttpRequest': 'HTTP请求', 'DataProcessor': '数据处理', 'NotificationSender': '通知发送',
      'IfCondition': '条件判断', 'ForLoop': '循环执行', 'ParallelExecution': '并行执行',
      'DataMapper': '数据映射', 'DataFilter': '数据过滤',
      'MySqlDatabase': 'MySQL数据库', 'NatsMessage': 'NATS消息', 'RestApiCall': 'REST API',
    };

    // 动态创建和加载节点
    nodeGroups.forEach(group => {
      const nodes = group.nodes.map(nodeType =>
        graph.createNode(createStencilNode(nodeType, nodeLabels[nodeType] || nodeType))
      );
      stencil.load(nodes, group.name);
    });

    // 添加一些示例节点到画布
    const startNode = graph.addNode(createNode('Start', '开始', 100, 100));
    const taskNode = graph.addNode(createNode('Task', '读数据', 300, 100));
    const decisionNode = graph.addNode(createNode('Decision', '逻辑回归', 500, 100));
    const actionNode1 = graph.addNode({ ...createNode('Task', '模型预测', 350, 250), data: { ...createNode('Task', '模型预测', 350, 250).data, type: 'database' } });
    const actionNode2 = graph.addNode({ ...createNode('Task', '读取参数', 650, 250), data: { ...createNode('Task', '读取参数', 650, 250).data, type: 'http' } });

    // 连接节点
    graph.addEdge({
      source: startNode,
      target: taskNode,
    });

    graph.addEdge({
      source: taskNode,
      target: decisionNode,
    });

    graph.addEdge({
      source: decisionNode,
      target: actionNode1,
    });

    graph.addEdge({
      source: decisionNode,
      target: actionNode2,
    });

    // 事件监听
    graph.on('history:change', () => {
      setCanUndo(graph.canUndo());
      setCanRedo(graph.canRedo());
    });

    // 键盘快捷键绑定
    graph.bindKey(['meta+z', 'ctrl+z'], () => {
      if (graph.canUndo()) {
        graph.undo();
      }
      return false;
    });

    graph.bindKey(['meta+shift+z', 'ctrl+shift+z'], () => {
      if (graph.canRedo()) {
        graph.redo();
      }
      return false;
    });

    graph.bindKey(['meta+c', 'ctrl+c'], () => {
      const cells = graph.getSelectedCells();
      if (cells.length) {
        graph.copy(cells);
      }
      return false;
    });

    graph.bindKey(['meta+v', 'ctrl+v'], () => {
      if (!graph.isClipboardEmpty()) {
        const cells = graph.paste({ offset: 32 });
        graph.cleanSelection();
        graph.select(cells);
      }
      return false;
    });

    graph.bindKey('delete', () => {
      const cells = graph.getSelectedCells();
      if (cells.length) {
        graph.removeCells(cells);
      }
    });

    graphRef.current = graph;

    // 添加一些测试节点来验证小地图显示
    const testNodes = [
      { id: 'test1', x: 100, y: 100, width: 120, height: 60, label: '测试节点1' },
      { id: 'test2', x: 300, y: 200, width: 120, height: 60, label: '测试节点2' },
      { id: 'test3', x: 500, y: 300, width: 120, height: 60, label: '测试节点3' },
    ];

    testNodes.forEach(node => {
      graph.addNode({
        id: node.id,
        x: node.x,
        y: node.y,
        width: node.width,
        height: node.height,
        shape: 'rect',
        attrs: {
          body: {
            stroke: '#1890ff',
            strokeWidth: 2,
            fill: '#f0f9ff',
            rx: 6,
            ry: 6,
          },
          text: {
            text: node.label,
            fontSize: 12,
            fill: '#262626',
          },
        },
      });
    });

    console.log('Graph initialized with test nodes for minimap testing');

    return () => {
      // 清理MiniMap
      if (minimapRef.current) {
        minimapRef.current = null;
      }
      // 清理Graph
      graph.dispose();
    };
  }, []);

  // 处理Stencil显示/隐藏状态变化
  useEffect(() => {
    if (stencilVisible && stencilRef.current && stencilContainerRef.current) {
      // 确保Stencil容器正确显示
      if (!stencilContainerRef.current.contains(stencilRef.current.container)) {
        stencilContainerRef.current.appendChild(stencilRef.current.container);
      }
    }
  }, [stencilVisible]);

  // 处理MiniMap的初始化和显示/隐藏
  useEffect(() => {
    if (!graphRef.current) return;

    if (minimapVisible && minimapContainerRef.current) {
      // 如果MiniMap还没有初始化，则创建它
      if (!minimapRef.current) {
        // 延迟初始化，确保容器布局完成
        setTimeout(() => {
          if (minimapContainerRef.current && graphRef.current && !minimapRef.current) {
            // 获取容器的实际尺寸
            const containerRect = minimapContainerRef.current.getBoundingClientRect();

            // 添加调试信息
            console.log('Initial - Container rect:', containerRect);

            const minimapWidth = Math.max(containerRect.width - 16, 100);
            const minimapHeight = Math.max(containerRect.height - 16, 100);

            console.log('Creating minimap with size:', { width: minimapWidth, height: minimapHeight });

            // 尝试最简单的配置
            const minimap = new MiniMap({
              container: minimapContainerRef.current,
              width: minimapWidth,
              height: minimapHeight,
              padding: 0,
              scalable: true,
              minScale: 0.01,
              maxScale: 16,
            });

            graphRef.current.use(minimap);
            minimapRef.current = minimap;
          }
        }, 100);
      } else {
        // 如果MiniMap已存在，确保容器正确挂载
        if (minimapContainerRef.current && !minimapContainerRef.current.contains(minimapRef.current.container)) {
          minimapContainerRef.current.appendChild(minimapRef.current.container);
        }
      }
    }
  }, [minimapVisible]);

  // 监听窗口大小变化，重新调整小地图尺寸
  useEffect(() => {
    const handleResize = () => {
      if (minimapRef.current && minimapContainerRef.current && graphRef.current) {
        // 获取容器的实际尺寸
        const containerRect = minimapContainerRef.current.getBoundingClientRect();

        // 添加调试信息
        console.log('Resize - Container rect:', containerRect);

        // 检查容器尺寸是否有效
        if (containerRect.width <= 0 || containerRect.height <= 0) {
          console.log('Container size invalid, skipping resize');
          return;
        }

        // 销毁旧的小地图
        graphRef.current.disposePlugins('minimap');
        minimapRef.current = null;

        // 重新创建小地图
        const minimapWidth = Math.max(containerRect.width - 16, 100);
        const minimapHeight = Math.max(containerRect.height - 16, 100);

        console.log('Resize - Creating minimap with size:', { width: minimapWidth, height: minimapHeight });

        // 尝试最简单的配置
        const minimap = new MiniMap({
          container: minimapContainerRef.current,
          width: minimapWidth,
          height: minimapHeight,
          padding: 0,
          scalable: true,
          minScale: 0.01,
          maxScale: 16,
        });

        graphRef.current.use(minimap);
        minimapRef.current = minimap;
      }
    };

    window.addEventListener('resize', handleResize);

    // 重新添加延迟执行机制，测试是否有用
    const timer = setTimeout(() => {
      console.log('Auto resize triggered after 200ms');
      handleResize();
    }, 200);

    return () => {
      window.removeEventListener('resize', handleResize);
      clearTimeout(timer);
    };
  }, [minimapVisible]);

  // 工具栏操作函数
  const handleUndo = () => {
    if (graphRef.current?.canUndo()) {
      graphRef.current.undo();
    }
  };

  const handleRedo = () => {
    if (graphRef.current?.canRedo()) {
      graphRef.current.redo();
    }
  };

  const handleCopy = () => {
    if (graphRef.current) {
      const cells = graphRef.current.getSelectedCells();
      if (cells.length) {
        graphRef.current.copy(cells);
        message.success('已复制到剪贴板');
      } else {
        message.warning('请先选择要复制的节点');
      }
    }
  };

  const handlePaste = () => {
    if (graphRef.current && !graphRef.current.isClipboardEmpty()) {
      const cells = graphRef.current.paste({ offset: 32 });
      graphRef.current.cleanSelection();
      graphRef.current.select(cells);
      message.success('已粘贴');
    } else {
      message.warning('剪贴板为空');
    }
  };

  const handleDelete = () => {
    if (graphRef.current) {
      const cells = graphRef.current.getSelectedCells();
      if (cells.length) {
        graphRef.current.removeCells(cells);
        message.success('已删除选中项');
      } else {
        message.warning('请先选择要删除的项');
      }
    }
  };

  const handleZoomIn = () => {
    if (graphRef.current) {
      graphRef.current.zoom(0.1);
    }
  };

  const handleZoomOut = () => {
    if (graphRef.current) {
      graphRef.current.zoom(-0.1);
    }
  };

  const handleZoomToFit = () => {
    if (graphRef.current) {
      graphRef.current.zoomToFit({ padding: 20 });
    }
  };

  const toggleStencil = () => {
    setStencilVisible(!stencilVisible);
  };

  const toggleMinimap = () => {
    setMinimapVisible(!minimapVisible);
  };

  return (
    <PageLayout
      title="工作流设计器"
      description="可视化设计和编辑工作流程"
      icon={<ApartmentOutlined />}
      actions={
        <Space>
          <Button
            icon={<SaveOutlined />}
            type="primary"
            onClick={() => message.success('保存成功')}
          >
            保存
          </Button>
          <Button
            icon={<PlayCircleOutlined />}
            onClick={() => message.info('执行功能开发中')}
          >
            执行
          </Button>
          <Button
            icon={<SettingOutlined />}
            onClick={() => message.info('设置功能开发中')}
          >
            设置
          </Button>
        </Space>
      }
    >
      {/* 🟠 工具栏容器 */}
      <div
        className="toolbar"
        style={{
          height: PAGE_LAYOUT_CONFIG.toolbarHeight,
          marginBottom: PAGE_LAYOUT_CONFIG.toolbarMargin,
          border: '2px solid #ff9800', // 橙色调试边框
          borderRadius: '6px',
          padding: '12px 16px',
          background: '#fff',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}
      >
        <div className="toolbar-left">
          <Space>
            <Button
              icon={<UndoOutlined />}
              onClick={handleUndo}
              disabled={!canUndo}
              title="撤销 (Ctrl+Z)"
            />
            <Button
              icon={<RedoOutlined />}
              onClick={handleRedo}
              disabled={!canRedo}
              title="重做 (Ctrl+Shift+Z)"
            />
            <Divider type="vertical" />
            <Button
              icon={<CopyOutlined />}
              onClick={handleCopy}
              title="复制 (Ctrl+C)"
            />
            <Button
              icon={<ScissorOutlined />}
              onClick={handlePaste}
              title="粘贴 (Ctrl+V)"
            />
            <Button
              icon={<DeleteOutlined />}
              onClick={handleDelete}
              title="删除 (Delete)"
            />
          </Space>
        </div>

        <div className="toolbar-right">
          <Space>
            <Button
              icon={<ZoomOutOutlined />}
              onClick={handleZoomOut}
              title="缩小"
            />
            <Button
              icon={<ZoomInOutlined />}
              onClick={handleZoomIn}
              title="放大"
            />
            <Button
              icon={<CompressOutlined />}
              onClick={handleZoomToFit}
              title="适应画布"
            />
            <Divider type="vertical" />
            <Button
              icon={<MenuOutlined />}
              onClick={toggleStencil}
              title={stencilVisible ? "隐藏组件库" : "显示组件库"}
              type={stencilVisible ? "default" : "primary"}
            />
            <Button
              icon={minimapVisible ? <EyeOutlined /> : <EyeInvisibleOutlined />}
              onClick={toggleMinimap}
              title={minimapVisible ? "隐藏小地图" : "显示小地图"}
              type={minimapVisible ? "default" : "primary"}
            />
          </Space>
        </div>
      </div>

      {/* 🔵 主要内容区域 */}
      <div
        className="workflow-designer-container"
        style={{
          height: PAGE_LAYOUT_CONFIG.designerHeight,
          border: '2px solid #2196f3', // 蓝色调试边框
          borderRadius: '6px',
          padding: 'var(--layout-element-spacing)',
          background: '#fff',
          overflow: 'hidden'
        }}
      >
        <div className="flex gap-4 h-full">
          {/* 左侧侧边栏 - X6 Stencil */}
          {stencilVisible && (
            <Card
              title="组件库"
              className={`${LAYOUT_CONFIG.leftPanel.width} ${LAYOUT_CONFIG.leftPanel.flexShrink}`}
              styles={{
                header: LAYOUT_CONFIG.cardHeaderStyle,
                body: { padding: 0, height: 'calc(100% - 40px)' }
              }}
            >
              <div ref={stencilContainerRef} className="h-full" />
            </Card>
          )}

          {/* 中间画布区域 - X6 Graph */}
          <Card
            className="flex-1"
            styles={{ body: { padding: 0, height: 'calc(100% - 40px)' } }}
          >
            <div ref={containerRef} className="w-full h-full" />
          </Card>

          {/* 右侧面板 - 重新布局 */}
          <div className={`${LAYOUT_CONFIG.rightPanel.width} ${LAYOUT_CONFIG.rightPanel.flexShrink} flex flex-col h-full`}>
            {/* 快捷键框 - 放在上方，固定高度 */}
            <Card
              title="快捷键"
              styles={{
                header: LAYOUT_CONFIG.cardHeaderStyle,
                body: LAYOUT_CONFIG.cardBodyStyle
              }}
              className={LAYOUT_CONFIG.rightPanel.spacing}
              style={{
                height: LAYOUT_CONFIG.rightPanel.shortcutCard.height,
                flexShrink: LAYOUT_CONFIG.rightPanel.shortcutCard.flexShrink
              }}
            >
              <div className="text-xs text-gray-600 space-y-1.5">
                <div className="flex justify-between">
                  <span className="font-mono text-gray-500">Ctrl+C</span>
                  <span>复制</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-mono text-gray-500">Ctrl+V</span>
                  <span>粘贴</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-mono text-gray-500">Ctrl+Z</span>
                  <span>撤销</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-mono text-gray-500">Delete</span>
                  <span>删除</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-mono text-gray-500">Shift+拖动</span>
                  <span>平移</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-mono text-gray-500">Ctrl+滚轮</span>
                  <span>缩放</span>
                </div>
              </div>
            </Card>

            {/* 小地图 - 放在下方，占据剩余空间 */}
            {minimapVisible && (
              <Card
                title="小地图"
                styles={{
                  header: LAYOUT_CONFIG.cardHeaderStyle,
                  body: LAYOUT_CONFIG.minimapCardBodyStyle
                }}
                style={{
                  height: LAYOUT_CONFIG.rightPanel.minimapCard.height,
                  minHeight: LAYOUT_CONFIG.rightPanel.minimapCard.minHeight,
                  border: '3px solid red' // 🔴 红色边框标示小地图Card
                }}
              >
                <div
                  ref={minimapContainerRef}
                  style={{
                    width: '100%',
                    height: '100%',
                    border: '3px solid blue', // 🔵 蓝色边框标示小地图容器
                    backgroundColor: 'black', // ⚫ 黑色背景标示小地图内部区域
                    boxSizing: 'border-box', // 确保边框不会超出容器
                    overflow: 'hidden' // 防止内容溢出
                  }}
                />
              </Card>
            )}
          </div>
        </div>
      </div>
    </PageLayout>
  );
};

export default WorkflowDesigner;
