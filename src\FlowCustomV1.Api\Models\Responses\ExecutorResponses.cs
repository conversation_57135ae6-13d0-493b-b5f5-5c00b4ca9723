using System.Text.Json.Serialization;

namespace FlowCustomV1.Api.Models.Responses;

/// <summary>
/// 执行响应模型
/// </summary>
public class ExecutionResponse
{
    /// <summary>
    /// 执行ID
    /// </summary>
    [JsonPropertyName("executionId")]
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 工作流ID
    /// </summary>
    [JsonPropertyName("workflowId")]
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 执行状态
    /// </summary>
    [JsonPropertyName("status")]
    public string Status { get; set; } = string.Empty;

    /// <summary>
    /// 执行器节点ID
    /// </summary>
    [JsonPropertyName("executorNodeId")]
    public string ExecutorNodeId { get; set; } = string.Empty;

    /// <summary>
    /// 开始时间
    /// </summary>
    [JsonPropertyName("startedAt")]
    public DateTime StartedAt { get; set; }

    /// <summary>
    /// 预估执行时间（毫秒）
    /// </summary>
    [JsonPropertyName("estimatedDurationMs")]
    public long EstimatedDurationMs { get; set; }

    /// <summary>
    /// 执行优先级
    /// </summary>
    [JsonPropertyName("priority")]
    public string Priority { get; set; } = "Normal";

    /// <summary>
    /// 执行标签
    /// </summary>
    [JsonPropertyName("tags")]
    public List<string> Tags { get; set; } = new();

    /// <summary>
    /// 执行元数据
    /// </summary>
    [JsonPropertyName("metadata")]
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// 执行状态响应模型
/// </summary>
public class ExecutionStatusResponse
{
    /// <summary>
    /// 执行ID
    /// </summary>
    [JsonPropertyName("executionId")]
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 工作流ID
    /// </summary>
    [JsonPropertyName("workflowId")]
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 当前状态
    /// </summary>
    [JsonPropertyName("currentState")]
    public string CurrentState { get; set; } = string.Empty;

    /// <summary>
    /// 执行进度（0-100）
    /// </summary>
    [JsonPropertyName("progress")]
    public double Progress { get; set; }

    /// <summary>
    /// 当前执行的节点ID
    /// </summary>
    [JsonPropertyName("currentNodeId")]
    public string? CurrentNodeId { get; set; }

    /// <summary>
    /// 已完成的节点数
    /// </summary>
    [JsonPropertyName("completedNodes")]
    public int CompletedNodes { get; set; }

    /// <summary>
    /// 总节点数
    /// </summary>
    [JsonPropertyName("totalNodes")]
    public int TotalNodes { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    [JsonPropertyName("startedAt")]
    public DateTime StartedAt { get; set; }

    /// <summary>
    /// 最后更新时间
    /// </summary>
    [JsonPropertyName("lastUpdated")]
    public DateTime LastUpdated { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    [JsonPropertyName("errorMessage")]
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 执行器节点ID
    /// </summary>
    [JsonPropertyName("executorNodeId")]
    public string ExecutorNodeId { get; set; } = string.Empty;
}

/// <summary>
/// 批量执行控制响应模型
/// </summary>
public class BatchExecutionControlResponse
{
    /// <summary>
    /// 总处理数
    /// </summary>
    [JsonPropertyName("totalProcessed")]
    public int TotalProcessed { get; set; }

    /// <summary>
    /// 成功数
    /// </summary>
    [JsonPropertyName("successCount")]
    public int SuccessCount { get; set; }

    /// <summary>
    /// 失败数
    /// </summary>
    [JsonPropertyName("failureCount")]
    public int FailureCount { get; set; }

    /// <summary>
    /// 详细结果
    /// </summary>
    [JsonPropertyName("results")]
    public List<ExecutionControlResult> Results { get; set; } = new();
}

/// <summary>
/// 执行控制结果
/// </summary>
public class ExecutionControlResult
{
    /// <summary>
    /// 执行ID
    /// </summary>
    [JsonPropertyName("executionId")]
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 是否成功
    /// </summary>
    [JsonPropertyName("success")]
    public bool Success { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    [JsonPropertyName("errorMessage")]
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 操作时间
    /// </summary>
    [JsonPropertyName("operationTime")]
    public DateTime OperationTime { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 执行容量响应模型
/// </summary>
public class ExecutionCapacityResponse
{
    /// <summary>
    /// 节点ID
    /// </summary>
    [JsonPropertyName("nodeId")]
    public string NodeId { get; set; } = string.Empty;

    /// <summary>
    /// 最大并发执行数
    /// </summary>
    [JsonPropertyName("maxConcurrentExecutions")]
    public int MaxConcurrentExecutions { get; set; }

    /// <summary>
    /// 当前运行的执行数
    /// </summary>
    [JsonPropertyName("currentExecutions")]
    public int CurrentExecutions { get; set; }

    /// <summary>
    /// 可用执行槽位
    /// </summary>
    [JsonPropertyName("availableSlots")]
    public int AvailableSlots { get; set; }

    /// <summary>
    /// CPU使用率（0-100）
    /// </summary>
    [JsonPropertyName("cpuUsagePercent")]
    public double CpuUsagePercent { get; set; }

    /// <summary>
    /// 内存使用率（0-100）
    /// </summary>
    [JsonPropertyName("memoryUsagePercent")]
    public double MemoryUsagePercent { get; set; }

    /// <summary>
    /// 磁盘使用率（0-100）
    /// </summary>
    [JsonPropertyName("diskUsagePercent")]
    public double DiskUsagePercent { get; set; }

    /// <summary>
    /// 网络使用率（0-100）
    /// </summary>
    [JsonPropertyName("networkUsagePercent")]
    public double NetworkUsagePercent { get; set; }

    /// <summary>
    /// 负载评分（0-100，越低越好）
    /// </summary>
    [JsonPropertyName("loadScore")]
    public double LoadScore { get; set; }

    /// <summary>
    /// 是否可以接受新执行
    /// </summary>
    [JsonPropertyName("canAcceptExecution")]
    public bool CanAcceptExecution { get; set; }

    /// <summary>
    /// 最后更新时间
    /// </summary>
    [JsonPropertyName("lastUpdated")]
    public DateTime LastUpdated { get; set; }
}

/// <summary>
/// 执行统计响应模型
/// </summary>
public class ExecutionStatisticsResponse
{
    /// <summary>
    /// 节点ID
    /// </summary>
    [JsonPropertyName("nodeId")]
    public string NodeId { get; set; } = string.Empty;

    /// <summary>
    /// 统计时间范围开始
    /// </summary>
    [JsonPropertyName("startTime")]
    public DateTime StartTime { get; set; }

    /// <summary>
    /// 统计时间范围结束
    /// </summary>
    [JsonPropertyName("endTime")]
    public DateTime EndTime { get; set; }

    /// <summary>
    /// 总执行数
    /// </summary>
    [JsonPropertyName("totalExecutions")]
    public long TotalExecutions { get; set; }

    /// <summary>
    /// 成功执行数
    /// </summary>
    [JsonPropertyName("successfulExecutions")]
    public long SuccessfulExecutions { get; set; }

    /// <summary>
    /// 失败执行数
    /// </summary>
    [JsonPropertyName("failedExecutions")]
    public long FailedExecutions { get; set; }

    /// <summary>
    /// 取消执行数
    /// </summary>
    [JsonPropertyName("cancelledExecutions")]
    public long CancelledExecutions { get; set; }

    /// <summary>
    /// 平均执行时间（毫秒）
    /// </summary>
    [JsonPropertyName("averageExecutionTimeMs")]
    public double AverageExecutionTimeMs { get; set; }

    /// <summary>
    /// 最短执行时间（毫秒）
    /// </summary>
    [JsonPropertyName("minExecutionTimeMs")]
    public long MinExecutionTimeMs { get; set; }

    /// <summary>
    /// 最长执行时间（毫秒）
    /// </summary>
    [JsonPropertyName("maxExecutionTimeMs")]
    public long MaxExecutionTimeMs { get; set; }

    /// <summary>
    /// 吞吐量（执行数/小时）
    /// </summary>
    [JsonPropertyName("throughputPerHour")]
    public double ThroughputPerHour { get; set; }

    /// <summary>
    /// 成功率（0-100）
    /// </summary>
    [JsonPropertyName("successRate")]
    public double SuccessRate { get; set; }

    /// <summary>
    /// 错误率（0-100）
    /// </summary>
    [JsonPropertyName("errorRate")]
    public double ErrorRate { get; set; }
}

/// <summary>
/// 节点负载信息响应模型
/// </summary>
public class NodeLoadInfoResponse
{
    /// <summary>
    /// 节点ID
    /// </summary>
    [JsonPropertyName("nodeId")]
    public string NodeId { get; set; } = string.Empty;

    /// <summary>
    /// 节点名称
    /// </summary>
    [JsonPropertyName("nodeName")]
    public string NodeName { get; set; } = string.Empty;

    /// <summary>
    /// 节点角色
    /// </summary>
    [JsonPropertyName("nodeRole")]
    public string NodeRole { get; set; } = string.Empty;

    /// <summary>
    /// CPU核心数
    /// </summary>
    [JsonPropertyName("cpuCores")]
    public int CpuCores { get; set; }

    /// <summary>
    /// 总内存（MB）
    /// </summary>
    [JsonPropertyName("totalMemoryMb")]
    public long TotalMemoryMb { get; set; }

    /// <summary>
    /// 可用内存（MB）
    /// </summary>
    [JsonPropertyName("availableMemoryMb")]
    public long AvailableMemoryMb { get; set; }

    /// <summary>
    /// 当前执行数
    /// </summary>
    [JsonPropertyName("currentExecutions")]
    public int CurrentExecutions { get; set; }

    /// <summary>
    /// 最大执行数
    /// </summary>
    [JsonPropertyName("maxExecutions")]
    public int MaxExecutions { get; set; }

    /// <summary>
    /// 节点健康状态
    /// </summary>
    [JsonPropertyName("healthStatus")]
    public string HealthStatus { get; set; } = string.Empty;

    /// <summary>
    /// 最后心跳时间
    /// </summary>
    [JsonPropertyName("lastHeartbeat")]
    public DateTime LastHeartbeat { get; set; }

    /// <summary>
    /// 节点标签
    /// </summary>
    [JsonPropertyName("tags")]
    public List<string> Tags { get; set; } = new();

    /// <summary>
    /// 节点能力
    /// </summary>
    [JsonPropertyName("capabilities")]
    public List<string> Capabilities { get; set; } = new();
}
