/**
 * 布局初始化工具
 * 
 * 在应用启动时自动应用布局配置
 */

import { 
  applyLayoutConfig, 
  loadLayoutConfig, 
  LAYOUT_CONFIG,
  getScreenType,
  getComputedSizes 
} from '../styles/layout-config.js';

/**
 * 初始化布局系统
 */
export const initLayout = () => {
  console.log('🚀 初始化布局系统...');
  
  // 1. 尝试从本地存储加载配置
  const loaded = loadLayoutConfig();
  if (!loaded) {
    console.log('📋 使用默认布局配置');
  }
  
  // 2. 应用配置到页面
  applyLayoutConfig();
  
  // 3. 监听窗口大小变化
  window.addEventListener('resize', handleResize);
  
  // 4. 输出当前配置信息
  logLayoutInfo();
  
  console.log('✅ 布局系统初始化完成');
};

/**
 * 处理窗口大小变化
 */
const handleResize = () => {
  // 重新应用配置以适应新的屏幕尺寸
  applyLayoutConfig();
  
  // 输出新的尺寸信息
  const sizes = getComputedSizes();
  console.log('📐 窗口大小变化:', sizes);
};

/**
 * 输出布局信息
 */
const logLayoutInfo = () => {
  const screenType = getScreenType();
  const sizes = getComputedSizes();
  
  console.log('📊 当前布局信息:');
  console.log('  屏幕类型:', screenType);
  console.log('  内容高度:', sizes.contentHeight + 'px');
  console.log('  表格高度:', sizes.tableHeight + 'px');
  console.log('  配置参数:', LAYOUT_CONFIG);
};

/**
 * 调试功能已移除
 * 如需调试布局，请使用浏览器开发者工具
 */
export const enableLayoutDebug = () => {
  console.log('🔍 调试模式已移除，请使用浏览器开发者工具');
};

export const disableLayoutDebug = () => {
  console.log('🔍 调试模式已移除，请使用浏览器开发者工具');
};

export const toggleLayoutDebug = () => {
  console.log('🔍 调试模式已移除，请使用浏览器开发者工具');
};

/**
 * 快速调整配置的工具函数
 */
export const quickAdjust = {
  // 调整表格高度
  tableHeight: (offset) => {
    const { updateLayoutConfig } = require('../styles/layout-config.js');
    updateLayoutConfig({ tableOffset: offset });
    console.log(`📏 表格偏移调整为: ${offset}px`);
  },
  
  // 调整间距
  spacing: (padding) => {
    const { updateLayoutConfig } = require('../styles/layout-config.js');
    updateLayoutConfig({ 
      contentPadding: padding,
      containerPadding: padding 
    });
    console.log(`📏 间距调整为: ${padding}px`);
  },
  
  // 调整工具栏边距
  toolbar: (margin) => {
    const { updateLayoutConfig } = require('../styles/layout-config.js');
    updateLayoutConfig({ toolbarMargin: margin });
    console.log(`📏 工具栏边距调整为: ${margin}px`);
  }
};

/**
 * 在控制台提供快捷命令
 */
export const setupConsoleCommands = () => {
  // 将工具函数挂载到全局，方便在控制台使用
  window.layoutDebug = {
    toggle: toggleLayoutDebug,
    enable: enableLayoutDebug,
    disable: disableLayoutDebug,
    info: logLayoutInfo,
    adjust: quickAdjust,
    config: LAYOUT_CONFIG
  };
  
  console.log('🛠️  控制台命令已就绪:');
  console.log('  layoutDebug.toggle() - 切换调试模式');
  console.log('  layoutDebug.info() - 查看布局信息');
  console.log('  layoutDebug.adjust.tableHeight(300) - 调整表格高度');
  console.log('  layoutDebug.adjust.spacing(12) - 调整间距');
  console.log('  layoutDebug.config - 查看当前配置');
};
