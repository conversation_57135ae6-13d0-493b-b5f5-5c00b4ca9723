namespace FlowCustomV1.Core.Interfaces.Configuration;

/// <summary>
/// 消息系统配置抽象接口
/// 定义消息系统的基本配置需求，不依赖具体的消息中间件实现
/// </summary>
public interface IMessagingConfiguration
{
    /// <summary>
    /// 连接名称
    /// </summary>
    string ConnectionName { get; }

    /// <summary>
    /// 连接超时时间（秒）
    /// </summary>
    int ConnectionTimeoutSeconds { get; }

    /// <summary>
    /// 重连间隔时间（秒）
    /// </summary>
    int ReconnectIntervalSeconds { get; }

    /// <summary>
    /// 最大重连次数
    /// </summary>
    int MaxReconnectAttempts { get; }

    /// <summary>
    /// 是否启用自动重连
    /// </summary>
    bool EnableAutoReconnect { get; }

    /// <summary>
    /// 是否启用详细日志
    /// </summary>
    bool EnableVerboseLogging { get; }
}

/// <summary>
/// 流式消息配置抽象接口
/// 定义流式消息系统的配置需求
/// </summary>
public interface IStreamingConfiguration
{
    /// <summary>
    /// 是否启用流式消息
    /// </summary>
    bool Enabled { get; }

    /// <summary>
    /// 域名
    /// </summary>
    string? Domain { get; }

    /// <summary>
    /// 默认流配置
    /// </summary>
    IStreamConfiguration DefaultStream { get; }
}

/// <summary>
/// 流配置抽象接口
/// </summary>
public interface IStreamConfiguration
{
    /// <summary>
    /// 流名称
    /// </summary>
    string Name { get; }

    /// <summary>
    /// 流主题列表
    /// </summary>
    IReadOnlyList<string> Subjects { get; }

    /// <summary>
    /// 存储类型
    /// </summary>
    string Storage { get; }

    /// <summary>
    /// 最大消息数
    /// </summary>
    long MaxMessages { get; }

    /// <summary>
    /// 最大字节数
    /// </summary>
    long MaxBytes { get; }

    /// <summary>
    /// 消息最大保留时间（秒）
    /// </summary>
    long MaxAgeSeconds { get; }

    /// <summary>
    /// 副本数
    /// </summary>
    int Replicas { get; }
}

/// <summary>
/// 连接池配置抽象接口
/// </summary>
public interface IConnectionPoolConfiguration
{
    /// <summary>
    /// 最小连接数
    /// </summary>
    int MinConnections { get; }

    /// <summary>
    /// 最大连接数
    /// </summary>
    int MaxConnections { get; }

    /// <summary>
    /// 连接空闲超时时间（秒）
    /// </summary>
    int IdleTimeoutSeconds { get; }

    /// <summary>
    /// 连接获取超时时间（秒）
    /// </summary>
    int AcquireTimeoutSeconds { get; }

    /// <summary>
    /// 是否启用连接池
    /// </summary>
    bool Enabled { get; }
}

/// <summary>
/// 序列化配置抽象接口
/// </summary>
public interface ISerializationConfiguration
{
    /// <summary>
    /// 序列化类型
    /// </summary>
    string Type { get; }

    /// <summary>
    /// 是否启用压缩
    /// </summary>
    bool EnableCompression { get; }

    /// <summary>
    /// 压缩类型
    /// </summary>
    string CompressionType { get; }
}
