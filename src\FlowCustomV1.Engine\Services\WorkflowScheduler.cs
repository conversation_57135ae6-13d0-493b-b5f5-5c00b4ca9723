using FlowCustomV1.Core.Interfaces;
using FlowCustomV1.Core.Models.Workflow;
using Microsoft.Extensions.Logging;

namespace FlowCustomV1.Engine.Services;

/// <summary>
/// 工作流调度器实现
/// 负责工作流的编排和调度逻辑（纯业务逻辑，不涉及技术实现）
/// </summary>
public class WorkflowScheduler : IWorkflowScheduler
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<WorkflowScheduler> _logger;

    /// <summary>
    /// 工作流执行开始事件
    /// </summary>
    public event EventHandler<WorkflowExecutionStartedEventArgs>? ExecutionStarted;

    /// <summary>
    /// 工作流执行完成事件
    /// </summary>
    public event EventHandler<WorkflowExecutionCompletedEventArgs>? ExecutionCompleted;

    /// <summary>
    /// 工作流执行失败事件
    /// </summary>
    public event EventHandler<WorkflowExecutionErrorEventArgs>? ExecutionFailed;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="serviceProvider">服务提供者</param>
    /// <param name="logger">日志记录器</param>
    public WorkflowScheduler(
        IServiceProvider serviceProvider,
        ILogger<WorkflowScheduler> logger)
    {
        _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        _logger.LogInformation("WorkflowScheduler initialized successfully");
    }

    /// <summary>
    /// 调度工作流执行
    /// </summary>
    /// <param name="workflowDefinition">工作流定义</param>
    /// <param name="inputData">输入数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>执行结果</returns>
    public Task<WorkflowExecutionResult> ScheduleWorkflowAsync(
        WorkflowDefinition workflowDefinition,
        Dictionary<string, object>? inputData = null,
        CancellationToken cancellationToken = default)
    {
        if (workflowDefinition == null)
            throw new ArgumentNullException(nameof(workflowDefinition));

        var executionId = Guid.NewGuid().ToString();
        _logger.LogInformation("Starting workflow scheduling: {WorkflowId} ({ExecutionId})", 
            workflowDefinition.WorkflowId, executionId);

        try
        {
            // 触发执行开始事件
            ExecutionStarted?.Invoke(this, new WorkflowExecutionStartedEventArgs
            {
                ExecutionId = executionId,
                WorkflowId = workflowDefinition.WorkflowId,
                StartedAt = DateTime.UtcNow,
                InputData = inputData ?? new Dictionary<string, object>()
            });

            // 在后台启动工作流执行，不阻塞当前线程
            _ = ExecuteWorkflowInternalAsync(workflowDefinition, inputData, executionId, cancellationToken);

            // 立即返回，表示工作流已成功启动
            return Task.FromResult(new WorkflowExecutionResult
            {
                ExecutionId = executionId,
                WorkflowId = workflowDefinition.WorkflowId,
                State = WorkflowExecutionState.Running,
                IsSuccess = true,
                StartedAt = DateTime.UtcNow,
                Message = "Workflow scheduling started successfully."
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Workflow scheduling failed: {WorkflowId} ({ExecutionId})", 
                workflowDefinition.WorkflowId, executionId);

            // 记录失败状态
            _logger.LogError(ex, "Workflow scheduling failed: {WorkflowId} ({ExecutionId})",
                workflowDefinition.WorkflowId, executionId);

            // 触发执行失败事件
            ExecutionFailed?.Invoke(this, new WorkflowExecutionErrorEventArgs
            {
                ExecutionId = executionId,
                WorkflowId = workflowDefinition.WorkflowId,
                Error = ex,
                ErrorAt = DateTime.UtcNow,
                ErrorContext = new Dictionary<string, object>
                {
                    ["Phase"] = "Scheduling",
                    ["WorkflowDefinition"] = workflowDefinition
                }
            });

            return Task.FromResult(new WorkflowExecutionResult
            {
                ExecutionId = executionId,
                WorkflowId = workflowDefinition.WorkflowId,
                State = WorkflowExecutionState.Failed,
                IsSuccess = false,
                StartedAt = DateTime.UtcNow,
                CompletedAt = DateTime.UtcNow,
                ErrorMessage = ex.Message,
                Exception = ex
            });
        }
    }

    /// <summary>
    /// 取消工作流执行
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>取消是否成功</returns>
    public Task<bool> CancelExecutionAsync(string executionId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Cancelling workflow execution: {ExecutionId}", executionId);
            // 简化实现：只记录取消请求
            return Task.FromResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to cancel workflow execution: {ExecutionId}", executionId);
            return Task.FromResult(false);
        }
    }

    /// <summary>
    /// 内部工作流执行方法
    /// </summary>
    private async Task ExecuteWorkflowInternalAsync(
        WorkflowDefinition workflowDefinition,
        Dictionary<string, object>? inputData,
        string executionId,
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Starting internal workflow execution: {ExecutionId}", executionId);

            // 简化实现：模拟工作流执行
            await Task.Delay(100, cancellationToken);

            // 触发执行完成事件
            ExecutionCompleted?.Invoke(this, new WorkflowExecutionCompletedEventArgs
            {
                ExecutionId = executionId,
                WorkflowId = workflowDefinition.WorkflowId,
                CompletedAt = DateTime.UtcNow,
                Duration = TimeSpan.FromMilliseconds(100),
                Result = new WorkflowExecutionResult
                {
                    ExecutionId = executionId,
                    WorkflowId = workflowDefinition.WorkflowId,
                    State = WorkflowExecutionState.Completed,
                    IsSuccess = true,
                    StartedAt = DateTime.UtcNow.AddMilliseconds(-100),
                    CompletedAt = DateTime.UtcNow
                }
            });

            _logger.LogInformation("Internal workflow execution completed: {ExecutionId}", executionId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Internal workflow execution failed: {ExecutionId}", executionId);

            // 触发执行失败事件
            ExecutionFailed?.Invoke(this, new WorkflowExecutionErrorEventArgs
            {
                ExecutionId = executionId,
                WorkflowId = workflowDefinition.WorkflowId,
                Error = ex,
                ErrorAt = DateTime.UtcNow,
                ErrorContext = new Dictionary<string, object>
                {
                    ["Phase"] = "Execution",
                    ["WorkflowDefinition"] = workflowDefinition
                }
            });
        }
    }


}
