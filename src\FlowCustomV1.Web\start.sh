#!/bin/bash

# FlowCustomV1 Web Frontend 启动脚本
# Bash 脚本用于 Linux/macOS 环境

echo "=== FlowCustomV1 Web Frontend 启动脚本 ==="
echo "版本: v0.0.1.11"
echo ""

# 检查 Node.js 版本
echo "检查 Node.js 环境..."
if ! command -v node &> /dev/null; then
    echo "错误: 未找到 Node.js，请先安装 Node.js >= 16.0.0"
    exit 1
fi

NODE_VERSION=$(node --version)
echo "Node.js 版本: $NODE_VERSION"

# 检查版本是否满足要求 (>= 16.0.0)
MAJOR_VERSION=$(echo $NODE_VERSION | sed 's/v//' | cut -d. -f1)
if [ "$MAJOR_VERSION" -lt 16 ]; then
    echo "错误: Node.js 版本过低，需要 >= 16.0.0"
    exit 1
fi

# 检查 npm 版本
if ! command -v npm &> /dev/null; then
    echo "错误: 未找到 npm"
    exit 1
fi

NPM_VERSION=$(npm --version)
echo "npm 版本: $NPM_VERSION"
echo ""

# 检查是否存在 node_modules
if [ ! -d "node_modules" ]; then
    echo "未找到 node_modules，开始安装依赖..."
    echo "这可能需要几分钟时间，请耐心等待..."
    
    npm install
    
    if [ $? -ne 0 ]; then
        echo "依赖安装失败！"
        exit 1
    fi
    
    echo "依赖安装完成！"
else
    echo "检查依赖更新..."
    npm install
fi

echo ""

# 检查后端 API 连接
echo "检查后端 API 连接..."
if curl -s --connect-timeout 5 http://localhost:5000/api/health > /dev/null; then
    echo "后端 API 连接正常"
else
    echo "警告: 无法连接到后端 API (http://localhost:5000)"
    echo "请确保后端服务已启动，或检查 vite.config.ts 中的代理配置"
fi

echo ""

# 启动开发服务器
echo "启动开发服务器..."
echo "访问地址: http://localhost:3000"
echo "按 Ctrl+C 停止服务器"
echo ""

npm run dev
