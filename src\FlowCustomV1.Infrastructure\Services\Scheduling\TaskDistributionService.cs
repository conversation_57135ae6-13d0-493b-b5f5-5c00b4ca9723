using System.Collections.Concurrent;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using FlowCustomV1.Core.Interfaces.Scheduling;
using FlowCustomV1.Core.Interfaces.Services;
using FlowCustomV1.Core.Interfaces.Messaging;
using FlowCustomV1.Core.Models.Cluster;
using FlowCustomV1.Core.Models.Scheduling;
using FlowCustomV1.Infrastructure.Configuration;
using NodeInfo = FlowCustomV1.Core.Models.Cluster.NodeInfo;

namespace FlowCustomV1.Infrastructure.Services.Scheduling;

/// <summary>
/// 智能任务分发服务实现
/// 提供基于多种策略的任务分发和负载均衡功能
/// </summary>
public partial class TaskDistributionService : ITaskDistributionService
{
    private readonly ILogger<TaskDistributionService> _logger;
    private readonly INodeDiscoveryService _nodeDiscoveryService;
    private readonly INatsMessageRouter _messageRouter;
    private readonly TaskDistributionConfiguration _config;
    private readonly Random _random = new();

    // 统计数据
    private readonly ConcurrentDictionary<string, NodeDistributionStatistics> _nodeStats = new();
    private readonly ConcurrentDictionary<TaskDistributionStrategy, StrategyStatistics> _strategyStats = new();
    private readonly ConcurrentQueue<TaskDistributionResult> _recentDistributions = new();
    private long _totalDistributions = 0;
    private long _successfulDistributions = 0;

    // 轮询计数器
    private readonly ConcurrentDictionary<string, int> _roundRobinCounters = new();

    public TaskDistributionService(
        ILogger<TaskDistributionService> logger,
        INodeDiscoveryService nodeDiscoveryService,
        INatsMessageRouter messageRouter,
        IOptions<TaskDistributionConfiguration> config)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _nodeDiscoveryService = nodeDiscoveryService ?? throw new ArgumentNullException(nameof(nodeDiscoveryService));
        _messageRouter = messageRouter ?? throw new ArgumentNullException(nameof(messageRouter));
        _config = config?.Value ?? throw new ArgumentNullException(nameof(config));
    }

    #region 事件

    public event EventHandler<TaskDistributedEventArgs>? TaskDistributed;
    public event EventHandler<TaskRedistributedEventArgs>? TaskRedistributed;
    public event EventHandler<LoadRebalancedEventArgs>? LoadRebalanced;

    #endregion

    #region 任务分发

    /// <inheritdoc />
    public async Task<TaskDistributionResult> DistributeTaskAsync(
        DistributedTask task,
        TaskDistributionStrategy strategy = TaskDistributionStrategy.SmartLoad,
        CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(task);

        var startTime = DateTime.UtcNow;
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        try
        {
            _logger.LogDebug("Starting task distribution for task {TaskId} using strategy {Strategy}", 
                task.TaskId, strategy);

            // 获取符合条件的候选节点
            var candidateNodes = await GetAvailableNodesAsync(task.Requirements, 
                _config.MaxCandidateNodes, cancellationToken);

            if (candidateNodes.Count == 0)
            {
                var errorMsg = $"No available nodes found for task {task.TaskId}";
                _logger.LogWarning(errorMsg);
                
                return new TaskDistributionResult
                {
                    TaskId = task.TaskId,
                    IsSuccess = false,
                    Strategy = strategy,
                    DistributedAt = startTime,
                    DistributionTimeMs = stopwatch.ElapsedMilliseconds,
                    CandidateNodeCount = 0,
                    ErrorMessage = errorMsg
                };
            }

            // 根据策略选择最佳节点
            var selectedNode = SelectNodeByStrategy(candidateNodes, strategy, task);
            if (selectedNode == null)
            {
                var errorMsg = $"Failed to select node for task {task.TaskId} using strategy {strategy}";
                _logger.LogWarning(errorMsg);
                
                return new TaskDistributionResult
                {
                    TaskId = task.TaskId,
                    IsSuccess = false,
                    Strategy = strategy,
                    DistributedAt = startTime,
                    DistributionTimeMs = stopwatch.ElapsedMilliseconds,
                    CandidateNodeCount = candidateNodes.Count,
                    ErrorMessage = errorMsg
                };
            }

            // 计算选择评分
            var selectionScore = CalculateNodeScore(selectedNode, task.Requirements, strategy);

            // 创建分发结果
            var result = new TaskDistributionResult
            {
                TaskId = task.TaskId,
                IsSuccess = true,
                AssignedNode = selectedNode,
                Strategy = strategy,
                DistributedAt = startTime,
                DistributionTimeMs = stopwatch.ElapsedMilliseconds,
                CandidateNodeCount = candidateNodes.Count,
                SelectionScore = selectionScore,
                Details = new Dictionary<string, object>
                {
                    ["CandidateNodeIds"] = candidateNodes.Select(n => n.NodeId).ToList(),
                    ["SelectionReason"] = GetSelectionReason(strategy, selectedNode),
                    ["TaskPriority"] = task.Priority,
                    ["TaskType"] = task.TaskType
                }
            };

            // 更新统计信息
            UpdateDistributionStatistics(result, selectedNode);

            // 触发事件
            OnTaskDistributed(new TaskDistributedEventArgs
            {
                Task = task,
                Result = result,
                AssignedNode = selectedNode,
                Strategy = strategy,
                DistributedAt = startTime,
                CandidateNodeCount = candidateNodes.Count,
                DistributionTimeMs = stopwatch.ElapsedMilliseconds,
                SelectionScore = selectionScore
            });

            _logger.LogInformation("Task {TaskId} successfully distributed to node {NodeId} using strategy {Strategy} " +
                                 "in {ElapsedMs}ms (score: {Score:F2})",
                task.TaskId, selectedNode.NodeId, strategy, stopwatch.ElapsedMilliseconds, selectionScore);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to distribute task {TaskId} using strategy {Strategy}", 
                task.TaskId, strategy);

            return new TaskDistributionResult
            {
                TaskId = task.TaskId,
                IsSuccess = false,
                Strategy = strategy,
                DistributedAt = startTime,
                DistributionTimeMs = stopwatch.ElapsedMilliseconds,
                ErrorMessage = ex.Message
            };
        }
    }

    /// <inheritdoc />
    public async Task<BatchTaskDistributionResult> DistributeTasksAsync(
        IReadOnlyList<DistributedTask> tasks,
        TaskDistributionStrategy strategy = TaskDistributionStrategy.SmartLoad,
        CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(tasks);

        var startTime = DateTime.UtcNow;
        var result = new BatchTaskDistributionResult
        {
            TotalTasks = tasks.Count,
            StartTime = startTime
        };

        try
        {
            _logger.LogInformation("Starting batch distribution of {TaskCount} tasks using strategy {Strategy}",
                tasks.Count, strategy);

            // 并行分发任务（受限于配置的最大并发数）
            var semaphore = new SemaphoreSlim(_config.MaxConcurrentDistributions);
            var distributionTasks = tasks.Select(async task =>
            {
                await semaphore.WaitAsync(cancellationToken);
                try
                {
                    return await DistributeTaskAsync(task, strategy, cancellationToken);
                }
                finally
                {
                    semaphore.Release();
                }
            });

            var distributionResults = await Task.WhenAll(distributionTasks);

            // 汇总结果
            result.TaskResults.AddRange(distributionResults);
            result.SuccessfulTasks = distributionResults.Count(r => r.IsSuccess);
            result.FailedTasks = distributionResults.Count(r => !r.IsSuccess);
            result.EndTime = DateTime.UtcNow;

            // 统计节点分发情况
            foreach (var taskResult in distributionResults.Where(r => r.IsSuccess && r.AssignedNode != null))
            {
                var nodeId = taskResult.AssignedNode!.NodeId;
                result.NodeDistributionStats[nodeId] = result.NodeDistributionStats.GetValueOrDefault(nodeId, 0) + 1;
            }

            // 收集错误信息
            result.ErrorSummary.AddRange(distributionResults
                .Where(r => !r.IsSuccess && !string.IsNullOrEmpty(r.ErrorMessage))
                .Select(r => r.ErrorMessage!)
                .Distinct());

            _logger.LogInformation("Batch distribution completed: {SuccessfulTasks}/{TotalTasks} tasks distributed " +
                                 "successfully in {ElapsedMs}ms (success rate: {SuccessRate:P2})",
                result.SuccessfulTasks, result.TotalTasks, result.TotalTimeMs, result.SuccessRate);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to distribute batch of {TaskCount} tasks", tasks.Count);
            
            result.EndTime = DateTime.UtcNow;
            result.ErrorSummary.Add(ex.Message);
            return result;
        }
    }

    /// <inheritdoc />
    public async Task<TaskDistributionResult> RedistributeTaskAsync(
        DistributedTask failedTask,
        IReadOnlyList<string>? excludeNodes = null,
        CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(failedTask);

        var startTime = DateTime.UtcNow;
        
        try
        {
            _logger.LogInformation("Redistributing failed task {TaskId} (retry {RetryCount}/{MaxRetries})",
                failedTask.TaskId, failedTask.RetryCount, failedTask.MaxRetries);

            // 检查是否超过最大重试次数
            if (failedTask.RetryCount >= failedTask.MaxRetries)
            {
                var errorMsg = $"Task {failedTask.TaskId} exceeded maximum retry count ({failedTask.MaxRetries})";
                _logger.LogWarning(errorMsg);
                
                return new TaskDistributionResult
                {
                    TaskId = failedTask.TaskId,
                    IsSuccess = false,
                    Strategy = TaskDistributionStrategy.SmartLoad,
                    DistributedAt = startTime,
                    ErrorMessage = errorMsg
                };
            }

            // 增加重试次数
            failedTask.RetryCount++;

            // 修改任务要求以排除失败的节点
            var modifiedRequirements = failedTask.Requirements;
            if (excludeNodes?.Count > 0)
            {
                // 添加反亲和性规则排除指定节点
                modifiedRequirements.AntiAffinityRules.Add(new AntiAffinityRule
                {
                    Type = AffinityType.NodeAntiAffinity,
                    LabelSelector = excludeNodes.ToDictionary(nodeId => "NodeId", nodeId => nodeId),
                    IsRequired = true
                });
            }

            // 使用智能负载策略重新分发
            var result = await DistributeTaskAsync(failedTask, TaskDistributionStrategy.SmartLoad, cancellationToken);

            // 触发重新分发事件
            if (result.IsSuccess)
            {
                OnTaskRedistributed(new TaskRedistributedEventArgs
                {
                    OriginalTask = failedTask,
                    Result = result,
                    NewAssignedNode = result.AssignedNode,
                    RedistributionReason = "Task execution failed",
                    RedistributedAt = startTime,
                    RetryCount = failedTask.RetryCount,
                    ExcludedNodes = excludeNodes?.ToList() ?? new List<string>(),
                    RedistributionTimeMs = result.DistributionTimeMs,
                    IsLastRetry = failedTask.RetryCount >= failedTask.MaxRetries
                });
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to redistribute task {TaskId}", failedTask.TaskId);
            
            return new TaskDistributionResult
            {
                TaskId = failedTask.TaskId,
                IsSuccess = false,
                Strategy = TaskDistributionStrategy.SmartLoad,
                DistributedAt = startTime,
                ErrorMessage = ex.Message
            };
        }
    }

    #endregion

    #region 节点选择

    /// <inheritdoc />
    public async Task<NodeInfo?> SelectBestNodeAsync(
        TaskRequirements taskRequirements,
        NodeSelectionStrategy strategy = NodeSelectionStrategy.SmartSelection,
        CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(taskRequirements);

        try
        {
            var candidateNodes = await GetAvailableNodesAsync(taskRequirements,
                _config.MaxCandidateNodes, cancellationToken);

            if (candidateNodes.Count == 0)
            {
                _logger.LogWarning("No candidate nodes found for task requirements");
                return null;
            }

            return SelectNodeBySelectionStrategy(candidateNodes, strategy, taskRequirements);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to select best node using strategy {Strategy}", strategy);
            return null;
        }
    }

    /// <inheritdoc />
    public async Task<IReadOnlyList<NodeInfo>> GetAvailableNodesAsync(
        TaskRequirements taskRequirements,
        int maxNodes = 10,
        CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(taskRequirements);

        try
        {
            // 获取所有活跃节点
            var allNodes = await _nodeDiscoveryService.DiscoverAllNodesAsync(cancellationToken);

            // 过滤符合要求的节点
            var eligibleNodes = allNodes
                .Where(node => IsNodeEligible(node, taskRequirements))
                .OrderByDescending(node => CalculateNodeScore(node, taskRequirements, TaskDistributionStrategy.SmartLoad))
                .Take(maxNodes)
                .ToList();

            _logger.LogDebug("Found {EligibleCount} eligible nodes out of {TotalCount} total nodes",
                eligibleNodes.Count, allNodes.Count);

            return eligibleNodes;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get available nodes");
            return new List<NodeInfo>();
        }
    }

    #endregion

    #region 负载均衡

    /// <inheritdoc />
    public async Task<LoadBalancingStatus> GetLoadBalancingStatusAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var allNodes = await _nodeDiscoveryService.DiscoverAllNodesAsync(cancellationToken);
            var activeNodes = allNodes.Where(n => n.Status.IsOnline()).ToList();

            if (activeNodes.Count == 0)
            {
                return new LoadBalancingStatus
                {
                    TotalNodes = allNodes.Count,
                    ActiveNodes = 0,
                    NeedsRebalancing = false,
                    Timestamp = DateTime.UtcNow
                };
            }

            // 计算负载统计
            var loadScores = activeNodes.Select(n => n.Load.LoadScore).ToList();
            var averageLoad = loadScores.Average();
            var loadStdDev = CalculateStandardDeviation(loadScores, averageLoad);
            var balanceScore = CalculateBalanceScore(loadScores);

            // 创建节点负载摘要
            var nodeLoadSummaries = activeNodes.Select(node => new NodeLoadSummary
            {
                NodeId = node.NodeId,
                NodeName = node.NodeName,
                NodeRole = node.Mode.ToString(),
                LoadScore = node.Load.LoadScore,
                CpuUsage = node.Load.CpuUsagePercentage,
                MemoryUsage = node.Load.MemoryUsagePercentage,
                ActiveTasks = node.Load.ActiveTaskCount,
                MaxTasks = node.Load.MaxTaskCapacity,
                HealthStatus = node.Health.IsHealthy ? "Healthy" : "Unhealthy",
                LastUpdated = node.Timestamps.LastActiveAt
            }).OrderByDescending(n => n.LoadScore).ToList();

            // 生成重新平衡建议
            var recommendations = GenerateRebalancingRecommendations(nodeLoadSummaries, balanceScore);

            return new LoadBalancingStatus
            {
                TotalNodes = allNodes.Count,
                ActiveNodes = activeNodes.Count,
                AverageLoad = averageLoad,
                LoadStandardDeviation = loadStdDev,
                BalanceScore = balanceScore,
                HighestLoadNode = nodeLoadSummaries.FirstOrDefault(),
                LowestLoadNode = nodeLoadSummaries.LastOrDefault(),
                NodeLoadDistribution = nodeLoadSummaries,
                NeedsRebalancing = balanceScore < _config.MinBalanceScore,
                RebalancingRecommendations = recommendations,
                Timestamp = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get load balancing status");
            throw;
        }
    }

    /// <inheritdoc />
    public async Task<LoadRebalancingResult> RebalanceLoadAsync(
        IReadOnlyList<string>? targetNodes = null,
        CancellationToken cancellationToken = default)
    {
        var startTime = DateTime.UtcNow;
        var result = new LoadRebalancingResult
        {
            StartTime = startTime
        };

        try
        {
            _logger.LogInformation("Starting load rebalancing for {NodeCount} target nodes",
                targetNodes?.Count ?? 0);

            // 获取当前负载状态
            var beforeStatus = await GetLoadBalancingStatusAsync(cancellationToken);
            result.BeforeStatus = beforeStatus;

            if (!beforeStatus.NeedsRebalancing)
            {
                _logger.LogInformation("Load balancing not needed (balance score: {BalanceScore:F2})",
                    beforeStatus.BalanceScore);

                result.IsSuccess = true;
                result.EndTime = DateTime.UtcNow;
                result.ImprovementScore = 0;
                return result;
            }

            // 执行重新平衡操作
            var operations = new List<RebalancingOperation>();

            foreach (var recommendation in beforeStatus.RebalancingRecommendations
                .Where(r => targetNodes == null || targetNodes.Contains(r.SourceNodeId) || targetNodes.Contains(r.TargetNodeId))
                .OrderByDescending(r => r.Priority)
                .Take(_config.MaxRebalancingOperations))
            {
                var operation = await ExecuteRebalancingOperation(recommendation, cancellationToken);
                operations.Add(operation);
            }

            result.Operations = operations;

            // 获取重新平衡后的状态
            var afterStatus = await GetLoadBalancingStatusAsync(cancellationToken);
            result.AfterStatus = afterStatus;

            // 计算改善程度
            result.ImprovementScore = CalculateImprovementScore(beforeStatus, afterStatus);
            result.IsSuccess = operations.Any(o => o.Status == OperationStatus.Completed);
            result.EndTime = DateTime.UtcNow;

            // 收集错误和警告
            result.Errors.AddRange(operations.Where(o => o.Status == OperationStatus.Failed)
                .Select(o => o.ErrorMessage ?? "Unknown error"));

            _logger.LogInformation("Load rebalancing completed: {SuccessfulOps}/{TotalOps} operations successful, " +
                                 "improvement score: {ImprovementScore:F2}",
                operations.Count(o => o.Status == OperationStatus.Completed),
                operations.Count, result.ImprovementScore);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to rebalance load");

            result.IsSuccess = false;
            result.EndTime = DateTime.UtcNow;
            result.Errors.Add(ex.Message);
            return result;
        }
    }

    #endregion

    #region 统计和监控

    /// <inheritdoc />
    public Task<TaskDistributionStatistics> GetDistributionStatisticsAsync(
        TimeSpan? timeRange = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var endTime = DateTime.UtcNow;
            var startTime = timeRange.HasValue ? endTime - timeRange.Value : endTime.AddDays(-1);

            // 获取时间范围内的分发记录
            var recentDistributions = _recentDistributions.ToList()
                .Where(r => r.DistributedAt >= startTime && r.DistributedAt <= endTime)
                .ToList();

            var statistics = new TaskDistributionStatistics
            {
                StartTime = startTime,
                EndTime = endTime,
                TotalDistributedTasks = recentDistributions.Count,
                SuccessfulDistributions = recentDistributions.Count(r => r.IsSuccess),
                FailedDistributions = recentDistributions.Count(r => !r.IsSuccess)
            };

            if (recentDistributions.Count > 0)
            {
                statistics.AverageDistributionTimeMs = recentDistributions.Average(r => r.DistributionTimeMs);
                statistics.MaxDistributionTimeMs = recentDistributions.Max(r => r.DistributionTimeMs);
                statistics.MinDistributionTimeMs = recentDistributions.Min(r => r.DistributionTimeMs);

                // 按策略统计
                foreach (var strategyGroup in recentDistributions.GroupBy(r => r.Strategy))
                {
                    var strategyDistributions = strategyGroup.ToList();
                    statistics.StrategyStats[strategyGroup.Key] = new StrategyStatistics
                    {
                        UsageCount = strategyDistributions.Count,
                        SuccessCount = strategyDistributions.Count(r => r.IsSuccess),
                        FailureCount = strategyDistributions.Count(r => !r.IsSuccess),
                        AverageTimeMs = strategyDistributions.Average(r => r.DistributionTimeMs),
                        AverageSelectionScore = strategyDistributions.Average(r => r.SelectionScore)
                    };
                }

                // 按节点统计
                foreach (var nodeGroup in recentDistributions.Where(r => r.AssignedNode != null).GroupBy(r => r.AssignedNode!.NodeId))
                {
                    var nodeDistributions = nodeGroup.ToList();
                    statistics.NodeStats[nodeGroup.Key] = new NodeDistributionStatistics
                    {
                        NodeId = nodeGroup.Key,
                        NodeName = nodeGroup.First().AssignedNode!.NodeName,
                        AssignedTasks = nodeDistributions.Count,
                        CompletedTasks = nodeDistributions.Count(r => r.IsSuccess),
                        FailedTasks = nodeDistributions.Count(r => !r.IsSuccess),
                        AverageExecutionTimeMs = nodeDistributions.Average(r => r.DistributionTimeMs),
                        SelectionCount = nodeDistributions.Count,
                        TotalSelectionScore = nodeDistributions.Sum(r => r.SelectionScore)
                    };
                }
            }

            return Task.FromResult(statistics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get distribution statistics");
            throw;
        }
    }

    /// <inheritdoc />
    public async Task<IReadOnlyList<NodePerformanceMetrics>> GetNodePerformanceMetricsAsync(
        string? nodeId = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var allNodes = await _nodeDiscoveryService.DiscoverAllNodesAsync(cancellationToken);
            var targetNodes = string.IsNullOrEmpty(nodeId)
                ? allNodes
                : allNodes.Where(n => n.NodeId == nodeId).ToList();

            var metrics = new List<NodePerformanceMetrics>();

            foreach (var node in targetNodes)
            {
                var nodeMetrics = new NodePerformanceMetrics
                {
                    NodeId = node.NodeId,
                    NodeName = node.NodeName,
                    NodeRole = node.Mode.ToString(),
                    Timestamp = DateTime.UtcNow,
                    CpuMetrics = new CpuMetrics
                    {
                        UsagePercent = node.Load.CpuUsagePercentage,
                        CoreCount = node.Capabilities.CpuCores
                    },
                    MemoryMetrics = new MemoryMetrics
                    {
                        UsagePercent = node.Load.MemoryUsagePercentage,
                        TotalMB = node.Capabilities.MemoryMb,
                        UsedMB = (long)(node.Capabilities.MemoryMb * node.Load.MemoryUsagePercentage / 100)
                    },
                    TaskMetrics = new TaskExecutionMetrics
                    {
                        ActiveTasks = node.Load.ActiveTaskCount,
                        MaxTasks = node.Load.MaxTaskCapacity,
                        QueuedTasks = node.Load.QueuedTaskCount,
                        AverageExecutionTimeMs = node.Load.AverageResponseTimeMs
                    },
                    HealthStatus = node.Health.IsHealthy ? "Healthy" : "Unhealthy",
                    PerformanceScore = CalculatePerformanceScore(node),
                    AvailabilityScore = CalculateAvailabilityScore(node),
                    ReliabilityScore = CalculateReliabilityScore(node)
                };

                nodeMetrics.OverallScore = (nodeMetrics.PerformanceScore + nodeMetrics.AvailabilityScore + nodeMetrics.ReliabilityScore) / 3;
                metrics.Add(nodeMetrics);
            }

            return metrics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get node performance metrics for node {NodeId}", nodeId);
            throw;
        }
    }

    #endregion

    #region 私有辅助方法

    /// <summary>
    /// 根据策略选择节点
    /// </summary>
    private NodeInfo? SelectNodeByStrategy(IReadOnlyList<NodeInfo> nodes, TaskDistributionStrategy strategy, DistributedTask task)
    {
        if (nodes.Count == 0) return null;

        return strategy switch
        {
            TaskDistributionStrategy.SmartLoad => SelectSmartLoad(nodes, task.Requirements),
            TaskDistributionStrategy.LeastLoad => SelectLeastLoad(nodes),
            TaskDistributionStrategy.FastestResponse => SelectFastestResponse(nodes),
            TaskDistributionStrategy.RoundRobin => SelectRoundRobin(nodes),
            TaskDistributionStrategy.Random => SelectRandom(nodes),
            TaskDistributionStrategy.CapabilityBased => SelectCapabilityBased(nodes, task.Requirements),
            TaskDistributionStrategy.GeographyBased => SelectGeographyBased(nodes, task.Requirements),
            TaskDistributionStrategy.WeightedRoundRobin => SelectWeightedRoundRobin(nodes),
            _ => nodes.First()
        };
    }

    /// <summary>
    /// 根据选择策略选择节点
    /// </summary>
    private NodeInfo? SelectNodeBySelectionStrategy(IReadOnlyList<NodeInfo> nodes, NodeSelectionStrategy strategy, TaskRequirements requirements)
    {
        if (nodes.Count == 0) return null;

        return strategy switch
        {
            NodeSelectionStrategy.SmartSelection => SelectSmartLoad(nodes, requirements),
            NodeSelectionStrategy.PerformanceBased => SelectPerformanceBased(nodes),
            NodeSelectionStrategy.AvailabilityBased => SelectAvailabilityBased(nodes),
            NodeSelectionStrategy.CostBased => SelectCostBased(nodes),
            NodeSelectionStrategy.LocationBased => SelectGeographyBased(nodes, requirements),
            _ => nodes.First()
        };
    }

    #endregion
}