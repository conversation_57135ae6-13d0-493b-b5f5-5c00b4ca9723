using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using FlowCustomV1.Core.Interfaces;
using FlowCustomV1.Core.Models.Workflow;
using FlowCustomV1.Engine.ErrorHandling;

namespace FlowCustomV1.Engine.Executors;

/// <summary>
/// 基础节点执行器抽象类
/// 提供节点执行的通用逻辑和生命周期管理
/// </summary>
public abstract class BaseNodeExecutor : INodeExecutor
{
    /// <summary>
    /// 日志记录器
    /// </summary>
    protected readonly ILogger Logger;

    /// <summary>
    /// 服务提供者
    /// </summary>
    protected readonly IServiceProvider ServiceProvider;

    /// <summary>
    /// 错误处理器
    /// </summary>
    protected readonly IErrorHandler ErrorHandler;

    /// <summary>
    /// 节点类型
    /// </summary>
    public abstract string NodeType { get; }

    /// <summary>
    /// 节点显示名称
    /// </summary>
    public virtual string DisplayName => NodeType;

    /// <summary>
    /// 节点描述
    /// </summary>
    public virtual string Description => $"{NodeType} node executor";

    /// <summary>
    /// 节点版本
    /// </summary>
    public virtual string Version => "1.0.0";

    /// <summary>
    /// 是否支持异步执行
    /// </summary>
    public virtual bool SupportsAsync => true;

    /// <summary>
    /// 是否有状态节点
    /// </summary>
    public virtual bool IsStateful => false;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="serviceProvider">服务提供者</param>
    /// <param name="logger">日志记录器</param>
    protected BaseNodeExecutor(IServiceProvider serviceProvider, ILogger logger)
    {
        ServiceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
        Logger = logger ?? throw new ArgumentNullException(nameof(logger));
        ErrorHandler = serviceProvider.GetRequiredService<IErrorHandler>();
    }

    /// <summary>
    /// 执行节点
    /// </summary>
    /// <param name="context">节点执行上下文</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>执行结果</returns>
    public async Task<NodeExecutionResult> ExecuteAsync(NodeExecutionContext context, CancellationToken cancellationToken = default)
    {
        if (context == null)
            throw new ArgumentNullException(nameof(context));

        // 使用标准的NodeExecutionContext

        var result = new NodeExecutionResult
        {
            NodeId = context.NodeId,
            ExecutionId = context.ExecutionId,
            StartedAt = DateTime.UtcNow
        };

        try
        {
            Logger.LogInformation("Starting execution of node {NodeId} (Type: {NodeType})", context.NodeId, NodeType);
            
            // 前置验证
            await ValidatePreConditionsAsync(context, cancellationToken);

            // 更新状态为运行中
            context.UpdateState(NodeExecutionState.Running, "Node execution started");
            context.UpdateProgress(0, "Initializing");

            // 执行前置处理
            await PreExecuteAsync(context, cancellationToken);
            context.UpdateProgress(10, "Pre-execution completed");

            // 执行核心逻辑
            context.UpdateProgress(20, "Executing core logic");
            var coreResult = await ExecuteInternalAsync(context, cancellationToken);
            
            // 合并核心执行结果
            result.State = coreResult.State;
            result.IsSuccess = coreResult.IsSuccess;
            result.OutputData = coreResult.OutputData;
            result.ErrorMessage = coreResult.ErrorMessage;
            result.Exception = coreResult.Exception;
            result.Metadata = coreResult.Metadata;

            if (result.IsSuccess)
            {
                context.UpdateProgress(80, "Core execution completed");

                // 执行后置处理
                await PostExecuteAsync(context, result, cancellationToken);
                context.UpdateProgress(90, "Post-execution completed");

                // 验证后置条件
                await ValidatePostConditionsAsync(context, result, cancellationToken);
                context.UpdateProgress(100, "Execution completed successfully");

                context.UpdateState(NodeExecutionState.Completed, "Node execution completed successfully");
                Logger.LogInformation("Node {NodeId} executed successfully", context.NodeId);
            }
            else
            {
                context.UpdateState(NodeExecutionState.Failed, $"Node execution failed: {result.ErrorMessage}");
                Logger.LogWarning("Node {NodeId} execution failed: {ErrorMessage}", context.NodeId, result.ErrorMessage);
            }
        }
        catch (OperationCanceledException)
        {
            result.State = NodeExecutionState.Cancelled;
            result.IsSuccess = false;
            result.ErrorMessage = "Node execution was cancelled";
            context.UpdateState(NodeExecutionState.Cancelled, "Node execution cancelled");
            Logger.LogInformation("Node {NodeId} execution was cancelled", context.NodeId);
        }
        catch (Exception ex)
        {
            result.State = NodeExecutionState.Failed;
            result.IsSuccess = false;
            result.Exception = ex;
            result.ErrorMessage = ex.Message;
            
            context.UpdateState(NodeExecutionState.Failed, $"Node execution failed with exception: {ex.Message}");
            
            Logger.LogError(ex, "Node {NodeId} execution failed with exception", context.NodeId);

            // 尝试错误处理
            try
            {
                await HandleExecutionErrorAsync(context, ex, cancellationToken);
            }
            catch (Exception errorHandlingEx)
            {
                Logger.LogError(errorHandlingEx, "Error handling failed for node {NodeId}", context.NodeId);
            }
        }
        finally
        {
            result.CompletedAt = DateTime.UtcNow;
            
            // 执行清理
            try
            {
                await CleanupAsync(context, result, cancellationToken);
            }
            catch (Exception cleanupEx)
            {
                Logger.LogError(cleanupEx, "Cleanup failed for node {NodeId}", context.NodeId);
            }
        }

        return result;
    }

    /// <summary>
    /// 验证前置条件
    /// </summary>
    /// <param name="context">节点执行上下文</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证任务</returns>
    protected virtual async Task ValidatePreConditionsAsync(NodeExecutionContext context, CancellationToken cancellationToken)
    {
        // 基础验证
        if (string.IsNullOrEmpty(context.NodeId))
            throw new InvalidOperationException("NodeId cannot be null or empty");
        
        if (string.IsNullOrEmpty(context.ExecutionId))
            throw new InvalidOperationException("ExecutionId cannot be null or empty");
        
        if (context.Configuration == null)
            throw new InvalidOperationException("Node configuration cannot be null");

        context.AddLog("Pre-conditions validated successfully");
        await Task.CompletedTask;
    }

    /// <summary>
    /// 前置执行处理
    /// </summary>
    /// <param name="context">节点执行上下文</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理任务</returns>
    protected virtual async Task PreExecuteAsync(NodeExecutionContext context, CancellationToken cancellationToken)
    {
        // 记录执行开始
        context.AddLog($"Pre-execution started for node type: {NodeType}");
        
        // 初始化性能指标
        context.RecordMetric("PreExecuteStartTime", DateTime.UtcNow.Ticks);
        
        await Task.CompletedTask;
    }

    /// <summary>
    /// 核心执行逻辑（子类必须实现）
    /// </summary>
    /// <param name="context">节点执行上下文</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>执行结果</returns>
    protected abstract Task<NodeExecutionResult> ExecuteInternalAsync(
        NodeExecutionContext context,
        CancellationToken cancellationToken);

    /// <summary>
    /// 后置执行处理
    /// </summary>
    /// <param name="context">节点执行上下文</param>
    /// <param name="result">执行结果</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理任务</returns>
    protected virtual async Task PostExecuteAsync(
        NodeExecutionContext context,
        NodeExecutionResult result,
        CancellationToken cancellationToken)
    {
        // 记录执行完成
        context.AddLog($"Post-execution started for node type: {NodeType}");
        
        // 记录性能指标
        var preExecuteStartTime = context.GetMetric("PreExecuteStartTime");
        if (preExecuteStartTime > 0)
        {
            var executionTime = DateTime.UtcNow.Ticks - preExecuteStartTime;
            context.RecordMetric("TotalExecutionTicks", executionTime);
            context.RecordMetric("TotalExecutionMs", executionTime / TimeSpan.TicksPerMillisecond);
        }
        
        await Task.CompletedTask;
    }

    /// <summary>
    /// 验证后置条件
    /// </summary>
    /// <param name="context">节点执行上下文</param>
    /// <param name="result">执行结果</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证任务</returns>
    protected virtual async Task ValidatePostConditionsAsync(
        NodeExecutionContext context,
        NodeExecutionResult result,
        CancellationToken cancellationToken)
    {
        // 基础后置验证
        if (result.IsSuccess && result.State != NodeExecutionState.Completed)
        {
            throw new InvalidOperationException($"Inconsistent result state: IsSuccess={result.IsSuccess}, State={result.State}");
        }

        context.AddLog("Post-conditions validated successfully");
        await Task.CompletedTask;
    }

    /// <summary>
    /// 处理执行错误
    /// </summary>
    /// <param name="context">节点执行上下文</param>
    /// <param name="exception">异常信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理任务</returns>
    protected virtual async Task HandleExecutionErrorAsync(
        NodeExecutionContext context,
        Exception exception,
        CancellationToken cancellationToken)
    {
        var executionError = new ExecutionError
        {
            ExecutionId = context.ExecutionId,
            WorkflowId = context.WorkflowId,
            NodeId = context.NodeId,
            Exception = exception,
            OccurredAt = DateTime.UtcNow,
            RetryCount = 0, // NodeExecutionContext doesn't have RetryCount, use 0 as default
            Context = new Dictionary<string, object>
            {
                ["NodeType"] = NodeType,
                ["ExecutionState"] = context.State.ToString(),
                ["Progress"] = context.ProgressPercentage
            }
        };

        // 获取重试策略 - 使用默认策略，因为NodeExecutionContext中的WorkflowContext是字典而不是WorkflowExecutionContext
        var retryStrategy = new NodeRetryStrategy();

        // 处理错误
        var errorResult = await ErrorHandler.HandleErrorAsync(executionError, retryStrategy, cancellationToken);
        
        context.AddLog($"Error handled with action: {errorResult.Action} - {errorResult.Message}", "ERROR");
    }

    /// <summary>
    /// 清理资源
    /// </summary>
    /// <param name="context">节点执行上下文</param>
    /// <param name="result">执行结果</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>清理任务</returns>
    protected virtual async Task CleanupAsync(
        NodeExecutionContext context,
        NodeExecutionResult result,
        CancellationToken cancellationToken)
    {
        context.AddLog("Cleanup completed");
        await Task.CompletedTask;
    }

    /// <summary>
    /// 获取服务实例
    /// </summary>
    /// <typeparam name="T">服务类型</typeparam>
    /// <returns>服务实例</returns>
    protected T GetService<T>() where T : notnull
    {
        return ServiceProvider.GetRequiredService<T>();
    }

    /// <summary>
    /// 尝试获取服务实例
    /// </summary>
    /// <typeparam name="T">服务类型</typeparam>
    /// <returns>服务实例或null</returns>
    protected T? GetServiceOrDefault<T>() where T : class
    {
        return ServiceProvider.GetService<T>();
    }

    #region INodeExecutor Interface Implementation

    /// <summary>
    /// 初始化节点执行器
    /// </summary>
    /// <param name="configuration">节点配置</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>初始化任务</returns>
    public virtual async Task InitializeAsync(
        NodeConfiguration configuration,
        CancellationToken cancellationToken = default)
    {
        Logger.LogDebug("Initializing {NodeType} executor", NodeType);
        await Task.CompletedTask;
    }

    /// <summary>
    /// 验证节点配置
    /// </summary>
    /// <param name="configuration">节点配置</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    public virtual async Task<NodeValidationResult> ValidateAsync(
        NodeConfiguration configuration,
        CancellationToken cancellationToken = default)
    {
        var result = new NodeValidationResult
        {
            IsValid = true,
            NodeType = NodeType,
            ValidationErrors = new List<ValidationError>(),
            ValidationWarnings = new List<ValidationWarning>(),
            ValidatedAt = DateTime.UtcNow
        };

        // 基本验证
        if (configuration == null)
        {
            result.IsValid = false;
            result.ValidationErrors.Add(new ValidationError
            {
                Code = "NULL_CONFIGURATION",
                Message = "Node configuration cannot be null",
                Severity = ValidationSeverity.Error
            });
        }

        return await Task.FromResult(result);
    }



    /// <summary>
    /// 清理节点资源
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>清理任务</returns>
    public virtual async Task CleanupAsync(CancellationToken cancellationToken = default)
    {
        Logger.LogDebug("Cleaning up {NodeType} executor", NodeType);
        await Task.CompletedTask;
    }

    /// <summary>
    /// 获取节点定义
    /// </summary>
    /// <returns>节点定义</returns>
    public virtual NodeDefinition GetNodeDefinition()
    {
        return new NodeDefinition
        {
            NodeType = NodeType,
            DisplayName = DisplayName,
            Description = Description,
            Version = Version,
            Category = NodeTypeCategory.Process,
            Icon = "default",
            SupportsAsync = SupportsAsync,
            IsStateful = IsStateful,
            CreatedAt = DateTime.UtcNow
        };
    }

    /// <summary>
    /// 获取节点的输入参数定义
    /// </summary>
    /// <returns>输入参数定义列表</returns>
    public virtual IEnumerable<NodeParameterDefinition> GetInputParameters()
    {
        return new List<NodeParameterDefinition>();
    }

    /// <summary>
    /// 获取节点的输出参数定义
    /// </summary>
    /// <returns>输出参数定义列表</returns>
    public virtual IEnumerable<NodeParameterDefinition> GetOutputParameters()
    {
        return new List<NodeParameterDefinition>();
    }

    /// <summary>
    /// 获取节点的配置参数定义
    /// </summary>
    /// <returns>配置参数定义列表</returns>
    public virtual IEnumerable<NodeParameterDefinition> GetConfigurationParameters()
    {
        return new List<NodeParameterDefinition>();
    }

    /// <summary>
    /// 检查节点是否可以处理指定的输入数据
    /// </summary>
    /// <param name="inputData">输入数据</param>
    /// <returns>是否可以处理</returns>
    public virtual bool CanHandle(Dictionary<string, object> inputData)
    {
        return true; // 默认可以处理任何输入
    }

    /// <summary>
    /// 估算节点执行时间
    /// </summary>
    /// <param name="inputData">输入数据</param>
    /// <param name="configuration">节点配置</param>
    /// <returns>预估执行时间</returns>
    public virtual TimeSpan EstimateExecutionTime(
        Dictionary<string, object> inputData,
        NodeConfiguration configuration)
    {
        return TimeSpan.FromMilliseconds(100); // 默认100ms
    }

    /// <summary>
    /// 获取节点的资源需求
    /// </summary>
    /// <param name="inputData">输入数据</param>
    /// <param name="configuration">节点配置</param>
    /// <returns>资源需求</returns>
    public virtual NodeResourceRequirements GetResourceRequirements(
        Dictionary<string, object> inputData,
        NodeConfiguration configuration)
    {
        return new NodeResourceRequirements
        {
            CpuCores = 0.1,
            RequiredMemoryMb = 64,
            RequiredDiskMb = 0,
            NetworkBandwidthMbps = 0,
            RequiredServices = new List<string>(),
            EstimatedExecutionTime = EstimateExecutionTime(inputData, configuration)
        };
    }

    /// <summary>
    /// 节点执行进度事件
    /// </summary>
    public event EventHandler<NodeExecutionProgressEventArgs>? ExecutionProgress;

    /// <summary>
    /// 节点状态变更事件
    /// </summary>
    public event EventHandler<NodeExecutionStatusChangedEventArgs>? StatusChanged;

    /// <summary>
    /// 触发执行进度事件
    /// </summary>
    /// <param name="args">事件参数</param>
    protected virtual void OnExecutionProgress(NodeExecutionProgressEventArgs args)
    {
        ExecutionProgress?.Invoke(this, args);
    }

    /// <summary>
    /// 触发状态变更事件
    /// </summary>
    /// <param name="args">事件参数</param>
    protected virtual void OnStatusChanged(NodeExecutionStatusChangedEventArgs args)
    {
        StatusChanged?.Invoke(this, args);
    }

    #endregion
}
