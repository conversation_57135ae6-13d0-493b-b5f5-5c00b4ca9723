using System.Collections.Concurrent;
using Microsoft.Extensions.Logging;
using FlowCustomV1.Core.Models.Workflow;
using FlowCustomV1.Engine.Context;

namespace FlowCustomV1.Engine.ErrorHandling;

/// <summary>
/// 默认错误处理器实现
/// 提供基础的错误处理、重试和恢复功能
/// </summary>
public class DefaultErrorHandler : IErrorHandler
{
    private readonly ILogger<DefaultErrorHandler> _logger;
    private readonly ConcurrentDictionary<string, List<ExecutionError>> _errorHistory = new();
    private readonly ConcurrentDictionary<string, ErrorStatistics> _errorStatistics = new();

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public DefaultErrorHandler(ILogger<DefaultErrorHandler> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 处理执行错误
    /// </summary>
    /// <param name="error">执行错误</param>
    /// <param name="retryStrategy">重试策略</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>错误处理结果</returns>
    public async Task<ErrorHandlingResult> HandleErrorAsync(
        ExecutionError error, 
        NodeRetryStrategy retryStrategy, 
        CancellationToken cancellationToken = default)
    {
        if (error == null)
            throw new ArgumentNullException(nameof(error));
        if (retryStrategy == null)
            throw new ArgumentNullException(nameof(retryStrategy));

        // 记录错误
        await LogErrorAsync(error, cancellationToken);

        // 分类错误
        error.Category = ClassifyError(error.Exception);
        error.Level = DetermineErrorLevel(error.Exception, error.Category);
        error.IsRecoverable = IsRecoverableError(error.Exception, error.Category);

        var result = new ErrorHandlingResult();

        try
        {
            // 判断是否应该重试
            if (await ShouldRetryAsync(error.Exception, retryStrategy, error.RetryCount, cancellationToken))
            {
                result.Action = ErrorHandlingAction.Retry;
                result.RetryDelay = await CalculateRetryDelayAsync(retryStrategy, error.RetryCount, cancellationToken);
                result.IsSuccess = true;
                result.Message = $"Error will be retried after {result.RetryDelay.TotalSeconds} seconds";

                _logger.LogWarning("Error will be retried: {ErrorMessage} (Retry {RetryCount}/{MaxRetries})",
                    error.Message, error.RetryCount + 1, retryStrategy.MaxRetryCount);
            }
            // 判断是否可以恢复
            else if (error.IsRecoverable && CanRecover(error))
            {
                result.Action = ErrorHandlingAction.Recover;
                result.RecoveryAction = CreateRecoveryAction(error);
                result.IsSuccess = true;
                result.Message = "Error recovery will be attempted";

                _logger.LogInformation("Error recovery will be attempted: {ErrorMessage}", error.Message);
            }
            // 判断是否可以跳过
            else if (CanSkip(error, retryStrategy))
            {
                result.Action = ErrorHandlingAction.Skip;
                result.IsSuccess = true;
                result.Message = "Error will be skipped and execution will continue";

                _logger.LogWarning("Error will be skipped: {ErrorMessage}", error.Message);
            }
            // 否则失败
            else
            {
                result.Action = ErrorHandlingAction.Fail;
                result.IsSuccess = false;
                result.Message = $"Error cannot be handled: {error.Message}";

                _logger.LogError("Error cannot be handled: {ErrorMessage}", error.Message);
            }

            // 更新统计信息
            UpdateErrorStatistics(error, result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while handling error: {OriginalError}", error.Message);
            result.Action = ErrorHandlingAction.Fail;
            result.IsSuccess = false;
            result.Message = $"Error handling failed: {ex.Message}";
        }

        return result;
    }

    /// <summary>
    /// 判断是否应该重试
    /// </summary>
    /// <param name="exception">异常信息</param>
    /// <param name="retryStrategy">重试策略</param>
    /// <param name="currentRetryCount">当前重试次数</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否应该重试</returns>
    public async Task<bool> ShouldRetryAsync(
        Exception exception, 
        NodeRetryStrategy retryStrategy, 
        int currentRetryCount, 
        CancellationToken cancellationToken = default)
    {
        if (retryStrategy.StrategyType == RetryStrategyType.None)
            return false;

        if (currentRetryCount >= retryStrategy.MaxRetryCount)
            return false;

        // 检查异常类型是否在重试列表中
        var exceptionTypeName = exception.GetType().Name;
        if (!retryStrategy.ShouldRetry(exceptionTypeName))
            return false;

        // 检查是否是可重试的异常类型
        var isRetryableException = IsRetryableException(exception);
        
        return await Task.FromResult(isRetryableException);
    }

    /// <summary>
    /// 计算重试延迟时间
    /// </summary>
    /// <param name="retryStrategy">重试策略</param>
    /// <param name="retryCount">重试次数</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>重试延迟时间</returns>
    public async Task<TimeSpan> CalculateRetryDelayAsync(
        NodeRetryStrategy retryStrategy, 
        int retryCount, 
        CancellationToken cancellationToken = default)
    {
        var delay = retryStrategy.StrategyType switch
        {
            RetryStrategyType.FixedInterval => retryStrategy.BaseDelay,
            RetryStrategyType.ExponentialBackoff => CalculateExponentialBackoff(retryStrategy.BaseDelay, retryCount),
            RetryStrategyType.LinearBackoff => CalculateLinearBackoff(retryStrategy.BaseDelay, retryCount),
            _ => TimeSpan.Zero
        };

        // 应用最大延迟限制
        if (delay > retryStrategy.MaxDelay)
        {
            delay = retryStrategy.MaxDelay;
        }

        return await Task.FromResult(delay);
    }

    /// <summary>
    /// 处理工作流级别的错误
    /// </summary>
    /// <param name="workflowContext">工作流执行上下文</param>
    /// <param name="exception">异常信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>工作流错误处理结果</returns>
    public async Task<WorkflowErrorHandlingResult> HandleWorkflowErrorAsync(
        EngineWorkflowContext workflowContext,
        Exception exception, 
        CancellationToken cancellationToken = default)
    {
        if (workflowContext == null)
            throw new ArgumentNullException(nameof(workflowContext));
        if (exception == null)
            throw new ArgumentNullException(nameof(exception));

        var result = new WorkflowErrorHandlingResult();

        try
        {
            var errorCategory = ClassifyError(exception);
            var errorLevel = DetermineErrorLevel(exception, errorCategory);

            _logger.LogError(exception, "Workflow error occurred: {WorkflowId} ({ExecutionId})", 
                workflowContext.WorkflowId, workflowContext.ExecutionId);

            // 根据错误级别和类型决定处理策略
            if (errorLevel == ErrorLevel.Fatal || errorLevel == ErrorLevel.Critical)
            {
                result.Action = WorkflowErrorAction.Terminate;
                result.Message = "Workflow terminated due to critical error";
                
                // 取消所有活跃的节点
                result.NodesToCancel = workflowContext.NodeStates
                    .Where(kvp => kvp.Value == NodeExecutionState.Running)
                    .Select(kvp => kvp.Key)
                    .ToList();
            }
            else if (CanPauseWorkflow(exception, errorCategory))
            {
                result.Action = WorkflowErrorAction.Pause;
                result.Message = "Workflow paused for manual intervention";
            }
            else if (CanContinueWorkflow(exception, errorCategory))
            {
                result.Action = WorkflowErrorAction.Continue;
                result.Message = "Workflow will continue despite error";
            }
            else
            {
                result.Action = WorkflowErrorAction.Terminate;
                result.Message = "Workflow terminated due to unhandleable error";
            }

            result.IsSuccess = true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while handling workflow error");
            result.Action = WorkflowErrorAction.Terminate;
            result.IsSuccess = false;
            result.Message = $"Workflow error handling failed: {ex.Message}";
        }

        return await Task.FromResult(result);
    }

    /// <summary>
    /// 执行错误恢复操作
    /// </summary>
    /// <param name="context">节点执行上下文</param>
    /// <param name="recoveryAction">恢复操作</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>恢复结果</returns>
    public async Task<ErrorRecoveryResult> ExecuteRecoveryAsync(
        NodeExecutionContext context,
        ErrorRecoveryAction recoveryAction,
        CancellationToken cancellationToken = default)
    {
        if (context == null)
            throw new ArgumentNullException(nameof(context));
        if (recoveryAction == null)
            throw new ArgumentNullException(nameof(recoveryAction));

        var result = new ErrorRecoveryResult();

        try
        {
            _logger.LogInformation("Executing error recovery: {ActionType} for node {NodeId}", 
                recoveryAction.ActionType, context.NodeId);

            // 根据恢复动作类型执行相应的恢复逻辑
            switch (recoveryAction.ActionType.ToLowerInvariant())
            {
                case "reset":
                    result = await ExecuteResetRecovery(context, recoveryAction, cancellationToken);
                    break;
                case "rollback":
                    result = await ExecuteRollbackRecovery(context, recoveryAction, cancellationToken);
                    break;
                case "cleanup":
                    result = await ExecuteCleanupRecovery(context, recoveryAction, cancellationToken);
                    break;
                case "default":
                    result = await ExecuteDefaultRecovery(context, recoveryAction, cancellationToken);
                    break;
                default:
                    result.IsSuccess = false;
                    result.Message = $"Unknown recovery action type: {recoveryAction.ActionType}";
                    break;
            }

            if (result.IsSuccess)
            {
                _logger.LogInformation("Error recovery completed successfully: {ActionType}", recoveryAction.ActionType);
                UpdateRecoveryStatistics(context.ExecutionId, true);
            }
            else
            {
                _logger.LogWarning("Error recovery failed: {ActionType} - {Message}", 
                    recoveryAction.ActionType, result.Message);
                UpdateRecoveryStatistics(context.ExecutionId, false);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred during recovery execution: {ActionType}", recoveryAction.ActionType);
            result.IsSuccess = false;
            result.Message = $"Recovery execution failed: {ex.Message}";
            UpdateRecoveryStatistics(context.ExecutionId, false);
        }

        return result;
    }

    /// <summary>
    /// 记录错误信息
    /// </summary>
    /// <param name="error">错误信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>记录任务</returns>
    public async Task LogErrorAsync(ExecutionError error, CancellationToken cancellationToken = default)
    {
        if (error == null)
            throw new ArgumentNullException(nameof(error));

        try
        {
            // 添加到错误历史
            _errorHistory.AddOrUpdate(
                error.ExecutionId,
                new List<ExecutionError> { error },
                (key, existingErrors) =>
                {
                    existingErrors.Add(error);
                    return existingErrors;
                });

            // 记录日志
            _logger.LogError(error.Exception,
                "Execution error logged: {ErrorId} - {WorkflowId}/{NodeId} - {Message}",
                error.ErrorId, error.WorkflowId, error.NodeId ?? "N/A", error.Message);

            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to log execution error: {ErrorId}", error.ErrorId);
        }
    }

    /// <summary>
    /// 获取错误统计信息
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>错误统计信息</returns>
    public async Task<ErrorStatistics> GetErrorStatisticsAsync(
        string? executionId = null,
        CancellationToken cancellationToken = default)
    {
        var statistics = new ErrorStatistics();

        try
        {
            if (!string.IsNullOrEmpty(executionId))
            {
                // 获取特定执行的统计信息
                if (_errorStatistics.TryGetValue(executionId, out var execStats))
                {
                    statistics = execStats;
                }
            }
            else
            {
                // 获取全局统计信息
                statistics.TotalErrors = _errorHistory.Values.Sum(errors => errors.Count);

                // 按级别统计
                foreach (var errors in _errorHistory.Values)
                {
                    foreach (var error in errors)
                    {
                        statistics.ErrorsByLevel.TryGetValue(error.Level, out var levelCount);
                        statistics.ErrorsByLevel[error.Level] = levelCount + 1;

                        statistics.ErrorsByCategory.TryGetValue(error.Category, out var categoryCount);
                        statistics.ErrorsByCategory[error.Category] = categoryCount + 1;
                    }
                }

                // 汇总所有执行的统计信息
                foreach (var execStats in _errorStatistics.Values)
                {
                    statistics.SuccessfulRetries += execStats.SuccessfulRetries;
                    statistics.FailedRetries += execStats.FailedRetries;
                    statistics.SuccessfulRecoveries += execStats.SuccessfulRecoveries;
                    statistics.FailedRecoveries += execStats.FailedRecoveries;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting error statistics for execution: {ExecutionId}", executionId);
        }

        return await Task.FromResult(statistics);
    }

    #region 私有辅助方法

    /// <summary>
    /// 分类错误
    /// </summary>
    /// <param name="exception">异常信息</param>
    /// <returns>错误类别</returns>
    private ErrorCategory ClassifyError(Exception exception)
    {
        return exception switch
        {
            ArgumentException or ArgumentNullException => ErrorCategory.Validation,
            TimeoutException => ErrorCategory.Timeout,
            UnauthorizedAccessException => ErrorCategory.Permission,
            DirectoryNotFoundException or FileNotFoundException => ErrorCategory.FileSystem,
            HttpRequestException => ErrorCategory.Network,
            InvalidOperationException => ErrorCategory.BusinessLogic,
            OutOfMemoryException => ErrorCategory.Resource,
            _ => ErrorCategory.Unknown
        };
    }

    /// <summary>
    /// 确定错误级别
    /// </summary>
    /// <param name="exception">异常信息</param>
    /// <param name="category">错误类别</param>
    /// <returns>错误级别</returns>
    private ErrorLevel DetermineErrorLevel(Exception exception, ErrorCategory category)
    {
        return exception switch
        {
            OutOfMemoryException or StackOverflowException => ErrorLevel.Fatal,
            UnauthorizedAccessException => ErrorLevel.Critical,
            TimeoutException => ErrorLevel.Warning,
            ArgumentException => ErrorLevel.Error,
            _ => category switch
            {
                ErrorCategory.System => ErrorLevel.Critical,
                ErrorCategory.Resource => ErrorLevel.Critical,
                ErrorCategory.Permission => ErrorLevel.Critical,
                ErrorCategory.Network => ErrorLevel.Warning,
                ErrorCategory.Validation => ErrorLevel.Error,
                _ => ErrorLevel.Error
            }
        };
    }

    /// <summary>
    /// 判断是否是可恢复的错误
    /// </summary>
    /// <param name="exception">异常信息</param>
    /// <param name="category">错误类别</param>
    /// <returns>是否可恢复</returns>
    private bool IsRecoverableError(Exception exception, ErrorCategory category)
    {
        return category switch
        {
            ErrorCategory.Network => true,
            ErrorCategory.Timeout => true,
            ErrorCategory.FileSystem => true,
            ErrorCategory.Database => true,
            ErrorCategory.Resource => false, // 资源不足通常不可恢复
            ErrorCategory.Permission => false, // 权限错误通常不可恢复
            ErrorCategory.System => false, // 系统错误通常不可恢复
            _ => false
        };
    }

    /// <summary>
    /// 判断是否是可重试的异常
    /// </summary>
    /// <param name="exception">异常信息</param>
    /// <returns>是否可重试</returns>
    private bool IsRetryableException(Exception exception)
    {
        return exception switch
        {
            TimeoutException => true,
            HttpRequestException => true,
            DirectoryNotFoundException => false, // 文件不存在不应该重试
            FileNotFoundException => false,
            ArgumentException => false, // 参数错误不应该重试
            UnauthorizedAccessException => false, // 权限错误不应该重试
            OutOfMemoryException => false, // 内存不足不应该重试
            _ => true // 默认可重试
        };
    }

    /// <summary>
    /// 判断是否可以跳过错误
    /// </summary>
    /// <param name="error">执行错误</param>
    /// <param name="retryStrategy">重试策略</param>
    /// <returns>是否可以跳过</returns>
    private bool CanSkip(ExecutionError error, NodeRetryStrategy retryStrategy)
    {
        // 只有在配置允许跳过且错误级别不是致命或严重时才能跳过
        return retryStrategy.AllowSkipOnFailure &&
               error.Level != ErrorLevel.Fatal &&
               error.Level != ErrorLevel.Critical;
    }

    /// <summary>
    /// 判断是否可以恢复
    /// </summary>
    /// <param name="error">执行错误</param>
    /// <returns>是否可以恢复</returns>
    private bool CanRecover(ExecutionError error)
    {
        return error.IsRecoverable && error.Level != ErrorLevel.Fatal;
    }

    /// <summary>
    /// 创建恢复动作
    /// </summary>
    /// <param name="error">执行错误</param>
    /// <returns>恢复动作</returns>
    private ErrorRecoveryAction CreateRecoveryAction(ExecutionError error)
    {
        var actionType = error.Category switch
        {
            ErrorCategory.Network => "reset",
            ErrorCategory.FileSystem => "cleanup",
            ErrorCategory.Database => "rollback",
            _ => "default"
        };

        return new ErrorRecoveryAction
        {
            ActionType = actionType,
            Parameters = new Dictionary<string, object>
            {
                ["ErrorId"] = error.ErrorId,
                ["ErrorCategory"] = error.Category.ToString(),
                ["RetryCount"] = error.RetryCount
            }
        };
    }

    /// <summary>
    /// 计算指数退避延迟
    /// </summary>
    /// <param name="baseDelay">基础延迟</param>
    /// <param name="retryCount">重试次数</param>
    /// <returns>延迟时间</returns>
    private TimeSpan CalculateExponentialBackoff(TimeSpan baseDelay, int retryCount)
    {
        var multiplier = Math.Pow(2, retryCount);
        return TimeSpan.FromMilliseconds(baseDelay.TotalMilliseconds * multiplier);
    }

    /// <summary>
    /// 计算线性退避延迟
    /// </summary>
    /// <param name="baseDelay">基础延迟</param>
    /// <param name="retryCount">重试次数</param>
    /// <returns>延迟时间</returns>
    private TimeSpan CalculateLinearBackoff(TimeSpan baseDelay, int retryCount)
    {
        return TimeSpan.FromMilliseconds(baseDelay.TotalMilliseconds * (retryCount + 1));
    }

    /// <summary>
    /// 判断是否可以暂停工作流
    /// </summary>
    /// <param name="exception">异常信息</param>
    /// <param name="category">错误类别</param>
    /// <returns>是否可以暂停</returns>
    private bool CanPauseWorkflow(Exception exception, ErrorCategory category)
    {
        return category switch
        {
            ErrorCategory.Permission => true, // 权限错误可以暂停等待人工干预
            ErrorCategory.Configuration => true, // 配置错误可以暂停等待修复
            ErrorCategory.Resource => true, // 资源不足可以暂停等待资源释放
            _ => false
        };
    }

    /// <summary>
    /// 判断是否可以继续工作流
    /// </summary>
    /// <param name="exception">异常信息</param>
    /// <param name="category">错误类别</param>
    /// <returns>是否可以继续</returns>
    private bool CanContinueWorkflow(Exception exception, ErrorCategory category)
    {
        return category switch
        {
            ErrorCategory.Validation => true, // 验证错误可以跳过继续
            ErrorCategory.BusinessLogic => true, // 业务逻辑错误可以跳过继续
            _ => false
        };
    }

    /// <summary>
    /// 更新错误统计信息
    /// </summary>
    /// <param name="error">执行错误</param>
    /// <param name="result">处理结果</param>
    private void UpdateErrorStatistics(ExecutionError error, ErrorHandlingResult result)
    {
        _errorStatistics.AddOrUpdate(
            error.ExecutionId,
            new ErrorStatistics
            {
                TotalErrors = 1,
                ErrorsByLevel = new Dictionary<ErrorLevel, int> { [error.Level] = 1 },
                ErrorsByCategory = new Dictionary<ErrorCategory, int> { [error.Category] = 1 },
                StartTime = DateTime.UtcNow,
                EndTime = DateTime.UtcNow
            },
            (key, existing) =>
            {
                existing.TotalErrors++;
                existing.ErrorsByLevel.TryGetValue(error.Level, out var levelCount);
                existing.ErrorsByLevel[error.Level] = levelCount + 1;
                existing.ErrorsByCategory.TryGetValue(error.Category, out var categoryCount);
                existing.ErrorsByCategory[error.Category] = categoryCount + 1;
                existing.EndTime = DateTime.UtcNow;

                if (result.Action == ErrorHandlingAction.Retry)
                {
                    if (result.IsSuccess)
                        existing.SuccessfulRetries++;
                    else
                        existing.FailedRetries++;
                }

                return existing;
            });
    }

    /// <summary>
    /// 更新恢复统计信息
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="isSuccess">是否成功</param>
    private void UpdateRecoveryStatistics(string executionId, bool isSuccess)
    {
        _errorStatistics.AddOrUpdate(
            executionId,
            new ErrorStatistics
            {
                SuccessfulRecoveries = isSuccess ? 1 : 0,
                FailedRecoveries = isSuccess ? 0 : 1,
                StartTime = DateTime.UtcNow,
                EndTime = DateTime.UtcNow
            },
            (key, existing) =>
            {
                if (isSuccess)
                    existing.SuccessfulRecoveries++;
                else
                    existing.FailedRecoveries++;
                existing.EndTime = DateTime.UtcNow;
                return existing;
            });
    }

    /// <summary>
    /// 执行重置恢复
    /// </summary>
    /// <param name="context">节点执行上下文</param>
    /// <param name="recoveryAction">恢复动作</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>恢复结果</returns>
    private async Task<ErrorRecoveryResult> ExecuteResetRecovery(
        NodeExecutionContext context,
        ErrorRecoveryAction recoveryAction,
        CancellationToken cancellationToken)
    {
        // 重置节点状态和数据
        context.UpdateState(NodeExecutionState.NotStarted, "Reset recovery executed");
        context.TemporaryData.Clear();
        context.ProgressPercentage = 0;
        context.CurrentStep = string.Empty;

        return await Task.FromResult(new ErrorRecoveryResult
        {
            IsSuccess = true,
            Message = "Node state reset successfully",
            RecoveredData = new Dictionary<string, object>
            {
                ["ResetAt"] = DateTime.UtcNow,
                ["NodeId"] = context.NodeId
            }
        });
    }

    /// <summary>
    /// 执行回滚恢复
    /// </summary>
    /// <param name="context">节点执行上下文</param>
    /// <param name="recoveryAction">恢复动作</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>恢复结果</returns>
    private async Task<ErrorRecoveryResult> ExecuteRollbackRecovery(
        NodeExecutionContext context,
        ErrorRecoveryAction recoveryAction,
        CancellationToken cancellationToken)
    {
        // 简化的回滚实现：恢复到之前的状态
        var result = new ErrorRecoveryResult
        {
            IsSuccess = true,
            Message = "Rollback recovery completed",
            RecoveredData = new Dictionary<string, object>
            {
                ["RolledBackAt"] = DateTime.UtcNow,
                ["NodeId"] = context.NodeId
            }
        };

        return await Task.FromResult(result);
    }

    /// <summary>
    /// 执行清理恢复
    /// </summary>
    /// <param name="context">节点执行上下文</param>
    /// <param name="recoveryAction">恢复动作</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>恢复结果</returns>
    private async Task<ErrorRecoveryResult> ExecuteCleanupRecovery(
        NodeExecutionContext context,
        ErrorRecoveryAction recoveryAction,
        CancellationToken cancellationToken)
    {
        // 清理临时数据和资源
        context.TemporaryData.Clear();
        context.ExecutionLog.Clear();

        return await Task.FromResult(new ErrorRecoveryResult
        {
            IsSuccess = true,
            Message = "Cleanup recovery completed",
            RecoveredData = new Dictionary<string, object>
            {
                ["CleanedAt"] = DateTime.UtcNow,
                ["NodeId"] = context.NodeId
            }
        });
    }

    /// <summary>
    /// 执行默认恢复
    /// </summary>
    /// <param name="context">节点执行上下文</param>
    /// <param name="recoveryAction">恢复动作</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>恢复结果</returns>
    private async Task<ErrorRecoveryResult> ExecuteDefaultRecovery(
        NodeExecutionContext context,
        ErrorRecoveryAction recoveryAction,
        CancellationToken cancellationToken)
    {
        // 默认恢复：记录恢复尝试但不做实际操作
        return await Task.FromResult(new ErrorRecoveryResult
        {
            IsSuccess = true,
            Message = "Default recovery completed (no action taken)",
            RecoveredData = new Dictionary<string, object>
            {
                ["RecoveredAt"] = DateTime.UtcNow,
                ["NodeId"] = context.NodeId,
                ["ActionType"] = "default"
            }
        });
    }

    #endregion
}
