# FlowCustomV1 性能测试套件

## 📋 测试脚本清单

### 🎯 基础性能测试

#### 1. `performance_test.py` - 综合性能测试
**作用**：全面测试FlowCustomV1 API的性能表现
- **测试范围**：所有主要API端点
- **测试类型**：单请求性能 + 并发性能
- **适用场景**：日常性能监控、版本发布前验证
- **执行时间**：约2-3分钟

**主要功能**：
- API响应时间测试
- 并发吞吐量测试
- 系统资源监控
- 性能统计分析

#### 2. `performance_analysis.py` - 深度性能分析
**作用**：深入分析特定性能问题
- **测试范围**：重点分析慢接口
- **测试类型**：详细时间分解分析
- **适用场景**：性能问题诊断、优化效果验证
- **执行时间**：约1-2分钟

**主要功能**：
- 请求时间分解（连接、处理、响应）
- 多次测试对比分析
- 性能瓶颈识别
- 优化建议生成

#### 3. `quick_performance_test.py` - 快速性能验证
**作用**：快速验证系统基本性能
- **测试范围**：核心API端点
- **测试类型**：快速响应时间检查
- **适用场景**：开发过程中快速验证、CI/CD流水线
- **执行时间**：约30秒

**主要功能**：
- 快速健康检查
- 基本性能指标验证
- 优化效果快速确认

### 🔥 基础设施压力测试

#### 4. `infrastructure_stress_test.py` - 基础设施压力测试
**作用**：测试NATS和MySQL的性能极限
- **测试范围**：NATS服务器 + MySQL数据库
- **测试类型**：中等强度压力测试
- **适用场景**：基础设施性能评估、容量规划
- **执行时间**：约3-5分钟

**主要功能**：
- NATS监控端点性能测试
- NATS并发负载测试（5-50并发）
- MySQL连接和查询性能测试
- MySQL并发负载测试（5-20并发）
- 系统资源使用监控

#### 5. `extreme_stress_test.py` - 极限压力测试
**作用**：测试系统在极限负载下的表现
- **测试范围**：整个系统基础设施
- **测试类型**：极限压力测试
- **适用场景**：系统极限评估、崩溃点分析
- **执行时间**：约5-10分钟

**主要功能**：
- NATS极限负载测试（100-1000并发）
- MySQL极限负载测试（50-500并发）
- 系统崩溃点测试
- 性能边界分析

## 🚀 使用指南

### 环境要求
```bash
# Python依赖
pip install requests mysql-connector-python

# 系统要求
- FlowCustomV1 API服务运行在 localhost:5000
- NATS服务器运行在 localhost:4222 (监控端口8222)
- MySQL数据库运行在 localhost:3306
```

### 执行顺序建议

#### 日常开发测试
1. `quick_performance_test.py` - 快速验证
2. `performance_test.py` - 全面测试

#### 性能问题诊断
1. `performance_analysis.py` - 问题分析
2. `performance_test.py` - 对比验证

#### 系统容量评估
1. `infrastructure_stress_test.py` - 基础设施评估
2. `extreme_stress_test.py` - 极限能力测试

### 执行命令
```bash
# 进入项目根目录
cd /path/to/FlowCustomV1

# 快速性能测试
python performance_tests/quick_performance_test.py

# 综合性能测试
python performance_tests/performance_test.py

# 深度性能分析
python performance_tests/performance_analysis.py

# 基础设施压力测试
python performance_tests/infrastructure_stress_test.py

# 极限压力测试（谨慎使用）
python performance_tests/extreme_stress_test.py
```

## 📊 性能基准参考

### API性能基准
- **轻量级API**：< 50ms（如executor_capacity、swagger）
- **中等复杂度API**：< 1000ms（如cluster_nodes）
- **并发处理能力**：100+ req/s（轻量级），5+ req/s（复杂API）

### 基础设施性能基准
- **NATS吞吐量**：250-280 req/s
- **MySQL吞吐量**：800-1000 queries/s
- **系统并发极限**：1300 并发请求（稳定）
- **系统崩溃点**：1500 并发请求

### 性能等级标准
- 🟢 **优秀**：超出预期性能
- 🟡 **良好**：满足性能要求
- 🟠 **一般**：接近性能边界
- 🔴 **较差**：需要优化改进

## ⚠️ 注意事项

### 测试环境
- 所有测试基于开发环境Docker容器
- 生产环境性能可能有所不同
- 建议在专用测试环境中运行压力测试

### 安全提醒
- `extreme_stress_test.py` 可能影响系统稳定性
- 压力测试期间避免其他重要操作
- 建议在非生产时间进行极限测试

### 结果解读
- 性能数据仅供参考，实际性能取决于硬件配置
- 关注趋势变化比绝对数值更重要
- 定期执行测试以监控性能退化

## 📈 性能优化建议

### 已实现的优化
- ✅ cluster_nodes端点：从10秒优化到1秒（90%提升）
- ✅ NATS JetStream内存限制：从128MB增加到512MB
- ✅ 节点发现超时：从10秒优化到1秒

### 待优化项目
- 🔄 cluster_nodes端点缓存机制
- 🔄 数据库查询优化
- 🔄 异步处理优化
- 🔄 负载均衡策略

---

**最后更新**：2025-09-08
**测试环境**：FlowCustomV1 开发环境 v0.0.1.0
