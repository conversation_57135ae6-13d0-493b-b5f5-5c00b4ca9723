using FlowCustomV1.Core.Interfaces;
using FlowCustomV1.Core.Interfaces.Services;
using FlowCustomV1.Core.Models.Cluster;
using Microsoft.AspNetCore.Mvc;

namespace FlowCustomV1.Api.Controllers;

/// <summary>
/// 集群管理API控制器
/// 提供集群状态查看、节点管理等功能
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public class ClusterController : ControllerBase
{
    private readonly ILogger<ClusterController> _logger;
    private readonly INodeDiscoveryService _nodeDiscoveryService;
    private readonly IClusterService _clusterService;

    /// <summary>
    /// 初始化集群控制器
    /// </summary>
    public ClusterController(
        ILogger<ClusterController> logger,
        INodeDiscoveryService nodeDiscoveryService,
        IClusterService clusterService)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _nodeDiscoveryService = nodeDiscoveryService ?? throw new ArgumentNullException(nameof(nodeDiscoveryService));
        _clusterService = clusterService ?? throw new ArgumentNullException(nameof(clusterService));
    }

    #region 集群状态查询

    /// <summary>
    /// 获取集群概览信息
    /// </summary>
    /// <returns>集群概览</returns>
    [HttpGet("overview")]
    [ProducesResponseType(typeof(ClusterOverview), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<ClusterOverview>> GetClusterOverviewAsync()
    {
        try
        {
            _logger.LogDebug("Getting cluster overview");

            var stats = await _clusterService.GetClusterStatsAsync();
            var currentNode = _nodeDiscoveryService.CurrentNode;
            var knownNodes = _nodeDiscoveryService.KnownNodes;

            var overview = new ClusterOverview
            {
                ClusterName = stats.ClusterName,
                TotalNodes = stats.TotalNodes,
                OnlineNodes = stats.OnlineNodes,
                OfflineNodes = stats.OfflineNodes,
                ClusterHealthScore = stats.ClusterHealthScore,
                CurrentNodeId = currentNode.NodeId,
                LastUpdatedAt = DateTime.UtcNow,
                NodesByMode = stats.NodesByMode,
                NodesByStatus = stats.NodesByStatus,
                AverageLoadScore = stats.AverageLoadScore,
                TotalCpuCores = stats.TotalCpuCores,
                TotalMemoryMB = stats.TotalMemoryMB,
                UsedCpuCores = stats.UsedCpuCores,
                UsedMemoryMB = stats.UsedMemoryMB,
                ActiveTasks = stats.ActiveTasks,
                QueuedTasks = stats.QueuedTasks
            };

            return Ok(overview);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get cluster overview");
            return StatusCode(StatusCodes.Status500InternalServerError, "Failed to get cluster overview");
        }
    }

    /// <summary>
    /// 获取集群统计信息
    /// </summary>
    /// <returns>集群统计</returns>
    [HttpGet("stats")]
    [ProducesResponseType(typeof(ClusterStats), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<ClusterStats>> GetClusterStatsAsync()
    {
        try
        {
            _logger.LogDebug("Getting cluster statistics");
            var stats = await _clusterService.GetClusterStatsAsync();
            return Ok(stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get cluster statistics");
            return StatusCode(StatusCodes.Status500InternalServerError, "Failed to get cluster statistics");
        }
    }

    /// <summary>
    /// 获取集群健康状态
    /// </summary>
    /// <returns>集群健康状态</returns>
    [HttpGet("health")]
    [ProducesResponseType(typeof(ClusterHealthStatus), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<ClusterHealthStatus>> GetClusterHealthAsync()
    {
        try
        {
            _logger.LogDebug("Getting cluster health status");

            var stats = await _clusterService.GetClusterStatsAsync();
            var knownNodes = _nodeDiscoveryService.KnownNodes;

            var healthStatus = new ClusterHealthStatus
            {
                IsHealthy = stats.ClusterHealthScore >= 70,
                HealthScore = stats.ClusterHealthScore,
                TotalNodes = stats.TotalNodes,
                HealthyNodes = knownNodes.Count(n => n.Status == NodeStatus.Healthy),
                UnhealthyNodes = knownNodes.Count(n => n.Status != NodeStatus.Healthy),
                CheckTime = DateTime.UtcNow,
                Issues = GetClusterHealthIssues(knownNodes, stats)
            };

            return Ok(healthStatus);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get cluster health status");
            return StatusCode(StatusCodes.Status500InternalServerError, "Failed to get cluster health status");
        }
    }

    #endregion

    #region 节点管理

    /// <summary>
    /// 获取所有节点信息
    /// </summary>
    /// <returns>节点列表</returns>
    [HttpGet("nodes")]
    [ProducesResponseType(typeof(IReadOnlyList<NodeInfo>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<IReadOnlyList<NodeInfo>>> GetAllNodesAsync()
    {
        try
        {
            _logger.LogDebug("Getting all cluster nodes");
            var nodes = await _nodeDiscoveryService.DiscoverAllNodesAsync();
            return Ok(nodes);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get cluster nodes");
            return StatusCode(StatusCodes.Status500InternalServerError, "Failed to get cluster nodes");
        }
    }

    /// <summary>
    /// 获取指定节点信息
    /// </summary>
    /// <param name="nodeId">节点ID</param>
    /// <returns>节点信息</returns>
    [HttpGet("nodes/{nodeId}")]
    [ProducesResponseType(typeof(NodeInfo), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<NodeInfo>> GetNodeAsync(string nodeId)
    {
        try
        {
            _logger.LogDebug("Getting node {NodeId}", nodeId);
            
            var node = await _nodeDiscoveryService.GetNodeAsync(nodeId);
            if (node == null)
            {
                return NotFound($"Node {nodeId} not found");
            }

            return Ok(node);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get node {NodeId}", nodeId);
            return StatusCode(StatusCodes.Status500InternalServerError, $"Failed to get node {nodeId}");
        }
    }

    /// <summary>
    /// 根据角色获取节点
    /// </summary>
    /// <param name="role">节点角色</param>
    /// <returns>节点列表</returns>
    [HttpGet("nodes/role/{role}")]
    [ProducesResponseType(typeof(IReadOnlyList<NodeInfo>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<IReadOnlyList<NodeInfo>>> GetNodesByRoleAsync(string role)
    {
        try
        {
            _logger.LogDebug("Getting nodes with role {Role}", role);
            var nodes = await _nodeDiscoveryService.DiscoverNodesByRoleAsync(role);
            return Ok(nodes);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get nodes by role {Role}", role);
            return StatusCode(StatusCodes.Status500InternalServerError, $"Failed to get nodes by role {role}");
        }
    }

    /// <summary>
    /// 获取当前节点信息
    /// </summary>
    /// <returns>当前节点信息</returns>
    [HttpGet("nodes/current")]
    [ProducesResponseType(typeof(NodeInfo), StatusCodes.Status200OK)]
    public ActionResult<NodeInfo> GetCurrentNode()
    {
        try
        {
            _logger.LogDebug("Getting current node information");
            var currentNode = _nodeDiscoveryService.CurrentNode;
            return Ok(currentNode);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get current node information");
            return StatusCode(StatusCodes.Status500InternalServerError, "Failed to get current node information");
        }
    }

    #endregion

    #region 集群拓扑

    /// <summary>
    /// 获取集群拓扑信息
    /// </summary>
    /// <returns>集群拓扑</returns>
    [HttpGet("topology")]
    [ProducesResponseType(typeof(ClusterTopology), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<ClusterTopology>> GetClusterTopologyAsync()
    {
        try
        {
            _logger.LogDebug("Getting cluster topology");

            var nodes = await _nodeDiscoveryService.DiscoverAllNodesAsync();
            var stats = await _clusterService.GetClusterStatsAsync();

            var topology = new ClusterTopology
            {
                ClusterName = stats.ClusterName,
                Nodes = nodes.ToList(),
                NodeConnections = BuildNodeConnections(nodes),
                LastUpdatedAt = DateTime.UtcNow,
                TopologyVersion = GenerateTopologyVersion(nodes)
            };

            return Ok(topology);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get cluster topology");
            return StatusCode(StatusCodes.Status500InternalServerError, "Failed to get cluster topology");
        }
    }

    #endregion

    #region 私有方法

    /// <summary>
    /// 获取集群健康问题
    /// </summary>
    private List<string> GetClusterHealthIssues(IReadOnlyList<NodeInfo> nodes, ClusterStats stats)
    {
        var issues = new List<string>();

        if (stats.OfflineNodes > 0)
        {
            issues.Add($"{stats.OfflineNodes} nodes are offline");
        }

        var overloadedNodes = nodes.Count(n => n.Load.LoadScore > 80);
        if (overloadedNodes > 0)
        {
            issues.Add($"{overloadedNodes} nodes are overloaded");
        }

        if (stats.ClusterHealthScore < 50)
        {
            issues.Add("Cluster health score is critically low");
        }

        return issues;
    }

    /// <summary>
    /// 构建节点连接关系
    /// </summary>
    private List<NodeConnection> BuildNodeConnections(IReadOnlyList<NodeInfo> nodes)
    {
        var connections = new List<NodeConnection>();
        
        // 简化实现：假设所有节点都通过NATS连接
        foreach (var node in nodes)
        {
            connections.Add(new NodeConnection
            {
                SourceNodeId = node.NodeId,
                TargetNodeId = "NATS-Cluster",
                ConnectionType = "NATS",
                IsActive = node.Status == NodeStatus.Healthy,
                LastSeen = node.Timestamps.LastActiveAt
            });
        }

        return connections;
    }

    /// <summary>
    /// 生成拓扑版本号
    /// </summary>
    private string GenerateTopologyVersion(IReadOnlyList<NodeInfo> nodes)
    {
        var nodeIds = nodes.Select(n => n.NodeId).OrderBy(id => id);
        var combined = string.Join(",", nodeIds);
        return combined.GetHashCode().ToString("X8");
    }

    #endregion
}
