#!/usr/bin/env python3
"""
FlowCustomV1 v0.0.1.7 NATS集群功能测试
测试NATS集群的基础功能、JetStream、故障转移等
"""

import asyncio
import json
import time
import argparse
import logging
from datetime import datetime
from typing import Dict, List, Any
import aiohttp
import nats
from nats.errors import TimeoutError, NoServersError

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class NatsClusterTester:
    def __init__(self):
        self.nats_servers = [
            "nats://nats-1:4222",
            "nats-2:4222", 
            "nats-3:4222"
        ]
        self.monitoring_urls = [
            "http://nats-1:8222",
            "http://nats-2:8222",
            "http://nats-3:8222"
        ]
        self.test_results = {
            "test_suite": "NATS Cluster Tests",
            "start_time": datetime.utcnow().isoformat(),
            "tests": [],
            "summary": {
                "total": 0,
                "passed": 0,
                "failed": 0,
                "errors": []
            }
        }

    async def run_test(self, test_name: str, test_func):
        """运行单个测试"""
        logger.info(f"🧪 运行测试: {test_name}")
        test_result = {
            "name": test_name,
            "start_time": datetime.utcnow().isoformat(),
            "status": "running",
            "duration": 0,
            "details": {}
        }
        
        start_time = time.time()
        try:
            details = await test_func()
            test_result["status"] = "passed"
            test_result["details"] = details
            self.test_results["summary"]["passed"] += 1
            logger.info(f"✅ {test_name} 通过")
        except Exception as e:
            test_result["status"] = "failed"
            test_result["error"] = str(e)
            self.test_results["summary"]["failed"] += 1
            self.test_results["summary"]["errors"].append(f"{test_name}: {str(e)}")
            logger.error(f"❌ {test_name} 失败: {str(e)}")
        finally:
            test_result["duration"] = time.time() - start_time
            test_result["end_time"] = datetime.utcnow().isoformat()
            self.test_results["tests"].append(test_result)
            self.test_results["summary"]["total"] += 1

    async def test_basic_connectivity(self) -> Dict[str, Any]:
        """测试基础连接"""
        results = {}
        
        for i, server in enumerate(self.nats_servers, 1):
            try:
                nc = await nats.connect(server, connect_timeout=10)
                await nc.close()
                results[f"nats-{i}"] = {"status": "connected", "server": server}
            except Exception as e:
                results[f"nats-{i}"] = {"status": "failed", "server": server, "error": str(e)}
        
        return results

    async def test_cluster_info(self) -> Dict[str, Any]:
        """测试集群信息"""
        results = {}
        
        async with aiohttp.ClientSession() as session:
            for i, url in enumerate(self.monitoring_urls, 1):
                try:
                    async with session.get(f"{url}/varz", timeout=10) as response:
                        if response.status == 200:
                            data = await response.json()
                            results[f"nats-{i}"] = {
                                "status": "healthy",
                                "connections": data.get("connections", 0),
                                "in_msgs": data.get("in_msgs", 0),
                                "out_msgs": data.get("out_msgs", 0),
                                "uptime": data.get("uptime", "unknown")
                            }
                        else:
                            results[f"nats-{i}"] = {"status": "unhealthy", "http_status": response.status}
                except Exception as e:
                    results[f"nats-{i}"] = {"status": "error", "error": str(e)}
        
        return results

    async def test_pub_sub_basic(self) -> Dict[str, Any]:
        """测试基础发布订阅"""
        nc = await nats.connect(self.nats_servers)
        
        received_messages = []
        test_subject = "test.basic.pubsub"
        test_message = "Hello FlowCustomV1!"
        
        async def message_handler(msg):
            received_messages.append(msg.data.decode())
        
        # 订阅
        await nc.subscribe(test_subject, cb=message_handler)
        await asyncio.sleep(0.1)  # 等待订阅生效
        
        # 发布
        await nc.publish(test_subject, test_message.encode())
        await nc.flush()
        await asyncio.sleep(0.5)  # 等待消息处理
        
        await nc.close()
        
        return {
            "sent_message": test_message,
            "received_messages": received_messages,
            "success": len(received_messages) > 0 and received_messages[0] == test_message
        }

    async def test_jetstream_basic(self) -> Dict[str, Any]:
        """测试JetStream基础功能"""
        nc = await nats.connect(self.nats_servers)
        js = nc.jetstream()
        
        stream_name = "TEST_STREAM"
        subject = "test.jetstream.*"
        
        try:
            # 创建流
            await js.add_stream(name=stream_name, subjects=[subject])
            
            # 发布消息
            test_messages = ["message1", "message2", "message3"]
            for i, msg in enumerate(test_messages):
                ack = await js.publish(f"test.jetstream.{i}", msg.encode())
                
            # 创建消费者并接收消息
            received_messages = []
            async def fetch_messages():
                psub = await js.pull_subscribe(subject, "test_consumer", stream=stream_name)
                msgs = await psub.fetch(len(test_messages), timeout=5)
                for msg in msgs:
                    received_messages.append(msg.data.decode())
                    await msg.ack()
            
            await fetch_messages()
            
            # 清理
            await js.delete_stream(stream_name)
            
        finally:
            await nc.close()
        
        return {
            "sent_messages": test_messages,
            "received_messages": received_messages,
            "success": len(received_messages) == len(test_messages)
        }

    async def test_request_reply(self) -> Dict[str, Any]:
        """测试请求-响应模式"""
        nc = await nats.connect(self.nats_servers)
        
        request_subject = "test.request"
        test_request = "ping"
        expected_response = "pong"
        
        # 设置响应处理器
        async def request_handler(msg):
            if msg.data.decode() == test_request:
                await nc.publish(msg.reply, expected_response.encode())
        
        await nc.subscribe(request_subject, cb=request_handler)
        await asyncio.sleep(0.1)  # 等待订阅生效
        
        # 发送请求
        try:
            response = await nc.request(request_subject, test_request.encode(), timeout=5)
            response_data = response.data.decode()
        except TimeoutError:
            response_data = None
        
        await nc.close()
        
        return {
            "request": test_request,
            "expected_response": expected_response,
            "actual_response": response_data,
            "success": response_data == expected_response
        }

    async def test_cluster_failover(self) -> Dict[str, Any]:
        """测试集群故障转移（模拟）"""
        # 注意：这个测试只是验证多服务器连接配置，不会真正停止服务器
        results = {}
        
        # 测试连接到多个服务器
        nc = await nats.connect(self.nats_servers, max_reconnect_attempts=3)
        
        # 发送测试消息
        test_subject = "test.failover"
        test_message = "failover_test"
        
        received = []
        async def handler(msg):
            received.append(msg.data.decode())
        
        await nc.subscribe(test_subject, cb=handler)
        await asyncio.sleep(0.1)
        
        await nc.publish(test_subject, test_message.encode())
        await nc.flush()
        await asyncio.sleep(0.5)
        
        results["connected_server"] = nc.connected_url.netloc if nc.connected_url else "unknown"
        results["is_connected"] = nc.is_connected
        results["message_received"] = len(received) > 0
        
        await nc.close()
        
        return results

    async def test_performance_basic(self) -> Dict[str, Any]:
        """基础性能测试"""
        nc = await nats.connect(self.nats_servers)
        
        subject = "test.performance"
        message_count = 1000
        message_size = 100
        test_message = "x" * message_size
        
        # 发布性能测试
        start_time = time.time()
        for i in range(message_count):
            await nc.publish(f"{subject}.{i}", test_message.encode())
        await nc.flush()
        publish_duration = time.time() - start_time
        
        await nc.close()
        
        return {
            "message_count": message_count,
            "message_size": message_size,
            "publish_duration": publish_duration,
            "messages_per_second": message_count / publish_duration if publish_duration > 0 else 0,
            "throughput_mb_per_second": (message_count * message_size) / (1024 * 1024) / publish_duration if publish_duration > 0 else 0
        }

    async def run_all_tests(self):
        """运行所有测试"""
        logger.info("🚀 开始NATS集群测试套件")
        
        # 定义测试列表
        tests = [
            ("基础连接测试", self.test_basic_connectivity),
            ("集群信息测试", self.test_cluster_info),
            ("发布订阅测试", self.test_pub_sub_basic),
            ("JetStream测试", self.test_jetstream_basic),
            ("请求响应测试", self.test_request_reply),
            ("集群故障转移测试", self.test_cluster_failover),
            ("基础性能测试", self.test_performance_basic)
        ]
        
        # 运行所有测试
        for test_name, test_func in tests:
            await self.run_test(test_name, test_func)
        
        # 完成测试
        self.test_results["end_time"] = datetime.utcnow().isoformat()
        self.test_results["total_duration"] = sum(test["duration"] for test in self.test_results["tests"])
        
        # 输出摘要
        summary = self.test_results["summary"]
        logger.info(f"📊 测试完成: {summary['passed']}/{summary['total']} 通过")
        
        if summary["failed"] > 0:
            logger.error("❌ 失败的测试:")
            for error in summary["errors"]:
                logger.error(f"  - {error}")
        
        return self.test_results

def main():
    parser = argparse.ArgumentParser(description="FlowCustomV1 NATS集群测试")
    parser.add_argument("--output", "-o", help="输出结果文件路径")
    args = parser.parse_args()
    
    async def run_tests():
        tester = NatsClusterTester()
        results = await tester.run_all_tests()
        
        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            logger.info(f"测试结果已保存到: {args.output}")
        
        return results["summary"]["failed"] == 0
    
    # 运行测试
    success = asyncio.run(run_tests())
    exit(0 if success else 1)

if __name__ == "__main__":
    main()
