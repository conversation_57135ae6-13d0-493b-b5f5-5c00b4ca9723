-- FlowCustomV1 v0.0.1.7 完整测试环境数据库初始化脚本
-- 创建测试所需的数据库结构和初始数据

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS flowcustom_simple_test CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS flowcustom_app_test CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 根据环境变量选择数据库
USE flowcustom_app_test;

-- 创建节点信息表
CREATE TABLE IF NOT EXISTS cluster_nodes (
    node_id VARCHAR(100) PRIMARY KEY,
    node_name VARCHAR(200) NOT NULL,
    node_role ENUM('Master', 'Worker', 'Designer', 'Validator', 'Executor') NOT NULL,
    region VARCHAR(50) NOT NULL,
    datacenter VARCHAR(100) NOT NULL,
    endpoint VARCHAR(500) NOT NULL,
    status ENUM('Online', 'Offline', 'Maintenance') DEFAULT 'Offline',
    capabilities JSON,
    resources JSON,
    last_heartbeat TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_role (node_role),
    INDEX idx_region (region),
    INDEX idx_status (status),
    INDEX idx_heartbeat (last_heartbeat)
);

-- 创建工作流定义表
CREATE TABLE IF NOT EXISTS workflow_definitions (
    workflow_id VARCHAR(100) PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    version VARCHAR(20) DEFAULT '1.0.0',
    definition JSON NOT NULL,
    status ENUM('Draft', 'Active', 'Inactive', 'Archived') DEFAULT 'Draft',
    created_by VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_status (status),
    INDEX idx_created_by (created_by)
);

-- 创建工作流执行表
CREATE TABLE IF NOT EXISTS workflow_executions (
    execution_id VARCHAR(100) PRIMARY KEY,
    workflow_id VARCHAR(100) NOT NULL,
    status ENUM('Pending', 'Running', 'Completed', 'Failed', 'Cancelled') DEFAULT 'Pending',
    input_data JSON,
    output_data JSON,
    error_message TEXT,
    executor_node_id VARCHAR(100),
    started_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (workflow_id) REFERENCES workflow_definitions(workflow_id),
    FOREIGN KEY (executor_node_id) REFERENCES cluster_nodes(node_id),
    INDEX idx_workflow_id (workflow_id),
    INDEX idx_status (status),
    INDEX idx_executor (executor_node_id),
    INDEX idx_created_at (created_at)
);

-- 创建任务表
CREATE TABLE IF NOT EXISTS tasks (
    task_id VARCHAR(100) PRIMARY KEY,
    execution_id VARCHAR(100),
    node_id VARCHAR(100),
    task_type VARCHAR(100) NOT NULL,
    status ENUM('Pending', 'Running', 'Completed', 'Failed', 'Cancelled') DEFAULT 'Pending',
    input_data JSON,
    output_data JSON,
    error_message TEXT,
    retry_count INT DEFAULT 0,
    max_retries INT DEFAULT 3,
    scheduled_at TIMESTAMP NULL,
    started_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (execution_id) REFERENCES workflow_executions(execution_id),
    FOREIGN KEY (node_id) REFERENCES cluster_nodes(node_id),
    INDEX idx_execution_id (execution_id),
    INDEX idx_node_id (node_id),
    INDEX idx_status (status),
    INDEX idx_task_type (task_type),
    INDEX idx_scheduled_at (scheduled_at)
);

-- 创建节点负载统计表
CREATE TABLE IF NOT EXISTS node_load_stats (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    node_id VARCHAR(100) NOT NULL,
    cpu_usage DECIMAL(5,2),
    memory_usage DECIMAL(5,2),
    active_tasks INT DEFAULT 0,
    max_tasks INT DEFAULT 0,
    load_score DECIMAL(5,2),
    recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (node_id) REFERENCES cluster_nodes(node_id),
    INDEX idx_node_id (node_id),
    INDEX idx_recorded_at (recorded_at)
);

-- 插入测试节点数据
INSERT INTO cluster_nodes (node_id, node_name, node_role, region, datacenter, endpoint, status, capabilities, resources) VALUES
-- Master节点
('master-beijing', 'MasterBeijing', 'Master', 'Beijing', 'Beijing-DC1', 'http://master-node-beijing:5000', 'Online', 
 '{"cluster_management": true, "task_scheduling": true, "workflow_orchestration": true}',
 '{"cpu_cores": 4, "memory_mb": 8192, "max_concurrent_workflows": 50}'),
('master-shanghai', 'MasterShanghai', 'Master', 'Shanghai', 'Shanghai-DC1', 'http://master-node-shanghai:5000', 'Online',
 '{"cluster_management": true, "task_scheduling": true, "workflow_orchestration": true}',
 '{"cpu_cores": 4, "memory_mb": 8192, "max_concurrent_workflows": 50}'),

-- Worker节点
('worker-beijing-1', 'WorkerBeijing1', 'Worker', 'Beijing', 'Beijing-DC1', 'http://worker-node-beijing-1:5000', 'Online',
 '{"task_execution": true, "data_processing": true, "file_operations": true}',
 '{"cpu_cores": 4, "memory_mb": 8192, "max_concurrent_tasks": 20}'),
('worker-beijing-2', 'WorkerBeijing2', 'Worker', 'Beijing', 'Beijing-DC1', 'http://worker-node-beijing-2:5000', 'Online',
 '{"task_execution": true, "data_processing": true, "file_operations": true, "gpu_acceleration": true}',
 '{"cpu_cores": 8, "memory_mb": 16384, "max_concurrent_tasks": 25}'),
('worker-shanghai-1', 'WorkerShanghai1', 'Worker', 'Shanghai', 'Shanghai-DC1', 'http://worker-node-shanghai-1:5000', 'Online',
 '{"task_execution": true, "data_processing": true, "file_operations": true}',
 '{"cpu_cores": 6, "memory_mb": 12288, "max_concurrent_tasks": 15}'),

-- Designer节点
('designer-beijing', 'DesignerBeijing', 'Designer', 'Beijing', 'Beijing-DC1', 'http://designer-node-beijing:5000', 'Online',
 '{"workflow_design": true, "visual_editor": true, "template_management": true}',
 '{"cpu_cores": 2, "memory_mb": 4096, "max_concurrent_designs": 10}'),
('designer-shanghai', 'DesignerShanghai', 'Designer', 'Shanghai', 'Shanghai-DC1', 'http://designer-node-shanghai:5000', 'Online',
 '{"workflow_design": true, "visual_editor": true, "template_management": true}',
 '{"cpu_cores": 2, "memory_mb": 4096, "max_concurrent_designs": 10}'),

-- Validator节点
('validator-beijing', 'ValidatorBeijing', 'Validator', 'Beijing', 'Beijing-DC1', 'http://validator-node-beijing:5000', 'Online',
 '{"workflow_validation": true, "syntax_checking": true, "dependency_analysis": true}',
 '{"cpu_cores": 2, "memory_mb": 4096, "max_concurrent_validations": 20}'),
('validator-shanghai', 'ValidatorShanghai', 'Validator', 'Shanghai', 'Shanghai-DC1', 'http://validator-node-shanghai:5000', 'Online',
 '{"workflow_validation": true, "syntax_checking": true, "dependency_analysis": true}',
 '{"cpu_cores": 2, "memory_mb": 4096, "max_concurrent_validations": 20}'),

-- Executor节点
('executor-beijing', 'ExecutorBeijing', 'Executor', 'Beijing', 'Beijing-DC1', 'http://executor-node-beijing:5000', 'Online',
 '{"workflow_execution": true, "state_management": true, "execution_monitoring": true}',
 '{"cpu_cores": 4, "memory_mb": 8192, "max_concurrent_workflows": 10}'),
('executor-shanghai', 'ExecutorShanghai', 'Executor', 'Shanghai', 'Shanghai-DC1', 'http://executor-node-shanghai:5000', 'Online',
 '{"workflow_execution": true, "state_management": true, "execution_monitoring": true}',
 '{"cpu_cores": 8, "memory_mb": 16384, "max_concurrent_workflows": 15}');

-- 插入测试工作流定义
INSERT INTO workflow_definitions (workflow_id, name, description, definition, status, created_by) VALUES
('test-simple-workflow', '简单测试工作流', '用于测试基础功能的简单工作流',
 '{"nodes": [{"nodeId": "start", "nodeType": "Start"}, {"nodeId": "task1", "nodeType": "Task", "configuration": {"taskType": "simple_task"}}, {"nodeId": "end", "nodeType": "End"}], "connections": [{"sourceNodeId": "start", "targetNodeId": "task1"}, {"sourceNodeId": "task1", "targetNodeId": "end"}]}',
 'Active', 'system'),

('test-complex-workflow', '复杂测试工作流', '用于测试复杂场景的工作流',
 '{"nodes": [{"nodeId": "start", "nodeType": "Start"}, {"nodeId": "parallel1", "nodeType": "Parallel"}, {"nodeId": "task1", "nodeType": "Task"}, {"nodeId": "task2", "nodeType": "Task"}, {"nodeId": "join1", "nodeType": "Join"}, {"nodeId": "end", "nodeType": "End"}], "connections": [{"sourceNodeId": "start", "targetNodeId": "parallel1"}, {"sourceNodeId": "parallel1", "targetNodeId": "task1"}, {"sourceNodeId": "parallel1", "targetNodeId": "task2"}, {"sourceNodeId": "task1", "targetNodeId": "join1"}, {"sourceNodeId": "task2", "targetNodeId": "join1"}, {"sourceNodeId": "join1", "targetNodeId": "end"}]}',
 'Active', 'system'),

('test-performance-workflow', '性能测试工作流', '用于性能测试的工作流',
 '{"nodes": [{"nodeId": "start", "nodeType": "Start"}, {"nodeId": "loop1", "nodeType": "Loop", "configuration": {"iterations": 100}}, {"nodeId": "task1", "nodeType": "Task", "configuration": {"taskType": "cpu_intensive"}}, {"nodeId": "end", "nodeType": "End"}], "connections": [{"sourceNodeId": "start", "targetNodeId": "loop1"}, {"sourceNodeId": "loop1", "targetNodeId": "task1"}, {"sourceNodeId": "task1", "targetNodeId": "end"}]}',
 'Active', 'system');

-- 插入初始负载统计数据
INSERT INTO node_load_stats (node_id, cpu_usage, memory_usage, active_tasks, max_tasks, load_score) VALUES
('master-beijing', 15.5, 25.3, 0, 50, 0.2),
('master-shanghai', 12.8, 22.1, 0, 50, 0.18),
('worker-beijing-1', 8.2, 18.5, 0, 20, 0.1),
('worker-beijing-2', 6.5, 15.2, 0, 25, 0.08),
('worker-shanghai-1', 10.1, 20.8, 0, 15, 0.12),
('designer-beijing', 5.2, 12.3, 0, 10, 0.06),
('designer-shanghai', 4.8, 11.7, 0, 10, 0.05),
('validator-beijing', 3.5, 8.9, 0, 20, 0.04),
('validator-shanghai', 3.2, 8.1, 0, 20, 0.03),
('executor-beijing', 7.8, 16.4, 0, 10, 0.09),
('executor-shanghai', 6.9, 14.2, 0, 15, 0.08);

-- 创建用于测试的存储过程
DELIMITER //

CREATE PROCEDURE GetClusterStatus()
BEGIN
    SELECT 
        node_role,
        COUNT(*) as node_count,
        SUM(CASE WHEN status = 'Online' THEN 1 ELSE 0 END) as online_count,
        AVG(CASE WHEN status = 'Online' THEN 1 ELSE 0 END) * 100 as availability_percent
    FROM cluster_nodes 
    GROUP BY node_role;
END //

CREATE PROCEDURE GetNodeLoadSummary()
BEGIN
    SELECT 
        cn.node_id,
        cn.node_name,
        cn.node_role,
        cn.region,
        cn.status,
        nls.cpu_usage,
        nls.memory_usage,
        nls.active_tasks,
        nls.max_tasks,
        nls.load_score,
        nls.recorded_at
    FROM cluster_nodes cn
    LEFT JOIN node_load_stats nls ON cn.node_id = nls.node_id
    WHERE nls.recorded_at = (
        SELECT MAX(recorded_at) 
        FROM node_load_stats nls2 
        WHERE nls2.node_id = cn.node_id
    )
    ORDER BY cn.node_role, cn.region, cn.node_id;
END //

DELIMITER ;

-- 创建测试用户（用于应用连接）
CREATE USER IF NOT EXISTS 'flowcustom_test'@'%' IDENTIFIED BY 'TestPassword123!';
GRANT ALL PRIVILEGES ON flowcustom_full_test.* TO 'flowcustom_test'@'%';
FLUSH PRIVILEGES;

-- 显示初始化完成信息
SELECT 'FlowCustomV1 v0.0.1.7 测试数据库初始化完成' as message;
SELECT COUNT(*) as total_nodes FROM cluster_nodes;
SELECT COUNT(*) as total_workflows FROM workflow_definitions;
SELECT COUNT(*) as total_load_records FROM node_load_stats;
