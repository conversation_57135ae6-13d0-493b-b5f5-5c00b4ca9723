namespace FlowCustomV1.Core.Models.Cluster;

/// <summary>
/// 集群架构模式
/// 定义集群的运行架构和节点协作方式
/// </summary>
public enum ClusterArchitectureMode
{
    /// <summary>
    /// Master-Worker模式 - 传统的主从架构
    /// Master节点负责调度，Worker节点负责执行
    /// 适用于传统的集中式任务调度场景
    /// </summary>
    MasterWorker = 0,

    /// <summary>
    /// 角色化模式 - 基于功能角色的分布式架构
    /// 节点根据功能角色提供专业化服务
    /// 适用于微服务化的分布式场景
    /// </summary>
    RoleBased = 1,

    /// <summary>
    /// 混合模式 - 同时支持两种架构模式
    /// 可以在运行时根据需要切换架构模式
    /// 适用于渐进式迁移和兼容性场景
    /// </summary>
    Hybrid = 2,

    /// <summary>
    /// 自适应模式 - 根据集群规模自动选择架构
    /// 小规模集群使用Master-Worker，大规模集群使用角色化
    /// 适用于动态扩缩容的云原生场景
    /// </summary>
    Adaptive = 3
}

/// <summary>
/// 集群架构模式扩展方法
/// </summary>
public static class ClusterArchitectureModeExtensions
{
    /// <summary>
    /// 获取架构模式的显示名称
    /// </summary>
    /// <param name="mode">架构模式</param>
    /// <returns>显示名称</returns>
    public static string GetDisplayName(this ClusterArchitectureMode mode)
    {
        return mode switch
        {
            ClusterArchitectureMode.MasterWorker => "Master-Worker模式",
            ClusterArchitectureMode.RoleBased => "角色化模式",
            ClusterArchitectureMode.Hybrid => "混合模式",
            ClusterArchitectureMode.Adaptive => "自适应模式",
            _ => mode.ToString()
        };
    }

    /// <summary>
    /// 获取架构模式的描述
    /// </summary>
    /// <param name="mode">架构模式</param>
    /// <returns>模式描述</returns>
    public static string GetDescription(this ClusterArchitectureMode mode)
    {
        return mode switch
        {
            ClusterArchitectureMode.MasterWorker => "传统的主从架构，Master节点负责调度，Worker节点负责执行",
            ClusterArchitectureMode.RoleBased => "基于功能角色的分布式架构，节点根据角色提供专业化服务",
            ClusterArchitectureMode.Hybrid => "同时支持Master-Worker和角色化两种架构模式",
            ClusterArchitectureMode.Adaptive => "根据集群规模和负载自动选择最适合的架构模式",
            _ => "未知架构模式"
        };
    }

    /// <summary>
    /// 检查架构模式是否支持Master-Worker
    /// </summary>
    /// <param name="mode">架构模式</param>
    /// <returns>是否支持</returns>
    public static bool SupportsMasterWorker(this ClusterArchitectureMode mode)
    {
        return mode == ClusterArchitectureMode.MasterWorker ||
               mode == ClusterArchitectureMode.Hybrid ||
               mode == ClusterArchitectureMode.Adaptive;
    }

    /// <summary>
    /// 检查架构模式是否支持角色化
    /// </summary>
    /// <param name="mode">架构模式</param>
    /// <returns>是否支持</returns>
    public static bool SupportsRoleBased(this ClusterArchitectureMode mode)
    {
        return mode == ClusterArchitectureMode.RoleBased ||
               mode == ClusterArchitectureMode.Hybrid ||
               mode == ClusterArchitectureMode.Adaptive;
    }

    /// <summary>
    /// 检查架构模式是否需要Master节点
    /// </summary>
    /// <param name="mode">架构模式</param>
    /// <returns>是否需要Master节点</returns>
    public static bool RequiresMasterNode(this ClusterArchitectureMode mode)
    {
        return mode == ClusterArchitectureMode.MasterWorker;
    }

    /// <summary>
    /// 检查架构模式是否支持动态角色切换
    /// </summary>
    /// <param name="mode">架构模式</param>
    /// <returns>是否支持</returns>
    public static bool SupportsDynamicRoleSwitching(this ClusterArchitectureMode mode)
    {
        return mode == ClusterArchitectureMode.RoleBased ||
               mode == ClusterArchitectureMode.Hybrid ||
               mode == ClusterArchitectureMode.Adaptive;
    }

    /// <summary>
    /// 获取推荐的节点角色（基于架构模式）
    /// </summary>
    /// <param name="mode">架构模式</param>
    /// <param name="nodeCount">集群节点数量</param>
    /// <returns>推荐的节点角色</returns>
    public static NodeRole GetRecommendedRoles(this ClusterArchitectureMode mode, int nodeCount)
    {
        return mode switch
        {
            ClusterArchitectureMode.MasterWorker => NodeRole.Executor, // Worker节点主要执行
            ClusterArchitectureMode.RoleBased when nodeCount <= 3 => NodeRole.All, // 小集群全功能
            ClusterArchitectureMode.RoleBased => NodeRole.Designer | NodeRole.Validator | NodeRole.Executor, // 大集群专业化
            ClusterArchitectureMode.Hybrid => NodeRole.All, // 混合模式支持所有角色
            ClusterArchitectureMode.Adaptive when nodeCount <= 5 => NodeRole.All, // 小集群全功能
            ClusterArchitectureMode.Adaptive => NodeRole.Designer | NodeRole.Validator | NodeRole.Executor, // 大集群专业化
            _ => NodeRole.All
        };
    }

    /// <summary>
    /// 根据集群规模推荐架构模式
    /// </summary>
    /// <param name="nodeCount">节点数量</param>
    /// <param name="expectedLoad">预期负载</param>
    /// <returns>推荐的架构模式</returns>
    public static ClusterArchitectureMode RecommendArchitectureMode(int nodeCount, int expectedLoad = 100)
    {
        return (nodeCount, expectedLoad) switch
        {
            (1, _) => ClusterArchitectureMode.RoleBased, // 单节点使用角色化
            (2 or 3, < 50) => ClusterArchitectureMode.MasterWorker, // 小集群低负载用Master-Worker
            (2 or 3 or 4 or 5, _) => ClusterArchitectureMode.Hybrid, // 中等集群用混合模式
            (> 5, _) => ClusterArchitectureMode.RoleBased, // 大集群用角色化
            _ => ClusterArchitectureMode.Adaptive // 默认自适应
        };
    }
}
