#!/usr/bin/env python3
"""
FlowCustomV1 测试环境集群启动脚本
启动完整的测试环境集群 (端口+20000)
"""

import subprocess
import sys
import time
import requests
from pathlib import Path

def run_command(command, cwd=None, timeout=60):
    """执行命令并返回结果"""
    try:
        result = subprocess.run(
            command, 
            cwd=cwd, 
            capture_output=True, 
            text=True, 
            shell=True,
            encoding='utf-8',
            errors='ignore',
            timeout=timeout
        )
        return result.returncode == 0, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return False, "", f"命令超时 ({timeout}秒)"
    except Exception as e:
        return False, "", str(e)

def check_service_health(url, timeout=10):
    """检查服务健康状态"""
    try:
        response = requests.get(f"{url}/health", timeout=timeout)
        return response.status_code == 200
    except:
        return False

def start_test_cluster():
    """启动测试环境集群"""
    print("🚀 启动FlowCustomV1测试环境集群...")
    print("⚠️  注意: 测试环境端口 = 默认端口 + 20000")
    
    script_dir = Path(__file__).parent
    
    # 启动基础设施
    print("🔄 启动基础设施服务 (NATS集群 + MySQL)...")
    success, stdout, stderr = run_command(
        "docker-compose up -d nats-server-1 nats-server-2 nats-server-3 mysql",
        cwd=script_dir,
        timeout=120
    )
    
    if not success:
        print(f"❌ 基础设施启动失败:")
        print(f"stderr: {stderr}")
        return False
    
    print("✅ 基础设施服务启动成功")
    
    # 等待基础设施启动
    print("⏳ 等待基础设施初始化...")
    time.sleep(15)
    
    # 启动应用节点
    print("🔄 启动应用节点...")
    app_nodes = ["api-node", "worker-node", "designer-node", "validator-node", 
                 "executor-node", "monitor-node", "scheduler-node", "multi-role-node"]
    
    for node in app_nodes:
        print(f"🔄 启动 {node}...")
        success, stdout, stderr = run_command(
            f"docker-compose up -d {node}",
            cwd=script_dir,
            timeout=120
        )
        
        if not success:
            print(f"⚠️ {node} 启动失败: {stderr}")
        else:
            print(f"✅ {node} 启动成功")
        
        time.sleep(5)  # 错开启动时间
    
    return True

def check_cluster_status():
    """检查集群状态"""
    print("\n🔍 检查集群状态...")
    
    script_dir = Path(__file__).parent
    
    # 检查容器状态
    success, stdout, stderr = run_command(
        "docker-compose ps",
        cwd=script_dir
    )
    
    if success:
        print("📊 容器状态:")
        print(stdout)
    
    # 检查NATS集群
    print("🔍 检查NATS集群状态...")
    for i, port in enumerate([24222, 24223, 24224], 1):
        try:
            result = subprocess.run(
                ["docker", "exec", f"nats-test-server-{i}", "nats", "server", "info"], 
                capture_output=True, text=True, timeout=10
            )
            if result.returncode == 0:
                print(f"✅ NATS服务器 {port} 运行正常")
            else:
                print(f"⚠️ NATS服务器 {port} 状态检查失败")
        except:
            print(f"⚠️ 无法检查NATS服务器 {port} 状态")
    
    # 检查MySQL
    try:
        result = subprocess.run(
            ["docker", "exec", "mysql-test", "mysqladmin", "ping", "-h", "localhost"], 
            capture_output=True, text=True, timeout=10
        )
        if result.returncode == 0:
            print("✅ MySQL数据库运行正常")
        else:
            print("⚠️ MySQL数据库状态检查失败")
    except:
        print("⚠️ 无法检查MySQL数据库状态")
    
    # 检查API节点
    print("🔍 检查API节点健康状态...")
    api_endpoints = [
        ("API节点", "http://localhost:25000"),
        ("多角色节点", "http://localhost:25001")
    ]
    
    for name, url in api_endpoints:
        if check_service_health(url):
            print(f"✅ {name} 健康检查通过")
        else:
            print(f"⚠️ {name} 健康检查失败")

def stop_test_cluster():
    """停止测试环境集群"""
    print("🛑 停止测试环境集群...")
    
    script_dir = Path(__file__).parent
    
    success, stdout, stderr = run_command(
        "docker-compose down",
        cwd=script_dir,
        timeout=60
    )
    
    if success:
        print("✅ 测试环境集群已停止")
    else:
        print(f"⚠️ 停止可能不完整: {stderr}")

def cleanup_test_cluster():
    """清理测试环境集群"""
    print("🧹 清理测试环境集群...")
    
    script_dir = Path(__file__).parent
    
    success, stdout, stderr = run_command(
        "docker-compose down -v",
        cwd=script_dir,
        timeout=60
    )
    
    if success:
        print("✅ 测试环境集群清理完成")
    else:
        print(f"⚠️ 清理可能不完整: {stderr}")

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python start-test-cluster.py [start|stop|status|cleanup]")
        sys.exit(1)
    
    command = sys.argv[1].lower()
    
    if command == "start":
        if start_test_cluster():
            check_cluster_status()
            print("\n🎉 测试环境集群启动完成!")
            print("\n🌐 集群访问信息 (端口+20000):")
            print("API节点:      http://localhost:25000")
            print("多角色节点:    http://localhost:25001")
            print("NATS监控:     http://localhost:28222-28224")
            print("MySQL数据库:  localhost:23306")
            print("\n💡 测试建议:")
            print("1. 运行集成测试套件")
            print("2. 测试故障转移功能")
            print("3. 验证集群通信")
        else:
            sys.exit(1)
    
    elif command == "stop":
        stop_test_cluster()
    
    elif command == "status":
        check_cluster_status()
    
    elif command == "cleanup":
        cleanup_test_cluster()
    
    else:
        print("❌ 未知命令，支持的命令: start, stop, status, cleanup")
        sys.exit(1)

if __name__ == "__main__":
    main()
