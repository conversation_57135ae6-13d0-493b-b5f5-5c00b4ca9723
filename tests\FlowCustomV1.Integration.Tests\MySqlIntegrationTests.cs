using Xunit;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using FlowCustomV1.Core.Models.Workflow;
using FlowCustomV1.Core.Interfaces.Repositories;
using FlowCustomV1.Infrastructure;
using FlowCustomV1.Infrastructure.Configuration;
using FlowCustomV1.Engine;
using System.Threading.Tasks;

namespace FlowCustomV1.Integration.Tests;

/// <summary>
/// MySQL数据库集成测试
/// 注意：这些测试需要本地MySQL服务器运行
/// </summary>
public class MySqlIntegrationTests : IClassFixture<DatabaseCleanupFixture>, IDisposable
{
    private readonly IServiceProvider _serviceProvider;
    private readonly IConfiguration _configuration;
    private readonly DatabaseCleanupFixture _fixture;

    public MySqlIntegrationTests(DatabaseCleanupFixture fixture)
    {
        _fixture = fixture;
        
        // 构建MySQL测试配置
        var configBuilder = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.mysql.json", optional: false, reloadOnChange: false);

        _configuration = configBuilder.Build();

        // 验证配置是否正确加载
        var connectionString = _configuration.GetConnectionString("Default") ?? _configuration["Database:ConnectionString"];
        var provider = _configuration["Database:Provider"];

        Console.WriteLine($"MySQL Test Configuration - Provider: {provider}, ConnectionString: {connectionString}");

        // 构建服务容器
        var services = new ServiceCollection();

        // 添加日志
        services.AddLogging(builder =>
        {
            builder.AddConsole();
            builder.AddDebug();
            builder.SetMinimumLevel(LogLevel.Debug);
        });

        // 添加基础设施和工作流引擎
        services.AddInfrastructure(_configuration);
        services.AddWorkflowEngine();

        _serviceProvider = services.BuildServiceProvider();
    }

    [Fact]
    public async Task MySQL_DatabaseConnection_ShouldWork()
    {
        // 清理数据库
        await _fixture.CleanupDatabaseAsync();
        
        using var scope = _serviceProvider.CreateScope();
        var dbInitService = scope.ServiceProvider.GetRequiredService<FlowCustomV1.Infrastructure.Services.IDatabaseInitializationService>();

        try
        {
            // 测试数据库连接和初始化
            var initResult = await dbInitService.InitializeDatabaseAsync();
            Assert.True(initResult, "MySQL数据库初始化应该成功");

            // 测试健康检查
            var healthStatus = await dbInitService.CheckDatabaseHealthAsync();
            Assert.True(healthStatus.IsHealthy, $"MySQL数据库应该健康。问题: {string.Join(", ", healthStatus.Issues)}");
            Assert.True(healthStatus.CanConnect, "应该能连接到MySQL数据库");
            Assert.True(healthStatus.DatabaseExists, "MySQL数据库应该存在");
            Assert.True(healthStatus.TablesExist, "MySQL数据库表应该存在");
        }
        catch (Exception ex) when (ex.Message.Contains("Unable to connect") || ex.Message.Contains("Connection refused"))
        {
            // 如果MySQL不可用，跳过测试
            Assert.Fail($"MySQL数据库连接失败，请确保Docker MySQL正在运行: {ex.Message}");
        }
    }

    [Fact]
    public async Task MySQL_WorkflowPersistence_ShouldWork()
    {
        // 清理数据库
        await _fixture.CleanupDatabaseAsync();
        
        using var scope = _serviceProvider.CreateScope();

        // 确保数据库已初始化
        var dbInitService = scope.ServiceProvider.GetRequiredService<FlowCustomV1.Infrastructure.Services.IDatabaseInitializationService>();
        await dbInitService.InitializeDatabaseAsync();

        var workflowRepo = scope.ServiceProvider.GetRequiredService<FlowCustomV1.Core.Interfaces.IWorkflowRepository>();
        var executionRepo = scope.ServiceProvider.GetRequiredService<FlowCustomV1.Core.Interfaces.Repositories.IExecutionRepository>();
        var workflowEngine = scope.ServiceProvider.GetRequiredService<FlowCustomV1.Core.Interfaces.IWorkflowEngine>();

        // 启动工作流引擎
        await workflowEngine.StartAsync(CancellationToken.None);

        try
        {
            // 创建测试工作流
            var workflowDefinition = CreateTestWorkflowDefinition();

            // 保存工作流定义
            var saveResult = await workflowRepo.SaveWorkflowDefinitionAsync(workflowDefinition);
            Assert.True(saveResult, "工作流定义保存应该成功");

            // 执行工作流
            var inputData = new Dictionary<string, object> { ["test"] = "mysql_test" };
            var executionResult = await workflowEngine.ExecuteWorkflowAsync(workflowDefinition, inputData, CancellationToken.None);

            // 验证执行结果
            Assert.NotNull(executionResult);
            Assert.True(executionResult.IsSuccess, "工作流执行应该成功");

            // 验证数据持久化
            var savedInstance = await executionRepo.GetWorkflowInstanceAsync(executionResult.ExecutionId);
            Assert.NotNull(savedInstance);
            Assert.Equal(workflowDefinition.WorkflowId, savedInstance.WorkflowId);

            // 验证节点执行记录 - 增加等待和重试机制
            List<FlowCustomV1.Core.Models.Workflow.NodeExecutionResult> nodeExecutions = new List<FlowCustomV1.Core.Models.Workflow.NodeExecutionResult>();
            for (int i = 0; i < 10; i++)
            {
                nodeExecutions = (await executionRepo.GetNodeExecutionsAsync(executionResult.ExecutionId)).ToList();
                if (nodeExecutions.Count > 0)
                    break;
                await Task.Delay(100); // 等待数据持久化完成
            }
            Assert.NotEmpty(nodeExecutions);
        }
        finally
        {
            // 停止工作流引擎
            await workflowEngine.StopAsync(CancellationToken.None);
        }
    }

    [Fact]
    public async Task MySQL_ConcurrentExecution_ShouldWork()
    {
        // 清理数据库
        await _fixture.CleanupDatabaseAsync();
        
        using var scope = _serviceProvider.CreateScope();

        // 确保数据库已初始化
        var dbInitService = scope.ServiceProvider.GetRequiredService<FlowCustomV1.Infrastructure.Services.IDatabaseInitializationService>();
        await dbInitService.InitializeDatabaseAsync();

        var workflowRepo = scope.ServiceProvider.GetRequiredService<FlowCustomV1.Core.Interfaces.IWorkflowRepository>();
        var workflowEngine = scope.ServiceProvider.GetRequiredService<FlowCustomV1.Core.Interfaces.IWorkflowEngine>();

        // 启动工作流引擎
        await workflowEngine.StartAsync(CancellationToken.None);

        try
        {
            // 创建测试工作流
            var workflowDefinition = CreateTestWorkflowDefinition();
            await workflowRepo.SaveWorkflowDefinitionAsync(workflowDefinition);

            // 并发执行多个工作流
            var tasks = new List<Task<FlowCustomV1.Core.Models.Workflow.WorkflowExecutionResult>>();
            for (int i = 0; i < 3; i++) // 减少并发数量以避免连接池问题
            {
                var inputData = new Dictionary<string, object> { ["test"] = $"concurrent_test_{i}" };
                tasks.Add(workflowEngine.ExecuteWorkflowAsync(workflowDefinition, inputData, CancellationToken.None));
            }

            var results = await Task.WhenAll(tasks);

            // 验证所有执行都成功
            Assert.All(results, result =>
            {
                Assert.NotNull(result);
                Assert.True(result.IsSuccess, "并发工作流执行应该成功");
            });

            // 验证执行ID都不同
            var executionIds = results.Select(r => r.ExecutionId).ToList();
            Assert.Equal(3, executionIds.Distinct().Count());
        }
        finally
        {
            // 停止工作流引擎
            await workflowEngine.StopAsync(CancellationToken.None);
        }
    }

    private static WorkflowDefinition CreateTestWorkflowDefinition()
    {
        // 使用较短的ID以适应数据库字段长度限制（50个字符）
        var guid = Guid.NewGuid().ToString("N");
        var workflowId = $"test-{guid.Substring(0, 30)}"; // 总长度为35个字符，留有余量
        
        return new WorkflowDefinition
        {
            WorkflowId = workflowId,
            Name = "MySQL Test Workflow",
            Description = "A test workflow for MySQL database testing",
            Version = "1.0.0",
            Author = "MySQL Integration Test",
            Nodes = new List<WorkflowNode>
            {
                new WorkflowNode
                {
                    NodeId = "start",
                    Name = "Start",
                    Description = "Start node",
                    NodeType = "Start",
                    Category = NodeTypeCategory.Control
                },
                new WorkflowNode
                {
                    NodeId = "task1",
                    Name = "MySQL Test Task",
                    Description = "A task for MySQL testing",
                    NodeType = "Task",
                    Category = NodeTypeCategory.Process,
                    Configuration = new NodeConfiguration
                    {
                        Parameters = new Dictionary<string, object>
                        {
                            ["TaskType"] = "LogMessage",
                            ["message"] = "MySQL test processing..."
                        }
                    }
                },
                new WorkflowNode
                {
                    NodeId = "end",
                    Name = "End",
                    Description = "End node",
                    NodeType = "End",
                    Category = NodeTypeCategory.Control
                }
            },
            Connections = new List<WorkflowConnection>
            {
                new WorkflowConnection
                {
                    ConnectionId = "start-to-task1",
                    SourceNodeId = "start",
                    TargetNodeId = "task1",
                    ConditionType = ConnectionConditionType.Always
                },
                new WorkflowConnection
                {
                    ConnectionId = "task1-to-end",
                    SourceNodeId = "task1",
                    TargetNodeId = "end",
                    ConditionType = ConnectionConditionType.Always
                }
            },
            CreatedAt = DateTime.UtcNow,
            LastModifiedAt = DateTime.UtcNow
        };
    }

    public void Dispose()
    {
        if (_serviceProvider is IDisposable disposable)
        {
            disposable.Dispose();
        }
    }
}