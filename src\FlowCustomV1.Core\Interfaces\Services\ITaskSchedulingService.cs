using FlowCustomV1.Core.Models.Scheduling;

namespace FlowCustomV1.Core.Interfaces.Services;

/// <summary>
/// 任务调度服务接口
/// </summary>
public interface ITaskSchedulerService
{
    /// <summary>
    /// 调度任务
    /// </summary>
    /// <param name="task">任务</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>调度结果</returns>
    Task<TaskSchedulingResult> ScheduleTaskAsync(ScheduledTask task, CancellationToken cancellationToken = default);

    /// <summary>
    /// 取消任务
    /// </summary>
    /// <param name="taskId">任务ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>取消结果</returns>
    Task<bool> CancelTaskAsync(string taskId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取任务状态
    /// </summary>
    /// <param name="taskId">任务ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务状态</returns>
    Task<TaskStatus?> GetTaskStatusAsync(string taskId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取调度统计
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>调度统计</returns>
    Task<SchedulingStatistics> GetStatisticsAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// 故障转移服务接口
/// </summary>
public interface IFailoverService
{
    /// <summary>
    /// 执行故障转移
    /// </summary>
    /// <param name="failedNodeId">失败节点ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>故障转移结果</returns>
    Task<FailoverResult> ExecuteFailoverAsync(string failedNodeId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 检查节点健康状态
    /// </summary>
    /// <param name="nodeId">节点ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>健康状态</returns>
    Task<NodeHealthStatus> CheckNodeHealthAsync(string nodeId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取故障转移历史
    /// </summary>
    /// <param name="timeRange">时间范围</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>故障转移历史</returns>
    Task<IReadOnlyList<FailoverRecord>> GetFailoverHistoryAsync(TimeSpan? timeRange = null, CancellationToken cancellationToken = default);
}

/// <summary>
/// 执行跟踪服务接口
/// </summary>
public interface IExecutionTrackingService
{
    /// <summary>
    /// 开始跟踪执行
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>跟踪结果</returns>
    Task<bool> StartTrackingAsync(string executionId, string workflowId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 更新执行状态
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="status">状态</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>更新结果</returns>
    Task<bool> UpdateExecutionStatusAsync(string executionId, ExecutionStatus status, CancellationToken cancellationToken = default);

    /// <summary>
    /// 停止跟踪执行
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>停止结果</returns>
    Task<bool> StopTrackingAsync(string executionId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取执行跟踪信息
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>跟踪信息</returns>
    Task<ExecutionTrackingInfo?> GetTrackingInfoAsync(string executionId, CancellationToken cancellationToken = default);
}

/// <summary>
/// 调度任务
/// </summary>
public class ScheduledTask
{
    /// <summary>
    /// 任务ID
    /// </summary>
    public string TaskId { get; set; } = string.Empty;

    /// <summary>
    /// 任务名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 任务类型
    /// </summary>
    public string TaskType { get; set; } = string.Empty;

    /// <summary>
    /// 调度时间
    /// </summary>
    public DateTime ScheduledTime { get; set; }

    /// <summary>
    /// 任务数据
    /// </summary>
    public Dictionary<string, object> Data { get; set; } = new();

    /// <summary>
    /// 优先级
    /// </summary>
    public int Priority { get; set; } = 0;

    /// <summary>
    /// 最大重试次数
    /// </summary>
    public int MaxRetries { get; set; } = 3;
}

/// <summary>
/// 任务调度结果
/// </summary>
public class TaskSchedulingResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccessful { get; set; }

    /// <summary>
    /// 任务ID
    /// </summary>
    public string TaskId { get; set; } = string.Empty;

    /// <summary>
    /// 调度时间
    /// </summary>
    public DateTime ScheduledAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// 任务状态
/// </summary>
public class TaskStatus
{
    /// <summary>
    /// 任务ID
    /// </summary>
    public string TaskId { get; set; } = string.Empty;

    /// <summary>
    /// 状态
    /// </summary>
    public TaskExecutionStatus Status { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime? StartedAt { get; set; }

    /// <summary>
    /// 完成时间
    /// </summary>
    public DateTime? CompletedAt { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 重试次数
    /// </summary>
    public int RetryCount { get; set; }
}

/// <summary>
/// 调度统计
/// </summary>
public class SchedulingStatistics
{
    /// <summary>
    /// 总任务数
    /// </summary>
    public long TotalTasks { get; set; }

    /// <summary>
    /// 成功任务数
    /// </summary>
    public long SuccessfulTasks { get; set; }

    /// <summary>
    /// 失败任务数
    /// </summary>
    public long FailedTasks { get; set; }

    /// <summary>
    /// 平均执行时间
    /// </summary>
    public TimeSpan AverageExecutionTime { get; set; }

    /// <summary>
    /// 按类型统计
    /// </summary>
    public Dictionary<string, long> TasksByType { get; set; } = new();

    /// <summary>
    /// 统计时间
    /// </summary>
    public DateTime StatisticsTime { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 故障转移结果
/// </summary>
public class FailoverResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccessful { get; set; }

    /// <summary>
    /// 失败节点ID
    /// </summary>
    public string FailedNodeId { get; set; } = string.Empty;

    /// <summary>
    /// 目标节点ID
    /// </summary>
    public string? TargetNodeId { get; set; }

    /// <summary>
    /// 转移的任务数
    /// </summary>
    public int TransferredTaskCount { get; set; }

    /// <summary>
    /// 故障转移时间
    /// </summary>
    public DateTime FailoverTime { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// 节点健康状态
/// </summary>
public class NodeHealthStatus
{
    /// <summary>
    /// 节点ID
    /// </summary>
    public string NodeId { get; set; } = string.Empty;

    /// <summary>
    /// 是否健康
    /// </summary>
    public bool IsHealthy { get; set; }

    /// <summary>
    /// 健康分数（0-100）
    /// </summary>
    public int HealthScore { get; set; }

    /// <summary>
    /// 最后检查时间
    /// </summary>
    public DateTime LastCheckTime { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 健康问题
    /// </summary>
    public List<string> HealthIssues { get; set; } = new();
}

/// <summary>
/// 故障转移记录
/// </summary>
public class FailoverRecord
{
    /// <summary>
    /// 记录ID
    /// </summary>
    public string RecordId { get; set; } = string.Empty;

    /// <summary>
    /// 失败节点ID
    /// </summary>
    public string FailedNodeId { get; set; } = string.Empty;

    /// <summary>
    /// 目标节点ID
    /// </summary>
    public string? TargetNodeId { get; set; }

    /// <summary>
    /// 故障转移时间
    /// </summary>
    public DateTime FailoverTime { get; set; }

    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccessful { get; set; }

    /// <summary>
    /// 转移的任务数
    /// </summary>
    public int TransferredTaskCount { get; set; }

    /// <summary>
    /// 故障原因
    /// </summary>
    public string? FailureReason { get; set; }
}

/// <summary>
/// 执行状态
/// </summary>
public enum ExecutionStatus
{
    /// <summary>
    /// 未开始
    /// </summary>
    NotStarted,

    /// <summary>
    /// 运行中
    /// </summary>
    Running,

    /// <summary>
    /// 已完成
    /// </summary>
    Completed,

    /// <summary>
    /// 失败
    /// </summary>
    Failed,

    /// <summary>
    /// 已取消
    /// </summary>
    Cancelled,

    /// <summary>
    /// 暂停
    /// </summary>
    Paused
}

/// <summary>
/// 执行跟踪信息
/// </summary>
public class ExecutionTrackingInfo
{
    /// <summary>
    /// 执行ID
    /// </summary>
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 工作流ID
    /// </summary>
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 当前状态
    /// </summary>
    public ExecutionStatus Status { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime StartedAt { get; set; }

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime LastUpdatedAt { get; set; }

    /// <summary>
    /// 执行节点
    /// </summary>
    public string? ExecutionNodeId { get; set; }

    /// <summary>
    /// 进度百分比
    /// </summary>
    public double ProgressPercentage { get; set; }

    /// <summary>
    /// 执行日志
    /// </summary>
    public List<ExecutionLogEntry> Logs { get; set; } = new();
}

/// <summary>
/// 执行日志条目
/// </summary>
public class ExecutionLogEntry
{
    /// <summary>
    /// 时间戳
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 日志级别
    /// </summary>
    public string Level { get; set; } = string.Empty;

    /// <summary>
    /// 消息
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 节点ID
    /// </summary>
    public string? NodeId { get; set; }
}
