using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using FlowCustomV1.Core.Interfaces.Scheduling;

namespace FlowCustomV1.Core.Models.Scheduling;

/// <summary>
/// 任务失败信息
/// </summary>
public class TaskFailureInfo
{
    /// <summary>
    /// 任务ID
    /// </summary>
    [Required]
    public string TaskId { get; set; } = string.Empty;

    /// <summary>
    /// 失败时间
    /// </summary>
    public DateTime FailedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 失败类型
    /// </summary>
    public FailureType FailureType { get; set; }

    /// <summary>
    /// 错误代码
    /// </summary>
    public string? ErrorCode { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string ErrorMessage { get; set; } = string.Empty;

    /// <summary>
    /// 错误详情
    /// </summary>
    public string? ErrorDetails { get; set; }

    /// <summary>
    /// 异常类型
    /// </summary>
    public string? ExceptionType { get; set; }

    /// <summary>
    /// 堆栈跟踪
    /// </summary>
    public string? StackTrace { get; set; }

    /// <summary>
    /// 执行节点ID
    /// </summary>
    public string ExecutorNodeId { get; set; } = string.Empty;

    /// <summary>
    /// 失败前的执行时间（毫秒）
    /// </summary>
    public long ExecutionTimeBeforeFailure { get; set; }

    /// <summary>
    /// 重试次数
    /// </summary>
    public int RetryCount { get; set; }

    /// <summary>
    /// 是否为瞬时错误
    /// </summary>
    public bool IsTransientError { get; set; }

    /// <summary>
    /// 是否可重试
    /// </summary>
    public bool IsRetryable { get; set; } = true;

    /// <summary>
    /// 失败上下文
    /// </summary>
    public Dictionary<string, object> FailureContext { get; set; } = new();

    /// <summary>
    /// 系统状态快照
    /// </summary>
    public SystemStateSnapshot? SystemSnapshot { get; set; }

    /// <summary>
    /// 相关的其他失败任务ID
    /// </summary>
    public List<string> RelatedFailedTasks { get; set; } = new();
}

/// <summary>
/// 失败类型枚举
/// </summary>
public enum FailureType
{
    /// <summary>
    /// 系统错误
    /// </summary>
    SystemError,

    /// <summary>
    /// 网络错误
    /// </summary>
    NetworkError,

    /// <summary>
    /// 超时错误
    /// </summary>
    TimeoutError,

    /// <summary>
    /// 资源不足
    /// </summary>
    ResourceExhaustion,

    /// <summary>
    /// 配置错误
    /// </summary>
    ConfigurationError,

    /// <summary>
    /// 数据错误
    /// </summary>
    DataError,

    /// <summary>
    /// 业务逻辑错误
    /// </summary>
    BusinessLogicError,

    /// <summary>
    /// 依赖服务错误
    /// </summary>
    DependencyError,

    /// <summary>
    /// 权限错误
    /// </summary>
    AuthorizationError,

    /// <summary>
    /// 未知错误
    /// </summary>
    UnknownError
}

/// <summary>
/// 系统状态快照
/// </summary>
public class SystemStateSnapshot
{
    /// <summary>
    /// 快照时间
    /// </summary>
    public DateTime SnapshotTime { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// CPU使用率
    /// </summary>
    [Range(0, 100)]
    public double CpuUsagePercent { get; set; }

    /// <summary>
    /// 内存使用率
    /// </summary>
    [Range(0, 100)]
    public double MemoryUsagePercent { get; set; }

    /// <summary>
    /// 磁盘使用率
    /// </summary>
    [Range(0, 100)]
    public double DiskUsagePercent { get; set; }

    /// <summary>
    /// 网络延迟（毫秒）
    /// </summary>
    public double NetworkLatencyMs { get; set; }

    /// <summary>
    /// 活跃连接数
    /// </summary>
    public int ActiveConnections { get; set; }

    /// <summary>
    /// 队列深度
    /// </summary>
    public int QueueDepth { get; set; }

    /// <summary>
    /// 错误率
    /// </summary>
    [Range(0, 100)]
    public double ErrorRatePercent { get; set; }

    /// <summary>
    /// 系统负载
    /// </summary>
    public double SystemLoad { get; set; }

    /// <summary>
    /// 可用内存（MB）
    /// </summary>
    public long AvailableMemoryMB { get; set; }

    /// <summary>
    /// 可用磁盘空间（MB）
    /// </summary>
    public long AvailableDiskSpaceMB { get; set; }
}

/// <summary>
/// 任务重试计划结果
/// </summary>
public class TaskRetryPlanResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 任务ID
    /// </summary>
    public string TaskId { get; set; } = string.Empty;

    /// <summary>
    /// 重试计划
    /// </summary>
    public TaskRetryPlan? RetryPlan { get; set; }

    /// <summary>
    /// 计划创建时间
    /// </summary>
    public DateTime PlanCreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 使用的重试策略
    /// </summary>
    public string RetryStrategy { get; set; } = string.Empty;

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 计划详情
    /// </summary>
    public Dictionary<string, object> PlanDetails { get; set; } = new();
}

/// <summary>
/// 任务重试计划
/// </summary>
public class TaskRetryPlan
{
    /// <summary>
    /// 计划ID
    /// </summary>
    public string PlanId { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 任务ID
    /// </summary>
    [Required]
    public string TaskId { get; set; } = string.Empty;

    /// <summary>
    /// 重试策略名称
    /// </summary>
    [Required]
    public string StrategyName { get; set; } = string.Empty;

    /// <summary>
    /// 当前重试次数
    /// </summary>
    public int CurrentRetryCount { get; set; }

    /// <summary>
    /// 最大重试次数
    /// </summary>
    [Range(0, 100)]
    public int MaxRetries { get; set; }

    /// <summary>
    /// 下次重试时间
    /// </summary>
    public DateTime NextRetryTime { get; set; }

    /// <summary>
    /// 重试延迟（毫秒）
    /// </summary>
    public long RetryDelayMs { get; set; }

    /// <summary>
    /// 重试条件
    /// </summary>
    public List<RetryCondition> RetryConditions { get; set; } = new();

    /// <summary>
    /// 排除的节点ID列表
    /// </summary>
    public List<string> ExcludedNodes { get; set; } = new();

    /// <summary>
    /// 首选的节点ID列表
    /// </summary>
    public List<string> PreferredNodes { get; set; } = new();

    /// <summary>
    /// 重试配置修改
    /// </summary>
    public Dictionary<string, object> ConfigModifications { get; set; } = new();

    /// <summary>
    /// 失败历史
    /// </summary>
    public List<TaskFailureInfo> FailureHistory { get; set; } = new();

    /// <summary>
    /// 计划状态
    /// </summary>
    public RetryPlanStatus Status { get; set; } = RetryPlanStatus.Pending;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 计划元数据
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// 重试条件
/// </summary>
public class RetryCondition
{
    /// <summary>
    /// 条件类型
    /// </summary>
    public RetryConditionType Type { get; set; }

    /// <summary>
    /// 条件值
    /// </summary>
    public string Value { get; set; } = string.Empty;

    /// <summary>
    /// 比较操作符
    /// </summary>
    public ComparisonOperator Operator { get; set; } = ComparisonOperator.Equals;

    /// <summary>
    /// 是否为必需条件
    /// </summary>
    public bool IsRequired { get; set; } = true;

    /// <summary>
    /// 条件描述
    /// </summary>
    public string Description { get; set; } = string.Empty;
}

/// <summary>
/// 重试条件类型枚举
/// </summary>
public enum RetryConditionType
{
    /// <summary>
    /// 错误类型
    /// </summary>
    ErrorType,

    /// <summary>
    /// 错误代码
    /// </summary>
    ErrorCode,

    /// <summary>
    /// 节点状态
    /// </summary>
    NodeStatus,

    /// <summary>
    /// 系统负载
    /// </summary>
    SystemLoad,

    /// <summary>
    /// 时间窗口
    /// </summary>
    TimeWindow,

    /// <summary>
    /// 资源可用性
    /// </summary>
    ResourceAvailability
}

/// <summary>
/// 比较操作符枚举
/// </summary>
public enum ComparisonOperator
{
    /// <summary>
    /// 等于
    /// </summary>
    Equals,

    /// <summary>
    /// 不等于
    /// </summary>
    NotEquals,

    /// <summary>
    /// 大于
    /// </summary>
    GreaterThan,

    /// <summary>
    /// 大于等于
    /// </summary>
    GreaterThanOrEqual,

    /// <summary>
    /// 小于
    /// </summary>
    LessThan,

    /// <summary>
    /// 小于等于
    /// </summary>
    LessThanOrEqual,

    /// <summary>
    /// 包含
    /// </summary>
    Contains,

    /// <summary>
    /// 不包含
    /// </summary>
    NotContains,

    /// <summary>
    /// 匹配正则表达式
    /// </summary>
    Matches,

    /// <summary>
    /// 在范围内
    /// </summary>
    InRange
}

/// <summary>
/// 重试计划状态枚举
/// </summary>
public enum RetryPlanStatus
{
    /// <summary>
    /// 等待中
    /// </summary>
    Pending,

    /// <summary>
    /// 已调度
    /// </summary>
    Scheduled,

    /// <summary>
    /// 执行中
    /// </summary>
    Executing,

    /// <summary>
    /// 已完成
    /// </summary>
    Completed,

    /// <summary>
    /// 已失败
    /// </summary>
    Failed,

    /// <summary>
    /// 已取消
    /// </summary>
    Cancelled,

    /// <summary>
    /// 已过期
    /// </summary>
    Expired
}

/// <summary>
/// 任务重试执行结果
/// </summary>
public class TaskRetryExecutionResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 任务ID
    /// </summary>
    public string TaskId { get; set; } = string.Empty;

    /// <summary>
    /// 计划ID
    /// </summary>
    public string PlanId { get; set; } = string.Empty;

    /// <summary>
    /// 重试次数
    /// </summary>
    public int RetryCount { get; set; }

    /// <summary>
    /// 执行开始时间
    /// </summary>
    public DateTime ExecutionStartTime { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 执行结束时间
    /// </summary>
    public DateTime? ExecutionEndTime { get; set; }

    /// <summary>
    /// 执行时长（毫秒）
    /// </summary>
    public long? ExecutionTimeMs => ExecutionEndTime.HasValue 
        ? (long)(ExecutionEndTime.Value - ExecutionStartTime).TotalMilliseconds 
        : null;

    /// <summary>
    /// 分配的节点ID
    /// </summary>
    public string? AssignedNodeId { get; set; }

    /// <summary>
    /// 使用的重试策略
    /// </summary>
    public string RetryStrategy { get; set; } = string.Empty;

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 任务执行结果
    /// </summary>
    public TaskExecutionResult? TaskResult { get; set; }

    /// <summary>
    /// 是否需要继续重试
    /// </summary>
    public bool ShouldContinueRetry { get; set; }

    /// <summary>
    /// 下次重试建议时间
    /// </summary>
    public DateTime? NextRetryRecommendedTime { get; set; }

    /// <summary>
    /// 执行详情
    /// </summary>
    public Dictionary<string, object> ExecutionDetails { get; set; } = new();
}

/// <summary>
/// 任务重试取消结果
/// </summary>
public class TaskRetryCancelResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 任务ID
    /// </summary>
    public string TaskId { get; set; } = string.Empty;

    /// <summary>
    /// 取消时间
    /// </summary>
    public DateTime CancelledAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 取消原因
    /// </summary>
    public string Reason { get; set; } = string.Empty;

    /// <summary>
    /// 已取消的重试计划数量
    /// </summary>
    public int CancelledPlanCount { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// 任务重试状态
/// </summary>
public class TaskRetryStatus
{
    /// <summary>
    /// 任务ID
    /// </summary>
    public string TaskId { get; set; } = string.Empty;

    /// <summary>
    /// 当前重试次数
    /// </summary>
    public int CurrentRetryCount { get; set; }

    /// <summary>
    /// 最大重试次数
    /// </summary>
    public int MaxRetries { get; set; }

    /// <summary>
    /// 重试状态
    /// </summary>
    public RetryStatus Status { get; set; } = RetryStatus.NotStarted;

    /// <summary>
    /// 下次重试时间
    /// </summary>
    public DateTime? NextRetryTime { get; set; }

    /// <summary>
    /// 最后重试时间
    /// </summary>
    public DateTime? LastRetryTime { get; set; }

    /// <summary>
    /// 使用的重试策略
    /// </summary>
    public string RetryStrategy { get; set; } = string.Empty;

    /// <summary>
    /// 失败历史
    /// </summary>
    public List<TaskFailureInfo> FailureHistory { get; set; } = new();

    /// <summary>
    /// 重试历史
    /// </summary>
    public List<RetryAttempt> RetryHistory { get; set; } = new();

    /// <summary>
    /// 最后错误信息
    /// </summary>
    public string? LastErrorMessage { get; set; }

    /// <summary>
    /// 是否可以继续重试
    /// </summary>
    public bool CanRetry { get; set; } = true;

    /// <summary>
    /// 重试被阻止的原因
    /// </summary>
    public string? RetryBlockedReason { get; set; }

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 重试状态枚举
/// </summary>
public enum RetryStatus
{
    /// <summary>
    /// 未开始
    /// </summary>
    NotStarted,

    /// <summary>
    /// 等待重试
    /// </summary>
    WaitingForRetry,

    /// <summary>
    /// 重试中
    /// </summary>
    Retrying,

    /// <summary>
    /// 重试成功
    /// </summary>
    RetrySucceeded,

    /// <summary>
    /// 重试失败
    /// </summary>
    RetryFailed,

    /// <summary>
    /// 重试已取消
    /// </summary>
    RetryCancelled,

    /// <summary>
    /// 重试已耗尽
    /// </summary>
    RetryExhausted
}

/// <summary>
/// 重试尝试记录
/// </summary>
public class RetryAttempt
{
    /// <summary>
    /// 尝试ID
    /// </summary>
    public string AttemptId { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 重试次数
    /// </summary>
    public int RetryCount { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime StartTime { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }

    /// <summary>
    /// 执行时长（毫秒）
    /// </summary>
    public long? DurationMs => EndTime.HasValue 
        ? (long)(EndTime.Value - StartTime).TotalMilliseconds 
        : null;

    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 执行节点ID
    /// </summary>
    public string? ExecutorNodeId { get; set; }

    /// <summary>
    /// 使用的策略
    /// </summary>
    public string Strategy { get; set; } = string.Empty;

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 失败信息
    /// </summary>
    public TaskFailureInfo? FailureInfo { get; set; }

    /// <summary>
    /// 尝试详情
    /// </summary>
    public Dictionary<string, object> Details { get; set; } = new();
}
