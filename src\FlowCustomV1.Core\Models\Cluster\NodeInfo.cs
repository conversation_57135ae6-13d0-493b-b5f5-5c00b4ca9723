using System.ComponentModel.DataAnnotations;

namespace FlowCustomV1.Core.Models.Cluster;

/// <summary>
/// 集群节点信息模型
/// 整合所有节点相关信息的统一模型，确保类型安全和数据一致性
/// </summary>
public class NodeInfo
{
    /// <summary>
    /// 节点唯一标识符
    /// </summary>
    [Required]
    public string NodeId { get; set; } = string.Empty;

    /// <summary>
    /// 节点名称
    /// </summary>
    [Required]
    public string NodeName { get; set; } = string.Empty;

    /// <summary>
    /// 节点运行模式
    /// </summary>
    public NodeMode Mode { get; set; } = NodeMode.Standalone;

    /// <summary>
    /// 节点当前状态
    /// </summary>
    public NodeStatus Status { get; set; } = NodeStatus.Unknown;

    /// <summary>
    /// 集群名称
    /// </summary>
    [Required]
    public string ClusterName { get; set; } = string.Empty;

    /// <summary>
    /// 网络通信信息
    /// </summary>
    public NetworkInfo Network { get; set; } = new();

    /// <summary>
    /// 节点能力信息
    /// </summary>
    public NodeCapabilities Capabilities { get; set; } = new();

    /// <summary>
    /// 节点负载信息
    /// </summary>
    public NodeLoad Load { get; set; } = new();

    /// <summary>
    /// 节点健康状态
    /// </summary>
    public HealthStatus Health { get; set; } = new();

    /// <summary>
    /// 时间戳信息
    /// </summary>
    public Timestamps Timestamps { get; set; } = new();

    /// <summary>
    /// 节点功能角色配置
    /// 支持新的角色化架构和传统Master-Worker模式兼容
    /// </summary>
    public NodeRoleConfiguration RoleConfiguration { get; set; } = new();

    /// <summary>
    /// 节点元数据
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();

    /// <summary>
    /// 创建节点信息的深拷贝
    /// </summary>
    /// <returns>节点信息的深拷贝</returns>
    public NodeInfo Clone()
    {
        return new NodeInfo
        {
            NodeId = NodeId,
            NodeName = NodeName,
            Mode = Mode,
            Status = Status,
            ClusterName = ClusterName,
            Network = Network.Clone(),
            Capabilities = Capabilities.Clone(),
            Load = Load.Clone(),
            Health = Health.Clone(),
            Timestamps = Timestamps.Clone(),
            RoleConfiguration = RoleConfiguration.Clone(),
            Metadata = new Dictionary<string, object>(Metadata)
        };
    }

    /// <summary>
    /// 验证节点信息的有效性
    /// </summary>
    /// <returns>验证结果</returns>
    public bool IsValid()
    {
        return !string.IsNullOrWhiteSpace(NodeId) &&
               !string.IsNullOrWhiteSpace(NodeName) &&
               !string.IsNullOrWhiteSpace(ClusterName) &&
               Network.IsValid() &&
               Capabilities.IsValid() &&
               RoleConfiguration.Validate().IsValid;
    }

    /// <summary>
    /// 检查节点是否支持指定角色
    /// </summary>
    /// <param name="role">目标角色</param>
    /// <returns>是否支持</returns>
    public bool SupportsRole(NodeRole role)
    {
        return RoleConfiguration.SupportsRole(role);
    }

    /// <summary>
    /// 获取节点的所有激活角色
    /// </summary>
    /// <returns>激活的角色列表</returns>
    public IEnumerable<NodeRole> GetActiveRoles()
    {
        return RoleConfiguration.Roles.GetActiveRoles();
    }

    /// <summary>
    /// 检查节点是否兼容Master-Worker模式
    /// </summary>
    /// <returns>是否兼容</returns>
    public bool IsCompatibleWithMasterWorker()
    {
        return RoleConfiguration.EnableLegacyMode ||
               RoleConfiguration.ArchitectureMode.SupportsMasterWorker();
    }

    /// <summary>
    /// 检查节点是否支持角色化模式
    /// </summary>
    /// <returns>是否支持</returns>
    public bool SupportsRoleBasedMode()
    {
        return RoleConfiguration.ArchitectureMode.SupportsRoleBased();
    }

    /// <summary>
    /// 获取节点在指定架构模式下的有效角色
    /// </summary>
    /// <param name="architectureMode">架构模式</param>
    /// <returns>有效角色列表</returns>
    public IEnumerable<NodeRole> GetEffectiveRoles(ClusterArchitectureMode architectureMode)
    {
        if (architectureMode == ClusterArchitectureMode.MasterWorker)
        {
            // 在Master-Worker模式下，映射角色到传统模式
            var roles = new List<NodeRole>();

            if (Mode == NodeMode.Master || Mode == NodeMode.Hybrid)
            {
                roles.AddRange(new[] { NodeRole.Scheduler, NodeRole.Gateway, NodeRole.Monitor });
            }

            if (Mode == NodeMode.Worker || Mode == NodeMode.Hybrid)
            {
                roles.Add(NodeRole.Executor);
            }

            return roles.Intersect(GetActiveRoles());
        }

        return GetActiveRoles();
    }

    /// <summary>
    /// 获取节点的简要描述
    /// </summary>
    /// <returns>节点简要描述</returns>
    public override string ToString()
    {
        return $"Node[{NodeId}] {NodeName} ({Mode}) - {Status}";
    }
}
