# FlowCustomV1 架构兼容性设计文档

## 文档信息

| 项目信息 | 详细内容 |
|---------|---------|
| **文档版本** | v0.0.1.6 |
| **创建日期** | 2025-09-06 |
| **最后更新** | 2025-09-06 |
| **文档类型** | 架构兼容性设计 |
| **适用版本** | v0.0.1.6+ |

---

## 1. 架构兼容性概述

### 1.1 设计目标

FlowCustomV1项目采用**渐进式架构迁移策略**，实现Master-Worker模式与角色化模式的平滑兼容，确保：

- **向后兼容性**：现有Master-Worker模式继续正常工作
- **渐进式迁移**：支持逐步迁移到角色化模式
- **双模式并存**：两种模式可以在同一集群中共存
- **零停机升级**：支持在线升级和模式切换

### 1.2 兼容性层次

```
┌─────────────────────────────────────────────────────────────┐
│                    应用层 (API/UI)                           │
├─────────────────────────────────────────────────────────────┤
│                  兼容性适配层                                │
│  ┌─────────────────┐    ┌─────────────────────────────────┐  │
│  │  传统模式适配器  │    │      角色化模式适配器        │  │
│  └─────────────────┘    └─────────────────────────────────┘  │
├─────────────────────────────────────────────────────────────┤
│                    核心服务层                                │
│  ┌─────────────────┐    ┌─────────────────────────────────┐  │
│  │   Master-Worker │    │    角色化服务 (Designer/       │  │
│  │      服务       │    │    Validator/Executor)         │  │
│  └─────────────────┘    └─────────────────────────────────┘  │
├─────────────────────────────────────────────────────────────┤
│                  统一消息路由层                              │
│           (支持两种模式的消息路由和负载均衡)                  │
├─────────────────────────────────────────────────────────────┤
│                    基础设施层                                │
│              (NATS、数据库、缓存、监控)                      │
└─────────────────────────────────────────────────────────────┘
```

---

## 2. 双模式架构设计

### 2.1 Master-Worker模式 (传统模式)

#### 2.1.1 架构特点
- **集中式管理**：Master节点负责任务调度和状态管理
- **简单部署**：节点角色明确，部署配置简单
- **成熟稳定**：经过验证的架构模式

#### 2.1.2 节点类型
```csharp
public enum LegacyNodeMode
{
    Master,    // 主节点：任务调度、状态管理、UI服务
    Worker,    // 工作节点：任务执行
    Hybrid     // 混合节点：既是主节点又是工作节点
}
```

#### 2.1.3 消息主题结构
```
flowcustom.legacy.
├── master.commands.*        # 主节点命令
├── worker.tasks.*          # 工作节点任务
├── cluster.heartbeat.*     # 集群心跳
└── system.notifications.*  # 系统通知
```

### 2.2 角色化模式 (新架构)

#### 2.2.1 架构特点
- **专业化分工**：每个角色专注特定功能
- **水平扩展**：各角色可独立扩展
- **高可用性**：单点故障影响最小化

#### 2.2.2 角色类型 (7角色架构)
```csharp
[Flags]
public enum NodeRole
{
    None = 0,
    Designer = 1 << 0,    // 工作流设计
    Validator = 1 << 1,   // 工作流验证
    Executor = 1 << 2,    // 工作流执行
    Monitor = 1 << 3,     // 系统监控
    Gateway = 1 << 4,     // API网关
    Storage = 1 << 5,     // 数据存储
    Scheduler = 1 << 6,   // 任务调度
    All = Designer | Validator | Executor | Monitor | Gateway | Storage | Scheduler
}
```

#### 2.2.3 消息主题结构 (7角色完整)
```
flowcustom.roles.
├── designer.*              # 设计器角色
├── validator.*             # 验证器角色
├── executor.*              # 执行器角色
├── monitor.*               # 监控角色
├── gateway.*               # 网关角色
├── storage.*               # 存储角色
└── scheduler.*             # 调度器角色
```

---

## 3. 兼容性适配层设计

### 3.1 模式检测和路由

#### 3.1.1 节点模式检测
```csharp
public interface INodeModeDetector
{
    /// <summary>
    /// 检测节点运行模式
    /// </summary>
    NodeOperationMode DetectNodeMode(NodeInfo nodeInfo);
    
    /// <summary>
    /// 判断是否支持角色化模式
    /// </summary>
    bool SupportsRoleBasedMode(NodeInfo nodeInfo);
}

public enum NodeOperationMode
{
    LegacyMasterWorker,  // 传统Master-Worker模式
    RoleBased,           // 角色化模式
    Hybrid               // 混合模式
}
```

#### 3.1.2 消息路由适配
```csharp
public interface ICompatibilityMessageRouter
{
    /// <summary>
    /// 路由消息到合适的模式处理器
    /// </summary>
    Task RouteMessageAsync(IMessage message, CancellationToken cancellationToken);
    
    /// <summary>
    /// 转换传统消息到角色化消息
    /// </summary>
    IMessage TransformLegacyToRoleMessage(IMessage legacyMessage);
    
    /// <summary>
    /// 转换角色化消息到传统消息
    /// </summary>
    IMessage TransformRoleToLegacyMessage(IMessage roleMessage);
}
```

### 3.2 服务适配器

#### 3.2.1 传统模式适配器
```csharp
public class LegacyModeAdapter : IWorkflowService
{
    private readonly IMasterWorkerService _masterWorkerService;
    private readonly ICompatibilityMessageRouter _messageRouter;
    
    public async Task<WorkflowExecutionResult> ExecuteWorkflowAsync(
        WorkflowDefinition workflow, 
        CancellationToken cancellationToken = default)
    {
        // 将角色化API调用适配到传统Master-Worker模式
        var legacyRequest = TransformToLegacyRequest(workflow);
        return await _masterWorkerService.ExecuteAsync(legacyRequest, cancellationToken);
    }
}
```

#### 3.2.2 角色化模式适配器
```csharp
public class RoleBasedModeAdapter : IWorkflowService
{
    private readonly IDesignerService _designerService;
    private readonly IValidatorService _validatorService;
    private readonly IExecutorService _executorService;
    
    public async Task<WorkflowExecutionResult> ExecuteWorkflowAsync(
        WorkflowDefinition workflow, 
        CancellationToken cancellationToken = default)
    {
        // 验证工作流
        var validationResult = await _validatorService.ValidateWorkflowAsync(workflow, cancellationToken);
        if (!validationResult.IsValid)
        {
            throw new WorkflowValidationException(validationResult.Errors);
        }
        
        // 执行工作流
        return await _executorService.ExecuteWorkflowAsync(workflow, cancellationToken);
    }
}
```

---

## 4. 配置管理

### 4.1 兼容性配置

#### 4.1.1 节点配置
```json
{
  "NodeConfiguration": {
    "NodeId": "node-001",
    "OperationMode": "Hybrid",  // Legacy, RoleBased, Hybrid
    "LegacyMode": {
      "NodeType": "Master",
      "WorkerCapacity": 10
    },
    "RoleBasedMode": {
      "Roles": ["Designer", "Validator"],
      "RoleConfiguration": {
        "Designer": {
          "MaxConcurrentDesigns": 5
        },
        "Validator": {
          "ValidationRules": ["structural", "semantic"]
        }
      }
    }
  }
}
```

#### 4.1.2 集群配置
```json
{
  "ClusterConfiguration": {
    "CompatibilityMode": "Mixed",  // Legacy, RoleBased, Mixed
    "MigrationStrategy": "Gradual",
    "LoadBalancing": {
      "LegacyNodes": {
        "Strategy": "RoundRobin"
      },
      "RoleBasedNodes": {
        "Strategy": "LeastLoad"
      }
    }
  }
}
```

### 4.2 迁移配置

#### 4.2.1 迁移策略
```csharp
public class MigrationConfiguration
{
    /// <summary>
    /// 迁移策略
    /// </summary>
    public MigrationStrategy Strategy { get; set; } = MigrationStrategy.Gradual;
    
    /// <summary>
    /// 迁移阶段
    /// </summary>
    public List<MigrationPhase> Phases { get; set; } = new();
    
    /// <summary>
    /// 回滚策略
    /// </summary>
    public RollbackStrategy RollbackStrategy { get; set; } = RollbackStrategy.Automatic;
}

public enum MigrationStrategy
{
    Immediate,  // 立即迁移
    Gradual,    // 渐进式迁移
    Manual      // 手动迁移
}
```

---

## 5. 消息路由兼容性

### 5.1 统一消息路由

#### 5.1.1 路由规则
```csharp
public class CompatibilityRoutingRules
{
    // 传统模式路由规则
    public static readonly Dictionary<string, string> LegacyRoutes = new()
    {
        ["workflow.execute"] = "flowcustom.legacy.master.commands.execute",
        ["task.assign"] = "flowcustom.legacy.worker.tasks.assign",
        ["node.heartbeat"] = "flowcustom.legacy.cluster.heartbeat"
    };
    
    // 角色化模式路由规则
    public static readonly Dictionary<string, string[]> RoleBasedRoutes = new()
    {
        ["workflow.design"] = new[] { "flowcustom.roles.designer.design" },
        ["workflow.validate"] = new[] { "flowcustom.roles.validator.validate" },
        ["workflow.execute"] = new[] { "flowcustom.roles.executor.execute" }
    };
}
```

#### 5.1.2 消息转换
```csharp
public class MessageTransformer
{
    /// <summary>
    /// 转换传统消息格式到角色化格式
    /// </summary>
    public RoleBasedMessage TransformLegacyMessage(LegacyMessage legacyMessage)
    {
        return new RoleBasedMessage
        {
            MessageId = legacyMessage.MessageId,
            MessageType = MapLegacyToRoleMessageType(legacyMessage.Type),
            TargetRole = DetermineTargetRole(legacyMessage.Type),
            Payload = TransformPayload(legacyMessage.Payload),
            Metadata = legacyMessage.Metadata
        };
    }
}
```

---

## 6. 数据兼容性

### 6.1 数据模型兼容

#### 6.1.1 统一数据接口
```csharp
public interface IWorkflowData
{
    string WorkflowId { get; }
    string Version { get; }
    WorkflowStatus Status { get; }
    
    // 传统模式数据
    LegacyWorkflowData? LegacyData { get; }
    
    // 角色化模式数据
    RoleBasedWorkflowData? RoleBasedData { get; }
}
```

#### 6.1.2 数据转换服务
```csharp
public interface IDataCompatibilityService
{
    /// <summary>
    /// 转换传统数据到角色化数据
    /// </summary>
    Task<RoleBasedWorkflowData> ConvertLegacyDataAsync(LegacyWorkflowData legacyData);
    
    /// <summary>
    /// 转换角色化数据到传统数据
    /// </summary>
    Task<LegacyWorkflowData> ConvertRoleBasedDataAsync(RoleBasedWorkflowData roleData);
}
```

---

## 7. 监控和诊断

### 7.1 兼容性监控

#### 7.1.1 监控指标
```csharp
public class CompatibilityMetrics
{
    /// <summary>
    /// 传统模式节点数量
    /// </summary>
    public int LegacyNodeCount { get; set; }
    
    /// <summary>
    /// 角色化模式节点数量
    /// </summary>
    public int RoleBasedNodeCount { get; set; }
    
    /// <summary>
    /// 混合模式节点数量
    /// </summary>
    public int HybridNodeCount { get; set; }
    
    /// <summary>
    /// 消息转换成功率
    /// </summary>
    public double MessageTransformationSuccessRate { get; set; }
    
    /// <summary>
    /// 兼容性错误数量
    /// </summary>
    public long CompatibilityErrorCount { get; set; }
}
```

#### 7.1.2 健康检查
```csharp
public class CompatibilityHealthCheck : IHealthCheck
{
    public async Task<HealthCheckResult> CheckHealthAsync(
        HealthCheckContext context, 
        CancellationToken cancellationToken = default)
    {
        var metrics = await GetCompatibilityMetricsAsync();
        
        var isHealthy = metrics.MessageTransformationSuccessRate > 0.95 &&
                       metrics.CompatibilityErrorCount < 100;
        
        return isHealthy 
            ? HealthCheckResult.Healthy("Compatibility layer is healthy")
            : HealthCheckResult.Degraded("Compatibility issues detected");
    }
}
```

---

## 8. 迁移指南

### 8.1 迁移步骤

#### 8.1.1 阶段1：准备阶段
1. **评估现有系统**：分析当前Master-Worker部署
2. **制定迁移计划**：确定迁移顺序和时间表
3. **部署兼容性层**：安装兼容性适配组件
4. **配置双模式支持**：启用混合模式配置

#### 8.1.2 阶段2：渐进迁移
1. **部署角色化节点**：逐步添加专业化角色节点
2. **流量切换**：逐步将流量从传统节点切换到角色化节点
3. **监控和调优**：监控性能和稳定性
4. **问题修复**：及时处理兼容性问题

#### 8.1.3 阶段3：完成迁移
1. **验证功能完整性**：确保所有功能正常工作
2. **性能优化**：优化角色化模式性能
3. **移除传统节点**：逐步下线Master-Worker节点
4. **清理兼容性代码**：移除不再需要的适配代码

### 8.2 回滚策略

#### 8.2.1 自动回滚触发条件
- 错误率超过阈值（5%）
- 响应时间增加超过50%
- 关键功能不可用
- 数据一致性问题

#### 8.2.2 回滚步骤
1. **停止新流量**：暂停向角色化节点发送新请求
2. **恢复传统节点**：重新启用Master-Worker节点
3. **数据同步**：确保数据一致性
4. **验证功能**：验证回滚后系统正常

---

## 9. 最佳实践

### 9.1 开发最佳实践

1. **接口优先设计**：定义统一接口，支持多种实现
2. **配置驱动**：通过配置控制模式切换，避免硬编码
3. **渐进式开发**：先实现兼容性，再优化性能
4. **充分测试**：确保两种模式都能正常工作

### 9.2 运维最佳实践

1. **监控覆盖**：监控两种模式的关键指标
2. **灰度发布**：使用灰度发布降低风险
3. **备份策略**：确保可以快速回滚
4. **文档维护**：及时更新操作文档

---

## 10. 总结

FlowCustomV1的架构兼容性设计实现了Master-Worker模式与角色化模式的平滑过渡，确保：

- **零停机迁移**：支持在线升级和模式切换
- **风险可控**：提供完整的回滚机制
- **性能优化**：角色化模式提供更好的扩展性
- **运维友好**：简化部署和管理复杂度

通过这种设计，FlowCustomV1能够在保持向后兼容的同时，逐步演进到更先进的角色化架构，为未来的功能扩展和性能优化奠定坚实基础。
