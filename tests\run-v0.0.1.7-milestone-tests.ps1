# FlowCustomV1 v0.0.1.7 里程碑版本全面测试脚本
# 基于完整功能开发历史的全面测试验证

param(
    [string]$TestScope = "All",
    [switch]$IncludeStressTests,
    [switch]$IncludeScenarioTests,
    [switch]$GenerateDetailedReport,
    [switch]$RunInParallel,
    [string]$LogLevel = "Information"
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# 测试配置
$TestProjectPath = "FlowCustomV1.Tests"
$ReportPath = "TestResults/v0.0.1.7-Milestone"
$TestStartTime = Get-Date

Write-Host "================================================================" -ForegroundColor Green
Write-Host "FlowCustomV1 v0.0.1.7 里程碑版本全面测试套件" -ForegroundColor Green
Write-Host "================================================================" -ForegroundColor Green
Write-Host "测试开始时间: $($TestStartTime.ToString('yyyy-MM-dd HH:mm:ss'))" -ForegroundColor Gray
Write-Host "测试范围: $TestScope" -ForegroundColor Gray
Write-Host "包含压力测试: $IncludeStressTests" -ForegroundColor Gray
Write-Host "包含场景测试: $IncludeScenarioTests" -ForegroundColor Gray
Write-Host ""

# 创建测试结果目录
if (!(Test-Path $ReportPath)) {
    New-Item -ItemType Directory -Path $ReportPath -Force | Out-Null
    Write-Host "✓ 创建测试结果目录: $ReportPath" -ForegroundColor Green
}

# 功能历史回顾
Write-Host "=== v0.0.1.7 版本功能历史回顾 ===" -ForegroundColor Yellow
Write-Host "v0.0.0.7-v0.0.0.10: 单机API服务 ✅" -ForegroundColor Green
Write-Host "v0.0.1.0: Docker NATS集群基础 ✅" -ForegroundColor Green
Write-Host "v0.0.1.1: NATS消息路由基础 ✅" -ForegroundColor Green
Write-Host "v0.0.1.2: 集成测试问题修复 ✅" -ForegroundColor Green
Write-Host "v0.0.1.3: 节点服务发现功能 ✅" -ForegroundColor Green
Write-Host "v0.0.1.4: Designer节点服务 ✅" -ForegroundColor Green
Write-Host "v0.0.1.5: Validator节点服务 ✅" -ForegroundColor Green
Write-Host "v0.0.1.6: 架构优化和类型冲突解决 ✅" -ForegroundColor Green
Write-Host "v0.0.1.7: 分布式任务调度系统 🎯 当前版本" -ForegroundColor Cyan
Write-Host ""

# 定义测试类别和优先级
$TestCategories = @{
    "Critical" = @{
        "Core" = @(
            "FlowCustomV1.Tests.Scheduling.TaskDistributionServiceTests",
            "FlowCustomV1.Tests.Scheduling.TaskExecutionTrackerTests"
        )
        "Integration" = @(
            "FlowCustomV1.Tests.Integration.ComprehensiveDistributedSystemTests"
        )
        "MultiNode" = @(
            "FlowCustomV1.Tests.Scenarios.MultiNodeClusterScenarioTests"
        )
    }
    "High" = @{
        "LoadBalancing" = @(
            "FlowCustomV1.Tests.Scheduling.LoadBalancingStrategyTests"
        )
        "Performance" = @(
            "FlowCustomV1.Tests.Performance.TaskSchedulingBenchmarkTests"
        )
        "Resilience" = @(
            "FlowCustomV1.Tests.Resilience.TaskSchedulingResilienceTests"
        )
    }
    "Medium" = @{
        "Suite" = @(
            "FlowCustomV1.Tests.TestSuites.DistributedTaskSchedulingTestSuite"
        )
    }
}

# 构建测试项目
Write-Host "=== 构建测试项目 ===" -ForegroundColor Yellow
try {
    dotnet build $TestProjectPath --configuration Release --no-restore --verbosity quiet
    if ($LASTEXITCODE -ne 0) {
        throw "构建失败"
    }
    Write-Host "✓ 测试项目构建成功" -ForegroundColor Green
}
catch {
    Write-Host "✗ 测试项目构建失败: $_" -ForegroundColor Red
    exit 1
}

# 运行测试的函数
function Run-TestCategory {
    param(
        [string]$Priority,
        [string]$Category,
        [array]$TestClasses,
        [bool]$IsParallel = $false
    )
    
    Write-Host "--- 运行 $Priority 优先级 $Category 测试 ---" -ForegroundColor Yellow
    
    $categoryResults = @{
        Priority = $Priority
        Category = $Category
        Total = 0
        Passed = 0
        Failed = 0
        Skipped = 0
        Duration = 0
        Details = @()
    }
    
    foreach ($testClass in $TestClasses) {
        Write-Host "执行: $testClass" -ForegroundColor Cyan
        
        $testArgs = @(
            "test"
            $TestProjectPath
            "--filter", "FullyQualifiedName~$testClass"
            "--logger", "trx;LogFileName=$Priority-$Category-$($testClass.Split('.')[-1]).trx"
            "--results-directory", $ReportPath
            "--verbosity", $LogLevel.ToLower()
            "--no-build"
            "--configuration", "Release"
        )
        
        if ($GenerateDetailedReport) {
            $testArgs += "--collect", "XPlat Code Coverage"
            $testArgs += "--logger", "json;LogFileName=$Priority-$Category-$($testClass.Split('.')[-1]).json"
        }
        
        try {
            $startTime = Get-Date
            & dotnet @testArgs
            $endTime = Get-Date
            $duration = ($endTime - $startTime).TotalSeconds
            
            $testDetail = @{
                TestClass = $testClass
                Duration = $duration
                Success = $LASTEXITCODE -eq 0
                StartTime = $startTime
                EndTime = $endTime
            }
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "  ✓ $($testClass.Split('.')[-1]) 通过 ($([math]::Round($duration, 2))s)" -ForegroundColor Green
                $categoryResults.Passed++
            } else {
                Write-Host "  ✗ $($testClass.Split('.')[-1]) 失败 ($([math]::Round($duration, 2))s)" -ForegroundColor Red
                $categoryResults.Failed++
            }
            
            $categoryResults.Total++
            $categoryResults.Duration += $duration
            $categoryResults.Details += $testDetail
        }
        catch {
            Write-Host "  ✗ $($testClass.Split('.')[-1]) 执行异常: $_" -ForegroundColor Red
            $categoryResults.Failed++
            $categoryResults.Total++
        }
    }
    
    # 输出类别结果
    $successRate = if ($categoryResults.Total -gt 0) { 
        ($categoryResults.Passed / $categoryResults.Total) * 100 
    } else { 0 }
    
    Write-Host ""
    Write-Host "$Priority-$Category 测试结果:" -ForegroundColor White
    Write-Host "  总计: $($categoryResults.Total)" -ForegroundColor Gray
    Write-Host "  通过: $($categoryResults.Passed)" -ForegroundColor Green
    Write-Host "  失败: $($categoryResults.Failed)" -ForegroundColor Red
    Write-Host "  成功率: $([math]::Round($successRate, 2))%" -ForegroundColor $(if ($successRate -eq 100) { "Green" } elseif ($successRate -ge 80) { "Yellow" } else { "Red" })
    Write-Host "  耗时: $([math]::Round($categoryResults.Duration, 2))s" -ForegroundColor Gray
    Write-Host ""
    
    return $categoryResults
}

# 主测试执行逻辑
$overallResults = @{
    StartTime = $TestStartTime
    EndTime = $null
    TotalDuration = $null
    Categories = @()
    Summary = @{
        Total = 0
        Passed = 0
        Failed = 0
        Skipped = 0
        SuccessRate = 0
    }
}

try {
    # 根据测试范围决定运行哪些测试
    $categoriesToRun = @()
    
    switch ($TestScope.ToLower()) {
        "all" { 
            $categoriesToRun += "Critical"
            $categoriesToRun += "High"
            if ($IncludeStressTests -or $IncludeScenarioTests) {
                $categoriesToRun += "Medium"
            }
        }
        "critical" { $categoriesToRun = @("Critical") }
        "high" { $categoriesToRun = @("High") }
        "core" { $categoriesToRun = @("Critical") }
        default { 
            Write-Host "未知的测试范围: $TestScope" -ForegroundColor Red
            Write-Host "支持的范围: All, Critical, High, Core" -ForegroundColor Yellow
            exit 1
        }
    }
    
    # 运行选定的测试类别
    foreach ($priority in $categoriesToRun) {
        if ($TestCategories.ContainsKey($priority)) {
            foreach ($categoryName in $TestCategories[$priority].Keys) {
                $testClasses = $TestCategories[$priority][$categoryName]
                
                # 根据类别决定是否跳过
                if ($categoryName -eq "Performance" -and -not $IncludeStressTests) {
                    Write-Host "跳过性能测试 (使用 -IncludeStressTests 启用)" -ForegroundColor Yellow
                    continue
                }
                
                if ($categoryName -eq "MultiNode" -and -not $IncludeScenarioTests) {
                    Write-Host "跳过场景测试 (使用 -IncludeScenarioTests 启用)" -ForegroundColor Yellow
                    continue
                }
                
                $categoryResult = Run-TestCategory -Priority $priority -Category $categoryName -TestClasses $testClasses -IsParallel $RunInParallel
                $overallResults.Categories += $categoryResult
                
                $overallResults.Summary.Total += $categoryResult.Total
                $overallResults.Summary.Passed += $categoryResult.Passed
                $overallResults.Summary.Failed += $categoryResult.Failed
                $overallResults.Summary.Skipped += $categoryResult.Skipped
            }
        }
    }
}
catch {
    Write-Host "测试执行过程中发生错误: $_" -ForegroundColor Red
    exit 1
}

$overallResults.EndTime = Get-Date
$overallResults.TotalDuration = $overallResults.EndTime - $overallResults.StartTime
$overallResults.Summary.SuccessRate = if ($overallResults.Summary.Total -gt 0) { 
    ($overallResults.Summary.Passed / $overallResults.Summary.Total) * 100 
} else { 0 }

# 生成详细测试报告
if ($GenerateDetailedReport) {
    Write-Host "=== 生成详细测试报告 ===" -ForegroundColor Yellow
    
    $reportContent = @"
# FlowCustomV1 v0.0.1.7 里程碑版本测试报告

## 版本信息
- **版本**: v0.0.1.7 - 分布式任务调度系统
- **测试日期**: $($TestStartTime.ToString('yyyy-MM-dd'))
- **测试开始时间**: $($overallResults.StartTime.ToString('yyyy-MM-dd HH:mm:ss'))
- **测试结束时间**: $($overallResults.EndTime.ToString('yyyy-MM-dd HH:mm:ss'))
- **总耗时**: $([math]::Round($overallResults.TotalDuration.TotalMinutes, 2)) 分钟

## 功能历史回顾
本版本是FlowCustomV1项目的重要里程碑，完成了从单机API服务到分布式集群架构的完整转变：

### 已完成的核心功能
- ✅ **v0.0.0.7-v0.0.0.10**: 单机工作流引擎和RESTful API
- ✅ **v0.0.1.0**: Docker NATS集群基础设施
- ✅ **v0.0.1.1**: NATS消息路由和通信机制
- ✅ **v0.0.1.2**: 系统稳定性和集成测试修复
- ✅ **v0.0.1.3**: 分布式节点发现和注册
- ✅ **v0.0.1.4**: Designer节点专业化服务
- ✅ **v0.0.1.5**: Validator节点专业化服务
- ✅ **v0.0.1.6**: 架构优化和类型冲突解决
- ✅ **v0.0.1.7**: 分布式任务调度和负载均衡

## 测试结果概览
- **总测试数**: $($overallResults.Summary.Total)
- **通过测试**: $($overallResults.Summary.Passed)
- **失败测试**: $($overallResults.Summary.Failed)
- **跳过测试**: $($overallResults.Summary.Skipped)
- **成功率**: $([math]::Round($overallResults.Summary.SuccessRate, 2))%

## 分类测试结果
"@

    foreach ($category in $overallResults.Categories) {
        $categorySuccessRate = if ($category.Total -gt 0) { 
            ($category.Passed / $category.Total) * 100 
        } else { 0 }
        
        $reportContent += @"

### $($category.Priority) 优先级 - $($category.Category) 测试
- **总计**: $($category.Total) 个测试
- **通过**: $($category.Passed) 个
- **失败**: $($category.Failed) 个
- **成功率**: $([math]::Round($categorySuccessRate, 2))%
- **耗时**: $([math]::Round($category.Duration, 2)) 秒

#### 详细结果
"@
        
        foreach ($detail in $category.Details) {
            $status = if ($detail.Success) { "✅ 通过" } else { "❌ 失败" }
            $reportContent += "- $($detail.TestClass.Split('.')[-1]): $status ($([math]::Round($detail.Duration, 2))s)`n"
        }
    }
    
    $reportContent += @"

## 测试环境信息
- **.NET 版本**: $(dotnet --version)
- **操作系统**: $([System.Environment]::OSVersion.VersionString)
- **机器名**: $([System.Environment]::MachineName)
- **处理器数**: $([System.Environment]::ProcessorCount)
- **测试范围**: $TestScope
- **包含压力测试**: $IncludeStressTests
- **包含场景测试**: $IncludeScenarioTests

## 质量评估

### 核心功能稳定性
基于测试结果，v0.0.1.7版本的核心功能稳定性评估：
- **分布式任务调度**: $(if ($overallResults.Summary.SuccessRate -ge 95) { "优秀" } elseif ($overallResults.Summary.SuccessRate -ge 85) { "良好" } else { "需要改进" })
- **多节点协作**: $(if ($overallResults.Summary.SuccessRate -ge 90) { "稳定" } else { "需要优化" })
- **负载均衡**: $(if ($overallResults.Summary.SuccessRate -ge 90) { "有效" } else { "需要调优" })
- **故障恢复**: $(if ($overallResults.Summary.SuccessRate -ge 85) { "可靠" } else { "需要加强" })

### 建议
$(if ($overallResults.Summary.SuccessRate -ge 95) {
"🎉 恭喜！v0.0.1.7版本测试表现优秀，可以考虑发布。"
} elseif ($overallResults.Summary.SuccessRate -ge 85) {
"⚠️ v0.0.1.7版本基本稳定，建议修复失败的测试后发布。"
} else {
"🚨 v0.0.1.7版本存在较多问题，建议深入分析失败原因并修复后再次测试。"
})

---
*报告生成时间: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')*
"@
    
    $reportPath = Join-Path $ReportPath "v0.0.1.7-milestone-test-report.md"
    $reportContent | Out-File -FilePath $reportPath -Encoding UTF8
    Write-Host "✓ 详细测试报告已生成: $reportPath" -ForegroundColor Green
}

# 输出最终结果
Write-Host "================================================================" -ForegroundColor Green
Write-Host "v0.0.1.7 里程碑版本测试完成" -ForegroundColor Green
Write-Host "================================================================" -ForegroundColor Green
Write-Host "结束时间: $($overallResults.EndTime.ToString('yyyy-MM-dd HH:mm:ss'))" -ForegroundColor Gray
Write-Host "总耗时: $([math]::Round($overallResults.TotalDuration.TotalMinutes, 2)) 分钟" -ForegroundColor Gray
Write-Host ""

Write-Host "最终测试结果:" -ForegroundColor White
Write-Host "  总测试数: $($overallResults.Summary.Total)" -ForegroundColor Gray
Write-Host "  通过: $($overallResults.Summary.Passed)" -ForegroundColor Green
Write-Host "  失败: $($overallResults.Summary.Failed)" -ForegroundColor Red
Write-Host "  跳过: $($overallResults.Summary.Skipped)" -ForegroundColor Yellow
Write-Host "  成功率: $([math]::Round($overallResults.Summary.SuccessRate, 2))%" -ForegroundColor $(
    if ($overallResults.Summary.SuccessRate -eq 100) { "Green" } 
    elseif ($overallResults.Summary.SuccessRate -ge 90) { "Yellow" } 
    else { "Red" }
)

# 里程碑评估
Write-Host ""
Write-Host "=== v0.0.1.7 里程碑评估 ===" -ForegroundColor Cyan
if ($overallResults.Summary.SuccessRate -ge 95) {
    Write-Host "🎉 里程碑达成！分布式任务调度系统功能完整，质量优秀！" -ForegroundColor Green
    Write-Host "   建议：可以开始准备v0.0.1.8版本的开发计划" -ForegroundColor Green
} elseif ($overallResults.Summary.SuccessRate -ge 85) {
    Write-Host "✅ 里程碑基本达成，分布式任务调度系统核心功能稳定" -ForegroundColor Yellow
    Write-Host "   建议：修复失败的测试用例，完善系统稳定性" -ForegroundColor Yellow
} else {
    Write-Host "⚠️ 里程碑未完全达成，需要进一步优化和修复" -ForegroundColor Red
    Write-Host "   建议：重点关注失败的测试用例，确保核心功能稳定" -ForegroundColor Red
}

# 设置退出代码
if ($overallResults.Summary.Failed -eq 0) {
    Write-Host ""
    Write-Host "🚀 v0.0.1.7版本测试全部通过，准备就绪！" -ForegroundColor Green
    exit 0
} else {
    Write-Host ""
    Write-Host "🔧 发现 $($overallResults.Summary.Failed) 个失败测试，需要修复" -ForegroundColor Red
    exit 1
}
