using System.Collections.Concurrent;
using FlowCustomV1.Core.Models.Workflow;
using CoreWorkflowExecutionContext = FlowCustomV1.Core.Models.Workflow.WorkflowExecutionContext;

namespace FlowCustomV1.Engine.Context;

/// <summary>
/// 引擎工作流执行上下文
/// 管理单个工作流实例的执行状态和数据（Engine层专用）
/// 使用组合模式包含Core层的WorkflowExecutionContext
/// </summary>
public class EngineWorkflowContext
{
    /// <summary>
    /// Core层工作流执行上下文（组合）
    /// </summary>
    public CoreWorkflowExecutionContext CoreContext { get; set; } = new();

    /// <summary>
    /// 执行唯一标识符（便捷访问）
    /// </summary>
    public string ExecutionId
    {
        get => CoreContext.ExecutionId;
        set => CoreContext.ExecutionId = value;
    }

    /// <summary>
    /// 工作流唯一标识符（便捷访问）
    /// </summary>
    public string WorkflowId
    {
        get => CoreContext.WorkflowId;
        set => CoreContext.WorkflowId = value;
    }

    /// <summary>
    /// 工作流定义（便捷访问）
    /// </summary>
    public WorkflowDefinition Definition
    {
        get => CoreContext.Definition;
        set => CoreContext.Definition = value;
    }

    /// <summary>
    /// 当前执行状态（便捷访问）
    /// </summary>
    public WorkflowExecutionState State
    {
        get => CoreContext.State;
        set => CoreContext.State = value;
    }

    /// <summary>
    /// 工作流共享数据（便捷访问）
    /// </summary>
    public ConcurrentDictionary<string, object> WorkflowData
    {
        get => CoreContext.WorkflowData;
        set => CoreContext.WorkflowData = value;
    }

    /// <summary>
    /// 系统执行上下文引用
    /// </summary>
    public SystemExecutionContext SystemContext { get; set; } = default!;

    /// <summary>
    /// 工作流级取消令牌
    /// </summary>
    public CancellationToken WorkflowCancellationToken { get; set; } = default;

    /// <summary>
    /// 执行开始时间（便捷访问）
    /// </summary>
    public DateTime StartedAt
    {
        get => CoreContext.StartedAt;
        set => CoreContext.StartedAt = value;
    }

    /// <summary>
    /// 执行完成时间（便捷访问）
    /// </summary>
    public DateTime? CompletedAt
    {
        get => CoreContext.CompletedAt;
        set => CoreContext.CompletedAt = value;
    }

    /// <summary>
    /// 输入数据（便捷访问）
    /// </summary>
    public ConcurrentDictionary<string, object> InputData
    {
        get => CoreContext.InputData;
        set => CoreContext.InputData = value;
    }

    /// <summary>
    /// 输出数据（便捷访问）
    /// </summary>
    public ConcurrentDictionary<string, object> OutputData
    {
        get => CoreContext.OutputData;
        set => CoreContext.OutputData = value;
    }

    /// <summary>
    /// 节点执行状态映射（便捷访问）
    /// </summary>
    public ConcurrentDictionary<string, NodeExecutionState> NodeStates
    {
        get => CoreContext.NodeStates;
        set => CoreContext.NodeStates = value;
    }

    /// <summary>
    /// 节点执行结果映射（便捷访问）
    /// </summary>
    public ConcurrentDictionary<string, NodeExecutionResult> NodeResults
    {
        get => CoreContext.NodeResults;
        set => CoreContext.NodeResults = value;
    }

    /// <summary>
    /// 执行统计信息（便捷访问）
    /// </summary>
    public WorkflowExecutionStats Stats
    {
        get => CoreContext.Stats;
        set => CoreContext.Stats = value;
    }

    /// <summary>
    /// 执行元数据（便捷访问）
    /// </summary>
    public Dictionary<string, object> Metadata
    {
        get => CoreContext.Metadata;
        set => CoreContext.Metadata = value;
    }

    /// <summary>
    /// 执行时长
    /// </summary>
    public TimeSpan Duration => CompletedAt?.Subtract(StartedAt) ?? DateTime.UtcNow.Subtract(StartedAt);

    /// <summary>
    /// 是否已完成
    /// </summary>
    public bool IsCompleted => State == WorkflowExecutionState.Completed || 
                              State == WorkflowExecutionState.Failed || 
                              State == WorkflowExecutionState.Cancelled;

    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess => State == WorkflowExecutionState.Completed;

    /// <summary>
    /// 获取工作流数据
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    /// <param name="key">数据键</param>
    /// <param name="defaultValue">默认值</param>
    /// <returns>数据值</returns>
    public T GetWorkflowData<T>(string key, T defaultValue = default!)
    {
        if (WorkflowData.TryGetValue(key, out var value))
        {
            try
            {
                if (value is T directValue)
                    return directValue;

                return (T)Convert.ChangeType(value, typeof(T));
            }
            catch
            {
                return defaultValue;
            }
        }
        return defaultValue;
    }

    /// <summary>
    /// 设置工作流数据
    /// </summary>
    /// <param name="key">数据键</param>
    /// <param name="value">数据值</param>
    public void SetWorkflowData(string key, object value)
    {
        WorkflowData[key] = value;
    }

    /// <summary>
    /// 更新节点状态
    /// </summary>
    /// <param name="nodeId">节点ID</param>
    /// <param name="state">节点状态</param>
    public void UpdateNodeState(string nodeId, NodeExecutionState state)
    {
        NodeStates[nodeId] = state;
        
        // 更新统计信息
        UpdateExecutionStats();
    }

    /// <summary>
    /// 获取节点状态
    /// </summary>
    /// <param name="nodeId">节点ID</param>
    /// <returns>节点状态</returns>
    public NodeExecutionState GetNodeState(string nodeId)
    {
        return NodeStates.TryGetValue(nodeId, out var state) ? state : NodeExecutionState.NotStarted;
    }

    /// <summary>
    /// 设置节点执行结果
    /// </summary>
    /// <param name="nodeId">节点ID</param>
    /// <param name="result">执行结果</param>
    public void SetNodeResult(string nodeId, NodeExecutionResult result)
    {
        NodeResults[nodeId] = result;
        UpdateNodeState(nodeId, result.State);
    }

    /// <summary>
    /// 获取节点执行结果
    /// </summary>
    /// <param name="nodeId">节点ID</param>
    /// <returns>执行结果</returns>
    public NodeExecutionResult? GetNodeResult(string nodeId)
    {
        return NodeResults.TryGetValue(nodeId, out var result) ? result : null;
    }

    /// <summary>
    /// 更新执行统计信息
    /// </summary>
    private void UpdateExecutionStats()
    {
        Stats.TotalNodes = Definition.Nodes.Count;
        Stats.ExecutedNodes = NodeStates.Count(kvp => kvp.Value != NodeExecutionState.NotStarted);
        Stats.SuccessfulNodes = NodeStates.Count(kvp => kvp.Value == NodeExecutionState.Completed);
        Stats.FailedNodes = NodeStates.Count(kvp => kvp.Value == NodeExecutionState.Failed);
        Stats.SkippedNodes = NodeStates.Count(kvp => kvp.Value == NodeExecutionState.Skipped);
    }

    /// <summary>
    /// 创建工作流执行结果
    /// </summary>
    /// <returns>执行结果</returns>
    public WorkflowExecutionResult CreateResult()
    {
        // 直接使用CoreContext的CreateResult方法
        return CoreContext.CreateResult();
    }

    /// <summary>
    /// 创建工作流上下文的副本
    /// </summary>
    /// <returns>工作流上下文副本</returns>
    public EngineWorkflowContext Clone()
    {
        return new EngineWorkflowContext
        {
            CoreContext = CoreContext.Clone(), // 克隆Core上下文
            SystemContext = SystemContext,
            WorkflowCancellationToken = WorkflowCancellationToken
        };
    }

    /// <summary>
    /// 获取工作流上下文的字符串表示
    /// </summary>
    /// <returns>工作流上下文字符串</returns>
    public override string ToString()
    {
        return $"WorkflowContext[{ExecutionId}] {WorkflowId} - {State}, Duration: {Duration:hh\\:mm\\:ss}, Nodes: {Stats.ExecutedNodes}/{Stats.TotalNodes}";
    }
}
