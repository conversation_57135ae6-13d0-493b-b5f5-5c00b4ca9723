using FlowCustomV1.Core.Models.Cluster;
using FlowCustomV1.Core.Models.Scheduling;
using FlowCustomV1.Core.Interfaces.Scheduling;
using Microsoft.Extensions.Logging;

namespace FlowCustomV1.Infrastructure.Services.Scheduling;

/// <summary>
/// TaskDistributionService 工具方法部分类
/// </summary>
public partial class TaskDistributionService
{
    #region 统计更新

    /// <summary>
    /// 更新分发统计信息
    /// </summary>
    private void UpdateDistributionStatistics(TaskDistributionResult result, NodeInfo selectedNode)
    {
        Interlocked.Increment(ref _totalDistributions);
        if (result.IsSuccess)
        {
            Interlocked.Increment(ref _successfulDistributions);
        }

        // 更新节点统计
        _nodeStats.AddOrUpdate(selectedNode.NodeId, 
            new NodeDistributionStatistics
            {
                NodeId = selectedNode.NodeId,
                NodeName = selectedNode.NodeName,
                AssignedTasks = 1,
                CompletedTasks = result.IsSuccess ? 1 : 0,
                FailedTasks = result.IsSuccess ? 0 : 1,
                SelectionCount = 1,
                TotalSelectionScore = result.SelectionScore
            },
            (key, existing) =>
            {
                existing.AssignedTasks++;
                if (result.IsSuccess)
                    existing.CompletedTasks++;
                else
                    existing.FailedTasks++;
                existing.SelectionCount++;
                existing.TotalSelectionScore += result.SelectionScore;
                return existing;
            });

        // 更新策略统计
        _strategyStats.AddOrUpdate(result.Strategy,
            new StrategyStatistics
            {
                UsageCount = 1,
                SuccessCount = result.IsSuccess ? 1 : 0,
                FailureCount = result.IsSuccess ? 0 : 1,
                AverageTimeMs = result.DistributionTimeMs,
                AverageSelectionScore = result.SelectionScore
            },
            (key, existing) =>
            {
                existing.UsageCount++;
                if (result.IsSuccess)
                    existing.SuccessCount++;
                else
                    existing.FailureCount++;
                
                // 更新平均值
                existing.AverageTimeMs = (existing.AverageTimeMs * (existing.UsageCount - 1) + result.DistributionTimeMs) / existing.UsageCount;
                existing.AverageSelectionScore = (existing.AverageSelectionScore * (existing.UsageCount - 1) + result.SelectionScore) / existing.UsageCount;
                return existing;
            });

        // 添加到最近分发记录
        _recentDistributions.Enqueue(result);
        
        // 保持队列大小
        while (_recentDistributions.Count > _config.PredictionHistoryWindowSize)
        {
            _recentDistributions.TryDequeue(out _);
        }
    }

    #endregion

    #region 负载均衡辅助方法

    /// <summary>
    /// 计算标准差
    /// </summary>
    private double CalculateStandardDeviation(IReadOnlyList<double> values, double mean)
    {
        if (values.Count <= 1) return 0;

        var sumOfSquares = values.Sum(value => Math.Pow(value - mean, 2));
        return Math.Sqrt(sumOfSquares / values.Count);
    }

    /// <summary>
    /// 计算负载均衡评分
    /// </summary>
    private double CalculateBalanceScore(IReadOnlyList<double> loadScores)
    {
        if (loadScores.Count <= 1) return 1.0;

        var mean = loadScores.Average();
        var stdDev = CalculateStandardDeviation(loadScores, mean);
        
        // 标准差越小，均衡度越高
        var maxStdDev = mean; // 最大可能的标准差
        if (maxStdDev == 0) return 1.0;
        
        return Math.Max(0, 1.0 - (stdDev / maxStdDev));
    }

    /// <summary>
    /// 生成重新平衡建议
    /// </summary>
    private List<RebalancingRecommendation> GenerateRebalancingRecommendations(
        List<NodeLoadSummary> nodeLoadSummaries, 
        double balanceScore)
    {
        var recommendations = new List<RebalancingRecommendation>();

        if (balanceScore >= _config.MinBalanceScore)
            return recommendations;

        var sortedNodes = nodeLoadSummaries.OrderByDescending(n => n.LoadScore).ToList();
        var highLoadNodes = sortedNodes.Take(sortedNodes.Count / 3).ToList();
        var lowLoadNodes = sortedNodes.Skip(sortedNodes.Count * 2 / 3).ToList();

        foreach (var highLoadNode in highLoadNodes)
        {
            foreach (var lowLoadNode in lowLoadNodes)
            {
                var loadDifference = highLoadNode.LoadScore - lowLoadNode.LoadScore;
                if (loadDifference > _config.RebalancingThreshold * 100)
                {
                    var tasksToMove = Math.Max(1, (int)(loadDifference / 20)); // 简化计算
                    
                    recommendations.Add(new RebalancingRecommendation
                    {
                        Type = RebalancingType.TaskMigration,
                        SourceNodeId = highLoadNode.NodeId,
                        TargetNodeId = lowLoadNode.NodeId,
                        TaskCount = tasksToMove,
                        ExpectedImprovement = loadDifference / 2,
                        Priority = CalculateRecommendationPriority(loadDifference),
                        Reason = $"Load difference: {loadDifference:F1}%",
                        EstimatedExecutionTimeMs = tasksToMove * 1000 // 估算每个任务迁移需要1秒
                    });
                }
            }
        }

        return recommendations.OrderByDescending(r => r.Priority).Take(_config.MaxRebalancingOperations).ToList();
    }

    /// <summary>
    /// 计算建议优先级
    /// </summary>
    private int CalculateRecommendationPriority(double loadDifference)
    {
        if (loadDifference > 80) return 10;
        if (loadDifference > 60) return 8;
        if (loadDifference > 40) return 6;
        if (loadDifference > 20) return 4;
        return 2;
    }

    /// <summary>
    /// 执行重新平衡操作
    /// </summary>
    private async Task<RebalancingOperation> ExecuteRebalancingOperation(
        RebalancingRecommendation recommendation, 
        CancellationToken cancellationToken)
    {
        var operation = new RebalancingOperation
        {
            Type = recommendation.Type,
            SourceNodeId = recommendation.SourceNodeId,
            TargetNodeId = recommendation.TargetNodeId,
            Status = OperationStatus.Running,
            StartTime = DateTime.UtcNow
        };

        try
        {
            _logger.LogInformation("Executing rebalancing operation: {Type} from {SourceNode} to {TargetNode}",
                operation.Type, operation.SourceNodeId, operation.TargetNodeId);

            // 这里应该实现实际的任务迁移逻辑
            // 暂时模拟操作
            await Task.Delay(1000, cancellationToken);

            operation.Status = OperationStatus.Completed;
            operation.EndTime = DateTime.UtcNow;

            _logger.LogInformation("Rebalancing operation completed successfully: {OperationId}", operation.OperationId);
        }
        catch (Exception ex)
        {
            operation.Status = OperationStatus.Failed;
            operation.EndTime = DateTime.UtcNow;
            operation.ErrorMessage = ex.Message;

            _logger.LogError(ex, "Rebalancing operation failed: {OperationId}", operation.OperationId);
        }

        return operation;
    }

    /// <summary>
    /// 计算改善程度
    /// </summary>
    private double CalculateImprovementScore(LoadBalancingStatus beforeStatus, LoadBalancingStatus afterStatus)
    {
        if (beforeStatus.BalanceScore >= afterStatus.BalanceScore)
            return 0;

        var improvement = afterStatus.BalanceScore - beforeStatus.BalanceScore;
        var maxPossibleImprovement = 1.0 - beforeStatus.BalanceScore;

        return maxPossibleImprovement > 0 ? improvement / maxPossibleImprovement : 0;
    }

    #endregion

    #region 事件触发

    /// <summary>
    /// 触发任务分发事件
    /// </summary>
    private void OnTaskDistributed(TaskDistributedEventArgs args)
    {
        try
        {
            TaskDistributed?.Invoke(this, args);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error invoking TaskDistributed event");
        }
    }

    /// <summary>
    /// 触发任务重新分发事件
    /// </summary>
    private void OnTaskRedistributed(TaskRedistributedEventArgs args)
    {
        try
        {
            TaskRedistributed?.Invoke(this, args);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error invoking TaskRedistributed event");
        }
    }

    /// <summary>
    /// 触发负载重新平衡事件
    /// </summary>
    private void OnLoadRebalanced(LoadRebalancedEventArgs args)
    {
        try
        {
            LoadRebalanced?.Invoke(this, args);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error invoking LoadRebalanced event");
        }
    }

    #endregion

    #region 辅助工具方法

    /// <summary>
    /// 获取选择原因
    /// </summary>
    private string GetSelectionReason(TaskDistributionStrategy strategy, NodeInfo selectedNode)
    {
        return strategy switch
        {
            TaskDistributionStrategy.SmartLoad => $"Smart selection based on comprehensive scoring (score: {CalculateNodeScore(selectedNode, new TaskRequirements(), strategy):F2})",
            TaskDistributionStrategy.LeastLoad => $"Lowest load node (load: {selectedNode.Load.LoadScore:F1}%)",
            TaskDistributionStrategy.FastestResponse => $"Fastest response time (avg: {selectedNode.Load.AverageResponseTimeMs:F0}ms)",
            TaskDistributionStrategy.RoundRobin => "Round-robin selection",
            TaskDistributionStrategy.Random => "Random selection",
            TaskDistributionStrategy.CapabilityBased => "Best capability match",
            TaskDistributionStrategy.GeographyBased => "Geographic proximity",
            TaskDistributionStrategy.WeightedRoundRobin => "Weighted round-robin selection",
            _ => "Default selection"
        };
    }

    /// <summary>
    /// 清理过期统计数据
    /// </summary>
    private void CleanupExpiredStatistics()
    {
        var cutoffTime = DateTime.UtcNow.AddDays(-_config.StatisticsRetentionDays);
        
        // 清理过期的分发记录
        var expiredCount = 0;
        while (_recentDistributions.TryPeek(out var oldestRecord) && oldestRecord.DistributedAt < cutoffTime)
        {
            if (_recentDistributions.TryDequeue(out _))
                expiredCount++;
        }

        if (expiredCount > 0)
        {
            _logger.LogDebug("Cleaned up {ExpiredCount} expired distribution records", expiredCount);
        }
    }

    /// <summary>
    /// 验证任务要求
    /// </summary>
    private bool ValidateTaskRequirements(TaskRequirements requirements)
    {
        if (requirements.MinCpuCores < 0 || requirements.MinMemoryMB < 0 || requirements.MinDiskSpaceMB < 0)
            return false;

        if (requirements.MaxNetworkLatencyMs <= 0)
            return false;

        return true;
    }

    /// <summary>
    /// 记录性能指标
    /// </summary>
    private void RecordPerformanceMetrics(string operation, long durationMs, bool success)
    {
        if (!_config.EnablePerformanceMonitoring)
            return;

        _logger.LogDebug("Performance: {Operation} took {Duration}ms, Success: {Success}", 
            operation, durationMs, success);
    }

    #endregion
}
