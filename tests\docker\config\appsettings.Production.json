{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "FlowCustomV1": "Information", "System.Net.Http.HttpClient": "Warning"}, "Console": {"IncludeScopes": false, "TimestampFormat": "yyyy-MM-dd HH:mm:ss.fff ", "LogToStandardErrorThreshold": "Error"}}, "AllowedHosts": "*", "Nats": {"Servers": ["nats://nats-1:4222", "nats://nats-2:4222", "nats://nats-3:4222"], "ConnectionName": "FlowCustomV1-Production-Node", "Username": "<PERSON><PERSON><PERSON>", "Password": "flowcustom_production_password", "ConnectionTimeoutSeconds": 30, "ReconnectIntervalSeconds": 5, "MaxReconnectAttempts": 20, "EnableAutoReconnect": true, "PingIntervalSeconds": 120, "MaxPingsOutstanding": 2, "EnableVerboseLogging": false, "JetStream": {"Enabled": true, "Domain": "flowcustom-production", "ApiPrefix": "$JS.API", "DefaultStream": {"Name": "FLOWCUSTOM", "Subjects": ["flowcustom.>"], "Storage": "file", "MaxMessages": 10000000, "MaxBytes": 10737418240, "MaxAgeSeconds": 86400, "Replicas": 3}}, "Streams": [{"Name": "FLOWCUSTOM", "Subjects": ["flowcustom.>"], "Storage": "file", "MaxMessages": 10000000, "MaxBytes": 10737418240, "MaxAgeSeconds": 86400, "Replicas": 3}, {"Name": "FLOWCUSTOM_AUDIT", "Subjects": ["flowcustom.audit.>"], "Storage": "file", "MaxMessages": 5000000, "MaxBytes": 5368709120, "MaxAgeSeconds": 2592000, "Replicas": 3}]}, "NodeDiscovery": {"ClusterName": "FlowCustomV1-Production", "HeartbeatIntervalSeconds": 30, "NodeTimeoutSeconds": 120, "NodeCleanupIntervalSeconds": 300, "DiscoveryTimeoutSeconds": 10, "EnableAutoRegistration": true, "EnableHeartbeat": true, "EnableNodeCleanup": true, "MaxRetryAttempts": 5, "RetryIntervalSeconds": 5, "NodeRole": "ProductionNode", "NodeTags": ["production", "high-availability"], "CapabilityTags": ["workflow-execution", "task-processing", "high-performance"], "EnableVerboseLogging": false}, "TaskDistribution": {"MaxCandidateNodes": 50, "MaxConcurrentDistributions": 20, "MinBalanceScore": 0.8, "MaxRebalancingOperations": 10, "NodeSelectionTimeoutMs": 5000, "TaskDistributionTimeoutMs": 30000, "AutoRebalancingEnabled": true, "RebalancingThreshold": 0.2, "RebalancingIntervalSeconds": 300, "EnablePerformanceMonitoring": true, "EnableDetailedLogging": false, "StatisticsRetentionDays": 30, "PredictionHistoryWindowSize": 10000, "HealthCheckWeight": 0.3, "LoadWeight": 0.3, "PerformanceWeight": 0.25, "GeographyWeight": 0.15, "DefaultStrategy": "SmartLoad", "EnableFailover": true, "FailoverTimeoutMs": 15000, "MaxRetryAttempts": 5, "RetryDelayMs": 2000}, "TaskTracking": {"CleanupIntervalMinutes": 60, "StatisticsUpdateIntervalSeconds": 30, "MaxRecentCompletedTasks": 10000, "AutoCleanupOnCompletion": false, "TaskStateRetentionDays": 30, "EnableDetailedLogging": false, "EnablePerformanceMonitoring": true, "MaxConcurrentTrackedTasks": 50000, "TimeoutCheckIntervalSeconds": 60, "EnableTimeoutCheck": true, "DefaultTaskTimeoutMs": 1800000, "EnableProgressTracking": true, "ProgressUpdateMinIntervalMs": 5000, "EnableResourceMonitoring": true, "ResourceMonitoringIntervalSeconds": 30, "EnableEventPublishing": true, "EventTopicPrefix": "task.tracking", "BatchOperationSize": 1000, "EnableDependencyTracking": true, "MaxDependencyDepth": 20, "EnableRetryTracking": true, "MaxRetryHistoryCount": 20, "EnableExecutionLogging": true, "MaxExecutionLogCount": 10000}, "WorkflowDesigner": {"EnableCollaboration": true, "MaxConcurrentDesigners": 50, "AutoSaveIntervalSeconds": 30, "VersionHistoryRetentionDays": 90, "MaxVersionsPerWorkflow": 100, "EnableRealTimeSync": true, "ConflictResolutionStrategy": "LastWriterWins", "EnableDesignValidation": true, "ValidationTimeoutMs": 30000, "EnableDesignTemplates": true, "TemplateStoragePath": "/app/data/templates"}, "WorkflowValidator": {"EnableDistributedValidation": true, "MaxConcurrentValidations": 20, "ValidationTimeoutMs": 60000, "EnablePerformanceAnalysis": true, "EnableSecurityValidation": true, "EnableComplianceCheck": true, "MaxValidationDepth": 50, "EnableCacheValidationResults": true, "ValidationCacheExpirationMinutes": 120, "EnableValidationMetrics": true, "MetricsRetentionDays": 30}, "TaskExecution": {"MaxConcurrentTasks": 100, "TaskTimeoutMs": 1800000, "EnableResourceMonitoring": true, "ResourceMonitoringIntervalMs": 10000, "EnableTaskIsolation": true, "TaskWorkingDirectory": "/app/data/tasks", "EnableTaskLogging": true, "TaskLogRetentionDays": 30, "MaxTaskLogSizeMB": 1000, "EnableTaskMetrics": true, "MetricsPublishIntervalMs": 30000}, "HealthChecks": {"EnableHealthChecks": true, "HealthCheckIntervalSeconds": 30, "HealthCheckTimeoutMs": 15000, "EnableDetailedHealthInfo": false, "EnableDependencyHealthChecks": true, "HealthCheckEndpoint": "/health", "ReadinessCheckEndpoint": "/ready", "LivenessCheckEndpoint": "/live"}, "Monitoring": {"EnableMetrics": true, "MetricsEndpoint": "/metrics", "EnableTracing": true, "TracingSamplingRate": 0.01, "EnableLogging": true, "LogLevel": "Information", "EnablePerformanceCounters": true, "MetricsRetentionDays": 30}, "Security": {"EnableAuthentication": true, "EnableAuthorization": true, "EnableHttps": true, "EnableCors": true, "AllowedOrigins": ["https://flowcustom.example.com"], "EnableRateLimiting": true, "RateLimitRequests": 10000, "RateLimitWindowMinutes": 1}, "Database": {"Provider": "MySQL", "ConnectionString": "Server=mysql-cluster;Database=flowcustom_production;Uid=flowcustom;Pwd=${MYSQL_PASSWORD};", "CommandTimeout": 60, "EnableRetryOnFailure": true, "MaxRetryCount": 5, "MaxRetryDelay": "00:01:00", "EnableSensitiveDataLogging": false, "EnableDetailedErrors": false, "MigrationsAssembly": "FlowCustomV1.Infrastructure"}, "Testing": {"Environment": "Production", "EnableTestEndpoints": false, "TestDataPath": "/app/data/test", "EnableMockServices": false, "TestTimeoutMs": 300000, "EnableTestLogging": false, "TestLogLevel": "Warning", "EnablePerformanceTesting": false, "EnableLoadTesting": false, "MaxTestConcurrency": 1}}