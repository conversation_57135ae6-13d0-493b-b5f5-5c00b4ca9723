import React from 'react';
import { Row, Col, Statistic, Button, Space, Tag, Alert, Progress, Table } from 'antd';
import { 
  ThunderboltOutlined, 
  PlayCircleOutlined, 

  CheckCircleOutlined,
  CloseCircleOutlined,
  SettingOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';

const NodeExecutor: React.FC = () => {
  // 模拟执行任务数据
  const executionTasks = [
    {
      key: '1',
      taskId: 'task-001',
      workflowName: '数据处理流水线',
      status: 'Running',
      progress: 65,
      startTime: '2025-01-13 15:30:00',
      nodeId: 'node-data-processor',
    },
    {
      key: '2',
      taskId: 'task-002',
      workflowName: 'API数据同步',
      status: 'Completed',
      progress: 100,
      startTime: '2025-01-13 15:25:00',
      nodeId: 'node-api-sync',
    },
    {
      key: '3',
      taskId: 'task-003',
      workflowName: '文件批处理',
      status: 'Failed',
      progress: 45,
      startTime: '2025-01-13 15:20:00',
      nodeId: 'node-file-processor',
    },
  ];

  const columns = [
    {
      title: '任务ID',
      dataIndex: 'taskId',
      key: 'taskId',
      render: (text: string) => (
        <code className="text-xs bg-gray-100 px-1 rounded">
          {text}
        </code>
      ),
    },
    {
      title: '工作流',
      dataIndex: 'workflowName',
      key: 'workflowName',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusMap = {
          Running: { color: 'processing', text: '运行中' },
          Completed: { color: 'success', text: '已完成' },
          Failed: { color: 'error', text: '失败' },
          Paused: { color: 'warning', text: '已暂停' },
        };
        const config = statusMap[status as keyof typeof statusMap] || { color: 'default', text: status };
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: '进度',
      dataIndex: 'progress',
      key: 'progress',
      render: (progress: number) => (
        <Progress percent={progress} size="small" />
      ),
    },
    {
      title: '当前节点',
      dataIndex: 'nodeId',
      key: 'nodeId',
      render: (nodeId: string) => (
        <code className="text-xs bg-blue-100 px-1 rounded">
          {nodeId}
        </code>
      ),
    },
    {
      title: '开始时间',
      dataIndex: 'startTime',
      key: 'startTime',
    },
  ];

  return (
    <div className="p-6">
      <div className="mb-6">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h1 className="text-2xl font-semibold text-gray-900 mb-2">
              <ThunderboltOutlined className="mr-2" />
              Executor 执行器节点
            </h1>
            <p className="text-gray-600">工作流执行和任务处理服务管理</p>
          </div>
          <Space>
            <Button icon={<SettingOutlined />}>
              节点配置
            </Button>
            <Button type="primary" icon={<ReloadOutlined />}>
              刷新状态
            </Button>
          </Space>
        </div>

        <Alert
          message="Executor 节点服务"
          description="负责工作流的实际执行、任务调度、资源管理和执行状态同步"
          type="warning"
          showIcon
          className="mb-6"
        />
      </div>

      {/* 执行统计 */}
      <Row gutter={[16, 16]} className="mb-6">
        <Col xs={24} sm={12} lg={6}>
          <ProCard>
            <Statistic
              title="运行中任务"
              value={3}
              prefix={<PlayCircleOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </ProCard>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <ProCard>
            <Statistic
              title="已完成任务"
              value={127}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </ProCard>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <ProCard>
            <Statistic
              title="失败任务"
              value={8}
              prefix={<CloseCircleOutlined />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </ProCard>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <ProCard>
            <Statistic
              title="执行容量"
              value="75%"
              prefix={<ThunderboltOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </ProCard>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        {/* 执行服务 */}
        <Col xs={24} lg={12}>
          <ProCard title="执行服务">
            <div className="space-y-4">
              <div className="flex justify-between items-center p-3 border rounded">
                <div>
                  <div className="font-medium">工作流执行引擎</div>
                  <div className="text-sm text-gray-500">核心工作流执行服务</div>
                </div>
                <Tag color="green">运行中</Tag>
              </div>
              
              <div className="flex justify-between items-center p-3 border rounded">
                <div>
                  <div className="font-medium">任务调度器</div>
                  <div className="text-sm text-gray-500">任务分发和调度管理</div>
                </div>
                <Tag color="green">运行中</Tag>
              </div>
              
              <div className="flex justify-between items-center p-3 border rounded">
                <div>
                  <div className="font-medium">资源管理器</div>
                  <div className="text-sm text-gray-500">执行资源分配和监控</div>
                </div>
                <Tag color="green">运行中</Tag>
              </div>
              
              <div className="flex justify-between items-center p-3 border rounded">
                <div>
                  <div className="font-medium">状态同步服务</div>
                  <div className="text-sm text-gray-500">执行状态实时同步</div>
                </div>
                <Tag color="green">运行中</Tag>
              </div>
              
              <div className="flex justify-between items-center p-3 border rounded">
                <div>
                  <div className="font-medium">故障恢复服务</div>
                  <div className="text-sm text-gray-500">执行失败处理和重试</div>
                </div>
                <Tag color="green">运行中</Tag>
              </div>
            </div>
          </ProCard>
        </Col>

        {/* 资源使用情况 */}
        <Col xs={24} lg={12}>
          <ProCard title="资源使用情况">
            <div className="space-y-4">
              <div>
                <div className="flex justify-between items-center mb-2">
                  <span>CPU 使用率</span>
                  <span className="font-semibold">68%</span>
                </div>
                <Progress percent={68} strokeColor="#1890ff" />
              </div>
              
              <div>
                <div className="flex justify-between items-center mb-2">
                  <span>内存使用率</span>
                  <span className="font-semibold">72%</span>
                </div>
                <Progress percent={72} strokeColor="#52c41a" />
              </div>
              
              <div>
                <div className="flex justify-between items-center mb-2">
                  <span>磁盘使用率</span>
                  <span className="font-semibold">45%</span>
                </div>
                <Progress percent={45} strokeColor="#722ed1" />
              </div>
              
              <div>
                <div className="flex justify-between items-center mb-2">
                  <span>网络使用率</span>
                  <span className="font-semibold">35%</span>
                </div>
                <Progress percent={35} strokeColor="#fa8c16" />
              </div>
              
              <div className="pt-3 border-t">
                <div className="flex justify-between items-center">
                  <span>执行槽位</span>
                  <span className="font-semibold">15 / 20</span>
                </div>
                <Progress percent={75} strokeColor="#13c2c2" className="mt-2" />
              </div>
            </div>
          </ProCard>
        </Col>
      </Row>

      {/* 当前执行任务 */}
      <Row className="mt-6">
        <Col span={24}>
          <ProCard title="当前执行任务">
            <Table
              columns={columns}
              dataSource={executionTasks}
              pagination={false}
              size="small"
            />
          </ProCard>
        </Col>
      </Row>

      {/* API 端点 */}
      <Row className="mt-6">
        <Col span={24}>
          <ProCard title="API 端点">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="border rounded p-3">
                <div className="font-medium text-blue-600">POST /api/executor/execute</div>
                <div className="text-sm text-gray-500">执行工作流</div>
              </div>
              
              <div className="border rounded p-3">
                <div className="font-medium text-green-600">GET /api/executor/status/&#123;id&#125;</div>
                <div className="text-sm text-gray-500">获取执行状态</div>
              </div>
              
              <div className="border rounded p-3">
                <div className="font-medium text-orange-600">POST /api/executor/pause/&#123;id&#125;</div>
                <div className="text-sm text-gray-500">暂停执行</div>
              </div>
              
              <div className="border rounded p-3">
                <div className="font-medium text-purple-600">POST /api/executor/resume/&#123;id&#125;</div>
                <div className="text-sm text-gray-500">恢复执行</div>
              </div>
              
              <div className="border rounded p-3">
                <div className="font-medium text-cyan-600">POST /api/executor/cancel/&#123;id&#125;</div>
                <div className="text-sm text-gray-500">取消执行</div>
              </div>
              
              <div className="border rounded p-3">
                <div className="font-medium text-pink-600">GET /api/executor/capacity</div>
                <div className="text-sm text-gray-500">获取执行容量</div>
              </div>
            </div>
          </ProCard>
        </Col>
      </Row>
    </div>
  );
};

export default NodeExecutor;
