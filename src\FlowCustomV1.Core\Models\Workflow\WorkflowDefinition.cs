using System.ComponentModel.DataAnnotations;

namespace FlowCustomV1.Core.Models.Workflow;

/// <summary>
/// 工作流定义
/// 描述工作流的结构、节点和连接关系
/// </summary>
public class WorkflowDefinition
{
    /// <summary>
    /// 工作流唯一标识符
    /// </summary>
    [Required]
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 工作流名称
    /// </summary>
    [Required]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 工作流描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 工作流版本
    /// </summary>
    [Required]
    public string Version { get; set; } = "1.0.0";

    /// <summary>
    /// 工作流作者
    /// </summary>
    public string Author { get; set; } = string.Empty;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 最后修改时间
    /// </summary>
    public DateTime LastModifiedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 是否激活
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// 是否已发布
    /// </summary>
    public bool IsPublished { get; set; } = false;

    /// <summary>
    /// 发布时间
    /// </summary>
    public DateTime? PublishedAt { get; set; }

    /// <summary>
    /// 发布者
    /// </summary>
    public string? PublishedBy { get; set; }

    /// <summary>
    /// 发布状态
    /// </summary>
    public PublishStatus PublishStatus { get; set; } = PublishStatus.Draft;

    /// <summary>
    /// 创建者
    /// </summary>
    public string? CreatedBy { get; set; }

    /// <summary>
    /// 最后修改者
    /// </summary>
    public string? LastModifiedBy { get; set; }

    /// <summary>
    /// 工作流节点列表
    /// </summary>
    public List<WorkflowNode> Nodes { get; set; } = new();

    /// <summary>
    /// 工作流连接列表
    /// </summary>
    public List<WorkflowConnection> Connections { get; set; } = new();

    /// <summary>
    /// 工作流输入参数定义
    /// </summary>
    public List<WorkflowParameterDefinition> InputParameters { get; set; } = new();

    /// <summary>
    /// 工作流输出参数定义
    /// </summary>
    public List<WorkflowParameterDefinition> OutputParameters { get; set; } = new();

    /// <summary>
    /// 工作流配置
    /// </summary>
    public WorkflowConfiguration Configuration { get; set; } = new();

    /// <summary>
    /// 工作流标签
    /// </summary>
    public HashSet<string> Tags { get; set; } = new();

    /// <summary>
    /// 工作流元数据
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();

    /// <summary>
    /// 验证工作流定义的有效性（基础验证）
    /// 注意：完整验证请使用IWorkflowValidatorService，避免重复验证逻辑
    /// </summary>
    /// <returns>验证结果</returns>
    public WorkflowValidationResult Validate()
    {
        var result = new WorkflowValidationResult
        {
            IsValid = true,
            Errors = new List<string>(),
            Warnings = new List<string>()
        };

        // 仅进行最基础的验证，避免与专门验证服务的重复
        if (string.IsNullOrWhiteSpace(WorkflowId))
        {
            result.Errors.Add("工作流ID不能为空");
            result.IsValid = false;
        }

        if (string.IsNullOrWhiteSpace(Name))
        {
            result.Errors.Add("工作流名称不能为空");
            result.IsValid = false;
        }

        if (Nodes.Count == 0)
        {
            result.Errors.Add("工作流必须包含至少一个节点");
            result.IsValid = false;
        }

        // 注意：详细的节点验证、连接验证、循环依赖检查等
        // 由专门的IWorkflowValidatorService处理，避免验证逻辑重复

        return result;
    }

    /// <summary>
    /// 获取起始节点
    /// </summary>
    /// <returns>起始节点列表</returns>
    public IEnumerable<WorkflowNode> GetStartNodes()
    {
        var targetNodeIds = Connections.Select(c => c.TargetNodeId).ToHashSet();
        return Nodes.Where(n => !targetNodeIds.Contains(n.NodeId));
    }

    /// <summary>
    /// 获取结束节点
    /// </summary>
    /// <returns>结束节点列表</returns>
    public IEnumerable<WorkflowNode> GetEndNodes()
    {
        var sourceNodeIds = Connections.Select(c => c.SourceNodeId).ToHashSet();
        return Nodes.Where(n => !sourceNodeIds.Contains(n.NodeId));
    }

    /// <summary>
    /// 获取指定节点的后续节点
    /// </summary>
    /// <param name="nodeId">节点ID</param>
    /// <returns>后续节点列表</returns>
    public IEnumerable<WorkflowNode> GetNextNodes(string nodeId)
    {
        var nextNodeIds = Connections
            .Where(c => c.SourceNodeId == nodeId)
            .Select(c => c.TargetNodeId);

        return Nodes.Where(n => nextNodeIds.Contains(n.NodeId));
    }

    /// <summary>
    /// 获取指定节点的前置节点
    /// </summary>
    /// <param name="nodeId">节点ID</param>
    /// <returns>前置节点列表</returns>
    public IEnumerable<WorkflowNode> GetPreviousNodes(string nodeId)
    {
        var prevNodeIds = Connections
            .Where(c => c.TargetNodeId == nodeId)
            .Select(c => c.SourceNodeId);

        return Nodes.Where(n => prevNodeIds.Contains(n.NodeId));
    }

    /// <summary>
    /// 检查工作流是否包含循环
    /// </summary>
    /// <returns>是否包含循环</returns>
    public bool HasCycles()
    {
        var visited = new HashSet<string>();
        var recursionStack = new HashSet<string>();

        foreach (var node in Nodes)
        {
            if (HasCyclesUtil(node.NodeId, visited, recursionStack))
                return true;
        }

        return false;
    }

    /// <summary>
    /// 循环检测辅助方法
    /// </summary>
    private bool HasCyclesUtil(string nodeId, HashSet<string> visited, HashSet<string> recursionStack)
    {
        if (recursionStack.Contains(nodeId))
            return true;

        if (visited.Contains(nodeId))
            return false;

        visited.Add(nodeId);
        recursionStack.Add(nodeId);

        var nextNodes = GetNextNodes(nodeId);
        foreach (var nextNode in nextNodes)
        {
            if (HasCyclesUtil(nextNode.NodeId, visited, recursionStack))
                return true;
        }

        recursionStack.Remove(nodeId);
        return false;
    }

    /// <summary>
    /// 创建工作流定义的深拷贝
    /// </summary>
    /// <returns>工作流定义的深拷贝</returns>
    public WorkflowDefinition Clone()
    {
        return new WorkflowDefinition
        {
            WorkflowId = WorkflowId,
            Name = Name,
            Description = Description,
            Version = Version,
            Author = Author,
            CreatedAt = CreatedAt,
            LastModifiedAt = LastModifiedAt,
            Nodes = Nodes.Select(n => n.Clone()).ToList(),
            Connections = Connections.Select(c => c.Clone()).ToList(),
            InputParameters = InputParameters.Select(p => p.Clone()).ToList(),
            OutputParameters = OutputParameters.Select(p => p.Clone()).ToList(),
            Configuration = Configuration.Clone(),
            Tags = new HashSet<string>(Tags),
            Metadata = new Dictionary<string, object>(Metadata)
        };
    }

    /// <summary>
    /// 获取工作流定义的字符串表示
    /// </summary>
    /// <returns>工作流定义字符串</returns>
    public override string ToString()
    {
        return $"Workflow[{WorkflowId}] {Name} v{Version} - {Nodes.Count} nodes, {Connections.Count} connections";
    }
}
