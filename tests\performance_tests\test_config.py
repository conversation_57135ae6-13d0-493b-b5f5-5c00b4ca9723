#!/usr/bin/env python3
"""
FlowCustomV1 性能测试配置文件
统一管理所有测试的配置参数
"""

# 基础配置
BASE_CONFIG = {
    "api_base_url": "http://localhost:5000",
    "nats_monitor_url": "http://localhost:8222",
    "mysql_config": {
        'host': 'localhost',
        'port': 3306,
        'user': 'flowcustom',
        'password': 'FlowCustom@2025',
        'database': 'flowcustom_dev'
    }
}

# API端点配置
API_ENDPOINTS = {
    "cluster_nodes": "/api/cluster/nodes",
    "executor_capacity": "/api/executor/capacity",
    "swagger": "/swagger"
}

# NATS监控端点配置
NATS_ENDPOINTS = {
    "server_info": "/varz",
    "connections": "/connz", 
    "jetstream": "/jsz",
    "subscriptions": "/subsz"
}

# 性能测试参数配置
PERFORMANCE_CONFIG = {
    # 基础性能测试
    "basic_test": {
        "iterations": 10,
        "timeout": 30,
        "concurrent_users": [5, 10],
        "requests_per_user": 10
    },
    
    # 快速测试
    "quick_test": {
        "iterations": 5,
        "timeout": 10,
        "wait_time": 10  # 等待容器启动时间
    },
    
    # 深度分析测试
    "analysis_test": {
        "iterations": 3,
        "timeout": 30,
        "detailed_analysis": True
    },
    
    # 基础设施压力测试
    "infrastructure_stress": {
        "nats_iterations": 20,
        "nats_concurrent_levels": [5, 10, 20, 50],
        "mysql_iterations": 20,
        "mysql_concurrent_levels": [5, 10, 20],
        "timeout": 5
    },
    
    # 极限压力测试
    "extreme_stress": {
        "nats_extreme_levels": [100, 200, 500, 1000],
        "mysql_extreme_levels": [50, 100, 200, 500],
        "breaking_point_start": 100,
        "breaking_point_increment": 200,
        "breaking_point_max": 2000,
        "timeout": 60,
        "success_threshold": 0.8  # 80%成功率认为可接受
    }
}

# 性能基准配置
PERFORMANCE_BENCHMARKS = {
    # API性能基准 (毫秒)
    "api_benchmarks": {
        "excellent": 50,      # 优秀
        "good": 200,          # 良好
        "acceptable": 1000,   # 可接受
        "poor": 5000         # 较差
    },
    
    # 吞吐量基准 (请求/秒)
    "throughput_benchmarks": {
        "api_excellent": 100,
        "api_good": 50,
        "api_acceptable": 10,
        "nats_excellent": 200,
        "nats_good": 100,
        "nats_acceptable": 50,
        "mysql_excellent": 1000,
        "mysql_good": 500,
        "mysql_acceptable": 200
    },
    
    # 系统极限基准
    "system_limits": {
        "max_stable_concurrent": 1300,
        "breaking_point": 1500,
        "warning_threshold": 1000
    }
}

# 测试结果评级配置
PERFORMANCE_GRADES = {
    "excellent": {
        "symbol": "🟢",
        "name": "优秀",
        "description": "超出预期性能"
    },
    "good": {
        "symbol": "🟡", 
        "name": "良好",
        "description": "满足性能要求"
    },
    "acceptable": {
        "symbol": "🟠",
        "name": "一般", 
        "description": "接近性能边界"
    },
    "poor": {
        "symbol": "🔴",
        "name": "较差",
        "description": "需要优化改进"
    }
}

# 测试查询配置
TEST_QUERIES = {
    "mysql_queries": {
        "simple_select": "SELECT 1",
        "show_tables": "SHOW TABLES",
        "database_info": "SELECT DATABASE(), VERSION(), NOW()",
        "table_status": "SHOW TABLE STATUS",
        "connection_info": "SELECT DATABASE(), CONNECTION_ID(), NOW(), RAND()"
    }
}

# 输出格式配置
OUTPUT_CONFIG = {
    "datetime_format": "%Y-%m-%d %H:%M:%S",
    "precision": 2,  # 小数点精度
    "show_progress": True,
    "show_details": True
}

def get_performance_grade(value, benchmark_type, metric_type="response_time"):
    """
    根据性能值获取评级
    
    Args:
        value: 性能值
        benchmark_type: 基准类型 (api, nats, mysql)
        metric_type: 指标类型 (response_time, throughput)
    
    Returns:
        dict: 包含评级信息的字典
    """
    if metric_type == "response_time":
        # 响应时间评级 (越小越好)
        benchmarks = PERFORMANCE_BENCHMARKS["api_benchmarks"]
        if value <= benchmarks["excellent"]:
            return PERFORMANCE_GRADES["excellent"]
        elif value <= benchmarks["good"]:
            return PERFORMANCE_GRADES["good"]
        elif value <= benchmarks["acceptable"]:
            return PERFORMANCE_GRADES["acceptable"]
        else:
            return PERFORMANCE_GRADES["poor"]
            
    elif metric_type == "throughput":
        # 吞吐量评级 (越大越好)
        benchmarks = PERFORMANCE_BENCHMARKS["throughput_benchmarks"]
        excellent_key = f"{benchmark_type}_excellent"
        good_key = f"{benchmark_type}_good"
        acceptable_key = f"{benchmark_type}_acceptable"
        
        if value >= benchmarks.get(excellent_key, 100):
            return PERFORMANCE_GRADES["excellent"]
        elif value >= benchmarks.get(good_key, 50):
            return PERFORMANCE_GRADES["good"]
        elif value >= benchmarks.get(acceptable_key, 10):
            return PERFORMANCE_GRADES["acceptable"]
        else:
            return PERFORMANCE_GRADES["poor"]
    
    return PERFORMANCE_GRADES["acceptable"]

def format_performance_result(value, unit="ms", precision=None):
    """
    格式化性能结果显示
    
    Args:
        value: 数值
        unit: 单位
        precision: 精度
    
    Returns:
        str: 格式化后的字符串
    """
    if precision is None:
        precision = OUTPUT_CONFIG["precision"]
        
    return f"{value:.{precision}f}{unit}"

def get_test_config(test_type):
    """
    获取指定测试类型的配置
    
    Args:
        test_type: 测试类型
    
    Returns:
        dict: 测试配置
    """
    return PERFORMANCE_CONFIG.get(test_type, {})

# 导出主要配置
__all__ = [
    'BASE_CONFIG',
    'API_ENDPOINTS', 
    'NATS_ENDPOINTS',
    'PERFORMANCE_CONFIG',
    'PERFORMANCE_BENCHMARKS',
    'PERFORMANCE_GRADES',
    'TEST_QUERIES',
    'OUTPUT_CONFIG',
    'get_performance_grade',
    'format_performance_result',
    'get_test_config'
]
