using FlowCustomV1.Core.Models.Workflow;
using FlowCustomV1.Engine.Context;

namespace FlowCustomV1.Engine.ErrorHandling;

/// <summary>
/// 错误处理器接口
/// 负责处理工作流执行过程中的错误和异常
/// </summary>
public interface IErrorHandler
{
    /// <summary>
    /// 处理执行错误
    /// </summary>
    /// <param name="error">执行错误</param>
    /// <param name="retryStrategy">重试策略</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>错误处理结果</returns>
    Task<ErrorHandlingResult> HandleErrorAsync(
        ExecutionError error, 
        NodeRetryStrategy retryStrategy, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 判断是否应该重试
    /// </summary>
    /// <param name="exception">异常信息</param>
    /// <param name="retryStrategy">重试策略</param>
    /// <param name="currentRetryCount">当前重试次数</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否应该重试</returns>
    Task<bool> ShouldRetryAsync(
        Exception exception, 
        NodeRetryStrategy retryStrategy, 
        int currentRetryCount, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 计算重试延迟时间
    /// </summary>
    /// <param name="retryStrategy">重试策略</param>
    /// <param name="retryCount">重试次数</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>重试延迟时间</returns>
    Task<TimeSpan> CalculateRetryDelayAsync(
        NodeRetryStrategy retryStrategy, 
        int retryCount, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 处理工作流级别的错误
    /// </summary>
    /// <param name="workflowContext">工作流执行上下文</param>
    /// <param name="exception">异常信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>工作流错误处理结果</returns>
    Task<WorkflowErrorHandlingResult> HandleWorkflowErrorAsync(
        EngineWorkflowContext workflowContext,
        Exception exception, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 执行错误恢复操作
    /// </summary>
    /// <param name="context">节点执行上下文</param>
    /// <param name="recoveryAction">恢复操作</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>恢复结果</returns>
    Task<ErrorRecoveryResult> ExecuteRecoveryAsync(
        NodeExecutionContext context,
        ErrorRecoveryAction recoveryAction,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 记录错误信息
    /// </summary>
    /// <param name="error">错误信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>记录任务</returns>
    Task LogErrorAsync(ExecutionError error, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取错误统计信息
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>错误统计信息</returns>
    Task<ErrorStatistics> GetErrorStatisticsAsync(
        string? executionId = null, 
        CancellationToken cancellationToken = default);
}

/// <summary>
/// 执行错误信息
/// </summary>
public class ExecutionError
{
    /// <summary>
    /// 错误唯一标识符
    /// </summary>
    public string ErrorId { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 执行ID
    /// </summary>
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 工作流ID
    /// </summary>
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 节点ID（可选）
    /// </summary>
    public string? NodeId { get; set; }

    /// <summary>
    /// 异常信息
    /// </summary>
    public Exception Exception { get; set; } = default!;

    /// <summary>
    /// 错误发生时间
    /// </summary>
    public DateTime OccurredAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 错误级别
    /// </summary>
    public ErrorLevel Level { get; set; } = ErrorLevel.Error;

    /// <summary>
    /// 错误类别
    /// </summary>
    public ErrorCategory Category { get; set; } = ErrorCategory.Unknown;

    /// <summary>
    /// 错误上下文信息
    /// </summary>
    public Dictionary<string, object> Context { get; set; } = new();

    /// <summary>
    /// 是否可恢复
    /// </summary>
    public bool IsRecoverable { get; set; } = false;

    /// <summary>
    /// 重试次数
    /// </summary>
    public int RetryCount { get; set; } = 0;

    /// <summary>
    /// 错误堆栈跟踪
    /// </summary>
    public string? StackTrace => Exception?.StackTrace;

    /// <summary>
    /// 错误消息
    /// </summary>
    public string Message => Exception?.Message ?? "Unknown error";
}

/// <summary>
/// 错误处理结果
/// </summary>
public class ErrorHandlingResult
{
    /// <summary>
    /// 处理是否成功
    /// </summary>
    public bool IsSuccess { get; set; } = false;

    /// <summary>
    /// 处理动作
    /// </summary>
    public ErrorHandlingAction Action { get; set; } = ErrorHandlingAction.Fail;

    /// <summary>
    /// 重试延迟时间
    /// </summary>
    public TimeSpan RetryDelay { get; set; } = TimeSpan.Zero;

    /// <summary>
    /// 处理消息
    /// </summary>
    public string? Message { get; set; }

    /// <summary>
    /// 恢复操作（如果有）
    /// </summary>
    public ErrorRecoveryAction? RecoveryAction { get; set; }

    /// <summary>
    /// 处理元数据
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// 工作流错误处理结果
/// </summary>
public class WorkflowErrorHandlingResult
{
    /// <summary>
    /// 处理是否成功
    /// </summary>
    public bool IsSuccess { get; set; } = false;

    /// <summary>
    /// 工作流处理动作
    /// </summary>
    public WorkflowErrorAction Action { get; set; } = WorkflowErrorAction.Terminate;

    /// <summary>
    /// 处理消息
    /// </summary>
    public string? Message { get; set; }

    /// <summary>
    /// 需要取消的节点列表
    /// </summary>
    public List<string> NodesToCancel { get; set; } = new();

    /// <summary>
    /// 需要重试的节点列表
    /// </summary>
    public List<string> NodesToRetry { get; set; } = new();

    /// <summary>
    /// 处理元数据
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// 错误恢复结果
/// </summary>
public class ErrorRecoveryResult
{
    /// <summary>
    /// 恢复是否成功
    /// </summary>
    public bool IsSuccess { get; set; } = false;

    /// <summary>
    /// 恢复消息
    /// </summary>
    public string? Message { get; set; }

    /// <summary>
    /// 恢复后的数据
    /// </summary>
    public Dictionary<string, object> RecoveredData { get; set; } = new();

    /// <summary>
    /// 恢复元数据
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// 错误级别枚举
/// </summary>
public enum ErrorLevel
{
    /// <summary>
    /// 信息
    /// </summary>
    Information = 0,

    /// <summary>
    /// 警告
    /// </summary>
    Warning = 1,

    /// <summary>
    /// 错误
    /// </summary>
    Error = 2,

    /// <summary>
    /// 严重错误
    /// </summary>
    Critical = 3,

    /// <summary>
    /// 致命错误
    /// </summary>
    Fatal = 4
}

/// <summary>
/// 错误类别枚举
/// </summary>
public enum ErrorCategory
{
    /// <summary>
    /// 未知错误
    /// </summary>
    Unknown = 0,

    /// <summary>
    /// 配置错误
    /// </summary>
    Configuration = 1,

    /// <summary>
    /// 验证错误
    /// </summary>
    Validation = 2,

    /// <summary>
    /// 网络错误
    /// </summary>
    Network = 3,

    /// <summary>
    /// 数据库错误
    /// </summary>
    Database = 4,

    /// <summary>
    /// 文件系统错误
    /// </summary>
    FileSystem = 5,

    /// <summary>
    /// 业务逻辑错误
    /// </summary>
    BusinessLogic = 6,

    /// <summary>
    /// 系统错误
    /// </summary>
    System = 7,

    /// <summary>
    /// 超时错误
    /// </summary>
    Timeout = 8,

    /// <summary>
    /// 权限错误
    /// </summary>
    Permission = 9,

    /// <summary>
    /// 资源不足错误
    /// </summary>
    Resource = 10
}

/// <summary>
/// 错误处理动作枚举
/// </summary>
public enum ErrorHandlingAction
{
    /// <summary>
    /// 失败
    /// </summary>
    Fail = 0,

    /// <summary>
    /// 重试
    /// </summary>
    Retry = 1,

    /// <summary>
    /// 跳过
    /// </summary>
    Skip = 2,

    /// <summary>
    /// 恢复
    /// </summary>
    Recover = 3,

    /// <summary>
    /// 继续
    /// </summary>
    Continue = 4
}

/// <summary>
/// 工作流错误动作枚举
/// </summary>
public enum WorkflowErrorAction
{
    /// <summary>
    /// 终止工作流
    /// </summary>
    Terminate = 0,

    /// <summary>
    /// 暂停工作流
    /// </summary>
    Pause = 1,

    /// <summary>
    /// 继续执行
    /// </summary>
    Continue = 2,

    /// <summary>
    /// 重试工作流
    /// </summary>
    Retry = 3,

    /// <summary>
    /// 回滚工作流
    /// </summary>
    Rollback = 4
}

/// <summary>
/// 错误恢复动作
/// </summary>
public class ErrorRecoveryAction
{
    /// <summary>
    /// 恢复动作类型
    /// </summary>
    public string ActionType { get; set; } = string.Empty;

    /// <summary>
    /// 恢复参数
    /// </summary>
    public Dictionary<string, object> Parameters { get; set; } = new();

    /// <summary>
    /// 恢复超时时间
    /// </summary>
    public TimeSpan Timeout { get; set; } = TimeSpan.FromMinutes(5);
}

/// <summary>
/// 错误统计信息
/// </summary>
public class ErrorStatistics
{
    /// <summary>
    /// 总错误数
    /// </summary>
    public int TotalErrors { get; set; } = 0;

    /// <summary>
    /// 按级别分组的错误数
    /// </summary>
    public Dictionary<ErrorLevel, int> ErrorsByLevel { get; set; } = new();

    /// <summary>
    /// 按类别分组的错误数
    /// </summary>
    public Dictionary<ErrorCategory, int> ErrorsByCategory { get; set; } = new();

    /// <summary>
    /// 重试成功数
    /// </summary>
    public int SuccessfulRetries { get; set; } = 0;

    /// <summary>
    /// 重试失败数
    /// </summary>
    public int FailedRetries { get; set; } = 0;

    /// <summary>
    /// 恢复成功数
    /// </summary>
    public int SuccessfulRecoveries { get; set; } = 0;

    /// <summary>
    /// 恢复失败数
    /// </summary>
    public int FailedRecoveries { get; set; } = 0;

    /// <summary>
    /// 统计时间范围开始
    /// </summary>
    public DateTime StartTime { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 统计时间范围结束
    /// </summary>
    public DateTime EndTime { get; set; } = DateTime.UtcNow;
}
