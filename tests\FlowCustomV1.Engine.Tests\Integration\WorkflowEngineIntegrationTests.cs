using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using FlowCustomV1.Core.Interfaces;

namespace FlowCustomV1.Engine.Tests.Integration;

/// <summary>
/// 工作流引擎集成测试
/// </summary>
public class WorkflowEngineIntegrationTests : TestBase
{
    private IWorkflowEngine _workflowEngine = null!;

    public WorkflowEngineIntegrationTests()
    {
        _workflowEngine = ServiceProvider.GetRequiredService<IWorkflowEngine>();
    }

    [Fact]
    public void WorkflowEngine_ShouldBeResolvedFromServiceProvider()
    {
        // Assert
        _workflowEngine.Should().NotBeNull();
    }

    [Fact]
    public async Task WorkflowEngine_CanBeStarted()
    {
        // Act
        Func<Task> act = async () => await _workflowEngine.StartAsync();

        // Assert
        await act.Should().NotThrowAsync();
    }
}