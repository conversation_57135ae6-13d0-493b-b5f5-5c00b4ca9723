# FlowCustomV1 项目目录结构规范

## 📋 文档信息

| 项目信息 | 详细内容 |
|---------|---------|
| **项目名称** | FlowCustomV1 工作流自动化系统 |
| **文档类型** | 项目目录结构规范 |
| **创建日期** | 2025-09-07 |
| **最后更新** | 2025-09-07 |
| **适用版本** | v0.0.1.7+ |

---

## 🏗️ 标准项目结构

### **根目录结构**
```
FlowCustom/
├── src/                          # 源代码目录
├── tests/                        # 测试代码目录
├── docs/                         # 项目文档目录
├── docker/                       # Docker配置目录
├── examples/                     # 示例代码目录
├── FlowCustomV1.sln             # 解决方案文件
├── README.md                    # 项目说明文档
├── .gitignore                   # Git忽略文件配置
└── fix-async-methods.ps1        # 工具脚本
```

### **源代码目录 (src/)**
```
src/
├── FlowCustomV1.Core/           # 核心业务逻辑层
│   ├── Constants/               # 常量定义
│   ├── Extensions/              # 扩展方法
│   ├── Interfaces/              # 接口定义
│   ├── Models/                  # 数据模型
│   └── Services/                # 核心服务
├── FlowCustomV1.Engine/         # 工作流引擎层
│   ├── Context/                 # 执行上下文
│   ├── Executors/               # 节点执行器
│   ├── Schedulers/              # 调度器
│   └── Services/                # 引擎服务
├── FlowCustomV1.Infrastructure/ # 基础设施层
│   ├── Configuration/           # 配置管理
│   ├── Data/                    # 数据访问
│   ├── Services/                # 基础设施服务
│   └── Repositories/            # 数据仓储
└── FlowCustomV1.Api/            # API表示层
    ├── Controllers/             # API控制器
    ├── Models/                  # API模型
    └── Services/                # API服务
```

### **测试目录 (tests/)**
```
tests/
├── FlowCustomV1.Core.Tests/         # 核心层单元测试
├── FlowCustomV1.Engine.Tests/       # 引擎层单元测试
├── FlowCustomV1.Integration.Tests/  # 集成测试
├── FlowCustomV1.Tests/              # 综合测试
├── docker/                          # Docker测试环境
│   ├── config/                      # 测试配置
│   ├── scripts/                     # 测试脚本
│   └── docker-compose.*.yml         # Docker编排文件
└── *.ps1                            # PowerShell测试脚本
```

### **文档目录 (docs/)**
```
docs/
├── 核心设计/                        # 核心设计文档
├── 开发规范/                        # 开发规范文档
├── 项目管理/                        # 项目管理文档
├── 版本发布/                        # 版本发布说明
├── 版本说明/                        # 版本详细说明
├── 质量文档/                        # 质量保证文档
├── 历史文档/                        # 历史文档存档
└── 工具文档/                        # 工具使用文档
```

---

## 🚫 禁止的目录和文件

### **根目录禁止项**
```
❌ Users/                    # 用户目录
❌ node_modules/             # Node.js依赖
❌ obj/                      # 编译输出
❌ bin/                      # 编译输出
❌ package.json              # Node.js配置
❌ package-lock.json         # Node.js锁定文件
❌ *.log                     # 日志文件
❌ *.db                      # 数据库文件
❌ *.sqlite                  # SQLite数据库文件
```

### **项目目录禁止项**
```
❌ src/*/bin/                # 编译输出目录
❌ src/*/obj/                # 编译缓存目录
❌ src/*/*.db                # 数据库文件
❌ src/*/*.log               # 日志文件
❌ tests/*/bin/              # 测试编译输出
❌ tests/*/obj/              # 测试编译缓存
```

---

## ✅ 目录命名规范

### **英文命名优先**
- **推荐**: `src/`, `tests/`, `docs/`, `examples/`
- **避免**: 中文目录名（可能导致跨平台问题）

### **Pascal命名法**
- **项目目录**: `FlowCustomV1.Core`, `FlowCustomV1.Engine`
- **子目录**: `Controllers`, `Services`, `Models`

### **小写命名法**
- **配置目录**: `config/`, `scripts/`, `docker/`
- **文档目录**: `docs/` (根目录除外)

---

## 🔧 .gitignore 配置

### **必须忽略的文件类型**
```gitignore
# 编译输出
bin/
obj/
out/

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 日志文件
*.log
api_output.log

# Node.js (不应在.NET项目中)
node_modules/
package.json
package-lock.json

# 临时文件
Users/
query/
test_workflow.json

# IDE文件
.vs/
.vscode/
.idea/
```

---

## 📁 目录职责说明

### **src/ 源代码职责**
- **Core**: 业务逻辑、领域模型、接口定义
- **Engine**: 工作流引擎、调度器、执行器
- **Infrastructure**: 数据访问、外部服务、基础设施
- **Api**: HTTP API、控制器、请求/响应模型

### **tests/ 测试职责**
- **单元测试**: 测试单个类或方法
- **集成测试**: 测试组件间交互
- **端到端测试**: 测试完整业务流程
- **性能测试**: 测试系统性能指标

### **docs/ 文档职责**
- **设计文档**: 架构设计、接口设计
- **规范文档**: 开发规范、编码标准
- **管理文档**: 项目计划、状态跟踪
- **发布文档**: 版本说明、发布记录

---

## 🛠️ 维护建议

### **定期清理**
1. **每周清理**: 删除临时文件、日志文件
2. **每月检查**: 检查目录结构是否符合规范
3. **版本发布前**: 全面清理和结构检查

### **新增目录规则**
1. **先讨论**: 新增目录前先讨论必要性
2. **遵循规范**: 新目录必须符合命名规范
3. **更新文档**: 新增目录后更新此文档

### **问题处理**
1. **发现问题**: 及时报告目录结构问题
2. **快速修复**: 立即清理不合规的文件/目录
3. **预防措施**: 更新.gitignore防止再次出现

---

## 📊 检查清单

### **项目结构检查**
- [ ] 根目录无不合理文件
- [ ] src/目录结构符合清洁架构
- [ ] tests/目录按层级组织
- [ ] docs/目录分类清晰
- [ ] .gitignore配置完整

### **文件命名检查**
- [ ] 项目名称使用Pascal命名法
- [ ] 目录名称符合规范
- [ ] 无中文路径问题
- [ ] 无特殊字符问题

### **内容检查**
- [ ] 无编译输出文件
- [ ] 无数据库文件
- [ ] 无日志文件
- [ ] 无临时文件
- [ ] 无Node.js相关文件

---

## 🎯 总结

良好的项目目录结构是项目成功的基础。遵循本规范可以：

1. **提高开发效率** - 清晰的结构便于快速定位文件
2. **降低维护成本** - 规范的组织减少混乱和错误
3. **增强团队协作** - 统一的标准便于团队成员理解
4. **保证项目质量** - 避免不必要的文件污染版本控制

**记住：保持项目结构的整洁和规范是每个开发者的责任！**
