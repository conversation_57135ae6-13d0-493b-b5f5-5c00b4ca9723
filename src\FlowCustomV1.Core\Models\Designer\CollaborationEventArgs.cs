using System.Text.Json.Serialization;

namespace FlowCustomV1.Core.Models.Designer;

/// <summary>
/// 协作者加入事件参数
/// </summary>
public class CollaboratorJoinedEventArgs : EventArgs
{
    /// <summary>
    /// 会话ID
    /// </summary>
    [JsonPropertyName("sessionId")]
    public string SessionId { get; set; } = string.Empty;

    /// <summary>
    /// 协作者信息
    /// </summary>
    [Json<PERSON>ropertyName("collaborator")]
    public CollaboratorInfo Collaborator { get; set; } = new();

    /// <summary>
    /// 加入时间
    /// </summary>
    [JsonPropertyName("joinedAt")]
    public DateTime JoinedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 当前会话中的协作者总数
    /// </summary>
    [JsonPropertyName("totalCollaborators")]
    public int TotalCollaborators { get; set; } = 0;
}

/// <summary>
/// 协作者离开事件参数
/// </summary>
public class CollaboratorLeftEventArgs : EventArgs
{
    /// <summary>
    /// 会话ID
    /// </summary>
    [JsonPropertyName("sessionId")]
    public string SessionId { get; set; } = string.Empty;

    /// <summary>
    /// 协作者ID
    /// </summary>
    [JsonPropertyName("collaboratorId")]
    public string CollaboratorId { get; set; } = string.Empty;

    /// <summary>
    /// 协作者名称
    /// </summary>
    [JsonPropertyName("collaboratorName")]
    public string CollaboratorName { get; set; } = string.Empty;

    /// <summary>
    /// 离开时间
    /// </summary>
    [JsonPropertyName("leftAt")]
    public DateTime LeftAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 离开原因
    /// </summary>
    [JsonPropertyName("reason")]
    public string Reason { get; set; } = string.Empty;

    /// <summary>
    /// 剩余协作者数量
    /// </summary>
    [JsonPropertyName("remainingCollaborators")]
    public int RemainingCollaborators { get; set; } = 0;
}

/// <summary>
/// 设计操作事件参数
/// </summary>
public class DesignOperationEventArgs : EventArgs
{
    /// <summary>
    /// 会话ID
    /// </summary>
    [JsonPropertyName("sessionId")]
    public string SessionId { get; set; } = string.Empty;

    /// <summary>
    /// 设计操作
    /// </summary>
    [JsonPropertyName("operation")]
    public DesignOperation Operation { get; set; } = new();

    /// <summary>
    /// 操作来源协作者
    /// </summary>
    [JsonPropertyName("sourceCollaborator")]
    public CollaboratorInfo? SourceCollaborator { get; set; }

    /// <summary>
    /// 接收时间
    /// </summary>
    [JsonPropertyName("receivedAt")]
    public DateTime ReceivedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 是否需要广播给其他协作者
    /// </summary>
    [JsonPropertyName("shouldBroadcast")]
    public bool ShouldBroadcast { get; set; } = true;
}

/// <summary>
/// 冲突解决事件参数
/// </summary>
public class ConflictResolvedEventArgs : EventArgs
{
    /// <summary>
    /// 会话ID
    /// </summary>
    [JsonPropertyName("sessionId")]
    public string SessionId { get; set; } = string.Empty;

    /// <summary>
    /// 冲突ID
    /// </summary>
    [JsonPropertyName("conflictId")]
    public string ConflictId { get; set; } = string.Empty;

    /// <summary>
    /// 解决方案
    /// </summary>
    [JsonPropertyName("resolution")]
    public ConflictResolution Resolution { get; set; } = new();

    /// <summary>
    /// 解决者ID
    /// </summary>
    [JsonPropertyName("resolvedBy")]
    public string ResolvedBy { get; set; } = string.Empty;

    /// <summary>
    /// 解决时间
    /// </summary>
    [JsonPropertyName("resolvedAt")]
    public DateTime ResolvedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 解决耗时（毫秒）
    /// </summary>
    [JsonPropertyName("resolutionTimeMs")]
    public long ResolutionTimeMs { get; set; } = 0;
}

/// <summary>
/// 会话状态变更事件参数
/// </summary>
public class SessionStatusChangedEventArgs : EventArgs
{
    /// <summary>
    /// 会话ID
    /// </summary>
    [JsonPropertyName("sessionId")]
    public string SessionId { get; set; } = string.Empty;

    /// <summary>
    /// 之前的状态
    /// </summary>
    [JsonPropertyName("previousStatus")]
    public SessionStatus PreviousStatus { get; set; }

    /// <summary>
    /// 新状态
    /// </summary>
    [JsonPropertyName("newStatus")]
    public SessionStatus NewStatus { get; set; }

    /// <summary>
    /// 状态变更时间
    /// </summary>
    [JsonPropertyName("changedAt")]
    public DateTime ChangedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 变更原因
    /// </summary>
    [JsonPropertyName("reason")]
    public string Reason { get; set; } = string.Empty;

    /// <summary>
    /// 触发变更的协作者ID
    /// </summary>
    [JsonPropertyName("triggeredBy")]
    public string TriggeredBy { get; set; } = string.Empty;
}

/// <summary>
/// 协作者状态更新事件参数
/// </summary>
public class CollaboratorStatusUpdatedEventArgs : EventArgs
{
    /// <summary>
    /// 会话ID
    /// </summary>
    [JsonPropertyName("sessionId")]
    public string SessionId { get; set; } = string.Empty;

    /// <summary>
    /// 协作者ID
    /// </summary>
    [JsonPropertyName("collaboratorId")]
    public string CollaboratorId { get; set; } = string.Empty;

    /// <summary>
    /// 之前的状态
    /// </summary>
    [JsonPropertyName("previousStatus")]
    public CollaboratorStatus PreviousStatus { get; set; }

    /// <summary>
    /// 新状态
    /// </summary>
    [JsonPropertyName("newStatus")]
    public CollaboratorStatus NewStatus { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    [JsonPropertyName("updatedAt")]
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 权限变更事件参数
/// </summary>
public class PermissionChangedEventArgs : EventArgs
{
    /// <summary>
    /// 会话ID
    /// </summary>
    [JsonPropertyName("sessionId")]
    public string SessionId { get; set; } = string.Empty;

    /// <summary>
    /// 协作者ID
    /// </summary>
    [JsonPropertyName("collaboratorId")]
    public string CollaboratorId { get; set; } = string.Empty;

    /// <summary>
    /// 之前的权限
    /// </summary>
    [JsonPropertyName("previousPermissions")]
    public HashSet<string> PreviousPermissions { get; set; } = new();

    /// <summary>
    /// 新权限
    /// </summary>
    [JsonPropertyName("newPermissions")]
    public HashSet<string> NewPermissions { get; set; } = new();

    /// <summary>
    /// 变更者ID
    /// </summary>
    [JsonPropertyName("changedBy")]
    public string ChangedBy { get; set; } = string.Empty;

    /// <summary>
    /// 变更时间
    /// </summary>
    [JsonPropertyName("changedAt")]
    public DateTime ChangedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 变更原因
    /// </summary>
    [JsonPropertyName("reason")]
    public string Reason { get; set; } = string.Empty;
}

/// <summary>
/// 角色变更事件参数
/// </summary>
public class RoleChangedEventArgs : EventArgs
{
    /// <summary>
    /// 会话ID
    /// </summary>
    [JsonPropertyName("sessionId")]
    public string SessionId { get; set; } = string.Empty;

    /// <summary>
    /// 协作者ID
    /// </summary>
    [JsonPropertyName("collaboratorId")]
    public string CollaboratorId { get; set; } = string.Empty;

    /// <summary>
    /// 之前的角色
    /// </summary>
    [JsonPropertyName("previousRole")]
    public CollaboratorRole PreviousRole { get; set; }

    /// <summary>
    /// 新角色
    /// </summary>
    [JsonPropertyName("newRole")]
    public CollaboratorRole NewRole { get; set; }

    /// <summary>
    /// 变更者ID
    /// </summary>
    [JsonPropertyName("changedBy")]
    public string ChangedBy { get; set; } = string.Empty;

    /// <summary>
    /// 变更时间
    /// </summary>
    [JsonPropertyName("changedAt")]
    public DateTime ChangedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 变更原因
    /// </summary>
    [JsonPropertyName("reason")]
    public string Reason { get; set; } = string.Empty;
}

/// <summary>
/// 聊天消息事件参数
/// </summary>
public class ChatMessageEventArgs : EventArgs
{
    /// <summary>
    /// 会话ID
    /// </summary>
    [JsonPropertyName("sessionId")]
    public string SessionId { get; set; } = string.Empty;

    /// <summary>
    /// 消息ID
    /// </summary>
    [JsonPropertyName("messageId")]
    public string MessageId { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 发送者ID
    /// </summary>
    [JsonPropertyName("senderId")]
    public string SenderId { get; set; } = string.Empty;

    /// <summary>
    /// 发送者名称
    /// </summary>
    [JsonPropertyName("senderName")]
    public string SenderName { get; set; } = string.Empty;

    /// <summary>
    /// 消息内容
    /// </summary>
    [JsonPropertyName("message")]
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 消息类型
    /// </summary>
    [JsonPropertyName("messageType")]
    public ChatMessageType MessageType { get; set; } = ChatMessageType.Text;

    /// <summary>
    /// 发送时间
    /// </summary>
    [JsonPropertyName("sentAt")]
    public DateTime SentAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 是否为私聊消息
    /// </summary>
    [JsonPropertyName("isPrivate")]
    public bool IsPrivate { get; set; } = false;

    /// <summary>
    /// 私聊目标协作者ID（私聊时使用）
    /// </summary>
    [JsonPropertyName("targetCollaboratorId")]
    public string? TargetCollaboratorId { get; set; }
}

/// <summary>
/// 聊天消息类型枚举
/// </summary>
public enum ChatMessageType
{
    /// <summary>
    /// 文本消息
    /// </summary>
    Text,

    /// <summary>
    /// 系统消息
    /// </summary>
    System,

    /// <summary>
    /// 文件分享
    /// </summary>
    File,

    /// <summary>
    /// 链接分享
    /// </summary>
    Link,

    /// <summary>
    /// 表情符号
    /// </summary>
    Emoji,

    /// <summary>
    /// 代码片段
    /// </summary>
    Code
}
