import React from 'react';
import { Button, Space, Tag, Modal, Form, Input, Select, message } from 'antd';
import { 
  UserOutlined, 
  PlusOutlined, 
  EditOutlined,
  DeleteOutlined,
  LockOutlined,
  UnlockOutlined,
  TeamOutlined
} from '@ant-design/icons';
import { ProTable, ProColumns, ProCard } from '@ant-design/pro-components';

// ========== 页面布局配置 ==========
const PAGE_LAYOUT_CONFIG = {
  // 表格滚动高度 - 有统计卡片容器 + 工具栏的页面
  tableScrollY: 'calc(100vh - 520px)'
};

const { Option } = Select;

// 模拟用户数据
const mockUsers = [
  {
    id: '1',
    username: 'admin',
    email: '<EMAIL>',
    fullName: '系统管理员',
    role: 'Admin',
    status: 'Active',
    lastLogin: '2025-01-13 15:30:00',
    createdAt: '2024-01-01 00:00:00',
  },
  {
    id: '2',
    username: 'designer1',
    email: '<EMAIL>',
    fullName: '工作流设计师',
    role: 'Designer',
    status: 'Active',
    lastLogin: '2025-01-13 14:20:00',
    createdAt: '2024-02-15 10:30:00',
  },
  {
    id: '3',
    username: 'operator1',
    email: '<EMAIL>',
    fullName: '系统操作员',
    role: 'Operator',
    status: 'Inactive',
    lastLogin: '2025-01-10 09:15:00',
    createdAt: '2024-03-20 14:45:00',
  },
];

const UserManagement: React.FC = () => {
  const [users, setUsers] = React.useState(mockUsers);
  const [modalVisible, setModalVisible] = React.useState(false);
  const [editingUser, setEditingUser] = React.useState<any>(null);
  const [form] = Form.useForm();

  // 获取角色标签
  const getRoleTag = (role: string) => {
    const roleMap = {
      Admin: { color: 'red', text: '管理员' },
      Designer: { color: 'blue', text: '设计师' },
      Operator: { color: 'green', text: '操作员' },
      Viewer: { color: 'default', text: '查看者' },
    };
    const config = roleMap[role as keyof typeof roleMap] || { color: 'default', text: role };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 获取状态标签
  const getStatusTag = (status: string) => {
    const statusMap = {
      Active: { color: 'success', text: '活跃' },
      Inactive: { color: 'default', text: '非活跃' },
      Locked: { color: 'error', text: '锁定' },
    };
    const config = statusMap[status as keyof typeof statusMap] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 表格列定义
  const columns: ProColumns<any>[] = [
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
      render: (text, record) => (
        <div className="flex items-center">
          <UserOutlined className="mr-2 text-gray-400" />
          <div>
            <div className="font-medium">{text}</div>
            <div className="text-sm text-gray-500">{record.fullName}</div>
          </div>
        </div>
      ),
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: '角色',
      dataIndex: 'role',
      key: 'role',
      render: (_, record) => getRoleTag(record.role),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (_, record) => getStatusTag(record.status),
    },
    {
      title: '最后登录',
      dataIndex: 'lastLogin',
      key: 'lastLogin',
      render: (_, record) => record.lastLogin ? new Date(record.lastLogin).toLocaleString() : '从未登录',
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (_, record) => new Date(record.createdAt).toLocaleDateString(),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record) => (
        <Space size="small">
          <Button 
            type="text" 
            size="small" 
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button 
            type="text" 
            size="small" 
            icon={record.status === 'Locked' ? <UnlockOutlined /> : <LockOutlined />}
            onClick={() => handleToggleLock(record)}
          >
            {record.status === 'Locked' ? '解锁' : '锁定'}
          </Button>
          <Button 
            type="text" 
            size="small" 
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  const handleEdit = (user: any) => {
    setEditingUser(user);
    form.setFieldsValue(user);
    setModalVisible(true);
  };

  const handleToggleLock = (user: any) => {
    const newStatus = user.status === 'Locked' ? 'Active' : 'Locked';
    setUsers(prev => prev.map(u => 
      u.id === user.id ? { ...u, status: newStatus } : u
    ));
    message.success(`用户已${newStatus === 'Locked' ? '锁定' : '解锁'}`);
  };

  const handleDelete = (user: any) => {
    Modal.confirm({
      title: '删除用户',
      content: `确定要删除用户 "${user.username}" 吗？`,
      okType: 'danger',
      onOk: () => {
        setUsers(prev => prev.filter(u => u.id !== user.id));
        message.success('用户已删除');
      },
    });
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      if (editingUser) {
        // 更新用户
        setUsers(prev => prev.map(u => 
          u.id === editingUser.id ? { ...u, ...values } : u
        ));
        message.success('用户信息已更新');
      } else {
        // 创建新用户
        const newUser = {
          id: Date.now().toString(),
          ...values,
          createdAt: new Date().toISOString(),
          lastLogin: '-',
        };
        setUsers(prev => [...prev, newUser]);
        message.success('用户创建成功');
      }
      setModalVisible(false);
      setEditingUser(null);
      form.resetFields();
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h1 className="text-2xl font-semibold text-gray-900 mb-2">
              <TeamOutlined className="mr-2" />
              用户管理
            </h1>
            <p className="text-gray-600">管理系统用户账户和权限</p>
          </div>
          <Button 
            type="primary" 
            icon={<PlusOutlined />}
            onClick={() => {
              setEditingUser(null);
              form.resetFields();
              setModalVisible(true);
            }}
          >
            添加用户
          </Button>
        </div>
      </div>

      {/* 用户统计 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <ProCard>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{users.length}</div>
            <div className="text-sm text-gray-500">总用户数</div>
          </div>
        </ProCard>
        <ProCard>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {users.filter(u => u.status === 'Active').length}
            </div>
            <div className="text-sm text-gray-500">活跃用户</div>
          </div>
        </ProCard>
        <ProCard>
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-600">
              {users.filter(u => u.role === 'Admin').length}
            </div>
            <div className="text-sm text-gray-500">管理员</div>
          </div>
        </ProCard>
        <ProCard>
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">
              {users.filter(u => u.status === 'Locked').length}
            </div>
            <div className="text-sm text-gray-500">锁定用户</div>
          </div>
        </ProCard>
      </div>

      {/* 用户表格 */}
      <ProTable
        columns={columns}
        dataSource={users}
        rowKey="id"
        pagination={{
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`,
        }}
        scroll={{
          x: 1200,
          y: PAGE_LAYOUT_CONFIG.tableScrollY
        }}
        search={false}
        toolBarRender={false}
        options={false}
      />

      {/* 用户编辑模态框 */}
      <Modal
        title={editingUser ? '编辑用户' : '添加用户'}
        open={modalVisible}
        onOk={handleSubmit}
        onCancel={() => {
          setModalVisible(false);
          setEditingUser(null);
          form.resetFields();
        }}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            status: 'Active',
            role: 'Viewer',
          }}
        >
          <Form.Item
            label="用户名"
            name="username"
            rules={[{ required: true, message: '请输入用户名' }]}
          >
            <Input placeholder="请输入用户名" />
          </Form.Item>

          <Form.Item
            label="全名"
            name="fullName"
            rules={[{ required: true, message: '请输入全名' }]}
          >
            <Input placeholder="请输入全名" />
          </Form.Item>

          <Form.Item
            label="邮箱"
            name="email"
            rules={[
              { required: true, message: '请输入邮箱' },
              { type: 'email', message: '请输入有效的邮箱地址' }
            ]}
          >
            <Input placeholder="请输入邮箱" />
          </Form.Item>

          <Form.Item
            label="角色"
            name="role"
            rules={[{ required: true, message: '请选择角色' }]}
          >
            <Select placeholder="请选择角色">
              <Option value="Admin">管理员</Option>
              <Option value="Designer">设计师</Option>
              <Option value="Operator">操作员</Option>
              <Option value="Viewer">查看者</Option>
            </Select>
          </Form.Item>

          <Form.Item
            label="状态"
            name="status"
            rules={[{ required: true, message: '请选择状态' }]}
          >
            <Select placeholder="请选择状态">
              <Option value="Active">活跃</Option>
              <Option value="Inactive">非活跃</Option>
              <Option value="Locked">锁定</Option>
            </Select>
          </Form.Item>

          {!editingUser && (
            <Form.Item
              label="密码"
              name="password"
              rules={[{ required: true, message: '请输入密码' }]}
            >
              <Input.Password placeholder="请输入密码" />
            </Form.Item>
          )}
        </Form>
      </Modal>
    </div>
  );
};

export default UserManagement;
