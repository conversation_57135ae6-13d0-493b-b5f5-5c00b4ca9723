using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using FlowCustomV1.Core.Extensions;
using FlowCustomV1.Core.Interfaces;
using FlowCustomV1.Core.Services;

namespace FlowCustomV1.Core.Tests.Extensions;

/// <summary>
/// 服务集合扩展方法测试
/// 验证依赖注入配置的正确性
/// </summary>
public class ServiceCollectionExtensionsTests
{
    /// <summary>
    /// 测试添加FlowCustomV1核心服务
    /// </summary>
    [Fact]
    public void AddFlowCustomV1Core_ShouldRegisterAllRequiredServices()
    {
        // Arrange
        var services = new ServiceCollection();
        var configuration = CreateTestConfiguration();

        // Act
        services.AddFlowCustomV1Core(configuration);

        // Assert
        var serviceProvider = services.BuildServiceProvider();
        
        // 验证所有核心服务都已注册
        Assert.NotNull(serviceProvider.GetService<IConfigurationService>());
        Assert.NotNull(serviceProvider.GetService<ILoggingService>());
        // 注意：IWorkflowEngine的实现在Engine层，Core层不提供实现
        // Assert.NotNull(serviceProvider.GetService<IWorkflowEngine>());
        Assert.NotNull(serviceProvider.GetService<INodeExecutor>());
        Assert.NotNull(serviceProvider.GetService<IClusterService>());
    }

    /// <summary>
    /// 测试添加日志服务
    /// </summary>
    [Fact]
    public void AddFlowCustomV1Logging_ShouldRegisterLoggingServices()
    {
        // Arrange
        var services = new ServiceCollection();

        // Act
        services.AddFlowCustomV1Logging();

        // Assert
        var serviceProvider = services.BuildServiceProvider();
        
        // 验证日志服务已注册
        Assert.NotNull(serviceProvider.GetService<ILoggingService>());
        Assert.NotNull(serviceProvider.GetService<ILogger<LoggingService>>());
    }

    /// <summary>
    /// 测试添加配置服务
    /// </summary>
    [Fact]
    public void AddFlowCustomV1Configuration_ShouldRegisterConfigurationServices()
    {
        // Arrange
        var services = new ServiceCollection();
        var configuration = CreateTestConfiguration();

        // Act
        services.AddFlowCustomV1Configuration(configuration);

        // Assert
        var serviceProvider = services.BuildServiceProvider();
        
        // 验证配置服务已注册
        Assert.NotNull(serviceProvider.GetService<IConfigurationService>());
        Assert.NotNull(serviceProvider.GetService<IConfiguration>());
    }

    /// <summary>
    /// 测试添加工作流引擎服务
    /// </summary>
    [Fact]
    public void AddFlowCustomV1WorkflowEngine_ShouldRegisterWorkflowEngine()
    {
        // Arrange
        var services = new ServiceCollection();
        var configuration = CreateTestConfiguration();
        services.AddSingleton(configuration); // ConfigurationService需要IConfiguration

        // Act
        services.AddFlowCustomV1WorkflowEngine();

        // Assert
        var serviceProvider = services.BuildServiceProvider();
        
        // 注意：IWorkflowEngine的实现现在在Engine层，Core层不再提供实现
        // 这个测试需要更新以反映新的架构
        // Assert.NotNull(serviceProvider.GetService<IWorkflowEngine>());
        // 验证其他核心服务已注册
        Assert.NotNull(serviceProvider.GetService<ILoggingService>());
        Assert.NotNull(serviceProvider.GetService<IConfigurationService>());
    }

    /// <summary>
    /// 测试添加节点执行器服务
    /// </summary>
    [Fact]
    public void AddFlowCustomV1NodeExecutor_ShouldRegisterNodeExecutor()
    {
        // Arrange
        var services = new ServiceCollection();

        // Act
        services.AddFlowCustomV1NodeExecutor();

        // Assert
        var serviceProvider = services.BuildServiceProvider();
        
        // 验证节点执行器已注册
        Assert.NotNull(serviceProvider.GetService<INodeExecutor>());
        Assert.IsType<NodeExecutor>(serviceProvider.GetService<INodeExecutor>());
    }

    /// <summary>
    /// 测试添加集群服务
    /// </summary>
    [Fact]
    public void AddFlowCustomV1ClusterService_ShouldRegisterClusterService()
    {
        // Arrange
        var services = new ServiceCollection();
        services.AddLogging(); // ClusterService需要ILoggingService

        // Act
        services.AddFlowCustomV1ClusterService();

        // Assert
        var serviceProvider = services.BuildServiceProvider();
        
        // 验证集群服务已注册
        Assert.NotNull(serviceProvider.GetService<IClusterService>());
        Assert.IsType<ClusterService>(serviceProvider.GetService<IClusterService>());
    }

    /// <summary>
    /// 测试服务注册验证 - 成功情况
    /// </summary>
    [Fact]
    public void ValidateServiceRegistration_WithAllServices_ShouldReturnValid()
    {
        // Arrange
        var services = new ServiceCollection();
        var configuration = CreateTestConfiguration();
        services.AddFlowCustomV1Core(configuration);

        // Act
        var result = services.ValidateServiceRegistration();

        // Assert
        Assert.True(result.IsValid);
        Assert.Empty(result.MissingServices);
        Assert.Null(result.ErrorMessage);
    }

    /// <summary>
    /// 测试服务注册验证 - 缺少服务情况
    /// </summary>
    [Fact]
    public void ValidateServiceRegistration_WithMissingServices_ShouldReturnInvalid()
    {
        // Arrange
        var services = new ServiceCollection();
        // 故意不注册任何服务

        // Act
        var result = services.ValidateServiceRegistration();

        // Assert
        Assert.False(result.IsValid);
        Assert.NotEmpty(result.MissingServices);
        Assert.Contains("ILoggingService", result.MissingServices);
        Assert.Contains("IConfigurationService", result.MissingServices);
        // 注意：IWorkflowEngine的实现在Engine层，Core层不验证此服务
        // Assert.Contains("IWorkflowEngine", result.MissingServices);
        Assert.Contains("INodeExecutor", result.MissingServices);
        Assert.Contains("IClusterService", result.MissingServices);
    }

    /// <summary>
    /// 测试服务生命周期
    /// </summary>
    [Fact]
    public void ServiceLifetimes_ShouldBeConfiguredCorrectly()
    {
        // Arrange
        var services = new ServiceCollection();
        var configuration = CreateTestConfiguration();
        services.AddFlowCustomV1Core(configuration);

        // Act & Assert
        var serviceProvider = services.BuildServiceProvider();
        
        // 验证单例服务
        var loggingService1 = serviceProvider.GetService<ILoggingService>();
        var loggingService2 = serviceProvider.GetService<ILoggingService>();
        Assert.Same(loggingService1, loggingService2);

        var configService1 = serviceProvider.GetService<IConfigurationService>();
        var configService2 = serviceProvider.GetService<IConfigurationService>();
        Assert.Same(configService1, configService2);

        // 验证作用域服务（使用INodeExecutor替代IWorkflowEngine，因为后者在Engine层）
        using var scope1 = serviceProvider.CreateScope();
        using var scope2 = serviceProvider.CreateScope();

        var nodeExecutor1 = scope1.ServiceProvider.GetService<INodeExecutor>();
        var nodeExecutor2 = scope1.ServiceProvider.GetService<INodeExecutor>();
        var nodeExecutor3 = scope2.ServiceProvider.GetService<INodeExecutor>();

        // 同一作用域内应该是同一实例
        Assert.Same(nodeExecutor1, nodeExecutor2);
        // 不同作用域应该是不同实例
        Assert.NotSame(nodeExecutor1, nodeExecutor3);
    }

    /// <summary>
    /// 测试空参数异常
    /// </summary>
    [Fact]
    public void AddFlowCustomV1Core_WithNullServices_ShouldThrowArgumentNullException()
    {
        // Arrange
        IServiceCollection? services = null;
        var configuration = CreateTestConfiguration();

        // Act & Assert
        Assert.Throws<ArgumentNullException>(() => services!.AddFlowCustomV1Core(configuration));
    }

    /// <summary>
    /// 测试空配置异常
    /// </summary>
    [Fact]
    public void AddFlowCustomV1Core_WithNullConfiguration_ShouldThrowArgumentNullException()
    {
        // Arrange
        var services = new ServiceCollection();
        IConfiguration? configuration = null;

        // Act & Assert
        Assert.Throws<ArgumentNullException>(() => services.AddFlowCustomV1Core(configuration!));
    }

    /// <summary>
    /// 创建测试配置
    /// </summary>
    /// <returns>测试配置对象</returns>
    private static IConfiguration CreateTestConfiguration()
    {
        var configurationBuilder = new ConfigurationBuilder();
        configurationBuilder.AddInMemoryCollection(new Dictionary<string, string?>
        {
            ["TestKey"] = "TestValue",
            ["Logging:LogLevel:Default"] = "Information"
        });
        
        return configurationBuilder.Build();
    }
}
