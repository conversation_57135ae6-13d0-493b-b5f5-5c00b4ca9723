#!/usr/bin/env python3
"""
FlowCustomV1 基础设施压力测试
专门测试NATS和MySQL的性能极限
"""

import requests
import time
import threading
import concurrent.futures
import statistics
import json
from datetime import datetime
import mysql.connector
import random
import string

# 测试配置
NATS_MONITOR_URL = "http://localhost:8222"
MYSQL_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'flowcustom',
    'password': 'FlowCustom@2025',
    'database': 'flowcustom_dev'
}

class InfrastructureStressTest:
    def __init__(self):
        self.results = {}
        
    def test_nats_performance(self):
        """测试NATS服务器性能"""
        print("\n🔥 NATS服务器压力测试")
        print("=" * 50)
        
        # 基础性能测试
        print("📊 NATS监控端点性能测试:")
        endpoints = {
            "server_info": "/varz",
            "connections": "/connz", 
            "jetstream": "/jsz",
            "subscriptions": "/subsz"
        }
        
        for name, path in endpoints.items():
            url = f"{NATS_MONITOR_URL}{path}"
            times = []
            
            print(f"\n  测试 {name}:")
            for i in range(20):  # 20次请求
                try:
                    start_time = time.time()
                    response = requests.get(url, timeout=5)
                    end_time = time.time()
                    
                    response_time = (end_time - start_time) * 1000
                    times.append(response_time)
                    
                    if i < 5:  # 只显示前5次
                        print(f"    请求 {i+1}: {response_time:.2f}ms")
                        
                except Exception as e:
                    print(f"    请求 {i+1}: 失败 ({str(e)})")
                    
            if times:
                avg_time = statistics.mean(times)
                min_time = min(times)
                max_time = max(times)
                
                print(f"    平均响应时间: {avg_time:.2f}ms")
                print(f"    最快/最慢: {min_time:.2f}ms / {max_time:.2f}ms")
                
                self.results[f"nats_{name}"] = {
                    "avg_time": avg_time,
                    "min_time": min_time,
                    "max_time": max_time,
                    "requests": len(times)
                }
        
        # 并发压力测试
        self.test_nats_concurrent_load()
        
    def test_nats_concurrent_load(self):
        """NATS并发负载测试"""
        print(f"\n🚀 NATS并发负载测试:")
        
        def make_nats_requests(thread_id, requests_count=50):
            times = []
            success_count = 0
            
            for i in range(requests_count):
                try:
                    start_time = time.time()
                    response = requests.get(f"{NATS_MONITOR_URL}/varz", timeout=5)
                    end_time = time.time()
                    
                    if response.status_code == 200:
                        success_count += 1
                        times.append((end_time - start_time) * 1000)
                        
                except Exception:
                    pass
                    
            return times, success_count
        
        # 测试不同并发级别
        concurrent_levels = [5, 10, 20, 50]
        
        for concurrent_users in concurrent_levels:
            print(f"\n  并发用户数: {concurrent_users}")
            
            start_time = time.time()
            
            with concurrent.futures.ThreadPoolExecutor(max_workers=concurrent_users) as executor:
                futures = [executor.submit(make_nats_requests, i, 20) for i in range(concurrent_users)]
                results = [future.result() for future in concurrent.futures.as_completed(futures)]
            
            end_time = time.time()
            total_duration = end_time - start_time
            
            # 汇总结果
            all_times = []
            total_success = 0
            
            for times, success in results:
                all_times.extend(times)
                total_success += success
                
            total_requests = concurrent_users * 20
            
            if all_times:
                avg_time = statistics.mean(all_times)
                throughput = total_requests / total_duration
                
                print(f"    总耗时: {total_duration:.2f}秒")
                print(f"    成功率: {total_success}/{total_requests} ({total_success/total_requests*100:.1f}%)")
                print(f"    吞吐量: {throughput:.2f} 请求/秒")
                print(f"    平均响应时间: {avg_time:.2f}ms")
                
                self.results[f"nats_concurrent_{concurrent_users}"] = {
                    "throughput": throughput,
                    "success_rate": total_success/total_requests,
                    "avg_time": avg_time,
                    "total_duration": total_duration
                }
    
    def test_mysql_performance(self):
        """测试MySQL数据库性能"""
        print("\n🔥 MySQL数据库压力测试")
        print("=" * 50)
        
        try:
            # 连接测试
            print("📊 MySQL连接性能测试:")
            connection_times = []
            
            for i in range(10):
                try:
                    start_time = time.time()
                    conn = mysql.connector.connect(**MYSQL_CONFIG)
                    end_time = time.time()
                    
                    connection_time = (end_time - start_time) * 1000
                    connection_times.append(connection_time)
                    
                    conn.close()
                    print(f"  连接 {i+1}: {connection_time:.2f}ms")
                    
                except Exception as e:
                    print(f"  连接 {i+1}: 失败 ({str(e)})")
                    
            if connection_times:
                avg_conn_time = statistics.mean(connection_times)
                print(f"  平均连接时间: {avg_conn_time:.2f}ms")
                
                self.results["mysql_connection"] = {
                    "avg_time": avg_conn_time,
                    "min_time": min(connection_times),
                    "max_time": max(connection_times)
                }
            
            # 查询性能测试
            self.test_mysql_queries()
            
            # 并发测试
            self.test_mysql_concurrent_load()
            
        except Exception as e:
            print(f"❌ MySQL连接失败: {str(e)}")
            
    def test_mysql_queries(self):
        """MySQL查询性能测试"""
        print(f"\n📊 MySQL查询性能测试:")
        
        try:
            conn = mysql.connector.connect(**MYSQL_CONFIG)
            cursor = conn.cursor()
            
            # 测试不同类型的查询
            queries = {
                "simple_select": "SELECT 1",
                "show_tables": "SHOW TABLES",
                "database_info": "SELECT DATABASE(), VERSION(), NOW()",
                "table_status": "SHOW TABLE STATUS"
            }
            
            for query_name, query in queries.items():
                times = []
                
                print(f"\n  测试查询: {query_name}")
                for i in range(20):
                    try:
                        start_time = time.time()
                        cursor.execute(query)
                        results = cursor.fetchall()
                        end_time = time.time()
                        
                        query_time = (end_time - start_time) * 1000
                        times.append(query_time)
                        
                        if i < 3:  # 只显示前3次
                            print(f"    查询 {i+1}: {query_time:.2f}ms (结果数: {len(results)})")
                            
                    except Exception as e:
                        print(f"    查询 {i+1}: 失败 ({str(e)})")
                        
                if times:
                    avg_time = statistics.mean(times)
                    print(f"    平均查询时间: {avg_time:.2f}ms")
                    
                    self.results[f"mysql_{query_name}"] = {
                        "avg_time": avg_time,
                        "min_time": min(times),
                        "max_time": max(times)
                    }
            
            cursor.close()
            conn.close()
            
        except Exception as e:
            print(f"❌ MySQL查询测试失败: {str(e)}")
            
    def test_mysql_concurrent_load(self):
        """MySQL并发负载测试"""
        print(f"\n🚀 MySQL并发负载测试:")
        
        def make_mysql_queries(thread_id, queries_count=20):
            times = []
            success_count = 0
            
            try:
                conn = mysql.connector.connect(**MYSQL_CONFIG)
                cursor = conn.cursor()
                
                for i in range(queries_count):
                    try:
                        start_time = time.time()
                        cursor.execute("SELECT DATABASE(), CONNECTION_ID(), NOW()")
                        results = cursor.fetchall()
                        end_time = time.time()
                        
                        success_count += 1
                        times.append((end_time - start_time) * 1000)
                        
                    except Exception:
                        pass
                        
                cursor.close()
                conn.close()
                
            except Exception:
                pass
                
            return times, success_count
        
        # 测试不同并发级别
        concurrent_levels = [5, 10, 20]
        
        for concurrent_users in concurrent_levels:
            print(f"\n  并发连接数: {concurrent_users}")
            
            start_time = time.time()
            
            with concurrent.futures.ThreadPoolExecutor(max_workers=concurrent_users) as executor:
                futures = [executor.submit(make_mysql_queries, i, 10) for i in range(concurrent_users)]
                results = [future.result() for future in concurrent.futures.as_completed(futures)]
            
            end_time = time.time()
            total_duration = end_time - start_time
            
            # 汇总结果
            all_times = []
            total_success = 0
            
            for times, success in results:
                all_times.extend(times)
                total_success += success
                
            total_requests = concurrent_users * 10
            
            if all_times:
                avg_time = statistics.mean(all_times)
                throughput = total_requests / total_duration
                
                print(f"    总耗时: {total_duration:.2f}秒")
                print(f"    成功率: {total_success}/{total_requests} ({total_success/total_requests*100:.1f}%)")
                print(f"    吞吐量: {throughput:.2f} 查询/秒")
                print(f"    平均查询时间: {avg_time:.2f}ms")
                
                self.results[f"mysql_concurrent_{concurrent_users}"] = {
                    "throughput": throughput,
                    "success_rate": total_success/total_requests,
                    "avg_time": avg_time,
                    "total_duration": total_duration
                }
    
    def test_system_limits(self):
        """测试系统极限"""
        print("\n🔥 系统极限测试")
        print("=" * 50)
        
        # 获取NATS服务器信息
        try:
            response = requests.get(f"{NATS_MONITOR_URL}/varz", timeout=5)
            if response.status_code == 200:
                nats_info = response.json()
                print(f"📊 NATS服务器资源使用:")
                print(f"  内存使用: {nats_info.get('mem', 0) / 1024 / 1024:.2f} MB")
                print(f"  CPU使用: {nats_info.get('cpu', 0):.2f}%")
                print(f"  连接数: {nats_info.get('connections', 0)}")
                print(f"  最大连接数: {nats_info.get('max_connections', 'N/A')}")
                
        except Exception as e:
            print(f"❌ 获取NATS信息失败: {str(e)}")
            
        # 获取JetStream信息
        try:
            response = requests.get(f"{NATS_MONITOR_URL}/jsz", timeout=5)
            if response.status_code == 200:
                js_info = response.json()
                print(f"\n📊 JetStream资源使用:")
                print(f"  内存使用: {js_info.get('memory', 0) / 1024:.2f} KB")
                print(f"  存储使用: {js_info.get('store', 0) / 1024:.2f} KB")
                print(f"  API错误数: {js_info.get('api', {}).get('errors', 0)}")
                
        except Exception as e:
            print(f"❌ 获取JetStream信息失败: {str(e)}")
    
    def generate_stress_report(self):
        """生成压力测试报告"""
        print("\n" + "=" * 60)
        print("📋 FlowCustomV1 基础设施压力测试报告")
        print("=" * 60)
        print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 运行所有测试
        self.test_nats_performance()
        self.test_mysql_performance()
        self.test_system_limits()
        
        # 生成总结
        self.print_stress_summary()
        
    def print_stress_summary(self):
        """打印压力测试总结"""
        print("\n" + "=" * 60)
        print("🎯 基础设施压力测试总结")
        print("=" * 60)
        
        print("\n🔸 NATS性能表现:")
        for key, data in self.results.items():
            if key.startswith("nats_"):
                if "concurrent" in key:
                    print(f"  {key}: {data['throughput']:.2f} req/s (并发)")
                else:
                    print(f"  {key}: {data['avg_time']:.2f}ms (平均)")
                    
        print("\n🔸 MySQL性能表现:")
        for key, data in self.results.items():
            if key.startswith("mysql_"):
                if "concurrent" in key:
                    print(f"  {key}: {data['throughput']:.2f} queries/s (并发)")
                else:
                    print(f"  {key}: {data['avg_time']:.2f}ms (平均)")
        
        print(f"\n测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    test = InfrastructureStressTest()
    test.generate_stress_report()
