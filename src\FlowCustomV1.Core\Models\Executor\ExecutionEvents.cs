using FlowCustomV1.Core.Models.Workflow;
using System.Text.Json.Serialization;

namespace FlowCustomV1.Core.Models.Executor;

/// <summary>
/// 执行事件基类
/// </summary>
public abstract class ExecutionEvent
{
    /// <summary>
    /// 事件ID
    /// </summary>
    public string EventId { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 执行ID
    /// </summary>
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 工作流ID
    /// </summary>
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 事件时间戳
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 事件类型
    /// </summary>
    public abstract ExecutionEventType EventType { get; }

    /// <summary>
    /// 节点ID
    /// </summary>
    public string NodeId { get; set; } = string.Empty;

    /// <summary>
    /// 事件数据
    /// </summary>
    public Dictionary<string, object> Data { get; set; } = new();

    /// <summary>
    /// 事件元数据
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// 执行开始事件参数
/// </summary>
public class ExecutionStartedEventArgs : EventArgs
{
    /// <summary>
    /// 执行ID
    /// </summary>
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 工作流ID
    /// </summary>
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 工作流名称
    /// </summary>
    public string WorkflowName { get; set; } = string.Empty;

    /// <summary>
    /// 执行节点ID
    /// </summary>
    public string ExecutorNodeId { get; set; } = string.Empty;

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime StartedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 输入数据
    /// </summary>
    public Dictionary<string, object> InputData { get; set; } = new();

    /// <summary>
    /// 执行优先级
    /// </summary>
    public ExecutionPriority Priority { get; set; } = ExecutionPriority.Normal;
}

/// <summary>
/// 执行完成事件参数
/// </summary>
public class ExecutionCompletedEventArgs : EventArgs
{
    /// <summary>
    /// 执行ID
    /// </summary>
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 工作流ID
    /// </summary>
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 执行结果
    /// </summary>
    public WorkflowExecutionResult Result { get; set; } = new();

    /// <summary>
    /// 完成时间
    /// </summary>
    public DateTime CompletedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 执行时长
    /// </summary>
    public TimeSpan Duration { get; set; }

    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }
}

/// <summary>
/// 执行失败事件参数
/// </summary>
public class ExecutionFailedEventArgs : EventArgs
{
    /// <summary>
    /// 执行ID
    /// </summary>
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 工作流ID
    /// </summary>
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 失败时间
    /// </summary>
    public DateTime FailedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 错误信息
    /// </summary>
    public string ErrorMessage { get; set; } = string.Empty;

    /// <summary>
    /// 异常信息
    /// </summary>
    public Exception? Exception { get; set; }

    /// <summary>
    /// 失败的节点ID
    /// </summary>
    public string? FailedNodeId { get; set; }

    /// <summary>
    /// 重试次数
    /// </summary>
    public int RetryCount { get; set; }

    /// <summary>
    /// 是否可以重试
    /// </summary>
    public bool CanRetry { get; set; }
}

/// <summary>
/// 执行状态变更事件参数
/// </summary>
public class ExecutionStatusChangedEventArgs : EventArgs
{
    /// <summary>
    /// 执行ID
    /// </summary>
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 工作流ID
    /// </summary>
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 旧状态
    /// </summary>
    public WorkflowExecutionState OldState { get; set; }

    /// <summary>
    /// 新状态
    /// </summary>
    public WorkflowExecutionState NewState { get; set; }

    /// <summary>
    /// 状态变更时间
    /// </summary>
    public DateTime ChangedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 变更原因
    /// </summary>
    public string Reason { get; set; } = string.Empty;

    /// <summary>
    /// 节点ID（如果是节点状态变更）
    /// </summary>
    public string? NodeId { get; set; }
}

/// <summary>
/// 执行状态变更事件
/// </summary>
public class ExecutionStateChangedEvent : ExecutionEvent
{
    /// <summary>
    /// 事件类型
    /// </summary>
    public override ExecutionEventType EventType => ExecutionEventType.StateChanged;

    /// <summary>
    /// 旧状态
    /// </summary>
    public WorkflowExecutionState OldState { get; set; }

    /// <summary>
    /// 新状态
    /// </summary>
    public WorkflowExecutionState NewState { get; set; }

    /// <summary>
    /// 变更原因
    /// </summary>
    public string Reason { get; set; } = string.Empty;
}

/// <summary>
/// 执行事件类型
/// </summary>
[JsonConverter(typeof(JsonStringEnumConverter))]
public enum ExecutionEventType
{
    /// <summary>
    /// 执行开始
    /// </summary>
    Started,

    /// <summary>
    /// 执行完成
    /// </summary>
    Completed,

    /// <summary>
    /// 执行失败
    /// </summary>
    Failed,

    /// <summary>
    /// 执行取消
    /// </summary>
    Cancelled,

    /// <summary>
    /// 执行暂停
    /// </summary>
    Paused,

    /// <summary>
    /// 执行恢复
    /// </summary>
    Resumed,

    /// <summary>
    /// 状态变更
    /// </summary>
    StateChanged,

    /// <summary>
    /// 节点开始
    /// </summary>
    NodeStarted,

    /// <summary>
    /// 节点完成
    /// </summary>
    NodeCompleted,

    /// <summary>
    /// 节点失败
    /// </summary>
    NodeFailed,

    /// <summary>
    /// 执行迁移
    /// </summary>
    Migrated,

    /// <summary>
    /// 资源分配
    /// </summary>
    ResourceAllocated,

    /// <summary>
    /// 资源释放
    /// </summary>
    ResourceReleased
}
