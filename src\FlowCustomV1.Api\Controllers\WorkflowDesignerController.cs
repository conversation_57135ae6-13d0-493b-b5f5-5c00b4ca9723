using Microsoft.AspNetCore.Mvc;
using FlowCustomV1.Core.Interfaces.Services;
using FlowCustomV1.Core.Models.Designer;
using FlowCustomV1.Core.Models.Workflow;

namespace FlowCustomV1.Api.Controllers;

/// <summary>
/// 工作流设计控制器
/// 提供工作流设计、模板管理和版本控制功能
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public class WorkflowDesignerController : ControllerBase
{
    private readonly IWorkflowDesignerService _designerService;
    private readonly ILogger<WorkflowDesignerController> _logger;

    /// <summary>
    /// 构造函数
    /// </summary>
    public WorkflowDesignerController(
        IWorkflowDesignerService designerService,
        ILogger<WorkflowDesignerController> logger)
    {
        _designerService = designerService ?? throw new ArgumentNullException(nameof(designerService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    #region 工作流设计

    /// <summary>
    /// 从模板创建工作流
    /// </summary>
    /// <param name="template">工作流模板</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>创建的工作流定义</returns>
    [HttpPost("workflows")]
    [ProducesResponseType(typeof(WorkflowDefinition), 201)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    public async Task<ActionResult<WorkflowDefinition>> CreateWorkflowAsync(
        [FromBody] WorkflowTemplate template,
        CancellationToken cancellationToken = default)
    {
        try
        {
            if (template == null)
            {
                return BadRequest("Template is required");
            }

            var workflow = await _designerService.CreateWorkflowAsync(template, cancellationToken);
            return Created($"/api/workflowdesigner/{workflow.WorkflowId}", workflow);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create workflow from template {TemplateId}", template?.TemplateId);
            return StatusCode(500, $"Failed to create workflow: {ex.Message}");
        }
    }

    /// <summary>
    /// 获取工作流定义
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>工作流定义</returns>
    [HttpGet("workflows/{workflowId}")]
    [ProducesResponseType(typeof(WorkflowDefinition), 200)]
    [ProducesResponseType(404)]
    [ProducesResponseType(500)]
    public async Task<ActionResult<WorkflowDefinition>> GetWorkflowAsync(
        string workflowId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var workflow = await _designerService.GetWorkflowAsync(workflowId, cancellationToken);
            if (workflow == null)
            {
                return NotFound($"Workflow {workflowId} not found");
            }

            return Ok(workflow);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get workflow {WorkflowId}", workflowId);
            return StatusCode(500, $"Failed to get workflow: {ex.Message}");
        }
    }

    /// <summary>
    /// 更新工作流定义
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="workflow">工作流定义</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>更新结果</returns>
    [HttpPut("workflows/{workflowId}")]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(404)]
    [ProducesResponseType(500)]
    public async Task<ActionResult> UpdateWorkflowAsync(
        string workflowId,
        [FromBody] WorkflowDefinition workflow,
        CancellationToken cancellationToken = default)
    {
        try
        {
            if (workflow == null)
            {
                return BadRequest("Workflow definition is required");
            }

            if (workflowId != workflow.WorkflowId)
            {
                return BadRequest("Workflow ID mismatch");
            }

            var success = await _designerService.UpdateWorkflowAsync(workflowId, workflow, cancellationToken);
            if (!success)
            {
                return NotFound($"Workflow {workflowId} not found or update failed");
            }

            return Ok(new { message = "Workflow updated successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update workflow {WorkflowId}", workflowId);
            return StatusCode(500, $"Failed to update workflow: {ex.Message}");
        }
    }

    /// <summary>
    /// 删除工作流
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>删除结果</returns>
    [HttpDelete("workflows/{workflowId}")]
    [ProducesResponseType(200)]
    [ProducesResponseType(404)]
    [ProducesResponseType(500)]
    public async Task<ActionResult> DeleteWorkflowAsync(
        string workflowId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var success = await _designerService.DeleteWorkflowAsync(workflowId, cancellationToken);
            if (!success)
            {
                return NotFound($"Workflow {workflowId} not found or delete failed");
            }

            return Ok(new { message = "Workflow deleted successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete workflow {WorkflowId}", workflowId);
            return StatusCode(500, $"Failed to delete workflow: {ex.Message}");
        }
    }

    /// <summary>
    /// 获取工作流列表
    /// </summary>
    /// <param name="name">名称过滤</param>
    /// <param name="author">作者过滤</param>
    /// <param name="pageSize">分页大小</param>
    /// <param name="pageNumber">页码</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>工作流列表</returns>
    [HttpGet("workflows")]
    [ProducesResponseType(typeof(IReadOnlyList<WorkflowDefinition>), 200)]
    [ProducesResponseType(500)]
    public async Task<ActionResult<IReadOnlyList<WorkflowDefinition>>> GetWorkflowsAsync(
        [FromQuery] string? name = null,
        [FromQuery] string? author = null,
        [FromQuery] int pageSize = 20,
        [FromQuery] int pageNumber = 1,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new WorkflowQuery
            {
                Name = name,
                Author = author,
                PageSize = pageSize,
                PageNumber = pageNumber
            };

            var workflows = await _designerService.GetWorkflowsAsync(query, cancellationToken);
            return Ok(workflows);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get workflows");
            return StatusCode(500, $"Failed to get workflows: {ex.Message}");
        }
    }

    #endregion

    #region 模板管理

    /// <summary>
    /// 获取工作流模板列表
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>模板列表</returns>
    [HttpGet("templates")]
    [ProducesResponseType(typeof(IReadOnlyList<WorkflowTemplate>), 200)]
    [ProducesResponseType(500)]
    public async Task<ActionResult<IReadOnlyList<WorkflowTemplate>>> GetTemplatesAsync(
        CancellationToken cancellationToken = default)
    {
        try
        {
            var templates = await _designerService.GetTemplatesAsync(cancellationToken);
            return Ok(templates);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get templates");
            return StatusCode(500, $"Failed to get templates: {ex.Message}");
        }
    }

    /// <summary>
    /// 创建工作流模板
    /// </summary>
    /// <param name="template">模板定义</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>创建结果</returns>
    [HttpPost("templates")]
    [ProducesResponseType(201)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    public async Task<ActionResult> CreateTemplateAsync(
        [FromBody] WorkflowTemplate template,
        CancellationToken cancellationToken = default)
    {
        try
        {
            if (template == null)
            {
                return BadRequest("Template is required");
            }

            var success = await _designerService.CreateTemplateAsync(template, cancellationToken);
            if (!success)
            {
                return StatusCode(500, "Failed to create template");
            }

            return Created($"/api/workflowdesigner/templates/{template.TemplateId}", template);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create template {TemplateId}", template?.TemplateId);
            return StatusCode(500, $"Failed to create template: {ex.Message}");
        }
    }

    /// <summary>
    /// 更新工作流模板
    /// </summary>
    /// <param name="templateId">模板ID</param>
    /// <param name="template">模板定义</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>更新结果</returns>
    [HttpPut("templates/{templateId}")]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(404)]
    [ProducesResponseType(500)]
    public async Task<ActionResult> UpdateTemplateAsync(
        string templateId,
        [FromBody] WorkflowTemplate template,
        CancellationToken cancellationToken = default)
    {
        try
        {
            if (template == null)
            {
                return BadRequest("Template is required");
            }

            if (templateId != template.TemplateId)
            {
                return BadRequest("Template ID mismatch");
            }

            var success = await _designerService.UpdateTemplateAsync(templateId, template, cancellationToken);
            if (!success)
            {
                return NotFound($"Template {templateId} not found or update failed");
            }

            return Ok(new { message = "Template updated successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update template {TemplateId}", templateId);
            return StatusCode(500, $"Failed to update template: {ex.Message}");
        }
    }

    /// <summary>
    /// 删除工作流模板
    /// </summary>
    /// <param name="templateId">模板ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>删除结果</returns>
    [HttpDelete("templates/{templateId}")]
    [ProducesResponseType(200)]
    [ProducesResponseType(404)]
    [ProducesResponseType(500)]
    public async Task<ActionResult> DeleteTemplateAsync(
        string templateId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var success = await _designerService.DeleteTemplateAsync(templateId, cancellationToken);
            if (!success)
            {
                return NotFound($"Template {templateId} not found or delete failed");
            }

            return Ok(new { message = "Template deleted successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete template {TemplateId}", templateId);
            return StatusCode(500, $"Failed to delete template: {ex.Message}");
        }
    }

    #endregion

    #region 版本控制

    /// <summary>
    /// 获取工作流版本历史
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>版本历史</returns>
    [HttpGet("workflows/{workflowId}/versions")]
    [ProducesResponseType(typeof(IReadOnlyList<WorkflowVersion>), 200)]
    [ProducesResponseType(404)]
    [ProducesResponseType(500)]
    public async Task<ActionResult<IReadOnlyList<WorkflowVersion>>> GetVersionHistoryAsync(
        string workflowId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var versions = await _designerService.GetVersionHistoryAsync(workflowId, cancellationToken);
            return Ok(versions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get version history for workflow {WorkflowId}", workflowId);
            return StatusCode(500, $"Failed to get version history: {ex.Message}");
        }
    }

    /// <summary>
    /// 创建工作流版本
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="versionInfo">版本信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>版本ID</returns>
    [HttpPost("workflows/{workflowId}/versions")]
    [ProducesResponseType(typeof(string), 201)]
    [ProducesResponseType(400)]
    [ProducesResponseType(404)]
    [ProducesResponseType(500)]
    public async Task<ActionResult<string>> CreateVersionAsync(
        string workflowId,
        [FromBody] WorkflowVersionInfo versionInfo,
        CancellationToken cancellationToken = default)
    {
        try
        {
            if (versionInfo == null)
            {
                return BadRequest("Version info is required");
            }

            var versionId = await _designerService.CreateVersionAsync(workflowId, versionInfo, cancellationToken);
            if (string.IsNullOrEmpty(versionId))
            {
                return NotFound($"Workflow {workflowId} not found or version creation failed");
            }

            return Created($"/api/workflowdesigner/{workflowId}/versions/{versionInfo.Version}", versionId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create version for workflow {WorkflowId}", workflowId);
            return StatusCode(500, $"Failed to create version: {ex.Message}");
        }
    }

    /// <summary>
    /// 获取指定版本的工作流定义
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="version">版本号</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>工作流定义</returns>
    [HttpGet("workflows/{workflowId}/versions/{version}")]
    [ProducesResponseType(typeof(WorkflowDefinition), 200)]
    [ProducesResponseType(404)]
    [ProducesResponseType(500)]
    public async Task<ActionResult<WorkflowDefinition>> GetWorkflowVersionAsync(
        string workflowId,
        string version,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var workflow = await _designerService.GetWorkflowVersionAsync(workflowId, version, cancellationToken);
            if (workflow == null)
            {
                return NotFound($"Workflow {workflowId} version {version} not found");
            }

            return Ok(workflow);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get workflow {WorkflowId} version {Version}", workflowId, version);
            return StatusCode(500, $"Failed to get workflow version: {ex.Message}");
        }
    }

    #endregion

    #region 协作功能

    /// <summary>
    /// 广播设计变更
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="operation">设计操作</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>广播结果</returns>
    [HttpPost("workflows/{workflowId}/operations")]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(404)]
    [ProducesResponseType(500)]
    public async Task<ActionResult> BroadcastDesignChangeAsync(
        string workflowId,
        [FromBody] DesignOperation operation,
        CancellationToken cancellationToken = default)
    {
        try
        {
            if (operation == null)
            {
                return BadRequest("Operation is required");
            }

            await _designerService.BroadcastDesignChangeAsync(workflowId, operation, cancellationToken);
            return Ok(new { message = "Design change broadcasted successfully", workflowId, operationId = operation.OperationId });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to broadcast design change for workflow {WorkflowId}", workflowId);
            return StatusCode(500, $"Failed to broadcast design change: {ex.Message}");
        }
    }

    /// <summary>
    /// 获取活跃协作者
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>协作者列表</returns>
    [HttpGet("workflows/{workflowId}/collaborators")]
    [ProducesResponseType(typeof(IReadOnlyList<CollaboratorInfo>), 200)]
    [ProducesResponseType(404)]
    [ProducesResponseType(500)]
    public async Task<ActionResult<IReadOnlyList<CollaboratorInfo>>> GetActiveCollaboratorsAsync(
        string workflowId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var collaborators = await _designerService.GetActiveCollaboratorsAsync(workflowId, cancellationToken);
            return Ok(collaborators);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get active collaborators for workflow {WorkflowId}", workflowId);
            return StatusCode(500, $"Failed to get active collaborators: {ex.Message}");
        }
    }

    /// <summary>
    /// 加入协作会话
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="collaborator">协作者信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>加入结果</returns>
    [HttpPost("workflows/{workflowId}/collaborators")]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(404)]
    [ProducesResponseType(500)]
    public async Task<ActionResult> JoinCollaborationAsync(
        string workflowId,
        [FromBody] CollaboratorInfo collaborator,
        CancellationToken cancellationToken = default)
    {
        try
        {
            if (collaborator == null)
            {
                return BadRequest("Collaborator info is required");
            }

            var success = await _designerService.JoinCollaborationAsync(workflowId, collaborator, cancellationToken);
            if (!success)
            {
                return NotFound($"Workflow {workflowId} not found or join failed");
            }

            return Ok(new { message = "Joined collaboration successfully", workflowId, collaboratorId = collaborator.CollaboratorId });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to join collaboration for workflow {WorkflowId}", workflowId);
            return StatusCode(500, $"Failed to join collaboration: {ex.Message}");
        }
    }

    /// <summary>
    /// 离开协作会话
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="collaboratorId">协作者ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>离开结果</returns>
    [HttpDelete("workflows/{workflowId}/collaborators/{collaboratorId}")]
    [ProducesResponseType(200)]
    [ProducesResponseType(404)]
    [ProducesResponseType(500)]
    public async Task<ActionResult> LeaveCollaborationAsync(
        string workflowId,
        string collaboratorId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var success = await _designerService.LeaveCollaborationAsync(workflowId, collaboratorId, cancellationToken);
            if (!success)
            {
                return NotFound($"Workflow {workflowId} or collaborator {collaboratorId} not found");
            }

            return Ok(new { message = "Left collaboration successfully", workflowId, collaboratorId });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to leave collaboration for workflow {WorkflowId}", workflowId);
            return StatusCode(500, $"Failed to leave collaboration: {ex.Message}");
        }
    }

    /// <summary>
    /// 解决设计冲突
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="conflictResolution">冲突解决方案</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>解决结果</returns>
    [HttpPost("workflows/{workflowId}/conflicts/resolve")]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(404)]
    [ProducesResponseType(500)]
    public async Task<ActionResult> ResolveDesignConflictAsync(
        string workflowId,
        [FromBody] ConflictResolution conflictResolution,
        CancellationToken cancellationToken = default)
    {
        try
        {
            if (conflictResolution == null)
            {
                return BadRequest("Conflict resolution is required");
            }

            var success = await _designerService.ResolveDesignConflictAsync(workflowId, conflictResolution, cancellationToken);
            if (!success)
            {
                return NotFound($"Workflow {workflowId} not found or conflict resolution failed");
            }

            return Ok(new { message = "Design conflict resolved successfully", workflowId, conflictId = conflictResolution.ConflictId });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to resolve design conflict for workflow {WorkflowId}", workflowId);
            return StatusCode(500, $"Failed to resolve design conflict: {ex.Message}");
        }
    }

    #endregion
}
