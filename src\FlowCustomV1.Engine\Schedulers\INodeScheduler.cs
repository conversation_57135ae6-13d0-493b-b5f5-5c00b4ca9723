using FlowCustomV1.Engine.Context;

namespace FlowCustomV1.Engine.Schedulers;

/// <summary>
/// 节点调度器接口
/// 负责管理和调度工作流节点的执行
/// </summary>
public interface INodeScheduler : IDisposable
{
    /// <summary>
    /// 调度器唯一标识符
    /// </summary>
    string SchedulerId { get; }

    /// <summary>
    /// 当前调度器状态
    /// </summary>
    SchedulerState State { get; }

    /// <summary>
    /// 调度器统计信息
    /// </summary>
    SchedulerStatistics Statistics { get; }

    /// <summary>
    /// 最大并发执行数
    /// </summary>
    int MaxConcurrentExecutions { get; set; }

    /// <summary>
    /// 启动调度器
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>启动任务</returns>
    Task StartAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 停止调度器
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>停止任务</returns>
    Task StopAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 暂停调度器
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>暂停任务</returns>
    Task PauseAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 恢复调度器
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>恢复任务</returns>
    Task ResumeAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 调度节点执行
    /// </summary>
    /// <param name="request">调度请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>调度任务</returns>
    Task ScheduleNodeAsync(NodeScheduleRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// 批量调度节点执行
    /// </summary>
    /// <param name="requests">调度请求列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>调度任务</returns>
    Task ScheduleNodesAsync(IEnumerable<NodeScheduleRequest> requests, CancellationToken cancellationToken = default);

    /// <summary>
    /// 取消指定节点的执行
    /// </summary>
    /// <param name="nodeId">节点ID</param>
    /// <param name="executionId">执行ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>取消任务</returns>
    Task CancelNodeExecutionAsync(string nodeId, string executionId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 取消指定工作流的所有节点执行
    /// </summary>
    /// <param name="executionId">工作流执行ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>取消任务</returns>
    Task CancelWorkflowExecutionAsync(string executionId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取当前队列中的请求数量
    /// </summary>
    /// <returns>队列中的请求数量</returns>
    int GetQueuedRequestCount();

    /// <summary>
    /// 获取当前正在执行的节点数量
    /// </summary>
    /// <returns>正在执行的节点数量</returns>
    int GetActiveExecutionCount();

    /// <summary>
    /// 获取指定工作流的执行状态
    /// </summary>
    /// <param name="executionId">工作流执行ID</param>
    /// <returns>执行状态信息</returns>
    Task<Dictionary<string, object>> GetWorkflowExecutionStatusAsync(string executionId);

    /// <summary>
    /// 清空调度队列
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>清空任务</returns>
    Task ClearQueueAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 重置调度器统计信息
    /// </summary>
    void ResetStatistics();

    /// <summary>
    /// 节点调度完成事件
    /// </summary>
    event EventHandler<NodeScheduledEventArgs>? NodeScheduled;

    /// <summary>
    /// 节点执行完成事件
    /// </summary>
    event EventHandler<NodeCompletedEventArgs>? NodeCompleted;

    /// <summary>
    /// 节点执行错误事件
    /// </summary>
    event EventHandler<NodeExecutionErrorEventArgs>? NodeExecutionError;

    /// <summary>
    /// 调度器状态变更事件
    /// </summary>
    event EventHandler<SchedulerStateChangedEventArgs>? StateChanged;
}

/// <summary>
/// 调度器配置选项
/// </summary>
public class SchedulerOptions
{
    /// <summary>
    /// 调度器ID
    /// </summary>
    public string SchedulerId { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 最大并发执行数
    /// </summary>
    public int MaxConcurrentExecutions { get; set; } = 10;

    /// <summary>
    /// 队列容量
    /// </summary>
    public int QueueCapacity { get; set; } = 1000;

    /// <summary>
    /// 是否启用优先级调度
    /// </summary>
    public bool EnablePriorityScheduling { get; set; } = true;

    /// <summary>
    /// 调度延迟检查间隔（毫秒）
    /// </summary>
    public int DelayCheckIntervalMs { get; set; } = 100;

    /// <summary>
    /// 统计信息更新间隔（毫秒）
    /// </summary>
    public int StatisticsUpdateIntervalMs { get; set; } = 1000;

    /// <summary>
    /// 是否启用详细日志
    /// </summary>
    public bool EnableDetailedLogging { get; set; } = false;

    /// <summary>
    /// 节点执行超时时间（分钟）
    /// </summary>
    public int NodeExecutionTimeoutMinutes { get; set; } = 30;

    /// <summary>
    /// 是否启用执行超时检查
    /// </summary>
    public bool EnableExecutionTimeoutCheck { get; set; } = true;

    /// <summary>
    /// 超时检查间隔（毫秒）
    /// </summary>
    public int TimeoutCheckIntervalMs { get; set; } = 5000;
}
