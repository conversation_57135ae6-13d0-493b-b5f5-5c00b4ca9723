#!/bin/bash

# FlowCustomV1 测试协调器启动脚本
# 负责协调和执行分布式集群测试

set -e

echo "=========================================="
echo "FlowCustomV1 Test Coordinator Startup"
echo "=========================================="
echo "Test Environment: ${TEST_ENVIRONMENT:-Unknown}"
echo "Cluster Nodes: ${CLUSTER_NODES:-Unknown}"
echo "NATS Servers: ${NATS_SERVERS:-Unknown}"
echo "Test Results Path: ${TEST_RESULTS_PATH:-/app/test-results}"
echo "=========================================="

# 等待集群节点启动
echo "Waiting for cluster nodes to be ready..."

if [ ! -z "$CLUSTER_NODES" ]; then
    IFS=',' read -ra NODE_ARRAY <<< "$CLUSTER_NODES"
    for node_endpoint in "${NODE_ARRAY[@]}"; do
        node_host=$(echo $node_endpoint | cut -d':' -f1)
        node_port=$(echo $node_endpoint | cut -d':' -f2)
        
        echo "Waiting for node: $node_host:$node_port"
        max_attempts=60
        attempt=0
        
        while [ $attempt -lt $max_attempts ]; do
            if curl -f -s "http://$node_host:$node_port/health" > /dev/null 2>&1; then
                echo "  ✓ Node $node_host:$node_port is ready"
                break
            else
                echo "  Node $node_host:$node_port is not ready, waiting... (attempt $((attempt+1))/$max_attempts)"
                sleep 5
                attempt=$((attempt+1))
            fi
        done
        
        if [ $attempt -eq $max_attempts ]; then
            echo "  ✗ Node $node_host:$node_port failed to start within timeout"
            exit 1
        fi
    done
fi

# 额外等待时间，确保集群完全形成
echo "Waiting additional 30 seconds for cluster formation..."
sleep 30

# 验证集群状态
echo "Verifying cluster status..."
python3 ./test-scripts/verify-cluster.py

# 创建测试结果目录
mkdir -p "${TEST_RESULTS_PATH}"
mkdir -p "${TEST_RESULTS_PATH}/logs"
mkdir -p "${TEST_RESULTS_PATH}/reports"
mkdir -p "${TEST_RESULTS_PATH}/coverage"

# 设置测试环境变量
export DOTNET_ENVIRONMENT=Testing
export TEST_CLUSTER_ENDPOINTS="$CLUSTER_NODES"
export TEST_NATS_SERVERS="$NATS_SERVERS"
export TEST_MYSQL_CONNECTION="$MYSQL_CONNECTION"

# 运行测试套件
echo "=========================================="
echo "Starting Test Execution"
echo "=========================================="

# 1. 运行基础功能测试
echo "Phase 1: Running Core Functionality Tests..."
dotnet test ./tests/FlowCustomV1.Tests.dll \
    --filter "Category=Core" \
    --logger "trx;LogFileName=core-tests.trx" \
    --logger "json;LogFileName=core-tests.json" \
    --results-directory "${TEST_RESULTS_PATH}" \
    --collect "XPlat Code Coverage" \
    --verbosity normal

# 2. 运行多节点集成测试
echo "Phase 2: Running Multi-Node Integration Tests..."
dotnet test ./tests/FlowCustomV1.Tests.dll \
    --filter "Category=MultiNode" \
    --logger "trx;LogFileName=multinode-tests.trx" \
    --logger "json;LogFileName=multinode-tests.json" \
    --results-directory "${TEST_RESULTS_PATH}" \
    --collect "XPlat Code Coverage" \
    --verbosity normal

# 3. 运行分布式场景测试
echo "Phase 3: Running Distributed Scenario Tests..."
dotnet test ./tests/FlowCustomV1.Tests.dll \
    --filter "Category=Scenario" \
    --logger "trx;LogFileName=scenario-tests.trx" \
    --logger "json;LogFileName=scenario-tests.json" \
    --results-directory "${TEST_RESULTS_PATH}" \
    --collect "XPlat Code Coverage" \
    --verbosity normal

# 4. 运行性能测试（如果启用）
if [ "${RUN_PERFORMANCE_TESTS:-false}" = "true" ]; then
    echo "Phase 4: Running Performance Tests..."
    dotnet test ./tests/FlowCustomV1.Tests.dll \
        --filter "Category=Performance" \
        --logger "trx;LogFileName=performance-tests.trx" \
        --logger "json;LogFileName=performance-tests.json" \
        --results-directory "${TEST_RESULTS_PATH}" \
        --verbosity normal
fi

# 5. 运行弹性测试（如果启用）
if [ "${RUN_RESILIENCE_TESTS:-false}" = "true" ]; then
    echo "Phase 5: Running Resilience Tests..."
    dotnet test ./tests/FlowCustomV1.Tests.dll \
        --filter "Category=Resilience" \
        --logger "trx;LogFileName=resilience-tests.trx" \
        --logger "json;LogFileName=resilience-tests.json" \
        --results-directory "${TEST_RESULTS_PATH}" \
        --verbosity normal
fi

# 6. 运行自定义Python测试脚本
echo "Phase 6: Running Custom Integration Tests..."
python3 ./test-scripts/run-integration-tests.py \
    --cluster-endpoints "$CLUSTER_NODES" \
    --output-dir "${TEST_RESULTS_PATH}/custom"

# 生成综合测试报告
echo "Generating comprehensive test report..."
python3 ./test-scripts/generate-test-report.py \
    --results-dir "${TEST_RESULTS_PATH}" \
    --output-file "${TEST_RESULTS_PATH}/comprehensive-test-report.html"

# 收集集群日志
echo "Collecting cluster logs..."
python3 ./test-scripts/collect-cluster-logs.py \
    --cluster-endpoints "$CLUSTER_NODES" \
    --output-dir "${TEST_RESULTS_PATH}/logs"

# 生成性能分析报告
echo "Generating performance analysis..."
python3 ./test-scripts/analyze-performance.py \
    --results-dir "${TEST_RESULTS_PATH}" \
    --output-file "${TEST_RESULTS_PATH}/performance-analysis.json"

echo "=========================================="
echo "Test Execution Completed"
echo "=========================================="
echo "Results available at: ${TEST_RESULTS_PATH}"

# 显示测试结果摘要
if [ -f "${TEST_RESULTS_PATH}/comprehensive-test-report.html" ]; then
    echo "Comprehensive report: ${TEST_RESULTS_PATH}/comprehensive-test-report.html"
fi

# 保持容器运行以便查看结果
if [ "${KEEP_ALIVE:-false}" = "true" ]; then
    echo "Keeping container alive for result inspection..."
    tail -f /dev/null
fi
