using System.Collections.Concurrent;
using FlowCustomV1.Core.Interfaces;
using FlowCustomV1.Core.Models.Workflow;
using NodeValidationResult = FlowCustomV1.Core.Models.Workflow.NodeValidationResult;

namespace FlowCustomV1.Core.Services;

/// <summary>
/// 节点执行器实现
/// 提供完整的节点执行、验证和生命周期管理功能
/// </summary>
public class NodeExecutor : INodeExecutor
{
    private readonly ILoggingService _loggingService;
    private bool _isInitialized = false;
    private NodeConfiguration? _configuration;

    /// <summary>
    /// 初始化节点执行器
    /// </summary>
    /// <param name="loggingService">日志服务</param>
    public NodeExecutor(ILoggingService loggingService)
    {
        _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
    }

    /// <inheritdoc />
    public string NodeType => "Generic";

    /// <inheritdoc />
    public string DisplayName => "通用节点执行器";

    /// <inheritdoc />
    public string Description => "通用的节点执行器，支持基本的节点执行功能";

    /// <inheritdoc />
    public string Version => "1.0.0";

    /// <inheritdoc />
    public bool SupportsAsync => true;

    /// <inheritdoc />
    public bool IsStateful => false;

    /// <inheritdoc />
    public event EventHandler<NodeExecutionProgressEventArgs>? ExecutionProgress;

    /// <inheritdoc />
    public event EventHandler<NodeExecutionStatusChangedEventArgs>? StatusChanged;

    /// <inheritdoc />
    public Task InitializeAsync(NodeConfiguration configuration, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(configuration);

        _loggingService.LogInformation("Initializing NodeExecutor with configuration");

        _configuration = configuration;
        _isInitialized = true;

        _loggingService.LogInformation("NodeExecutor initialized successfully");
        return Task.CompletedTask;
    }

    /// <inheritdoc />
    public Task<NodeValidationResult> ValidateAsync(NodeConfiguration configuration, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(configuration);

        _loggingService.LogDebug("Validating node configuration");

        var result = new NodeValidationResult();

        // 基本验证 - NodeConfiguration本身不包含NodeId和NodeType
        // 这些验证应该在WorkflowEngine层面进行

        // 验证参数
        var parameterDefinitions = GetConfigurationParameters();
        foreach (var paramDef in parameterDefinitions)
        {
            if (configuration.Parameters.TryGetValue(paramDef.Name, out var value))
            {
                // 基本类型验证
                if (!ValidateParameterValue(paramDef, value))
                {
                    result.Errors.Add($"Invalid parameter value for: {paramDef.Name}");
                }
            }
            else if (paramDef.IsRequired)
            {
                result.Errors.Add($"Required parameter missing: {paramDef.Name}");
            }
        }

        // 验证资源限制
        if (configuration.ResourceLimits != null)
        {
            if (configuration.ResourceLimits.MaxMemoryMb < 0)
            {
                result.Warnings.Add("Memory limit should not be negative");
            }

            if (configuration.ResourceLimits.MaxCpuPercent < 0 || configuration.ResourceLimits.MaxCpuPercent > 100)
            {
                result.Warnings.Add("CPU limit should be between 0 and 100");
            }

            if (configuration.ResourceLimits.MaxExecutionSeconds < 0)
            {
                result.Warnings.Add("Execution timeout should not be negative");
            }
        }

        result.IsValid = result.Errors.Count == 0;

        _loggingService.LogDebug("Node validation completed. IsValid: {IsValid}, Errors: {ErrorCount}, Warnings: {WarningCount}",
            result.IsValid, result.Errors.Count, result.Warnings.Count);

        return Task.FromResult(result);
    }

    /// <inheritdoc />
    public async Task<NodeExecutionResult> ExecuteAsync(NodeExecutionContext context, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(context);

        _loggingService.LogInformation("Starting execution of node {NodeId} (type: {NodeType})",
            context.NodeId, context.NodeType);

        var result = new NodeExecutionResult
        {
            NodeId = context.NodeId,
            ExecutionId = context.ExecutionId,
            State = NodeExecutionState.Running,
            StartedAt = DateTime.UtcNow,
            InputData = new Dictionary<string, object>(context.InputData)
        };

        try
        {
            // 触发状态变更事件
            OnStatusChanged(new NodeExecutionStatusChangedEventArgs
            {
                NodeId = context.NodeId,
                ExecutionId = context.ExecutionId,
                OldState = NodeExecutionState.NotStarted,
                NewState = NodeExecutionState.Running,
                Reason = "Node execution started"
            });

            // 触发进度事件
            OnExecutionProgress(new NodeExecutionProgressEventArgs
            {
                NodeId = context.NodeId,
                ExecutionId = context.ExecutionId,
                ProgressPercentage = 0,
                Message = "Starting node execution"
            });

            // 检查取消令牌
            cancellationToken.ThrowIfCancellationRequested();

            // 模拟执行过程
            await SimulateNodeExecution(context, result, cancellationToken);

            // 设置成功结果
            result.State = NodeExecutionState.Completed;
            result.IsSuccess = true;
            result.CompletedAt = DateTime.UtcNow;

            // 触发完成进度事件
            OnExecutionProgress(new NodeExecutionProgressEventArgs
            {
                NodeId = context.NodeId,
                ExecutionId = context.ExecutionId,
                ProgressPercentage = 100,
                Message = "Node execution completed successfully"
            });

            // 触发状态变更事件
            OnStatusChanged(new NodeExecutionStatusChangedEventArgs
            {
                NodeId = context.NodeId,
                ExecutionId = context.ExecutionId,
                OldState = NodeExecutionState.Running,
                NewState = NodeExecutionState.Completed,
                Reason = "Node execution completed successfully"
            });

            _loggingService.LogInformation("Node {NodeId} executed successfully in {Duration}ms", 
                context.NodeId, result.Duration.TotalMilliseconds);

            return result;
        }
        catch (OperationCanceledException)
        {
            _loggingService.LogWarning("Node {NodeId} execution was cancelled", context.NodeId);
            
            result.State = NodeExecutionState.Cancelled;
            result.IsSuccess = false;
            result.ErrorMessage = "Execution was cancelled";
            result.CompletedAt = DateTime.UtcNow;

            OnStatusChanged(new NodeExecutionStatusChangedEventArgs
            {
                NodeId = context.NodeId,
                ExecutionId = context.ExecutionId,
                OldState = NodeExecutionState.Running,
                NewState = NodeExecutionState.Cancelled,
                Reason = "Execution was cancelled"
            });

            return result;
        }
        catch (Exception ex)
        {
            _loggingService.LogError(ex, "Error executing node {NodeId}", context.NodeId);
            
            result.State = NodeExecutionState.Failed;
            result.IsSuccess = false;
            result.ErrorMessage = ex.Message;
            result.Exception = ex;
            result.CompletedAt = DateTime.UtcNow;

            OnStatusChanged(new NodeExecutionStatusChangedEventArgs
            {
                NodeId = context.NodeId,
                ExecutionId = context.ExecutionId,
                OldState = NodeExecutionState.Running,
                NewState = NodeExecutionState.Failed,
                Reason = $"Execution failed: {ex.Message}"
            });

            return result;
        }
    }

    /// <inheritdoc />
    public Task CleanupAsync(CancellationToken cancellationToken = default)
    {
        _loggingService.LogInformation("Cleaning up NodeExecutor resources");
        
        _isInitialized = false;
        _configuration = null;
        
        _loggingService.LogInformation("NodeExecutor cleanup completed");
        return Task.CompletedTask;
    }

    /// <inheritdoc />
    public NodeDefinition GetNodeDefinition()
    {
        return new NodeDefinition
        {
            NodeType = "GenericNode",
            DisplayName = "Generic Node",
            Description = "A generic node that can execute various tasks",
            Version = "1.0.0",
            Category = NodeTypeCategory.Process,
            Icon = "generic-node",
            Color = "#007ACC",
            SupportsAsync = true,
            IsStateful = false,
            Tags = new HashSet<string> { "generic", "process" },
            Metadata = new Dictionary<string, object>
            {
                ["SupportsParallel"] = true,
                ["MaxParallelism"] = 10,
                ["EstimatedExecutionTimeSeconds"] = 30,
                ["Author"] = "FlowCustomV1 Team",
                ["License"] = "MIT"
            }
        };
    }

    /// <inheritdoc />
    public IEnumerable<NodeParameterDefinition> GetInputParameters()
    {
        return new List<NodeParameterDefinition>
        {
            new NodeParameterDefinition
            {
                Name = "input",
                DisplayName = "输入数据",
                Description = "节点的输入数据",
                DataType = ParameterDataType.Object,
                IsRequired = false,
                Group = "输入"
            }
        };
    }

    /// <inheritdoc />
    public IEnumerable<NodeParameterDefinition> GetOutputParameters()
    {
        return new List<NodeParameterDefinition>
        {
            new NodeParameterDefinition
            {
                Name = "output",
                DisplayName = "输出数据",
                Description = "节点的输出数据",
                DataType = ParameterDataType.Object,
                IsRequired = false,
                Group = "输出"
            },
            new NodeParameterDefinition
            {
                Name = "executionTime",
                DisplayName = "执行时间",
                Description = "节点执行耗时（毫秒）",
                DataType = ParameterDataType.Double,
                IsRequired = false,
                Group = "统计"
            }
        };
    }

    /// <inheritdoc />
    public IEnumerable<NodeParameterDefinition> GetConfigurationParameters()
    {
        return new List<NodeParameterDefinition>
        {
            new NodeParameterDefinition
            {
                Name = "simulationDelay",
                DisplayName = "模拟延迟",
                Description = "模拟执行延迟时间（毫秒）",
                DataType = ParameterDataType.Integer,
                IsRequired = false,
                DefaultValue = 1000,
                Group = "执行设置",
                Metadata = new Dictionary<string, object>
                {
                    ["MinValue"] = 0,
                    ["MaxValue"] = 60000
                }
            },
            new NodeParameterDefinition
            {
                Name = "enableLogging",
                DisplayName = "启用日志",
                Description = "是否启用详细日志记录",
                DataType = ParameterDataType.Boolean,
                IsRequired = false,
                DefaultValue = true,
                Group = "调试设置"
            }
        };
    }

    /// <inheritdoc />
    public bool CanHandle(Dictionary<string, object> inputData)
    {
        ArgumentNullException.ThrowIfNull(inputData);

        // 通用节点执行器可以处理任何输入数据
        _loggingService.LogDebug("Checking if NodeExecutor can handle input data with {Count} parameters",
            inputData.Count);

        return true;
    }

    /// <inheritdoc />
    public TimeSpan EstimateExecutionTime(Dictionary<string, object> inputData, NodeConfiguration configuration)
    {
        ArgumentNullException.ThrowIfNull(inputData);
        ArgumentNullException.ThrowIfNull(configuration);

        // 基于配置参数估算执行时间
        var baseTime = 1000; // 默认1秒

        if (configuration.Parameters.TryGetValue("simulationDelay", out var delayValue))
        {
            if (int.TryParse(delayValue?.ToString(), out var delay))
            {
                baseTime = delay;
            }
        }

        // 根据输入数据大小调整时间
        var dataComplexity = CalculateDataComplexity(inputData);
        var adjustedTime = baseTime + (dataComplexity * 100); // 每个复杂度单位增加100ms

        _loggingService.LogDebug("Estimated execution time: {Time}ms", adjustedTime);

        return TimeSpan.FromMilliseconds(adjustedTime);
    }

    /// <inheritdoc />
    public NodeResourceRequirements GetResourceRequirements(Dictionary<string, object> inputData, NodeConfiguration configuration)
    {
        ArgumentNullException.ThrowIfNull(inputData);
        ArgumentNullException.ThrowIfNull(configuration);

        var baseRequirements = new NodeResourceRequirements
        {
            RequiredCpuCores = 1.0,
            RequiredMemoryMb = 512,
            RequiredDiskMb = 100,
            RequiredNetworkMbps = 10
        };

        // 根据输入数据大小调整资源需求
        var dataComplexity = CalculateDataComplexity(inputData);

        if (dataComplexity > 10)
        {
            baseRequirements.RequiredCpuCores = 2.0;
            baseRequirements.RequiredMemoryMb = 1024;
            baseRequirements.RequiredDiskMb = 200;
        }

        if (dataComplexity > 50)
        {
            baseRequirements.RequiredCpuCores = 4.0;
            baseRequirements.RequiredMemoryMb = 2048;
            baseRequirements.RequiredDiskMb = 500;
        }

        // 应用配置中的资源限制
        if (configuration.ResourceLimits != null)
        {
            if (configuration.ResourceLimits.MaxMemoryMb > 0)
                baseRequirements.RequiredMemoryMb = Math.Min(baseRequirements.RequiredMemoryMb, configuration.ResourceLimits.MaxMemoryMb);
        }

        _loggingService.LogDebug("Resource requirements calculated: CPU={CpuCores}, Memory={MemoryMB}MB",
            baseRequirements.RequiredCpuCores, baseRequirements.RequiredMemoryMb);

        return baseRequirements;
    }

    /// <summary>
    /// 模拟节点执行过程
    /// </summary>
    /// <param name="context">执行上下文</param>
    /// <param name="result">执行结果</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>执行任务</returns>
    private async Task SimulateNodeExecution(NodeExecutionContext context, NodeExecutionResult result, CancellationToken cancellationToken)
    {
        // 获取模拟延迟时间
        var delay = 1000; // 默认1秒
        if (context.Configuration.Parameters.TryGetValue("simulationDelay", out var delayValue))
        {
            if (int.TryParse(delayValue?.ToString(), out var configDelay))
            {
                delay = configDelay;
            }
        }

        // 分步骤执行，支持进度报告
        var steps = 5;
        var stepDelay = delay / steps;

        for (int i = 0; i < steps; i++)
        {
            cancellationToken.ThrowIfCancellationRequested();

            // 模拟处理步骤
            await Task.Delay(stepDelay, cancellationToken);

            // 报告进度
            var progress = (int)((double)(i + 1) / steps * 100);
            OnExecutionProgress(new NodeExecutionProgressEventArgs
            {
                NodeId = context.NodeId,
                ExecutionId = context.ExecutionId,
                ProgressPercentage = progress,
                Message = $"Processing step {i + 1} of {steps}",
                CurrentStep = $"Step {i + 1}",
                TotalSteps = steps,
                CurrentStepIndex = i
            });

            // 记录执行日志
            var logMessage = $"Completed step {i + 1}/{steps}";
            result.ExecutionLogs.Add(logMessage);

            // 检查是否启用详细日志
            if (context.Configuration.Parameters.TryGetValue("enableLogging", out var loggingValue))
            {
                if (bool.TryParse(loggingValue?.ToString(), out var enableLogging) && enableLogging)
                {
                    _loggingService.LogDebug("Node {NodeId}: {LogMessage}", context.NodeId, logMessage);
                }
            }
        }

        // 生成输出数据
        result.OutputData["output"] = GenerateOutputData(context.InputData);
        result.OutputData["executionTime"] = result.Duration.TotalMilliseconds;
        result.OutputData["processedAt"] = DateTime.UtcNow;
        result.OutputData["nodeType"] = context.NodeType;

        // 添加性能指标
        result.PerformanceMetrics["executionTimeMs"] = result.Duration.TotalMilliseconds;
        result.PerformanceMetrics["memoryUsageMB"] = CalculateMemoryUsage(context.InputData);
        result.PerformanceMetrics["cpuUsagePercent"] = CalculateCpuUsage();

        // 添加元数据
        result.Metadata["executorVersion"] = Version;
        result.Metadata["executorType"] = NodeType;
        result.Metadata["inputDataSize"] = context.InputData.Count;
        result.Metadata["outputDataSize"] = result.OutputData.Count;
    }

    /// <summary>
    /// 生成输出数据
    /// </summary>
    /// <param name="inputData">输入数据</param>
    /// <returns>输出数据</returns>
    private static object GenerateOutputData(ConcurrentDictionary<string, object> inputData)
    {
        // 简单的数据处理：将输入数据包装并添加处理信息
        return new
        {
            processedInput = inputData,
            processedAt = DateTime.UtcNow,
            processingResult = "success",
            dataCount = inputData.Count
        };
    }

    /// <summary>
    /// 计算数据复杂度
    /// </summary>
    /// <param name="data">数据</param>
    /// <returns>复杂度值</returns>
    private static int CalculateDataComplexity(Dictionary<string, object> data)
    {
        var complexity = data.Count;

        foreach (var value in data.Values)
        {
            if (value is string str)
            {
                complexity += str.Length / 100; // 每100个字符增加1个复杂度
            }
            else if (value is System.Collections.ICollection collection)
            {
                complexity += collection.Count;
            }
            else if (value != null)
            {
                complexity += 1;
            }
        }

        return complexity;
    }

    /// <summary>
    /// 计算内存使用量（模拟）
    /// </summary>
    /// <param name="inputData">输入数据</param>
    /// <returns>内存使用量（MB）</returns>
    private static double CalculateMemoryUsage(ConcurrentDictionary<string, object> inputData)
    {
        // 简单的内存使用量估算
        var baseMemory = 50.0; // 基础50MB
        var dataMemory = inputData.Count * 0.1; // 每个参数0.1MB

        return baseMemory + dataMemory;
    }

    /// <summary>
    /// 计算CPU使用率（模拟）
    /// </summary>
    /// <returns>CPU使用率百分比</returns>
    private static double CalculateCpuUsage()
    {
        // 模拟CPU使用率
        var random = new Random();
        return random.NextDouble() * 30 + 10; // 10-40%之间的随机值
    }

    /// <summary>
    /// 触发执行进度事件
    /// </summary>
    /// <param name="args">事件参数</param>
    protected virtual void OnExecutionProgress(NodeExecutionProgressEventArgs args)
    {
        ExecutionProgress?.Invoke(this, args);
    }

    /// <summary>
    /// 触发状态变更事件
    /// </summary>
    /// <param name="args">事件参数</param>
    protected virtual void OnStatusChanged(NodeExecutionStatusChangedEventArgs args)
    {
        StatusChanged?.Invoke(this, args);
    }

    /// <summary>
    /// 验证参数值
    /// </summary>
    /// <param name="paramDef">参数定义</param>
    /// <param name="value">参数值</param>
    /// <returns>是否有效</returns>
    private bool ValidateParameterValue(NodeParameterDefinition paramDef, object value)
    {
        try
        {
            // 基本类型验证
            switch (paramDef.DataType)
            {
                case ParameterDataType.Integer:
                    if (value is not int intValue) return false;
                    if (paramDef.Metadata.TryGetValue("MinValue", out var minVal) && intValue < Convert.ToInt32(minVal)) return false;
                    if (paramDef.Metadata.TryGetValue("MaxValue", out var maxVal) && intValue > Convert.ToInt32(maxVal)) return false;
                    break;
                case ParameterDataType.String:
                    if (value is not string) return false;
                    break;
                case ParameterDataType.Boolean:
                    if (value is not bool) return false;
                    break;
                default:
                    return true; // 其他类型暂时不验证
            }
            return true;
        }
        catch
        {
            return false;
        }
    }
}
