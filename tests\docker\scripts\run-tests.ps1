# FlowCustomV1 Docker Environment Test Script
# Version: v0.0.1.8
# Date: 2025-09-07

param(
    [switch]$SkipBuild,
    [switch]$Verbose
)

# Test results
$TestResults = @()
$StartTime = Get-Date

function Write-TestLog {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $color = switch ($Level) {
        "ERROR" { "Red" }
        "WARN" { "Yellow" }
        "SUCCESS" { "Green" }
        default { "White" }
    }
    Write-Host "[$timestamp] [$Level] $Message" -ForegroundColor $color
}

function Add-TestResult {
    param(
        [string]$TestName,
        [bool]$Passed,
        [string]$Details = ""
    )
    
    $script:TestResults += [PSCustomObject]@{
        TestName = $TestName
        Passed = $Passed
        Details = $Details
        Timestamp = Get-Date
    }
    
    $status = if ($Passed) { "PASS" } else { "FAIL" }
    Write-TestLog "$status - $TestName $(if($Details) { "($Details)" })" $(if($Passed) { "SUCCESS" } else { "ERROR" })
}

function Test-ServiceHealth {
    param([string]$Url, [int]$TimeoutSec = 10)
    
    try {
        $response = Invoke-WebRequest -Uri $Url -TimeoutSec $TimeoutSec -UseBasicParsing
        return $response.StatusCode -eq 200
    }
    catch {
        return $false
    }
}

function Test-ContainerStatus {
    param([string]$ContainerName)
    
    try {
        $status = docker inspect --format='{{.State.Status}}' $ContainerName 2>$null
        return $status -eq "running"
    }
    catch {
        return $false
    }
}

Write-TestLog "Starting FlowCustomV1 Docker Environment Tests" "INFO"

try {
    # Set working directory
    $testDir = Split-Path -Parent $PSScriptRoot
    Set-Location $testDir
    Write-TestLog "Working directory: $testDir" "INFO"

    # Test 1: Environment cleanup
    Write-TestLog "Test 1: Environment cleanup" "INFO"
    try {
        docker-compose -f docker-compose.simple.yml down -v 2>$null
        Add-TestResult "Environment Cleanup" $true "Old environment cleaned"
    }
    catch {
        Add-TestResult "Environment Cleanup" $false $_.Exception.Message
    }

    # Test 2: Image build
    if (-not $SkipBuild) {
        Write-TestLog "Test 2: Image build" "INFO"
        try {
            $buildOutput = docker-compose -f docker-compose.simple.yml build 2>&1
            if ($LASTEXITCODE -eq 0) {
                Add-TestResult "Image Build" $true "Build successful"
            } else {
                Add-TestResult "Image Build" $false "Build failed"
                throw "Image build failed"
            }
        }
        catch {
            Add-TestResult "Image Build" $false $_.Exception.Message
            throw
        }
    }

    # Test 3: Infrastructure startup
    Write-TestLog "Test 3: Infrastructure startup" "INFO"
    try {
        docker-compose -f docker-compose.simple.yml up -d nats mysql
        Start-Sleep -Seconds 30
        
        $natsHealthy = Test-ContainerStatus "flowcustom-test-nats"
        $mysqlHealthy = Test-ContainerStatus "flowcustom-test-mysql"
        
        if ($natsHealthy -and $mysqlHealthy) {
            Add-TestResult "Infrastructure Startup" $true "NATS and MySQL started successfully"
        } else {
            Add-TestResult "Infrastructure Startup" $false "NATS:$natsHealthy, MySQL:$mysqlHealthy"
        }
    }
    catch {
        Add-TestResult "Infrastructure Startup" $false $_.Exception.Message
    }

    # Test 4: NATS service verification
    Write-TestLog "Test 4: NATS service verification" "INFO"
    $natsHealthy = Test-ServiceHealth "http://localhost:8222/healthz"
    Add-TestResult "NATS Service" $natsHealthy $(if($natsHealthy) { "Health check passed" } else { "Health check failed" })

    # Test 5: MySQL service verification
    Write-TestLog "Test 5: MySQL service verification" "INFO"
    try {
        $mysqlTest = docker exec flowcustom-test-mysql mysql -uflowcustom -pTestPassword123! -e "SELECT 1;" 2>$null
        $mysqlHealthy = $LASTEXITCODE -eq 0
        Add-TestResult "MySQL Service" $mysqlHealthy $(if($mysqlHealthy) { "Database connection successful" } else { "Database connection failed" })
    }
    catch {
        Add-TestResult "MySQL Service" $false $_.Exception.Message
    }

    # Test 6: Application nodes startup
    Write-TestLog "Test 6: Application nodes startup" "INFO"
    try {
        docker-compose -f docker-compose.simple.yml up -d master-node worker-node
        Start-Sleep -Seconds 45
        
        $masterHealthy = Test-ContainerStatus "flowcustom-test-master"
        $workerHealthy = Test-ContainerStatus "flowcustom-test-worker"
        
        if ($masterHealthy -and $workerHealthy) {
            Add-TestResult "Application Nodes" $true "Master and Worker nodes started successfully"
        } else {
            Add-TestResult "Application Nodes" $false "Master:$masterHealthy, Worker:$workerHealthy"
        }
    }
    catch {
        Add-TestResult "Application Nodes" $false $_.Exception.Message
    }

    # Test 7: Configuration verification
    Write-TestLog "Test 7: Configuration verification" "INFO"
    try {
        $masterEnv = docker exec flowcustom-test-master env | Select-String "Nats__Servers__0"
        $workerEnv = docker exec flowcustom-test-worker env | Select-String "Nats__Servers__0"
        
        $configValid = ($masterEnv -match "nats://nats:4222") -and ($workerEnv -match "nats://nats:4222")
        Add-TestResult "Configuration" $configValid $(if($configValid) { "Environment variables correct" } else { "Environment variables incorrect" })
    }
    catch {
        Add-TestResult "Configuration" $false $_.Exception.Message
    }

    # Test 8: Application startup verification
    Write-TestLog "Test 8: Application startup verification" "INFO"
    try {
        $masterLogs = docker logs flowcustom-test-master 2>&1 | Select-String "Application started"
        $workerLogs = docker logs flowcustom-test-worker 2>&1 | Select-String "Application started"
        
        $appStarted = ($masterLogs.Count -gt 0) -and ($workerLogs.Count -gt 0)
        Add-TestResult "Application Startup" $appStarted $(if($appStarted) { "Applications started successfully" } else { "Application startup failed" })
    }
    catch {
        Add-TestResult "Application Startup" $false $_.Exception.Message
    }

    # Test 9: Node communication verification
    Write-TestLog "Test 9: Node communication verification" "INFO"
    try {
        Start-Sleep -Seconds 15
        
        $masterHeartbeat = docker logs flowcustom-test-master 2>&1 | Select-String "heartbeat" | Select-Object -Last 1
        $workerHeartbeat = docker logs flowcustom-test-worker 2>&1 | Select-String "heartbeat" | Select-Object -Last 1
        
        $communicationWorking = ($masterHeartbeat -ne $null) -and ($workerHeartbeat -ne $null)
        Add-TestResult "Node Communication" $communicationWorking $(if($communicationWorking) { "Node heartbeat normal" } else { "Node heartbeat abnormal" })
    }
    catch {
        Add-TestResult "Node Communication" $false $_.Exception.Message
    }

    # Test 10: Container resource usage
    Write-TestLog "Test 10: Container resource usage" "INFO"
    try {
        $stats = docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}" 2>$null
        $resourcesOk = $stats -ne $null
        Add-TestResult "Resource Usage" $resourcesOk $(if($resourcesOk) { "Resource usage normal" } else { "Cannot get resource info" })
    }
    catch {
        Add-TestResult "Resource Usage" $false $_.Exception.Message
    }

}
catch {
    Write-TestLog "Critical error during test execution: $($_.Exception.Message)" "ERROR"
}
finally {
    # Generate test report
    $EndTime = Get-Date
    $TotalDuration = ($EndTime - $StartTime).TotalSeconds
    
    Write-TestLog "Tests completed, generating report..." "INFO"
    
    # Statistics
    $TotalTests = $TestResults.Count
    $PassedTests = ($TestResults | Where-Object { $_.Passed }).Count
    $FailedTests = $TotalTests - $PassedTests
    $SuccessRate = if ($TotalTests -gt 0) { [math]::Round(($PassedTests / $TotalTests) * 100, 2) } else { 0 }
    
    # Output test report
    Write-Host "`n" + "="*80 -ForegroundColor Cyan
    Write-Host "FlowCustomV1 Docker Environment Test Report" -ForegroundColor Cyan
    Write-Host "="*80 -ForegroundColor Cyan
    Write-Host "Test Time: $StartTime - $EndTime" -ForegroundColor White
    Write-Host "Total Duration: $([math]::Round($TotalDuration, 2)) seconds" -ForegroundColor White
    Write-Host "Total Tests: $TotalTests" -ForegroundColor White
    Write-Host "Passed Tests: $PassedTests" -ForegroundColor Green
    Write-Host "Failed Tests: $FailedTests" -ForegroundColor Red
    Write-Host "Success Rate: $SuccessRate%" -ForegroundColor $(if($SuccessRate -ge 80) { "Green" } else { "Red" })
    Write-Host ""
    
    # Detailed results
    Write-Host "Detailed Test Results:" -ForegroundColor Yellow
    Write-Host "-"*80 -ForegroundColor Yellow
    
    foreach ($result in $TestResults) {
        $status = if ($result.Passed) { "PASS" } else { "FAIL" }
        Write-Host "$status $($result.TestName)" -ForegroundColor $(if($result.Passed) { "Green" } else { "Red" })
        if ($result.Details) {
            Write-Host "    Details: $($result.Details)" -ForegroundColor Gray
        }
    }
    
    # Failed tests summary
    $failedTests = $TestResults | Where-Object { -not $_.Passed }
    if ($failedTests.Count -gt 0) {
        Write-Host "`nFailed Tests Summary:" -ForegroundColor Red
        Write-Host "-"*80 -ForegroundColor Red
        foreach ($failed in $failedTests) {
            Write-Host "FAIL $($failed.TestName): $($failed.Details)" -ForegroundColor Red
        }
    }
    
    Write-Host "`n" + "="*80 -ForegroundColor Cyan
    
    # Return exit code
    if ($FailedTests -eq 0) {
        Write-TestLog "All tests passed!" "SUCCESS"
        exit 0
    } else {
        Write-TestLog "$FailedTests tests failed" "ERROR"
        exit 1
    }
}
