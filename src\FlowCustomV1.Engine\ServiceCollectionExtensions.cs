using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Extensions.Logging;
using FlowCustomV1.Core.Interfaces;
using FlowCustomV1.Core.Interfaces.Repositories;
using FlowCustomV1.Engine.Services;
using FlowCustomV1.Engine.Schedulers;
using FlowCustomV1.Engine.StateTracking;
using FlowCustomV1.Engine.ErrorHandling;
using FlowCustomV1.Engine.Executors;

using FlowCustomV1.Core.Models.Workflow;
using System;

namespace FlowCustomV1.Engine;

/// <summary>
/// 服务集合扩展方法
/// 用于注册Engine层的所有服务
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// 添加工作流引擎服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configureOptions">配置选项</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddWorkflowEngine(
        this IServiceCollection services,
        Action<WorkflowEngineOptions>? configureOptions = null)
    {
        // 配置选项
        var options = new WorkflowEngineOptions();
        configureOptions?.Invoke(options);
        services.TryAddSingleton(options);

        // 注册核心服务 - 工作流调度器实现
        services.TryAddScoped<IWorkflowScheduler, WorkflowScheduler>();

        // 保持向后兼容性 - 将WorkflowEngineService标记为过时
        services.TryAddScoped<IWorkflowEngine, WorkflowEngineService>();

        // 注册调度器
        services.AddNodeScheduler(options.SchedulerOptions);

        // 注册状态跟踪器
        services.AddStateTracker(options.StateTrackerOptions);

        // 注册错误处理器
        services.AddErrorHandler(options.ErrorHandlerOptions);

        // 注册节点执行器
        services.AddNodeExecutors();

        // 注册仓储服务
        services.AddRepositories();

        return services;
    }

    /// <summary>
    /// 添加工作流引擎服务（带数据持久化）
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">配置</param>
    /// <param name="configureOptions">配置选项</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddWorkflowEngineWithPersistence(
        this IServiceCollection services,
        IConfiguration configuration,
        Action<WorkflowEngineOptions>? configureOptions = null)
    {
        // 添加基础工作流引擎服务
        services.AddWorkflowEngine(configureOptions);

        // 注意：不再直接添加Infrastructure服务，由调用方负责
        // 这样保持了架构层次的清晰性

        return services;
    }

    /// <summary>
    /// 添加工作流引擎服务（带自定义数据库配置）
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configureOptions">配置选项</param>
    /// <returns>服务集合</returns>
    /// <remarks>
    /// 注意：此方法不再直接添加Infrastructure服务
    /// 需要在调用方手动添加Infrastructure服务以保持架构清晰性
    /// </remarks>
    public static IServiceCollection AddWorkflowEngineWithPersistence(
        this IServiceCollection services,
        Action<WorkflowEngineOptions>? configureOptions = null)
    {
        // 添加基础工作流引擎服务
        services.AddWorkflowEngine(configureOptions);

        // 注意：不再直接添加Infrastructure服务，由调用方负责
        // 这样保持了架构层次的清晰性

        return services;
    }

    /// <summary>
    /// 添加工作流引擎服务（内存模式，用于测试）
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configureOptions">配置选项</param>
    /// <returns>服务集合</returns>
    /// <remarks>
    /// 注意：此方法不再直接添加内存数据库服务
    /// 需要在调用方手动添加Infrastructure服务以保持架构清晰性
    /// </remarks>
    public static IServiceCollection AddWorkflowEngineInMemory(
        this IServiceCollection services,
        Action<WorkflowEngineOptions>? configureOptions = null)
    {
        // 添加基础工作流引擎服务
        services.AddWorkflowEngine(configureOptions);

        // 注意：不再直接添加内存数据库，由调用方负责
        // 这样保持了架构层次的清晰性

        return services;
    }

    /// <summary>
    /// 添加节点调度器服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="schedulerOptions">调度器选项</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddNodeScheduler(
        this IServiceCollection services,
        SchedulerOptions? schedulerOptions = null)
    {
        schedulerOptions ??= new SchedulerOptions();
        services.TryAddSingleton(schedulerOptions);
        services.TryAddSingleton<INodeScheduler, ChannelBasedNodeScheduler>();
        
        return services;
    }

    /// <summary>
    /// 添加状态跟踪器服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="stateTrackerOptions">状态跟踪器选项</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddStateTracker(
        this IServiceCollection services,
        StateTrackerOptions? stateTrackerOptions = null)
    {
        stateTrackerOptions ??= new StateTrackerOptions();
        services.TryAddSingleton(stateTrackerOptions);
        services.TryAddSingleton<IExecutionStateTracker, InMemoryExecutionStateTracker>();
        
        return services;
    }

    /// <summary>
    /// 添加错误处理器服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="errorHandlerOptions">错误处理器选项</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddErrorHandler(
        this IServiceCollection services,
        ErrorHandlerOptions? errorHandlerOptions = null)
    {
        errorHandlerOptions ??= new ErrorHandlerOptions();
        services.TryAddSingleton(errorHandlerOptions);
        services.TryAddSingleton<IErrorHandler, DefaultErrorHandler>();
        
        return services;
    }

    /// <summary>
    /// 添加节点执行器服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddNodeExecutors(this IServiceCollection services)
    {
        // 注册基础节点执行器 - 现在通过插件系统动态加载
        // 硬编码的执行器已被移除，改为使用插件系统

        return services;
    }

    /// <summary>
    /// 添加仓储服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    /// <remarks>
    /// 注意：此方法已移除具体实现的注册，由Infrastructure层负责
    /// 这样保持了架构层次的清晰性
    /// </remarks>
    public static IServiceCollection AddRepositories(this IServiceCollection services)
    {
        // 注意：不再直接注册具体实现，由Infrastructure层负责
        // Engine层只依赖接口，不依赖具体实现

        return services;
    }

    /// <summary>
    /// 添加自定义节点执行器
    /// </summary>
    /// <typeparam name="TExecutor">执行器类型</typeparam>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddNodeExecutor<TExecutor>(this IServiceCollection services)
        where TExecutor : class, INodeExecutor
    {
        services.TryAddTransient<INodeExecutor, TExecutor>();
        return services;
    }

    /// <summary>
    /// 添加默认的仓储实现
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    /// <remarks>
    /// 注意：此方法已移除具体实现的注册，由Infrastructure层负责
    /// 这样保持了架构层次的清晰性
    /// </remarks>
    public static IServiceCollection AddDefaultRepositories(this IServiceCollection services)
    {
        // 注意：不再直接注册具体实现，由Infrastructure层负责
        // Engine层只依赖接口，不依赖具体实现

        return services;
    }
}

/// <summary>
/// 工作流引擎配置选项
/// </summary>
public class WorkflowEngineOptions
{
    /// <summary>
    /// 调度器选项
    /// </summary>
    public SchedulerOptions SchedulerOptions { get; set; } = new();

    /// <summary>
    /// 状态跟踪器选项
    /// </summary>
    public StateTrackerOptions StateTrackerOptions { get; set; } = new();

    /// <summary>
    /// 错误处理器选项
    /// </summary>
    public ErrorHandlerOptions ErrorHandlerOptions { get; set; } = new();

    /// <summary>
    /// 是否启用详细日志
    /// </summary>
    public bool EnableDetailedLogging { get; set; } = false;

    /// <summary>
    /// 工作流执行超时时间（分钟）
    /// </summary>
    public int WorkflowExecutionTimeoutMinutes { get; set; } = 60;

    /// <summary>
    /// 是否启用性能监控
    /// </summary>
    public bool EnablePerformanceMonitoring { get; set; } = true;

    /// <summary>
    /// 最大并发工作流数
    /// </summary>
    public int MaxConcurrentWorkflows { get; set; } = 100;
}

/// <summary>
/// 状态跟踪器配置选项
/// </summary>
public class StateTrackerOptions
{
    /// <summary>
    /// 是否启用状态历史记录
    /// </summary>
    public bool EnableStateHistory { get; set; } = true;

    /// <summary>
    /// 状态历史保留天数
    /// </summary>
    public int StateHistoryRetentionDays { get; set; } = 30;

    /// <summary>
    /// 是否启用状态变更事件
    /// </summary>
    public bool EnableStateChangeEvents { get; set; } = true;

    /// <summary>
    /// 状态清理间隔（小时）
    /// </summary>
    public int StateCleanupIntervalHours { get; set; } = 24;

    /// <summary>
    /// 最大内存中状态数量
    /// </summary>
    public int MaxInMemoryStates { get; set; } = 10000;
}

/// <summary>
/// 错误处理器配置选项
/// </summary>
public class ErrorHandlerOptions
{
    /// <summary>
    /// 默认重试次数
    /// </summary>
    public int DefaultMaxRetryCount { get; set; } = 3;

    /// <summary>
    /// 默认重试延迟（毫秒）
    /// </summary>
    public int DefaultRetryDelayMs { get; set; } = 1000;

    /// <summary>
    /// 是否启用错误恢复
    /// </summary>
    public bool EnableErrorRecovery { get; set; } = true;

    /// <summary>
    /// 错误日志保留天数
    /// </summary>
    public int ErrorLogRetentionDays { get; set; } = 7;

    /// <summary>
    /// 是否启用错误统计
    /// </summary>
    public bool EnableErrorStatistics { get; set; } = true;

    /// <summary>
    /// 最大错误历史记录数
    /// </summary>
    public int MaxErrorHistoryCount { get; set; } = 1000;
}

/// <summary>
/// 工作流引擎构建器
/// </summary>
public class WorkflowEngineBuilder
{
    private readonly IServiceCollection _services;
    private readonly WorkflowEngineOptions _options;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="options">引擎选项</param>
    public WorkflowEngineBuilder(IServiceCollection services, WorkflowEngineOptions options)
    {
        _services = services ?? throw new ArgumentNullException(nameof(services));
        _options = options ?? throw new ArgumentNullException(nameof(options));
    }

    /// <summary>
    /// 配置调度器
    /// </summary>
    /// <param name="configure">配置委托</param>
    /// <returns>构建器</returns>
    public WorkflowEngineBuilder ConfigureScheduler(Action<SchedulerOptions> configure)
    {
        configure?.Invoke(_options.SchedulerOptions);
        return this;
    }

    /// <summary>
    /// 配置状态跟踪器
    /// </summary>
    /// <param name="configure">配置委托</param>
    /// <returns>构建器</returns>
    public WorkflowEngineBuilder ConfigureStateTracker(Action<StateTrackerOptions> configure)
    {
        configure?.Invoke(_options.StateTrackerOptions);
        return this;
    }

    /// <summary>
    /// 配置错误处理器
    /// </summary>
    /// <param name="configure">配置委托</param>
    /// <returns>构建器</returns>
    public WorkflowEngineBuilder ConfigureErrorHandler(Action<ErrorHandlerOptions> configure)
    {
        configure?.Invoke(_options.ErrorHandlerOptions);
        return this;
    }

    /// <summary>
    /// 添加自定义节点执行器
    /// </summary>
    /// <typeparam name="TExecutor">执行器类型</typeparam>
    /// <returns>构建器</returns>
    public WorkflowEngineBuilder AddNodeExecutor<TExecutor>()
        where TExecutor : class, INodeExecutor
    {
        _services.AddNodeExecutor<TExecutor>();
        return this;
    }

    /// <summary>
    /// 替换调度器实现
    /// </summary>
    /// <typeparam name="TScheduler">调度器类型</typeparam>
    /// <returns>构建器</returns>
    public WorkflowEngineBuilder UseScheduler<TScheduler>()
        where TScheduler : class, INodeScheduler
    {
        _services.Replace(ServiceDescriptor.Singleton<INodeScheduler, TScheduler>());
        return this;
    }

    /// <summary>
    /// 替换状态跟踪器实现
    /// </summary>
    /// <typeparam name="TStateTracker">状态跟踪器类型</typeparam>
    /// <returns>构建器</returns>
    public WorkflowEngineBuilder UseStateTracker<TStateTracker>()
        where TStateTracker : class, IExecutionStateTracker
    {
        _services.Replace(ServiceDescriptor.Singleton<IExecutionStateTracker, TStateTracker>());
        return this;
    }

    /// <summary>
    /// 替换错误处理器实现
    /// </summary>
    /// <typeparam name="TErrorHandler">错误处理器类型</typeparam>
    /// <returns>构建器</returns>
    public WorkflowEngineBuilder UseErrorHandler<TErrorHandler>()
        where TErrorHandler : class, IErrorHandler
    {
        _services.Replace(ServiceDescriptor.Singleton<IErrorHandler, TErrorHandler>());
        return this;
    }

    /// <summary>
    /// 构建服务集合
    /// </summary>
    /// <returns>服务集合</returns>
    public IServiceCollection Build()
    {
        return _services;
    }
}

/// <summary>
/// 工作流引擎构建器扩展方法
/// </summary>
public static class WorkflowEngineBuilderExtensions
{
    /// <summary>
    /// 添加工作流引擎并返回构建器
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configureOptions">配置选项</param>
    /// <returns>工作流引擎构建器</returns>
    public static WorkflowEngineBuilder AddWorkflowEngineBuilder(
        this IServiceCollection services,
        Action<WorkflowEngineOptions>? configureOptions = null)
    {
        var options = new WorkflowEngineOptions();
        configureOptions?.Invoke(options);

        services.AddWorkflowEngine(opt => 
        {
            opt.SchedulerOptions = options.SchedulerOptions;
            opt.StateTrackerOptions = options.StateTrackerOptions;
            opt.ErrorHandlerOptions = options.ErrorHandlerOptions;
            opt.EnableDetailedLogging = options.EnableDetailedLogging;
            opt.WorkflowExecutionTimeoutMinutes = options.WorkflowExecutionTimeoutMinutes;
            opt.EnablePerformanceMonitoring = options.EnablePerformanceMonitoring;
            opt.MaxConcurrentWorkflows = options.MaxConcurrentWorkflows;
        });

        return new WorkflowEngineBuilder(services, options);
    }
}
