#!/bin/bash

echo "=========================================="
echo "FlowCustomV1 Configuration Debug"
echo "=========================================="

echo "Environment Variables:"
echo "ASPNETCORE_ENVIRONMENT: $ASPNETCORE_ENVIRONMENT"
echo "NODE_ROLE: $NODE_ROLE"
echo "NODE_ID: $NODE_ID"
echo ""

echo "NATS Configuration Environment Variables:"
echo "Nats__Servers__0: $Nats__Servers__0"
echo "Nats__ConnectionName: $Nats__ConnectionName"
echo ""

echo "Database Configuration Environment Variables:"
echo "Database__ConnectionString: $Database__ConnectionString"
echo ""

echo "Available Configuration Files:"
ls -la /app/*.json
echo ""

echo "Content of appsettings.Test.json:"
if [ -f "/app/appsettings.Test.json" ]; then
    cat /app/appsettings.Test.json | head -30
else
    echo "appsettings.Test.json not found!"
fi
echo ""

echo "Content of appsettings.json (if exists):"
if [ -f "/app/appsettings.json" ]; then
    cat /app/appsettings.json | head -30
else
    echo "appsettings.json not found!"
fi
echo ""

echo "Network connectivity test:"
echo "Testing NATS connection to nats:4222..."
if command -v nc >/dev/null 2>&1; then
    nc -zv nats 4222 || echo "Cannot connect to nats:4222"
else
    echo "netcat not available"
fi

echo "Testing MySQL connection to mysql:3306..."
if command -v nc >/dev/null 2>&1; then
    nc -zv mysql 3306 || echo "Cannot connect to mysql:3306"
else
    echo "netcat not available"
fi

echo "=========================================="
