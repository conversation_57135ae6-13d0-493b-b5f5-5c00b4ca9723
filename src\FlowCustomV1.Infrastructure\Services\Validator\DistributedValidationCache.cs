using System.Collections.Concurrent;
using System.Diagnostics;
using System.Text.Json;
using FlowCustomV1.Core.Interfaces.Validator;
using FlowCustomV1.Core.Interfaces.Messaging;
using FlowCustomV1.Core.Models.Workflow;
using Microsoft.Extensions.Logging;

namespace FlowCustomV1.Infrastructure.Services.Validator;

/// <summary>
/// 分布式验证结果缓存实现
/// 支持跨节点的验证结果缓存和同步
/// </summary>
public class DistributedValidationCache : IDistributedValidationCache
{
    private readonly ILogger<DistributedValidationCache> _logger;
    private readonly INatsService _natsService;
    private readonly ConcurrentDictionary<string, CacheItem> _cache;
    private readonly CacheStatistics _statistics;
    private readonly Timer _cleanupTimer;
    private readonly object _lockObject = new();

    private CacheStrategy _strategy = CacheStrategy.LRU;
    private int _maxItems = 10000;
    private int _maxMemoryMb = 100;
    private TimeSpan _defaultExpiration = TimeSpan.FromHours(1);

    /// <summary>
    /// 构造函数
    /// </summary>
    public DistributedValidationCache(
        ILogger<DistributedValidationCache> logger,
        INatsService natsService)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _natsService = natsService ?? throw new ArgumentNullException(nameof(natsService));
        
        _cache = new ConcurrentDictionary<string, CacheItem>();
        _statistics = new CacheStatistics();
        
        // 每5分钟清理一次过期项
        _cleanupTimer = new Timer(CleanupExpiredItems, null, TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(5));
        
        _logger.LogInformation("DistributedValidationCache initialized with strategy: {Strategy}", _strategy);
    }

    #region 缓存操作

    /// <inheritdoc />
    public async Task<WorkflowValidationResult?> GetAsync(string cacheKey, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(cacheKey);

        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            if (_cache.TryGetValue(cacheKey, out var item))
            {
                if (!item.IsExpired)
                {
                    item.LastAccessed = DateTime.UtcNow;
                    item.AccessCount++;
                    
                    UpdateStatistics(true, stopwatch.ElapsedMilliseconds);
                    OnCacheHit(cacheKey, stopwatch.ElapsedMilliseconds);
                    
                    _logger.LogDebug("Cache hit for key: {CacheKey}", cacheKey);
                    return item.Value;
                }
                else
                {
                    // 移除过期项
                    _cache.TryRemove(cacheKey, out _);
                    lock (_lockObject)
                    {
                        _statistics.ExpiredItems++;
                    }
                }
            }

            UpdateStatistics(false, stopwatch.ElapsedMilliseconds);
            OnCacheMiss(cacheKey, "Key not found or expired");
            
            _logger.LogDebug("Cache miss for key: {CacheKey}", cacheKey);
            return null;
        }
        finally
        {
            await Task.CompletedTask; // 保持异步接口一致性
        }
    }

    /// <inheritdoc />
    public async Task SetAsync(string cacheKey, WorkflowValidationResult validationResult, TimeSpan? expiration = null, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(cacheKey);
        ArgumentNullException.ThrowIfNull(validationResult);

        var exp = expiration ?? _defaultExpiration;
        var item = new CacheItem
        {
            Key = cacheKey,
            Value = validationResult,
            CreatedAt = DateTime.UtcNow,
            ExpiresAt = DateTime.UtcNow.Add(exp),
            LastAccessed = DateTime.UtcNow,
            AccessCount = 0
        };

        // 检查容量限制
        await EnsureCapacityAsync();

        _cache.AddOrUpdate(cacheKey, item, (key, oldItem) => item);
        
        lock (_lockObject)
        {
            _statistics.TotalItems = _cache.Count;
            _statistics.CacheSizeBytes += EstimateItemSize(item);
        }

        _logger.LogDebug("Cached validation result for key: {CacheKey}, expires at: {ExpiresAt}", 
            cacheKey, item.ExpiresAt);

        // 同步到其他节点
        await SyncToNodesAsync(cacheKey, cancellationToken);
    }

    /// <inheritdoc />
    public async Task<bool> RemoveAsync(string cacheKey, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(cacheKey);

        var removed = _cache.TryRemove(cacheKey, out var item);
        
        if (removed && item != null)
        {
            lock (_lockObject)
            {
                _statistics.TotalItems = _cache.Count;
                _statistics.CacheSizeBytes -= EstimateItemSize(item);
            }
            
            _logger.LogDebug("Removed cache item for key: {CacheKey}", cacheKey);
            
            // 广播失效通知
            await BroadcastInvalidationAsync(cacheKey, cancellationToken);
        }

        return removed;
    }

    /// <inheritdoc />
    public async Task<bool> ExistsAsync(string cacheKey, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(cacheKey);

        await Task.CompletedTask;
        
        if (_cache.TryGetValue(cacheKey, out var item))
        {
            return !item.IsExpired;
        }
        
        return false;
    }

    /// <inheritdoc />
    public async Task<Dictionary<string, WorkflowValidationResult?>> GetBatchAsync(IEnumerable<string> cacheKeys, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(cacheKeys);

        var result = new Dictionary<string, WorkflowValidationResult?>();
        
        foreach (var key in cacheKeys)
        {
            result[key] = await GetAsync(key, cancellationToken);
        }
        
        return result;
    }

    /// <inheritdoc />
    public async Task SetBatchAsync(Dictionary<string, WorkflowValidationResult> items, TimeSpan? expiration = null, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(items);

        var tasks = items.Select(kvp => SetAsync(kvp.Key, kvp.Value, expiration, cancellationToken));
        await Task.WhenAll(tasks);
    }

    #endregion

    #region 缓存管理

    /// <inheritdoc />
    public async Task ClearAsync(CancellationToken cancellationToken = default)
    {
        _cache.Clear();
        
        lock (_lockObject)
        {
            _statistics.TotalItems = 0;
            _statistics.CacheSizeBytes = 0;
        }
        
        _logger.LogInformation("Cache cleared");
        await Task.CompletedTask;
    }

    /// <inheritdoc />
    public async Task ClearByPatternAsync(string pattern, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(pattern);

        var keysToRemove = _cache.Keys
            .Where(key => IsPatternMatch(pattern, key))
            .ToList();

        foreach (var key in keysToRemove)
        {
            await RemoveAsync(key, cancellationToken);
        }
        
        _logger.LogInformation("Cleared {Count} cache items matching pattern: {Pattern}", 
            keysToRemove.Count, pattern);
    }

    /// <inheritdoc />
    public async Task<CacheStatistics> GetStatisticsAsync(CancellationToken cancellationToken = default)
    {
        await Task.CompletedTask;
        
        lock (_lockObject)
        {
            _statistics.TotalItems = _cache.Count;
            _statistics.LastUpdated = DateTime.UtcNow;
            
            return new CacheStatistics
            {
                TotalItems = _statistics.TotalItems,
                HitCount = _statistics.HitCount,
                MissCount = _statistics.MissCount,
                CacheSizeBytes = _statistics.CacheSizeBytes,
                ExpiredItems = _statistics.ExpiredItems,
                EvictedItems = _statistics.EvictedItems,
                AverageAccessTimeMs = _statistics.AverageAccessTimeMs,
                LastUpdated = _statistics.LastUpdated
            };
        }
    }

    /// <inheritdoc />
    public async Task ResetStatisticsAsync(CancellationToken cancellationToken = default)
    {
        lock (_lockObject)
        {
            _statistics.HitCount = 0;
            _statistics.MissCount = 0;
            _statistics.ExpiredItems = 0;
            _statistics.EvictedItems = 0;
            _statistics.AverageAccessTimeMs = 0;
            _statistics.LastUpdated = DateTime.UtcNow;
        }
        
        _logger.LogInformation("Cache statistics reset");
        await Task.CompletedTask;
    }

    #endregion

    #region 分布式同步

    /// <inheritdoc />
    public async Task SyncToNodesAsync(string cacheKey, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(cacheKey);

        try
        {
            if (_cache.TryGetValue(cacheKey, out var item))
            {
                var syncMessage = new CacheSyncMessage
                {
                    CacheKey = cacheKey,
                    ValidationResult = item.Value,
                    ExpiresAt = item.ExpiresAt,
                    SourceNodeId = Environment.MachineName // 简化的节点ID
                };

                var subject = "flowcustom.cache.sync";
                await _natsService.PublishAsync(subject, syncMessage, cancellationToken);
                
                OnCacheSynced(cacheKey, SyncDirection.Push, null);
                _logger.LogDebug("Synced cache item to nodes: {CacheKey}", cacheKey);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error syncing cache item to nodes: {CacheKey}", cacheKey);
        }
    }

    /// <inheritdoc />
    public async Task SyncFromNodesAsync(string cacheKey, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(cacheKey);

        try
        {
            var requestMessage = new CacheSyncRequestMessage
            {
                CacheKey = cacheKey,
                RequestorNodeId = Environment.MachineName
            };

            var subject = "flowcustom.cache.request";
            await _natsService.PublishAsync(subject, requestMessage, cancellationToken);
            
            _logger.LogDebug("Requested cache sync from nodes: {CacheKey}", cacheKey);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error requesting cache sync from nodes: {CacheKey}", cacheKey);
        }
    }

    /// <inheritdoc />
    public async Task BroadcastInvalidationAsync(string cacheKey, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(cacheKey);

        try
        {
            var invalidationMessage = new CacheInvalidationMessage
            {
                CacheKey = cacheKey,
                SourceNodeId = Environment.MachineName,
                InvalidatedAt = DateTime.UtcNow
            };

            var subject = "flowcustom.cache.invalidate";
            await _natsService.PublishAsync(subject, invalidationMessage, cancellationToken);
            
            _logger.LogDebug("Broadcasted cache invalidation: {CacheKey}", cacheKey);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error broadcasting cache invalidation: {CacheKey}", cacheKey);
        }
    }

    /// <inheritdoc />
    public async Task HandleInvalidationAsync(string cacheKey, string sourceNodeId, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(cacheKey);
        ArgumentException.ThrowIfNullOrWhiteSpace(sourceNodeId);

        // 不处理自己发出的失效通知
        if (sourceNodeId == Environment.MachineName)
        {
            return;
        }

        var removed = _cache.TryRemove(cacheKey, out var item);
        
        if (removed && item != null)
        {
            lock (_lockObject)
            {
                _statistics.TotalItems = _cache.Count;
                _statistics.CacheSizeBytes -= EstimateItemSize(item);
            }
            
            OnCacheInvalidated(cacheKey, "Invalidated by remote node", sourceNodeId);
            _logger.LogDebug("Handled cache invalidation from {SourceNodeId}: {CacheKey}", 
                sourceNodeId, cacheKey);
        }

        await Task.CompletedTask;
    }

    #endregion

    #region 缓存策略

    /// <inheritdoc />
    public void SetCacheStrategy(CacheStrategy strategy)
    {
        _strategy = strategy;
        _logger.LogInformation("Cache strategy changed to: {Strategy}", strategy);
    }

    /// <inheritdoc />
    public CacheStrategy GetCacheStrategy()
    {
        return _strategy;
    }

    /// <inheritdoc />
    public void SetCapacityLimits(int maxItems, int maxMemoryMb)
    {
        _maxItems = Math.Max(1, maxItems);
        _maxMemoryMb = Math.Max(1, maxMemoryMb);

        _logger.LogInformation("Cache capacity limits updated: MaxItems={MaxItems}, MaxMemoryMb={MaxMemoryMb}",
            _maxItems, _maxMemoryMb);
    }

    #endregion

    #region 事件

    /// <inheritdoc />
    public event EventHandler<CacheHitEventArgs>? CacheHit;

    /// <inheritdoc />
    public event EventHandler<CacheMissEventArgs>? CacheMiss;

    /// <inheritdoc />
    public event EventHandler<CacheInvalidatedEventArgs>? CacheInvalidated;

    /// <inheritdoc />
    public event EventHandler<CacheSyncEventArgs>? CacheSynced;

    #endregion

    #region 私有方法

    /// <summary>
    /// 确保缓存容量不超限
    /// </summary>
    private async Task EnsureCapacityAsync()
    {
        if (_cache.Count >= _maxItems || GetCacheSizeMb() >= _maxMemoryMb)
        {
            await EvictItemsAsync();
        }
    }

    /// <summary>
    /// 驱逐缓存项
    /// </summary>
    private async Task EvictItemsAsync()
    {
        var itemsToEvict = _strategy switch
        {
            CacheStrategy.LRU => _cache.Values.OrderBy(i => i.LastAccessed).Take(_cache.Count / 4),
            CacheStrategy.LFU => _cache.Values.OrderBy(i => i.AccessCount).Take(_cache.Count / 4),
            CacheStrategy.FIFO => _cache.Values.OrderBy(i => i.CreatedAt).Take(_cache.Count / 4),
            _ => _cache.Values.OrderBy(i => i.LastAccessed).Take(_cache.Count / 4)
        };

        foreach (var item in itemsToEvict.ToList())
        {
            if (_cache.TryRemove(item.Key, out _))
            {
                lock (_lockObject)
                {
                    _statistics.EvictedItems++;
                    _statistics.CacheSizeBytes -= EstimateItemSize(item);
                }
            }
        }

        lock (_lockObject)
        {
            _statistics.TotalItems = _cache.Count;
        }

        _logger.LogDebug("Evicted {Count} cache items", itemsToEvict.Count());
        await Task.CompletedTask;
    }

    /// <summary>
    /// 清理过期项
    /// </summary>
    private void CleanupExpiredItems(object? state)
    {
        try
        {
            var expiredKeys = _cache
                .Where(kvp => kvp.Value.IsExpired)
                .Select(kvp => kvp.Key)
                .ToList();

            foreach (var key in expiredKeys)
            {
                if (_cache.TryRemove(key, out var item))
                {
                    lock (_lockObject)
                    {
                        _statistics.ExpiredItems++;
                        _statistics.CacheSizeBytes -= EstimateItemSize(item);
                    }
                }
            }

            if (expiredKeys.Any())
            {
                lock (_lockObject)
                {
                    _statistics.TotalItems = _cache.Count;
                }

                _logger.LogDebug("Cleaned up {Count} expired cache items", expiredKeys.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during cache cleanup");
        }
    }

    /// <summary>
    /// 估算缓存项大小
    /// </summary>
    private static long EstimateItemSize(CacheItem item)
    {
        try
        {
            var json = JsonSerializer.Serialize(item.Value);
            return json.Length * 2 + 200; // 估算值：JSON字符串 + 对象开销
        }
        catch
        {
            return 1024; // 默认1KB
        }
    }

    /// <summary>
    /// 获取缓存大小（MB）
    /// </summary>
    private double GetCacheSizeMb()
    {
        lock (_lockObject)
        {
            return _statistics.CacheSizeBytes / (1024.0 * 1024.0);
        }
    }

    /// <summary>
    /// 检查模式匹配
    /// </summary>
    private static bool IsPatternMatch(string pattern, string key)
    {
        // 简单的通配符匹配
        if (pattern.Contains('*'))
        {
            var regexPattern = pattern.Replace("*", ".*");
            return System.Text.RegularExpressions.Regex.IsMatch(key, $"^{regexPattern}$");
        }

        return key.Equals(pattern, StringComparison.OrdinalIgnoreCase);
    }

    /// <summary>
    /// 更新统计信息
    /// </summary>
    private void UpdateStatistics(bool hit, long accessTimeMs)
    {
        lock (_lockObject)
        {
            if (hit)
            {
                _statistics.HitCount++;
            }
            else
            {
                _statistics.MissCount++;
            }

            // 更新平均访问时间
            var totalRequests = _statistics.HitCount + _statistics.MissCount;
            var totalTime = _statistics.AverageAccessTimeMs * (totalRequests - 1) + accessTimeMs;
            _statistics.AverageAccessTimeMs = totalTime / totalRequests;
        }
    }

    /// <summary>
    /// 触发缓存命中事件
    /// </summary>
    private void OnCacheHit(string cacheKey, long accessTimeMs)
    {
        CacheHit?.Invoke(this, new CacheHitEventArgs
        {
            CacheKey = cacheKey,
            AccessTimeMs = accessTimeMs
        });
    }

    /// <summary>
    /// 触发缓存未命中事件
    /// </summary>
    private void OnCacheMiss(string cacheKey, string reason)
    {
        CacheMiss?.Invoke(this, new CacheMissEventArgs
        {
            CacheKey = cacheKey,
            Reason = reason
        });
    }

    /// <summary>
    /// 触发缓存失效事件
    /// </summary>
    private void OnCacheInvalidated(string cacheKey, string reason, string? sourceNodeId = null)
    {
        CacheInvalidated?.Invoke(this, new CacheInvalidatedEventArgs
        {
            CacheKey = cacheKey,
            Reason = reason,
            SourceNodeId = sourceNodeId
        });
    }

    /// <summary>
    /// 触发缓存同步事件
    /// </summary>
    private void OnCacheSynced(string cacheKey, SyncDirection direction, string? targetNodeId)
    {
        CacheSynced?.Invoke(this, new CacheSyncEventArgs
        {
            CacheKey = cacheKey,
            Direction = direction,
            TargetNodeId = targetNodeId
        });
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        _cleanupTimer?.Dispose();
        _cache.Clear();
    }

    #endregion
}

/// <summary>
/// 缓存项
/// </summary>
internal class CacheItem
{
    public string Key { get; set; } = string.Empty;
    public WorkflowValidationResult Value { get; set; } = null!;
    public DateTime CreatedAt { get; set; }
    public DateTime ExpiresAt { get; set; }
    public DateTime LastAccessed { get; set; }
    public long AccessCount { get; set; }

    public bool IsExpired => DateTime.UtcNow > ExpiresAt;
}

/// <summary>
/// 缓存同步消息
/// </summary>
internal class CacheSyncMessage
{
    public string CacheKey { get; set; } = string.Empty;
    public WorkflowValidationResult ValidationResult { get; set; } = null!;
    public DateTime ExpiresAt { get; set; }
    public string SourceNodeId { get; set; } = string.Empty;
}

/// <summary>
/// 缓存同步请求消息
/// </summary>
internal class CacheSyncRequestMessage
{
    public string CacheKey { get; set; } = string.Empty;
    public string RequestorNodeId { get; set; } = string.Empty;
}

/// <summary>
/// 缓存失效消息
/// </summary>
internal class CacheInvalidationMessage
{
    public string CacheKey { get; set; } = string.Empty;
    public string SourceNodeId { get; set; } = string.Empty;
    public DateTime InvalidatedAt { get; set; }
}
