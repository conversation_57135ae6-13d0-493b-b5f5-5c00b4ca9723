using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using FlowCustomV1.Core.Interfaces;
using FlowCustomV1.Core.Interfaces.Services;
using FlowCustomV1.Infrastructure.Configuration;
using FlowCustomV1.Infrastructure.Data;
using FlowCustomV1.Infrastructure.Repositories;
using FlowCustomV1.Infrastructure.Services;
using FlowCustomV1.Infrastructure.Services.Cluster;
using FlowCustomV1.Infrastructure.Services.Designer;
using FlowCustomV1.Infrastructure.Services.Executor;
using FlowCustomV1.Infrastructure.Extensions;
using FlowCustomV1.Core.Interfaces.Executor;
using FlowCustomV1.Core.Extensions;
using FlowCustomV1.Engine;
using FlowCustomV1.Core.Interfaces.Plugins;
using FlowCustomV1.Infrastructure.Services.Plugins;

namespace FlowCustomV1.Infrastructure;

/// <summary>
/// Infrastructure层服务注册扩展
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// 添加Infrastructure层服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">配置</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
    {
        // 注册配置选项
        services.Configure<DatabaseOptions>(configuration.GetSection(DatabaseOptions.SectionName));

        // 注册数据库上下文
        // 移除AddDbContext，只使用DbContextFactory避免生命周期冲突
        /*
        services.AddDbContext<WorkflowDbContext>((serviceProvider, options) =>
        {
            var databaseOptions = configuration.GetSection(DatabaseOptions.SectionName).Get<DatabaseOptions>()
                                 ?? new DatabaseOptions();

            ConfigureDbContext(options, databaseOptions, serviceProvider);
        });
        */

        // 注册数据库上下文工厂（用于解决并发问题）
        // 直接配置，避免依赖scoped的DbContextOptions
        services.AddDbContextFactory<WorkflowDbContext>(options =>
        {
            // 在这里直接读取配置，不依赖DI容器中的scoped服务
            var databaseOptions = configuration.GetSection(DatabaseOptions.SectionName).Get<DatabaseOptions>()
                                 ?? new DatabaseOptions();

            // 如果连接字符串为空，使用默认的内存数据库（用于测试）
            if (string.IsNullOrEmpty(databaseOptions.ConnectionString))
            {
                options.UseInMemoryDatabase("DefaultInMemoryDb");
            }
            else
            {
                // 直接配置数据库连接，避免依赖scoped服务
                switch (databaseOptions.Provider.ToLower())
                {
                    case "mysql":
                        options.UseMySql(databaseOptions.ConnectionString,
                            ServerVersion.AutoDetect(databaseOptions.ConnectionString),
                            mysqlOptions =>
                            {
                                mysqlOptions.EnableRetryOnFailure(
                                    maxRetryCount: 3,
                                    maxRetryDelay: TimeSpan.FromSeconds(30),
                                    errorNumbersToAdd: null);
                            });
                        break;
                    default:
                        options.UseMySql(databaseOptions.ConnectionString,
                            ServerVersion.AutoDetect(databaseOptions.ConnectionString));
                        break;
                }
            }

            options.EnableSensitiveDataLogging(false);
            options.EnableDetailedErrors(true);
        });

        // 注册仓储
        services.AddScoped<IWorkflowRepository, WorkflowRepository>();
        services.AddScoped<FlowCustomV1.Core.Interfaces.Repositories.IExecutionRepository, ExecutionRepository>();

        // 注册数据库初始化服务
        services.AddScoped<IDatabaseInitializationService, DatabaseInitializationService>();

        // 注册NATS消息服务（如果配置了）
        var natsSection = configuration.GetSection("Nats");
        if (natsSection.Exists())
        {
            services.AddNatsMessaging(configuration);
        }

        // 注册节点发现服务配置
        services.Configure<NodeDiscoveryConfiguration>(
            configuration.GetSection(NodeDiscoveryConfiguration.SectionName));

        // 注册节点发现服务
        services.AddSingleton<INodeDiscoveryService, NodeDiscoveryService>();

        // 注册Designer服务
        services.AddScoped<IWorkflowDesignerService, WorkflowDesignerService>();
        services.AddScoped<ITemplateManagementService, TemplateManagementService>();
        services.AddScoped<ICollaborationService, NatsCollaborationService>();

        // 注册业务服务
        services.AddScoped<IWorkflowService, WorkflowService>();

        // 注册Executor服务
        services.AddExecutorServices();

        // 注册Validator服务
        services.AddValidatorServices();

        // 注册Scheduling服务
        services.AddSchedulingServices();

        // 注册插件系统服务
        services.AddPluginServices();

        // 统一注册Core层服务 - Infrastructure层负责所有服务的注册
        services.AddFlowCustomV1Core(configuration);

        // 统一注册Engine层服务 - Infrastructure层负责所有服务的注册
        services.AddWorkflowEngine();

        return services;
    }

    /// <summary>
    /// 添加Infrastructure层服务（带自定义数据库选项）
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="databaseOptions">数据库选项</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddInfrastructure(this IServiceCollection services, DatabaseOptions databaseOptions)
    {
        // 注册配置选项
        services.Configure<DatabaseOptions>(options =>
        {
            options.ConnectionString = databaseOptions.ConnectionString;
            options.Provider = databaseOptions.Provider;
            options.AutoInitialize = databaseOptions.AutoInitialize;
            options.AutoMigrate = databaseOptions.AutoMigrate;
            options.BackupBeforeMigration = databaseOptions.BackupBeforeMigration;
            options.FallbackToInMemory = databaseOptions.FallbackToInMemory;
            options.HealthCheckInterval = databaseOptions.HealthCheckInterval;
            options.Migration = databaseOptions.Migration;
            options.ConnectionPool = databaseOptions.ConnectionPool;
        });

        // 注册数据库上下文
        // 移除AddDbContext，只使用DbContextFactory避免生命周期冲突
        /*
        services.AddDbContext<WorkflowDbContext>((serviceProvider, options) =>
        {
            ConfigureDbContext(options, databaseOptions, serviceProvider);
        });
        */

        // 注册数据库上下文工厂
        // 直接配置，避免依赖scoped的DbContextOptions
        services.AddDbContextFactory<WorkflowDbContext>(options =>
        {
            // 如果连接字符串为空，使用默认的内存数据库（用于测试）
            if (string.IsNullOrEmpty(databaseOptions.ConnectionString))
            {
                options.UseInMemoryDatabase("DefaultInMemoryDb");
            }
            else
            {
                // 直接配置数据库连接，避免依赖scoped服务
                switch (databaseOptions.Provider.ToLower())
                {
                    case "mysql":
                        options.UseMySql(databaseOptions.ConnectionString,
                            ServerVersion.AutoDetect(databaseOptions.ConnectionString),
                            mysqlOptions =>
                            {
                                mysqlOptions.EnableRetryOnFailure(
                                    maxRetryCount: 3,
                                    maxRetryDelay: TimeSpan.FromSeconds(30),
                                    errorNumbersToAdd: null);
                            });
                        break;
                    default:
                        options.UseMySql(databaseOptions.ConnectionString,
                            ServerVersion.AutoDetect(databaseOptions.ConnectionString));
                        break;
                }
            }

            options.EnableSensitiveDataLogging(false);
            options.EnableDetailedErrors(true);
        });

        // 注册仓储
        services.AddScoped<IWorkflowRepository, WorkflowRepository>();
        services.AddScoped<FlowCustomV1.Core.Interfaces.Repositories.IExecutionRepository, ExecutionRepository>();

        // 注册数据库初始化服务
        services.AddScoped<IDatabaseInitializationService, DatabaseInitializationService>();

        // 注册插件系统服务
        services.AddPluginServices();

        return services;
    }

    /// <summary>
    /// 配置数据库上下文
    /// </summary>
    /// <param name="options">DbContext选项构建器</param>
    /// <param name="databaseOptions">数据库选项</param>
    /// <param name="serviceProvider">服务提供者（可为null）</param>
    private static void ConfigureDbContext(DbContextOptionsBuilder options, DatabaseOptions databaseOptions, IServiceProvider? serviceProvider)
    {
        // 启用敏感数据日志记录（仅在开发环境）
        if (serviceProvider != null)
        {
            var logger = serviceProvider.GetService<ILogger<WorkflowDbContext>>();
            if (logger != null)
            {
                options.UseLoggerFactory(serviceProvider.GetService<ILoggerFactory>());
                options.EnableSensitiveDataLogging(false); // 生产环境应设为false
                options.EnableDetailedErrors(true);
            }
        }

        // 根据提供程序配置数据库
        switch (databaseOptions.Provider.ToLower())
        {
            case "mysql":
                ConfigureMySql(options, databaseOptions);
                break;
            default:
                // 只支持MySQL，不再支持SQLite
                ConfigureMySql(options, databaseOptions);
                break;
        }
    }


    /// <summary>
    /// 配置MySQL数据库
    /// </summary>
    /// <param name="options">DbContext选项构建器</param>
    /// <param name="databaseOptions">数据库选项</param>
    private static void ConfigureMySql(DbContextOptionsBuilder options, DatabaseOptions databaseOptions)
    {
        var serverVersion = ServerVersion.AutoDetect(databaseOptions.ConnectionString);
        
        options.UseMySql(databaseOptions.ConnectionString, serverVersion, mySqlOptions =>
        {
            mySqlOptions.CommandTimeout(databaseOptions.ConnectionPool.CommandTimeout);
            mySqlOptions.EnableRetryOnFailure(
                maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(5),
                errorNumbersToAdd: null);
        });

        // MySQL特定配置
        options.ConfigureWarnings(warnings =>
        {
            // 可以在这里配置MySQL特定的警告处理
        });
    }

    /// <summary>
    /// 初始化数据库
    /// 在应用启动时调用此方法来自动初始化数据库
    /// </summary>
    /// <param name="serviceProvider">服务提供者</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>初始化是否成功</returns>
    public static async Task<bool> InitializeDatabaseAsync(this IServiceProvider serviceProvider, CancellationToken cancellationToken = default)
    {
        using var scope = serviceProvider.CreateScope();
        var databaseInitializationService = scope.ServiceProvider.GetRequiredService<IDatabaseInitializationService>();
        
        return await databaseInitializationService.InitializeDatabaseAsync(cancellationToken);
    }

    /// <summary>
    /// 检查数据库健康状态
    /// </summary>
    /// <param name="serviceProvider">服务提供者</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>健康状态</returns>
    public static async Task<Health.DatabaseHealthStatus> CheckDatabaseHealthAsync(this IServiceProvider serviceProvider, CancellationToken cancellationToken = default)
    {
        using var scope = serviceProvider.CreateScope();
        var databaseInitializationService = scope.ServiceProvider.GetRequiredService<IDatabaseInitializationService>();
        
        return await databaseInitializationService.CheckDatabaseHealthAsync(cancellationToken);
    }

    /// <summary>
    /// 添加内存数据库（用于测试）
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="databaseName">数据库名称</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddInMemoryDatabase(this IServiceCollection services, string databaseName = "TestDatabase")
    {
        // 注册内存数据库配置
        services.Configure<DatabaseOptions>(options =>
        {
            options.Provider = "InMemory";
            options.ConnectionString = databaseName;
            options.AutoInitialize = true;
            options.AutoMigrate = false; // 内存数据库不需要迁移
            options.FallbackToInMemory = true;
        });

        // 注册内存数据库上下文
        services.AddDbContext<WorkflowDbContext>(options =>
        {
            options.UseInMemoryDatabase(databaseName);
            options.EnableSensitiveDataLogging(true);
            options.EnableDetailedErrors(true);
        });

        // 注册内存数据库上下文工厂
        services.AddDbContextFactory<WorkflowDbContext>(options =>
        {
            options.UseInMemoryDatabase(databaseName);
            options.EnableSensitiveDataLogging(true);
            options.EnableDetailedErrors(true);
        });

        // 注册仓储
        services.AddScoped<IWorkflowRepository, WorkflowRepository>();
        services.AddScoped<FlowCustomV1.Core.Interfaces.Repositories.IExecutionRepository, ExecutionRepository>();

        // 注册数据库初始化服务
        services.AddScoped<IDatabaseInitializationService, DatabaseInitializationService>();

        return services;
    }

    /// <summary>
    /// 添加Executor服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddExecutorServices(this IServiceCollection services)
    {
        // 注册Executor核心服务
        services.AddScoped<IWorkflowExecutorService, WorkflowExecutorService>();
        services.AddScoped<IExecutionCapacityManager, ExecutionCapacityManager>();
        services.AddScoped<IExecutionStateSyncService, ExecutionStateSyncService>();

        return services;
    }

    /// <summary>
    /// 添加Validator服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddValidatorServices(this IServiceCollection services)
    {
        // 注册分布式验证规则引擎
        services.AddScoped<FlowCustomV1.Core.Interfaces.Validator.IDistributedValidationRuleEngine, Services.Validator.DistributedValidationRuleEngine>();

        // 注册工作流验证器 (Engine层实现)
        services.AddScoped<FlowCustomV1.Core.Interfaces.IWorkflowValidator, FlowCustomV1.Engine.Services.WorkflowValidator>();

        // 注册Validator核心服务
        services.AddScoped<FlowCustomV1.Core.Interfaces.Validator.IWorkflowValidatorService, Services.Validator.WorkflowValidatorService>();

        return services;
    }

    /// <summary>
    /// 添加Scheduling服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddSchedulingServices(this IServiceCollection services)
    {
        // 注册Scheduling核心服务
        services.AddScoped<FlowCustomV1.Core.Interfaces.Scheduling.ITaskDistributionService, Services.Scheduling.TaskDistributionService>();

        return services;
    }

    /// <summary>
    /// 添加插件系统服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddPluginServices(this IServiceCollection services)
    {
        // 注册动态编译器
        services.AddSingleton<IDynamicNodeCompiler, NatashaCompilerService>();

        // 注册插件加载器
        services.AddSingleton<IPluginLoader, McMasterPluginLoader>();

        // 注册插件管理器
        services.AddSingleton<IPluginManager, PluginManager>();

        return services;
    }
}
