#!/usr/bin/env pwsh
<#
.SYNOPSIS
    FlowCustomV1 集成测试运行脚本

.DESCRIPTION
    运行FlowCustomV1项目的集成测试，支持不同的测试环境和配置选项

.PARAMETER Environment
    测试环境 (Development, Testing, Production)

.PARAMETER TestCategory
    测试类别 (All, WorkflowManagement, ClusterManagement, NodeServices, Collaboration)

.PARAMETER Parallel
    是否并行运行测试

.PARAMETER Verbose
    是否显示详细输出

.PARAMETER SkipInfrastructure
    是否跳过基础设施检查

.EXAMPLE
    .\run-integration-tests.ps1 -Environment Testing -TestCategory All -Verbose

.EXAMPLE
    .\run-integration-tests.ps1 -Environment Development -TestCategory WorkflowManagement -Parallel
#>

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("Development", "Testing", "Production")]
    [string]$Environment = "Testing",
    
    [Parameter(Mandatory=$false)]
    [ValidateSet("All", "WorkflowManagement", "ClusterManagement", "NodeServices", "Collaboration")]
    [string]$TestCategory = "All",
    
    [Parameter(Mandatory=$false)]
    [switch]$Parallel,
    
    [Parameter(Mandatory=$false)]
    [switch]$Verbose,
    
    [Parameter(Mandatory=$false)]
    [switch]$SkipInfrastructure
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# 颜色输出函数
function Write-TestLog {
    param([string]$Message, [string]$Level = "Info")
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    switch ($Level) {
        "Success" { Write-Host "[$timestamp] ✅ $Message" -ForegroundColor Green }
        "Warning" { Write-Host "[$timestamp] ⚠️  $Message" -ForegroundColor Yellow }
        "Error"   { Write-Host "[$timestamp] ❌ $Message" -ForegroundColor Red }
        default   { Write-Host "[$timestamp] ℹ️  $Message" -ForegroundColor Cyan }
    }
}

# 检查基础设施状态
function Test-Infrastructure {
    Write-TestLog "检查基础设施状态..." "Info"
    
    # 检查Docker容器状态
    $requiredContainers = @(
        "mysql-test",
        "nats-test-server-1", 
        "nats-test-server-2",
        "nats-test-server-3"
    )
    
    foreach ($container in $requiredContainers) {
        $status = docker ps --filter "name=$container" --format "{{.Status}}"
        if (-not $status -or $status -notlike "*Up*") {
            Write-TestLog "容器 $container 未运行" "Error"
            return $false
        }
        Write-TestLog "容器 $container 运行正常" "Success"
    }
    
    # 检查MySQL连接
    try {
        $mysqlTest = docker exec mysql-test mysql -u flowcustom -pTestPassword123! -e "SELECT 1" 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-TestLog "MySQL连接测试通过" "Success"
        } else {
            Write-TestLog "MySQL连接测试失败" "Error"
            return $false
        }
    } catch {
        Write-TestLog "MySQL连接测试异常: $($_.Exception.Message)" "Error"
        return $false
    }
    
    # 检查NATS集群状态
    try {
        $natsHealth = Invoke-RestMethod -Uri "http://localhost:28222/healthz" -TimeoutSec 5
        Write-TestLog "NATS集群健康检查通过" "Success"
    } catch {
        Write-TestLog "NATS集群健康检查失败: $($_.Exception.Message)" "Warning"
    }
    
    return $true
}

# 构建测试过滤器
function Get-TestFilter {
    param([string]$Category)
    
    switch ($Category) {
        "WorkflowManagement" { return "FullyQualifiedName~WorkflowManagementTests" }
        "ClusterManagement"  { return "FullyQualifiedName~ClusterManagementTests" }
        "NodeServices"       { return "FullyQualifiedName~SpecializedNodeServicesTests" }
        "Collaboration"      { return "FullyQualifiedName~CollaborationFeaturesTests" }
        default              { return "" }
    }
}

# 运行测试
function Invoke-IntegrationTests {
    param(
        [string]$Environment,
        [string]$TestCategory,
        [bool]$ParallelExecution,
        [bool]$VerboseOutput
    )
    
    Write-TestLog "开始运行集成测试" "Info"
    Write-TestLog "环境: $Environment" "Info"
    Write-TestLog "测试类别: $TestCategory" "Info"
    
    # 设置环境变量
    $env:ASPNETCORE_ENVIRONMENT = $Environment
    $env:DOTNET_ENVIRONMENT = $Environment
    
    # 构建测试命令
    $testCommand = "dotnet test tests/FlowCustomV1.Integration.Tests"
    
    # 添加过滤器
    $filter = Get-TestFilter -Category $TestCategory
    if ($filter) {
        $testCommand += " --filter `"$filter`""
    }
    
    # 添加日志选项
    if ($VerboseOutput) {
        $testCommand += " --logger `"console;verbosity=detailed`""
    } else {
        $testCommand += " --logger `"console;verbosity=normal`""
    }
    
    # 添加并行选项
    if ($ParallelExecution) {
        $testCommand += " --parallel"
    }
    
    # 添加结果输出
    $testCommand += " --logger trx --results-directory TestResults"
    
    Write-TestLog "执行命令: $testCommand" "Info"
    
    # 执行测试
    try {
        $startTime = Get-Date
        Invoke-Expression $testCommand
        $endTime = Get-Date
        $duration = $endTime - $startTime
        
        if ($LASTEXITCODE -eq 0) {
            Write-TestLog "集成测试执行成功 (耗时: $($duration.TotalSeconds.ToString('F2'))秒)" "Success"
            return $true
        } else {
            Write-TestLog "集成测试执行失败 (退出代码: $LASTEXITCODE)" "Error"
            return $false
        }
    } catch {
        Write-TestLog "测试执行异常: $($_.Exception.Message)" "Error"
        return $false
    }
}

# 生成测试报告
function New-TestReport {
    Write-TestLog "生成测试报告..." "Info"
    
    $reportPath = "TestResults"
    if (Test-Path $reportPath) {
        $trxFiles = Get-ChildItem -Path $reportPath -Filter "*.trx" | Sort-Object LastWriteTime -Descending
        if ($trxFiles.Count -gt 0) {
            $latestTrx = $trxFiles[0]
            Write-TestLog "最新测试结果文件: $($latestTrx.FullName)" "Info"
            
            # 这里可以添加更多报告生成逻辑
            # 例如转换为HTML报告、发送邮件等
        }
    }
}

# 主执行流程
function Main {
    Write-TestLog "=== FlowCustomV1 集成测试运行器 ===" "Info"
    Write-TestLog "开始时间: $(Get-Date)" "Info"
    
    try {
        # 检查基础设施
        if (-not $SkipInfrastructure) {
            if (-not (Test-Infrastructure)) {
                Write-TestLog "基础设施检查失败，请确保测试环境正常运行" "Error"
                exit 1
            }
        } else {
            Write-TestLog "跳过基础设施检查" "Warning"
        }
        
        # 运行测试
        $testSuccess = Invoke-IntegrationTests -Environment $Environment -TestCategory $TestCategory -ParallelExecution $Parallel -VerboseOutput $Verbose
        
        # 生成报告
        New-TestReport
        
        # 输出结果
        if ($testSuccess) {
            Write-TestLog "=== 集成测试完成 - 成功 ===" "Success"
            exit 0
        } else {
            Write-TestLog "=== 集成测试完成 - 失败 ===" "Error"
            exit 1
        }
        
    } catch {
        Write-TestLog "执行过程中发生异常: $($_.Exception.Message)" "Error"
        Write-TestLog "堆栈跟踪: $($_.ScriptStackTrace)" "Error"
        exit 1
    } finally {
        Write-TestLog "结束时间: $(Get-Date)" "Info"
    }
}

# 显示帮助信息
function Show-Help {
    Write-Host @"
FlowCustomV1 集成测试运行器

用法:
    .\run-integration-tests.ps1 [选项]

选项:
    -Environment <env>     测试环境 (Development|Testing|Production)
    -TestCategory <cat>    测试类别 (All|WorkflowManagement|ClusterManagement|NodeServices|Collaboration)
    -Parallel             并行运行测试
    -Verbose              显示详细输出
    -SkipInfrastructure   跳过基础设施检查
    -Help                 显示此帮助信息

示例:
    .\run-integration-tests.ps1 -Environment Testing -TestCategory All -Verbose
    .\run-integration-tests.ps1 -Environment Development -TestCategory WorkflowManagement
    .\run-integration-tests.ps1 -SkipInfrastructure -Parallel

测试类别说明:
    WorkflowManagement    - 工作流管理功能测试 (FR-WM)
    ClusterManagement     - 分布式集群管理测试 (FR-CM)
    NodeServices          - 专业化节点服务测试 (FR-NS)
    Collaboration         - 协作功能测试 (FR-CF)
    All                   - 运行所有测试类别
"@ -ForegroundColor White
}

# 检查是否请求帮助
if ($args -contains "-Help" -or $args -contains "--help" -or $args -contains "-h") {
    Show-Help
    exit 0
}

# 执行主程序
Main
