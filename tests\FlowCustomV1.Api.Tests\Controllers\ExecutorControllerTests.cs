using FluentAssertions;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System.Net;
using System.Net.Http.Json;
using System.Text.Json;
using FlowCustomV1.Api.Models.Requests;
using FlowCustomV1.Api.Models.Responses;
using FlowCustomV1.Core.Models.Workflow;
using FlowCustomV1.Core.Models.Executor;
using FlowCustomV1.Core.Interfaces.Executor;

namespace FlowCustomV1.Api.Tests.Controllers;

/// <summary>
/// ExecutorController 集成测试
/// </summary>
public class ExecutorControllerTests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;
    private readonly HttpClient _client;
    private readonly ILogger<ExecutorControllerTests> _logger;

    public ExecutorControllerTests(WebApplicationFactory<Program> factory)
    {
        _factory = factory;
        _client = _factory.CreateClient();
        
        // 获取日志服务
        using var scope = _factory.Services.CreateScope();
        var loggerFactory = scope.ServiceProvider.GetRequiredService<ILoggerFactory>();
        _logger = loggerFactory.CreateLogger<ExecutorControllerTests>();
    }

    [Fact]
    public async Task GetServiceStatus_ShouldReturnOk()
    {
        // Act
        var response = await _client.GetAsync("/api/executor/status");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var apiResponse = JsonSerializer.Deserialize<ApiResponse<ExecutorServiceStatus>>(content, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        apiResponse.Should().NotBeNull();
        apiResponse!.Success.Should().BeTrue();
        apiResponse.Data.Should().NotBeNull();
        
        _logger.LogInformation("Executor service status: {Status}", apiResponse.Data);
    }

    [Fact]
    public async Task GetExecutionCapacity_ShouldReturnOk()
    {
        // Act
        var response = await _client.GetAsync("/api/executor/capacity");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var apiResponse = JsonSerializer.Deserialize<ApiResponse<ExecutionCapacity>>(content, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        apiResponse.Should().NotBeNull();
        apiResponse!.Success.Should().BeTrue();
        apiResponse.Data.Should().NotBeNull();
        apiResponse.Data.NodeId.Should().NotBeNullOrEmpty();
        
        _logger.LogInformation("Execution capacity: {Capacity}", JsonSerializer.Serialize(apiResponse.Data));
    }

    [Fact]
    public async Task GetRunningExecutions_ShouldReturnOk()
    {
        // Act
        var response = await _client.GetAsync("/api/executor/executions/running");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var apiResponse = JsonSerializer.Deserialize<ApiResponse<IReadOnlyList<ExecutionInfo>>>(content, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        apiResponse.Should().NotBeNull();
        apiResponse!.Success.Should().BeTrue();
        apiResponse.Data.Should().NotBeNull();
        
        _logger.LogInformation("Running executions count: {Count}", apiResponse.Data.Count);
    }

    [Fact]
    public async Task ExecuteWorkflow_WithValidRequest_ShouldReturnOk()
    {
        // Arrange
        var request = new ExecutionRequest
        {
            WorkflowId = "test-workflow-001",
            InputData = new Dictionary<string, object>
            {
                ["testParam"] = "testValue"
            }
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/executor/execute", request);

        // Assert
        // 注意：这个测试可能会失败，因为工作流可能不存在
        // 但我们主要测试API的响应格式
        var content = await response.Content.ReadAsStringAsync();
        _logger.LogInformation("Execute workflow response: {StatusCode}, Content: {Content}", 
            response.StatusCode, content);

        // 验证响应格式
        content.Should().NotBeNullOrEmpty();
        
        if (response.StatusCode == HttpStatusCode.OK)
        {
            var apiResponse = JsonSerializer.Deserialize<ApiResponse<ExecutionResponse>>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });
            
            apiResponse.Should().NotBeNull();
            apiResponse!.Success.Should().BeTrue();
            apiResponse.Data.Should().NotBeNull();
            apiResponse.Data.ExecutionId.Should().NotBeNullOrEmpty();
        }
        else if (response.StatusCode == HttpStatusCode.BadRequest)
        {
            var apiResponse = JsonSerializer.Deserialize<ApiResponse>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });
            
            apiResponse.Should().NotBeNull();
            apiResponse!.Success.Should().BeFalse();
            apiResponse.Message.Should().NotBeNullOrEmpty();
        }
    }

    [Fact]
    public async Task GetExecutionResult_WithInvalidId_ShouldReturnNotFound()
    {
        // Arrange
        var invalidExecutionId = "invalid-execution-id";

        // Act
        var response = await _client.GetAsync($"/api/executor/executions/{invalidExecutionId}");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NotFound);
        
        var content = await response.Content.ReadAsStringAsync();
        var apiResponse = JsonSerializer.Deserialize<ApiResponse>(content, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        apiResponse.Should().NotBeNull();
        apiResponse!.Success.Should().BeFalse();
        apiResponse.Message.Should().Contain("not found");
    }

    [Fact]
    public async Task CancelExecution_WithInvalidId_ShouldReturnNotFound()
    {
        // Arrange
        var invalidExecutionId = "invalid-execution-id";

        // Act
        var response = await _client.PostAsync($"/api/executor/executions/{invalidExecutionId}/cancel", null);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NotFound);
        
        var content = await response.Content.ReadAsStringAsync();
        var apiResponse = JsonSerializer.Deserialize<ApiResponse>(content, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        apiResponse.Should().NotBeNull();
        apiResponse!.Success.Should().BeFalse();
        apiResponse.Message.Should().Contain("not found");
    }

    [Fact]
    public async Task GetExecutionHistory_WithValidWorkflowId_ShouldReturnOk()
    {
        // Arrange
        var workflowId = "test-workflow-001";

        // Act
        var response = await _client.GetAsync($"/api/executor/executions/history/{workflowId}?pageIndex=0&pageSize=10");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var apiResponse = JsonSerializer.Deserialize<PagedApiResponse<ExecutionInfo>>(content, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        apiResponse.Should().NotBeNull();
        apiResponse!.Success.Should().BeTrue();
        apiResponse.Data.Should().NotBeNull();
        apiResponse.Pagination.Should().NotBeNull();
        apiResponse.Pagination.CurrentPage.Should().Be(1); // API使用1基索引
        apiResponse.Pagination.PageSize.Should().Be(10);
        
        _logger.LogInformation("Execution history count: {Count}", apiResponse.Data.Count);
    }

    [Fact]
    public async Task ExecuteWorkflow_WithInvalidRequest_ShouldReturnBadRequest()
    {
        // Arrange
        var invalidRequest = new ExecutionRequest
        {
            WorkflowId = "", // 空的工作流ID
            InputData = new Dictionary<string, object>()
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/executor/execute", invalidRequest);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        
        var content = await response.Content.ReadAsStringAsync();
        var apiResponse = JsonSerializer.Deserialize<ApiResponse>(content, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        apiResponse.Should().NotBeNull();
        apiResponse!.Success.Should().BeFalse();
        apiResponse.Message.Should().NotBeNullOrEmpty();
    }

    [Fact]
    public async Task GetNodeLoadInfo_ShouldReturnOk()
    {
        // Act
        var response = await _client.GetAsync("/api/executor/load");

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var apiResponse = JsonSerializer.Deserialize<ApiResponse<NodeLoadInfo>>(content, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        apiResponse.Should().NotBeNull();
        apiResponse!.Success.Should().BeTrue();
        apiResponse.Data.Should().NotBeNull();
        apiResponse.Data.NodeId.Should().NotBeNullOrEmpty();
        apiResponse.Data.NodeRole.Should().Be("Executor");
        
        _logger.LogInformation("Node load info: {LoadInfo}", JsonSerializer.Serialize(apiResponse.Data));
    }
}
