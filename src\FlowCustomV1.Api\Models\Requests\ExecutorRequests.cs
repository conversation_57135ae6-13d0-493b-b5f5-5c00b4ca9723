using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace FlowCustomV1.Api.Models.Requests;

/// <summary>
/// 执行工作流请求模型
/// </summary>
public class ExecuteWorkflowRequest
{
    /// <summary>
    /// 工作流ID
    /// </summary>
    [Required(ErrorMessage = "WorkflowId is required")]
    [JsonPropertyName("workflowId")]
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 输入数据
    /// </summary>
    [JsonPropertyName("inputData")]
    public Dictionary<string, object> InputData { get; set; } = new();

    /// <summary>
    /// 执行选项
    /// </summary>
    [JsonPropertyName("options")]
    public ExecutionOptionsRequest? Options { get; set; }

    /// <summary>
    /// 执行标签
    /// </summary>
    [JsonPropertyName("tags")]
    public List<string> Tags { get; set; } = new();

    /// <summary>
    /// 执行元数据
    /// </summary>
    [JsonPropertyName("metadata")]
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// 执行选项请求模型
/// </summary>
public class ExecutionOptionsRequest
{
    /// <summary>
    /// 超时时间（毫秒）
    /// </summary>
    [JsonPropertyName("timeoutMs")]
    [Range(1000, 3600000, ErrorMessage = "Timeout must be between 1 second and 1 hour")]
    public long TimeoutMs { get; set; } = 300000; // 5分钟默认超时

    /// <summary>
    /// 最大重试次数
    /// </summary>
    [JsonPropertyName("maxRetries")]
    [Range(0, 10, ErrorMessage = "Max retries must be between 0 and 10")]
    public int MaxRetries { get; set; } = 3;

    /// <summary>
    /// 是否允许迁移
    /// </summary>
    [JsonPropertyName("allowMigration")]
    public bool AllowMigration { get; set; } = true;

    /// <summary>
    /// 是否持久化结果
    /// </summary>
    [JsonPropertyName("persistResult")]
    public bool PersistResult { get; set; } = true;

    /// <summary>
    /// 执行优先级
    /// </summary>
    [JsonPropertyName("priority")]
    public string Priority { get; set; } = "Normal";
}

/// <summary>
/// 迁移执行请求模型
/// </summary>
public class MigrateExecutionRequest
{
    /// <summary>
    /// 目标节点ID
    /// </summary>
    [Required(ErrorMessage = "TargetNodeId is required")]
    [JsonPropertyName("targetNodeId")]
    public string TargetNodeId { get; set; } = string.Empty;

    /// <summary>
    /// 迁移原因
    /// </summary>
    [JsonPropertyName("reason")]
    public string Reason { get; set; } = "Manual migration";

    /// <summary>
    /// 迁移类型
    /// </summary>
    [JsonPropertyName("migrationType")]
    public string MigrationType { get; set; } = "Manual";

    /// <summary>
    /// 是否强制迁移
    /// </summary>
    [JsonPropertyName("force")]
    public bool Force { get; set; } = false;
}

/// <summary>
/// 批量执行控制请求模型
/// </summary>
public class BatchExecutionControlRequest
{
    /// <summary>
    /// 执行ID列表
    /// </summary>
    [Required(ErrorMessage = "ExecutionIds is required")]
    [MinLength(1, ErrorMessage = "At least one execution ID is required")]
    [JsonPropertyName("executionIds")]
    public List<string> ExecutionIds { get; set; } = new();

    /// <summary>
    /// 控制操作
    /// </summary>
    [Required(ErrorMessage = "Operation is required")]
    [JsonPropertyName("operation")]
    public string Operation { get; set; } = string.Empty;

    /// <summary>
    /// 操作参数
    /// </summary>
    [JsonPropertyName("parameters")]
    public Dictionary<string, object> Parameters { get; set; } = new();
}

/// <summary>
/// 执行查询请求模型
/// </summary>
public class ExecutionQueryRequest
{
    /// <summary>
    /// 工作流ID过滤器
    /// </summary>
    [JsonPropertyName("workflowId")]
    public string? WorkflowId { get; set; }

    /// <summary>
    /// 执行状态过滤器
    /// </summary>
    [JsonPropertyName("states")]
    public List<string>? States { get; set; }

    /// <summary>
    /// 执行器节点ID过滤器
    /// </summary>
    [JsonPropertyName("executorNodeId")]
    public string? ExecutorNodeId { get; set; }

    /// <summary>
    /// 开始时间范围（开始）
    /// </summary>
    [JsonPropertyName("startedAfter")]
    public DateTime? StartedAfter { get; set; }

    /// <summary>
    /// 开始时间范围（结束）
    /// </summary>
    [JsonPropertyName("startedBefore")]
    public DateTime? StartedBefore { get; set; }

    /// <summary>
    /// 标签过滤器
    /// </summary>
    [JsonPropertyName("tags")]
    public List<string>? Tags { get; set; }

    /// <summary>
    /// 页索引
    /// </summary>
    [JsonPropertyName("pageIndex")]
    [Range(0, int.MaxValue, ErrorMessage = "Page index must be non-negative")]
    public int PageIndex { get; set; } = 0;

    /// <summary>
    /// 页大小
    /// </summary>
    [JsonPropertyName("pageSize")]
    [Range(1, 100, ErrorMessage = "Page size must be between 1 and 100")]
    public int PageSize { get; set; } = 20;

    /// <summary>
    /// 排序字段
    /// </summary>
    [JsonPropertyName("sortBy")]
    public string? SortBy { get; set; }

    /// <summary>
    /// 是否降序排序
    /// </summary>
    [JsonPropertyName("sortDescending")]
    public bool SortDescending { get; set; } = true;
}

/// <summary>
/// 容量配置请求模型
/// </summary>
public class CapacityConfigRequest
{
    /// <summary>
    /// 最大并发执行数
    /// </summary>
    [JsonPropertyName("maxConcurrentExecutions")]
    [Range(1, 1000, ErrorMessage = "Max concurrent executions must be between 1 and 1000")]
    public int MaxConcurrentExecutions { get; set; } = 10;

    /// <summary>
    /// 最大CPU使用率（0-100）
    /// </summary>
    [JsonPropertyName("maxCpuUsagePercent")]
    [Range(10, 100, ErrorMessage = "Max CPU usage must be between 10% and 100%")]
    public double MaxCpuUsagePercent { get; set; } = 80;

    /// <summary>
    /// 最大内存使用率（0-100）
    /// </summary>
    [JsonPropertyName("maxMemoryUsagePercent")]
    [Range(10, 100, ErrorMessage = "Max memory usage must be between 10% and 100%")]
    public double MaxMemoryUsagePercent { get; set; } = 80;

    /// <summary>
    /// 最大磁盘使用率（0-100）
    /// </summary>
    [JsonPropertyName("maxDiskUsagePercent")]
    [Range(10, 100, ErrorMessage = "Max disk usage must be between 10% and 100%")]
    public double MaxDiskUsagePercent { get; set; } = 90;

    /// <summary>
    /// 最大网络使用率（0-100）
    /// </summary>
    [JsonPropertyName("maxNetworkUsagePercent")]
    [Range(10, 100, ErrorMessage = "Max network usage must be between 10% and 100%")]
    public double MaxNetworkUsagePercent { get; set; } = 80;

    /// <summary>
    /// 最大负载评分
    /// </summary>
    [JsonPropertyName("maxLoadScore")]
    [Range(10, 100, ErrorMessage = "Max load score must be between 10 and 100")]
    public double MaxLoadScore { get; set; } = 90;
}

/// <summary>
/// 节点负载更新请求模型
/// </summary>
public class NodeLoadUpdateRequest
{
    /// <summary>
    /// 节点名称
    /// </summary>
    [JsonPropertyName("nodeName")]
    public string? NodeName { get; set; }

    /// <summary>
    /// 节点角色
    /// </summary>
    [JsonPropertyName("nodeRole")]
    public string NodeRole { get; set; } = "Executor";

    /// <summary>
    /// 节点标签
    /// </summary>
    [JsonPropertyName("tags")]
    public List<string> Tags { get; set; } = new();

    /// <summary>
    /// 节点能力
    /// </summary>
    [JsonPropertyName("capabilities")]
    public List<string> Capabilities { get; set; } = new();

    /// <summary>
    /// 自定义属性
    /// </summary>
    [JsonPropertyName("customProperties")]
    public Dictionary<string, object> CustomProperties { get; set; } = new();
}
