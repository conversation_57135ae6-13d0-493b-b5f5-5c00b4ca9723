using FlowCustomV1.Core.Interfaces.Executor;
using FlowCustomV1.Core.Interfaces.Messaging;
using FlowCustomV1.Core.Interfaces;
using FlowCustomV1.Core.Interfaces.Repositories;
using FlowCustomV1.Core.Models.Executor;
using FlowCustomV1.Core.Models.Messages;
using FlowCustomV1.Core.Models.Workflow;
using FlowCustomV1.Core.Models.Common;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Diagnostics;

namespace FlowCustomV1.Infrastructure.Services.Executor;

/// <summary>
/// 工作流执行器服务实现
/// 基于NATS消息系统的分布式工作流执行服务
/// </summary>
public class WorkflowExecutorService : IWorkflowExecutorService, IDisposable
{
    private readonly INatsService _natsService;
    private readonly IWorkflowScheduler _workflowScheduler;
    private readonly IWorkflowRepository _workflowRepository;
    private readonly FlowCustomV1.Core.Interfaces.Repositories.IExecutionRepository _executionRepository;
    private readonly IExecutionCapacityManager _capacityManager;
    private readonly IExecutionStateSyncService _stateSyncService;
    private readonly IMessageTopicService _topicService;
    private readonly ILogger<WorkflowExecutorService> _logger;

    // 执行管理
    private readonly ConcurrentDictionary<string, WorkflowExecutionContext> _activeExecutions = new();
    private readonly ConcurrentDictionary<string, CancellationTokenSource> _executionCancellationTokens = new();
    private readonly ConcurrentDictionary<string, TaskCompletionSource<WorkflowExecutionResult>> _executionTasks = new();

    // 服务状态
    private ExecutorServiceStatus _status = ExecutorServiceStatus.NotStarted;
    private readonly object _statusLock = new();
    private bool _disposed = false;

    // 统计信息
    private long _totalExecutions = 0;
    private long _successfulExecutions = 0;
    private long _failedExecutions = 0;
    private readonly ConcurrentDictionary<string, ExecutionStatistics> _statistics = new();

    public WorkflowExecutorService(
        INatsService natsService,
        IWorkflowScheduler workflowScheduler,
        IWorkflowRepository workflowRepository,
        FlowCustomV1.Core.Interfaces.Repositories.IExecutionRepository executionRepository,
        IExecutionCapacityManager capacityManager,
        IExecutionStateSyncService stateSyncService,
        IMessageTopicService topicService,
        ILogger<WorkflowExecutorService> logger)
    {
        _natsService = natsService ?? throw new ArgumentNullException(nameof(natsService));
        _workflowScheduler = workflowScheduler ?? throw new ArgumentNullException(nameof(workflowScheduler));
        _workflowRepository = workflowRepository ?? throw new ArgumentNullException(nameof(workflowRepository));
        _executionRepository = executionRepository ?? throw new ArgumentNullException(nameof(executionRepository));
        _capacityManager = capacityManager ?? throw new ArgumentNullException(nameof(capacityManager));
        _stateSyncService = stateSyncService ?? throw new ArgumentNullException(nameof(stateSyncService));
        _topicService = topicService ?? throw new ArgumentNullException(nameof(topicService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    #region 执行管理

    /// <inheritdoc />
    public async Task<string> ExecuteWorkflowAsync(string workflowId, Dictionary<string, object>? inputData = null, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrEmpty(workflowId);

        try
        {
            _logger.LogDebug("Executing workflow {WorkflowId}", workflowId);

            // 获取工作流定义
            var workflowDefinition = await _workflowRepository.GetByIdAsync(workflowId, cancellationToken);
            if (workflowDefinition == null)
            {
                throw new InvalidOperationException($"Workflow {workflowId} not found");
            }

            return await ExecuteWorkflowAsync(workflowDefinition, inputData, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to execute workflow {WorkflowId}", workflowId);
            throw;
        }
    }

    /// <inheritdoc />
    public async Task<string> ExecuteWorkflowAsync(WorkflowDefinition workflowDefinition, Dictionary<string, object>? inputData = null, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(workflowDefinition);

        if (_status != ExecutorServiceStatus.Running)
        {
            throw new InvalidOperationException($"Executor service is not running. Current status: {_status}");
        }

        var executionId = Guid.NewGuid().ToString();
        var stopwatch = Stopwatch.StartNew();

        try
        {
            _logger.LogInformation("Starting workflow execution {ExecutionId} for workflow {WorkflowId}", 
                executionId, workflowDefinition.WorkflowId);

            // 检查执行容量
            var estimatedUsage = EstimateResourceUsage(workflowDefinition);
            if (!_capacityManager.CanAcceptExecution(estimatedUsage))
            {
                throw new InvalidOperationException("Insufficient capacity to execute workflow");
            }

            // 预留资源
            if (!_capacityManager.ReserveResources(executionId, estimatedUsage))
            {
                throw new InvalidOperationException("Failed to reserve resources for execution");
            }

            // 创建执行上下文
            var executionContext = CreateExecutionContext(executionId, workflowDefinition, inputData);
            
            // 创建取消令牌
            var executionCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
            _executionCancellationTokens[executionId] = executionCts;

            // 创建执行任务完成源
            var executionTcs = new TaskCompletionSource<WorkflowExecutionResult>();
            _executionTasks[executionId] = executionTcs;

            // 注册活跃执行
            _activeExecutions[executionId] = executionContext;

            // 触发执行开始事件
            OnExecutionStarted(new ExecutionStartedEventArgs
            {
                ExecutionId = executionId,
                WorkflowId = workflowDefinition.WorkflowId,
                WorkflowName = workflowDefinition.Name,
                ExecutorNodeId = Environment.MachineName,
                StartedAt = DateTime.UtcNow,
                InputData = inputData ?? new Dictionary<string, object>(),
                Priority = ExecutionPriority.Normal
            });

            // 同步执行状态
            await _stateSyncService.SyncExecutionStateAsync(executionId, WorkflowExecutionState.Running, cancellationToken);

            // 异步执行工作流
            _ = Task.Run(async () => await ExecuteWorkflowInternalAsync(executionContext, executionCts.Token), cancellationToken);

            // 更新统计
            Interlocked.Increment(ref _totalExecutions);

            _logger.LogInformation("Workflow execution {ExecutionId} started successfully", executionId);
            return executionId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start workflow execution {ExecutionId}", executionId);
            
            // 清理资源
            CleanupExecution(executionId);
            
            throw;
        }
    }

    /// <inheritdoc />
    public async Task<WorkflowExecutionResult?> GetExecutionResultAsync(string executionId, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrEmpty(executionId);

        try
        {
            // 首先检查活跃执行
            if (_executionTasks.TryGetValue(executionId, out var executionTcs))
            {
                // 如果执行还在进行中，等待完成或超时
                var timeoutTask = Task.Delay(TimeSpan.FromSeconds(30), cancellationToken);
                var completedTask = await Task.WhenAny(executionTcs.Task, timeoutTask);
                
                if (completedTask == executionTcs.Task)
                {
                    return await executionTcs.Task;
                }
                
                // 超时，返回当前状态
                if (_activeExecutions.TryGetValue(executionId, out var context))
                {
                    return CreatePartialResult(context);
                }
            }

            // 从持久化存储获取结果
            var executionResult = await _executionRepository.GetExecutionResultAsync(executionId, cancellationToken);
            return executionResult;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get execution result for {ExecutionId}", executionId);
            throw;
        }
    }

    /// <inheritdoc />
    public async Task<bool> CancelExecutionAsync(string executionId, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrEmpty(executionId);

        try
        {
            _logger.LogInformation("Cancelling execution {ExecutionId}", executionId);

            // 取消执行
            if (_executionCancellationTokens.TryGetValue(executionId, out var cts))
            {
                cts.Cancel();
            }

            // 同步状态
            await _stateSyncService.SyncExecutionStateAsync(executionId, WorkflowExecutionState.Cancelled, cancellationToken);

            // 清理资源
            CleanupExecution(executionId);

            _logger.LogInformation("Execution {ExecutionId} cancelled successfully", executionId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to cancel execution {ExecutionId}", executionId);
            return false;
        }
    }

    /// <inheritdoc />
    public async Task<bool> PauseExecutionAsync(string executionId, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrEmpty(executionId);

        try
        {
            _logger.LogInformation("Pausing execution {ExecutionId}", executionId);

            if (_activeExecutions.TryGetValue(executionId, out var context))
            {
                context.State = WorkflowExecutionState.Paused;
                await _stateSyncService.SyncExecutionStateAsync(executionId, WorkflowExecutionState.Paused, cancellationToken);
                
                _logger.LogInformation("Execution {ExecutionId} paused successfully", executionId);
                return true;
            }

            _logger.LogWarning("Execution {ExecutionId} not found for pause operation", executionId);
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to pause execution {ExecutionId}", executionId);
            return false;
        }
    }

    /// <inheritdoc />
    public async Task<bool> ResumeExecutionAsync(string executionId, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrEmpty(executionId);

        try
        {
            _logger.LogInformation("Resuming execution {ExecutionId}", executionId);

            if (_activeExecutions.TryGetValue(executionId, out var context))
            {
                context.State = WorkflowExecutionState.Running;
                await _stateSyncService.SyncExecutionStateAsync(executionId, WorkflowExecutionState.Running, cancellationToken);
                
                _logger.LogInformation("Execution {ExecutionId} resumed successfully", executionId);
                return true;
            }

            _logger.LogWarning("Execution {ExecutionId} not found for resume operation", executionId);
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to resume execution {ExecutionId}", executionId);
            return false;
        }
    }

    #endregion

    #region 执行查询

    /// <inheritdoc />
    public async Task<IReadOnlyList<ExecutionInfo>> GetRunningExecutionsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var runningExecutions = new List<ExecutionInfo>();

            foreach (var kvp in _activeExecutions)
            {
                var executionId = kvp.Key;
                var context = kvp.Value;

                var executionInfo = new ExecutionInfo
                {
                    ExecutionId = executionId,
                    WorkflowId = context.WorkflowId,
                    WorkflowName = context.Definition.Name,
                    State = context.State,
                    ExecutorNodeId = Environment.MachineName,
                    StartedAt = context.StartedAt,
                    InputDataSize = EstimateDataSize(context.InputData),
                    Priority = ExecutionPriority.Normal,
                    Tags = new List<string> { "running" }
                };

                runningExecutions.Add(executionInfo);
            }

            return runningExecutions;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get running executions");
            throw;
        }
    }

    /// <inheritdoc />
    public async Task<PagedResult<ExecutionInfo>> GetExecutionHistoryAsync(string workflowId, int pageIndex = 0, int pageSize = 20, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrEmpty(workflowId);

        try
        {
            // 从持久化存储获取执行历史
            var executions = await _executionRepository.GetExecutionHistoryResultsAsync(workflowId, pageIndex, pageSize, cancellationToken);

            var executionInfos = executions.Items.Select(e => new ExecutionInfo
            {
                ExecutionId = e.ExecutionId,
                WorkflowId = e.WorkflowId,
                WorkflowName = e.WorkflowName ?? "Unknown",
                State = e.State,
                ExecutorNodeId = e.ExecutorNodeId ?? Environment.MachineName,
                StartedAt = e.StartedAt,
                CompletedAt = e.CompletedAt,
                ErrorMessage = e.ErrorMessage,
                Priority = ExecutionPriority.Normal
            }).ToList();

            return new PagedResult<ExecutionInfo>
            {
                Items = executionInfos,
                TotalCount = executions.TotalCount,
                PageIndex = pageIndex,
                PageSize = pageSize
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get execution history for workflow {WorkflowId}", workflowId);
            throw;
        }
    }

    /// <inheritdoc />
    public async Task<ExecutionStatistics> GetExecutionStatisticsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var now = DateTime.UtcNow;
            var startTime = now.AddHours(-24); // 最近24小时的统计

            var statistics = new ExecutionStatistics
            {
                NodeId = Environment.MachineName,
                StartTime = startTime,
                EndTime = now,
                TotalExecutions = _totalExecutions,
                SuccessfulExecutions = _successfulExecutions,
                FailedExecutions = _failedExecutions,
                CancelledExecutions = _totalExecutions - _successfulExecutions - _failedExecutions
            };

            // 计算平均执行时间等统计信息
            if (_totalExecutions > 0)
            {
                statistics.ThroughputPerHour = (double)_totalExecutions / 24;
            }

            return statistics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get execution statistics");
            throw;
        }
    }

    #endregion

    #region 资源管理

    /// <inheritdoc />
    public async Task<ExecutionCapacity> GetExecutionCapacityAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            return _capacityManager.GetCurrentCapacity();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get execution capacity");
            throw;
        }
    }

    /// <inheritdoc />
    public async Task UpdateNodeLoadAsync(NodeLoadInfo loadInfo, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(loadInfo);

        try
        {
            // 更新本地负载信息
            loadInfo.NodeId = Environment.MachineName;
            loadInfo.CurrentExecutions = _activeExecutions.Count;
            loadInfo.LastHeartbeat = DateTime.UtcNow;

            // 通过NATS广播负载信息
            var loadUpdateTopic = _topicService.GetNodeLoadUpdateTopic();
            await _natsService.PublishAsync(loadUpdateTopic, loadInfo, cancellationToken);

            _logger.LogDebug("Node load information updated and broadcasted");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update node load information");
            throw;
        }
    }

    #endregion

    #region 分布式执行

    /// <inheritdoc />
    public async Task<bool> MigrateExecutionAsync(string executionId, string targetNodeId, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrEmpty(executionId);
        ArgumentException.ThrowIfNullOrEmpty(targetNodeId);

        try
        {
            _logger.LogInformation("Migrating execution {ExecutionId} to node {TargetNodeId}", executionId, targetNodeId);

            if (!_activeExecutions.TryGetValue(executionId, out var context))
            {
                _logger.LogWarning("Execution {ExecutionId} not found for migration", executionId);
                return false;
            }

            // 创建执行上下文快照
            var contextSnapshot = CreateContextSnapshot(context);

            // 创建迁移消息
            var migrationMessage = new ExecutionMigrationMessage
            {
                ExecutionId = executionId,
                SourceNodeId = Environment.MachineName,
                TargetNodeId = targetNodeId,
                Reason = "Manual migration",
                ContextSnapshot = contextSnapshot,
                MigrationType = MigrationType.Manual
            };

            // 发送迁移请求
            var migrationTopic = _topicService.GetExecutionMigrationTopic(targetNodeId);
            var response = await _natsService.RequestAsync<ExecutionMigrationMessage, ExecutionMigrationResponseMessage>(
                migrationTopic, migrationMessage, TimeSpan.FromSeconds(30), cancellationToken);

            if (response?.Success == true)
            {
                // 迁移成功，清理本地执行
                CleanupExecution(executionId);
                _logger.LogInformation("Execution {ExecutionId} migrated successfully to {TargetNodeId}", executionId, targetNodeId);
                return true;
            }

            _logger.LogWarning("Failed to migrate execution {ExecutionId} to {TargetNodeId}: {Error}",
                executionId, targetNodeId, response?.ErrorMessage);
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to migrate execution {ExecutionId} to {TargetNodeId}", executionId, targetNodeId);
            return false;
        }
    }

    /// <inheritdoc />
    public async Task<bool> ReceiveMigratedExecutionAsync(ExecutionMigrationContext executionContext, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(executionContext);

        try
        {
            _logger.LogInformation("Receiving migrated execution {ExecutionId} from {SourceNodeId}",
                executionContext.ExecutionId, executionContext.SourceNodeId);

            // 检查容量
            var estimatedUsage = EstimateResourceUsage(executionContext.ContextSnapshot.WorkflowDefinition);
            if (!_capacityManager.CanAcceptExecution(estimatedUsage))
            {
                _logger.LogWarning("Insufficient capacity to receive migrated execution {ExecutionId}", executionContext.ExecutionId);
                return false;
            }

            // 恢复执行上下文
            var restoredContext = RestoreExecutionContext(executionContext.ContextSnapshot);

            // 注册执行
            _activeExecutions[executionContext.ExecutionId] = restoredContext;

            // 创建取消令牌
            var executionCts = new CancellationTokenSource();
            _executionCancellationTokens[executionContext.ExecutionId] = executionCts;

            // 创建执行任务完成源
            var executionTcs = new TaskCompletionSource<WorkflowExecutionResult>();
            _executionTasks[executionContext.ExecutionId] = executionTcs;

            // 预留资源
            _capacityManager.ReserveResources(executionContext.ExecutionId, estimatedUsage);

            // 继续执行工作流
            _ = Task.Run(async () => await ExecuteWorkflowInternalAsync(restoredContext, executionCts.Token), cancellationToken);

            _logger.LogInformation("Migrated execution {ExecutionId} received and resumed successfully", executionContext.ExecutionId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to receive migrated execution {ExecutionId}", executionContext.ExecutionId);
            return false;
        }
    }

    #endregion

    #region 服务生命周期

    /// <inheritdoc />
    public async Task StartAsync(CancellationToken cancellationToken = default)
    {
        lock (_statusLock)
        {
            if (_status != ExecutorServiceStatus.NotStarted && _status != ExecutorServiceStatus.Stopped)
            {
                throw new InvalidOperationException($"Cannot start service in {_status} state");
            }
            _status = ExecutorServiceStatus.Starting;
        }

        try
        {
            _logger.LogInformation("Starting Workflow Executor Service");

            // 启动状态同步服务
            await _stateSyncService.StartAsync(cancellationToken);

            // 初始化NATS订阅
            await InitializeNatsSubscriptionsAsync();

            lock (_statusLock)
            {
                _status = ExecutorServiceStatus.Running;
            }

            _logger.LogInformation("Workflow Executor Service started successfully");
        }
        catch (Exception ex)
        {
            lock (_statusLock)
            {
                _status = ExecutorServiceStatus.Error;
            }
            _logger.LogError(ex, "Failed to start Workflow Executor Service");
            throw;
        }
    }

    /// <inheritdoc />
    public async Task StopAsync(CancellationToken cancellationToken = default)
    {
        lock (_statusLock)
        {
            if (_status == ExecutorServiceStatus.Stopped || _status == ExecutorServiceStatus.NotStarted)
            {
                return;
            }
            _status = ExecutorServiceStatus.Stopping;
        }

        try
        {
            _logger.LogInformation("Stopping Workflow Executor Service");

            // 取消所有活跃执行
            var cancellationTasks = new List<Task>();
            foreach (var kvp in _executionCancellationTokens)
            {
                kvp.Value.Cancel();
                if (_executionTasks.TryGetValue(kvp.Key, out var tcs))
                {
                    cancellationTasks.Add(tcs.Task);
                }
            }

            // 等待所有执行完成或超时
            if (cancellationTasks.Count > 0)
            {
                var timeoutTask = Task.Delay(TimeSpan.FromSeconds(30), cancellationToken);
                var allExecutionsTask = Task.WhenAll(cancellationTasks);
                await Task.WhenAny(allExecutionsTask, timeoutTask);
            }

            // 停止状态同步服务
            await _stateSyncService.StopAsync(cancellationToken);

            // 清理资源
            _activeExecutions.Clear();
            _executionCancellationTokens.Clear();
            _executionTasks.Clear();

            lock (_statusLock)
            {
                _status = ExecutorServiceStatus.Stopped;
            }

            _logger.LogInformation("Workflow Executor Service stopped successfully");
        }
        catch (Exception ex)
        {
            lock (_statusLock)
            {
                _status = ExecutorServiceStatus.Error;
            }
            _logger.LogError(ex, "Failed to stop Workflow Executor Service");
            throw;
        }
    }

    /// <inheritdoc />
    public ExecutorServiceStatus GetStatus()
    {
        lock (_statusLock)
        {
            return _status;
        }
    }

    #endregion

    #region 事件

    /// <inheritdoc />
    public event EventHandler<ExecutionStartedEventArgs>? ExecutionStarted;

    /// <inheritdoc />
    public event EventHandler<ExecutionCompletedEventArgs>? ExecutionCompleted;

    /// <inheritdoc />
    public event EventHandler<ExecutionFailedEventArgs>? ExecutionFailed;

    /// <inheritdoc />
    public event EventHandler<ExecutionStatusChangedEventArgs>? ExecutionStatusChanged;

    /// <summary>
    /// 触发执行开始事件
    /// </summary>
    protected virtual void OnExecutionStarted(ExecutionStartedEventArgs e)
    {
        ExecutionStarted?.Invoke(this, e);
    }

    /// <summary>
    /// 触发执行完成事件
    /// </summary>
    protected virtual void OnExecutionCompleted(ExecutionCompletedEventArgs e)
    {
        ExecutionCompleted?.Invoke(this, e);
    }

    /// <summary>
    /// 触发执行失败事件
    /// </summary>
    protected virtual void OnExecutionFailed(ExecutionFailedEventArgs e)
    {
        ExecutionFailed?.Invoke(this, e);
    }

    /// <summary>
    /// 触发执行状态变更事件
    /// </summary>
    protected virtual void OnExecutionStatusChanged(ExecutionStatusChangedEventArgs e)
    {
        ExecutionStatusChanged?.Invoke(this, e);
    }

    #endregion

    #region 私有方法

    /// <summary>
    /// 初始化NATS订阅
    /// </summary>
    private async Task InitializeNatsSubscriptionsAsync()
    {
        try
        {
            // 订阅执行请求
            var executionRequestTopic = _topicService.GetExecutionRequestTopic();
            await _natsService.SubscribeAsync<ExecutionRequestMessage>(
                executionRequestTopic, OnExecutionRequestReceived);

            // 订阅执行控制消息
            var executionControlTopic = _topicService.GetExecutionControlTopic();
            await _natsService.SubscribeAsync<ExecutionControlMessage>(
                executionControlTopic, OnExecutionControlReceived);

            // 订阅执行迁移消息
            var migrationTopic = _topicService.GetExecutionMigrationTopic(Environment.MachineName);
            await _natsService.SubscribeAsync<ExecutionMigrationMessage>(
                migrationTopic, OnExecutionMigrationReceived);

            // 订阅容量查询消息
            var capacityQueryTopic = _topicService.GetCapacityQueryTopic();
            await _natsService.SubscribeAsync<CapacityQueryMessage>(
                capacityQueryTopic, OnCapacityQueryReceived);

            _logger.LogInformation("NATS subscriptions initialized successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize NATS subscriptions");
            throw;
        }
    }

    /// <summary>
    /// 处理执行请求消息
    /// </summary>
    private async Task OnExecutionRequestReceived(ExecutionRequestMessage message)
    {
        try
        {
            _logger.LogDebug("Received execution request for workflow {WorkflowId}", message.WorkflowId);

            string executionId;
            if (message.WorkflowDefinition != null)
            {
                executionId = await ExecuteWorkflowAsync(message.WorkflowDefinition, message.InputData);
            }
            else
            {
                executionId = await ExecuteWorkflowAsync(message.WorkflowId, message.InputData);
            }

            // 发送响应
            var response = new ExecutionResponseMessage
            {
                ExecutionId = executionId,
                Success = true,
                ExecutorNodeId = Environment.MachineName,
                StartedAt = DateTime.UtcNow
            };

            if (!string.IsNullOrEmpty(message.CallbackTopic))
            {
                await _natsService.PublishAsync(message.CallbackTopic, response);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to handle execution request for workflow {WorkflowId}", message.WorkflowId);

            // 发送错误响应
            if (!string.IsNullOrEmpty(message.CallbackTopic))
            {
                var errorResponse = new ExecutionResponseMessage
                {
                    Success = false,
                    ErrorMessage = ex.Message,
                    ExecutorNodeId = Environment.MachineName
                };
                await _natsService.PublishAsync(message.CallbackTopic, errorResponse);
            }
        }
    }

    /// <summary>
    /// 处理执行控制消息
    /// </summary>
    private async Task OnExecutionControlReceived(ExecutionControlMessage message)
    {
        try
        {
            _logger.LogDebug("Received execution control message: {Operation} for {ExecutionId}",
                message.Operation, message.ExecutionId);

            bool success = false;
            string? errorMessage = null;

            switch (message.Operation)
            {
                case ExecutionControlOperation.Cancel:
                    success = await CancelExecutionAsync(message.ExecutionId);
                    break;
                case ExecutionControlOperation.Pause:
                    success = await PauseExecutionAsync(message.ExecutionId);
                    break;
                case ExecutionControlOperation.Resume:
                    success = await ResumeExecutionAsync(message.ExecutionId);
                    break;
                case ExecutionControlOperation.QueryStatus:
                    success = _activeExecutions.ContainsKey(message.ExecutionId);
                    break;
                default:
                    errorMessage = $"Unsupported operation: {message.Operation}";
                    break;
            }

            // 发送响应
            var response = new ExecutionControlResponseMessage
            {
                ExecutionId = message.ExecutionId,
                Operation = message.Operation,
                Success = success,
                ErrorMessage = errorMessage,
                ExecutorNodeId = Environment.MachineName
            };

            var responseTopic = _topicService.GetExecutionControlResponseTopic();
            await _natsService.PublishAsync(responseTopic, response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to handle execution control message for {ExecutionId}", message.ExecutionId);
        }
    }

    /// <summary>
    /// 处理执行迁移消息
    /// </summary>
    private async Task OnExecutionMigrationReceived(ExecutionMigrationMessage message)
    {
        try
        {
            _logger.LogDebug("Received execution migration request for {ExecutionId}", message.ExecutionId);

            var migrationContext = new ExecutionMigrationContext
            {
                ExecutionId = message.ExecutionId,
                SourceNodeId = message.SourceNodeId,
                TargetNodeId = message.TargetNodeId,
                Reason = message.Reason,
                ContextSnapshot = message.ContextSnapshot,
                MigrationTime = DateTime.UtcNow
            };

            var success = await ReceiveMigratedExecutionAsync(migrationContext);

            // 发送响应
            var response = new ExecutionMigrationResponseMessage
            {
                ExecutionId = message.ExecutionId,
                Success = success,
                TargetNodeId = Environment.MachineName,
                CompletedAt = DateTime.UtcNow
            };

            var responseTopic = _topicService.GetExecutionMigrationResponseTopic();
            await _natsService.PublishAsync(responseTopic, response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to handle execution migration for {ExecutionId}", message.ExecutionId);
        }
    }

    /// <summary>
    /// 处理容量查询消息
    /// </summary>
    private async Task OnCapacityQueryReceived(CapacityQueryMessage message)
    {
        try
        {
            _logger.LogDebug("Received capacity query: {QueryType}", message.QueryType);

            var capacity = await GetExecutionCapacityAsync();
            var nodeCapacities = new List<ExecutionCapacity> { capacity };

            var clusterCapacity = new ClusterCapacity
            {
                TotalNodes = 1,
                HealthyNodes = 1,
                TotalSlots = capacity.MaxConcurrentExecutions,
                AvailableSlots = capacity.AvailableSlots,
                CurrentExecutions = capacity.CurrentExecutions,
                AverageLoadScore = capacity.LoadScore,
                HealthStatus = capacity.CanAcceptExecution ? ClusterHealthStatus.Healthy : ClusterHealthStatus.Warning
            };

            var response = new CapacityResponseMessage
            {
                NodeCapacities = nodeCapacities,
                ClusterCapacity = clusterCapacity,
                ResponseTime = DateTime.UtcNow
            };

            var responseTopic = _topicService.GetCapacityResponseTopic();
            await _natsService.PublishAsync(responseTopic, response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to handle capacity query");
        }
    }

    #endregion

    #region 辅助方法

    /// <summary>
    /// 创建执行上下文
    /// </summary>
    private WorkflowExecutionContext CreateExecutionContext(string executionId, WorkflowDefinition workflowDefinition, Dictionary<string, object>? inputData)
    {
        return new WorkflowExecutionContext
        {
            ExecutionId = executionId,
            WorkflowId = workflowDefinition.WorkflowId,
            Definition = workflowDefinition,
            State = WorkflowExecutionState.Running,
            StartedAt = DateTime.UtcNow,
            InputData = new ConcurrentDictionary<string, object>(inputData ?? new Dictionary<string, object>())
        };
    }

    /// <summary>
    /// 估算资源使用量
    /// </summary>
    private ResourceUsage EstimateResourceUsage(WorkflowDefinition workflowDefinition)
    {
        // 简单的资源估算逻辑，实际项目中可以更复杂
        var nodeCount = workflowDefinition.Nodes?.Count ?? 1;

        return new ResourceUsage
        {
            CpuCores = Math.Min(nodeCount * 0.1, 1.0), // 每个节点0.1核心，最多1核心
            MemoryMB = nodeCount * 50, // 每个节点50MB内存
            DiskMB = nodeCount * 10, // 每个节点10MB磁盘
            NetworkMbps = 1.0, // 1Mbps网络
            EstimatedDurationMs = nodeCount * 1000, // 每个节点1秒
            Weight = 1.0
        };
    }

    /// <summary>
    /// 估算数据大小
    /// </summary>
    private long EstimateDataSize(ConcurrentDictionary<string, object> data)
    {
        // 简单估算，实际项目中可以使用序列化来精确计算
        return data.Count * 100; // 每个键值对100字节
    }

    /// <summary>
    /// 创建部分结果
    /// </summary>
    private WorkflowExecutionResult CreatePartialResult(WorkflowExecutionContext context)
    {
        return new WorkflowExecutionResult
        {
            ExecutionId = context.ExecutionId,
            WorkflowId = context.WorkflowId,
            State = context.State,
            IsSuccess = false,
            StartedAt = context.StartedAt,
            OutputData = new Dictionary<string, object>(context.OutputData),
            NodeResults = new List<NodeExecutionResult>(context.NodeResults.Values),
            Stats = context.Stats
        };
    }

    /// <summary>
    /// 创建上下文快照
    /// </summary>
    private ExecutionContextSnapshot CreateContextSnapshot(WorkflowExecutionContext context)
    {
        return new ExecutionContextSnapshot
        {
            ExecutionId = context.ExecutionId,
            WorkflowId = context.WorkflowId,
            SnapshotTime = DateTime.UtcNow,
            State = context.State,
            WorkflowDefinition = context.Definition,
            InputData = new Dictionary<string, object>(context.InputData),
            WorkflowData = new Dictionary<string, object>(context.WorkflowData),
            NodeStates = new Dictionary<string, NodeExecutionState>(context.NodeStates),
            NodeResults = new Dictionary<string, NodeExecutionResult>(context.NodeResults),
            Stats = context.Stats,
            Metadata = new Dictionary<string, object>(context.Metadata)
        };
    }

    /// <summary>
    /// 恢复执行上下文
    /// </summary>
    private WorkflowExecutionContext RestoreExecutionContext(ExecutionContextSnapshot snapshot)
    {
        return new WorkflowExecutionContext
        {
            ExecutionId = snapshot.ExecutionId,
            WorkflowId = snapshot.WorkflowId,
            Definition = snapshot.WorkflowDefinition,
            State = snapshot.State,
            StartedAt = snapshot.SnapshotTime,
            InputData = new ConcurrentDictionary<string, object>(snapshot.InputData),
            WorkflowData = new ConcurrentDictionary<string, object>(snapshot.WorkflowData),
            NodeStates = new ConcurrentDictionary<string, NodeExecutionState>(snapshot.NodeStates),
            NodeResults = new ConcurrentDictionary<string, NodeExecutionResult>(snapshot.NodeResults),
            Stats = snapshot.Stats,
            Metadata = new Dictionary<string, object>(snapshot.Metadata)
        };
    }

    /// <summary>
    /// 内部执行工作流
    /// </summary>
    private async Task ExecuteWorkflowInternalAsync(WorkflowExecutionContext context, CancellationToken cancellationToken)
    {
        var stopwatch = Stopwatch.StartNew();

        try
        {
            _logger.LogDebug("Starting internal workflow execution {ExecutionId}", context.ExecutionId);

            // 执行工作流
            var result = await _workflowScheduler.ScheduleWorkflowAsync(context.Definition,
                new Dictionary<string, object>(context.InputData), cancellationToken);

            // 更新统计
            if (result.IsSuccess)
            {
                Interlocked.Increment(ref _successfulExecutions);
            }
            else
            {
                Interlocked.Increment(ref _failedExecutions);
            }

            // 同步执行结果
            await _stateSyncService.SyncExecutionResultAsync(result, cancellationToken);

            // 持久化结果
            await _executionRepository.SaveExecutionResultAsync(result, cancellationToken);

            // 完成执行任务
            if (_executionTasks.TryGetValue(context.ExecutionId, out var tcs))
            {
                tcs.SetResult(result);
            }

            // 触发完成事件
            OnExecutionCompleted(new ExecutionCompletedEventArgs
            {
                ExecutionId = context.ExecutionId,
                WorkflowId = context.WorkflowId,
                Result = result,
                CompletedAt = DateTime.UtcNow,
                Duration = stopwatch.Elapsed,
                IsSuccess = result.IsSuccess
            });

            _logger.LogInformation("Workflow execution {ExecutionId} completed successfully in {Duration}ms",
                context.ExecutionId, stopwatch.ElapsedMilliseconds);
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("Workflow execution {ExecutionId} was cancelled", context.ExecutionId);

            // 完成执行任务
            if (_executionTasks.TryGetValue(context.ExecutionId, out var tcs))
            {
                tcs.SetCanceled();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Workflow execution {ExecutionId} failed", context.ExecutionId);

            Interlocked.Increment(ref _failedExecutions);

            // 触发失败事件
            OnExecutionFailed(new ExecutionFailedEventArgs
            {
                ExecutionId = context.ExecutionId,
                WorkflowId = context.WorkflowId,
                FailedAt = DateTime.UtcNow,
                ErrorMessage = ex.Message,
                Exception = ex,
                CanRetry = true
            });

            // 完成执行任务
            if (_executionTasks.TryGetValue(context.ExecutionId, out var tcs))
            {
                tcs.SetException(ex);
            }
        }
        finally
        {
            // 清理资源
            CleanupExecution(context.ExecutionId);
        }
    }

    /// <summary>
    /// 清理执行资源
    /// </summary>
    private void CleanupExecution(string executionId)
    {
        try
        {
            // 移除活跃执行
            _activeExecutions.TryRemove(executionId, out _);

            // 释放取消令牌
            if (_executionCancellationTokens.TryRemove(executionId, out var cts))
            {
                cts.Dispose();
            }

            // 移除执行任务
            _executionTasks.TryRemove(executionId, out _);

            // 释放资源
            _capacityManager.ReleaseResources(executionId);

            _logger.LogDebug("Execution {ExecutionId} resources cleaned up", executionId);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to cleanup execution {ExecutionId} resources", executionId);
        }
    }

    #endregion

    #region IDisposable

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            try
            {
                // 停止服务
                StopAsync(CancellationToken.None).GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while disposing WorkflowExecutorService");
            }

            _disposed = true;
        }
    }

    #endregion
}
