using FlowCustomV1.Core.Interfaces.Messaging;
using FlowCustomV1.Core.Interfaces;
using FlowCustomV1.Core.Interfaces.Services;
using FlowCustomV1.Core.Models.Designer;
using FlowCustomV1.Core.Models.Messages;
using FlowCustomV1.Core.Models.Workflow;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;

namespace FlowCustomV1.Infrastructure.Services.Designer;

/// <summary>
/// 工作流设计服务实现
/// 基于NATS消息系统的分布式工作流设计服务
/// </summary>
public class WorkflowDesignerService : IWorkflowDesignerService, IDisposable
{
    private readonly INatsService _natsService;
    private readonly IWorkflowRepository _workflowRepository;
    private readonly IMessageTopicService _topicService;
    private readonly ILogger<WorkflowDesignerService> _logger;
    
    // 协作会话管理
    private readonly ConcurrentDictionary<string, List<CollaboratorInfo>> _collaborationSessions = new();
    private readonly ConcurrentDictionary<string, List<WorkflowTemplate>> _templateCache = new();
    private readonly ConcurrentDictionary<string, TaskCompletionSource<object>> _pendingRequests = new();
    
    private bool _disposed = false;

    /// <summary>
    /// 构造函数
    /// </summary>
    public WorkflowDesignerService(
        INatsService natsService,
        IWorkflowRepository workflowRepository,
        IMessageTopicService topicService,
        ILogger<WorkflowDesignerService> logger)
    {
        _natsService = natsService ?? throw new ArgumentNullException(nameof(natsService));
        _workflowRepository = workflowRepository ?? throw new ArgumentNullException(nameof(workflowRepository));
        _topicService = topicService ?? throw new ArgumentNullException(nameof(topicService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        // 初始化消息订阅
        _ = Task.Run(InitializeMessageSubscriptionsAsync);
    }

    #region 工作流设计

    /// <inheritdoc />
    public async Task<WorkflowDefinition> CreateWorkflowAsync(WorkflowTemplate template, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(template);

        try
        {
            _logger.LogInformation("Creating workflow from template {TemplateId}", template.TemplateId);

            // 从模板创建工作流定义
            var workflow = template.CreateWorkflowDefinition(template.Name, template.Description);
            
            // 通过NATS发送创建请求
            var message = new WorkflowCrudMessage
            {
                SenderId = Environment.MachineName,
                Operation = CrudOperation.Create,
                WorkflowDefinition = workflow,
                OperatorId = "system" // TODO: 从上下文获取用户ID
            };

            var response = await SendWorkflowCrudRequestAsync(message, cancellationToken);
            
            if (!response.Success)
            {
                throw new InvalidOperationException($"Failed to create workflow: {response.ErrorMessage}");
            }

            // 触发工作流创建事件
            OnWorkflowCreated(new WorkflowCreatedEventArgs
            {
                Workflow = workflow,
                CreatedBy = message.OperatorId,
                CreatedAt = DateTime.UtcNow,
                TemplateId = template.TemplateId
            });

            _logger.LogInformation("Workflow {WorkflowId} created successfully from template {TemplateId}", 
                workflow.WorkflowId, template.TemplateId);

            return workflow;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create workflow from template {TemplateId}", template.TemplateId);
            throw;
        }
    }

    /// <inheritdoc />
    public async Task<bool> UpdateWorkflowAsync(string workflowId, WorkflowDefinition workflow, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(workflowId);
        ArgumentNullException.ThrowIfNull(workflow);

        try
        {
            _logger.LogInformation("Updating workflow {WorkflowId}", workflowId);

            // 获取当前工作流定义（用于变更比较）
            var currentWorkflow = await GetWorkflowAsync(workflowId, cancellationToken);

            // 更新时间戳
            workflow.LastModifiedAt = DateTime.UtcNow;

            // 通过NATS发送更新请求
            var message = new WorkflowCrudMessage
            {
                SenderId = Environment.MachineName,
                Operation = CrudOperation.Update,
                WorkflowId = workflowId,
                WorkflowDefinition = workflow,
                OperatorId = "system" // TODO: 从上下文获取用户ID
            };

            var response = await SendWorkflowCrudRequestAsync(message, cancellationToken);
            
            if (!response.Success)
            {
                _logger.LogWarning("Failed to update workflow {WorkflowId}: {Error}", workflowId, response.ErrorMessage);
                return false;
            }

            // 触发工作流更新事件
            OnWorkflowUpdated(new WorkflowUpdatedEventArgs
            {
                WorkflowId = workflowId,
                Workflow = workflow,
                PreviousWorkflow = currentWorkflow,
                UpdatedBy = message.OperatorId,
                UpdatedAt = DateTime.UtcNow,
                Changes = new List<VersionChange>() // TODO: 实现变更检测
            });

            _logger.LogInformation("Workflow {WorkflowId} updated successfully", workflowId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update workflow {WorkflowId}", workflowId);
            return false;
        }
    }

    /// <inheritdoc />
    public async Task<WorkflowDefinition?> GetWorkflowAsync(string workflowId, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(workflowId);

        try
        {
            _logger.LogDebug("Getting workflow {WorkflowId}", workflowId);

            // 通过NATS发送查询请求
            var message = new WorkflowCrudMessage
            {
                SenderId = Environment.MachineName,
                Operation = CrudOperation.Read,
                WorkflowId = workflowId,
                OperatorId = "system"
            };

            var response = await SendWorkflowCrudRequestAsync(message, cancellationToken);
            
            if (!response.Success)
            {
                _logger.LogWarning("Failed to get workflow {WorkflowId}: {Error}", workflowId, response.ErrorMessage);
                return null;
            }

            return response.Workflow;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get workflow {WorkflowId}", workflowId);
            return null;
        }
    }

    /// <inheritdoc />
    public async Task<bool> DeleteWorkflowAsync(string workflowId, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(workflowId);

        try
        {
            _logger.LogInformation("Deleting workflow {WorkflowId}", workflowId);

            // 获取工作流定义（用于事件）
            var workflow = await GetWorkflowAsync(workflowId, cancellationToken);

            // 通过NATS发送删除请求
            var message = new WorkflowCrudMessage
            {
                SenderId = Environment.MachineName,
                Operation = CrudOperation.Delete,
                WorkflowId = workflowId,
                OperatorId = "system"
            };

            var response = await SendWorkflowCrudRequestAsync(message, cancellationToken);
            
            if (!response.Success)
            {
                _logger.LogWarning("Failed to delete workflow {WorkflowId}: {Error}", workflowId, response.ErrorMessage);
                return false;
            }

            // 触发工作流删除事件
            if (workflow != null)
            {
                OnWorkflowDeleted(new WorkflowDeletedEventArgs
                {
                    WorkflowId = workflowId,
                    DeletedWorkflow = workflow,
                    DeletedBy = message.OperatorId,
                    DeletedAt = DateTime.UtcNow
                });
            }

            _logger.LogInformation("Workflow {WorkflowId} deleted successfully", workflowId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete workflow {WorkflowId}", workflowId);
            return false;
        }
    }

    /// <inheritdoc />
    public async Task<IReadOnlyList<WorkflowDefinition>> GetWorkflowsAsync(WorkflowQuery? query = null, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting workflows with query");

            // 通过NATS发送列表查询请求
            var message = new WorkflowCrudMessage
            {
                SenderId = Environment.MachineName,
                Operation = CrudOperation.List,
                Query = query,
                OperatorId = "system"
            };

            var response = await SendWorkflowCrudRequestAsync(message, cancellationToken);
            
            if (!response.Success)
            {
                _logger.LogWarning("Failed to get workflows: {Error}", response.ErrorMessage);
                return new List<WorkflowDefinition>();
            }

            return response.Workflows ?? new List<WorkflowDefinition>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get workflows");
            return new List<WorkflowDefinition>();
        }
    }

    #endregion

    #region 模板管理

    /// <inheritdoc />
    public async Task<IReadOnlyList<WorkflowTemplate>> GetTemplatesAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting workflow templates");

            // 检查缓存
            if (_templateCache.TryGetValue("all", out var cachedTemplates))
            {
                return cachedTemplates;
            }

            // 通过NATS发送模板查询请求
            var message = new TemplateOperationMessage
            {
                SenderId = Environment.MachineName,
                Operation = CrudOperation.List,
                OperatorId = "system"
            };

            var response = await SendTemplateOperationRequestAsync(message, cancellationToken);
            
            if (!response.Success)
            {
                _logger.LogWarning("Failed to get templates: {Error}", response.ErrorMessage);
                return new List<WorkflowTemplate>();
            }

            var templates = response.Templates ?? new List<WorkflowTemplate>();
            
            // 更新缓存
            _templateCache.TryAdd("all", templates);

            return templates;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get templates");
            return new List<WorkflowTemplate>();
        }
    }

    /// <inheritdoc />
    public async Task<bool> CreateTemplateAsync(WorkflowTemplate template, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(template);

        try
        {
            _logger.LogInformation("Creating template {TemplateId}", template.TemplateId);

            // 验证模板
            var validationResult = template.Validate();
            if (!validationResult.IsValid)
            {
                _logger.LogWarning("Template validation failed: {Errors}", string.Join(", ", validationResult.Errors));
                return false;
            }

            // 设置创建时间
            template.CreatedAt = DateTime.UtcNow;
            template.LastModifiedAt = DateTime.UtcNow;

            // 通过NATS发送创建请求
            var message = new TemplateOperationMessage
            {
                SenderId = Environment.MachineName,
                Operation = CrudOperation.Create,
                Template = template,
                OperatorId = "system"
            };

            var response = await SendTemplateOperationRequestAsync(message, cancellationToken);
            
            if (!response.Success)
            {
                _logger.LogWarning("Failed to create template {TemplateId}: {Error}", template.TemplateId, response.ErrorMessage);
                return false;
            }

            // 清除缓存
            _templateCache.TryRemove("all", out _);

            // 触发模板变更事件
            OnTemplateChanged(new TemplateChangedEventArgs
            {
                TemplateId = template.TemplateId,
                ChangeType = TemplateChangeType.Created,
                Template = template,
                ChangedBy = message.OperatorId,
                ChangedAt = DateTime.UtcNow
            });

            _logger.LogInformation("Template {TemplateId} created successfully", template.TemplateId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create template {TemplateId}", template.TemplateId);
            return false;
        }
    }

    /// <inheritdoc />
    public async Task<bool> UpdateTemplateAsync(string templateId, WorkflowTemplate template, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(templateId);
        ArgumentNullException.ThrowIfNull(template);

        try
        {
            _logger.LogInformation("Updating template {TemplateId}", templateId);

            // 验证模板
            var validationResult = template.Validate();
            if (!validationResult.IsValid)
            {
                _logger.LogWarning("Template validation failed: {Errors}", string.Join(", ", validationResult.Errors));
                return false;
            }

            // 更新时间戳
            template.LastModifiedAt = DateTime.UtcNow;

            // 通过NATS发送更新请求
            var message = new TemplateOperationMessage
            {
                SenderId = Environment.MachineName,
                Operation = CrudOperation.Update,
                TemplateId = templateId,
                Template = template,
                OperatorId = "system"
            };

            var response = await SendTemplateOperationRequestAsync(message, cancellationToken);
            
            if (!response.Success)
            {
                _logger.LogWarning("Failed to update template {TemplateId}: {Error}", templateId, response.ErrorMessage);
                return false;
            }

            // 清除缓存
            _templateCache.TryRemove("all", out _);

            // 触发模板变更事件
            OnTemplateChanged(new TemplateChangedEventArgs
            {
                TemplateId = templateId,
                ChangeType = TemplateChangeType.Updated,
                Template = template,
                ChangedBy = message.OperatorId,
                ChangedAt = DateTime.UtcNow
            });

            _logger.LogInformation("Template {TemplateId} updated successfully", templateId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update template {TemplateId}", templateId);
            return false;
        }
    }

    /// <inheritdoc />
    public async Task<bool> DeleteTemplateAsync(string templateId, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(templateId);

        try
        {
            _logger.LogInformation("Deleting template {TemplateId}", templateId);

            // 通过NATS发送删除请求
            var message = new TemplateOperationMessage
            {
                SenderId = Environment.MachineName,
                Operation = CrudOperation.Delete,
                TemplateId = templateId,
                OperatorId = "system"
            };

            var response = await SendTemplateOperationRequestAsync(message, cancellationToken);
            
            if (!response.Success)
            {
                _logger.LogWarning("Failed to delete template {TemplateId}: {Error}", templateId, response.ErrorMessage);
                return false;
            }

            // 清除缓存
            _templateCache.TryRemove("all", out _);

            _logger.LogInformation("Template {TemplateId} deleted successfully", templateId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete template {TemplateId}", templateId);
            return false;
        }
    }

    #endregion

    #region 版本控制

    /// <inheritdoc />
    public async Task<IReadOnlyList<WorkflowVersion>> GetVersionHistoryAsync(string workflowId, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(workflowId);

        try
        {
            _logger.LogDebug("Getting version history for workflow {WorkflowId}", workflowId);

            // 通过NATS发送版本历史查询请求
            var message = new VersionControlMessage
            {
                SenderId = Environment.MachineName,
                Operation = VersionOperation.GetHistory,
                WorkflowId = workflowId,
                OperatorId = "system"
            };

            var response = await SendVersionControlRequestAsync(message, cancellationToken);

            if (!response.Success)
            {
                _logger.LogWarning("Failed to get version history for workflow {WorkflowId}: {Error}", workflowId, response.ErrorMessage);
                return new List<WorkflowVersion>();
            }

            return response.Versions ?? new List<WorkflowVersion>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get version history for workflow {WorkflowId}", workflowId);
            return new List<WorkflowVersion>();
        }
    }

    /// <inheritdoc />
    public async Task<string> CreateVersionAsync(string workflowId, WorkflowVersionInfo versionInfo, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(workflowId);
        ArgumentNullException.ThrowIfNull(versionInfo);

        try
        {
            _logger.LogInformation("Creating version {Version} for workflow {WorkflowId}", versionInfo.Version, workflowId);

            // 通过NATS发送版本创建请求
            var message = new VersionControlMessage
            {
                SenderId = Environment.MachineName,
                Operation = VersionOperation.Create,
                WorkflowId = workflowId,
                VersionInfo = versionInfo,
                OperatorId = versionInfo.CreatedBy
            };

            var response = await SendVersionControlRequestAsync(message, cancellationToken);

            if (!response.Success)
            {
                _logger.LogWarning("Failed to create version for workflow {WorkflowId}: {Error}", workflowId, response.ErrorMessage);
                return string.Empty;
            }

            var versionId = response.CreatedVersionId ?? string.Empty;
            _logger.LogInformation("Version {VersionId} created successfully for workflow {WorkflowId}", versionId, workflowId);

            return versionId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create version for workflow {WorkflowId}", workflowId);
            return string.Empty;
        }
    }

    /// <inheritdoc />
    public async Task<WorkflowDefinition?> GetWorkflowVersionAsync(string workflowId, string version, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(workflowId);
        ArgumentException.ThrowIfNullOrWhiteSpace(version);

        try
        {
            _logger.LogDebug("Getting version {Version} of workflow {WorkflowId}", version, workflowId);

            // 通过NATS发送版本查询请求
            var message = new VersionControlMessage
            {
                SenderId = Environment.MachineName,
                Operation = VersionOperation.GetVersion,
                WorkflowId = workflowId,
                VersionId = version,
                OperatorId = "system"
            };

            var response = await SendVersionControlRequestAsync(message, cancellationToken);

            if (!response.Success)
            {
                _logger.LogWarning("Failed to get version {Version} of workflow {WorkflowId}: {Error}", version, workflowId, response.ErrorMessage);
                return null;
            }

            return response.Version?.Definition;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get version {Version} of workflow {WorkflowId}", version, workflowId);
            return null;
        }
    }

    #endregion

    #region 协作功能

    /// <inheritdoc />
    public async Task BroadcastDesignChangeAsync(string workflowId, DesignOperation operation, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(workflowId);
        ArgumentNullException.ThrowIfNull(operation);

        try
        {
            _logger.LogDebug("Broadcasting design change for workflow {WorkflowId}", workflowId);

            // 创建协作消息
            var message = new CollaborationMessage
            {
                SenderId = Environment.MachineName,
                WorkflowId = workflowId,
                Action = CollaborationAction.DesignChange,
                Operation = operation,
                SessionId = Guid.NewGuid().ToString()
            };

            // 发布到协作主题
            var collaborationTopic = GetCollaborationTopic(workflowId);
            await _natsService.PublishAsync(collaborationTopic, message, cancellationToken);

            _logger.LogDebug("Design change broadcasted for workflow {WorkflowId}", workflowId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to broadcast design change for workflow {WorkflowId}", workflowId);
            throw;
        }
    }

    /// <inheritdoc />
    public Task<IReadOnlyList<CollaboratorInfo>> GetActiveCollaboratorsAsync(string workflowId, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(workflowId);

        try
        {
            if (_collaborationSessions.TryGetValue(workflowId, out var collaborators))
            {
                // 过滤掉非活跃的协作者（超过5分钟未活动）
                var activeCollaborators = collaborators
                    .Where(c => DateTime.UtcNow - c.LastActiveAt < TimeSpan.FromMinutes(5))
                    .ToList();

                // 更新会话
                _collaborationSessions.TryUpdate(workflowId, activeCollaborators, collaborators);

                return Task.FromResult<IReadOnlyList<CollaboratorInfo>>(activeCollaborators);
            }

            return Task.FromResult<IReadOnlyList<CollaboratorInfo>>(new List<CollaboratorInfo>());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get active collaborators for workflow {WorkflowId}", workflowId);
            return Task.FromResult<IReadOnlyList<CollaboratorInfo>>(new List<CollaboratorInfo>());
        }
    }

    /// <inheritdoc />
    public async Task<bool> JoinCollaborationAsync(string workflowId, CollaboratorInfo collaborator, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(workflowId);
        ArgumentNullException.ThrowIfNull(collaborator);

        try
        {
            _logger.LogInformation("Collaborator {CollaboratorId} joining workflow {WorkflowId}", collaborator.CollaboratorId, workflowId);

            // 更新协作会话
            _collaborationSessions.AddOrUpdate(workflowId,
                new List<CollaboratorInfo> { collaborator },
                (key, existing) =>
                {
                    var updated = existing.Where(c => c.CollaboratorId != collaborator.CollaboratorId).ToList();
                    updated.Add(collaborator);
                    return updated;
                });

            // 广播加入事件
            var message = new CollaborationMessage
            {
                SenderId = Environment.MachineName,
                WorkflowId = workflowId,
                Action = CollaborationAction.Join,
                Collaborator = collaborator,
                SessionId = Guid.NewGuid().ToString()
            };

            var collaborationTopic = GetCollaborationTopic(workflowId);
            await _natsService.PublishAsync(collaborationTopic, message, cancellationToken);

            // 触发协作事件
            OnDesignCollaborationChanged(new DesignCollaborationEventArgs
            {
                WorkflowId = workflowId,
                Action = CollaborationAction.Join,
                Collaborator = collaborator,
                Timestamp = DateTime.UtcNow,
                ActiveCollaborators = await GetActiveCollaboratorsAsync(workflowId, cancellationToken) as List<CollaboratorInfo> ?? new()
            });

            _logger.LogInformation("Collaborator {CollaboratorId} joined workflow {WorkflowId} successfully", collaborator.CollaboratorId, workflowId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to join collaboration for workflow {WorkflowId}", workflowId);
            return false;
        }
    }

    /// <inheritdoc />
    public async Task<bool> LeaveCollaborationAsync(string workflowId, string collaboratorId, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(workflowId);
        ArgumentException.ThrowIfNullOrWhiteSpace(collaboratorId);

        try
        {
            _logger.LogInformation("Collaborator {CollaboratorId} leaving workflow {WorkflowId}", collaboratorId, workflowId);

            CollaboratorInfo? leavingCollaborator = null;

            // 更新协作会话
            _collaborationSessions.AddOrUpdate(workflowId,
                new List<CollaboratorInfo>(),
                (key, existing) =>
                {
                    leavingCollaborator = existing.FirstOrDefault(c => c.CollaboratorId == collaboratorId);
                    return existing.Where(c => c.CollaboratorId != collaboratorId).ToList();
                });

            if (leavingCollaborator != null)
            {
                // 广播离开事件
                var message = new CollaborationMessage
                {
                    SenderId = Environment.MachineName,
                    WorkflowId = workflowId,
                    Action = CollaborationAction.Leave,
                    Collaborator = leavingCollaborator,
                    SessionId = Guid.NewGuid().ToString()
                };

                var collaborationTopic = GetCollaborationTopic(workflowId);
                await _natsService.PublishAsync(collaborationTopic, message, cancellationToken);

                // 触发协作事件
                OnDesignCollaborationChanged(new DesignCollaborationEventArgs
                {
                    WorkflowId = workflowId,
                    Action = CollaborationAction.Leave,
                    Collaborator = leavingCollaborator,
                    Timestamp = DateTime.UtcNow,
                    ActiveCollaborators = await GetActiveCollaboratorsAsync(workflowId, cancellationToken) as List<CollaboratorInfo> ?? new()
                });
            }

            _logger.LogInformation("Collaborator {CollaboratorId} left workflow {WorkflowId} successfully", collaboratorId, workflowId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to leave collaboration for workflow {WorkflowId}", workflowId);
            return false;
        }
    }

    /// <inheritdoc />
    public Task<bool> ResolveDesignConflictAsync(string workflowId, ConflictResolution conflictResolution, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(workflowId);
        ArgumentNullException.ThrowIfNull(conflictResolution);

        try
        {
            _logger.LogInformation("Resolving design conflict {ConflictId} for workflow {WorkflowId}",
                conflictResolution.ConflictId, workflowId);

            // TODO: 实现冲突解决逻辑
            // 这里需要根据解决策略处理冲突
            switch (conflictResolution.Strategy)
            {
                case ConflictResolutionStrategy.AcceptCurrent:
                    // 保持当前版本，忽略冲突变更
                    break;
                case ConflictResolutionStrategy.AcceptIncoming:
                    // 接受传入的变更
                    break;
                case ConflictResolutionStrategy.ManualMerge:
                    // 使用手动合并的数据
                    if (conflictResolution.MergedData != null)
                    {
                        // 应用合并后的数据
                    }
                    break;
                case ConflictResolutionStrategy.CreateBranch:
                    // 创建新分支
                    break;
            }

            _logger.LogInformation("Design conflict {ConflictId} resolved successfully", conflictResolution.ConflictId);
            return Task.FromResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to resolve design conflict {ConflictId} for workflow {WorkflowId}",
                conflictResolution.ConflictId, workflowId);
            return Task.FromResult(false);
        }
    }

    #endregion

    #region 事件

    /// <inheritdoc />
    public event EventHandler<WorkflowCreatedEventArgs>? WorkflowCreated;

    /// <inheritdoc />
    public event EventHandler<WorkflowUpdatedEventArgs>? WorkflowUpdated;

    /// <inheritdoc />
    public event EventHandler<WorkflowDeletedEventArgs>? WorkflowDeleted;

    /// <inheritdoc />
    public event EventHandler<DesignCollaborationEventArgs>? DesignCollaborationChanged;

    /// <inheritdoc />
    public event EventHandler<TemplateChangedEventArgs>? TemplateChanged;

    /// <summary>
    /// 触发工作流创建事件
    /// </summary>
    protected virtual void OnWorkflowCreated(WorkflowCreatedEventArgs e)
    {
        WorkflowCreated?.Invoke(this, e);
    }

    /// <summary>
    /// 触发工作流更新事件
    /// </summary>
    protected virtual void OnWorkflowUpdated(WorkflowUpdatedEventArgs e)
    {
        WorkflowUpdated?.Invoke(this, e);
    }

    /// <summary>
    /// 触发工作流删除事件
    /// </summary>
    protected virtual void OnWorkflowDeleted(WorkflowDeletedEventArgs e)
    {
        WorkflowDeleted?.Invoke(this, e);
    }

    /// <summary>
    /// 触发设计协作事件
    /// </summary>
    protected virtual void OnDesignCollaborationChanged(DesignCollaborationEventArgs e)
    {
        DesignCollaborationChanged?.Invoke(this, e);
    }

    /// <summary>
    /// 触发模板变更事件
    /// </summary>
    protected virtual void OnTemplateChanged(TemplateChangedEventArgs e)
    {
        TemplateChanged?.Invoke(this, e);
    }

    #endregion

    #region 私有辅助方法

    /// <summary>
    /// 初始化消息订阅
    /// </summary>
    private async Task InitializeMessageSubscriptionsAsync()
    {
        try
        {
            _logger.LogInformation("Initializing NATS message subscriptions for WorkflowDesignerService");

            // 确保NATS连接
            if (!_natsService.IsConnected)
            {
                await _natsService.ConnectAsync();
            }

            // 订阅工作流CRUD响应
            var workflowResponseTopic = GetWorkflowCrudResponseTopic();
            await _natsService.SubscribeAsync<WorkflowCrudResponseMessage>(
                workflowResponseTopic,
                OnWorkflowCrudResponseReceived);

            // 订阅模板操作响应
            var templateResponseTopic = GetTemplateOperationResponseTopic();
            await _natsService.SubscribeAsync<TemplateOperationResponseMessage>(
                templateResponseTopic,
                OnTemplateOperationResponseReceived);

            // 订阅版本控制响应
            var versionResponseTopic = GetVersionControlResponseTopic();
            await _natsService.SubscribeAsync<VersionControlResponseMessage>(
                versionResponseTopic,
                OnVersionControlResponseReceived);

            // 订阅协作消息（通配符订阅所有工作流的协作消息）
            var collaborationPattern = GetCollaborationTopicPattern();
            await _natsService.SubscribeAsync<CollaborationMessage>(
                collaborationPattern,
                OnCollaborationMessageReceived);

            _logger.LogInformation("NATS message subscriptions initialized successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize NATS message subscriptions");
            throw;
        }
    }

    /// <summary>
    /// 发送工作流CRUD请求并等待响应
    /// </summary>
    private async Task<WorkflowCrudResponseMessage> SendWorkflowCrudRequestAsync(WorkflowCrudMessage request, CancellationToken cancellationToken)
    {
        var tcs = new TaskCompletionSource<object>();
        _pendingRequests.TryAdd(request.RequestId, tcs);

        try
        {
            var topic = GetWorkflowCrudTopic();
            await _natsService.PublishAsync(topic, request, cancellationToken);

            // 等待响应（超时10秒）
            using var timeoutCts = new CancellationTokenSource(TimeSpan.FromSeconds(10));
            using var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, timeoutCts.Token);

            var result = await tcs.Task.WaitAsync(combinedCts.Token);
            return (WorkflowCrudResponseMessage)result;
        }
        catch (OperationCanceledException)
        {
            return new WorkflowCrudResponseMessage
            {
                RequestId = request.RequestId,
                Success = false,
                ErrorMessage = "Request timeout"
            };
        }
        finally
        {
            _pendingRequests.TryRemove(request.RequestId, out _);
        }
    }

    /// <summary>
    /// 发送模板操作请求并等待响应
    /// </summary>
    private async Task<TemplateOperationResponseMessage> SendTemplateOperationRequestAsync(TemplateOperationMessage request, CancellationToken cancellationToken)
    {
        var tcs = new TaskCompletionSource<object>();
        _pendingRequests.TryAdd(request.RequestId, tcs);

        try
        {
            var topic = GetTemplateOperationTopic();
            await _natsService.PublishAsync(topic, request, cancellationToken);

            // 等待响应（超时10秒）
            using var timeoutCts = new CancellationTokenSource(TimeSpan.FromSeconds(10));
            using var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, timeoutCts.Token);

            var result = await tcs.Task.WaitAsync(combinedCts.Token);
            return (TemplateOperationResponseMessage)result;
        }
        catch (OperationCanceledException)
        {
            return new TemplateOperationResponseMessage
            {
                RequestId = request.RequestId,
                Success = false,
                ErrorMessage = "Request timeout"
            };
        }
        finally
        {
            _pendingRequests.TryRemove(request.RequestId, out _);
        }
    }

    /// <summary>
    /// 发送版本控制请求并等待响应
    /// </summary>
    private async Task<VersionControlResponseMessage> SendVersionControlRequestAsync(VersionControlMessage request, CancellationToken cancellationToken)
    {
        var tcs = new TaskCompletionSource<object>();
        _pendingRequests.TryAdd(request.RequestId, tcs);

        try
        {
            var topic = GetVersionControlTopic();
            await _natsService.PublishAsync(topic, request, cancellationToken);

            // 等待响应（超时10秒）
            using var timeoutCts = new CancellationTokenSource(TimeSpan.FromSeconds(10));
            using var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, timeoutCts.Token);

            var result = await tcs.Task.WaitAsync(combinedCts.Token);
            return (VersionControlResponseMessage)result;
        }
        catch (OperationCanceledException)
        {
            return new VersionControlResponseMessage
            {
                RequestId = request.RequestId,
                Success = false,
                ErrorMessage = "Request timeout"
            };
        }
        finally
        {
            _pendingRequests.TryRemove(request.RequestId, out _);
        }
    }

    #endregion

    #region 消息处理方法

    /// <summary>
    /// 处理工作流CRUD响应消息
    /// </summary>
    private async Task OnWorkflowCrudResponseReceived(WorkflowCrudResponseMessage response)
    {
        try
        {
            if (_pendingRequests.TryGetValue(response.RequestId, out var tcs))
            {
                tcs.SetResult(response);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to handle workflow CRUD response");
        }

        await Task.CompletedTask;
    }

    /// <summary>
    /// 处理模板操作响应消息
    /// </summary>
    private async Task OnTemplateOperationResponseReceived(TemplateOperationResponseMessage response)
    {
        try
        {
            if (_pendingRequests.TryGetValue(response.RequestId, out var tcs))
            {
                tcs.SetResult(response);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to handle template operation response");
        }

        await Task.CompletedTask;
    }

    /// <summary>
    /// 处理版本控制响应消息
    /// </summary>
    private async Task OnVersionControlResponseReceived(VersionControlResponseMessage response)
    {
        try
        {
            if (_pendingRequests.TryGetValue(response.RequestId, out var tcs))
            {
                tcs.SetResult(response);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to handle version control response");
        }

        await Task.CompletedTask;
    }

    /// <summary>
    /// 处理协作消息
    /// </summary>
    private async Task OnCollaborationMessageReceived(CollaborationMessage message)
    {
        try
        {
            _logger.LogDebug("Received collaboration message for workflow {WorkflowId}", message.WorkflowId);

            // 更新协作者状态
            if (message.Collaborator != null)
            {
                message.Collaborator.LastActiveAt = DateTime.UtcNow;

                switch (message.Action)
                {
                    case CollaborationAction.Join:
                        await HandleCollaboratorJoin(message.WorkflowId, message.Collaborator);
                        break;
                    case CollaborationAction.Leave:
                        await HandleCollaboratorLeave(message.WorkflowId, message.Collaborator.CollaboratorId);
                        break;
                    case CollaborationAction.DesignChange:
                        await HandleDesignChange(message.WorkflowId, message.Operation);
                        break;
                    case CollaborationAction.StatusUpdate:
                        await HandleStatusUpdate(message.WorkflowId, message.Collaborator);
                        break;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to handle collaboration message");
        }
    }

    /// <summary>
    /// 处理协作者加入
    /// </summary>
    private async Task HandleCollaboratorJoin(string workflowId, CollaboratorInfo collaborator)
    {
        // 更新本地协作会话
        _collaborationSessions.AddOrUpdate(workflowId,
            new List<CollaboratorInfo> { collaborator },
            (key, existing) =>
            {
                var updated = existing.Where(c => c.CollaboratorId != collaborator.CollaboratorId).ToList();
                updated.Add(collaborator);
                return updated;
            });

        _logger.LogInformation("Collaborator {CollaboratorId} joined workflow {WorkflowId}",
            collaborator.CollaboratorId, workflowId);

        await Task.CompletedTask;
    }

    /// <summary>
    /// 处理协作者离开
    /// </summary>
    private async Task HandleCollaboratorLeave(string workflowId, string collaboratorId)
    {
        // 更新本地协作会话
        _collaborationSessions.AddOrUpdate(workflowId,
            new List<CollaboratorInfo>(),
            (key, existing) => existing.Where(c => c.CollaboratorId != collaboratorId).ToList());

        _logger.LogInformation("Collaborator {CollaboratorId} left workflow {WorkflowId}",
            collaboratorId, workflowId);

        await Task.CompletedTask;
    }

    /// <summary>
    /// 处理设计变更
    /// </summary>
    private async Task HandleDesignChange(string workflowId, DesignOperation? operation)
    {
        if (operation == null) return;

        _logger.LogDebug("Handling design change {OperationType} for workflow {WorkflowId}",
            operation.Type, workflowId);

        // 触发设计协作事件
        OnDesignCollaborationChanged(new DesignCollaborationEventArgs
        {
            WorkflowId = workflowId,
            Action = CollaborationAction.DesignChange,
            Operation = operation,
            Timestamp = DateTime.UtcNow,
            ActiveCollaborators = await GetActiveCollaboratorsAsync(workflowId) as List<CollaboratorInfo> ?? new()
        });
    }

    /// <summary>
    /// 处理状态更新
    /// </summary>
    private async Task HandleStatusUpdate(string workflowId, CollaboratorInfo collaborator)
    {
        // 更新协作者状态
        if (_collaborationSessions.TryGetValue(workflowId, out var collaborators))
        {
            var existing = collaborators.FirstOrDefault(c => c.CollaboratorId == collaborator.CollaboratorId);
            if (existing != null)
            {
                existing.Status = collaborator.Status;
                existing.LastActiveAt = collaborator.LastActiveAt;
                existing.CursorPosition = collaborator.CursorPosition;
                existing.CurrentSelection = collaborator.CurrentSelection;
            }
        }

        _logger.LogDebug("Updated status for collaborator {CollaboratorId} in workflow {WorkflowId}",
            collaborator.CollaboratorId, workflowId);

        await Task.CompletedTask;
    }

    #endregion

    #region 主题生成方法

    /// <summary>
    /// 获取工作流CRUD主题
    /// </summary>
    private string GetWorkflowCrudTopic()
    {
        return "flowcustom.designer.workflows.crud";
    }

    /// <summary>
    /// 获取工作流CRUD响应主题
    /// </summary>
    private string GetWorkflowCrudResponseTopic()
    {
        return "flowcustom.designer.workflows.crud.response";
    }

    /// <summary>
    /// 获取模板操作主题
    /// </summary>
    private string GetTemplateOperationTopic()
    {
        return "flowcustom.designer.templates.operation";
    }

    /// <summary>
    /// 获取模板操作响应主题
    /// </summary>
    private string GetTemplateOperationResponseTopic()
    {
        return "flowcustom.designer.templates.operation.response";
    }

    /// <summary>
    /// 获取版本控制主题
    /// </summary>
    private string GetVersionControlTopic()
    {
        return "flowcustom.designer.versions.control";
    }

    /// <summary>
    /// 获取版本控制响应主题
    /// </summary>
    private string GetVersionControlResponseTopic()
    {
        return "flowcustom.designer.versions.control.response";
    }

    /// <summary>
    /// 获取协作主题
    /// </summary>
    private string GetCollaborationTopic(string workflowId)
    {
        return $"flowcustom.designer.collaboration.{workflowId}";
    }

    /// <summary>
    /// 获取协作主题模式（用于订阅）
    /// </summary>
    private string GetCollaborationTopicPattern()
    {
        return "flowcustom.designer.collaboration.*";
    }

    #endregion

    #region IDisposable实现

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed && disposing)
        {
            try
            {
                // 清理协作会话
                _collaborationSessions.Clear();

                // 清理模板缓存
                _templateCache.Clear();

                // 清理待处理请求
                foreach (var kvp in _pendingRequests)
                {
                    kvp.Value.TrySetCanceled();
                }
                _pendingRequests.Clear();

                _logger.LogInformation("WorkflowDesignerService disposed successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while disposing WorkflowDesignerService");
            }
            finally
            {
                _disposed = true;
            }
        }
    }

    /// <summary>
    /// 析构函数
    /// </summary>
    ~WorkflowDesignerService()
    {
        Dispose(false);
    }

    #endregion
}
