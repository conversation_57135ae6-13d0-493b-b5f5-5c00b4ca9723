namespace FlowCustomV1.Core.Interfaces.Services;

/// <summary>
/// 错误处理服务接口
/// </summary>
public interface IErrorHandlingService
{
    /// <summary>
    /// 处理错误
    /// </summary>
    /// <param name="error">错误信息</param>
    /// <param name="context">错误上下文</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理结果</returns>
    Task<ErrorHandlingResult> HandleErrorAsync(Exception error, ErrorContext context, CancellationToken cancellationToken = default);

    /// <summary>
    /// 记录错误
    /// </summary>
    /// <param name="error">错误信息</param>
    /// <param name="context">错误上下文</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>记录结果</returns>
    Task<bool> LogErrorAsync(Exception error, ErrorContext context, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取错误统计
    /// </summary>
    /// <param name="timeRange">时间范围</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>错误统计</returns>
    Task<ErrorStatistics> GetErrorStatisticsAsync(TimeSpan? timeRange = null, CancellationToken cancellationToken = default);
}

/// <summary>
/// 错误分类服务接口
/// </summary>
public interface IErrorClassificationService
{
    /// <summary>
    /// 分类错误
    /// </summary>
    /// <param name="error">错误信息</param>
    /// <param name="context">错误上下文</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>错误分类结果</returns>
    Task<ErrorClassification> ClassifyErrorAsync(Exception error, ErrorContext context, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取错误模式
    /// </summary>
    /// <param name="timeRange">时间范围</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>错误模式列表</returns>
    Task<IReadOnlyList<ErrorPattern>> GetErrorPatternsAsync(TimeSpan? timeRange = null, CancellationToken cancellationToken = default);
}

/// <summary>
/// 恢复服务接口
/// </summary>
public interface IRecoveryService
{
    /// <summary>
    /// 尝试恢复
    /// </summary>
    /// <param name="recoveryContext">恢复上下文</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>恢复结果</returns>
    Task<RecoveryResult> TryRecoverAsync(RecoveryContext recoveryContext, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取恢复策略
    /// </summary>
    /// <param name="errorType">错误类型</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>恢复策略</returns>
    Task<RecoveryStrategy?> GetRecoveryStrategyAsync(string errorType, CancellationToken cancellationToken = default);
}

/// <summary>
/// 错误上下文
/// </summary>
public class ErrorContext
{
    /// <summary>
    /// 工作流ID
    /// </summary>
    public string? WorkflowId { get; set; }

    /// <summary>
    /// 执行ID
    /// </summary>
    public string? ExecutionId { get; set; }

    /// <summary>
    /// 节点ID
    /// </summary>
    public string? NodeId { get; set; }

    /// <summary>
    /// 错误发生时间
    /// </summary>
    public DateTime OccurredAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 附加信息
    /// </summary>
    public Dictionary<string, object> AdditionalInfo { get; set; } = new();
}

/// <summary>
/// 错误处理结果
/// </summary>
public class ErrorHandlingResult
{
    /// <summary>
    /// 是否处理成功
    /// </summary>
    public bool IsHandled { get; set; }

    /// <summary>
    /// 处理动作
    /// </summary>
    public ErrorHandlingAction Action { get; set; }

    /// <summary>
    /// 处理消息
    /// </summary>
    public string? Message { get; set; }

    /// <summary>
    /// 建议的恢复策略
    /// </summary>
    public RecoveryStrategy? SuggestedRecovery { get; set; }
}

/// <summary>
/// 错误处理动作
/// </summary>
public enum ErrorHandlingAction
{
    /// <summary>
    /// 忽略错误
    /// </summary>
    Ignore,

    /// <summary>
    /// 重试操作
    /// </summary>
    Retry,

    /// <summary>
    /// 停止执行
    /// </summary>
    Stop,

    /// <summary>
    /// 回滚操作
    /// </summary>
    Rollback,

    /// <summary>
    /// 转移到错误处理节点
    /// </summary>
    TransferToErrorHandler
}

/// <summary>
/// 错误分类
/// </summary>
public class ErrorClassification
{
    /// <summary>
    /// 错误类型
    /// </summary>
    public string ErrorType { get; set; } = string.Empty;

    /// <summary>
    /// 严重程度
    /// </summary>
    public ErrorSeverity Severity { get; set; }

    /// <summary>
    /// 是否可恢复
    /// </summary>
    public bool IsRecoverable { get; set; }

    /// <summary>
    /// 分类置信度
    /// </summary>
    public double Confidence { get; set; }

    /// <summary>
    /// 分类原因
    /// </summary>
    public string? Reason { get; set; }
}

/// <summary>
/// 错误严重程度
/// </summary>
public enum ErrorSeverity
{
    /// <summary>
    /// 低
    /// </summary>
    Low,

    /// <summary>
    /// 中等
    /// </summary>
    Medium,

    /// <summary>
    /// 高
    /// </summary>
    High,

    /// <summary>
    /// 严重
    /// </summary>
    Critical
}

/// <summary>
/// 错误模式
/// </summary>
public class ErrorPattern
{
    /// <summary>
    /// 模式ID
    /// </summary>
    public string PatternId { get; set; } = string.Empty;

    /// <summary>
    /// 错误类型
    /// </summary>
    public string ErrorType { get; set; } = string.Empty;

    /// <summary>
    /// 发生频率
    /// </summary>
    public int Frequency { get; set; }

    /// <summary>
    /// 模式描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 建议的解决方案
    /// </summary>
    public string? SuggestedSolution { get; set; }
}

/// <summary>
/// 恢复上下文
/// </summary>
public class RecoveryContext
{
    /// <summary>
    /// 错误信息
    /// </summary>
    public Exception Error { get; set; } = null!;

    /// <summary>
    /// 错误上下文
    /// </summary>
    public ErrorContext ErrorContext { get; set; } = new();

    /// <summary>
    /// 恢复尝试次数
    /// </summary>
    public int AttemptCount { get; set; }

    /// <summary>
    /// 最大重试次数
    /// </summary>
    public int MaxRetries { get; set; } = 3;
}

/// <summary>
/// 恢复结果
/// </summary>
public class RecoveryResult
{
    /// <summary>
    /// 是否恢复成功
    /// </summary>
    public bool IsSuccessful { get; set; }

    /// <summary>
    /// 恢复动作
    /// </summary>
    public RecoveryAction Action { get; set; }

    /// <summary>
    /// 恢复消息
    /// </summary>
    public string? Message { get; set; }

    /// <summary>
    /// 下次重试延迟
    /// </summary>
    public TimeSpan? NextRetryDelay { get; set; }
}

/// <summary>
/// 恢复动作
/// </summary>
public enum RecoveryAction
{
    /// <summary>
    /// 重试
    /// </summary>
    Retry,

    /// <summary>
    /// 跳过
    /// </summary>
    Skip,

    /// <summary>
    /// 回滚
    /// </summary>
    Rollback,

    /// <summary>
    /// 停止
    /// </summary>
    Stop,

    /// <summary>
    /// 转移
    /// </summary>
    Transfer
}

/// <summary>
/// 恢复策略
/// </summary>
public class RecoveryStrategy
{
    /// <summary>
    /// 策略名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 策略描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 最大重试次数
    /// </summary>
    public int MaxRetries { get; set; } = 3;

    /// <summary>
    /// 重试延迟
    /// </summary>
    public TimeSpan RetryDelay { get; set; } = TimeSpan.FromSeconds(1);

    /// <summary>
    /// 是否使用指数退避
    /// </summary>
    public bool UseExponentialBackoff { get; set; } = true;

    /// <summary>
    /// 适用的错误类型
    /// </summary>
    public List<string> ApplicableErrorTypes { get; set; } = new();
}

/// <summary>
/// 错误统计
/// </summary>
public class ErrorStatistics
{
    /// <summary>
    /// 总错误数
    /// </summary>
    public long TotalErrors { get; set; }

    /// <summary>
    /// 按类型统计
    /// </summary>
    public Dictionary<string, long> ErrorsByType { get; set; } = new();

    /// <summary>
    /// 按严重程度统计
    /// </summary>
    public Dictionary<ErrorSeverity, long> ErrorsBySeverity { get; set; } = new();

    /// <summary>
    /// 恢复成功率
    /// </summary>
    public double RecoverySuccessRate { get; set; }

    /// <summary>
    /// 统计时间范围
    /// </summary>
    public TimeSpan TimeRange { get; set; }

    /// <summary>
    /// 统计生成时间
    /// </summary>
    public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
}
