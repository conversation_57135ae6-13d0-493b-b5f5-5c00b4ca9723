using System.ComponentModel.DataAnnotations;

namespace FlowCustomV1.Core.Models.Workflow;

/// <summary>
/// 工作流连接
/// 表示工作流节点之间的连接关系
/// </summary>
public class WorkflowConnection
{
    /// <summary>
    /// 连接唯一标识符
    /// </summary>
    [Required]
    public string ConnectionId { get; set; } = string.Empty;

    /// <summary>
    /// 源节点ID
    /// </summary>
    [Required]
    public string SourceNodeId { get; set; } = string.Empty;

    /// <summary>
    /// 目标节点ID
    /// </summary>
    [Required]
    public string TargetNodeId { get; set; } = string.Empty;

    /// <summary>
    /// 源节点输出端口
    /// </summary>
    public string SourcePort { get; set; } = "default";

    /// <summary>
    /// 目标节点输入端口
    /// </summary>
    public string TargetPort { get; set; } = "default";

    /// <summary>
    /// 连接名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 连接描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 连接条件类型
    /// </summary>
    public ConnectionConditionType ConditionType { get; set; } = ConnectionConditionType.Always;

    /// <summary>
    /// 连接条件表达式
    /// </summary>
    public string? ConditionExpression { get; set; }

    /// <summary>
    /// 连接优先级
    /// </summary>
    public int Priority { get; set; } = 0;

    /// <summary>
    /// 是否启用连接
    /// </summary>
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// 数据转换配置
    /// </summary>
    public ConnectionDataTransform DataTransform { get; set; } = new();

    /// <summary>
    /// 连接标签
    /// </summary>
    public HashSet<string> Tags { get; set; } = new();

    /// <summary>
    /// 连接元数据
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();

    /// <summary>
    /// 验证连接的有效性
    /// </summary>
    /// <returns>是否有效</returns>
    public bool IsValid()
    {
        return !string.IsNullOrWhiteSpace(ConnectionId) &&
               !string.IsNullOrWhiteSpace(SourceNodeId) &&
               !string.IsNullOrWhiteSpace(TargetNodeId) &&
               SourceNodeId != TargetNodeId &&
               !string.IsNullOrWhiteSpace(SourcePort) &&
               !string.IsNullOrWhiteSpace(TargetPort);
    }

    /// <summary>
    /// 检查连接条件是否满足
    /// </summary>
    /// <param name="sourceNodeResult">源节点执行结果</param>
    /// <param name="context">执行上下文</param>
    /// <returns>是否满足条件</returns>
    public bool EvaluateCondition(NodeExecutionResult sourceNodeResult, Dictionary<string, object> context)
    {
        return ConditionType switch
        {
            ConnectionConditionType.Always => true,
            ConnectionConditionType.OnSuccess => sourceNodeResult.IsSuccess,
            ConnectionConditionType.OnFailure => !sourceNodeResult.IsSuccess,
            ConnectionConditionType.Expression => EvaluateExpression(sourceNodeResult, context),
            ConnectionConditionType.Custom => EvaluateCustomCondition(sourceNodeResult, context),
            _ => true
        };
    }

    /// <summary>
    /// 评估表达式条件
    /// </summary>
    /// <param name="sourceNodeResult">源节点执行结果</param>
    /// <param name="context">执行上下文</param>
    /// <returns>表达式结果</returns>
    private bool EvaluateExpression(NodeExecutionResult sourceNodeResult, Dictionary<string, object> context)
    {
        if (string.IsNullOrWhiteSpace(ConditionExpression))
            return true;

        try
        {
            // 这里应该实现表达式引擎
            // 暂时简单处理
            return ConditionExpression.ToLowerInvariant() switch
            {
                "true" => true,
                "false" => false,
                "success" => sourceNodeResult.IsSuccess,
                "failure" => !sourceNodeResult.IsSuccess,
                _ => true
            };
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 评估自定义条件
    /// </summary>
    /// <param name="sourceNodeResult">源节点执行结果</param>
    /// <param name="context">执行上下文</param>
    /// <returns>自定义条件结果</returns>
    private bool EvaluateCustomCondition(NodeExecutionResult sourceNodeResult, Dictionary<string, object> context)
    {
        // 这里可以实现自定义条件逻辑
        // 暂时返回true
        return true;
    }

    /// <summary>
    /// 创建连接的深拷贝
    /// </summary>
    /// <returns>连接的深拷贝</returns>
    public WorkflowConnection Clone()
    {
        return new WorkflowConnection
        {
            ConnectionId = ConnectionId,
            SourceNodeId = SourceNodeId,
            TargetNodeId = TargetNodeId,
            SourcePort = SourcePort,
            TargetPort = TargetPort,
            Name = Name,
            Description = Description,
            ConditionType = ConditionType,
            ConditionExpression = ConditionExpression,
            Priority = Priority,
            IsEnabled = IsEnabled,
            DataTransform = DataTransform.Clone(),
            Tags = new HashSet<string>(Tags),
            Metadata = new Dictionary<string, object>(Metadata)
        };
    }

    /// <summary>
    /// 获取连接的字符串表示
    /// </summary>
    /// <returns>连接字符串</returns>
    public override string ToString()
    {
        return $"Connection[{ConnectionId}] {SourceNodeId}:{SourcePort} -> {TargetNodeId}:{TargetPort} ({ConditionType})";
    }
}

/// <summary>
/// 连接数据转换配置
/// </summary>
public class ConnectionDataTransform
{
    /// <summary>
    /// 是否启用数据转换
    /// </summary>
    public bool EnableTransform { get; set; } = false;

    /// <summary>
    /// 转换脚本或表达式
    /// </summary>
    public string? TransformScript { get; set; }

    /// <summary>
    /// 转换类型
    /// </summary>
    public string TransformType { get; set; } = "JavaScript";

    /// <summary>
    /// 字段映射配置
    /// </summary>
    public Dictionary<string, string> FieldMappings { get; set; } = new();

    /// <summary>
    /// 数据过滤配置
    /// </summary>
    public List<string> DataFilters { get; set; } = new();

    /// <summary>
    /// 转换参数
    /// </summary>
    public Dictionary<string, object> TransformParameters { get; set; } = new();

    /// <summary>
    /// 执行数据转换
    /// </summary>
    /// <param name="inputData">输入数据</param>
    /// <returns>转换后的数据</returns>
    public Dictionary<string, object> Transform(Dictionary<string, object> inputData)
    {
        if (!EnableTransform || inputData == null)
            return inputData ?? new Dictionary<string, object>();

        var result = new Dictionary<string, object>(inputData);

        // 应用字段映射
        ApplyFieldMappings(result);

        // 应用数据过滤
        ApplyDataFilters(result);

        // 执行转换脚本
        if (!string.IsNullOrWhiteSpace(TransformScript))
        {
            result = ExecuteTransformScript(result);
        }

        return result;
    }

    /// <summary>
    /// 应用字段映射
    /// </summary>
    /// <param name="data">数据</param>
    private void ApplyFieldMappings(Dictionary<string, object> data)
    {
        foreach (var mapping in FieldMappings)
        {
            if (data.TryGetValue(mapping.Key, out var value))
            {
                data.Remove(mapping.Key);
                data[mapping.Value] = value;
            }
        }
    }

    /// <summary>
    /// 应用数据过滤
    /// </summary>
    /// <param name="data">数据</param>
    private void ApplyDataFilters(Dictionary<string, object> data)
    {
        foreach (var filter in DataFilters)
        {
            // 这里可以实现复杂的过滤逻辑
            // 暂时简单处理：如果过滤器以"remove:"开头，则移除对应字段
            if (filter.StartsWith("remove:", StringComparison.OrdinalIgnoreCase))
            {
                var fieldName = filter.Substring(7);
                data.Remove(fieldName);
            }
        }
    }

    /// <summary>
    /// 执行转换脚本
    /// </summary>
    /// <param name="data">数据</param>
    /// <returns>转换后的数据</returns>
    private Dictionary<string, object> ExecuteTransformScript(Dictionary<string, object> data)
    {
        // 这里应该实现脚本引擎
        // 暂时返回原数据
        return data;
    }

    /// <summary>
    /// 创建数据转换配置的深拷贝
    /// </summary>
    /// <returns>数据转换配置的深拷贝</returns>
    public ConnectionDataTransform Clone()
    {
        return new ConnectionDataTransform
        {
            EnableTransform = EnableTransform,
            TransformScript = TransformScript,
            TransformType = TransformType,
            FieldMappings = new Dictionary<string, string>(FieldMappings),
            DataFilters = new List<string>(DataFilters),
            TransformParameters = new Dictionary<string, object>(TransformParameters)
        };
    }

    /// <summary>
    /// 获取数据转换配置的字符串表示
    /// </summary>
    /// <returns>数据转换配置字符串</returns>
    public override string ToString()
    {
        return $"DataTransform[{(EnableTransform ? "Enabled" : "Disabled")}] Type:{TransformType} Mappings:{FieldMappings.Count}";
    }
}
