import React from 'react';
import { Button, Space, Tag, Modal, Form, Input, Select, message } from 'antd';
import { 
  DatabaseOutlined, 
  PlusOutlined, 
  EditOutlined,
  DeleteOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ApiOutlined
} from '@ant-design/icons';
import { ProTable, ProColumns, ProCard } from '@ant-design/pro-components';

// ========== 页面布局配置 ==========
const PAGE_LAYOUT_CONFIG = {
  // 表格滚动高度 - 有统计卡片容器 + 工具栏的页面
  tableScrollY: 'calc(100vh - 520px)'
};

const { Option } = Select;
const { TextArea } = Input;

// 模拟数据源数据
const mockDataSources = [
  {
    id: '1',
    name: 'MySQL主数据库',
    type: 'MySQL',
    host: 'mysql.example.com',
    port: 3306,
    database: 'flowcustom_main',
    username: 'app_user',
    status: 'Connected',
    lastTest: '2025-01-13 15:30:00',
    description: '主要业务数据库',
  },
  {
    id: '2',
    name: 'Redis缓存',
    type: 'Redis',
    host: 'redis.example.com',
    port: 6379,
    database: '0',
    username: '',
    status: 'Connected',
    lastTest: '2025-01-13 15:28:00',
    description: '系统缓存数据库',
  },
  {
    id: '3',
    name: 'MongoDB文档库',
    type: 'MongoDB',
    host: 'mongo.example.com',
    port: 27017,
    database: 'flowcustom_docs',
    username: 'mongo_user',
    status: 'Disconnected',
    lastTest: '2025-01-13 14:20:00',
    description: '文档和日志存储',
  },
  {
    id: '4',
    name: 'API数据源',
    type: 'HTTP API',
    host: 'api.external.com',
    port: 443,
    database: '',
    username: 'api_key',
    status: 'Connected',
    lastTest: '2025-01-13 15:25:00',
    description: '外部API数据源',
  },
];

const DataSources: React.FC = () => {
  const [dataSources, setDataSources] = React.useState(mockDataSources);
  const [modalVisible, setModalVisible] = React.useState(false);
  const [editingSource, setEditingSource] = React.useState<any>(null);
  const [form] = Form.useForm();

  // 获取数据源类型标签
  const getTypeTag = (type: string) => {
    const typeMap = {
      'MySQL': { color: 'blue', icon: <DatabaseOutlined /> },
      'PostgreSQL': { color: 'green', icon: <DatabaseOutlined /> },
      'Redis': { color: 'red', icon: <DatabaseOutlined /> },
      'MongoDB': { color: 'orange', icon: <DatabaseOutlined /> },
      'HTTP API': { color: 'purple', icon: <ApiOutlined /> },
      'File': { color: 'cyan', icon: <DatabaseOutlined /> },
    };
    const config = typeMap[type as keyof typeof typeMap] || { color: 'default', icon: <DatabaseOutlined /> };
    return (
      <Tag color={config.color} icon={config.icon}>
        {type}
      </Tag>
    );
  };

  // 获取状态标签
  const getStatusTag = (status: string) => {
    const statusMap = {
      Connected: { color: 'success', text: '已连接', icon: <CheckCircleOutlined /> },
      Disconnected: { color: 'error', text: '未连接', icon: <ExclamationCircleOutlined /> },
      Testing: { color: 'processing', text: '测试中', icon: <ExclamationCircleOutlined /> },
    };
    const config = statusMap[status as keyof typeof statusMap] || { color: 'default', text: status, icon: null };
    return (
      <Tag color={config.color} icon={config.icon}>
        {config.text}
      </Tag>
    );
  };

  // 测试连接
  const handleTestConnection = async (source: any) => {
    // 更新状态为测试中
    setDataSources(prev => prev.map(s => 
      s.id === source.id ? { ...s, status: 'Testing' } : s
    ));

    // 模拟测试连接
    setTimeout(() => {
      const success = Math.random() > 0.3; // 70% 成功率
      setDataSources(prev => prev.map(s => 
        s.id === source.id ? { 
          ...s, 
          status: success ? 'Connected' : 'Disconnected',
          lastTest: new Date().toLocaleString()
        } : s
      ));
      message[success ? 'success' : 'error'](
        success ? '连接测试成功' : '连接测试失败'
      );
    }, 2000);
  };

  // 表格列定义
  const columns: ProColumns<any>[] = [
    {
      title: '数据源名称',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <div>
          <div className="font-medium">{text}</div>
          <div className="text-sm text-gray-500">{record.description}</div>
        </div>
      ),
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (_, record) => getTypeTag(record.type),
    },
    {
      title: '连接信息',
      key: 'connection',
      render: (_, record) => (
        <div className="text-sm">
          <div>{record.host}:{record.port}</div>
          {record.database && <div className="text-gray-500">DB: {record.database}</div>}
          {record.username && <div className="text-gray-500">User: {record.username}</div>}
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (_, record) => getStatusTag(record.status),
    },
    {
      title: '最后测试',
      dataIndex: 'lastTest',
      key: 'lastTest',
      render: (_, record) => new Date(record.lastTest).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record) => (
        <Space size="small">
          <Button 
            type="text" 
            size="small" 
            onClick={() => handleTestConnection(record)}
            loading={record.status === 'Testing'}
          >
            测试连接
          </Button>
          <Button 
            type="text" 
            size="small" 
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button 
            type="text" 
            size="small" 
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  const handleEdit = (source: any) => {
    setEditingSource(source);
    form.setFieldsValue(source);
    setModalVisible(true);
  };

  const handleDelete = (source: any) => {
    Modal.confirm({
      title: '删除数据源',
      content: `确定要删除数据源 "${source.name}" 吗？`,
      okType: 'danger',
      onOk: () => {
        setDataSources(prev => prev.filter(s => s.id !== source.id));
        message.success('数据源已删除');
      },
    });
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      if (editingSource) {
        // 更新数据源
        setDataSources(prev => prev.map(s => 
          s.id === editingSource.id ? { ...s, ...values } : s
        ));
        message.success('数据源信息已更新');
      } else {
        // 创建新数据源
        const newSource = {
          id: Date.now().toString(),
          ...values,
          status: 'Disconnected',
          lastTest: '-',
        };
        setDataSources(prev => [...prev, newSource]);
        message.success('数据源创建成功');
      }
      setModalVisible(false);
      setEditingSource(null);
      form.resetFields();
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  return (
    <div>
      <div className="mb-6">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h1 className="text-2xl font-semibold text-gray-900 mb-2">
              <DatabaseOutlined className="mr-2" />
              数据源管理
            </h1>
            <p className="text-gray-600">管理系统中的数据源连接配置</p>
          </div>
          <Button 
            type="primary" 
            icon={<PlusOutlined />}
            onClick={() => {
              setEditingSource(null);
              form.resetFields();
              setModalVisible(true);
            }}
          >
            添加数据源
          </Button>
        </div>
      </div>

      {/* 数据源统计 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <ProCard>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{dataSources.length}</div>
            <div className="text-sm text-gray-500">总数据源</div>
          </div>
        </ProCard>
        <ProCard>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {dataSources.filter(s => s.status === 'Connected').length}
            </div>
            <div className="text-sm text-gray-500">已连接</div>
          </div>
        </ProCard>
        <ProCard>
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-600">
              {dataSources.filter(s => s.type === 'MySQL').length}
            </div>
            <div className="text-sm text-gray-500">MySQL</div>
          </div>
        </ProCard>
        <ProCard>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">
              {dataSources.filter(s => s.type === 'HTTP API').length}
            </div>
            <div className="text-sm text-gray-500">API</div>
          </div>
        </ProCard>
      </div>

      {/* 数据源表格 */}
      <ProTable
        columns={columns}
        dataSource={dataSources}
        rowKey="id"
        pagination={{
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`,
        }}
        scroll={{
          x: 1200,
          y: PAGE_LAYOUT_CONFIG.tableScrollY
        }}
        search={false}
        toolBarRender={false}
        options={false}
      />

      {/* 数据源编辑模态框 */}
      <Modal
        title={editingSource ? '编辑数据源' : '添加数据源'}
        open={modalVisible}
        onOk={handleSubmit}
        onCancel={() => {
          setModalVisible(false);
          setEditingSource(null);
          form.resetFields();
        }}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            type: 'MySQL',
            port: 3306,
          }}
        >
          <Form.Item
            label="数据源名称"
            name="name"
            rules={[{ required: true, message: '请输入数据源名称' }]}
          >
            <Input placeholder="请输入数据源名称" />
          </Form.Item>

          <Form.Item
            label="类型"
            name="type"
            rules={[{ required: true, message: '请选择数据源类型' }]}
          >
            <Select placeholder="请选择数据源类型">
              <Option value="MySQL">MySQL</Option>
              <Option value="PostgreSQL">PostgreSQL</Option>
              <Option value="Redis">Redis</Option>
              <Option value="MongoDB">MongoDB</Option>
              <Option value="HTTP API">HTTP API</Option>
              <Option value="File">文件</Option>
            </Select>
          </Form.Item>

          <div className="grid grid-cols-2 gap-4">
            <Form.Item
              label="主机地址"
              name="host"
              rules={[{ required: true, message: '请输入主机地址' }]}
            >
              <Input placeholder="请输入主机地址" />
            </Form.Item>

            <Form.Item
              label="端口"
              name="port"
              rules={[{ required: true, message: '请输入端口' }]}
            >
              <Input placeholder="请输入端口" />
            </Form.Item>
          </div>

          <Form.Item
            label="数据库名"
            name="database"
          >
            <Input placeholder="请输入数据库名" />
          </Form.Item>

          <div className="grid grid-cols-2 gap-4">
            <Form.Item
              label="用户名"
              name="username"
            >
              <Input placeholder="请输入用户名" />
            </Form.Item>

            <Form.Item
              label="密码"
              name="password"
            >
              <Input.Password placeholder="请输入密码" />
            </Form.Item>
          </div>

          <Form.Item
            label="描述"
            name="description"
          >
            <TextArea 
              placeholder="请输入描述信息" 
              rows={3}
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default DataSources;
