using FlowCustomV1.Core.Interfaces.Messaging;
using FlowCustomV1.Infrastructure.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Collections.Concurrent;

namespace FlowCustomV1.Infrastructure.Services.Messaging;

/// <summary>
/// 连接状态管理器
/// 负责管理NATS连接状态、重连逻辑和异常恢复
/// </summary>
public class ConnectionStateManager : IDisposable
{
    private readonly NatsConfiguration _config;
    private readonly ILogger<ConnectionStateManager> _logger;
    private readonly ConcurrentDictionary<string, ConnectionState> _connectionStates;
    private readonly Timer _healthCheckTimer;
    private readonly Timer _reconnectTimer;
    private readonly SemaphoreSlim _reconnectSemaphore;
    private bool _disposed;

    /// <summary>
    /// 连接状态变更事件
    /// </summary>
    public event EventHandler<ConnectionStateChangedEventArgs>? ConnectionStateChanged;

    /// <summary>
    /// 重连尝试事件
    /// </summary>
    public event EventHandler<ReconnectAttemptEventArgs>? ReconnectAttempt;

    /// <summary>
    /// 健康检查事件
    /// </summary>
    public event EventHandler<HealthCheckEventArgs>? HealthCheck;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="config">NATS配置</param>
    /// <param name="logger">日志记录器</param>
    public ConnectionStateManager(IOptions<NatsConfiguration> config, ILogger<ConnectionStateManager> logger)
    {
        _config = config.Value ?? throw new ArgumentNullException(nameof(config));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _connectionStates = new ConcurrentDictionary<string, ConnectionState>();
        _reconnectSemaphore = new SemaphoreSlim(1, 1);

        // 初始化定时器
        _healthCheckTimer = new Timer(PerformHealthCheck, null, 
            TimeSpan.FromSeconds(30), TimeSpan.FromSeconds(30));
        
        _reconnectTimer = new Timer(AttemptReconnection, null, 
            Timeout.InfiniteTimeSpan, Timeout.InfiniteTimeSpan);
    }

    /// <summary>
    /// 注册连接
    /// </summary>
    /// <param name="connectionId">连接ID</param>
    /// <param name="serverUrl">服务器URL</param>
    public void RegisterConnection(string connectionId, string serverUrl)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(connectionId);
        ArgumentException.ThrowIfNullOrWhiteSpace(serverUrl);

        var state = new ConnectionState
        {
            ConnectionId = connectionId,
            ServerUrl = serverUrl,
            Status = ConnectionStatus.Connecting,
            LastConnected = DateTime.UtcNow,
            ReconnectAttempts = 0
        };

        _connectionStates.AddOrUpdate(connectionId, state, (key, existing) =>
        {
            existing.Status = ConnectionStatus.Connecting;
            existing.LastStatusChange = DateTime.UtcNow;
            return existing;
        });

        _logger.LogDebug("Registered connection {ConnectionId} to {ServerUrl}", connectionId, serverUrl);
    }

    /// <summary>
    /// 更新连接状态
    /// </summary>
    /// <param name="connectionId">连接ID</param>
    /// <param name="status">连接状态</param>
    /// <param name="error">错误信息</param>
    public void UpdateConnectionStatus(string connectionId, ConnectionStatus status, string? error = null)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(connectionId);

        if (_connectionStates.TryGetValue(connectionId, out var state))
        {
            var previousStatus = state.Status;
            state.Status = status;
            state.LastStatusChange = DateTime.UtcNow;
            state.LastError = error;

            if (status == ConnectionStatus.Connected)
            {
                state.LastConnected = DateTime.UtcNow;
                state.ReconnectAttempts = 0;
                state.LastError = null;
            }
            else if (status == ConnectionStatus.Disconnected || status == ConnectionStatus.Failed)
            {
                state.LastDisconnected = DateTime.UtcNow;
            }

            _logger.LogInformation("Connection {ConnectionId} status changed from {PreviousStatus} to {NewStatus}", 
                connectionId, previousStatus, status);

            // 触发状态变更事件
            OnConnectionStateChanged(new ConnectionStateChangedEventArgs
            {
                ConnectionId = connectionId,
                ServerUrl = state.ServerUrl,
                PreviousStatus = previousStatus,
                CurrentStatus = status,
                Error = error,
                Timestamp = DateTime.UtcNow
            });

            // 如果连接失败且启用了自动重连，启动重连定时器
            if ((status == ConnectionStatus.Disconnected || status == ConnectionStatus.Failed) && 
                _config.EnableAutoReconnect && 
                state.ReconnectAttempts < _config.MaxReconnectAttempts)
            {
                StartReconnectTimer();
            }
        }
        else
        {
            _logger.LogWarning("Attempted to update status for unknown connection {ConnectionId}", connectionId);
        }
    }

    /// <summary>
    /// 获取连接状态
    /// </summary>
    /// <param name="connectionId">连接ID</param>
    /// <returns>连接状态</returns>
    public ConnectionState? GetConnectionState(string connectionId)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(connectionId);
        return _connectionStates.TryGetValue(connectionId, out var state) ? state : null;
    }

    /// <summary>
    /// 获取所有连接状态
    /// </summary>
    /// <returns>连接状态列表</returns>
    public IReadOnlyDictionary<string, ConnectionState> GetAllConnectionStates()
    {
        return _connectionStates.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
    }

    /// <summary>
    /// 移除连接
    /// </summary>
    /// <param name="connectionId">连接ID</param>
    public void UnregisterConnection(string connectionId)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(connectionId);

        if (_connectionStates.TryRemove(connectionId, out var state))
        {
            _logger.LogDebug("Unregistered connection {ConnectionId}", connectionId);
        }
    }

    /// <summary>
    /// 启动重连定时器
    /// </summary>
    private void StartReconnectTimer()
    {
        var interval = TimeSpan.FromSeconds(_config.ReconnectIntervalSeconds);
        _reconnectTimer.Change(interval, Timeout.InfiniteTimeSpan);
    }

    /// <summary>
    /// 尝试重连
    /// </summary>
    /// <param name="state">定时器状态</param>
    private async void AttemptReconnection(object? state)
    {
        if (_disposed || !await _reconnectSemaphore.WaitAsync(100))
            return;

        try
        {
            var disconnectedConnections = _connectionStates.Values
                .Where(c => (c.Status == ConnectionStatus.Disconnected || c.Status == ConnectionStatus.Failed) &&
                           c.ReconnectAttempts < _config.MaxReconnectAttempts)
                .ToList();

            foreach (var connection in disconnectedConnections)
            {
                connection.ReconnectAttempts++;
                
                _logger.LogInformation("Attempting reconnection {Attempt}/{MaxAttempts} for connection {ConnectionId}", 
                    connection.ReconnectAttempts, _config.MaxReconnectAttempts, connection.ConnectionId);

                // 触发重连尝试事件
                OnReconnectAttempt(new ReconnectAttemptEventArgs
                {
                    ConnectionId = connection.ConnectionId,
                    ServerUrl = connection.ServerUrl,
                    AttemptNumber = connection.ReconnectAttempts,
                    MaxAttempts = _config.MaxReconnectAttempts,
                    Timestamp = DateTime.UtcNow
                });

                // 这里应该由具体的连接实现来处理重连逻辑
                // 我们只是更新状态和触发事件
            }

            // 如果还有需要重连的连接，继续定时器
            if (disconnectedConnections.Any(c => c.ReconnectAttempts < _config.MaxReconnectAttempts))
            {
                StartReconnectTimer();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during reconnection attempt");
        }
        finally
        {
            _reconnectSemaphore.Release();
        }
    }

    /// <summary>
    /// 执行健康检查
    /// </summary>
    /// <param name="state">定时器状态</param>
    private void PerformHealthCheck(object? state)
    {
        if (_disposed) return;

        try
        {
            var now = DateTime.UtcNow;
            var healthCheckResults = new List<ConnectionHealthResult>();

            foreach (var kvp in _connectionStates)
            {
                var connection = kvp.Value;
                var timeSinceLastStatusChange = now - connection.LastStatusChange;
                var isHealthy = connection.Status == ConnectionStatus.Connected && 
                               timeSinceLastStatusChange < TimeSpan.FromMinutes(5);

                var healthResult = new ConnectionHealthResult
                {
                    ConnectionId = connection.ConnectionId,
                    ServerUrl = connection.ServerUrl,
                    IsHealthy = isHealthy,
                    Status = connection.Status,
                    TimeSinceLastStatusChange = timeSinceLastStatusChange,
                    ReconnectAttempts = connection.ReconnectAttempts,
                    LastError = connection.LastError
                };

                healthCheckResults.Add(healthResult);

                if (!isHealthy && connection.Status == ConnectionStatus.Connected)
                {
                    _logger.LogWarning("Connection {ConnectionId} appears unhealthy: no status change for {Duration}", 
                        connection.ConnectionId, timeSinceLastStatusChange);
                }
            }

            // 触发健康检查事件
            OnHealthCheck(new HealthCheckEventArgs
            {
                Results = healthCheckResults,
                Timestamp = now
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during health check");
        }
    }

    /// <summary>
    /// 触发连接状态变更事件
    /// </summary>
    /// <param name="args">事件参数</param>
    protected virtual void OnConnectionStateChanged(ConnectionStateChangedEventArgs args)
    {
        ConnectionStateChanged?.Invoke(this, args);
    }

    /// <summary>
    /// 触发重连尝试事件
    /// </summary>
    /// <param name="args">事件参数</param>
    protected virtual void OnReconnectAttempt(ReconnectAttemptEventArgs args)
    {
        ReconnectAttempt?.Invoke(this, args);
    }

    /// <summary>
    /// 触发健康检查事件
    /// </summary>
    /// <param name="args">事件参数</param>
    protected virtual void OnHealthCheck(HealthCheckEventArgs args)
    {
        HealthCheck?.Invoke(this, args);
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (_disposed) return;

        _healthCheckTimer?.Dispose();
        _reconnectTimer?.Dispose();
        _reconnectSemaphore?.Dispose();
        _connectionStates.Clear();
        _disposed = true;
    }
}

/// <summary>
/// 连接状态
/// </summary>
public class ConnectionState
{
    /// <summary>
    /// 连接ID
    /// </summary>
    public string ConnectionId { get; set; } = string.Empty;

    /// <summary>
    /// 服务器URL
    /// </summary>
    public string ServerUrl { get; set; } = string.Empty;

    /// <summary>
    /// 连接状态
    /// </summary>
    public ConnectionStatus Status { get; set; }

    /// <summary>
    /// 最后状态变更时间
    /// </summary>
    public DateTime LastStatusChange { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 最后连接时间
    /// </summary>
    public DateTime? LastConnected { get; set; }

    /// <summary>
    /// 最后断开时间
    /// </summary>
    public DateTime? LastDisconnected { get; set; }

    /// <summary>
    /// 重连尝试次数
    /// </summary>
    public int ReconnectAttempts { get; set; }

    /// <summary>
    /// 最后错误信息
    /// </summary>
    public string? LastError { get; set; }
}

/// <summary>
/// 连接状态枚举
/// </summary>
public enum ConnectionStatus
{
    /// <summary>
    /// 连接中
    /// </summary>
    Connecting,

    /// <summary>
    /// 已连接
    /// </summary>
    Connected,

    /// <summary>
    /// 已断开
    /// </summary>
    Disconnected,

    /// <summary>
    /// 连接失败
    /// </summary>
    Failed,

    /// <summary>
    /// 重连中
    /// </summary>
    Reconnecting
}

/// <summary>
/// 连接状态变更事件参数
/// </summary>
public class ConnectionStateChangedEventArgs : EventArgs
{
    /// <summary>
    /// 连接ID
    /// </summary>
    public string ConnectionId { get; set; } = string.Empty;

    /// <summary>
    /// 服务器URL
    /// </summary>
    public string? ServerUrl { get; set; }

    /// <summary>
    /// 之前的状态
    /// </summary>
    public ConnectionStatus PreviousStatus { get; set; }

    /// <summary>
    /// 当前状态
    /// </summary>
    public ConnectionStatus CurrentStatus { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? Error { get; set; }

    /// <summary>
    /// 时间戳
    /// </summary>
    public DateTime Timestamp { get; set; }
}

/// <summary>
/// 重连尝试事件参数
/// </summary>
public class ReconnectAttemptEventArgs : EventArgs
{
    /// <summary>
    /// 连接ID
    /// </summary>
    public string ConnectionId { get; set; } = string.Empty;

    /// <summary>
    /// 服务器URL
    /// </summary>
    public string ServerUrl { get; set; } = string.Empty;

    /// <summary>
    /// 尝试次数
    /// </summary>
    public int AttemptNumber { get; set; }

    /// <summary>
    /// 最大尝试次数
    /// </summary>
    public int MaxAttempts { get; set; }

    /// <summary>
    /// 时间戳
    /// </summary>
    public DateTime Timestamp { get; set; }
}

/// <summary>
/// 健康检查事件参数
/// </summary>
public class HealthCheckEventArgs : EventArgs
{
    /// <summary>
    /// 健康检查结果列表
    /// </summary>
    public List<ConnectionHealthResult> Results { get; set; } = new();

    /// <summary>
    /// 时间戳
    /// </summary>
    public DateTime Timestamp { get; set; }
}

/// <summary>
/// 连接健康检查结果
/// </summary>
public class ConnectionHealthResult
{
    /// <summary>
    /// 连接ID
    /// </summary>
    public string ConnectionId { get; set; } = string.Empty;

    /// <summary>
    /// 服务器URL
    /// </summary>
    public string ServerUrl { get; set; } = string.Empty;

    /// <summary>
    /// 是否健康
    /// </summary>
    public bool IsHealthy { get; set; }

    /// <summary>
    /// 连接状态
    /// </summary>
    public ConnectionStatus Status { get; set; }

    /// <summary>
    /// 距离上次状态变更的时间
    /// </summary>
    public TimeSpan TimeSinceLastStatusChange { get; set; }

    /// <summary>
    /// 重连尝试次数
    /// </summary>
    public int ReconnectAttempts { get; set; }

    /// <summary>
    /// 最后错误信息
    /// </summary>
    public string? LastError { get; set; }
}
