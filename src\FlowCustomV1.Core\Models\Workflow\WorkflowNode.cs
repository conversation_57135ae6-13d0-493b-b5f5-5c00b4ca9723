using System.ComponentModel.DataAnnotations;

namespace FlowCustomV1.Core.Models.Workflow;

/// <summary>
/// 工作流节点
/// 表示工作流中的一个执行单元
/// </summary>
public class WorkflowNode
{
    /// <summary>
    /// 节点唯一标识符
    /// </summary>
    [Required]
    public string NodeId { get; set; } = string.Empty;

    /// <summary>
    /// 节点名称
    /// </summary>
    [Required]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 节点描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 节点类型
    /// </summary>
    [Required]
    public string NodeType { get; set; } = string.Empty;

    /// <summary>
    /// 节点类型分类
    /// </summary>
    public NodeTypeCategory Category { get; set; } = NodeTypeCategory.Process;

    /// <summary>
    /// 节点版本
    /// </summary>
    public string Version { get; set; } = "1.0.0";

    /// <summary>
    /// 节点配置
    /// </summary>
    public NodeConfiguration Configuration { get; set; } = new();

    /// <summary>
    /// 节点位置信息（用于UI显示）
    /// </summary>
    public NodePosition Position { get; set; } = new();

    /// <summary>
    /// 是否启用节点
    /// </summary>
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// 是否为关键节点（失败时停止整个工作流）
    /// </summary>
    public bool IsCritical { get; set; } = false;

    /// <summary>
    /// 节点超时时间（分钟）
    /// </summary>
    public int TimeoutMinutes { get; set; } = 30;

    /// <summary>
    /// 重试策略
    /// </summary>
    public NodeRetryStrategy RetryStrategy { get; set; } = new();

    /// <summary>
    /// 节点标签
    /// </summary>
    public HashSet<string> Tags { get; set; } = new();

    /// <summary>
    /// 节点元数据
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();

    /// <summary>
    /// 节点执行优先级（数值越小优先级越高）
    /// </summary>
    public int Priority { get; set; } = 0;

    /// <summary>
    /// 验证节点的有效性
    /// </summary>
    /// <returns>是否有效</returns>
    public bool IsValid()
    {
        return !string.IsNullOrWhiteSpace(NodeId) &&
               !string.IsNullOrWhiteSpace(Name) &&
               !string.IsNullOrWhiteSpace(NodeType) &&
               TimeoutMinutes > 0 &&
               Configuration.IsValid();
    }

    /// <summary>
    /// 创建节点的深拷贝
    /// </summary>
    /// <returns>节点的深拷贝</returns>
    public WorkflowNode Clone()
    {
        return new WorkflowNode
        {
            NodeId = NodeId,
            Name = Name,
            Description = Description,
            NodeType = NodeType,
            Category = Category,
            Version = Version,
            Configuration = Configuration.Clone(),
            Position = Position.Clone(),
            IsEnabled = IsEnabled,
            IsCritical = IsCritical,
            TimeoutMinutes = TimeoutMinutes,
            RetryStrategy = RetryStrategy.Clone(),
            Tags = new HashSet<string>(Tags),
            Metadata = new Dictionary<string, object>(Metadata),
            Priority = Priority
        };
    }

    /// <summary>
    /// 获取节点的字符串表示
    /// </summary>
    /// <returns>节点字符串</returns>
    public override string ToString()
    {
        return $"Node[{NodeId}] {Name} ({NodeType}) - {(IsEnabled ? "Enabled" : "Disabled")}";
    }
}

/// <summary>
/// 节点位置信息
/// </summary>
public class NodePosition
{
    /// <summary>
    /// X坐标
    /// </summary>
    public double X { get; set; } = 0;

    /// <summary>
    /// Y坐标
    /// </summary>
    public double Y { get; set; } = 0;

    /// <summary>
    /// 宽度
    /// </summary>
    public double Width { get; set; } = 100;

    /// <summary>
    /// 高度
    /// </summary>
    public double Height { get; set; } = 50;

    /// <summary>
    /// 创建位置信息的深拷贝
    /// </summary>
    /// <returns>位置信息的深拷贝</returns>
    public NodePosition Clone()
    {
        return new NodePosition
        {
            X = X,
            Y = Y,
            Width = Width,
            Height = Height
        };
    }

    /// <summary>
    /// 获取位置信息的字符串表示
    /// </summary>
    /// <returns>位置信息字符串</returns>
    public override string ToString()
    {
        return $"Position({X}, {Y}) Size({Width}x{Height})";
    }
}

/// <summary>
/// 节点重试策略
/// </summary>
public class NodeRetryStrategy
{
    /// <summary>
    /// 重试策略类型
    /// </summary>
    public RetryStrategyType StrategyType { get; set; } = RetryStrategyType.None;

    /// <summary>
    /// 最大重试次数
    /// </summary>
    public int MaxRetryCount { get; set; } = 0;

    /// <summary>
    /// 重试间隔（秒）
    /// </summary>
    public int RetryIntervalSeconds { get; set; } = 5;

    /// <summary>
    /// 退避倍数（用于指数退避）
    /// </summary>
    public double BackoffMultiplier { get; set; } = 2.0;

    /// <summary>
    /// 最大重试间隔（秒）
    /// </summary>
    public int MaxRetryIntervalSeconds { get; set; } = 300;

    /// <summary>
    /// 需要重试的异常类型
    /// </summary>
    public List<string> RetryableExceptions { get; set; } = new();

    /// <summary>
    /// 不需要重试的异常类型
    /// </summary>
    public List<string> NonRetryableExceptions { get; set; } = new();

    /// <summary>
    /// 是否允许失败时跳过
    /// </summary>
    public bool AllowSkipOnFailure { get; set; } = false;

    // 兼容性属性
    /// <summary>
    /// 基础延迟时间（兼容性属性）
    /// </summary>
    public TimeSpan BaseDelay
    {
        get => TimeSpan.FromSeconds(RetryIntervalSeconds);
        set => RetryIntervalSeconds = (int)value.TotalSeconds;
    }

    /// <summary>
    /// 最大延迟时间（兼容性属性）
    /// </summary>
    public TimeSpan MaxDelay
    {
        get => TimeSpan.FromSeconds(MaxRetryIntervalSeconds);
        set => MaxRetryIntervalSeconds = (int)value.TotalSeconds;
    }

    /// <summary>
    /// 计算指定重试次数的延迟时间
    /// </summary>
    /// <param name="retryCount">重试次数</param>
    /// <returns>延迟时间（秒）</returns>
    public int CalculateDelay(int retryCount)
    {
        if (StrategyType == RetryStrategyType.None || retryCount <= 0)
            return 0;

        var delay = StrategyType switch
        {
            RetryStrategyType.FixedInterval => RetryIntervalSeconds,
            RetryStrategyType.LinearBackoff => RetryIntervalSeconds * retryCount,
            RetryStrategyType.ExponentialBackoff => (int)(RetryIntervalSeconds * Math.Pow(BackoffMultiplier, retryCount - 1)),
            _ => RetryIntervalSeconds
        };

        return Math.Min(delay, MaxRetryIntervalSeconds);
    }

    /// <summary>
    /// 判断异常是否可以重试
    /// </summary>
    /// <param name="exceptionType">异常类型</param>
    /// <returns>是否可以重试</returns>
    public bool ShouldRetry(string exceptionType)
    {
        if (StrategyType == RetryStrategyType.None || MaxRetryCount <= 0)
            return false;

        // 如果在不可重试列表中，则不重试
        if (NonRetryableExceptions.Contains(exceptionType, StringComparer.OrdinalIgnoreCase))
            return false;

        // 如果指定了可重试列表，则只重试列表中的异常
        if (RetryableExceptions.Count > 0)
            return RetryableExceptions.Contains(exceptionType, StringComparer.OrdinalIgnoreCase);

        // 默认可以重试
        return true;
    }

    /// <summary>
    /// 创建重试策略的深拷贝
    /// </summary>
    /// <returns>重试策略的深拷贝</returns>
    public NodeRetryStrategy Clone()
    {
        return new NodeRetryStrategy
        {
            StrategyType = StrategyType,
            MaxRetryCount = MaxRetryCount,
            RetryIntervalSeconds = RetryIntervalSeconds,
            BackoffMultiplier = BackoffMultiplier,
            MaxRetryIntervalSeconds = MaxRetryIntervalSeconds,
            RetryableExceptions = new List<string>(RetryableExceptions),
            NonRetryableExceptions = new List<string>(NonRetryableExceptions)
        };
    }

    /// <summary>
    /// 获取重试策略的字符串表示
    /// </summary>
    /// <returns>重试策略字符串</returns>
    public override string ToString()
    {
        return $"RetryStrategy[{StrategyType}] MaxRetries:{MaxRetryCount} Interval:{RetryIntervalSeconds}s";
    }
}
