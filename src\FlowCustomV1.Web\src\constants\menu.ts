import {
  DashboardOutlined,
  PartitionOutlined,
  PlayCircleOutlined,
  ClusterOutlined,
  DesktopOutlined,
  MonitorOutlined,
  SettingOutlined,
  ApiOutlined,
  DatabaseOutlined,
  SafetyOutlined,
  ToolOutlined,
  AppstoreOutlined,
} from '@ant-design/icons';

export interface MenuItem {
  key: string;
  label: string;
  icon?: any;
  path?: string;
  children?: MenuItem[];
  disabled?: boolean;
}

// 主菜单配置
export const menuItems: MenuItem[] = [
  {
    key: 'dashboard',
    label: '仪表盘',
    icon: DashboardOutlined,
    path: '/dashboard',
  },
  {
    key: 'workflow',
    label: '工作流管理',
    icon: PartitionOutlined,
    children: [
      {
        key: 'workflow-list',
        label: '工作流列表',
        path: '/workflow/list',
      },
      {
        key: 'workflow-designer',
        label: '可视化设计器',
        path: '/workflow/designer',
      },
      {
        key: 'workflow-templates',
        label: '模板库',
        path: '/workflow/templates',
      },
      {
        key: 'workflow-import-export',
        label: '导入导出',
        path: '/workflow/import-export',
      },
      {
        key: 'workflow-debugger',
        label: '工作流调试器',
        path: '/workflow/debugger',
      },
    ],
  },
  {
    key: 'execution',
    label: '执行管理',
    icon: PlayCircleOutlined,
    children: [
      {
        key: 'execution-monitor',
        label: '执行监控',
        path: '/execution/monitor',
      },
      {
        key: 'execution-history',
        label: '执行历史',
        path: '/execution/history',
      },
      {
        key: 'execution-schedule',
        label: '定时调度',
        path: '/execution/schedule',
      },
      {
        key: 'execution-queue',
        label: '执行队列',
        path: '/execution/queue',
      },
    ],
  },
  {
    key: 'cluster',
    label: '集群管理',
    icon: ClusterOutlined,
    children: [
      {
        key: 'cluster-overview',
        label: '集群概览',
        path: '/cluster/overview',
      },
      {
        key: 'cluster-nodes',
        label: '节点管理',
        path: '/cluster/nodes',
      },
      {
        key: 'cluster-topology',
        label: '集群拓扑',
        path: '/cluster/topology',
      },
      {
        key: 'cluster-health',
        label: '健康检查',
        path: '/cluster/health',
      },
    ],
  },
  {
    key: 'nodes',
    label: '节点服务',
    icon: DesktopOutlined,
    children: [
      {
        key: 'node-designer',
        label: 'Designer 设计器',
        path: '/nodes/designer',
      },
      {
        key: 'node-validator',
        label: 'Validator 验证器',
        path: '/nodes/validator',
      },
      {
        key: 'node-executor',
        label: 'Executor 执行器',
        path: '/nodes/executor',
      },
      {
        key: 'node-monitor',
        label: 'Monitor 监控器',
        path: '/nodes/monitor',
      },
      {
        key: 'node-scheduler',
        label: 'Scheduler 调度器',
        path: '/nodes/scheduler',
      },
      {
        key: 'node-storage',
        label: 'Storage 存储器',
        path: '/nodes/storage',
      },
    ],
  },
  {
    key: 'monitoring',
    label: '监控中心',
    icon: MonitorOutlined,
    children: [
      {
        key: 'monitoring-dashboard',
        label: '监控仪表盘',
        path: '/monitoring/dashboard',
      },
      {
        key: 'monitoring-metrics',
        label: '性能指标',
        path: '/monitoring/metrics',
      },
      {
        key: 'monitoring-alerts',
        label: '告警管理',
        path: '/monitoring/alerts',
      },
      {
        key: 'monitoring-logs',
        label: '日志中心',
        path: '/monitoring/logs',
      },
    ],
  },
  {
    key: 'plugins',
    label: '插件管理',
    icon: AppstoreOutlined,
    children: [
      {
        key: 'plugins-market',
        label: '插件市场',
        path: '/plugins/market',
      },
      {
        key: 'plugins-installed',
        label: '已安装插件',
        path: '/plugins/installed',
      },
      {
        key: 'plugins-development',
        label: '插件开发',
        path: '/plugins/development',
      },
      {
        key: 'plugins-natasha',
        label: 'Natasha 编译器',
        path: '/plugins/natasha',
      },
    ],
  },
  {
    key: 'data',
    label: '数据管理',
    icon: DatabaseOutlined,
    children: [
      {
        key: 'data-sources',
        label: '数据源管理',
        path: '/data/sources',
      },
      {
        key: 'data-processing',
        label: '数据处理',
        path: '/data/processing',
      },
      {
        key: 'data-backup',
        label: '数据备份',
        path: '/data/backup',
      },
      {
        key: 'data-migration',
        label: '数据迁移',
        path: '/data/migration',
      },
    ],
  },
  {
    key: 'integration',
    label: '系统集成',
    icon: ApiOutlined,
    children: [
      {
        key: 'integration-apis',
        label: 'API 管理',
        path: '/integration/apis',
      },
      {
        key: 'integration-webhooks',
        label: 'Webhook 管理',
        path: '/integration/webhooks',
      },
      {
        key: 'integration-nats',
        label: 'NATS 消息队列',
        path: '/integration/nats',
      },
      {
        key: 'integration-external',
        label: '外部系统',
        path: '/integration/external',
      },
    ],
  },
  {
    key: 'security',
    label: '安全管理',
    icon: SafetyOutlined,
    children: [
      {
        key: 'security-users',
        label: '用户管理',
        path: '/security/users',
      },
      {
        key: 'security-roles',
        label: '角色权限',
        path: '/security/roles',
      },
      {
        key: 'security-audit',
        label: '审计日志',
        path: '/security/audit',
      },
      {
        key: 'security-tokens',
        label: 'API 令牌',
        path: '/security/tokens',
      },
    ],
  },
  {
    key: 'tools',
    label: '开发工具',
    icon: ToolOutlined,
    children: [
      {
        key: 'tools-debugger',
        label: '工作流调试器',
        path: '/tools/debugger',
      },
      {
        key: 'tools-testing',
        label: '测试工具',
        path: '/tools/testing',
      },
      {
        key: 'tools-performance',
        label: '性能分析',
        path: '/tools/performance',
      },
      {
        key: 'tools-code-generator',
        label: '代码生成器',
        path: '/tools/code-generator',
      },
    ],
  },
  {
    key: 'system',
    label: '系统设置',
    icon: SettingOutlined,
    children: [
      {
        key: 'system-config',
        label: '系统配置',
        path: '/system/config',
      },
      {
        key: 'system-environment',
        label: '环境管理',
        path: '/system/environment',
      },
      {
        key: 'system-maintenance',
        label: '系统维护',
        path: '/system/maintenance',
      },
      {
        key: 'system-about',
        label: '关于系统',
        path: '/system/about',
      },
    ],
  },
];
