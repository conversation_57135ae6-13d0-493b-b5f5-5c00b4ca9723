using FlowCustomV1.Core.Models.Plugins;
using FlowCustomV1.Core.Models.Workflow;

namespace FlowCustomV1.Core.Interfaces.Plugins;

/// <summary>
/// 动态节点编译器接口
/// 基于Natasha实现运行时C#代码编译
/// </summary>
public interface IDynamicNodeCompiler
{
    /// <summary>
    /// 初始化编译器
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>初始化任务</returns>
    Task InitializeAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 编译节点执行器代码
    /// </summary>
    /// <param name="sourceCode">源代码</param>
    /// <param name="nodeType">节点类型</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>编译结果</returns>
    Task<CompilationResult> CompileNodeExecutorAsync(
        string sourceCode, 
        string nodeType, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 从JSON配置编译节点执行器
    /// </summary>
    /// <param name="jsonConfig">JSON配置</param>
    /// <param name="nodeType">节点类型</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>编译结果</returns>
    Task<CompilationResult> CompileFromJsonConfigAsync(
        string jsonConfig, 
        string nodeType, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 从内置插件定义编译节点执行器
    /// </summary>
    /// <param name="pluginDefinition">插件定义</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>编译结果</returns>
    Task<CompilationResult> CompileFromBuiltinDefinitionAsync(
        BuiltinPluginDefinition pluginDefinition, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取编译缓存中的执行器
    /// </summary>
    /// <param name="nodeType">节点类型</param>
    /// <returns>缓存的执行器</returns>
    INodeExecutor? GetCachedExecutor(string nodeType);

    /// <summary>
    /// 清除编译缓存
    /// </summary>
    /// <param name="nodeType">节点类型，为空则清除所有缓存</param>
    /// <returns>清除任务</returns>
    Task ClearCacheAsync(string? nodeType = null);

    /// <summary>
    /// 预编译常用节点
    /// </summary>
    /// <param name="nodeTypes">节点类型列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>预编译任务</returns>
    Task PrecompileNodesAsync(IEnumerable<string> nodeTypes, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取编译统计信息
    /// </summary>
    /// <returns>编译统计信息</returns>
    CompilationStatistics GetCompilationStatistics();

    /// <summary>
    /// 验证源代码语法
    /// </summary>
    /// <param name="sourceCode">源代码</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    Task<ValidationResult> ValidateSourceCodeAsync(string sourceCode, CancellationToken cancellationToken = default);

    /// <summary>
    /// 编译完成事件
    /// </summary>
    event EventHandler<CompilationCompletedEventArgs> CompilationCompleted;

    /// <summary>
    /// 编译错误事件
    /// </summary>
    event EventHandler<CompilationErrorEventArgs> CompilationError;
}
