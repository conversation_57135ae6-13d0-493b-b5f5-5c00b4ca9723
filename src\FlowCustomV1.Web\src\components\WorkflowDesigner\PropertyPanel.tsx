import React, { useState, useEffect } from 'react';
import { Card, Form, Input, Button, Space, Badge, Tooltip } from 'antd';
import { DeleteOutlined, EditOutlined, InfoCircleOutlined } from '@ant-design/icons';
import type { WorkflowNode } from '@/types/workflow';
import { getNodeDefinition } from '@/constants/nodeDefinitions';

const { TextArea } = Input;

interface PropertyPanelProps {
  selectedNode: WorkflowNode | null;
  onNodeUpdate?: (nodeId: string, updates: Partial<WorkflowNode['data']>) => void;
  onNodeDelete?: (nodeId: string) => void;
}

const PropertyPanel: React.FC<PropertyPanelProps> = ({
  selectedNode,
  onNodeUpdate,
  onNodeDelete,
}) => {
  const [form] = Form.useForm();
  const [isEditing, setIsEditing] = useState(false);

  // 当选中节点变化时，更新表单
  useEffect(() => {
    if (selectedNode) {
      form.setFieldsValue({
        label: selectedNode.data.label,
        description: selectedNode.data.description || '',
        ...selectedNode.data.properties,
      });
      setIsEditing(false);
    }
  }, [selectedNode, form]);

  // 保存节点属性
  const handleSave = async () => {
    if (!selectedNode) return;

    try {
      const values = await form.validateFields();
      const { label, description, ...properties } = values;

      onNodeUpdate?.(selectedNode.id, {
        label,
        description,
        properties,
      });

      setIsEditing(false);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 取消编辑
  const handleCancel = () => {
    if (selectedNode) {
      form.setFieldsValue({
        label: selectedNode.data.label,
        description: selectedNode.data.description || '',
        ...selectedNode.data.properties,
      });
    }
    setIsEditing(false);
  };

  // 删除节点
  const handleDelete = () => {
    if (selectedNode) {
      onNodeDelete?.(selectedNode.id);
    }
  };

  if (!selectedNode) {
    return (
      <div className="w-80 bg-white border-l flex flex-col h-full">
        <div className="p-4 border-b">
          <h3 className="font-semibold text-gray-800">属性面板</h3>
        </div>
        <div className="flex-1 flex items-center justify-center text-gray-500">
          <div className="text-center">
            <InfoCircleOutlined className="text-2xl mb-2" />
            <p>请选择一个节点</p>
          </div>
        </div>
      </div>
    );
  }

  const nodeDefinition = getNodeDefinition(selectedNode.type);

  return (
    <div className="w-80 bg-white border-l flex flex-col h-full">
      {/* 标题栏 */}
      <div className="p-4 border-b">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <h3 className="font-semibold text-gray-800">节点属性</h3>
            <Badge 
              color={nodeDefinition?.color || '#1890ff'} 
              text={nodeDefinition?.displayName || selectedNode.type}
            />
          </div>
          <Space>
            {!isEditing ? (
              <Button
                type="text"
                icon={<EditOutlined />}
                onClick={() => setIsEditing(true)}
                size="small"
              />
            ) : null}
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              onClick={handleDelete}
              size="small"
            />
          </Space>
        </div>
      </div>

      {/* 表单内容 */}
      <div className="flex-1 overflow-auto p-4">
        <Form
          form={form}
          layout="vertical"
          disabled={!isEditing}
          size="small"
        >
          {/* 基本信息 */}
          <Card title="基本信息" size="small" className="mb-4">
            <Form.Item
              label="节点名称"
              name="label"
              rules={[{ required: true, message: '请输入节点名称' }]}
            >
              <Input placeholder="请输入节点名称" />
            </Form.Item>

            <Form.Item
              label="节点描述"
              name="description"
            >
              <TextArea 
                placeholder="请输入节点描述" 
                rows={2}
                showCount
                maxLength={200}
              />
            </Form.Item>

            <Form.Item label="节点类型">
              <Input value={nodeDefinition?.displayName || selectedNode.type} disabled />
            </Form.Item>

            <Form.Item label="节点ID">
              <Input value={selectedNode.id} disabled />
            </Form.Item>
          </Card>

          {/* 状态信息 */}
          <Card title="状态信息" size="small">
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">执行状态:</span>
                <Badge 
                  status={
                    selectedNode.data.status === 'success' ? 'success' :
                    selectedNode.data.status === 'error' ? 'error' :
                    selectedNode.data.status === 'running' ? 'processing' : 'default'
                  }
                  text={
                    selectedNode.data.status === 'success' ? '成功' :
                    selectedNode.data.status === 'error' ? '失败' :
                    selectedNode.data.status === 'running' ? '运行中' : '待执行'
                  }
                />
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">位置:</span>
                <span>({selectedNode.position.x}, {selectedNode.position.y})</span>
              </div>
              {selectedNode.data.error && (
                <div>
                  <span className="text-gray-600">错误信息:</span>
                  <div className="text-red-500 text-xs mt-1 p-2 bg-red-50 rounded">
                    {selectedNode.data.error}
                  </div>
                </div>
              )}
            </div>
          </Card>
        </Form>
      </div>

      {/* 操作按钮 */}
      {isEditing && (
        <div className="p-4 border-t bg-gray-50">
          <Space className="w-full justify-end">
            <Button onClick={handleCancel}>
              取消
            </Button>
            <Button type="primary" onClick={handleSave}>
              保存
            </Button>
          </Space>
        </div>
      )}
    </div>
  );
};

export default PropertyPanel;
