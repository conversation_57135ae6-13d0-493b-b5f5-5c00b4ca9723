using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Extensions.Logging;
using FlowCustomV1.Core.Interfaces;
using FlowCustomV1.Core.Services;

namespace FlowCustomV1.Core.Extensions;

/// <summary>
/// 服务集合扩展方法
/// 提供依赖注入容器配置和服务注册功能
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// 添加FlowCustomV1核心服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">配置对象</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddFlowCustomV1Core(this IServiceCollection services, IConfiguration configuration)
    {
        ArgumentNullException.ThrowIfNull(services);
        ArgumentNullException.ThrowIfNull(configuration);

        // 注册配置对象
        services.AddSingleton(configuration);

        // 注册日志服务（需要先注册Microsoft.Extensions.Logging）
        services.AddLogging();
        services.AddSingleton<ILoggingService, LoggingService>();

        // 注册配置服务
        services.AddSingleton<IConfigurationService, ConfigurationService>();

        // 注册验证服务
        services.AddSingleton<IValidationService, ValidationService>();

        // 注册核心接口的基础实现（注意：完整实现在Engine和Infrastructure层）
        services.TryAddScoped<INodeExecutor, NodeExecutor>();
        services.TryAddScoped<IClusterService, ClusterService>();

        // 注意：IWorkflowEngine的实现在Engine层，由Infrastructure层统一注册

        return services;
    }

    /// <summary>
    /// 添加FlowCustomV1日志服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configureLogging">日志配置委托</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddFlowCustomV1Logging(this IServiceCollection services, Action<ILoggingBuilder>? configureLogging = null)
    {
        ArgumentNullException.ThrowIfNull(services);

        // 添加Microsoft.Extensions.Logging（只添加抽象，具体提供程序由调用方添加）
        services.AddLogging(builder =>
        {
            builder.ClearProviders();

            // 应用自定义配置
            configureLogging?.Invoke(builder);
        });

        // 注册自定义日志服务
        services.AddSingleton<ILoggingService, LoggingService>();

        return services;
    }

    /// <summary>
    /// 添加FlowCustomV1配置服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">配置对象</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddFlowCustomV1Configuration(this IServiceCollection services, IConfiguration configuration)
    {
        ArgumentNullException.ThrowIfNull(services);
        ArgumentNullException.ThrowIfNull(configuration);

        // 注册配置对象
        services.AddSingleton(configuration);

        // 注册配置服务
        services.AddSingleton<IConfigurationService, ConfigurationService>();

        return services;
    }

    /// <summary>
    /// 添加FlowCustomV1工作流引擎服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddFlowCustomV1WorkflowEngine(this IServiceCollection services)
    {
        ArgumentNullException.ThrowIfNull(services);

        // 确保日志服务已注册
        services.AddLogging();
        services.TryAddSingleton<ILoggingService, LoggingService>();

        // 确保配置服务已注册（WorkflowEngine的依赖）
        services.TryAddSingleton<IConfigurationService, ConfigurationService>();

        // 确保节点执行器已注册（WorkflowEngine的依赖）
        services.TryAddScoped<INodeExecutor, NodeExecutor>();

        // 注意：IWorkflowEngine的实现在Engine层，由Infrastructure层统一注册
        // 这里不再注册，避免与Engine层的实现冲突

        return services;
    }

    /// <summary>
    /// 添加FlowCustomV1节点执行器服务（已弃用）
    /// 注意：此方法已被Engine层的AddNodeExecutors替代
    /// 建议使用Engine层的服务注册以获得完整的节点执行器支持
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    [Obsolete("Use Engine layer's AddNodeExecutors method instead for complete node executor support", false)]
    public static IServiceCollection AddFlowCustomV1NodeExecutor(this IServiceCollection services)
    {
        ArgumentNullException.ThrowIfNull(services);

        // 确保日志服务已注册
        services.AddLogging();
        services.TryAddSingleton<ILoggingService, LoggingService>();

        // 注册基础节点执行器（仅用于向后兼容）
        services.TryAddScoped<INodeExecutor, NodeExecutor>();

        return services;
    }

    /// <summary>
    /// 添加FlowCustomV1集群服务（已弃用）
    /// 注意：此方法已被Infrastructure层的NodeDiscoveryService替代
    /// 建议使用Infrastructure层的服务注册以获得完整的分布式集群支持
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    [Obsolete("Use Infrastructure layer's NodeDiscoveryService instead for complete distributed cluster support", false)]
    public static IServiceCollection AddFlowCustomV1ClusterService(this IServiceCollection services)
    {
        ArgumentNullException.ThrowIfNull(services);

        // 确保日志服务已注册
        services.AddLogging();
        services.TryAddSingleton<ILoggingService, LoggingService>();

        // 注册基础集群服务（仅用于向后兼容）
        services.TryAddScoped<IClusterService, ClusterService>();

        return services;
    }

    /// <summary>
    /// 验证服务注册
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>验证结果</returns>
    public static ServiceRegistrationValidationResult ValidateServiceRegistration(this IServiceCollection services)
    {
        ArgumentNullException.ThrowIfNull(services);

        var result = new ServiceRegistrationValidationResult();
        var requiredServices = new[]
        {
            typeof(ILoggingService),
            typeof(IConfigurationService),
            // 注意：IWorkflowEngine的实现在Engine层，Core层不注册此服务
            // typeof(IWorkflowEngine),
            typeof(INodeExecutor),
            typeof(IClusterService)
        };

        foreach (var serviceType in requiredServices)
        {
            if (!services.Any(s => s.ServiceType == serviceType))
            {
                result.MissingServices.Add(serviceType.Name);
            }
        }

        result.IsValid = result.MissingServices.Count == 0;
        return result;
    }
}

/// <summary>
/// 服务注册验证结果
/// </summary>
public class ServiceRegistrationValidationResult
{
    /// <summary>
    /// 验证是否成功
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 缺失的服务
    /// </summary>
    public IList<string> MissingServices { get; set; } = new List<string>();

    /// <summary>
    /// 验证错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }
}
