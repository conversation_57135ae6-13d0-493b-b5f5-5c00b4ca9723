using FlowCustomV1.Core.Models.Cluster;
using FlowCustomV1.Core.Models.Messages;

namespace FlowCustomV1.Core.Interfaces;

/// <summary>
/// 集群服务接口
/// 提供集群管理、节点发现、负载均衡等核心功能
/// </summary>
public interface IClusterService
{
    /// <summary>
    /// 当前节点信息
    /// </summary>
    NodeInfo CurrentNode { get; }

    /// <summary>
    /// 集群是否已初始化
    /// </summary>
    bool IsInitialized { get; }

    /// <summary>
    /// 集群名称
    /// </summary>
    string ClusterName { get; }

    /// <summary>
    /// 初始化集群服务
    /// </summary>
    /// <param name="clusterName">集群名称</param>
    /// <param name="nodeInfo">当前节点信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>初始化任务</returns>
    Task InitializeAsync(
        string clusterName, 
        NodeInfo nodeInfo, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 启动集群服务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>启动任务</returns>
    Task StartAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 停止集群服务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>停止任务</returns>
    Task StopAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 注册节点到集群
    /// </summary>
    /// <param name="nodeInfo">节点信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>注册任务</returns>
    Task RegisterNodeAsync(
        NodeInfo nodeInfo,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 从集群注销节点
    /// </summary>
    /// <param name="nodeId">节点ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>注销任务</returns>
    Task UnregisterNodeAsync(
        string nodeId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 发现集群节点
    /// </summary>
    /// <param name="query">发现查询条件</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>节点列表</returns>
    Task<IEnumerable<NodeInfo>> DiscoverNodesAsync(
        NodeDiscoveryQuery? query = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取所有集群节点
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>节点列表</returns>
    Task<IEnumerable<NodeInfo>> GetAllNodesAsync(
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取指定节点信息
    /// </summary>
    /// <param name="nodeId">节点ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>节点信息</returns>
    Task<NodeInfo?> GetNodeAsync(
        string nodeId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 更新节点信息
    /// </summary>
    /// <param name="nodeInfo">节点信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>更新任务</returns>
    Task UpdateNodeAsync(
        NodeInfo nodeInfo,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 选择最佳节点执行任务
    /// </summary>
    /// <param name="requirements">执行要求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>最佳节点</returns>
    Task<NodeInfo?> SelectBestNodeAsync(
        NodeExecutionRequirements? requirements = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 发送心跳
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>心跳任务</returns>
    Task SendHeartbeatAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 检查节点健康状态
    /// </summary>
    /// <param name="nodeId">节点ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>健康检查结果</returns>
    Task<ClusterHealthCheckResult> CheckNodeHealthAsync(
        string nodeId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取集群统计信息
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>集群统计信息</returns>
    Task<ClusterStats> GetClusterStatsAsync(
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 广播消息到所有节点
    /// </summary>
    /// <param name="message">消息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>广播任务</returns>
    Task BroadcastMessageAsync(
        ClusterMessage message,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 发送消息到指定节点
    /// </summary>
    /// <param name="nodeId">目标节点ID</param>
    /// <param name="message">消息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送任务</returns>
    Task SendMessageToNodeAsync(
        string nodeId,
        ClusterMessage message,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 集群事件：节点加入
    /// </summary>
    event EventHandler<NodeJoinedEventArgs>? NodeJoined;

    /// <summary>
    /// 集群事件：节点离开
    /// </summary>
    event EventHandler<NodeLeftEventArgs>? NodeLeft;

    /// <summary>
    /// 集群事件：节点状态变化
    /// </summary>
    event EventHandler<NodeStatusChangedEventArgs>? NodeStatusChanged;

    /// <summary>
    /// 集群事件：消息接收
    /// </summary>
    event EventHandler<MessageReceivedEventArgs>? MessageReceived;
}

/// <summary>
/// 节点加入事件参数
/// </summary>
public class NodeJoinedEventArgs : EventArgs
{
    /// <summary>
    /// 加入的节点信息
    /// </summary>
    public NodeInfo NodeInfo { get; set; } = new();

    /// <summary>
    /// 加入时间
    /// </summary>
    public DateTime JoinedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 节点离开事件参数
/// </summary>
public class NodeLeftEventArgs : EventArgs
{
    /// <summary>
    /// 离开的节点ID
    /// </summary>
    public string NodeId { get; set; } = string.Empty;

    /// <summary>
    /// 离开原因
    /// </summary>
    public string Reason { get; set; } = string.Empty;

    /// <summary>
    /// 离开时间
    /// </summary>
    public DateTime LeftAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 节点状态变化事件参数
/// </summary>
public class NodeStatusChangedEventArgs : EventArgs
{
    /// <summary>
    /// 节点ID
    /// </summary>
    public string NodeId { get; set; } = string.Empty;

    /// <summary>
    /// 旧状态
    /// </summary>
    public NodeStatus OldStatus { get; set; } = NodeStatus.Unknown;

    /// <summary>
    /// 新状态
    /// </summary>
    public NodeStatus NewStatus { get; set; } = NodeStatus.Unknown;

    /// <summary>
    /// 变化时间
    /// </summary>
    public DateTime ChangedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 消息接收事件参数
/// </summary>
public class MessageReceivedEventArgs : EventArgs
{
    /// <summary>
    /// 接收的消息
    /// </summary>
    public ClusterMessage Message { get; set; } = new NodeHeartbeatMessage();

    /// <summary>
    /// 发送者节点ID
    /// </summary>
    public string SenderId { get; set; } = string.Empty;

    /// <summary>
    /// 接收时间
    /// </summary>
    public DateTime ReceivedAt { get; set; } = DateTime.UtcNow;
}
