using FlowCustomV1.Core.Models.Cluster;
using FlowCustomV1.Infrastructure.Configuration;
using Xunit;

namespace FlowCustomV1.Core.Tests;

/// <summary>
/// 节点发现服务单元测试
/// </summary>
public class NodeDiscoveryServiceUnitTests
{
    [Fact]
    public void NodeDiscoveryConfiguration_ShouldHaveValidDefaults()
    {
        // Arrange & Act
        var config = new NodeDiscoveryConfiguration();

        // Assert
        Assert.Equal("FlowCustomV1", config.ClusterName);
        Assert.Equal(30, config.HeartbeatIntervalSeconds);
        Assert.Equal(120, config.NodeTimeoutSeconds);
        Assert.Equal(60, config.NodeCleanupIntervalSeconds);
        Assert.Equal(5, config.DiscoveryTimeoutSeconds);
        Assert.True(config.EnableAutoRegistration);
        Assert.True(config.EnableHeartbeat);
        Assert.True(config.EnableNodeCleanup);
        Assert.Equal(3, config.MaxRetryAttempts);
        Assert.Equal(5, config.RetryIntervalSeconds);
        Assert.True(config.IsValid());
    }

    [Fact]
    public void NodeDiscoveryConfiguration_IsValid_ShouldReturnFalseForInvalidConfig()
    {
        // Arrange
        var config = new NodeDiscoveryConfiguration
        {
            ClusterName = "", // Invalid
            HeartbeatIntervalSeconds = 0, // Invalid
            NodeTimeoutSeconds = 10, // Invalid (should be > heartbeat)
            DiscoveryTimeoutSeconds = 0 // Invalid
        };

        // Act & Assert
        Assert.False(config.IsValid());
    }

    [Fact]
    public void NodeInfo_ShouldCreateValidInstance()
    {
        // Arrange & Act
        var nodeInfo = new NodeInfo
        {
            NodeId = "test-node-001",
            NodeName = "Test Node",
            ClusterName = "TestCluster",
            Mode = NodeMode.Worker,
            Status = NodeStatus.Healthy,
            Network = new NetworkInfo
            {
                IpAddress = "*************",
                HttpPort = 5000,
                NatsPort = 4222,
                ManagementPort = 8080,
                HostName = "test-host"
            },
            Capabilities = new NodeCapabilities
            {
                CpuCores = 4,
                MemoryMb = 8192,
                MaxConcurrentExecutions = 10,
                PerformanceLevel = 7,
                Tags = new HashSet<string> { "test", "worker" }
            },
            Load = new NodeLoad
            {
                MaxTaskCapacity = 10,
                CpuUsagePercentage = 50.0,
                MemoryUsagePercentage = 60.0,
                ActiveTaskCount = 3,
                QueuedTaskCount = 1
            },
            Health = new HealthStatus
            {
                IsHealthy = true,
                HealthScore = 95.0
            },
            Timestamps = new Timestamps
            {
                CreatedAt = DateTime.UtcNow,
                LastActiveAt = DateTime.UtcNow
            }
        };

        // Assert
        Assert.True(nodeInfo.IsValid());
        Assert.Equal("test-node-001", nodeInfo.NodeId);
        Assert.Equal("Test Node", nodeInfo.NodeName);
        Assert.Equal("TestCluster", nodeInfo.ClusterName);
        Assert.Equal(NodeMode.Worker, nodeInfo.Mode);
        Assert.Equal(NodeStatus.Healthy, nodeInfo.Status);
        Assert.NotNull(nodeInfo.Network);
        Assert.NotNull(nodeInfo.Capabilities);
        Assert.NotNull(nodeInfo.Load);
        Assert.NotNull(nodeInfo.Health);
        Assert.NotNull(nodeInfo.Timestamps);
    }

    [Fact]
    public void NodeLoad_CalculateLoadScore_ShouldReturnCorrectValue()
    {
        // Arrange
        var nodeLoad = new NodeLoad
        {
            CpuUsagePercentage = 70.0,
            MemoryUsagePercentage = 60.0,
            ActiveTaskCount = 8,
            MaxTaskCapacity = 10
        };

        // Act
        var loadScore = nodeLoad.CalculateLoadScore();

        // Assert
        Assert.True(loadScore > 0);
        Assert.True(loadScore <= 100);
        
        // 负载评分应该反映CPU、内存和任务负载
        // 高负载应该有高评分
        Assert.True(loadScore > 50); // 因为CPU和内存使用率都比较高
    }

    [Fact]
    public void ClusterTopology_ShouldCalculateCorrectStatistics()
    {
        // Arrange
        var topology = new ClusterTopology
        {
            ClusterName = "TestCluster",
            Nodes = new List<NodeInfo>
            {
                new NodeInfo
                {
                    NodeId = "node-1",
                    Mode = NodeMode.Worker,
                    Status = NodeStatus.Healthy
                },
                new NodeInfo
                {
                    NodeId = "node-2",
                    Mode = NodeMode.Master,
                    Status = NodeStatus.Healthy
                },
                new NodeInfo
                {
                    NodeId = "node-3",
                    Mode = NodeMode.Worker,
                    Status = NodeStatus.Offline
                }
            },
            NodeConnections = new List<NodeConnection>
            {
                new NodeConnection
                {
                    SourceNodeId = "node-1",
                    TargetNodeId = "nats-server",
                    IsActive = true
                },
                new NodeConnection
                {
                    SourceNodeId = "node-2",
                    TargetNodeId = "nats-server", 
                    IsActive = false
                }
            }
        };

        // Act & Assert
        Assert.Equal(3, topology.Nodes.Count);
        Assert.Equal(2, topology.OnlineNodeCount);
        Assert.Equal(1, topology.OfflineNodeCount);
        Assert.Equal(1, topology.ActiveConnectionCount);
        
        // 检查按角色分组的统计
        var nodesByRole = topology.NodesByRole;
        Assert.Equal(2, nodesByRole["Worker"]);
        Assert.Equal(1, nodesByRole["Master"]);
        
        // 检查按状态分组的统计
        var nodesByStatus = topology.NodesByStatus;
        Assert.Equal(2, nodesByStatus["Healthy"]);
        Assert.Equal(1, nodesByStatus["Offline"]);
    }

    [Fact]
    public void ClusterOverview_ShouldCalculateResourceUsage()
    {
        // Arrange
        var overview = new ClusterOverview
        {
            TotalCpuCores = 16,
            UsedCpuCores = 8,
            TotalMemoryMB = 32768,
            UsedMemoryMB = 16384
        };

        // Act & Assert
        Assert.Equal(50.0, overview.CpuUsagePercentage);
        Assert.Equal(50.0, overview.MemoryUsagePercentage);
    }

    [Fact]
    public void ClusterHealthStatus_ShouldDetermineCorrectHealthLevel()
    {
        // Arrange & Act
        var excellentHealth = new ClusterHealthStatus { HealthScore = 90 };
        var goodHealth = new ClusterHealthStatus { HealthScore = 70 };
        var warningHealth = new ClusterHealthStatus { HealthScore = 50 };
        var criticalHealth = new ClusterHealthStatus { HealthScore = 30 };

        // Assert
        Assert.Equal(HealthLevel.Excellent, excellentHealth.HealthLevel);
        Assert.Equal(HealthLevel.Good, goodHealth.HealthLevel);
        Assert.Equal(HealthLevel.Warning, warningHealth.HealthLevel);
        Assert.Equal(HealthLevel.Critical, criticalHealth.HealthLevel);
    }

    [Theory]
    [InlineData("", false)] // 空节点ID
    [InlineData("valid-node-id", true)] // 有效节点ID
    public void NodeInfo_IsValid_ShouldValidateNodeId(string nodeId, bool expectedValid)
    {
        // Arrange
        var nodeInfo = new NodeInfo
        {
            NodeId = nodeId,
            NodeName = "Test Node",
            ClusterName = "TestCluster",
            Mode = NodeMode.Worker,
            Status = NodeStatus.Healthy,
            Network = new NetworkInfo
            {
                IpAddress = "*************",
                HttpPort = 5000,
                NatsPort = 4222,
                ManagementPort = 8080,
                HostName = "test-host"
            },
            Capabilities = new NodeCapabilities
            {
                CpuCores = 4,
                MemoryMb = 8192,
                MaxConcurrentExecutions = 10,
                PerformanceLevel = 7
            },
            Load = new NodeLoad(),
            Health = new HealthStatus(),
            Timestamps = new Timestamps()
        };

        // Act & Assert
        Assert.Equal(expectedValid, nodeInfo.IsValid());
    }

    [Fact]
    public void NodeConnection_ShouldHaveValidDefaults()
    {
        // Arrange & Act
        var connection = new NodeConnection
        {
            SourceNodeId = "node-1",
            TargetNodeId = "node-2",
            ConnectionType = "NATS"
        };

        // Assert
        Assert.True(connection.IsActive);
        Assert.Equal(100.0, connection.QualityScore);
        Assert.Equal(0, connection.LatencyMs);
        Assert.Equal(0, connection.BandwidthMbps);
        Assert.True(connection.LastSeen <= DateTime.UtcNow);
        Assert.True(connection.EstablishedAt <= DateTime.UtcNow);
        Assert.NotNull(connection.Metadata);
    }
}
