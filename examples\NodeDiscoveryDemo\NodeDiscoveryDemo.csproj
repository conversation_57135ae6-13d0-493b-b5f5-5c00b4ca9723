<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\FlowCustomV1.Core\FlowCustomV1.Core.csproj" />
    <ProjectReference Include="..\..\src\FlowCustomV1.Infrastructure\FlowCustomV1.Infrastructure.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.8" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="9.0.8" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="9.0.8" />
    <PackageReference Include="Microsoft.Extensions.Options" Version="9.0.8" />
  </ItemGroup>

</Project>
