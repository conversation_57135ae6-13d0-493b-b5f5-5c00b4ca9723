# 纯CSS布局配置系统

## 🎯 设计理念

**"一个CSS文件控制所有布局"** - 通过CSS自定义属性（CSS变量），实现全局布局的统一管理，无需JavaScript。

## 📁 文件结构

```
src/styles/
├── layout-config.css    # 🎯 核心配置文件 - 只需修改这里！
├── layout-base.css      # 📐 基础样式（使用CSS变量）
└── global.css          # 🌐 全局样式
```

## 🔧 使用方法

### 1. 基础配置调整

直接修改 `layout-config.css` 中的CSS变量：

```css
:root {
  /* 只需修改这些值 */
  --layout-header-height: 64px;        /* 头部高度 */
  --layout-content-padding: 8px;       /* 内容边距 */
  --layout-table-offset: 320px;        /* 表格偏移 */
  --layout-toolbar-margin: 16px;       /* 工具栏边距 */
}
```

### 2. 预设布局

在 `body` 标签上添加预设类：

```html
<!-- 紧凑布局 -->
<body class="layout-preset-compact">

<!-- 标准布局（默认） -->
<body class="layout-preset-standard">

<!-- 宽松布局 -->
<body class="layout-preset-spacious">
```

### 3. 快速调整

在容器上添加调整类：

```html
<!-- 表格高度调整 -->
<div class="table-height-small">    <!-- 小表格 -->
<div class="table-height-medium">   <!-- 中表格 -->
<div class="table-height-large">    <!-- 大表格 -->

<!-- 间距调整 -->
<div class="spacing-tight">          <!-- 紧凑间距 -->
<div class="spacing-normal">         <!-- 正常间距 -->
<div class="spacing-loose">          <!-- 宽松间距 -->

<!-- 工具栏边距调整 -->
<div class="toolbar-margin-small">   <!-- 小边距 -->
<div class="toolbar-margin-medium">  <!-- 中边距 -->
<div class="toolbar-margin-large">   <!-- 大边距 -->
```

### 4. 主题适配

```html
<!-- 深色主题 -->
<body class="theme-dark">

<!-- 高对比度主题 -->
<body class="theme-high-contrast">

<!-- 组合使用 -->
<body class="layout-preset-standard theme-dark">
```

### 5. 调试模式

```html
<!-- 开启调试模式，显示容器边框 -->
<body class="layout-debug">
```

## 🎨 配置面板

使用可视化配置面板进行实时调整：

```tsx
import LayoutConfigPanel from '@/components/Layout/LayoutConfigPanel';

const [showConfig, setShowConfig] = useState(false);

<Button onClick={() => setShowConfig(true)}>布局设置</Button>
<LayoutConfigPanel visible={showConfig} onClose={() => setShowConfig(false)} />
```

## 📱 响应式支持

系统自动适配不同屏幕尺寸：

- **移动端** (≤768px)：紧凑布局，较小间距
- **平板端** (769px-1024px)：适中布局
- **桌面端** (>1024px)：标准布局

## 🔍 调试技巧

### 1. 浏览器开发者工具

在Elements面板中可以实时修改CSS变量：

```css
/* 在:root选择器中修改 */
--layout-table-offset: 300px;  /* 实时预览效果 */
```

### 2. 控制台快速调整

```javascript
// 修改CSS变量
document.documentElement.style.setProperty('--layout-table-offset', '300px');

// 切换预设
document.body.className = 'layout-preset-compact';

// 开启调试模式
document.body.classList.add('layout-debug');
```

## 📋 常用配置示例

### 紧凑型工作台

```css
:root {
  --layout-header-height: 56px;
  --layout-content-padding: 4px;
  --layout-table-offset: 280px;
  --layout-toolbar-margin: 8px;
}
```

### 宽松型展示页

```css
:root {
  --layout-header-height: 72px;
  --layout-content-padding: 16px;
  --layout-table-offset: 360px;
  --layout-toolbar-margin: 24px;
}
```

### 移动端优化

```css
@media (max-width: 768px) {
  :root {
    --layout-header-height: 56px;
    --layout-content-padding: 12px;
    --layout-table-offset: 240px;
  }
}
```

## 🚀 最佳实践

1. **统一配置**：所有布局参数都在 `layout-config.css` 中管理
2. **预设优先**：优先使用预设配置，减少自定义
3. **响应式**：利用媒体查询自动适配不同设备
4. **调试模式**：开发时使用调试模式查看布局结构
5. **渐进增强**：从标准配置开始，根据需要微调

## 🔧 扩展配置

如需添加新的配置项：

1. 在 `layout-config.css` 中添加CSS变量
2. 在 `layout-base.css` 中使用该变量
3. 在配置面板中添加对应的控制项

```css
/* 1. 添加新变量 */
:root {
  --layout-sidebar-width: 240px;
}

/* 2. 使用变量 */
.sidebar {
  width: var(--layout-sidebar-width);
}
```

## 📝 注意事项

- CSS变量名以 `--layout-` 开头，保持命名一致性
- 修改配置后刷新页面查看效果
- 调试模式会显示边框，生产环境记得关闭
- 响应式配置会覆盖基础配置，注意优先级
