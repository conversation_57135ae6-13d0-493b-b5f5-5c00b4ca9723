using FlowCustomV1.Core.Interfaces.Services;

namespace FlowCustomV1.Api.Services;

/// <summary>
/// 节点发现后台服务
/// 负责启动和管理节点发现服务的生命周期
/// </summary>
public class NodeDiscoveryHostedService : BackgroundService
{
    private readonly ILogger<NodeDiscoveryHostedService> _logger;
    private readonly IServiceProvider _serviceProvider;
    private INodeDiscoveryService? _nodeDiscoveryService;

    /// <summary>
    /// 初始化节点发现后台服务
    /// </summary>
    public NodeDiscoveryHostedService(
        ILogger<NodeDiscoveryHostedService> logger,
        IServiceProvider serviceProvider)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
    }

    /// <summary>
    /// 启动后台服务
    /// </summary>
    public override async Task StartAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Starting NodeDiscoveryHostedService...");

        try
        {
            // 获取节点发现服务实例
            _nodeDiscoveryService = _serviceProvider.GetRequiredService<INodeDiscoveryService>();

            // 订阅事件
            SubscribeToEvents();

            // 启动节点发现服务
            await _nodeDiscoveryService.StartAsync(cancellationToken);

            _logger.LogInformation("NodeDiscoveryHostedService started successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start NodeDiscoveryHostedService");
            throw;
        }

        await base.StartAsync(cancellationToken);
    }

    /// <summary>
    /// 停止后台服务
    /// </summary>
    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Stopping NodeDiscoveryHostedService...");

        try
        {
            if (_nodeDiscoveryService != null)
            {
                // 取消订阅事件
                UnsubscribeFromEvents();

                // 停止节点发现服务
                await _nodeDiscoveryService.StopAsync(cancellationToken);
            }

            _logger.LogInformation("NodeDiscoveryHostedService stopped successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while stopping NodeDiscoveryHostedService");
        }

        await base.StopAsync(cancellationToken);
    }

    /// <summary>
    /// 执行后台任务
    /// </summary>
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogDebug("NodeDiscoveryHostedService is running");

        try
        {
            // 保持服务运行，直到取消
            while (!stoppingToken.IsCancellationRequested)
            {
                // 定期检查服务状态
                await CheckServiceHealthAsync(stoppingToken);

                // 等待一段时间再次检查
                await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
            }
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("NodeDiscoveryHostedService execution was cancelled");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in NodeDiscoveryHostedService execution");
        }
    }

    /// <summary>
    /// 订阅节点发现服务事件
    /// </summary>
    private void SubscribeToEvents()
    {
        if (_nodeDiscoveryService == null)
            return;

        _nodeDiscoveryService.NodeJoined += OnNodeJoined;
        _nodeDiscoveryService.NodeLeft += OnNodeLeft;
        _nodeDiscoveryService.NodeStatusChanged += OnNodeStatusChanged;
        _nodeDiscoveryService.HeartbeatReceived += OnHeartbeatReceived;

        _logger.LogDebug("Subscribed to NodeDiscoveryService events");
    }

    /// <summary>
    /// 取消订阅节点发现服务事件
    /// </summary>
    private void UnsubscribeFromEvents()
    {
        if (_nodeDiscoveryService == null)
            return;

        _nodeDiscoveryService.NodeJoined -= OnNodeJoined;
        _nodeDiscoveryService.NodeLeft -= OnNodeLeft;
        _nodeDiscoveryService.NodeStatusChanged -= OnNodeStatusChanged;
        _nodeDiscoveryService.HeartbeatReceived -= OnHeartbeatReceived;

        _logger.LogDebug("Unsubscribed from NodeDiscoveryService events");
    }

    /// <summary>
    /// 处理节点加入事件
    /// </summary>
    private void OnNodeJoined(object? sender, NodeJoinedEventArgs e)
    {
        _logger.LogInformation("Node joined cluster: {NodeId} ({NodeName}) - Role: {Role}", 
            e.Node.NodeId, e.Node.NodeName, e.Node.Mode);
    }

    /// <summary>
    /// 处理节点离开事件
    /// </summary>
    private void OnNodeLeft(object? sender, NodeLeftEventArgs e)
    {
        _logger.LogWarning("Node left cluster: {NodeId} - Reason: {Reason}", 
            e.NodeId, e.Reason);
    }

    /// <summary>
    /// 处理节点状态变更事件
    /// </summary>
    private void OnNodeStatusChanged(object? sender, NodeStatusChangedEventArgs e)
    {
        _logger.LogInformation("Node status changed: {NodeId} - {OldStatus} → {NewStatus}", 
            e.NodeId, e.OldStatus, e.NewStatus);
    }

    /// <summary>
    /// 处理心跳接收事件
    /// </summary>
    private void OnHeartbeatReceived(object? sender, HeartbeatReceivedEventArgs e)
    {
        _logger.LogDebug("Heartbeat received from node: {NodeId}", e.NodeId);
    }

    /// <summary>
    /// 检查服务健康状态
    /// </summary>
    private async Task CheckServiceHealthAsync(CancellationToken cancellationToken)
    {
        try
        {
            if (_nodeDiscoveryService == null)
            {
                _logger.LogWarning("NodeDiscoveryService is null");
                return;
            }

            if (!_nodeDiscoveryService.IsStarted)
            {
                _logger.LogWarning("NodeDiscoveryService is not started, attempting to restart...");
                await _nodeDiscoveryService.StartAsync(cancellationToken);
            }

            // 记录集群状态
            var onlineNodeCount = _nodeDiscoveryService.OnlineNodeCount;
            var totalNodeCount = _nodeDiscoveryService.KnownNodes.Count;
            
            _logger.LogDebug("Cluster status: {OnlineNodes}/{TotalNodes} nodes online", 
                onlineNodeCount, totalNodeCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking NodeDiscoveryService health");
        }
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public override void Dispose()
    {
        try
        {
            UnsubscribeFromEvents();
            _nodeDiscoveryService?.Dispose();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error disposing NodeDiscoveryHostedService");
        }

        base.Dispose();
    }
}
