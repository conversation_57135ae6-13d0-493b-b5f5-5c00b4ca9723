using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace FlowCustomV1.Core.Models.Designer;

/// <summary>
/// 协作会话信息
/// </summary>
public class CollaborationSession
{
    /// <summary>
    /// 会话唯一标识符
    /// </summary>
    [Required]
    [JsonPropertyName("sessionId")]
    public string SessionId { get; set; } = string.Empty;

    /// <summary>
    /// 工作流ID
    /// </summary>
    [Required]
    [JsonPropertyName("workflowId")]
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 会话名称
    /// </summary>
    [JsonPropertyName("name")]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 会话描述
    /// </summary>
    [JsonPropertyName("description")]
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 会话创建者ID
    /// </summary>
    [JsonPropertyName("createdBy")]
    public string CreatedBy { get; set; } = string.Empty;

    /// <summary>
    /// 会话创建时间
    /// </summary>
    [JsonPropertyName("createdAt")]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 会话状态
    /// </summary>
    [JsonPropertyName("status")]
    public SessionStatus Status { get; set; } = SessionStatus.Active;

    /// <summary>
    /// 最大协作者数量
    /// </summary>
    [JsonPropertyName("maxCollaborators")]
    public int MaxCollaborators { get; set; } = 10;

    /// <summary>
    /// 当前协作者列表
    /// </summary>
    [JsonPropertyName("collaborators")]
    public List<CollaboratorInfo> Collaborators { get; set; } = new();

    /// <summary>
    /// 会话配置
    /// </summary>
    [JsonPropertyName("settings")]
    public CollaborationSettings Settings { get; set; } = new();

    /// <summary>
    /// 最后活动时间
    /// </summary>
    [JsonPropertyName("lastActivityAt")]
    public DateTime LastActivityAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 会话结束时间
    /// </summary>
    [JsonPropertyName("endedAt")]
    public DateTime? EndedAt { get; set; }

    /// <summary>
    /// 会话元数据
    /// </summary>
    [JsonPropertyName("metadata")]
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// 协作会话信息（用于创建会话）
/// </summary>
public class CollaborationSessionInfo
{
    /// <summary>
    /// 会话名称
    /// </summary>
    [Required]
    [JsonPropertyName("name")]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 会话描述
    /// </summary>
    [JsonPropertyName("description")]
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 最大协作者数量
    /// </summary>
    [JsonPropertyName("maxCollaborators")]
    public int MaxCollaborators { get; set; } = 10;

    /// <summary>
    /// 会话配置
    /// </summary>
    [JsonPropertyName("settings")]
    public CollaborationSettings Settings { get; set; } = new();

    /// <summary>
    /// 创建者ID
    /// </summary>
    [JsonPropertyName("createdBy")]
    public string CreatedBy { get; set; } = string.Empty;
}

/// <summary>
/// 协作设置
/// </summary>
public class CollaborationSettings
{
    /// <summary>
    /// 是否允许匿名协作者
    /// </summary>
    [JsonPropertyName("allowAnonymous")]
    public bool AllowAnonymous { get; set; } = false;

    /// <summary>
    /// 是否启用实时同步
    /// </summary>
    [JsonPropertyName("enableRealTimeSync")]
    public bool EnableRealTimeSync { get; set; } = true;

    /// <summary>
    /// 是否启用冲突检测
    /// </summary>
    [JsonPropertyName("enableConflictDetection")]
    public bool EnableConflictDetection { get; set; } = true;

    /// <summary>
    /// 自动保存间隔（秒）
    /// </summary>
    [JsonPropertyName("autoSaveIntervalSeconds")]
    public int AutoSaveIntervalSeconds { get; set; } = 30;

    /// <summary>
    /// 协作者超时时间（分钟）
    /// </summary>
    [JsonPropertyName("collaboratorTimeoutMinutes")]
    public int CollaboratorTimeoutMinutes { get; set; } = 5;

    /// <summary>
    /// 是否记录操作历史
    /// </summary>
    [JsonPropertyName("recordHistory")]
    public bool RecordHistory { get; set; } = true;

    /// <summary>
    /// 历史记录保留天数
    /// </summary>
    [JsonPropertyName("historyRetentionDays")]
    public int HistoryRetentionDays { get; set; } = 30;

    /// <summary>
    /// 是否启用语音聊天
    /// </summary>
    [JsonPropertyName("enableVoiceChat")]
    public bool EnableVoiceChat { get; set; } = false;

    /// <summary>
    /// 是否启用文字聊天
    /// </summary>
    [JsonPropertyName("enableTextChat")]
    public bool EnableTextChat { get; set; } = true;
}

/// <summary>
/// 会话状态枚举
/// </summary>
public enum SessionStatus
{
    /// <summary>
    /// 活跃
    /// </summary>
    Active,

    /// <summary>
    /// 暂停
    /// </summary>
    Paused,

    /// <summary>
    /// 已结束
    /// </summary>
    Ended,

    /// <summary>
    /// 已过期
    /// </summary>
    Expired
}

/// <summary>
/// 冲突检测结果
/// </summary>
public class ConflictDetectionResult
{
    /// <summary>
    /// 是否存在冲突
    /// </summary>
    [JsonPropertyName("hasConflict")]
    public bool HasConflict { get; set; } = false;

    /// <summary>
    /// 冲突类型
    /// </summary>
    [JsonPropertyName("conflictType")]
    public ConflictType ConflictType { get; set; }

    /// <summary>
    /// 冲突ID
    /// </summary>
    [JsonPropertyName("conflictId")]
    public string ConflictId { get; set; } = string.Empty;

    /// <summary>
    /// 冲突描述
    /// </summary>
    [JsonPropertyName("description")]
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 涉及的协作者
    /// </summary>
    [JsonPropertyName("involvedCollaborators")]
    public List<string> InvolvedCollaborators { get; set; } = new();

    /// <summary>
    /// 冲突的操作
    /// </summary>
    [JsonPropertyName("conflictingOperations")]
    public List<DesignOperation> ConflictingOperations { get; set; } = new();

    /// <summary>
    /// 建议的解决方案
    /// </summary>
    [JsonPropertyName("suggestedResolutions")]
    public List<ConflictResolutionSuggestion> SuggestedResolutions { get; set; } = new();

    /// <summary>
    /// 检测时间
    /// </summary>
    [JsonPropertyName("detectedAt")]
    public DateTime DetectedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 设计冲突
/// </summary>
public class DesignConflict
{
    /// <summary>
    /// 冲突ID
    /// </summary>
    [JsonPropertyName("conflictId")]
    public string ConflictId { get; set; } = string.Empty;

    /// <summary>
    /// 会话ID
    /// </summary>
    [JsonPropertyName("sessionId")]
    public string SessionId { get; set; } = string.Empty;

    /// <summary>
    /// 冲突类型
    /// </summary>
    [JsonPropertyName("conflictType")]
    public ConflictType ConflictType { get; set; }

    /// <summary>
    /// 冲突状态
    /// </summary>
    [JsonPropertyName("status")]
    public ConflictStatus Status { get; set; } = ConflictStatus.Pending;

    /// <summary>
    /// 冲突描述
    /// </summary>
    [JsonPropertyName("description")]
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 涉及的协作者
    /// </summary>
    [JsonPropertyName("involvedCollaborators")]
    public List<CollaboratorInfo> InvolvedCollaborators { get; set; } = new();

    /// <summary>
    /// 冲突的操作
    /// </summary>
    [JsonPropertyName("conflictingOperations")]
    public List<DesignOperation> ConflictingOperations { get; set; } = new();

    /// <summary>
    /// 创建时间
    /// </summary>
    [JsonPropertyName("createdAt")]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 解决时间
    /// </summary>
    [JsonPropertyName("resolvedAt")]
    public DateTime? ResolvedAt { get; set; }

    /// <summary>
    /// 解决方案
    /// </summary>
    [JsonPropertyName("resolution")]
    public ConflictResolution? Resolution { get; set; }
}

/// <summary>
/// 冲突状态枚举
/// </summary>
public enum ConflictStatus
{
    /// <summary>
    /// 待处理
    /// </summary>
    Pending,

    /// <summary>
    /// 处理中
    /// </summary>
    InProgress,

    /// <summary>
    /// 已解决
    /// </summary>
    Resolved,

    /// <summary>
    /// 已忽略
    /// </summary>
    Ignored,

    /// <summary>
    /// 已过期
    /// </summary>
    Expired
}

/// <summary>
/// 冲突解决建议
/// </summary>
public class ConflictResolutionSuggestion
{
    /// <summary>
    /// 建议ID
    /// </summary>
    [JsonPropertyName("suggestionId")]
    public string SuggestionId { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 解决策略
    /// </summary>
    [JsonPropertyName("strategy")]
    public ConflictResolutionStrategy Strategy { get; set; }

    /// <summary>
    /// 建议描述
    /// </summary>
    [JsonPropertyName("description")]
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 优先级
    /// </summary>
    [JsonPropertyName("priority")]
    public int Priority { get; set; } = 0;

    /// <summary>
    /// 是否为自动解决
    /// </summary>
    [JsonPropertyName("isAutomatic")]
    public bool IsAutomatic { get; set; } = false;

    /// <summary>
    /// 预期结果
    /// </summary>
    [JsonPropertyName("expectedOutcome")]
    public string ExpectedOutcome { get; set; } = string.Empty;
}

/// <summary>
/// 操作应用结果
/// </summary>
public class OperationApplicationResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    [JsonPropertyName("success")]
    public bool Success { get; set; } = true;

    /// <summary>
    /// 成功应用的操作数量
    /// </summary>
    [JsonPropertyName("appliedCount")]
    public int AppliedCount { get; set; } = 0;

    /// <summary>
    /// 失败的操作数量
    /// </summary>
    [JsonPropertyName("failedCount")]
    public int FailedCount { get; set; } = 0;

    /// <summary>
    /// 跳过的操作数量
    /// </summary>
    [JsonPropertyName("skippedCount")]
    public int SkippedCount { get; set; } = 0;

    /// <summary>
    /// 错误信息列表
    /// </summary>
    [JsonPropertyName("errors")]
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// 警告信息列表
    /// </summary>
    [JsonPropertyName("warnings")]
    public List<string> Warnings { get; set; } = new();

    /// <summary>
    /// 应用时间
    /// </summary>
    [JsonPropertyName("appliedAt")]
    public DateTime AppliedAt { get; set; } = DateTime.UtcNow;
}
