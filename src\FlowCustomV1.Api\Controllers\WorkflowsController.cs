using FlowCustomV1.Core.Models.Workflow;
using FlowCustomV1.Core.Interfaces;
using FlowCustomV1.Api.Models;
using Microsoft.AspNetCore.Mvc;
using System.Text.Json;
using System.Text;

namespace FlowCustomV1.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
public class WorkflowsController(IWorkflowRepository workflowRepository, IWorkflowEngine workflowEngineService) : ControllerBase
{

    // GET: api/workflows
    [HttpGet]
    public async Task<ActionResult<IEnumerable<WorkflowDefinition>>> GetWorkflows(
        [FromQuery] string? search = null,
        [FromQuery] string? status = null,
        [FromQuery] DateTime? createdAfter = null,
        [FromQuery] DateTime? createdBefore = null,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 10)
    {
        try
        {
            var workflows = await workflowRepository.GetAllWorkflowDefinitionsAsync();

            // 应用搜索过滤器
            if (!string.IsNullOrWhiteSpace(search))
            {
                workflows = workflows.Where(w =>
                    w.Name.Contains(search, StringComparison.OrdinalIgnoreCase) ||
                    w.Description.Contains(search, StringComparison.OrdinalIgnoreCase));
            }

            // 应用状态过滤器
            if (!string.IsNullOrWhiteSpace(status))
            {
                workflows = workflows.Where(w => w.PublishStatus.ToString().Equals(status, StringComparison.OrdinalIgnoreCase));
            }

            // 应用时间过滤器
            if (createdAfter.HasValue)
            {
                workflows = workflows.Where(w => w.CreatedAt >= createdAfter.Value);
            }

            if (createdBefore.HasValue)
            {
                workflows = workflows.Where(w => w.CreatedAt <= createdBefore.Value);
            }

            // 分页
            var totalCount = workflows.Count();
            var pagedWorkflows = workflows
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToList();

            Console.WriteLine($"Found {totalCount} workflows (page {page}, size {pageSize})");

            // 返回分页结果
            var result = new
            {
                Data = pagedWorkflows,
                TotalCount = totalCount,
                Page = page,
                PageSize = pageSize,
                TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
            };

            return Ok(result);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error getting workflows: {ex}");
            return StatusCode(500, $"Error: {ex.Message}");
        }
    }

    // GET: api/workflows/debug
    [HttpGet("debug")]
    public async Task<ActionResult> GetDebugInfo()
    {
        try
        {
            // 直接查询数据库
            var allWorkflows = await workflowRepository.GetAllWorkflowDefinitionsAsync();
            var count = allWorkflows.Count();

            return Ok(new {
                TotalWorkflows = count,
                Message = $"Found {count} workflows in database"
            });
        }
        catch (Exception ex)
        {
            return Ok(new {
                Error = ex.Message,
                StackTrace = ex.StackTrace
            });
        }
    }

    // GET: api/workflows/{id}
    [HttpGet("{id}")]
    public async Task<ActionResult<WorkflowDefinition>> GetWorkflow(string id)
    {
        var workflow = await workflowRepository.GetWorkflowDefinitionAsync(id);
        if (workflow == null)
        {
            return NotFound();
        }
        return Ok(workflow);
    }

    // POST: api/workflows
    [HttpPost]
    public async Task<ActionResult<WorkflowDefinition>> CreateWorkflow(WorkflowDefinition workflow)
    {
        // 如果没有提供WorkflowId，则自动生成一个
        if (string.IsNullOrEmpty(workflow.WorkflowId))
        {
            workflow.WorkflowId = Guid.NewGuid().ToString("N");
        }

        // 设置创建和修改时间
        workflow.CreatedAt = DateTime.UtcNow;
        workflow.LastModifiedAt = DateTime.UtcNow;

        // 验证工作流定义
        var validationResult = await workflowEngineService.ValidateWorkflowAsync(workflow);
        if (!validationResult.IsValid)
        {
            return BadRequest(new { 
                Message = "Workflow validation failed", 
                Errors = validationResult.Errors,
                Warnings = validationResult.Warnings
            });
        }

        var result = await workflowRepository.SaveWorkflowDefinitionAsync(workflow);
        if (!result)
        {
            return BadRequest("Failed to save workflow");
        }
        return CreatedAtAction(nameof(GetWorkflow), new { id = workflow.WorkflowId }, workflow);
    }

    // POST: api/workflows/validate
    [HttpPost("validate")]
    public async Task<ActionResult> ValidateWorkflow(WorkflowDefinition workflow)
    {
        var validationResult = await workflowEngineService.ValidateWorkflowAsync(workflow);
        if (!validationResult.IsValid)
        {
            return BadRequest(validationResult.Errors);
        }
        return Ok("Workflow is valid");
    }

    // PUT: api/workflows/{id}
    [HttpPut("{id}")]
    public async Task<IActionResult> UpdateWorkflow(string id, WorkflowDefinition workflow)
    {
        if (id != workflow.WorkflowId)
        {
            return BadRequest();
        }

        var result = await workflowRepository.SaveWorkflowDefinitionAsync(workflow);
        if (!result)
        {
            return BadRequest("Failed to update workflow");
        }
        return NoContent();
    }

    // DELETE: api/workflows/{id}
    [HttpDelete("{id}")]
    public async Task<IActionResult> DeleteWorkflow(string id)
    {
        var result = await workflowRepository.DeleteWorkflowDefinitionAsync(id);
        if (!result)
        {
            return NotFound();
        }
        return NoContent();
    }

    // POST: api/workflows/{id}/copy
    [HttpPost("{id}/copy")]
    public async Task<ActionResult<WorkflowDefinition>> CopyWorkflow(string id, [FromBody] CopyWorkflowRequest? request = null)
    {
        try
        {
            var originalWorkflow = await workflowRepository.GetWorkflowDefinitionAsync(id);
            if (originalWorkflow == null)
            {
                return NotFound($"Workflow with ID '{id}' not found");
            }

            // 创建工作流副本
            var copiedWorkflow = originalWorkflow.Clone();
            copiedWorkflow.WorkflowId = Guid.NewGuid().ToString("N");
            copiedWorkflow.Name = request?.Name ?? $"{originalWorkflow.Name} - Copy";
            copiedWorkflow.Description = request?.Description ?? $"Copy of {originalWorkflow.Description}";
            copiedWorkflow.CreatedAt = DateTime.UtcNow;
            copiedWorkflow.LastModifiedAt = DateTime.UtcNow;
            copiedWorkflow.PublishStatus = PublishStatus.Draft; // 复制的工作流默认为草稿状态

            // 保存副本
            var result = await workflowRepository.SaveWorkflowDefinitionAsync(copiedWorkflow);
            if (!result)
            {
                return BadRequest("Failed to save copied workflow");
            }

            return CreatedAtAction(nameof(GetWorkflow), new { id = copiedWorkflow.WorkflowId }, copiedWorkflow);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error copying workflow: {ex}");
            return StatusCode(500, $"Error: {ex.Message}");
        }
    }

    // GET: api/workflows/{id}/export
    [HttpGet("{id}/export")]
    public async Task<ActionResult> ExportWorkflow(string id)
    {
        try
        {
            var workflow = await workflowRepository.GetWorkflowDefinitionAsync(id);
            if (workflow == null)
            {
                return NotFound($"Workflow with ID '{id}' not found");
            }

            // 序列化工作流为JSON
            var jsonOptions = new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };
            var jsonContent = JsonSerializer.Serialize(workflow, jsonOptions);
            var bytes = Encoding.UTF8.GetBytes(jsonContent);

            // 返回文件下载
            var fileName = $"{workflow.Name}_{DateTime.UtcNow:yyyyMMdd_HHmmss}.json";
            return File(bytes, "application/json", fileName);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error exporting workflow: {ex}");
            return StatusCode(500, $"Error: {ex.Message}");
        }
    }

    // POST: api/workflows/import
    [HttpPost("import")]
    public async Task<ActionResult<WorkflowDefinition>> ImportWorkflow(IFormFile file)
    {
        try
        {
            if (file == null || file.Length == 0)
            {
                return BadRequest("No file provided");
            }

            if (!file.ContentType.Contains("json") && !file.FileName.EndsWith(".json"))
            {
                return BadRequest("Only JSON files are supported");
            }

            // 读取文件内容
            using var stream = file.OpenReadStream();
            using var reader = new StreamReader(stream);
            var jsonContent = await reader.ReadToEndAsync();

            // 反序列化工作流
            var jsonOptions = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };
            var workflow = JsonSerializer.Deserialize<WorkflowDefinition>(jsonContent, jsonOptions);

            if (workflow == null)
            {
                return BadRequest("Invalid workflow file format");
            }

            // 生成新的ID和时间戳
            workflow.WorkflowId = Guid.NewGuid().ToString("N");
            workflow.CreatedAt = DateTime.UtcNow;
            workflow.LastModifiedAt = DateTime.UtcNow;
            workflow.PublishStatus = PublishStatus.Draft; // 导入的工作流默认为草稿状态

            // 验证工作流
            var validationResult = await workflowEngineService.ValidateWorkflowAsync(workflow);
            if (!validationResult.IsValid)
            {
                return BadRequest(new {
                    Message = "Imported workflow validation failed",
                    Errors = validationResult.Errors,
                    Warnings = validationResult.Warnings
                });
            }

            // 保存工作流
            var result = await workflowRepository.SaveWorkflowDefinitionAsync(workflow);
            if (!result)
            {
                return BadRequest("Failed to save imported workflow");
            }

            return CreatedAtAction(nameof(GetWorkflow), new { id = workflow.WorkflowId }, workflow);
        }
        catch (JsonException ex)
        {
            Console.WriteLine($"JSON parsing error: {ex}");
            return BadRequest("Invalid JSON format in the uploaded file");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error importing workflow: {ex}");
            return StatusCode(500, $"Error: {ex.Message}");
        }
    }
}