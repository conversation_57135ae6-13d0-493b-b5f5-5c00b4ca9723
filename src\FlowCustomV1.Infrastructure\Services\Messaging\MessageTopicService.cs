using FlowCustomV1.Core.Interfaces.Messaging;
using FlowCustomV1.Infrastructure.Configuration;
using Microsoft.Extensions.Options;

namespace FlowCustomV1.Infrastructure.Services.Messaging;

/// <summary>
/// 消息主题服务实现
/// 基于配置动态生成消息主题名称
/// </summary>
public class MessageTopicService : IMessageTopicService
{
    private readonly MessagingTopicConfiguration _config;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="config">消息主题配置</param>
    public MessageTopicService(IOptions<MessagingTopicConfiguration> config)
    {
        _config = config.Value ?? throw new ArgumentNullException(nameof(config));
    }

    #region 集群管理主题

    /// <inheritdoc />
    public string GetClusterRootTopic()
    {
        return $"{_config.RootPrefix}.{_config.Cluster.Root}";
    }

    /// <inheritdoc />
    public string GetNodeHeartbeatTopic(string nodeId)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(nodeId);
        var clusterRoot = GetClusterRootTopic();
        var heartbeatPattern = string.Format(_config.Cluster.NodeHeartbeat, nodeId);
        return $"{clusterRoot}.{heartbeatPattern}";
    }

    /// <inheritdoc />
    public string GetServiceDiscoveryTopic()
    {
        return $"{GetClusterRootTopic()}.{_config.Cluster.ServiceDiscovery}";
    }

    /// <inheritdoc />
    public string GetClusterConfigTopic()
    {
        return $"{GetClusterRootTopic()}.{_config.Cluster.Config}";
    }

    /// <inheritdoc />
    public string GetNodeRegisterTopic()
    {
        return $"{GetClusterRootTopic()}.{_config.Cluster.NodeRegister}";
    }

    /// <inheritdoc />
    public string GetNodeUnregisterTopic()
    {
        return $"{GetClusterRootTopic()}.{_config.Cluster.NodeUnregister}";
    }

    #endregion

    #region 工作流核心功能主题

    /// <inheritdoc />
    public string GetWorkflowRootTopic()
    {
        return $"{_config.RootPrefix}.{_config.Workflows.Root}";
    }

    /// <inheritdoc />
    public string GetWorkflowEventsTopic(string workflowId)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(workflowId);
        var workflowRoot = GetWorkflowRootTopic();
        var eventsPattern = string.Format(_config.Workflows.Events, workflowId);
        return $"{workflowRoot}.{eventsPattern}";
    }

    /// <inheritdoc />
    public string GetWorkflowStateTopic(string workflowId)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(workflowId);
        var workflowRoot = GetWorkflowRootTopic();
        var statePattern = string.Format(_config.Workflows.State, workflowId);
        return $"{workflowRoot}.{statePattern}";
    }

    /// <inheritdoc />
    public string GetWorkflowExecutionTopic(string workflowId, string executionId)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(workflowId);
        ArgumentException.ThrowIfNullOrWhiteSpace(executionId);
        var workflowRoot = GetWorkflowRootTopic();
        var executionPattern = string.Format(_config.Workflows.Execution, workflowId, executionId);
        return $"{workflowRoot}.{executionPattern}";
    }

    #endregion

    #region 节点任务分发主题

    /// <inheritdoc />
    public string GetNodesRootTopic()
    {
        return $"{_config.RootPrefix}.{_config.Nodes.Root}";
    }

    /// <inheritdoc />
    public string GetNodeTasksTopic(string nodeId)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(nodeId);
        var nodesRoot = GetNodesRootTopic();
        var tasksPattern = string.Format(_config.Nodes.Tasks, nodeId);
        return $"{nodesRoot}.{tasksPattern}";
    }

    /// <inheritdoc />
    public string GetNodeHealthTopic(string nodeId)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(nodeId);
        var nodesRoot = GetNodesRootTopic();
        var healthPattern = string.Format(_config.Nodes.Health, nodeId);
        return $"{nodesRoot}.{healthPattern}";
    }

    /// <inheritdoc />
    public string GetNodeStatusTopic(string nodeId)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(nodeId);
        var nodesRoot = GetNodesRootTopic();
        var statusPattern = string.Format(_config.Nodes.Status, nodeId);
        return $"{nodesRoot}.{statusPattern}";
    }

    #endregion

    #region 任务调度主题

    /// <inheritdoc />
    public string GetTasksRootTopic()
    {
        return $"{_config.RootPrefix}.{_config.Tasks.Root}";
    }

    /// <inheritdoc />
    public string GetHighPriorityTasksTopic()
    {
        return $"{GetTasksRootTopic()}.{_config.Tasks.HighPriority}";
    }

    /// <inheritdoc />
    public string GetNormalPriorityTasksTopic()
    {
        return $"{GetTasksRootTopic()}.{_config.Tasks.NormalPriority}";
    }

    /// <inheritdoc />
    public string GetLowPriorityTasksTopic()
    {
        return $"{GetTasksRootTopic()}.{_config.Tasks.LowPriority}";
    }

    #endregion

    #region 角色专业化主题

    /// <inheritdoc />
    public string GetDesignerRootTopic()
    {
        return $"{_config.RootPrefix}.{_config.Roles.Designer}";
    }

    /// <inheritdoc />
    public string GetValidatorRootTopic()
    {
        return $"{_config.RootPrefix}.{_config.Roles.Validator}";
    }

    /// <inheritdoc />
    public string GetExecutorRootTopic()
    {
        return $"{_config.RootPrefix}.{_config.Roles.Executor}";
    }

    #endregion

    #region UI通信主题

    /// <inheritdoc />
    public string GetUiRootTopic()
    {
        return $"{_config.RootPrefix}.{_config.Ui.Root}";
    }

    /// <inheritdoc />
    public string GetUiUpdatesTopic()
    {
        return $"{GetUiRootTopic()}.{_config.Ui.Updates}";
    }

    /// <inheritdoc />
    public string GetUiNotificationsTopic()
    {
        return $"{GetUiRootTopic()}.{_config.Ui.Notifications}";
    }

    /// <inheritdoc />
    public string GetUiEventsTopic()
    {
        return $"{GetUiRootTopic()}.{_config.Ui.Events}";
    }

    #endregion

    #region 监控主题

    /// <inheritdoc />
    public string GetMonitoringRootTopic()
    {
        return $"{_config.RootPrefix}.{_config.Monitoring.Root}";
    }

    /// <inheritdoc />
    public string GetMonitoringMetricsTopic()
    {
        return $"{GetMonitoringRootTopic()}.{_config.Monitoring.Metrics}";
    }

    /// <inheritdoc />
    public string GetMonitoringHealthTopic()
    {
        return $"{GetMonitoringRootTopic()}.{_config.Monitoring.Health}";
    }

    /// <inheritdoc />
    public string GetMonitoringAlertsTopic()
    {
        return $"{GetMonitoringRootTopic()}.{_config.Monitoring.Alerts}";
    }

    /// <inheritdoc />
    public string GetMonitoringLogsTopic()
    {
        return $"{GetMonitoringRootTopic()}.{_config.Monitoring.Logs}";
    }

    #endregion

    #region Executor专用主题

    /// <inheritdoc />
    public string GetExecutionRequestTopic()
    {
        return $"{GetExecutorRootTopic()}.execution.request";
    }

    /// <inheritdoc />
    public string GetExecutionControlTopic()
    {
        return $"{GetExecutorRootTopic()}.execution.control";
    }

    /// <inheritdoc />
    public string GetExecutionControlResponseTopic()
    {
        return $"{GetExecutorRootTopic()}.execution.control.response";
    }

    /// <inheritdoc />
    public string GetExecutionMigrationTopic()
    {
        return $"{GetExecutorRootTopic()}.execution.migration";
    }

    /// <inheritdoc />
    public string GetExecutionMigrationTopic(string targetNodeId)
    {
        return $"{GetExecutorRootTopic()}.execution.migration.{targetNodeId}";
    }

    /// <inheritdoc />
    public string GetExecutionMigrationResponseTopic()
    {
        return $"{GetExecutorRootTopic()}.execution.migration.response";
    }

    /// <inheritdoc />
    public string GetCapacityQueryTopic()
    {
        return $"{GetExecutorRootTopic()}.capacity.query";
    }

    /// <inheritdoc />
    public string GetCapacityResponseTopic()
    {
        return $"{GetExecutorRootTopic()}.capacity.response";
    }

    /// <inheritdoc />
    public string GetNodeLoadUpdateTopic()
    {
        return $"{GetNodesRootTopic()}.load.update";
    }

    /// <inheritdoc />
    public string GetExecutionStatusTopic()
    {
        return $"{GetExecutorRootTopic()}.execution.status";
    }

    /// <inheritdoc />
    public string GetExecutionResultTopic()
    {
        return $"{GetExecutorRootTopic()}.execution.result";
    }

    /// <inheritdoc />
    public string GetNodeStateTopic()
    {
        return $"{GetNodesRootTopic()}.state";
    }

    /// <inheritdoc />
    public string GetExecutionStateQueryTopic()
    {
        return $"{GetExecutorRootTopic()}.execution.state.query";
    }

    /// <inheritdoc />
    public string GetExecutionStateResponseTopic()
    {
        return $"{GetExecutorRootTopic()}.execution.state.response";
    }

    /// <inheritdoc />
    public string GetExecutionEventTopic()
    {
        return $"{GetExecutorRootTopic()}.execution.event";
    }

    /// <inheritdoc />
    public string GetExecutionContextTopic()
    {
        return $"{GetExecutorRootTopic()}.execution.context";
    }

    /// <inheritdoc />
    public string GetExecutionContextQueryTopic()
    {
        return $"{GetExecutorRootTopic()}.execution.context.query";
    }

    /// <inheritdoc />
    public string GetExecutionContextResponseTopic()
    {
        return $"{GetExecutorRootTopic()}.execution.context.response";
    }

    #endregion
}
