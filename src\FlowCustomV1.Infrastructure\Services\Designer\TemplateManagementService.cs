using FlowCustomV1.Core.Interfaces.Services;
using FlowCustomV1.Core.Models.Designer;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Text.Json;

namespace FlowCustomV1.Infrastructure.Services.Designer;

/// <summary>
/// 工作流模板管理服务实现
/// 提供工作流模板的创建、管理和版本控制功能
/// </summary>
public class TemplateManagementService : ITemplateManagementService, IDisposable
{
    private readonly ILogger<TemplateManagementService> _logger;
    
    // 内存存储（实际项目中应该使用数据库）
    private readonly ConcurrentDictionary<string, WorkflowTemplate> _templates = new();
    private readonly ConcurrentDictionary<string, List<TemplateVersion>> _templateVersions = new();
    private readonly ConcurrentDictionary<string, TemplateUsageStatistics> _usageStatistics = new();
    
    private bool _disposed = false;

    /// <summary>
    /// 构造函数
    /// </summary>
    public TemplateManagementService(ILogger<TemplateManagementService> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        
        // 初始化内置模板
        _ = Task.Run(InitializeBuiltInTemplatesAsync);
    }

    #region 模板基础操作

    /// <inheritdoc />
    public async Task<bool> CreateTemplateAsync(WorkflowTemplate template, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(template);

        try
        {
            _logger.LogInformation("Creating template {TemplateId}", template.TemplateId);

            // 验证模板
            var validationResult = await ValidateTemplateAsync(template, cancellationToken);
            if (!validationResult.IsValid)
            {
                _logger.LogWarning("Template validation failed: {Errors}", string.Join(", ", validationResult.Errors));
                return false;
            }

            // 检查模板ID是否已存在
            if (_templates.ContainsKey(template.TemplateId))
            {
                _logger.LogWarning("Template {TemplateId} already exists", template.TemplateId);
                return false;
            }

            // 设置创建时间
            template.CreatedAt = DateTime.UtcNow;
            template.LastModifiedAt = DateTime.UtcNow;

            // 添加到存储
            _templates.TryAdd(template.TemplateId, template);

            // 初始化使用统计
            _usageStatistics.TryAdd(template.TemplateId, new TemplateUsageStatistics
            {
                TemplateId = template.TemplateId
            });

            // 创建初始版本
            var initialVersion = new TemplateVersion
            {
                VersionId = Guid.NewGuid().ToString("N"),
                TemplateId = template.TemplateId,
                Version = template.Version,
                Description = "Initial version",
                Template = CloneTemplate(template),
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = template.CreatedBy,
                CreatedByName = template.Author
            };

            _templateVersions.TryAdd(template.TemplateId, new List<TemplateVersion> { initialVersion });

            // 触发模板创建事件
            OnTemplateCreated(new TemplateCreatedEventArgs
            {
                Template = template,
                CreatedBy = template.CreatedBy,
                CreatedAt = DateTime.UtcNow,
                IsBuiltIn = template.IsBuiltIn
            });

            _logger.LogInformation("Template {TemplateId} created successfully", template.TemplateId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create template {TemplateId}", template.TemplateId);
            return false;
        }
    }

    /// <inheritdoc />
    public async Task<bool> UpdateTemplateAsync(string templateId, WorkflowTemplate template, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(templateId);
        ArgumentNullException.ThrowIfNull(template);

        try
        {
            _logger.LogInformation("Updating template {TemplateId}", templateId);

            // 验证模板
            var validationResult = await ValidateTemplateAsync(template, cancellationToken);
            if (!validationResult.IsValid)
            {
                _logger.LogWarning("Template validation failed: {Errors}", string.Join(", ", validationResult.Errors));
                return false;
            }

            // 获取现有模板
            if (!_templates.TryGetValue(templateId, out var existingTemplate))
            {
                _logger.LogWarning("Template {TemplateId} not found", templateId);
                return false;
            }

            var previousTemplate = CloneTemplate(existingTemplate);

            // 更新模板
            template.TemplateId = templateId;
            template.CreatedAt = existingTemplate.CreatedAt;
            template.CreatedBy = existingTemplate.CreatedBy;
            template.LastModifiedAt = DateTime.UtcNow;
            template.UsageCount = existingTemplate.UsageCount;

            _templates.TryUpdate(templateId, template, existingTemplate);

            // 触发模板更新事件
            OnTemplateUpdated(new TemplateUpdatedEventArgs
            {
                TemplateId = templateId,
                Template = template,
                PreviousTemplate = previousTemplate,
                UpdatedBy = template.LastModifiedBy,
                UpdatedAt = DateTime.UtcNow,
                Changes = DetectChanges(previousTemplate, template)
            });

            _logger.LogInformation("Template {TemplateId} updated successfully", templateId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update template {TemplateId}", templateId);
            return false;
        }
    }

    /// <inheritdoc />
    public Task<bool> DeleteTemplateAsync(string templateId, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(templateId);

        try
        {
            _logger.LogInformation("Deleting template {TemplateId}", templateId);

            // 获取模板
            if (!_templates.TryRemove(templateId, out var deletedTemplate))
            {
                _logger.LogWarning("Template {TemplateId} not found", templateId);
                return Task.FromResult(false);
            }

            // 删除版本历史
            _templateVersions.TryRemove(templateId, out _);

            // 删除使用统计
            _usageStatistics.TryRemove(templateId, out _);

            // 触发模板删除事件
            OnTemplateDeleted(new TemplateDeletedEventArgs
            {
                TemplateId = templateId,
                DeletedTemplate = deletedTemplate,
                DeletedBy = "system", // TODO: 从上下文获取用户ID
                DeletedAt = DateTime.UtcNow,
                IsSoftDelete = false
            });

            _logger.LogInformation("Template {TemplateId} deleted successfully", templateId);
            return Task.FromResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete template {TemplateId}", templateId);
            return Task.FromResult(false);
        }
    }

    /// <inheritdoc />
    public Task<WorkflowTemplate?> GetTemplateAsync(string templateId, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(templateId);

        try
        {
            if (_templates.TryGetValue(templateId, out var template))
            {
                return Task.FromResult<WorkflowTemplate?>(CloneTemplate(template));
            }

            return Task.FromResult<WorkflowTemplate?>(null);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get template {TemplateId}", templateId);
            return Task.FromResult<WorkflowTemplate?>(null);
        }
    }

    /// <inheritdoc />
    public Task<IReadOnlyList<WorkflowTemplate>> GetTemplatesAsync(TemplateQuery? query = null, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting templates with query");

            var templates = _templates.Values.AsEnumerable();

            // 应用查询过滤
            if (query != null)
            {
                if (!string.IsNullOrWhiteSpace(query.Name))
                {
                    templates = templates.Where(t => t.Name.Contains(query.Name, StringComparison.OrdinalIgnoreCase));
                }

                if (!string.IsNullOrWhiteSpace(query.Category))
                {
                    templates = templates.Where(t => t.Category.Equals(query.Category, StringComparison.OrdinalIgnoreCase));
                }

                if (!string.IsNullOrWhiteSpace(query.Author))
                {
                    templates = templates.Where(t => t.Author.Equals(query.Author, StringComparison.OrdinalIgnoreCase));
                }

                if (query.Tags?.Count > 0)
                {
                    templates = templates.Where(t => query.Tags.Any(tag => t.Tags.Contains(tag)));
                }

                if (query.PublicOnly)
                {
                    templates = templates.Where(t => t.IsPublic);
                }

                if (query.BuiltInOnly)
                {
                    templates = templates.Where(t => t.IsBuiltIn);
                }

                if (query.MinRating.HasValue)
                {
                    templates = templates.Where(t => t.Rating >= query.MinRating.Value);
                }

                if (query.CreatedAfter.HasValue)
                {
                    templates = templates.Where(t => t.CreatedAt >= query.CreatedAfter.Value);
                }

                if (query.CreatedBefore.HasValue)
                {
                    templates = templates.Where(t => t.CreatedAt <= query.CreatedBefore.Value);
                }

                // 排序
                templates = query.SortBy.ToLowerInvariant() switch
                {
                    "name" => query.SortDirection == SortDirection.Ascending 
                        ? templates.OrderBy(t => t.Name) 
                        : templates.OrderByDescending(t => t.Name),
                    "createdat" => query.SortDirection == SortDirection.Ascending 
                        ? templates.OrderBy(t => t.CreatedAt) 
                        : templates.OrderByDescending(t => t.CreatedAt),
                    "rating" => query.SortDirection == SortDirection.Ascending 
                        ? templates.OrderBy(t => t.Rating) 
                        : templates.OrderByDescending(t => t.Rating),
                    "usagecount" => query.SortDirection == SortDirection.Ascending 
                        ? templates.OrderBy(t => t.UsageCount) 
                        : templates.OrderByDescending(t => t.UsageCount),
                    _ => templates.OrderByDescending(t => t.CreatedAt)
                };

                // 分页
                templates = templates.Skip((query.PageNumber - 1) * query.PageSize).Take(query.PageSize);
            }

            var result = templates.Select(CloneTemplate).ToList();
            return Task.FromResult<IReadOnlyList<WorkflowTemplate>>(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get templates");
            return Task.FromResult<IReadOnlyList<WorkflowTemplate>>(new List<WorkflowTemplate>());
        }
    }

    #endregion

    #region 模板分类管理

    /// <inheritdoc />
    public Task<IReadOnlyList<string>> GetCategoriesAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var categories = _templates.Values
                .Where(t => !string.IsNullOrWhiteSpace(t.Category))
                .Select(t => t.Category)
                .Distinct()
                .OrderBy(c => c)
                .ToList();

            return Task.FromResult<IReadOnlyList<string>>(categories);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get template categories");
            return Task.FromResult<IReadOnlyList<string>>(new List<string>());
        }
    }

    /// <inheritdoc />
    public Task<IReadOnlyList<WorkflowTemplate>> GetTemplatesByCategoryAsync(string category, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(category);

        try
        {
            var templates = _templates.Values
                .Where(t => t.Category.Equals(category, StringComparison.OrdinalIgnoreCase))
                .Select(CloneTemplate)
                .OrderBy(t => t.Name)
                .ToList();

            return Task.FromResult<IReadOnlyList<WorkflowTemplate>>(templates);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get templates by category {Category}", category);
            return Task.FromResult<IReadOnlyList<WorkflowTemplate>>(new List<WorkflowTemplate>());
        }
    }

    #endregion

    #region 模板版本管理

    /// <inheritdoc />
    public Task<string> CreateTemplateVersionAsync(string templateId, TemplateVersionInfo versionInfo, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(templateId);
        ArgumentNullException.ThrowIfNull(versionInfo);

        try
        {
            _logger.LogInformation("Creating version {Version} for template {TemplateId}", versionInfo.Version, templateId);

            // 获取模板
            if (!_templates.TryGetValue(templateId, out var template))
            {
                _logger.LogWarning("Template {TemplateId} not found", templateId);
                return Task.FromResult(string.Empty);
            }

            // 创建版本
            var version = new TemplateVersion
            {
                VersionId = Guid.NewGuid().ToString("N"),
                TemplateId = templateId,
                Version = versionInfo.Version,
                Description = versionInfo.Description,
                Template = CloneTemplate(template),
                IsActive = versionInfo.SetAsActive,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = versionInfo.CreatedBy,
                CreatedByName = versionInfo.CreatedByName,
                ChangeSummary = versionInfo.ChangeSummary
            };

            // 添加到版本历史
            _templateVersions.AddOrUpdate(templateId,
                new List<TemplateVersion> { version },
                (key, existing) =>
                {
                    // 如果设为活跃版本，取消其他版本的活跃状态
                    if (versionInfo.SetAsActive)
                    {
                        foreach (var v in existing)
                        {
                            v.IsActive = false;
                        }
                    }
                    existing.Add(version);
                    return existing;
                });

            // 触发版本创建事件
            OnTemplateVersionCreated(new TemplateVersionCreatedEventArgs
            {
                TemplateId = templateId,
                Version = version,
                CreatedBy = versionInfo.CreatedBy,
                CreatedAt = DateTime.UtcNow
            });

            _logger.LogInformation("Version {VersionId} created successfully for template {TemplateId}", version.VersionId, templateId);
            return Task.FromResult(version.VersionId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create version for template {TemplateId}", templateId);
            return Task.FromResult(string.Empty);
        }
    }

    /// <inheritdoc />
    public Task<IReadOnlyList<TemplateVersion>> GetTemplateVersionsAsync(string templateId, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(templateId);

        try
        {
            if (_templateVersions.TryGetValue(templateId, out var versions))
            {
                return Task.FromResult<IReadOnlyList<TemplateVersion>>(versions.OrderByDescending(v => v.CreatedAt).ToList());
            }

            return Task.FromResult<IReadOnlyList<TemplateVersion>>(new List<TemplateVersion>());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get versions for template {TemplateId}", templateId);
            return Task.FromResult<IReadOnlyList<TemplateVersion>>(new List<TemplateVersion>());
        }
    }

    /// <inheritdoc />
    public Task<WorkflowTemplate?> GetTemplateVersionAsync(string templateId, string version, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(templateId);
        ArgumentException.ThrowIfNullOrWhiteSpace(version);

        try
        {
            if (_templateVersions.TryGetValue(templateId, out var versions))
            {
                var templateVersion = versions.FirstOrDefault(v => v.Version == version);
                return Task.FromResult<WorkflowTemplate?>(templateVersion?.Template != null ? CloneTemplate(templateVersion.Template) : null);
            }

            return Task.FromResult<WorkflowTemplate?>(null);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get version {Version} of template {TemplateId}", version, templateId);
            return Task.FromResult<WorkflowTemplate?>(null);
        }
    }

    /// <inheritdoc />
    public Task<bool> ActivateTemplateVersionAsync(string templateId, string version, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(templateId);
        ArgumentException.ThrowIfNullOrWhiteSpace(version);

        try
        {
            _logger.LogInformation("Activating version {Version} for template {TemplateId}", version, templateId);

            if (_templateVersions.TryGetValue(templateId, out var versions))
            {
                var targetVersion = versions.FirstOrDefault(v => v.Version == version);
                if (targetVersion == null)
                {
                    _logger.LogWarning("Version {Version} not found for template {TemplateId}", version, templateId);
                    return Task.FromResult(false);
                }

                // 取消所有版本的活跃状态
                foreach (var v in versions)
                {
                    v.IsActive = false;
                }

                // 激活目标版本
                targetVersion.IsActive = true;

                _logger.LogInformation("Version {Version} activated successfully for template {TemplateId}", version, templateId);
                return Task.FromResult(true);
            }

            return Task.FromResult(false);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to activate version {Version} for template {TemplateId}", version, templateId);
            return Task.FromResult(false);
        }
    }

    #endregion

    #region 模板共享和发布

    /// <inheritdoc />
    public Task<bool> PublishTemplateAsync(string templateId, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(templateId);

        try
        {
            _logger.LogInformation("Publishing template {TemplateId}", templateId);

            if (_templates.TryGetValue(templateId, out var template))
            {
                template.IsPublic = true;
                template.LastModifiedAt = DateTime.UtcNow;

                // 触发模板发布事件
                OnTemplatePublished(new TemplatePublishedEventArgs
                {
                    TemplateId = templateId,
                    Template = template,
                    PublishedBy = "system", // TODO: 从上下文获取用户ID
                    PublishedAt = DateTime.UtcNow,
                    PublishType = TemplatePublishType.Public
                });

                _logger.LogInformation("Template {TemplateId} published successfully", templateId);
                return Task.FromResult(true);
            }

            return Task.FromResult(false);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to publish template {TemplateId}", templateId);
            return Task.FromResult(false);
        }
    }

    /// <inheritdoc />
    public Task<bool> UnpublishTemplateAsync(string templateId, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(templateId);

        try
        {
            _logger.LogInformation("Unpublishing template {TemplateId}", templateId);

            if (_templates.TryGetValue(templateId, out var template))
            {
                template.IsPublic = false;
                template.LastModifiedAt = DateTime.UtcNow;

                _logger.LogInformation("Template {TemplateId} unpublished successfully", templateId);
                return Task.FromResult(true);
            }

            return Task.FromResult(false);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to unpublish template {TemplateId}", templateId);
            return Task.FromResult(false);
        }
    }

    /// <inheritdoc />
    public async Task<IReadOnlyList<WorkflowTemplate>> GetPublicTemplatesAsync(TemplateQuery? query = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var publicQuery = query ?? new TemplateQuery();
            publicQuery.PublicOnly = true;

            return await GetTemplatesAsync(publicQuery, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get public templates");
            return new List<WorkflowTemplate>();
        }
    }

    /// <inheritdoc />
    public async Task<string> CloneTemplateAsync(string sourceTemplateId, string newTemplateName, string newTemplateDescription = "", CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(sourceTemplateId);
        ArgumentException.ThrowIfNullOrWhiteSpace(newTemplateName);

        try
        {
            _logger.LogInformation("Cloning template {SourceTemplateId} to {NewTemplateName}", sourceTemplateId, newTemplateName);

            // 获取源模板
            if (!_templates.TryGetValue(sourceTemplateId, out var sourceTemplate))
            {
                _logger.LogWarning("Source template {SourceTemplateId} not found", sourceTemplateId);
                return string.Empty;
            }

            // 创建新模板
            var newTemplate = CloneTemplate(sourceTemplate);
            newTemplate.TemplateId = Guid.NewGuid().ToString("N");
            newTemplate.Name = newTemplateName;
            newTemplate.Description = string.IsNullOrEmpty(newTemplateDescription) ? sourceTemplate.Description : newTemplateDescription;
            newTemplate.CreatedAt = DateTime.UtcNow;
            newTemplate.LastModifiedAt = DateTime.UtcNow;
            newTemplate.CreatedBy = "system"; // TODO: 从上下文获取用户ID
            newTemplate.Author = "system";
            newTemplate.UsageCount = 0;
            newTemplate.Rating = 0.0;
            newTemplate.IsPublic = false;
            newTemplate.IsBuiltIn = false;

            // 创建新模板
            var success = await CreateTemplateAsync(newTemplate, cancellationToken);
            if (success)
            {
                _logger.LogInformation("Template cloned successfully: {NewTemplateId}", newTemplate.TemplateId);
                return newTemplate.TemplateId;
            }

            return string.Empty;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to clone template {SourceTemplateId}", sourceTemplateId);
            return string.Empty;
        }
    }

    #endregion

    #region 模板统计和分析

    /// <inheritdoc />
    public Task<TemplateUsageStatistics> GetTemplateUsageStatisticsAsync(string templateId, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(templateId);

        try
        {
            if (_usageStatistics.TryGetValue(templateId, out var statistics))
            {
                return Task.FromResult(statistics);
            }

            // 如果没有统计数据，创建默认的
            var defaultStats = new TemplateUsageStatistics
            {
                TemplateId = templateId
            };

            _usageStatistics.TryAdd(templateId, defaultStats);
            return Task.FromResult(defaultStats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get usage statistics for template {TemplateId}", templateId);
            return Task.FromResult(new TemplateUsageStatistics { TemplateId = templateId });
        }
    }

    /// <inheritdoc />
    public async Task<bool> IncrementTemplateUsageAsync(string templateId, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(templateId);

        try
        {
            // 更新模板使用次数
            if (_templates.TryGetValue(templateId, out var template))
            {
                template.UsageCount++;
            }

            // 更新统计信息
            var statistics = await GetTemplateUsageStatisticsAsync(templateId, cancellationToken);
            statistics.TotalUsageCount++;
            statistics.MonthlyUsageCount++;
            statistics.WeeklyUsageCount++;
            statistics.DailyUsageCount++;
            statistics.LastUsedAt = DateTime.UtcNow;

            // 更新每日使用历史
            var today = DateTime.UtcNow.ToString("yyyy-MM-dd");
            if (statistics.DailyUsageHistory.ContainsKey(today))
            {
                statistics.DailyUsageHistory[today]++;
            }
            else
            {
                statistics.DailyUsageHistory[today] = 1;
            }

            // 保持最近30天的历史记录
            var cutoffDate = DateTime.UtcNow.AddDays(-30).ToString("yyyy-MM-dd");
            var keysToRemove = statistics.DailyUsageHistory.Keys
                .Where(k => string.Compare(k, cutoffDate) < 0)
                .ToList();

            foreach (var key in keysToRemove)
            {
                statistics.DailyUsageHistory.Remove(key);
            }

            // 触发模板使用事件
            OnTemplateUsed(new TemplateUsedEventArgs
            {
                TemplateId = templateId,
                TemplateName = template?.Name ?? "",
                UserId = "system", // TODO: 从上下文获取用户ID
                UserName = "System",
                UsedAt = DateTime.UtcNow
            });

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to increment usage for template {TemplateId}", templateId);
            return false;
        }
    }

    /// <inheritdoc />
    public Task<IReadOnlyList<WorkflowTemplate>> GetPopularTemplatesAsync(int count = 10, CancellationToken cancellationToken = default)
    {
        try
        {
            var popularTemplates = _templates.Values
                .Where(t => t.IsPublic)
                .OrderByDescending(t => t.UsageCount)
                .ThenByDescending(t => t.Rating)
                .Take(count)
                .Select(CloneTemplate)
                .ToList();

            return Task.FromResult<IReadOnlyList<WorkflowTemplate>>(popularTemplates);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get popular templates");
            return Task.FromResult<IReadOnlyList<WorkflowTemplate>>(new List<WorkflowTemplate>());
        }
    }

    #endregion

    #region 模板验证

    /// <inheritdoc />
    public async Task<TemplateValidationResult> ValidateTemplateAsync(WorkflowTemplate template, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(template);

        try
        {
            // 使用模板内置的验证方法
            var result = template.Validate();

            // 可以在这里添加更多的验证逻辑
            // 例如：检查节点类型是否支持、验证连接的有效性等

            return await Task.FromResult(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to validate template {TemplateId}", template.TemplateId);
            return new TemplateValidationResult
            {
                IsValid = false,
                Errors = new List<string> { $"Validation failed: {ex.Message}" }
            };
        }
    }

    /// <inheritdoc />
    public async Task<TemplateCompatibilityResult> CheckTemplateCompatibilityAsync(string templateId, string targetVersion, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(templateId);
        ArgumentException.ThrowIfNullOrWhiteSpace(targetVersion);

        try
        {
            _logger.LogDebug("Checking compatibility for template {TemplateId} with version {TargetVersion}", templateId, targetVersion);

            // 获取模板
            if (!_templates.TryGetValue(templateId, out var template))
            {
                return new TemplateCompatibilityResult
                {
                    IsCompatible = false,
                    CompatibilityLevel = CompatibilityLevel.Incompatible,
                    IncompatibilityReasons = new List<string> { "Template not found" }
                };
            }

            // 简单的版本兼容性检查
            var result = new TemplateCompatibilityResult
            {
                IsCompatible = true,
                CompatibilityLevel = CompatibilityLevel.FullyCompatible,
                CheckedAt = DateTime.UtcNow
            };

            // TODO: 实现更复杂的兼容性检查逻辑
            // 例如：检查节点类型兼容性、API版本兼容性等

            return await Task.FromResult(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to check compatibility for template {TemplateId}", templateId);
            return new TemplateCompatibilityResult
            {
                IsCompatible = false,
                CompatibilityLevel = CompatibilityLevel.Incompatible,
                IncompatibilityReasons = new List<string> { $"Compatibility check failed: {ex.Message}" }
            };
        }
    }

    #endregion

    #region 事件

    /// <inheritdoc />
    public event EventHandler<TemplateCreatedEventArgs>? TemplateCreated;

    /// <inheritdoc />
    public event EventHandler<TemplateUpdatedEventArgs>? TemplateUpdated;

    /// <inheritdoc />
    public event EventHandler<TemplateDeletedEventArgs>? TemplateDeleted;

    /// <inheritdoc />
    public event EventHandler<TemplatePublishedEventArgs>? TemplatePublished;

    /// <inheritdoc />
    public event EventHandler<TemplateUsedEventArgs>? TemplateUsed;

    /// <summary>
    /// 模板版本创建事件
    /// </summary>
    public event EventHandler<TemplateVersionCreatedEventArgs>? TemplateVersionCreated;

    /// <summary>
    /// 触发模板创建事件
    /// </summary>
    protected virtual void OnTemplateCreated(TemplateCreatedEventArgs e)
    {
        TemplateCreated?.Invoke(this, e);
    }

    /// <summary>
    /// 触发模板更新事件
    /// </summary>
    protected virtual void OnTemplateUpdated(TemplateUpdatedEventArgs e)
    {
        TemplateUpdated?.Invoke(this, e);
    }

    /// <summary>
    /// 触发模板删除事件
    /// </summary>
    protected virtual void OnTemplateDeleted(TemplateDeletedEventArgs e)
    {
        TemplateDeleted?.Invoke(this, e);
    }

    /// <summary>
    /// 触发模板发布事件
    /// </summary>
    protected virtual void OnTemplatePublished(TemplatePublishedEventArgs e)
    {
        TemplatePublished?.Invoke(this, e);
    }

    /// <summary>
    /// 触发模板使用事件
    /// </summary>
    protected virtual void OnTemplateUsed(TemplateUsedEventArgs e)
    {
        TemplateUsed?.Invoke(this, e);
    }

    /// <summary>
    /// 触发模板版本创建事件
    /// </summary>
    protected virtual void OnTemplateVersionCreated(TemplateVersionCreatedEventArgs e)
    {
        TemplateVersionCreated?.Invoke(this, e);
    }

    #endregion

    #region 私有辅助方法

    /// <summary>
    /// 克隆模板对象
    /// </summary>
    private static WorkflowTemplate CloneTemplate(WorkflowTemplate template)
    {
        // 使用JSON序列化进行深拷贝
        var json = JsonSerializer.Serialize(template);
        return JsonSerializer.Deserialize<WorkflowTemplate>(json) ?? new WorkflowTemplate();
    }

    /// <summary>
    /// 检测模板变更
    /// </summary>
    private static List<string> DetectChanges(WorkflowTemplate oldTemplate, WorkflowTemplate newTemplate)
    {
        var changes = new List<string>();

        if (oldTemplate.Name != newTemplate.Name)
            changes.Add($"Name changed from '{oldTemplate.Name}' to '{newTemplate.Name}'");

        if (oldTemplate.Description != newTemplate.Description)
            changes.Add("Description updated");

        if (oldTemplate.Category != newTemplate.Category)
            changes.Add($"Category changed from '{oldTemplate.Category}' to '{newTemplate.Category}'");

        if (oldTemplate.Version != newTemplate.Version)
            changes.Add($"Version changed from '{oldTemplate.Version}' to '{newTemplate.Version}'");

        if (oldTemplate.DefaultNodes.Count != newTemplate.DefaultNodes.Count)
            changes.Add($"Node count changed from {oldTemplate.DefaultNodes.Count} to {newTemplate.DefaultNodes.Count}");

        if (oldTemplate.DefaultConnections.Count != newTemplate.DefaultConnections.Count)
            changes.Add($"Connection count changed from {oldTemplate.DefaultConnections.Count} to {newTemplate.DefaultConnections.Count}");

        if (!oldTemplate.Tags.SetEquals(newTemplate.Tags))
            changes.Add("Tags updated");

        if (oldTemplate.IsPublic != newTemplate.IsPublic)
            changes.Add($"Visibility changed to {(newTemplate.IsPublic ? "Public" : "Private")}");

        return changes;
    }

    /// <summary>
    /// 初始化内置模板
    /// </summary>
    private async Task InitializeBuiltInTemplatesAsync()
    {
        try
        {
            _logger.LogInformation("Initializing built-in templates");

            // 创建一些内置模板示例
            var basicTemplate = new WorkflowTemplate
            {
                TemplateId = "builtin-basic-workflow",
                Name = "Basic Workflow",
                Description = "A simple workflow template with start, task, and end nodes",
                Category = "Basic",
                Version = "1.0.0",
                Author = "System",
                CreatedBy = "system",
                IsBuiltIn = true,
                IsPublic = true,
                Tags = new HashSet<string> { "basic", "starter", "simple" },
                DefaultNodes = new List<FlowCustomV1.Core.Models.Workflow.WorkflowNode>
                {
                    new() { NodeId = "start", NodeType = "Start", Name = "Start" },
                    new() { NodeId = "task", NodeType = "Task", Name = "Task" },
                    new() { NodeId = "end", NodeType = "End", Name = "End" }
                },
                DefaultConnections = new List<FlowCustomV1.Core.Models.Workflow.WorkflowConnection>
                {
                    new() { ConnectionId = "conn1", SourceNodeId = "start", TargetNodeId = "task" },
                    new() { ConnectionId = "conn2", SourceNodeId = "task", TargetNodeId = "end" }
                }
            };

            await CreateTemplateAsync(basicTemplate);

            var dataProcessingTemplate = new WorkflowTemplate
            {
                TemplateId = "builtin-data-processing",
                Name = "Data Processing Workflow",
                Description = "A template for data processing workflows with validation and transformation",
                Category = "Data Processing",
                Version = "1.0.0",
                Author = "System",
                CreatedBy = "system",
                IsBuiltIn = true,
                IsPublic = true,
                Tags = new HashSet<string> { "data", "processing", "etl" },
                DefaultNodes = new List<FlowCustomV1.Core.Models.Workflow.WorkflowNode>
                {
                    new() { NodeId = "start", NodeType = "Start", Name = "Start" },
                    new() { NodeId = "validate", NodeType = "Validation", Name = "Validate Data" },
                    new() { NodeId = "transform", NodeType = "Transform", Name = "Transform Data" },
                    new() { NodeId = "save", NodeType = "Save", Name = "Save Results" },
                    new() { NodeId = "end", NodeType = "End", Name = "End" }
                },
                DefaultConnections = new List<FlowCustomV1.Core.Models.Workflow.WorkflowConnection>
                {
                    new() { ConnectionId = "conn1", SourceNodeId = "start", TargetNodeId = "validate" },
                    new() { ConnectionId = "conn2", SourceNodeId = "validate", TargetNodeId = "transform" },
                    new() { ConnectionId = "conn3", SourceNodeId = "transform", TargetNodeId = "save" },
                    new() { ConnectionId = "conn4", SourceNodeId = "save", TargetNodeId = "end" }
                }
            };

            await CreateTemplateAsync(dataProcessingTemplate);

            _logger.LogInformation("Built-in templates initialized successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize built-in templates");
        }
    }

    #endregion

    #region IDisposable实现

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed && disposing)
        {
            try
            {
                // 清理模板存储
                _templates.Clear();

                // 清理版本历史
                _templateVersions.Clear();

                // 清理使用统计
                _usageStatistics.Clear();

                _logger.LogInformation("TemplateManagementService disposed successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while disposing TemplateManagementService");
            }
            finally
            {
                _disposed = true;
            }
        }
    }

    /// <summary>
    /// 析构函数
    /// </summary>
    ~TemplateManagementService()
    {
        Dispose(false);
    }

    #endregion
}
