using System.ComponentModel.DataAnnotations;
using System.Net.Mail;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Xml;
using FlowCustomV1.Core.Interfaces;
using FlowCustomV1.Core.Models.Workflow;
using ValidationResult = FlowCustomV1.Core.Models.Workflow.ValidationResult;

namespace FlowCustomV1.Core.Services;

/// <summary>
/// 验证服务实现
/// 提供通用的数据验证功能
/// </summary>
public class ValidationService : IValidationService
{
    private readonly ILoggingService _loggingService;
    private readonly Dictionary<Type, object> _validators;

    /// <summary>
    /// 初始化验证服务
    /// </summary>
    /// <param name="loggingService">日志服务</param>
    public ValidationService(ILoggingService loggingService)
    {
        _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
        _validators = new Dictionary<Type, object>();
        
        _loggingService.LogInformation("ValidationService initialized");
    }

    /// <inheritdoc />
    public async Task<ValidationResult> ValidateAsync<T>(T obj, CancellationToken cancellationToken = default) where T : class
    {
        ArgumentNullException.ThrowIfNull(obj);

        _loggingService.LogDebug($"Validating object of type {typeof(T).Name}");

        var result = new ValidationResult();

        try
        {
            // 首先尝试使用自定义验证器
            var customValidator = GetValidator<T>();
            if (customValidator != null)
            {
                return await customValidator.ValidateAsync(obj, cancellationToken);
            }

            // 使用数据注解验证
            var validationContext = new System.ComponentModel.DataAnnotations.ValidationContext(obj);
            var validationResults = new List<System.ComponentModel.DataAnnotations.ValidationResult>();

            bool isValid = Validator.TryValidateObject(obj, validationContext, validationResults, true);

            result.IsValid = isValid;

            if (!isValid)
            {
                foreach (var validationResult in validationResults)
                {
                    result.Errors.Add(validationResult.ErrorMessage ?? "Unknown validation error");
                    
                    result.Details.Add(new ValidationDetail
                    {
                        Item = string.Join(", ", validationResult.MemberNames),
                        Type = "DataAnnotation",
                        Message = validationResult.ErrorMessage ?? "Unknown validation error",
                        Severity = ValidationSeverity.Error
                    });
                }
            }

            _loggingService.LogDebug($"Validation completed for {typeof(T).Name}. IsValid: {result.IsValid}");
        }
        catch (Exception ex)
        {
            _loggingService.LogError($"Error during validation of {typeof(T).Name}: {ex.Message}", ex);
            result.IsValid = false;
            result.Errors.Add($"Validation error: {ex.Message}");
        }

        return result;
    }

    /// <inheritdoc />
    public async Task<IEnumerable<ValidationResult>> ValidateCollectionAsync<T>(IEnumerable<T> objects, CancellationToken cancellationToken = default) where T : class
    {
        ArgumentNullException.ThrowIfNull(objects);

        var results = new List<ValidationResult>();

        foreach (var obj in objects)
        {
            var result = await ValidateAsync(obj, cancellationToken);
            results.Add(result);
        }

        return results;
    }

    /// <inheritdoc />
    public async Task<PropertyValidationResult> ValidatePropertyAsync(object? value, string propertyName, IEnumerable<ParameterValidationRule> validationRules, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(propertyName);
        ArgumentNullException.ThrowIfNull(validationRules);

        var result = new PropertyValidationResult
        {
            PropertyName = propertyName,
            PropertyValue = value
        };

        try
        {
            foreach (var rule in validationRules)
            {
                var ruleResult = await ValidatePropertyRule(value, propertyName, rule, cancellationToken);
                if (!ruleResult.IsValid)
                {
                    result.IsValid = false;
                    result.Errors.AddRange(ruleResult.Errors);
                    result.Details.AddRange(ruleResult.Details);
                }
            }
        }
        catch (Exception ex)
        {
            _loggingService.LogError($"Error validating property {propertyName}: {ex.Message}", ex);
            result.IsValid = false;
            result.Errors.Add($"Property validation error: {ex.Message}");
        }

        return result;
    }

    /// <inheritdoc />
    public async Task<ParameterValidationResult> ValidateParametersAsync(Dictionary<string, object> parameters, IEnumerable<WorkflowParameterDefinition> parameterDefinitions, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(parameters);
        ArgumentNullException.ThrowIfNull(parameterDefinitions);

        var result = new ParameterValidationResult();

        try
        {
            foreach (var paramDef in parameterDefinitions)
            {
                // 检查必需参数
                if (paramDef.IsRequired && !parameters.ContainsKey(paramDef.Name))
                {
                    result.MissingParameters.Add(paramDef.Name);
                    result.IsValid = false;
                    result.Errors.Add($"Required parameter '{paramDef.Name}' is missing");
                    continue;
                }

                // 验证参数值
                if (parameters.TryGetValue(paramDef.Name, out var value))
                {
                    var propertyResult = await ValidatePropertyAsync(value, paramDef.Name, paramDef.ValidationRules, cancellationToken);
                    if (!propertyResult.IsValid)
                    {
                        result.InvalidParameters.Add(paramDef.Name);
                        result.IsValid = false;
                        result.Errors.AddRange(propertyResult.Errors);
                        result.Details.AddRange(propertyResult.Details);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _loggingService.LogError($"Error validating parameters: {ex.Message}", ex);
            result.IsValid = false;
            result.Errors.Add($"Parameter validation error: {ex.Message}");
        }

        return result;
    }

    /// <inheritdoc />
    public async Task<JsonValidationResult> ValidateJsonAsync(string json, CancellationToken cancellationToken = default)
    {
        var result = new JsonValidationResult();

        if (string.IsNullOrWhiteSpace(json))
        {
            result.IsValid = false;
            result.IsValidJson = false;
            result.Errors.Add("JSON string is null or empty");
            return result;
        }

        try
        {
            var jsonDocument = JsonDocument.Parse(json);
            result.ParsedObject = jsonDocument;
            result.IsValidJson = true;
            
            _loggingService.LogDebug("JSON validation successful");
        }
        catch (JsonException ex)
        {
            result.IsValid = false;
            result.IsValidJson = false;
            result.ParseError = ex.Message;
            result.Errors.Add($"Invalid JSON: {ex.Message}");
            
            _loggingService.LogDebug($"JSON validation failed: {ex.Message}");
        }

        return await Task.FromResult(result);
    }

    /// <inheritdoc />
    public async Task<XmlValidationResult> ValidateXmlAsync(string xml, CancellationToken cancellationToken = default)
    {
        var result = new XmlValidationResult();

        if (string.IsNullOrWhiteSpace(xml))
        {
            result.IsValid = false;
            result.IsValidXml = false;
            result.Errors.Add("XML string is null or empty");
            return result;
        }

        try
        {
            var xmlDocument = new XmlDocument();
            xmlDocument.LoadXml(xml);
            result.ParsedDocument = xmlDocument;
            result.IsValidXml = true;
            
            _loggingService.LogDebug("XML validation successful");
        }
        catch (XmlException ex)
        {
            result.IsValid = false;
            result.IsValidXml = false;
            result.ParseError = ex.Message;
            result.Errors.Add($"Invalid XML: {ex.Message}");
            
            _loggingService.LogDebug($"XML validation failed: {ex.Message}");
        }

        return await Task.FromResult(result);
    }

    /// <inheritdoc />
    public async Task<ValidationResult> ValidateEmailAsync(string email, CancellationToken cancellationToken = default)
    {
        var result = new ValidationResult();

        if (string.IsNullOrWhiteSpace(email))
        {
            result.IsValid = false;
            result.Errors.Add("Email address is null or empty");
            return result;
        }

        try
        {
            var mailAddress = new MailAddress(email);
            _loggingService.LogDebug($"Email validation successful: {email}");
        }
        catch (FormatException ex)
        {
            result.IsValid = false;
            result.Errors.Add($"Invalid email format: {ex.Message}");
            _loggingService.LogDebug($"Email validation failed: {email} - {ex.Message}");
        }

        return await Task.FromResult(result);
    }

    /// <inheritdoc />
    public async Task<ValidationResult> ValidateUrlAsync(string url, CancellationToken cancellationToken = default)
    {
        var result = new ValidationResult();

        if (string.IsNullOrWhiteSpace(url))
        {
            result.IsValid = false;
            result.Errors.Add("URL is null or empty");
            return result;
        }

        if (!Uri.TryCreate(url, UriKind.Absolute, out var uri))
        {
            result.IsValid = false;
            result.Errors.Add("Invalid URL format");
            _loggingService.LogDebug($"URL validation failed: {url}");
        }
        else
        {
            _loggingService.LogDebug($"URL validation successful: {url}");
        }

        return await Task.FromResult(result);
    }

    /// <inheritdoc />
    public async Task<ValidationResult> ValidateRegexAsync(string pattern, string input, CancellationToken cancellationToken = default)
    {
        var result = new ValidationResult();

        if (string.IsNullOrWhiteSpace(pattern))
        {
            result.IsValid = false;
            result.Errors.Add("Regex pattern is null or empty");
            return result;
        }

        if (input == null)
        {
            result.IsValid = false;
            result.Errors.Add("Input string is null");
            return result;
        }

        try
        {
            var regex = new Regex(pattern);
            bool isMatch = regex.IsMatch(input);
            
            if (!isMatch)
            {
                result.IsValid = false;
                result.Errors.Add($"Input does not match pattern: {pattern}");
            }
            
            _loggingService.LogDebug($"Regex validation completed. Pattern: {pattern}, Match: {isMatch}");
        }
        catch (ArgumentException ex)
        {
            result.IsValid = false;
            result.Errors.Add($"Invalid regex pattern: {ex.Message}");
            _loggingService.LogError($"Invalid regex pattern: {pattern} - {ex.Message}", ex);
        }

        return await Task.FromResult(result);
    }

    /// <inheritdoc />
    public void RegisterValidator<T>(IValidator<T> validator) where T : class
    {
        ArgumentNullException.ThrowIfNull(validator);
        
        _validators[typeof(T)] = validator;
        _loggingService.LogInformation($"Registered validator for type {typeof(T).Name}: {validator.ValidatorName}");
    }

    /// <inheritdoc />
    public bool RemoveValidator<T>() where T : class
    {
        bool removed = _validators.Remove(typeof(T));
        if (removed)
        {
            _loggingService.LogInformation($"Removed validator for type {typeof(T).Name}");
        }
        return removed;
    }

    /// <inheritdoc />
    public IValidator<T>? GetValidator<T>() where T : class
    {
        return _validators.TryGetValue(typeof(T), out var validator) ? (IValidator<T>)validator : null;
    }

    /// <inheritdoc />
    public IEnumerable<Type> GetRegisteredValidatorTypes()
    {
        return _validators.Keys.ToList();
    }

    /// <summary>
    /// 验证单个属性规则
    /// </summary>
    private async Task<ValidationResult> ValidatePropertyRule(object? value, string propertyName, ParameterValidationRule rule, CancellationToken cancellationToken)
    {
        var result = new ValidationResult();

        try
        {
            switch (rule.RuleType.ToLowerInvariant())
            {
                case "required":
                    if (value == null || (value is string str && string.IsNullOrWhiteSpace(str)))
                    {
                        result.IsValid = false;
                        result.Errors.Add(string.IsNullOrEmpty(rule.ErrorMessage) ? $"Property {propertyName} is required" : rule.ErrorMessage);
                    }
                    break;

                case "minlength":
                    if (value is string stringValue && rule.RuleValue is int minLength)
                    {
                        if (stringValue.Length < minLength)
                        {
                            result.IsValid = false;
                            result.Errors.Add(string.IsNullOrEmpty(rule.ErrorMessage) ? $"Property {propertyName} must be at least {minLength} characters long" : rule.ErrorMessage);
                        }
                    }
                    break;

                case "maxlength":
                    if (value is string stringValue2 && rule.RuleValue is int maxLength)
                    {
                        if (stringValue2.Length > maxLength)
                        {
                            result.IsValid = false;
                            result.Errors.Add(string.IsNullOrEmpty(rule.ErrorMessage) ? $"Property {propertyName} must be no more than {maxLength} characters long" : rule.ErrorMessage);
                        }
                    }
                    break;

                case "range":
                    if (rule.RuleValue is Dictionary<string, object> range && 
                        range.TryGetValue("min", out var minObj) && 
                        range.TryGetValue("max", out var maxObj))
                    {
                        if (value is IComparable comparable && minObj is IComparable min && maxObj is IComparable max)
                        {
                            if (comparable.CompareTo(min) < 0 || comparable.CompareTo(max) > 0)
                            {
                                result.IsValid = false;
                                result.Errors.Add(string.IsNullOrEmpty(rule.ErrorMessage) ? $"Property {propertyName} must be between {min} and {max}" : rule.ErrorMessage);
                            }
                        }
                    }
                    break;

                case "regex":
                    if (value is string regexInput && rule.RuleValue is string pattern)
                    {
                        var regexResult = await ValidateRegexAsync(pattern, regexInput, cancellationToken);
                        if (!regexResult.IsValid)
                        {
                            result.IsValid = false;
                            result.Errors.Add(string.IsNullOrEmpty(rule.ErrorMessage) ? $"Property {propertyName} does not match required pattern" : rule.ErrorMessage);
                        }
                    }
                    break;

                default:
                    _loggingService.LogWarning($"Unknown validation rule type: {rule.RuleType}");
                    break;
            }
        }
        catch (Exception ex)
        {
            _loggingService.LogError($"Error validating rule {rule.RuleType} for property {propertyName}: {ex.Message}", ex);
            result.IsValid = false;
            result.Errors.Add($"Validation rule error: {ex.Message}");
        }

        return result;
    }
}
