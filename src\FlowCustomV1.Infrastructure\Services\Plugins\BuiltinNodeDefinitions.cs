using FlowCustomV1.Core.Models.Plugins;

namespace FlowCustomV1.Infrastructure.Services.Plugins;

/// <summary>
/// 内置节点定义集合
/// </summary>
public static class BuiltinNodeDefinitions
{
    /// <summary>
    /// 获取控制流节点定义
    /// </summary>
    public static List<BuiltinPluginDefinition> GetControlFlowNodes()
    {
        return new List<BuiltinPluginDefinition>
        {
            new BuiltinPluginDefinition
            {
                NodeType = "IfCondition",
                Name = "条件分支",
                DisplayName = "条件判断",
                Description = "根据条件执行不同的分支逻辑",
                Version = "1.0.0",
                Author = "FlowCustomV1",
                Tags = new List<string> { "builtin", "control", "condition", "branch" },
                ExecutionLogic = new ExecutionLogicTemplate
                {
                    Type = ExecutionLogicType.Code,
                    CodeTemplate = @"
                        // 获取条件配置
                        var condition = context.InputData.GetValueOrDefault(""condition"", ""true"").ToString();
                        var leftValue = context.InputData.GetValueOrDefault(""leftValue"", """");
                        var rightValue = context.InputData.GetValueOrDefault(""rightValue"", """");
                        var operator_ = context.InputData.GetValueOrDefault(""operator"", ""equals"").ToString();
                        
                        bool conditionResult = false;
                        
                        try
                        {
                            // 简单的条件评估
                            switch (operator_.ToLower())
                            {
                                case ""equals"":
                                case ""=="":
                                    conditionResult = leftValue?.ToString() == rightValue?.ToString();
                                    break;
                                    
                                case ""notequals"":
                                case ""!="":
                                    conditionResult = leftValue?.ToString() != rightValue?.ToString();
                                    break;
                                    
                                case ""greaterthan"":
                                case "">"":
                                    if (double.TryParse(leftValue?.ToString(), out double left) && 
                                        double.TryParse(rightValue?.ToString(), out double right))
                                    {
                                        conditionResult = left > right;
                                    }
                                    break;
                                    
                                case ""lessthan"":
                                case ""<"":
                                    if (double.TryParse(leftValue?.ToString(), out double leftLess) && 
                                        double.TryParse(rightValue?.ToString(), out double rightLess))
                                    {
                                        conditionResult = leftLess < rightLess;
                                    }
                                    break;
                                    
                                case ""contains"":
                                    conditionResult = leftValue?.ToString()?.Contains(rightValue?.ToString() ?? """") ?? false;
                                    break;
                                    
                                case ""startswith"":
                                    conditionResult = leftValue?.ToString()?.StartsWith(rightValue?.ToString() ?? """") ?? false;
                                    break;
                                    
                                case ""endswith"":
                                    conditionResult = leftValue?.ToString()?.EndsWith(rightValue?.ToString() ?? """") ?? false;
                                    break;
                                    
                                case ""isempty"":
                                    conditionResult = string.IsNullOrEmpty(leftValue?.ToString());
                                    break;
                                    
                                case ""isnotempty"":
                                    conditionResult = !string.IsNullOrEmpty(leftValue?.ToString());
                                    break;
                                    
                                default:
                                    // 尝试直接评估条件字符串
                                    conditionResult = bool.TryParse(condition, out bool directResult) ? directResult : false;
                                    break;
                            }
                            
                            result.OutputData[""conditionResult""] = conditionResult;
                            result.OutputData[""branch""] = conditionResult ? ""true"" : ""false"";
                            result.OutputData[""leftValue""] = leftValue;
                            result.OutputData[""rightValue""] = rightValue;
                            result.OutputData[""operator""] = operator_;
                            result.OutputData[""evaluatedAt""] = DateTime.UtcNow;
                            result.OutputData[""success""] = true;
                        }
                        catch (Exception ex)
                        {
                            result.OutputData[""success""] = false;
                            result.OutputData[""error""] = ex.Message;
                            result.OutputData[""conditionResult""] = false;
                            result.OutputData[""branch""] = ""error"";
                        }
                        
                        await Task.CompletedTask;
                    "
                }
            },
            new BuiltinPluginDefinition
            {
                NodeType = "ForLoop",
                Name = "循环执行",
                DisplayName = "For循环",
                Description = "按指定次数循环执行逻辑",
                Version = "1.0.0",
                Author = "FlowCustomV1",
                Tags = new List<string> { "builtin", "control", "loop", "iteration" },
                ExecutionLogic = new ExecutionLogicTemplate
                {
                    Type = ExecutionLogicType.Code,
                    CodeTemplate = @"
                        // 获取循环配置
                        var startIndex = Convert.ToInt32(context.InputData.GetValueOrDefault(""startIndex"", 0));
                        var endIndex = Convert.ToInt32(context.InputData.GetValueOrDefault(""endIndex"", 10));
                        var step = Convert.ToInt32(context.InputData.GetValueOrDefault(""step"", 1));
                        var currentIndex = Convert.ToInt32(context.InputData.GetValueOrDefault(""currentIndex"", startIndex));
                        var maxIterations = Convert.ToInt32(context.InputData.GetValueOrDefault(""maxIterations"", 1000));
                        
                        var iterations = new List<Dictionary<string, object>>();
                        var iterationCount = 0;
                        
                        try
                        {
                            // 执行循环
                            for (int i = currentIndex; i < endIndex && iterationCount < maxIterations; i += step)
                            {
                                var iterationData = new Dictionary<string, object>
                                {
                                    [""index""] = i,
                                    [""iteration""] = iterationCount + 1,
                                    [""timestamp""] = DateTime.UtcNow,
                                    [""data""] = context.InputData.GetValueOrDefault($""data_{i}"", $""Item {i}"")
                                };
                                
                                iterations.Add(iterationData);
                                iterationCount++;
                                
                                // 模拟处理时间
                                await Task.Delay(10, cancellationToken);
                                
                                // 检查取消令牌
                                cancellationToken.ThrowIfCancellationRequested();
                            }
                            
                            result.OutputData[""success""] = true;
                            result.OutputData[""iterations""] = iterations;
                            result.OutputData[""totalIterations""] = iterationCount;
                            result.OutputData[""startIndex""] = startIndex;
                            result.OutputData[""endIndex""] = endIndex;
                            result.OutputData[""step""] = step;
                            result.OutputData[""completed""] = currentIndex >= endIndex;
                            result.OutputData[""nextIndex""] = Math.Min(currentIndex + (iterationCount * step), endIndex);
                            result.OutputData[""executedAt""] = DateTime.UtcNow;
                        }
                        catch (OperationCanceledException)
                        {
                            result.OutputData[""success""] = false;
                            result.OutputData[""error""] = ""Loop execution was cancelled"";
                            result.OutputData[""partialIterations""] = iterations;
                            result.OutputData[""completedIterations""] = iterationCount;
                        }
                        catch (Exception ex)
                        {
                            result.OutputData[""success""] = false;
                            result.OutputData[""error""] = ex.Message;
                            result.OutputData[""errorType""] = ex.GetType().Name;
                            result.OutputData[""partialIterations""] = iterations;
                            result.OutputData[""completedIterations""] = iterationCount;
                        }
                    "
                }
            },
            new BuiltinPluginDefinition
            {
                NodeType = "ParallelExecution",
                Name = "并行执行",
                DisplayName = "并行处理",
                Description = "并行执行多个任务",
                Version = "1.0.0",
                Author = "FlowCustomV1",
                Tags = new List<string> { "builtin", "control", "parallel", "concurrent" },
                ExecutionLogic = new ExecutionLogicTemplate
                {
                    Type = ExecutionLogicType.Code,
                    CodeTemplate = @"
                        // 获取并行执行配置
                        var tasks = context.InputData.GetValueOrDefault(""tasks"", new List<Dictionary<string, object>>()) as List<Dictionary<string, object>> ?? new List<Dictionary<string, object>>();
                        var maxConcurrency = Convert.ToInt32(context.InputData.GetValueOrDefault(""maxConcurrency"", Environment.ProcessorCount));
                        var timeoutMs = Convert.ToInt32(context.InputData.GetValueOrDefault(""timeoutMs"", 30000));
                        
                        var results = new List<Dictionary<string, object>>();
                        var semaphore = new SemaphoreSlim(maxConcurrency, maxConcurrency);
                        
                        try
                        {
                            var parallelTasks = tasks.Select(async (task, index) =>
                            {
                                await semaphore.WaitAsync(cancellationToken);
                                try
                                {
                                    var taskId = task.GetValueOrDefault(""id"", $""task_{index}"").ToString();
                                    var taskData = task.GetValueOrDefault(""data"", new Dictionary<string, object>());
                                    var taskDelay = Convert.ToInt32(task.GetValueOrDefault(""delay"", 100));
                                    
                                    var startTime = DateTime.UtcNow;
                                    
                                    // 模拟任务执行
                                    await Task.Delay(taskDelay, cancellationToken);
                                    
                                    var endTime = DateTime.UtcNow;
                                    
                                    return new Dictionary<string, object>
                                    {
                                        [""taskId""] = taskId,
                                        [""success""] = true,
                                        [""startTime""] = startTime,
                                        [""endTime""] = endTime,
                                        [""duration""] = (endTime - startTime).TotalMilliseconds,
                                        [""result""] = $""Task {taskId} completed successfully"",
                                        [""inputData""] = taskData
                                    };
                                }
                                catch (Exception ex)
                                {
                                    return new Dictionary<string, object>
                                    {
                                        [""taskId""] = task.GetValueOrDefault(""id"", $""task_{index}"").ToString(),
                                        [""success""] = false,
                                        [""error""] = ex.Message,
                                        [""errorType""] = ex.GetType().Name
                                    };
                                }
                                finally
                                {
                                    semaphore.Release();
                                }
                            }).ToArray();
                            
                            // 等待所有任务完成或超时
                            using var timeoutCts = new CancellationTokenSource(timeoutMs);
                            using var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, timeoutCts.Token);
                            
                            var completedTasks = await Task.WhenAll(parallelTasks);
                            results.AddRange(completedTasks);
                            
                            var successCount = results.Count(r => (bool)r.GetValueOrDefault(""success"", false));
                            var failureCount = results.Count - successCount;
                            
                            result.OutputData[""success""] = true;
                            result.OutputData[""results""] = results;
                            result.OutputData[""totalTasks""] = tasks.Count;
                            result.OutputData[""successCount""] = successCount;
                            result.OutputData[""failureCount""] = failureCount;
                            result.OutputData[""maxConcurrency""] = maxConcurrency;
                            result.OutputData[""executedAt""] = DateTime.UtcNow;
                        }
                        catch (OperationCanceledException)
                        {
                            result.OutputData[""success""] = false;
                            result.OutputData[""error""] = ""Parallel execution was cancelled or timed out"";
                            result.OutputData[""partialResults""] = results;
                            result.OutputData[""completedTasks""] = results.Count;
                        }
                        catch (Exception ex)
                        {
                            result.OutputData[""success""] = false;
                            result.OutputData[""error""] = ex.Message;
                            result.OutputData[""errorType""] = ex.GetType().Name;
                            result.OutputData[""partialResults""] = results;
                        }
                        finally
                        {
                            semaphore?.Dispose();
                        }
                    "
                }
            }
        };
    }

    /// <summary>
    /// 获取数据转换节点定义
    /// </summary>
    public static List<BuiltinPluginDefinition> GetDataTransformNodes()
    {
        return new List<BuiltinPluginDefinition>
        {
            new BuiltinPluginDefinition
            {
                NodeType = "DataMapper",
                Name = "数据映射器",
                DisplayName = "数据映射",
                Description = "将输入数据映射到指定的输出格式",
                Version = "1.0.0",
                Author = "FlowCustomV1",
                Tags = new List<string> { "builtin", "transform", "mapping", "data" },
                ExecutionLogic = new ExecutionLogicTemplate
                {
                    Type = ExecutionLogicType.Code,
                    CodeTemplate = @"
                        // 获取映射配置
                        var mappingRules = context.InputData.GetValueOrDefault(""mappingRules"", new Dictionary<string, string>()) as Dictionary<string, string> ?? new Dictionary<string, string>();
                        var sourceData = context.InputData.GetValueOrDefault(""sourceData"", new Dictionary<string, object>()) as Dictionary<string, object> ?? new Dictionary<string, object>();
                        var defaultValue = context.InputData.GetValueOrDefault(""defaultValue"", null);
                        var strictMode = Convert.ToBoolean(context.InputData.GetValueOrDefault(""strictMode"", false));

                        var mappedData = new Dictionary<string, object>();
                        var mappingErrors = new List<string>();

                        try
                        {
                            // 如果没有提供源数据，使用输入数据作为源数据
                            if (!sourceData.Any())
                            {
                                sourceData = context.InputData.Where(kvp => kvp.Key != ""mappingRules"" && kvp.Key != ""defaultValue"" && kvp.Key != ""strictMode"").ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
                            }

                            // 执行映射
                            foreach (var rule in mappingRules)
                            {
                                var targetKey = rule.Key;
                                var sourceKey = rule.Value;

                                if (sourceData.ContainsKey(sourceKey))
                                {
                                    mappedData[targetKey] = sourceData[sourceKey];
                                }
                                else if (defaultValue != null)
                                {
                                    mappedData[targetKey] = defaultValue;
                                }
                                else if (strictMode)
                                {
                                    mappingErrors.Add($""Source key '{sourceKey}' not found for target key '{targetKey}'"");
                                }
                            }

                            // 如果没有映射规则，执行直接复制
                            if (!mappingRules.Any())
                            {
                                mappedData = new Dictionary<string, object>(sourceData);
                            }

                            result.OutputData[""success""] = !mappingErrors.Any();
                            result.OutputData[""mappedData""] = mappedData;
                            result.OutputData[""sourceCount""] = sourceData.Count;
                            result.OutputData[""mappedCount""] = mappedData.Count;
                            result.OutputData[""mappingRules""] = mappingRules;
                            result.OutputData[""mappingErrors""] = mappingErrors;
                            result.OutputData[""mappedAt""] = DateTime.UtcNow;
                        }
                        catch (Exception ex)
                        {
                            result.OutputData[""success""] = false;
                            result.OutputData[""error""] = ex.Message;
                            result.OutputData[""errorType""] = ex.GetType().Name;
                            result.OutputData[""partialMappedData""] = mappedData;
                        }

                        await Task.CompletedTask;
                    "
                }
            },
            new BuiltinPluginDefinition
            {
                NodeType = "DataFilter",
                Name = "数据过滤器",
                DisplayName = "数据过滤",
                Description = "根据指定条件过滤数据",
                Version = "1.0.0",
                Author = "FlowCustomV1",
                Tags = new List<string> { "builtin", "transform", "filter", "data" },
                ExecutionLogic = new ExecutionLogicTemplate
                {
                    Type = ExecutionLogicType.Code,
                    CodeTemplate = @"
                        // 获取过滤配置
                        var filterType = context.InputData.GetValueOrDefault(""filterType"", ""include"").ToString();
                        var filterKeys = context.InputData.GetValueOrDefault(""filterKeys"", new List<string>()) as List<string> ?? new List<string>();
                        var filterConditions = context.InputData.GetValueOrDefault(""filterConditions"", new Dictionary<string, object>()) as Dictionary<string, object> ?? new Dictionary<string, object>();
                        var sourceData = context.InputData.GetValueOrDefault(""sourceData"", new Dictionary<string, object>()) as Dictionary<string, object> ?? new Dictionary<string, object>();

                        var filteredData = new Dictionary<string, object>();
                        var filteredCount = 0;

                        try
                        {
                            // 如果没有提供源数据，使用输入数据作为源数据
                            if (!sourceData.Any())
                            {
                                sourceData = context.InputData.Where(kvp =>
                                    kvp.Key != ""filterType"" &&
                                    kvp.Key != ""filterKeys"" &&
                                    kvp.Key != ""filterConditions"").ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
                            }

                            switch (filterType.ToLower())
                            {
                                case ""include"":
                                    // 包含指定的键
                                    if (filterKeys.Any())
                                    {
                                        foreach (var key in filterKeys)
                                        {
                                            if (sourceData.ContainsKey(key))
                                            {
                                                filteredData[key] = sourceData[key];
                                                filteredCount++;
                                            }
                                        }
                                    }
                                    else
                                    {
                                        filteredData = new Dictionary<string, object>(sourceData);
                                        filteredCount = sourceData.Count;
                                    }
                                    break;

                                case ""exclude"":
                                    // 排除指定的键
                                    foreach (var kvp in sourceData)
                                    {
                                        if (!filterKeys.Contains(kvp.Key))
                                        {
                                            filteredData[kvp.Key] = kvp.Value;
                                            filteredCount++;
                                        }
                                    }
                                    break;

                                case ""condition"":
                                    // 根据条件过滤
                                    foreach (var kvp in sourceData)
                                    {
                                        bool includeItem = true;

                                        foreach (var condition in filterConditions)
                                        {
                                            if (kvp.Key == condition.Key)
                                            {
                                                var expectedValue = condition.Value?.ToString();
                                                var actualValue = kvp.Value?.ToString();

                                                if (actualValue != expectedValue)
                                                {
                                                    includeItem = false;
                                                    break;
                                                }
                                            }
                                        }

                                        if (includeItem)
                                        {
                                            filteredData[kvp.Key] = kvp.Value;
                                            filteredCount++;
                                        }
                                    }
                                    break;

                                case ""notnull"":
                                    // 过滤掉null值
                                    foreach (var kvp in sourceData)
                                    {
                                        if (kvp.Value != null && !string.IsNullOrEmpty(kvp.Value.ToString()))
                                        {
                                            filteredData[kvp.Key] = kvp.Value;
                                            filteredCount++;
                                        }
                                    }
                                    break;

                                default:
                                    filteredData = new Dictionary<string, object>(sourceData);
                                    filteredCount = sourceData.Count;
                                    break;
                            }

                            result.OutputData[""success""] = true;
                            result.OutputData[""filteredData""] = filteredData;
                            result.OutputData[""originalCount""] = sourceData.Count;
                            result.OutputData[""filteredCount""] = filteredCount;
                            result.OutputData[""filterType""] = filterType;
                            result.OutputData[""filterKeys""] = filterKeys;
                            result.OutputData[""filterConditions""] = filterConditions;
                            result.OutputData[""filteredAt""] = DateTime.UtcNow;
                        }
                        catch (Exception ex)
                        {
                            result.OutputData[""success""] = false;
                            result.OutputData[""error""] = ex.Message;
                            result.OutputData[""errorType""] = ex.GetType().Name;
                            result.OutputData[""partialFilteredData""] = filteredData;
                        }

                        await Task.CompletedTask;
                    "
                }
            }
        };
    }

    /// <summary>
    /// 获取外部服务集成节点定义
    /// </summary>
    public static List<BuiltinPluginDefinition> GetExternalServiceNodes()
    {
        return new List<BuiltinPluginDefinition>
        {
            new BuiltinPluginDefinition
            {
                NodeType = "MySqlDatabase",
                Name = "MySQL数据库",
                DisplayName = "MySQL数据库",
                Description = "连接MySQL数据库执行查询和操作",
                Version = "1.0.0",
                Author = "FlowCustomV1",
                Tags = new List<string> { "builtin", "database", "mysql", "external" },
                ExecutionLogic = new ExecutionLogicTemplate
                {
                    Type = ExecutionLogicType.Code,
                    CodeTemplate = @"
                        // 获取MySQL连接配置
                        var connectionString = context.InputData.GetValueOrDefault(""connectionString"", ""Server=localhost;Database=test;Uid=root;Pwd=password;"").ToString();
                        var queryType = context.InputData.GetValueOrDefault(""queryType"", ""SELECT"").ToString();
                        var sqlQuery = context.InputData.GetValueOrDefault(""sqlQuery"", ""SELECT 1"").ToString();
                        var parameters = context.InputData.GetValueOrDefault(""parameters"", new Dictionary<string, object>()) as Dictionary<string, object> ?? new Dictionary<string, object>();
                        var timeout = Convert.ToInt32(context.InputData.GetValueOrDefault(""timeout"", 30));

                        try
                        {
                            // 模拟MySQL数据库连接和查询
                            await Task.Delay(200, cancellationToken); // 模拟数据库连接时间

                            var queryResult = new List<Dictionary<string, object>>();
                            var affectedRows = 0;

                            switch (queryType.ToUpper())
                            {
                                case ""SELECT"":
                                    // 模拟SELECT查询结果
                                    queryResult.Add(new Dictionary<string, object>
                                    {
                                        [""id""] = 1,
                                        [""name""] = ""Sample Data"",
                                        [""created_at""] = DateTime.UtcNow,
                                        [""status""] = ""active""
                                    });
                                    queryResult.Add(new Dictionary<string, object>
                                    {
                                        [""id""] = 2,
                                        [""name""] = ""Another Record"",
                                        [""created_at""] = DateTime.UtcNow.AddHours(-1),
                                        [""status""] = ""inactive""
                                    });
                                    break;

                                case ""INSERT"":
                                case ""UPDATE"":
                                case ""DELETE"":
                                    // 模拟DML操作结果
                                    affectedRows = new Random().Next(1, 5);
                                    break;

                                default:
                                    // 其他SQL操作
                                    affectedRows = 1;
                                    break;
                            }

                            result.OutputData[""success""] = true;
                            result.OutputData[""queryType""] = queryType;
                            result.OutputData[""sqlQuery""] = sqlQuery;
                            result.OutputData[""parameters""] = parameters;
                            result.OutputData[""queryResult""] = queryResult;
                            result.OutputData[""affectedRows""] = affectedRows;
                            result.OutputData[""recordCount""] = queryResult.Count;
                            result.OutputData[""executedAt""] = DateTime.UtcNow;
                            result.OutputData[""executionTime""] = 200; // 模拟执行时间(ms)
                        }
                        catch (Exception ex)
                        {
                            result.OutputData[""success""] = false;
                            result.OutputData[""error""] = ex.Message;
                            result.OutputData[""errorType""] = ex.GetType().Name;
                            result.OutputData[""sqlQuery""] = sqlQuery;
                        }

                        await Task.CompletedTask;
                    "
                }
            },
            new BuiltinPluginDefinition
            {
                NodeType = "NatsMessage",
                Name = "NATS消息队列",
                DisplayName = "NATS消息",
                Description = "发送和接收NATS消息队列消息",
                Version = "1.0.0",
                Author = "FlowCustomV1",
                Tags = new List<string> { "builtin", "messaging", "nats", "external" },
                ExecutionLogic = new ExecutionLogicTemplate
                {
                    Type = ExecutionLogicType.Code,
                    CodeTemplate = @"
                        // 获取NATS配置
                        var operation = context.InputData.GetValueOrDefault(""operation"", ""publish"").ToString();
                        var subject = context.InputData.GetValueOrDefault(""subject"", ""test.subject"").ToString();
                        var message = context.InputData.GetValueOrDefault(""message"", ""Hello NATS"").ToString();
                        var servers = context.InputData.GetValueOrDefault(""servers"", new List<string> { ""nats://localhost:4222"" }) as List<string> ?? new List<string> { ""nats://localhost:4222"" };
                        var timeout = Convert.ToInt32(context.InputData.GetValueOrDefault(""timeout"", 5000));
                        var queueGroup = context.InputData.GetValueOrDefault(""queueGroup"", """").ToString();

                        try
                        {
                            var operationResult = new Dictionary<string, object>();

                            switch (operation.ToLower())
                            {
                                case ""publish"":
                                    // 模拟NATS消息发布
                                    await Task.Delay(100, cancellationToken);

                                    operationResult[""messageId""] = Guid.NewGuid().ToString();
                                    operationResult[""published""] = true;
                                    operationResult[""subject""] = subject;
                                    operationResult[""messageSize""] = System.Text.Encoding.UTF8.GetByteCount(message);
                                    break;

                                case ""subscribe"":
                                    // 模拟NATS消息订阅
                                    await Task.Delay(200, cancellationToken);

                                    operationResult[""subscribed""] = true;
                                    operationResult[""subject""] = subject;
                                    operationResult[""queueGroup""] = queueGroup;
                                    operationResult[""subscriptionId""] = Guid.NewGuid().ToString();
                                    break;

                                case ""request"":
                                    // 模拟NATS请求-响应
                                    await Task.Delay(300, cancellationToken);

                                    operationResult[""requestId""] = Guid.NewGuid().ToString();
                                    operationResult[""response""] = $""Response to: {message}"";
                                    operationResult[""responseTime""] = 300;
                                    break;

                                default:
                                    throw new ArgumentException($""Unsupported NATS operation: {operation}"");
                            }

                            result.OutputData[""success""] = true;
                            result.OutputData[""operation""] = operation;
                            result.OutputData[""subject""] = subject;
                            result.OutputData[""message""] = message;
                            result.OutputData[""servers""] = servers;
                            result.OutputData[""operationResult""] = operationResult;
                            result.OutputData[""executedAt""] = DateTime.UtcNow;
                        }
                        catch (Exception ex)
                        {
                            result.OutputData[""success""] = false;
                            result.OutputData[""error""] = ex.Message;
                            result.OutputData[""errorType""] = ex.GetType().Name;
                            result.OutputData[""operation""] = operation;
                            result.OutputData[""subject""] = subject;
                        }

                        await Task.CompletedTask;
                    "
                }
            },
            new BuiltinPluginDefinition
            {
                NodeType = "RestApiCall",
                Name = "REST API调用",
                DisplayName = "REST API",
                Description = "调用REST API接口，支持GET、POST、PUT、DELETE等方法",
                Version = "1.0.0",
                Author = "FlowCustomV1",
                Tags = new List<string> { "builtin", "api", "rest", "http", "external" },
                ExecutionLogic = new ExecutionLogicTemplate
                {
                    Type = ExecutionLogicType.Code,
                    CodeTemplate = @"
                        // 获取REST API配置
                        var url = context.InputData.GetValueOrDefault(""url"", ""https://jsonplaceholder.typicode.com/posts/1"").ToString();
                        var method = context.InputData.GetValueOrDefault(""method"", ""GET"").ToString();
                        var headers = context.InputData.GetValueOrDefault(""headers"", new Dictionary<string, string>()) as Dictionary<string, string> ?? new Dictionary<string, string>();
                        var body = context.InputData.GetValueOrDefault(""body"", """").ToString();
                        var timeout = Convert.ToInt32(context.InputData.GetValueOrDefault(""timeout"", 30000));
                        var contentType = context.InputData.GetValueOrDefault(""contentType"", ""application/json"").ToString();
                        var authentication = context.InputData.GetValueOrDefault(""authentication"", new Dictionary<string, string>()) as Dictionary<string, string> ?? new Dictionary<string, string>();

                        try
                        {
                            using var httpClient = new HttpClient();
                            httpClient.Timeout = TimeSpan.FromMilliseconds(timeout);

                            // 添加认证头
                            if (authentication.ContainsKey(""type""))
                            {
                                switch (authentication[""type""].ToLower())
                                {
                                    case ""bearer"":
                                        if (authentication.ContainsKey(""token""))
                                        {
                                            httpClient.DefaultRequestHeaders.Authorization =
                                                new System.Net.Http.Headers.AuthenticationHeaderValue(""Bearer"", authentication[""token""]);
                                        }
                                        break;
                                    case ""basic"":
                                        if (authentication.ContainsKey(""username"") && authentication.ContainsKey(""password""))
                                        {
                                            var credentials = Convert.ToBase64String(
                                                System.Text.Encoding.ASCII.GetBytes($""{authentication[""username""]}:{authentication[""password""]}""));
                                            httpClient.DefaultRequestHeaders.Authorization =
                                                new System.Net.Http.Headers.AuthenticationHeaderValue(""Basic"", credentials);
                                        }
                                        break;
                                    case ""apikey"":
                                        if (authentication.ContainsKey(""key"") && authentication.ContainsKey(""value""))
                                        {
                                            httpClient.DefaultRequestHeaders.Add(authentication[""key""], authentication[""value""]);
                                        }
                                        break;
                                }
                            }

                            // 添加自定义请求头
                            foreach (var header in headers)
                            {
                                httpClient.DefaultRequestHeaders.TryAddWithoutValidation(header.Key, header.Value);
                            }

                            HttpResponseMessage response;
                            var startTime = DateTime.UtcNow;

                            // 根据方法发送请求
                            switch (method.ToUpper())
                            {
                                case ""GET"":
                                    response = await httpClient.GetAsync(url, cancellationToken);
                                    break;
                                case ""POST"":
                                    var postContent = new StringContent(body, System.Text.Encoding.UTF8, contentType);
                                    response = await httpClient.PostAsync(url, postContent, cancellationToken);
                                    break;
                                case ""PUT"":
                                    var putContent = new StringContent(body, System.Text.Encoding.UTF8, contentType);
                                    response = await httpClient.PutAsync(url, putContent, cancellationToken);
                                    break;
                                case ""DELETE"":
                                    response = await httpClient.DeleteAsync(url, cancellationToken);
                                    break;
                                case ""PATCH"":
                                    var patchContent = new StringContent(body, System.Text.Encoding.UTF8, contentType);
                                    var patchRequest = new HttpRequestMessage(new HttpMethod(""PATCH""), url) { Content = patchContent };
                                    response = await httpClient.SendAsync(patchRequest, cancellationToken);
                                    break;
                                case ""HEAD"":
                                    var headRequest = new HttpRequestMessage(HttpMethod.Head, url);
                                    response = await httpClient.SendAsync(headRequest, cancellationToken);
                                    break;
                                case ""OPTIONS"":
                                    var optionsRequest = new HttpRequestMessage(HttpMethod.Options, url);
                                    response = await httpClient.SendAsync(optionsRequest, cancellationToken);
                                    break;
                                default:
                                    throw new NotSupportedException($""HTTP method {method} is not supported"");
                            }

                            var endTime = DateTime.UtcNow;
                            var responseBody = await response.Content.ReadAsStringAsync(cancellationToken);
                            var responseHeaders = response.Headers.ToDictionary(h => h.Key, h => string.Join("","", h.Value));

                            // 尝试解析JSON响应
                            object parsedResponse = responseBody;
                            try
                            {
                                if (response.Content.Headers.ContentType?.MediaType?.Contains(""json"") == true)
                                {
                                    parsedResponse = System.Text.Json.JsonSerializer.Deserialize<object>(responseBody);
                                }
                            }
                            catch
                            {
                                // 如果JSON解析失败，保持原始字符串
                            }

                            result.OutputData[""success""] = response.IsSuccessStatusCode;
                            result.OutputData[""statusCode""] = (int)response.StatusCode;
                            result.OutputData[""statusText""] = response.StatusCode.ToString();
                            result.OutputData[""responseBody""] = responseBody;
                            result.OutputData[""parsedResponse""] = parsedResponse;
                            result.OutputData[""responseHeaders""] = responseHeaders;
                            result.OutputData[""contentType""] = response.Content.Headers.ContentType?.ToString();
                            result.OutputData[""contentLength""] = response.Content.Headers.ContentLength;
                            result.OutputData[""requestTime""] = startTime;
                            result.OutputData[""responseTime""] = endTime;
                            result.OutputData[""duration""] = (endTime - startTime).TotalMilliseconds;
                            result.OutputData[""url""] = url;
                            result.OutputData[""method""] = method;
                        }
                        catch (Exception ex)
                        {
                            result.OutputData[""success""] = false;
                            result.OutputData[""error""] = ex.Message;
                            result.OutputData[""errorType""] = ex.GetType().Name;
                            result.OutputData[""url""] = url;
                            result.OutputData[""method""] = method;
                        }

                        await Task.CompletedTask;
                    "
                }
            }
        };
    }
}
