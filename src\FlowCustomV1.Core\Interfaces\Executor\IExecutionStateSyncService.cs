using FlowCustomV1.Core.Models.Executor;
using FlowCustomV1.Core.Models.Workflow;

namespace FlowCustomV1.Core.Interfaces.Executor;

/// <summary>
/// 执行状态同步服务接口
/// 负责在分布式环境中同步工作流执行状态
/// </summary>
public interface IExecutionStateSyncService
{
    /// <summary>
    /// 同步执行状态到集群
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="state">执行状态</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>同步任务</returns>
    Task SyncExecutionStateAsync(string executionId, WorkflowExecutionState state, CancellationToken cancellationToken = default);

    /// <summary>
    /// 同步节点执行状态
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="nodeId">节点ID</param>
    /// <param name="state">节点状态</param>
    /// <param name="result">节点执行结果</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>同步任务</returns>
    Task SyncNodeStateAsync(string executionId, string nodeId, NodeExecutionState state, NodeExecutionResult? result = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 同步执行结果
    /// </summary>
    /// <param name="executionResult">执行结果</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>同步任务</returns>
    Task SyncExecutionResultAsync(WorkflowExecutionResult executionResult, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取执行状态
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>执行状态信息</returns>
    Task<ExecutionStateInfo?> GetExecutionStateAsync(string executionId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 订阅执行状态变更
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="onStateChanged">状态变更回调</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>订阅任务</returns>
    Task SubscribeExecutionStateAsync(string executionId, Action<ExecutionStateChangedEvent> onStateChanged, CancellationToken cancellationToken = default);

    /// <summary>
    /// 取消订阅执行状态变更
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>取消订阅任务</returns>
    Task UnsubscribeExecutionStateAsync(string executionId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 广播执行事件
    /// </summary>
    /// <param name="executionEvent">执行事件</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>广播任务</returns>
    Task BroadcastExecutionEventAsync(ExecutionEvent executionEvent, CancellationToken cancellationToken = default);

    /// <summary>
    /// 同步执行上下文
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="context">执行上下文</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>同步任务</returns>
    Task SyncExecutionContextAsync(string executionId, ExecutionContextSnapshot context, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取执行上下文
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>执行上下文快照</returns>
    Task<ExecutionContextSnapshot?> GetExecutionContextAsync(string executionId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 清理过期的状态数据
    /// </summary>
    /// <param name="expirationTime">过期时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>清理的记录数</returns>
    Task<int> CleanupExpiredStatesAsync(TimeSpan expirationTime, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取状态同步统计信息
    /// </summary>
    /// <returns>同步统计</returns>
    StateSyncStatistics GetSyncStatistics();

    /// <summary>
    /// 启动状态同步服务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>启动任务</returns>
    Task StartAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 停止状态同步服务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>停止任务</returns>
    Task StopAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 状态同步事件
    /// </summary>
    event EventHandler<StateSyncEventArgs>? StateSynced;

    /// <summary>
    /// 状态同步失败事件
    /// </summary>
    event EventHandler<StateSyncFailedEventArgs>? StateSyncFailed;
}

/// <summary>
/// 状态同步事件参数
/// </summary>
public class StateSyncEventArgs : EventArgs
{
    /// <summary>
    /// 执行ID
    /// </summary>
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 同步类型
    /// </summary>
    public StateSyncType SyncType { get; set; }

    /// <summary>
    /// 同步时间戳
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 同步耗时
    /// </summary>
    public TimeSpan Duration { get; set; }

    /// <summary>
    /// 节点ID（可选）
    /// </summary>
    public string? NodeId { get; set; }
}

/// <summary>
/// 状态同步失败事件参数
/// </summary>
public class StateSyncFailedEventArgs : StateSyncEventArgs
{
    /// <summary>
    /// 错误信息
    /// </summary>
    public string ErrorMessage { get; set; } = string.Empty;

    /// <summary>
    /// 异常信息
    /// </summary>
    public Exception? Exception { get; set; }

    /// <summary>
    /// 重试次数
    /// </summary>
    public int RetryCount { get; set; }
}
