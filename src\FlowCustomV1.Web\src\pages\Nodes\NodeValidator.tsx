import React from 'react';
import { Row, Col, Statistic, Button, Space, Tag, Alert, Progress } from 'antd';
import { 
  CheckCircleOutlined, 
  ExclamationCircleOutlined, 
  CloseCircleOutlined,
  SettingOutlined,
  ReloadOutlined,
  BugOutlined
} from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';

const NodeValidator: React.FC = () => {
  return (
    <div className="p-6">
      <div className="mb-6">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h1 className="text-2xl font-semibold text-gray-900 mb-2">
              <CheckCircleOutlined className="mr-2" />
              Validator 验证器节点
            </h1>
            <p className="text-gray-600">工作流验证和规则检查服务管理</p>
          </div>
          <Space>
            <Button icon={<SettingOutlined />}>
              节点配置
            </Button>
            <Button type="primary" icon={<ReloadOutlined />}>
              刷新状态
            </Button>
          </Space>
        </div>

        <Alert
          message="Validator 节点服务"
          description="负责工作流的结构验证、语义检查、循环依赖检测和性能分析"
          type="success"
          showIcon
          className="mb-6"
        />
      </div>

      {/* 验证统计 */}
      <Row gutter={[16, 16]} className="mb-6">
        <Col xs={24} sm={12} lg={6}>
          <ProCard>
            <Statistic
              title="验证请求"
              value={156}
              prefix={<BugOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </ProCard>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <ProCard>
            <Statistic
              title="通过验证"
              value={142}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </ProCard>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <ProCard>
            <Statistic
              title="验证失败"
              value={14}
              prefix={<CloseCircleOutlined />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </ProCard>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <ProCard>
            <Statistic
              title="缓存命中率"
              value={85}
              suffix="%"
              prefix={<ExclamationCircleOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </ProCard>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        {/* 验证服务 */}
        <Col xs={24} lg={12}>
          <ProCard title="验证服务">
            <div className="space-y-4">
              <div className="flex justify-between items-center p-3 border rounded">
                <div>
                  <div className="font-medium">结构验证服务</div>
                  <div className="text-sm text-gray-500">工作流节点和连接结构检查</div>
                </div>
                <Tag color="green">运行中</Tag>
              </div>
              
              <div className="flex justify-between items-center p-3 border rounded">
                <div>
                  <div className="font-medium">语义检查服务</div>
                  <div className="text-sm text-gray-500">节点配置和参数语义验证</div>
                </div>
                <Tag color="green">运行中</Tag>
              </div>
              
              <div className="flex justify-between items-center p-3 border rounded">
                <div>
                  <div className="font-medium">循环依赖检测</div>
                  <div className="text-sm text-gray-500">检测工作流中的循环依赖</div>
                </div>
                <Tag color="green">运行中</Tag>
              </div>
              
              <div className="flex justify-between items-center p-3 border rounded">
                <div>
                  <div className="font-medium">性能分析服务</div>
                  <div className="text-sm text-gray-500">工作流性能预测和优化建议</div>
                </div>
                <Tag color="green">运行中</Tag>
              </div>
              
              <div className="flex justify-between items-center p-3 border rounded">
                <div>
                  <div className="font-medium">分布式验证缓存</div>
                  <div className="text-sm text-gray-500">验证结果缓存和同步</div>
                </div>
                <Tag color="green">运行中</Tag>
              </div>
            </div>
          </ProCard>
        </Col>

        {/* 验证规则引擎 */}
        <Col xs={24} lg={12}>
          <ProCard title="验证规则引擎">
            <div className="space-y-3">
              <div className="border rounded p-3">
                <div className="flex justify-between items-start mb-2">
                  <div className="font-medium">基础结构规则</div>
                  <Tag color="blue">活跃</Tag>
                </div>
                <div className="text-sm text-gray-500 mb-2">
                  检查节点连接、端口匹配等基础结构
                </div>
                <Progress percent={100} size="small" />
              </div>
              
              <div className="border rounded p-3">
                <div className="flex justify-between items-start mb-2">
                  <div className="font-medium">数据类型规则</div>
                  <Tag color="blue">活跃</Tag>
                </div>
                <div className="text-sm text-gray-500 mb-2">
                  验证节点间数据类型兼容性
                </div>
                <Progress percent={95} size="small" />
              </div>
              
              <div className="border rounded p-3">
                <div className="flex justify-between items-start mb-2">
                  <div className="font-medium">业务逻辑规则</div>
                  <Tag color="blue">活跃</Tag>
                </div>
                <div className="text-sm text-gray-500 mb-2">
                  检查业务逻辑的合理性和完整性
                </div>
                <Progress percent={88} size="small" />
              </div>
              
              <div className="border rounded p-3">
                <div className="flex justify-between items-start mb-2">
                  <div className="font-medium">性能优化规则</div>
                  <Tag color="orange">开发中</Tag>
                </div>
                <div className="text-sm text-gray-500 mb-2">
                  分析性能瓶颈并提供优化建议
                </div>
                <Progress percent={65} size="small" />
              </div>
            </div>
          </ProCard>
        </Col>
      </Row>

      {/* 最近验证记录 */}
      <Row className="mt-6">
        <Col span={24}>
          <ProCard title="最近验证记录">
            <div className="space-y-3">
              <div className="flex justify-between items-center p-3 border rounded">
                <div className="flex-1">
                  <div className="font-medium">数据处理流水线</div>
                  <div className="text-sm text-gray-500">结构验证 + 语义检查</div>
                </div>
                <div className="text-center mx-4">
                  <Tag color="green">通过</Tag>
                </div>
                <div className="text-right text-sm text-gray-400">
                  2025-01-13 15:30:25
                </div>
              </div>
              
              <div className="flex justify-between items-center p-3 border rounded">
                <div className="flex-1">
                  <div className="font-medium">API数据同步</div>
                  <div className="text-sm text-gray-500">循环依赖检测</div>
                </div>
                <div className="text-center mx-4">
                  <Tag color="red">失败</Tag>
                </div>
                <div className="text-right text-sm text-gray-400">
                  2025-01-13 15:28:12
                </div>
              </div>
              
              <div className="flex justify-between items-center p-3 border rounded">
                <div className="flex-1">
                  <div className="font-medium">文件批处理</div>
                  <div className="text-sm text-gray-500">性能分析</div>
                </div>
                <div className="text-center mx-4">
                  <Tag color="orange">警告</Tag>
                </div>
                <div className="text-right text-sm text-gray-400">
                  2025-01-13 15:25:08
                </div>
              </div>
              
              <div className="flex justify-between items-center p-3 border rounded">
                <div className="flex-1">
                  <div className="font-medium">邮件通知系统</div>
                  <div className="text-sm text-gray-500">完整验证</div>
                </div>
                <div className="text-center mx-4">
                  <Tag color="green">通过</Tag>
                </div>
                <div className="text-right text-sm text-gray-400">
                  2025-01-13 15:20:45
                </div>
              </div>
            </div>
          </ProCard>
        </Col>
      </Row>

      {/* API 端点 */}
      <Row className="mt-6">
        <Col span={24}>
          <ProCard title="API 端点">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="border rounded p-3">
                <div className="font-medium text-blue-600">POST /api/validator/validate</div>
                <div className="text-sm text-gray-500">验证工作流</div>
              </div>
              
              <div className="border rounded p-3">
                <div className="font-medium text-green-600">POST /api/validator/batch-validate</div>
                <div className="text-sm text-gray-500">批量验证</div>
              </div>
              
              <div className="border rounded p-3">
                <div className="font-medium text-orange-600">GET /api/validator/rules</div>
                <div className="text-sm text-gray-500">获取验证规则</div>
              </div>
              
              <div className="border rounded p-3">
                <div className="font-medium text-purple-600">POST /api/validator/rules</div>
                <div className="text-sm text-gray-500">创建验证规则</div>
              </div>
              
              <div className="border rounded p-3">
                <div className="font-medium text-cyan-600">GET /api/validator/cache/stats</div>
                <div className="text-sm text-gray-500">缓存统计</div>
              </div>
              
              <div className="border rounded p-3">
                <div className="font-medium text-pink-600">DELETE /api/validator/cache</div>
                <div className="text-sm text-gray-500">清理缓存</div>
              </div>
            </div>
          </ProCard>
        </Col>
      </Row>
    </div>
  );
};

export default NodeValidator;
