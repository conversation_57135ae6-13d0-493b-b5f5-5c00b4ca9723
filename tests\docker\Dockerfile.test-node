# FlowCustomV1 测试节点 Docker 镜像
# 支持多角色节点：Master, Worker, Designer, Validator

FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
WORKDIR /app
EXPOSE 5000

# 安装必要的工具
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    procps \
    net-tools \
    netcat-openbsd \
    && rm -rf /var/lib/apt/lists/*

FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build

# 构建参数
ARG NODE_ROLE=Worker
ARG NODE_ID=test-node
ARG REGION=Default

WORKDIR /src

# 复制项目文件
COPY ["src/FlowCustomV1.Api/FlowCustomV1.Api.csproj", "src/FlowCustomV1.Api/"]
COPY ["src/FlowCustomV1.Infrastructure/FlowCustomV1.Infrastructure.csproj", "src/FlowCustomV1.Infrastructure/"]
COPY ["src/FlowCustomV1.Engine/FlowCustomV1.Engine.csproj", "src/FlowCustomV1.Engine/"]
COPY ["src/FlowCustomV1.Core/FlowCustomV1.Core.csproj", "src/FlowCustomV1.Core/"]

# 还原依赖
RUN dotnet restore "src/FlowCustomV1.Api/FlowCustomV1.Api.csproj"

# 复制所有源代码
COPY . .

# 构建应用
WORKDIR "/src/src/FlowCustomV1.Api"
RUN dotnet build "FlowCustomV1.Api.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "FlowCustomV1.Api.csproj" -c Release -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app

# 复制发布的应用
COPY --from=publish /app/publish .

# 删除默认配置文件，避免配置冲突
RUN rm -f ./appsettings.json ./appsettings.Development.json

# 复制测试配置文件
COPY ["tests/docker/config/appsettings.Test.json", "./appsettings.Test.json"]
COPY ["tests/docker/config/appsettings.Production.json", "./appsettings.Production.json"]
COPY ["tests/docker/scripts/", "./scripts/"]

# 设置权限
RUN chmod +x ./scripts/*.sh

# 创建日志目录
RUN mkdir -p /app/logs /app/data

# 环境变量
ENV ASPNETCORE_ENVIRONMENT=Docker
ENV ASPNETCORE_URLS=http://+:5000
ENV NODE_ROLE=${NODE_ROLE}
ENV NODE_ID=${NODE_ID}
ENV REGION=${REGION}

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:5000/health || exit 1

# 启动脚本
COPY ["tests/docker/scripts/start-node.sh", "./start-node.sh"]
RUN chmod +x ./start-node.sh

ENTRYPOINT ["./start-node.sh"]
