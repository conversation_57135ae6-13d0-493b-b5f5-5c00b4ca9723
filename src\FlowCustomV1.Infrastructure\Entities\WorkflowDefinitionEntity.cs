using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FlowCustomV1.Infrastructure.Entities;

/// <summary>
/// 工作流定义实体
/// 对应数据库中的WorkflowDefinitions表
/// </summary>
[Table("WorkflowDefinitions")]
public class WorkflowDefinitionEntity
{
    /// <summary>
    /// 主键ID
    /// </summary>
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public long Id { get; set; }

    /// <summary>
    /// 工作流唯一标识符
    /// </summary>
    [Column(TypeName = "varchar(255)")]
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 工作流名称
    /// </summary>
    [Required]
    [MaxLength(200)]
    [Column("Name")]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 工作流描述
    /// </summary>
    [Column("Description", TypeName = "TEXT")]
    public string? Description { get; set; }

    /// <summary>
    /// 工作流版本
    /// </summary>
    [Required]
    [MaxLength(20)]
    [Column("Version")]
    public string Version { get; set; } = "1.0.0";

    /// <summary>
    /// 工作流作者
    /// </summary>
    [MaxLength(100)]
    [Column("Author")]
    public string? Author { get; set; }

    /// <summary>
    /// 工作流定义JSON
    /// </summary>
    [Required]
    [Column("DefinitionJson", TypeName = "TEXT")]
    public string DefinitionJson { get; set; } = string.Empty;

    /// <summary>
    /// 创建时间
    /// </summary>
    [Required]
    [Column("CreatedAt")]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 最后修改时间
    /// </summary>
    [Required]
    [Column("LastModifiedAt")]
    public DateTime LastModifiedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 是否激活
    /// </summary>
    [Column("IsActive")]
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// 标签（JSON格式存储）
    /// </summary>
    [Column("Tags", TypeName = "TEXT")]
    public string? Tags { get; set; }

    /// <summary>
    /// 元数据（JSON格式存储）
    /// </summary>
    [Column("Metadata", TypeName = "TEXT")]
    public string? Metadata { get; set; }

    /// <summary>
    /// 关联的工作流实例
    /// </summary>
    public virtual ICollection<WorkflowInstanceEntity> Instances { get; set; } = new List<WorkflowInstanceEntity>();

    /// <summary>
    /// 创建时间戳
    /// </summary>
    [Timestamp]
    public byte[]? RowVersion { get; set; }
}
