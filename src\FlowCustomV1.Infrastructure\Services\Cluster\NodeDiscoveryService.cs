using System.Collections.Concurrent;
using FlowCustomV1.Core.Interfaces.Messaging;
using FlowCustomV1.Core.Interfaces.Services;
using FlowCustomV1.Core.Models.Cluster;
using FlowCustomV1.Core.Models.Messages;
using FlowCustomV1.Infrastructure.Configuration;
using FlowCustomV1.Infrastructure.Services.Messaging;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NodeInfo = FlowCustomV1.Core.Models.Cluster.NodeInfo;
using NodeStatusChangedEventArgs = FlowCustomV1.Core.Interfaces.Services.NodeStatusChangedEventArgs;

namespace FlowCustomV1.Infrastructure.Services.Cluster;

/// <summary>
/// 节点服务发现实现
/// 基于NATS消息系统的分布式节点发现和管理服务
/// </summary>
public class NodeDiscoveryService : INodeDiscoveryService
{
    private readonly ILogger<NodeDiscoveryService> _logger;
    private readonly INatsService _natsService;
    private readonly IMessageTopicService _topicService;
    private readonly NodeDiscoveryConfiguration _config;
    
    private readonly ConcurrentDictionary<string, NodeInfo> _knownNodes;
    private readonly Timer _heartbeatTimer;
    private readonly Timer _nodeCleanupTimer;
    private readonly object _lockObject = new();
    
    private NodeInfo _currentNode;
    private bool _isStarted;
    private bool _disposed;

    /// <summary>
    /// 初始化节点发现服务
    /// </summary>
    public NodeDiscoveryService(
        ILogger<NodeDiscoveryService> logger,
        INatsService natsService,
        IMessageTopicService topicService,
        IOptions<NodeDiscoveryConfiguration> config)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _natsService = natsService ?? throw new ArgumentNullException(nameof(natsService));
        _topicService = topicService ?? throw new ArgumentNullException(nameof(topicService));
        _config = config.Value ?? throw new ArgumentNullException(nameof(config));

        _knownNodes = new ConcurrentDictionary<string, NodeInfo>();
        _currentNode = CreateDefaultNodeInfo();
        
        // 初始化定时器（但不启动）
        _heartbeatTimer = new Timer(SendHeartbeatCallback, null, Timeout.Infinite, Timeout.Infinite);
        _nodeCleanupTimer = new Timer(CleanupOfflineNodesCallback, null, Timeout.Infinite, Timeout.Infinite);
        
        _logger.LogInformation("NodeDiscoveryService initialized for node {NodeId}", _currentNode.NodeId);
    }

    #region 生命周期管理

    /// <inheritdoc />
    public bool IsStarted => _isStarted;

    /// <inheritdoc />
    public async Task StartAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(NodeDiscoveryService));

        if (_isStarted)
        {
            _logger.LogWarning("NodeDiscoveryService is already started");
            return;
        }

        _logger.LogInformation("Starting NodeDiscoveryService...");

        try
        {
            // 确保NATS连接
            if (!_natsService.IsConnected)
            {
                await _natsService.ConnectAsync(cancellationToken);
            }

            // 订阅集群相关主题
            await SubscribeToClusterTopicsAsync(cancellationToken);

            // 注册当前节点
            await RegisterNodeAsync(_currentNode, cancellationToken);

            // 启动定时器
            lock (_lockObject)
            {
                if (!_isStarted)
                {
                    var heartbeatInterval = TimeSpan.FromSeconds(_config.HeartbeatIntervalSeconds);
                    var cleanupInterval = TimeSpan.FromSeconds(_config.NodeCleanupIntervalSeconds);
                    
                    _heartbeatTimer.Change(heartbeatInterval, heartbeatInterval);
                    _nodeCleanupTimer.Change(cleanupInterval, cleanupInterval);
                    
                    _isStarted = true;
                }
            }

            _logger.LogInformation("NodeDiscoveryService started successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start NodeDiscoveryService");
            throw;
        }
    }

    /// <inheritdoc />
    public async Task StopAsync(CancellationToken cancellationToken = default)
    {
        if (!_isStarted)
        {
            _logger.LogWarning("NodeDiscoveryService is not started");
            return;
        }

        _logger.LogInformation("Stopping NodeDiscoveryService...");

        try
        {
            // 停止定时器
            lock (_lockObject)
            {
                if (_isStarted)
                {
                    _heartbeatTimer.Change(Timeout.Infinite, Timeout.Infinite);
                    _nodeCleanupTimer.Change(Timeout.Infinite, Timeout.Infinite);
                    _isStarted = false;
                }
            }

            // 注销当前节点
            await UnregisterNodeAsync(cancellationToken);

            _logger.LogInformation("NodeDiscoveryService stopped successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while stopping NodeDiscoveryService");
            throw;
        }
    }

    #endregion

    #region 节点注册管理

    /// <inheritdoc />
    public async Task RegisterNodeAsync(NodeInfo nodeInfo, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(nodeInfo);

        _logger.LogInformation("Registering node {NodeId} ({NodeName}) with role {Role}", 
            nodeInfo.NodeId, nodeInfo.NodeName, nodeInfo.Mode);

        try
        {
            // 更新节点状态
            nodeInfo.Status = NodeStatus.Healthy;
            nodeInfo.Timestamps.CreatedAt = DateTime.UtcNow;
            nodeInfo.Timestamps.LastActiveAt = DateTime.UtcNow;

            // 如果是当前节点，更新引用
            if (nodeInfo.NodeId == _currentNode.NodeId)
            {
                _currentNode = nodeInfo;
            }

            // 添加到已知节点列表
            _knownNodes.AddOrUpdate(nodeInfo.NodeId, nodeInfo, (key, existing) => nodeInfo);

            // 创建注册消息
            var registrationMessage = new NodeRegistrationMessage
            {
                MessageId = Guid.NewGuid().ToString(),
                SenderId = nodeInfo.NodeId,
                CreatedAt = DateTime.UtcNow,
                NodeInfo = nodeInfo,
                RegistrationType = RegistrationType.Join
            };

            // 发布注册消息到集群
            var registrationTopic = _topicService.GetNodeRegisterTopic();
            await _natsService.PublishAsync(registrationTopic, registrationMessage, cancellationToken);

            // 触发节点加入事件
            OnNodeJoined(nodeInfo);

            _logger.LogInformation("Node {NodeId} registered successfully", nodeInfo.NodeId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to register node {NodeId}", nodeInfo.NodeId);
            throw;
        }
    }

    /// <inheritdoc />
    public async Task UnregisterNodeAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Unregistering current node {NodeId}", _currentNode.NodeId);

        try
        {
            // 更新节点状态
            _currentNode.Status = NodeStatus.Offline;
            _currentNode.Timestamps.UpdateLastActive();

            // 创建注销消息
            var unregistrationMessage = new NodeRegistrationMessage
            {
                MessageId = Guid.NewGuid().ToString(),
                SenderId = _currentNode.NodeId,
                CreatedAt = DateTime.UtcNow,
                NodeInfo = _currentNode,
                RegistrationType = RegistrationType.Leave
            };

            // 发布注销消息到集群
            var unregistrationTopic = _topicService.GetNodeUnregisterTopic();
            await _natsService.PublishAsync(unregistrationTopic, unregistrationMessage, cancellationToken);

            // 从已知节点列表中移除
            _knownNodes.TryRemove(_currentNode.NodeId, out _);

            // 触发节点离开事件
            OnNodeLeft(_currentNode.NodeId, "Node shutdown");

            _logger.LogInformation("Node {NodeId} unregistered successfully", _currentNode.NodeId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to unregister node {NodeId}", _currentNode.NodeId);
            throw;
        }
    }

    /// <inheritdoc />
    public Task UpdateNodeInfoAsync(NodeInfo nodeInfo, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(nodeInfo);

        _logger.LogDebug("Updating node info for {NodeId}", nodeInfo.NodeId);

        try
        {
            // 更新时间戳
            nodeInfo.Timestamps.UpdateLastActive();

            // 如果是当前节点，更新引用
            if (nodeInfo.NodeId == _currentNode.NodeId)
            {
                var oldStatus = _currentNode.Status;
                _currentNode = nodeInfo;
                
                // 如果状态发生变化，触发事件
                if (oldStatus != nodeInfo.Status)
                {
                    OnNodeStatusChanged(nodeInfo.NodeId, oldStatus, nodeInfo.Status);
                }
            }

            // 更新已知节点列表
            _knownNodes.AddOrUpdate(nodeInfo.NodeId, nodeInfo, (key, existing) =>
            {
                var oldStatus = existing.Status;
                if (oldStatus != nodeInfo.Status)
                {
                    OnNodeStatusChanged(nodeInfo.NodeId, oldStatus, nodeInfo.Status);
                }
                return nodeInfo;
            });

            _logger.LogDebug("Node info updated for {NodeId}", nodeInfo.NodeId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update node info for {NodeId}", nodeInfo.NodeId);
            throw;
        }

        return Task.CompletedTask;
    }

    #endregion

    #region 心跳机制

    /// <inheritdoc />
    public int HeartbeatIntervalSeconds 
    { 
        get => _config.HeartbeatIntervalSeconds; 
        set => _config.HeartbeatIntervalSeconds = value; 
    }

    /// <inheritdoc />
    public int NodeTimeoutSeconds 
    { 
        get => _config.NodeTimeoutSeconds; 
        set => _config.NodeTimeoutSeconds = value; 
    }

    /// <inheritdoc />
    public async Task SendHeartbeatAsync(CancellationToken cancellationToken = default)
    {
        if (!_isStarted)
        {
            _logger.LogDebug("Service not started, skipping heartbeat");
            return;
        }

        try
        {
            // 更新当前节点的最后活跃时间
            _currentNode.Timestamps.UpdateLastActive();

            // 创建心跳消息
            var heartbeatMessage = new NodeHeartbeatMessage
            {
                MessageId = Guid.NewGuid().ToString(),
                SenderId = _currentNode.NodeId,
                CreatedAt = DateTime.UtcNow,
                NodeStatus = _currentNode.Status,
                LoadInfo = _currentNode.Load,
                HealthStatus = _currentNode.Health,
                NodeInfo = _currentNode
            };

            // 发布心跳消息
            var heartbeatTopic = _topicService.GetNodeHeartbeatTopic(_currentNode.NodeId);
            await _natsService.PublishAsync(heartbeatTopic, heartbeatMessage, cancellationToken);

            _logger.LogDebug("Heartbeat sent for node {NodeId}", _currentNode.NodeId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send heartbeat for node {NodeId}", _currentNode.NodeId);
        }
    }

    #endregion

    #region 属性

    /// <inheritdoc />
    public NodeInfo CurrentNode => _currentNode;

    /// <inheritdoc />
    public IReadOnlyList<NodeInfo> KnownNodes => _knownNodes.Values.ToList();

    /// <inheritdoc />
    public int OnlineNodeCount => _knownNodes.Values.Count(n => n.Status == NodeStatus.Healthy);

    #endregion

    #region 事件

    /// <inheritdoc />
    public event EventHandler<NodeJoinedEventArgs>? NodeJoined;

    /// <inheritdoc />
    public event EventHandler<NodeLeftEventArgs>? NodeLeft;

    /// <inheritdoc />
    public event EventHandler<NodeStatusChangedEventArgs>? NodeStatusChanged;

    /// <inheritdoc />
    public event EventHandler<HeartbeatReceivedEventArgs>? HeartbeatReceived;

    #endregion

    #region 服务发现

    /// <inheritdoc />
    public async Task<IReadOnlyList<NodeInfo>> DiscoverAllNodesAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Discovering all nodes in cluster");

        try
        {
            // 发送发现请求
            await SendDiscoveryRequestAsync(null, cancellationToken);

            // 等待响应
            await Task.Delay(TimeSpan.FromSeconds(_config.DiscoveryTimeoutSeconds), cancellationToken);

            // 返回所有已知节点
            var allNodes = _knownNodes.Values.ToList();
            _logger.LogInformation("Discovered {NodeCount} nodes", allNodes.Count);

            return allNodes;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to discover all nodes");
            throw;
        }
    }

    /// <inheritdoc />
    public async Task<IReadOnlyList<NodeInfo>> DiscoverNodesByRoleAsync(string role, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(role);

        _logger.LogDebug("Discovering nodes with role {Role}", role);

        try
        {
            // 创建角色查询
            var query = new NodeDiscoveryQuery
            {
                NodeMode = Enum.TryParse<NodeMode>(role, true, out var mode) ? mode : null
            };

            return await DiscoverNodesAsync(query, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to discover nodes by role {Role}", role);
            throw;
        }
    }

    /// <inheritdoc />
    public async Task<IReadOnlyList<NodeInfo>> DiscoverNodesAsync(NodeDiscoveryQuery query, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(query);

        _logger.LogDebug("Discovering nodes with query: {Query}", query);

        try
        {
            // 发送发现请求
            await SendDiscoveryRequestAsync(query, cancellationToken);

            // 等待响应
            await Task.Delay(TimeSpan.FromSeconds(_config.DiscoveryTimeoutSeconds), cancellationToken);

            // 过滤已知节点
            var filteredNodes = FilterNodesByQuery(_knownNodes.Values, query);
            _logger.LogInformation("Discovered {NodeCount} nodes matching query", filteredNodes.Count);

            return filteredNodes;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to discover nodes with query");
            throw;
        }
    }

    /// <inheritdoc />
    public async Task<NodeInfo?> GetNodeAsync(string nodeId, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(nodeId);

        _logger.LogDebug("Getting node {NodeId}", nodeId);

        // 首先检查本地缓存
        if (_knownNodes.TryGetValue(nodeId, out var cachedNode))
        {
            return cachedNode;
        }

        // 如果本地没有，发送发现请求
        await SendDiscoveryRequestAsync(null, cancellationToken);
        await Task.Delay(TimeSpan.FromSeconds(_config.DiscoveryTimeoutSeconds), cancellationToken);

        // 再次检查
        _knownNodes.TryGetValue(nodeId, out var node);
        return node;
    }

    /// <inheritdoc />
    public Task UpdateNodeLoadAsync(string nodeId, NodeLoad loadInfo, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(nodeId);
        ArgumentNullException.ThrowIfNull(loadInfo);

        _logger.LogDebug("Updating load info for node {NodeId}", nodeId);

        try
        {
            if (_knownNodes.TryGetValue(nodeId, out var node))
            {
                node.Load = loadInfo;
                node.Timestamps.UpdateLastActive();

                _logger.LogDebug("Updated load info for node {NodeId}: CPU={CpuUsage}%, Memory={MemoryUsage}%, Tasks={ActiveTasks}/{MaxTasks}",
                    nodeId, loadInfo.CpuUsagePercentage, loadInfo.MemoryUsagePercentage,
                    loadInfo.ActiveTaskCount, loadInfo.MaxTaskCapacity);
            }
            else
            {
                _logger.LogWarning("Node {NodeId} not found for load update", nodeId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update load info for node {NodeId}", nodeId);
            throw;
        }

        return Task.CompletedTask;
    }

    /// <summary>
    /// 发送服务发现请求
    /// </summary>
    private async Task SendDiscoveryRequestAsync(NodeDiscoveryQuery? query, CancellationToken cancellationToken)
    {
        try
        {
            var discoveryMessage = new NodeDiscoveryMessage
            {
                MessageId = Guid.NewGuid().ToString(),
                SenderId = _currentNode.NodeId,
                CreatedAt = DateTime.UtcNow,
                DiscoveryType = DiscoveryType.Request,
                Query = query
            };

            var discoveryTopic = _topicService.GetServiceDiscoveryTopic();
            await _natsService.PublishAsync(discoveryTopic, discoveryMessage, cancellationToken);

            _logger.LogDebug("Discovery request sent");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send discovery request");
            throw;
        }
    }

    /// <summary>
    /// 根据查询条件过滤节点
    /// </summary>
    private List<NodeInfo> FilterNodesByQuery(IEnumerable<NodeInfo> nodes, NodeDiscoveryQuery? query)
    {
        if (query == null)
            return nodes.ToList();

        var result = nodes.AsEnumerable();

        // 按节点模式过滤
        if (query.NodeMode.HasValue)
        {
            result = result.Where(n => n.Mode == query.NodeMode.Value);
        }

        // 按节点状态过滤
        if (query.NodeStatus.HasValue)
        {
            result = result.Where(n => n.Status == query.NodeStatus.Value);
        }

        // 按最小性能等级过滤
        if (query.MinPerformanceLevel.HasValue)
        {
            result = result.Where(n => n.Capabilities.PerformanceLevel >= query.MinPerformanceLevel.Value);
        }

        // 按最大负载评分过滤
        if (query.MaxLoadScore.HasValue)
        {
            result = result.Where(n => n.Load.LoadScore <= query.MaxLoadScore.Value);
        }

        // 按必需能力标签过滤
        if (query.RequiredCapabilityTags.Any())
        {
            result = result.Where(n => query.RequiredCapabilityTags.All(tag =>
                n.Capabilities.Tags.Contains(tag)));
        }

        // 按集群名称过滤
        if (!string.IsNullOrWhiteSpace(query.ClusterNameFilter))
        {
            result = result.Where(n => n.ClusterName.Equals(query.ClusterNameFilter, StringComparison.OrdinalIgnoreCase));
        }

        return result.ToList();
    }

    #endregion

    #region 私有方法

    /// <summary>
    /// 创建默认节点信息
    /// </summary>
    private NodeInfo CreateDefaultNodeInfo()
    {
        var nodeId = Environment.MachineName + "-" + Guid.NewGuid().ToString("N")[..8];
        
        return new NodeInfo
        {
            NodeId = nodeId,
            NodeName = Environment.MachineName,
            ClusterName = _config.ClusterName,
            Mode = NodeMode.Worker,
            Status = NodeStatus.Initializing,
            Network = new NetworkInfo
            {
                IpAddress = "127.0.0.1",
                HttpPort = 5000,
                NatsPort = 4222,
                ManagementPort = 8080,
                HostName = Environment.MachineName
            },
            Capabilities = new NodeCapabilities
            {
                CpuCores = Environment.ProcessorCount,
                MemoryMb = 4096,
                DiskSpaceMb = 10240,
                MaxConcurrentExecutions = Environment.ProcessorCount * 2,
                PerformanceLevel = 5
            },
            Load = new NodeLoad
            {
                MaxTaskCapacity = Environment.ProcessorCount * 2
            },
            Health = new HealthStatus(),
            Timestamps = new Timestamps
            {
                CreatedAt = DateTime.UtcNow,
                LastActiveAt = DateTime.UtcNow
            }
        };
    }

    /// <summary>
    /// 订阅集群相关主题
    /// </summary>
    private async Task SubscribeToClusterTopicsAsync(CancellationToken cancellationToken)
    {
        try
        {
            // 订阅节点注册主题
            var registrationTopic = _topicService.GetNodeRegisterTopic();
            await _natsService.SubscribeAsync<NodeRegistrationMessage>(
                registrationTopic,
                OnNodeRegistrationReceived,
                cancellationToken: cancellationToken);

            // 订阅节点注销主题
            var unregistrationTopic = _topicService.GetNodeUnregisterTopic();
            await _natsService.SubscribeAsync<NodeRegistrationMessage>(
                unregistrationTopic,
                OnNodeUnregistrationReceived,
                cancellationToken: cancellationToken);

            // 订阅心跳主题（通配符订阅所有节点的心跳）
            var heartbeatPattern = _topicService.GetClusterRootTopic() + ".nodes.*";
            await _natsService.SubscribeAsync<NodeHeartbeatMessage>(
                heartbeatPattern,
                OnHeartbeatReceived,
                cancellationToken: cancellationToken);

            // 订阅服务发现主题
            var discoveryTopic = _topicService.GetServiceDiscoveryTopic();
            await _natsService.SubscribeAsync<NodeDiscoveryMessage>(
                discoveryTopic,
                OnDiscoveryRequestReceived,
                cancellationToken: cancellationToken);

            _logger.LogInformation("Subscribed to cluster topics successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to subscribe to cluster topics");
            throw;
        }
    }

    /// <summary>
    /// 处理节点注册消息
    /// </summary>
    private Task OnNodeRegistrationReceived(NodeRegistrationMessage message)
    {
        try
        {
            if (message.NodeInfo == null || message.SenderId == _currentNode.NodeId)
                return Task.CompletedTask;

            _logger.LogInformation("Received node registration from {NodeId}", message.SenderId);

            // 添加到已知节点列表
            _knownNodes.AddOrUpdate(message.NodeInfo.NodeId, message.NodeInfo, (key, existing) => message.NodeInfo);

            // 触发节点加入事件
            OnNodeJoined(message.NodeInfo);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing node registration message from {SenderId}", message.SenderId);
        }

        return Task.CompletedTask;
    }

    /// <summary>
    /// 处理节点注销消息
    /// </summary>
    private Task OnNodeUnregistrationReceived(NodeRegistrationMessage message)
    {
        try
        {
            if (message.SenderId == _currentNode.NodeId)
                return Task.CompletedTask;

            _logger.LogInformation("Received node unregistration from {NodeId}", message.SenderId);

            // 从已知节点列表中移除
            if (_knownNodes.TryRemove(message.SenderId, out var removedNode))
            {
                // 触发节点离开事件
                OnNodeLeft(message.SenderId, "Node unregistered");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing node unregistration message from {SenderId}", message.SenderId);
        }

        return Task.CompletedTask;
    }

    /// <summary>
    /// 处理心跳消息
    /// </summary>
    private Task OnHeartbeatReceived(NodeHeartbeatMessage message)
    {
        try
        {
            if (message.SenderId == _currentNode.NodeId)
                return Task.CompletedTask;

            _logger.LogDebug("Received heartbeat from {NodeId}", message.SenderId);

            // 更新节点信息
            if (_knownNodes.TryGetValue(message.SenderId, out var existingNode))
            {
                var oldStatus = existingNode.Status;

                // 更新节点状态和负载信息
                existingNode.Status = message.NodeStatus;
                existingNode.Load = message.LoadInfo;
                existingNode.Health = message.HealthStatus;
                existingNode.Timestamps.UpdateLastActive();

                // 如果状态发生变化，触发事件
                if (oldStatus != message.NodeStatus)
                {
                    OnNodeStatusChanged(message.SenderId, oldStatus, message.NodeStatus);
                }
            }
            else if (message.NodeInfo != null)
            {
                // 新节点，添加到列表
                _knownNodes.TryAdd(message.SenderId, message.NodeInfo);
                OnNodeJoined(message.NodeInfo);
            }

            // 触发心跳接收事件
            TriggerHeartbeatReceivedEvent(message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing heartbeat message from {SenderId}", message.SenderId);
        }

        return Task.CompletedTask;
    }

    /// <summary>
    /// 处理服务发现请求
    /// </summary>
    private async Task OnDiscoveryRequestReceived(NodeDiscoveryMessage message)
    {
        try
        {
            if (message.SenderId == _currentNode.NodeId)
                return;

            _logger.LogDebug("Received discovery request from {NodeId}", message.SenderId);

            // 响应发现请求，发送当前节点信息
            var responseMessage = new NodeDiscoveryMessage
            {
                MessageId = Guid.NewGuid().ToString(),
                SenderId = _currentNode.NodeId,
                CreatedAt = DateTime.UtcNow,
                DiscoveryType = DiscoveryType.Response,
                DiscoveredNodes = new List<NodeInfo> { _currentNode }
            };

            // 发送响应到发现主题
            var discoveryTopic = _topicService.GetServiceDiscoveryTopic();
            await _natsService.PublishAsync(discoveryTopic, responseMessage);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing discovery request from {SenderId}", message.SenderId);
        }
    }

    /// <summary>
    /// 心跳定时器回调
    /// </summary>
    private async void SendHeartbeatCallback(object? state)
    {
        try
        {
            await SendHeartbeatAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in heartbeat timer callback");
        }
    }

    /// <summary>
    /// 节点清理定时器回调
    /// </summary>
    private void CleanupOfflineNodesCallback(object? state)
    {
        try
        {
            var timeoutThreshold = DateTime.UtcNow.AddSeconds(-_config.NodeTimeoutSeconds);
            var nodesToRemove = new List<string>();

            foreach (var kvp in _knownNodes)
            {
                var node = kvp.Value;
                if (node.NodeId != _currentNode.NodeId &&
                    node.Timestamps.LastActiveAt < timeoutThreshold)
                {
                    nodesToRemove.Add(node.NodeId);
                }
            }

            foreach (var nodeId in nodesToRemove)
            {
                if (_knownNodes.TryRemove(nodeId, out var removedNode))
                {
                    _logger.LogWarning("Removed offline node {NodeId} due to timeout", nodeId);
                    OnNodeLeft(nodeId, "Node timeout");
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in node cleanup timer callback");
        }
    }

    /// <summary>
    /// 触发节点加入事件
    /// </summary>
    private void OnNodeJoined(NodeInfo node)
    {
        try
        {
            NodeJoined?.Invoke(this, new NodeJoinedEventArgs { Node = node });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error invoking NodeJoined event for {NodeId}", node.NodeId);
        }
    }

    /// <summary>
    /// 触发节点离开事件
    /// </summary>
    private void OnNodeLeft(string nodeId, string reason)
    {
        try
        {
            NodeLeft?.Invoke(this, new NodeLeftEventArgs { NodeId = nodeId, Reason = reason });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error invoking NodeLeft event for {NodeId}", nodeId);
        }
    }

    /// <summary>
    /// 触发节点状态变更事件
    /// </summary>
    private void OnNodeStatusChanged(string nodeId, NodeStatus oldStatus, NodeStatus newStatus)
    {
        try
        {
            NodeStatusChanged?.Invoke(this, new NodeStatusChangedEventArgs
            {
                NodeId = nodeId,
                OldStatus = oldStatus,
                NewStatus = newStatus
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error invoking NodeStatusChanged event for {NodeId}", nodeId);
        }
    }

    /// <summary>
    /// 触发心跳接收事件
    /// </summary>
    private void TriggerHeartbeatReceivedEvent(NodeHeartbeatMessage message)
    {
        try
        {
            HeartbeatReceived?.Invoke(this, new HeartbeatReceivedEventArgs
            {
                NodeId = message.SenderId,
                HeartbeatMessage = message
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error invoking HeartbeatReceived event for {NodeId}", message.SenderId);
        }
    }

    #endregion

    #region IDisposable

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (_disposed)
            return;

        try
        {
            if (_isStarted)
            {
                StopAsync().GetAwaiter().GetResult();
            }

            _heartbeatTimer?.Dispose();
            _nodeCleanupTimer?.Dispose();

            _disposed = true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error disposing NodeDiscoveryService");
        }
    }

    #endregion
}
