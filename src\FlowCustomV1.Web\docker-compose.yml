version: '3.8'

services:
  # FlowCustomV1 Web Frontend
  flowcustomv1-web:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: flowcustomv1-web
    ports:
      - "3000:80"
    environment:
      - NODE_ENV=production
    networks:
      - flowcustom-network
    depends_on:
      - flowcustomv1-api
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # FlowCustomV1 API Backend (假设存在)
  flowcustomv1-api:
    image: flowcustomv1-api:latest
    container_name: flowcustomv1-api
    ports:
      - "5000:5000"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ConnectionStrings__DefaultConnection=Server=db;Database=FlowCustomV1;User=sa;Password=YourPassword123!;
    networks:
      - flowcustom-network
    depends_on:
      - db
      - nats
    restart: unless-stopped

  # SQL Server 数据库
  db:
    image: mcr.microsoft.com/mssql/server:2022-latest
    container_name: flowcustomv1-db
    environment:
      - ACCEPT_EULA=Y
      - SA_PASSWORD=YourPassword123!
      - MSSQL_PID=Express
    ports:
      - "1433:1433"
    volumes:
      - db_data:/var/opt/mssql
    networks:
      - flowcustom-network
    restart: unless-stopped

  # NATS 消息队列
  nats:
    image: nats:alpine
    container_name: flowcustomv1-nats
    command: 
      - "--cluster_name=flowcustom-cluster"
      - "--jetstream"
      - "--store_dir=/data"
    ports:
      - "4222:4222"
      - "8222:8222"
    volumes:
      - nats_data:/data
    networks:
      - flowcustom-network
    restart: unless-stopped

  # Redis 缓存
  redis:
    image: redis:alpine
    container_name: flowcustomv1-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - flowcustom-network
    restart: unless-stopped
    command: redis-server --appendonly yes

networks:
  flowcustom-network:
    driver: bridge

volumes:
  db_data:
    driver: local
  nats_data:
    driver: local
  redis_data:
    driver: local
