import requests
import json
import random
from datetime import datetime, timedelta
import time

API_BASE_URL = 'http://localhost:5000/api'

# 工作流模板数据
workflow_templates = [
    {
        'name': '用户注册流程',
        'description': '处理新用户注册的完整流程，包括验证、创建账户和发送欢迎邮件',
        'category': '用户管理',
        'tags': ['用户', '注册', '邮件']
    },
    {
        'name': '订单处理流程',
        'description': '电商订单的完整处理流程，从下单到发货',
        'category': '电商',
        'tags': ['订单', '支付', '物流']
    },
    {
        'name': '数据备份流程',
        'description': '定期数据备份和验证流程',
        'category': '运维',
        'tags': ['备份', '数据库', '定时任务']
    },
    {
        'name': '客户服务工单处理',
        'description': '客户服务工单的自动分配和处理流程',
        'category': '客服',
        'tags': ['工单', '客服', '自动化']
    },
    {
        'name': '财务报表生成',
        'description': '月度财务报表的自动生成和分发流程',
        'category': '财务',
        'tags': ['报表', '财务', '月度']
    },
    {
        'name': '员工入职流程',
        'description': '新员工入职的完整流程，包括账户创建、权限分配等',
        'category': '人事',
        'tags': ['入职', '员工', '权限']
    },
    {
        'name': '库存预警流程',
        'description': '库存不足时的自动预警和补货流程',
        'category': '库存',
        'tags': ['库存', '预警', '补货']
    },
    {
        'name': '营销活动审批',
        'description': '营销活动的多级审批流程',
        'category': '营销',
        'tags': ['审批', '营销', '活动']
    },
    {
        'name': '系统监控告警',
        'description': '系统异常监控和告警通知流程',
        'category': '监控',
        'tags': ['监控', '告警', '系统']
    },
    {
        'name': '合同审批流程',
        'description': '合同的多级审批和签署流程',
        'category': '法务',
        'tags': ['合同', '审批', '签署']
    },
    {
        'name': '产品发布流程',
        'description': '新产品发布的完整流程，包括测试、审批和上线',
        'category': '产品',
        'tags': ['产品', '发布', '测试']
    },
    {
        'name': '客户反馈处理',
        'description': '客户反馈的收集、分析和处理流程',
        'category': '客服',
        'tags': ['反馈', '客户', '分析']
    },
    {
        'name': '供应商管理流程',
        'description': '供应商的评估、选择和管理流程',
        'category': '采购',
        'tags': ['供应商', '采购', '评估']
    },
    {
        'name': '项目立项审批',
        'description': '新项目的立项申请和审批流程',
        'category': '项目',
        'tags': ['项目', '立项', '审批']
    },
    {
        'name': '设备维护流程',
        'description': '设备的定期维护和故障处理流程',
        'category': '运维',
        'tags': ['设备', '维护', '故障']
    }
]

def generate_workflow(template, index):
    """生成工作流数据"""
    import uuid

    publish_statuses = ['Draft', 'Published', 'Deprecated', 'Archived']
    authors = ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十']

    # 生成随机版本号
    major = random.randint(1, 3)
    minor = random.randint(0, 9)
    patch = random.randint(0, 9)

    workflow_id = str(uuid.uuid4()).replace('-', '')

    workflow_data = {
        'workflowId': workflow_id,
        'name': f"{template['name']} v{major}.{minor}",
        'description': template['description'],
        'version': f"{major}.{minor}.{patch}",
        'author': random.choice(authors),
        'isActive': random.random() > 0.1,  # 90% 概率为活跃
        'publishStatus': random.choice(publish_statuses),
        'createdBy': random.choice(authors),
        'lastModifiedBy': random.choice(authors),
        'tags': template['tags'] + [template['category']],
        'nodes': [
            {
                'nodeId': 'start_' + str(uuid.uuid4()).replace('-', '')[:8],
                'nodeType': 'StartNode',
                'name': '开始',
                'description': '工作流开始节点',
                'position': {'x': 100, 'y': 100}
            },
            {
                'nodeId': 'process_' + str(uuid.uuid4()).replace('-', '')[:8],
                'nodeType': 'ProcessNode',
                'name': '处理步骤1',
                'description': '执行主要业务逻辑',
                'position': {'x': 300, 'y': 100}
            },
            {
                'nodeId': 'end_' + str(uuid.uuid4()).replace('-', '')[:8],
                'nodeType': 'EndNode',
                'name': '结束',
                'description': '工作流结束节点',
                'position': {'x': 500, 'y': 100}
            }
        ],
        'connections': [
            {
                'connectionId': 'conn_' + str(uuid.uuid4()).replace('-', '')[:8],
                'sourceNodeId': workflow_data['nodes'][0]['nodeId'] if 'nodes' in locals() else 'start',
                'targetNodeId': workflow_data['nodes'][1]['nodeId'] if 'nodes' in locals() else 'process',
                'sourcePort': 'output',
                'targetPort': 'input'
            }
        ],
        'inputParameters': [],
        'outputParameters': [],
        'configuration': {
            'maxExecutionTime': 300 + random.randint(0, 600),  # 5-15分钟
            'retryCount': random.randint(1, 5),
            'enableLogging': random.random() > 0.2  # 80% 概率启用日志
        }
    }

    # 修正连接中的节点ID引用
    start_node_id = workflow_data['nodes'][0]['nodeId']
    process_node_id = workflow_data['nodes'][1]['nodeId']
    end_node_id = workflow_data['nodes'][2]['nodeId']

    workflow_data['connections'] = [
        {
            'connectionId': 'conn1_' + str(uuid.uuid4()).replace('-', '')[:8],
            'sourceNodeId': start_node_id,
            'targetNodeId': process_node_id,
            'sourcePort': 'output',
            'targetPort': 'input'
        },
        {
            'connectionId': 'conn2_' + str(uuid.uuid4()).replace('-', '')[:8],
            'sourceNodeId': process_node_id,
            'targetNodeId': end_node_id,
            'sourcePort': 'output',
            'targetPort': 'input'
        }
    ]

    return workflow_data

def create_workflow(workflow_data):
    """创建工作流"""
    try:
        response = requests.post(
            f"{API_BASE_URL}/workflows",
            json=workflow_data,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        if response.status_code == 201:
            return response.json()
        else:
            print(f"创建失败，状态码: {response.status_code}")
            print(f"错误信息: {response.text}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")
        return None

def main():
    """主函数"""
    print("开始生成50个测试工作流...")
    
    success_count = 0
    fail_count = 0
    
    for i in range(50):
        template = workflow_templates[i % len(workflow_templates)]
        workflow_data = generate_workflow(template, i)
        
        print(f"正在创建第 {i + 1} 个工作流: {workflow_data['name']}")
        
        result = create_workflow(workflow_data)
        if result:
            success_count += 1
            print(f"✅ 成功创建: {workflow_data['name']}")
        else:
            fail_count += 1
            print(f"❌ 创建失败: {workflow_data['name']}")
        
        # 添加小延迟避免请求过快
        time.sleep(0.1)
    
    print(f"\n生成完成！成功: {success_count}, 失败: {fail_count}")

if __name__ == "__main__":
    main()
