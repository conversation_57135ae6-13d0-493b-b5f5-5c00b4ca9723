using FlowCustomV1.Core.Models.Executor;
using FlowCustomV1.Core.Models.Workflow;
using System.Text.Json.Serialization;

namespace FlowCustomV1.Core.Models.Messages;

/// <summary>
/// 执行请求消息
/// </summary>
public class ExecutionRequestMessage : BaseMessage
{
    /// <summary>
    /// 工作流ID
    /// </summary>
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 工作流定义（可选，如果提供则直接使用）
    /// </summary>
    public WorkflowDefinition? WorkflowDefinition { get; set; }

    /// <summary>
    /// 输入数据
    /// </summary>
    public Dictionary<string, object> InputData { get; set; } = new();

    /// <summary>
    /// 执行优先级
    /// </summary>
    public ExecutionPriority ExecutionPriority { get; set; } = ExecutionPriority.Normal;

    /// <summary>
    /// 执行选项
    /// </summary>
    public ExecutionOptions Options { get; set; } = new();

    /// <summary>
    /// 请求者ID
    /// </summary>
    public string RequesterId { get; set; } = string.Empty;

    /// <summary>
    /// 回调主题（用于接收执行结果）
    /// </summary>
    public string? CallbackTopic { get; set; }
}

/// <summary>
/// 执行响应消息
/// </summary>
public class ExecutionResponseMessage : BaseMessage
{
    /// <summary>
    /// 执行ID
    /// </summary>
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 是否成功接受执行请求
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 执行器节点ID
    /// </summary>
    public string ExecutorNodeId { get; set; } = string.Empty;

    /// <summary>
    /// 预估执行时间（毫秒）
    /// </summary>
    public long EstimatedDurationMs { get; set; }

    /// <summary>
    /// 执行开始时间
    /// </summary>
    public DateTime StartedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 执行状态更新消息
/// </summary>
public class ExecutionStatusUpdateMessage : BaseMessage
{
    /// <summary>
    /// 执行ID
    /// </summary>
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 工作流ID
    /// </summary>
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 执行状态
    /// </summary>
    public WorkflowExecutionState State { get; set; }

    /// <summary>
    /// 当前执行的节点ID
    /// </summary>
    public string? CurrentNodeId { get; set; }

    /// <summary>
    /// 已完成的节点数
    /// </summary>
    public int CompletedNodes { get; set; }

    /// <summary>
    /// 总节点数
    /// </summary>
    public int TotalNodes { get; set; }

    /// <summary>
    /// 执行进度（0-100）
    /// </summary>
    public double Progress { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 执行器节点ID
    /// </summary>
    public string ExecutorNodeId { get; set; } = string.Empty;
}

/// <summary>
/// 执行结果消息
/// </summary>
public class ExecutionResultMessage : BaseMessage
{
    /// <summary>
    /// 执行ID
    /// </summary>
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 工作流ID
    /// </summary>
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 执行结果
    /// </summary>
    public WorkflowExecutionResult Result { get; set; } = new();

    /// <summary>
    /// 执行器节点ID
    /// </summary>
    public string ExecutorNodeId { get; set; } = string.Empty;

    /// <summary>
    /// 完成时间
    /// </summary>
    public DateTime CompletedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 执行控制消息
/// </summary>
public class ExecutionControlMessage : BaseMessage
{
    /// <summary>
    /// 执行ID
    /// </summary>
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 控制操作
    /// </summary>
    public ExecutionControlOperation Operation { get; set; }

    /// <summary>
    /// 操作参数
    /// </summary>
    public Dictionary<string, object> Parameters { get; set; } = new();

    /// <summary>
    /// 请求者ID
    /// </summary>
    public string RequesterId { get; set; } = string.Empty;
}

/// <summary>
/// 执行控制响应消息
/// </summary>
public class ExecutionControlResponseMessage : BaseMessage
{
    /// <summary>
    /// 执行ID
    /// </summary>
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 控制操作
    /// </summary>
    public ExecutionControlOperation Operation { get; set; }

    /// <summary>
    /// 操作是否成功
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 执行器节点ID
    /// </summary>
    public string ExecutorNodeId { get; set; } = string.Empty;

    /// <summary>
    /// 响应时间
    /// </summary>
    public DateTime ResponseTime { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 执行迁移消息
/// </summary>
public class ExecutionMigrationMessage : BaseMessage
{
    /// <summary>
    /// 执行ID
    /// </summary>
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 源节点ID
    /// </summary>
    public string SourceNodeId { get; set; } = string.Empty;

    /// <summary>
    /// 目标节点ID
    /// </summary>
    public string TargetNodeId { get; set; } = string.Empty;

    /// <summary>
    /// 迁移原因
    /// </summary>
    public string Reason { get; set; } = string.Empty;

    /// <summary>
    /// 执行上下文快照
    /// </summary>
    public ExecutionContextSnapshot ContextSnapshot { get; set; } = new();

    /// <summary>
    /// 迁移类型
    /// </summary>
    public MigrationType MigrationType { get; set; } = MigrationType.Manual;
}

/// <summary>
/// 执行迁移响应消息
/// </summary>
public class ExecutionMigrationResponseMessage : BaseMessage
{
    /// <summary>
    /// 执行ID
    /// </summary>
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 迁移是否成功
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 目标节点ID
    /// </summary>
    public string TargetNodeId { get; set; } = string.Empty;

    /// <summary>
    /// 迁移完成时间
    /// </summary>
    public DateTime CompletedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 容量查询消息
/// </summary>
public class CapacityQueryMessage : BaseMessage
{
    /// <summary>
    /// 查询类型
    /// </summary>
    public CapacityQueryType QueryType { get; set; } = CapacityQueryType.Current;

    /// <summary>
    /// 节点ID过滤器（可选）
    /// </summary>
    public List<string>? NodeIds { get; set; }

    /// <summary>
    /// 查询参数
    /// </summary>
    public Dictionary<string, object> Parameters { get; set; } = new();
}

/// <summary>
/// 容量响应消息
/// </summary>
public class CapacityResponseMessage : BaseMessage
{
    /// <summary>
    /// 节点容量信息
    /// </summary>
    public List<ExecutionCapacity> NodeCapacities { get; set; } = new();

    /// <summary>
    /// 集群总容量
    /// </summary>
    public ClusterCapacity ClusterCapacity { get; set; } = new();

    /// <summary>
    /// 响应时间
    /// </summary>
    public DateTime ResponseTime { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 执行选项
/// </summary>
public class ExecutionOptions
{
    /// <summary>
    /// 超时时间（毫秒）
    /// </summary>
    public long TimeoutMs { get; set; } = 300000; // 5分钟默认超时

    /// <summary>
    /// 最大重试次数
    /// </summary>
    public int MaxRetries { get; set; } = 3;

    /// <summary>
    /// 是否允许迁移
    /// </summary>
    public bool AllowMigration { get; set; } = true;

    /// <summary>
    /// 是否持久化结果
    /// </summary>
    public bool PersistResult { get; set; } = true;

    /// <summary>
    /// 执行标签
    /// </summary>
    public List<string> Tags { get; set; } = new();

    /// <summary>
    /// 执行元数据
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// 集群容量信息
/// </summary>
public class ClusterCapacity
{
    /// <summary>
    /// 总节点数
    /// </summary>
    public int TotalNodes { get; set; }

    /// <summary>
    /// 健康节点数
    /// </summary>
    public int HealthyNodes { get; set; }

    /// <summary>
    /// 总执行槽位
    /// </summary>
    public int TotalSlots { get; set; }

    /// <summary>
    /// 可用执行槽位
    /// </summary>
    public int AvailableSlots { get; set; }

    /// <summary>
    /// 当前执行数
    /// </summary>
    public int CurrentExecutions { get; set; }

    /// <summary>
    /// 平均负载评分
    /// </summary>
    public double AverageLoadScore { get; set; }

    /// <summary>
    /// 集群健康状态
    /// </summary>
    public ClusterHealthStatus HealthStatus { get; set; } = ClusterHealthStatus.Healthy;
}

/// <summary>
/// 执行控制操作
/// </summary>
[JsonConverter(typeof(JsonStringEnumConverter))]
public enum ExecutionControlOperation
{
    /// <summary>
    /// 取消执行
    /// </summary>
    Cancel,

    /// <summary>
    /// 暂停执行
    /// </summary>
    Pause,

    /// <summary>
    /// 恢复执行
    /// </summary>
    Resume,

    /// <summary>
    /// 重启执行
    /// </summary>
    Restart,

    /// <summary>
    /// 查询状态
    /// </summary>
    QueryStatus
}

/// <summary>
/// 迁移类型
/// </summary>
[JsonConverter(typeof(JsonStringEnumConverter))]
public enum MigrationType
{
    /// <summary>
    /// 手动迁移
    /// </summary>
    Manual,

    /// <summary>
    /// 自动迁移（负载均衡）
    /// </summary>
    LoadBalancing,

    /// <summary>
    /// 故障转移
    /// </summary>
    Failover,

    /// <summary>
    /// 节点维护
    /// </summary>
    Maintenance
}

/// <summary>
/// 容量查询类型
/// </summary>
[JsonConverter(typeof(JsonStringEnumConverter))]
public enum CapacityQueryType
{
    /// <summary>
    /// 当前容量
    /// </summary>
    Current,

    /// <summary>
    /// 历史容量
    /// </summary>
    Historical,

    /// <summary>
    /// 预测容量
    /// </summary>
    Predicted,

    /// <summary>
    /// 详细统计
    /// </summary>
    Detailed
}

/// <summary>
/// 集群健康状态
/// </summary>
[JsonConverter(typeof(JsonStringEnumConverter))]
public enum ClusterHealthStatus
{
    /// <summary>
    /// 健康
    /// </summary>
    Healthy,

    /// <summary>
    /// 警告
    /// </summary>
    Warning,

    /// <summary>
    /// 不健康
    /// </summary>
    Unhealthy,

    /// <summary>
    /// 严重
    /// </summary>
    Critical
}

/// <summary>
/// 节点状态更新消息
/// </summary>
public class NodeStateUpdateMessage : BaseMessage
{
    /// <summary>
    /// 执行ID
    /// </summary>
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 节点ID
    /// </summary>
    public string NodeId { get; set; } = string.Empty;

    /// <summary>
    /// 节点状态
    /// </summary>
    public NodeExecutionState State { get; set; }

    /// <summary>
    /// 节点执行结果
    /// </summary>
    public NodeExecutionResult? Result { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 执行器节点ID
    /// </summary>
    public string ExecutorNodeId { get; set; } = string.Empty;
}

/// <summary>
/// 执行上下文同步消息
/// </summary>
public class ExecutionContextSyncMessage : BaseMessage
{
    /// <summary>
    /// 执行ID
    /// </summary>
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 执行上下文快照
    /// </summary>
    public ExecutionContextSnapshot Context { get; set; } = new();

    /// <summary>
    /// 同步时间
    /// </summary>
    public DateTime SyncedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 执行器节点ID
    /// </summary>
    public string ExecutorNodeId { get; set; } = string.Empty;
}

/// <summary>
/// 执行状态查询消息
/// </summary>
public class ExecutionStateQueryMessage : BaseMessage
{
    /// <summary>
    /// 执行ID
    /// </summary>
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 请求者ID
    /// </summary>
    public string RequesterId { get; set; } = string.Empty;
}

/// <summary>
/// 执行状态响应消息
/// </summary>
public class ExecutionStateResponseMessage : BaseMessage
{
    /// <summary>
    /// 执行ID
    /// </summary>
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 执行状态信息
    /// </summary>
    public ExecutionStateInfo? StateInfo { get; set; }

    /// <summary>
    /// 响应时间
    /// </summary>
    public DateTime ResponseTime { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 响应者节点ID
    /// </summary>
    public string ResponderNodeId { get; set; } = string.Empty;
}

/// <summary>
/// 执行上下文查询消息
/// </summary>
public class ExecutionContextQueryMessage : BaseMessage
{
    /// <summary>
    /// 执行ID
    /// </summary>
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 请求者ID
    /// </summary>
    public string RequesterId { get; set; } = string.Empty;
}

/// <summary>
/// 执行上下文响应消息
/// </summary>
public class ExecutionContextResponseMessage : BaseMessage
{
    /// <summary>
    /// 执行ID
    /// </summary>
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 执行上下文快照
    /// </summary>
    public ExecutionContextSnapshot? Context { get; set; }

    /// <summary>
    /// 响应时间
    /// </summary>
    public DateTime ResponseTime { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 响应者节点ID
    /// </summary>
    public string ResponderNodeId { get; set; } = string.Empty;
}
