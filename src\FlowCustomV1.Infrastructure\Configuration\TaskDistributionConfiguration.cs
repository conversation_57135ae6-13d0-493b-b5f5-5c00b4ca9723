using System.ComponentModel.DataAnnotations;

namespace FlowCustomV1.Infrastructure.Configuration;

/// <summary>
/// 任务分发配置
/// </summary>
public class TaskDistributionConfiguration
{
    /// <summary>
    /// 配置节名称
    /// </summary>
    public const string SectionName = "TaskDistribution";

    /// <summary>
    /// 最大候选节点数量
    /// </summary>
    [Range(1, 100)]
    public int MaxCandidateNodes { get; set; } = 10;

    /// <summary>
    /// 最大并发分发数量
    /// </summary>
    [Range(1, 50)]
    public int MaxConcurrentDistributions { get; set; } = 10;

    /// <summary>
    /// 最小负载均衡评分（0-1）
    /// </summary>
    [Range(0.0, 1.0)]
    public double MinBalanceScore { get; set; } = 0.7;

    /// <summary>
    /// 最大重新平衡操作数量
    /// </summary>
    [Range(1, 20)]
    public int MaxRebalancingOperations { get; set; } = 5;

    /// <summary>
    /// 节点选择超时时间（毫秒）
    /// </summary>
    [Range(100, 30000)]
    public int NodeSelectionTimeoutMs { get; set; } = 5000;

    /// <summary>
    /// 任务分发超时时间（毫秒）
    /// </summary>
    [Range(1000, 60000)]
    public int TaskDistributionTimeoutMs { get; set; } = 10000;

    /// <summary>
    /// 负载均衡检查间隔（秒）
    /// </summary>
    [Range(10, 3600)]
    public int LoadBalanceCheckIntervalSeconds { get; set; } = 60;

    /// <summary>
    /// 自动重新平衡启用
    /// </summary>
    public bool AutoRebalancingEnabled { get; set; } = true;

    /// <summary>
    /// 重新平衡触发阈值（负载差异百分比）
    /// </summary>
    [Range(0.1, 1.0)]
    public double RebalancingThreshold { get; set; } = 0.3;

    /// <summary>
    /// 节点健康检查权重（0-1）
    /// </summary>
    [Range(0.0, 1.0)]
    public double HealthCheckWeight { get; set; } = 0.3;

    /// <summary>
    /// 负载权重（0-1）
    /// </summary>
    [Range(0.0, 1.0)]
    public double LoadWeight { get; set; } = 0.4;

    /// <summary>
    /// 性能权重（0-1）
    /// </summary>
    [Range(0.0, 1.0)]
    public double PerformanceWeight { get; set; } = 0.2;

    /// <summary>
    /// 地理位置权重（0-1）
    /// </summary>
    [Range(0.0, 1.0)]
    public double GeographyWeight { get; set; } = 0.1;

    /// <summary>
    /// 启用智能预测
    /// </summary>
    public bool EnableSmartPrediction { get; set; } = true;

    /// <summary>
    /// 预测历史窗口大小
    /// </summary>
    [Range(10, 1000)]
    public int PredictionHistoryWindowSize { get; set; } = 100;

    /// <summary>
    /// 统计数据保留天数
    /// </summary>
    [Range(1, 365)]
    public int StatisticsRetentionDays { get; set; } = 30;

    /// <summary>
    /// 启用详细日志
    /// </summary>
    public bool EnableDetailedLogging { get; set; } = false;

    /// <summary>
    /// 启用性能监控
    /// </summary>
    public bool EnablePerformanceMonitoring { get; set; } = true;

    /// <summary>
    /// 性能监控间隔（秒）
    /// </summary>
    [Range(5, 300)]
    public int PerformanceMonitoringIntervalSeconds { get; set; } = 30;

    /// <summary>
    /// 节点黑名单
    /// </summary>
    public List<string> NodeBlacklist { get; set; } = new();

    /// <summary>
    /// 节点白名单（为空表示允许所有节点）
    /// </summary>
    public List<string> NodeWhitelist { get; set; } = new();

    /// <summary>
    /// 默认任务优先级
    /// </summary>
    [Range(1, 10)]
    public int DefaultTaskPriority { get; set; } = 5;

    /// <summary>
    /// 高优先级任务阈值
    /// </summary>
    [Range(6, 10)]
    public int HighPriorityThreshold { get; set; } = 8;

    /// <summary>
    /// 低优先级任务阈值
    /// </summary>
    [Range(1, 5)]
    public int LowPriorityThreshold { get; set; } = 3;

    /// <summary>
    /// 任务重试配置
    /// </summary>
    public TaskRetryConfiguration RetryConfiguration { get; set; } = new();

    /// <summary>
    /// 负载均衡策略配置
    /// </summary>
    public LoadBalancingStrategyConfiguration StrategyConfiguration { get; set; } = new();

    /// <summary>
    /// 验证配置的有效性
    /// </summary>
    /// <returns>验证结果</returns>
    public ValidationResult Validate()
    {
        var errors = new List<string>();

        // 检查权重总和
        var totalWeight = HealthCheckWeight + LoadWeight + PerformanceWeight + GeographyWeight;
        if (Math.Abs(totalWeight - 1.0) > 0.01)
        {
            errors.Add($"权重总和必须等于1.0，当前为{totalWeight:F2}");
        }

        // 检查优先级阈值
        if (HighPriorityThreshold <= LowPriorityThreshold)
        {
            errors.Add("高优先级阈值必须大于低优先级阈值");
        }

        // 验证子配置
        var retryValidation = RetryConfiguration.Validate();
        if (!retryValidation.IsValid)
        {
            errors.AddRange(retryValidation.Errors.Select(e => $"重试配置: {e}"));
        }

        var strategyValidation = StrategyConfiguration.Validate();
        if (!strategyValidation.IsValid)
        {
            errors.AddRange(strategyValidation.Errors.Select(e => $"策略配置: {e}"));
        }

        return new ValidationResult
        {
            IsValid = errors.Count == 0,
            Errors = errors
        };
    }
}

/// <summary>
/// 任务重试配置
/// </summary>
public class TaskRetryConfiguration
{
    /// <summary>
    /// 默认最大重试次数
    /// </summary>
    [Range(0, 10)]
    public int DefaultMaxRetries { get; set; } = 3;

    /// <summary>
    /// 重试延迟基数（毫秒）
    /// </summary>
    [Range(100, 10000)]
    public int RetryDelayBaseMs { get; set; } = 1000;

    /// <summary>
    /// 重试延迟倍数
    /// </summary>
    [Range(1.0, 5.0)]
    public double RetryDelayMultiplier { get; set; } = 2.0;

    /// <summary>
    /// 最大重试延迟（毫秒）
    /// </summary>
    [Range(1000, 300000)]
    public int MaxRetryDelayMs { get; set; } = 30000;

    /// <summary>
    /// 启用指数退避
    /// </summary>
    public bool EnableExponentialBackoff { get; set; } = true;

    /// <summary>
    /// 启用随机抖动
    /// </summary>
    public bool EnableJitter { get; set; } = true;

    /// <summary>
    /// 抖动范围（0-1）
    /// </summary>
    [Range(0.0, 1.0)]
    public double JitterRange { get; set; } = 0.1;

    /// <summary>
    /// 验证配置的有效性
    /// </summary>
    /// <returns>验证结果</returns>
    public ValidationResult Validate()
    {
        var errors = new List<string>();

        if (MaxRetryDelayMs < RetryDelayBaseMs)
        {
            errors.Add("最大重试延迟必须大于等于重试延迟基数");
        }

        return new ValidationResult
        {
            IsValid = errors.Count == 0,
            Errors = errors
        };
    }
}

/// <summary>
/// 负载均衡策略配置
/// </summary>
public class LoadBalancingStrategyConfiguration
{
    /// <summary>
    /// 轮询策略权重
    /// </summary>
    [Range(0, 100)]
    public int RoundRobinWeight { get; set; } = 20;

    /// <summary>
    /// 最少负载策略权重
    /// </summary>
    [Range(0, 100)]
    public int LeastLoadWeight { get; set; } = 40;

    /// <summary>
    /// 最快响应策略权重
    /// </summary>
    [Range(0, 100)]
    public int FastestResponseWeight { get; set; } = 30;

    /// <summary>
    /// 随机策略权重
    /// </summary>
    [Range(0, 100)]
    public int RandomWeight { get; set; } = 10;

    /// <summary>
    /// 验证配置的有效性
    /// </summary>
    /// <returns>验证结果</returns>
    public ValidationResult Validate()
    {
        var errors = new List<string>();

        var totalWeight = RoundRobinWeight + LeastLoadWeight + FastestResponseWeight + RandomWeight;
        if (totalWeight == 0)
        {
            errors.Add("至少需要启用一种负载均衡策略");
        }

        return new ValidationResult
        {
            IsValid = errors.Count == 0,
            Errors = errors
        };
    }
}

/// <summary>
/// 验证结果
/// </summary>
public class ValidationResult
{
    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 错误列表
    /// </summary>
    public List<string> Errors { get; set; } = new();
}
