using System.ComponentModel.DataAnnotations;
using FlowCustomV1.Core.Interfaces;
using FlowCustomV1.Core.Services;
using Microsoft.Extensions.DependencyInjection;
using WorkflowValidationResult = FlowCustomV1.Core.Models.Workflow.ValidationResult;

namespace FlowCustomV1.Core.Tests.Services;

/// <summary>
/// ValidationService 单元测试
/// </summary>
public class ValidationServiceTests : IDisposable
{
    private readonly ServiceProvider _serviceProvider;
    private readonly IValidationService _validationService;

    public ValidationServiceTests()
    {
        var services = new ServiceCollection();

        // 注册真实的日志服务
        services.AddLogging();
        services.AddSingleton<ILoggingService, LoggingService>();

        // 注册验证服务
        services.AddSingleton<IValidationService, ValidationService>();

        _serviceProvider = services.BuildServiceProvider();
        _validationService = _serviceProvider.GetRequiredService<IValidationService>();
    }

    /// <summary>
    /// 测试对象验证 - 有效对象
    /// </summary>
    [Fact]
    public async Task ValidateAsync_ValidObject_ReturnsValidResult()
    {
        // Arrange
        var testObject = new TestValidationModel
        {
            Name = "Test Name",
            Email = "<EMAIL>",
            Age = 25
        };

        // Act
        var result = await _validationService.ValidateAsync(testObject);

        // Assert
        Assert.True(result.IsValid);
        Assert.Empty(result.Errors);
    }

    /// <summary>
    /// 测试对象验证 - 无效对象
    /// </summary>
    [Fact]
    public async Task ValidateAsync_InvalidObject_ReturnsInvalidResult()
    {
        // Arrange
        var testObject = new TestValidationModel
        {
            Name = "", // Required field is empty
            Email = "invalid-email", // Invalid email format
            Age = -1 // Invalid age
        };

        // Act
        var result = await _validationService.ValidateAsync(testObject);

        // Assert
        Assert.False(result.IsValid);
        Assert.NotEmpty(result.Errors);
    }

    /// <summary>
    /// 测试JSON验证 - 有效JSON
    /// </summary>
    [Fact]
    public async Task ValidateJsonAsync_ValidJson_ReturnsValidResult()
    {
        // Arrange
        var validJson = """{"name": "test", "value": 123}""";

        // Act
        var result = await _validationService.ValidateJsonAsync(validJson);

        // Assert
        Assert.True(result.IsValid);
        Assert.True(result.IsValidJson);
        Assert.NotNull(result.ParsedObject);
    }

    /// <summary>
    /// 测试JSON验证 - 无效JSON
    /// </summary>
    [Fact]
    public async Task ValidateJsonAsync_InvalidJson_ReturnsInvalidResult()
    {
        // Arrange
        var invalidJson = """{"name": "test", "value":}"""; // Missing value

        // Act
        var result = await _validationService.ValidateJsonAsync(invalidJson);

        // Assert
        Assert.False(result.IsValid);
        Assert.False(result.IsValidJson);
        Assert.NotNull(result.ParseError);
        Assert.NotEmpty(result.Errors);
    }

    /// <summary>
    /// 测试XML验证 - 有效XML
    /// </summary>
    [Fact]
    public async Task ValidateXmlAsync_ValidXml_ReturnsValidResult()
    {
        // Arrange
        var validXml = "<root><item>test</item></root>";

        // Act
        var result = await _validationService.ValidateXmlAsync(validXml);

        // Assert
        Assert.True(result.IsValid);
        Assert.True(result.IsValidXml);
        Assert.NotNull(result.ParsedDocument);
    }

    /// <summary>
    /// 测试XML验证 - 无效XML
    /// </summary>
    [Fact]
    public async Task ValidateXmlAsync_InvalidXml_ReturnsInvalidResult()
    {
        // Arrange
        var invalidXml = "<root><item>test</root>"; // Missing closing tag

        // Act
        var result = await _validationService.ValidateXmlAsync(invalidXml);

        // Assert
        Assert.False(result.IsValid);
        Assert.False(result.IsValidXml);
        Assert.NotNull(result.ParseError);
        Assert.NotEmpty(result.Errors);
    }

    /// <summary>
    /// 测试邮箱验证 - 有效邮箱
    /// </summary>
    [Fact]
    public async Task ValidateEmailAsync_ValidEmail_ReturnsValidResult()
    {
        // Arrange
        var validEmail = "<EMAIL>";

        // Act
        var result = await _validationService.ValidateEmailAsync(validEmail);

        // Assert
        Assert.True(result.IsValid);
        Assert.Empty(result.Errors);
    }

    /// <summary>
    /// 测试邮箱验证 - 无效邮箱
    /// </summary>
    [Fact]
    public async Task ValidateEmailAsync_InvalidEmail_ReturnsInvalidResult()
    {
        // Arrange
        var invalidEmail = "invalid-email";

        // Act
        var result = await _validationService.ValidateEmailAsync(invalidEmail);

        // Assert
        Assert.False(result.IsValid);
        Assert.NotEmpty(result.Errors);
    }

    /// <summary>
    /// 测试URL验证 - 有效URL
    /// </summary>
    [Fact]
    public async Task ValidateUrlAsync_ValidUrl_ReturnsValidResult()
    {
        // Arrange
        var validUrl = "https://www.example.com";

        // Act
        var result = await _validationService.ValidateUrlAsync(validUrl);

        // Assert
        Assert.True(result.IsValid);
        Assert.Empty(result.Errors);
    }

    /// <summary>
    /// 测试URL验证 - 无效URL
    /// </summary>
    [Fact]
    public async Task ValidateUrlAsync_InvalidUrl_ReturnsInvalidResult()
    {
        // Arrange
        var invalidUrl = "not-a-url";

        // Act
        var result = await _validationService.ValidateUrlAsync(invalidUrl);

        // Assert
        Assert.False(result.IsValid);
        Assert.NotEmpty(result.Errors);
    }

    /// <summary>
    /// 测试正则表达式验证 - 匹配
    /// </summary>
    [Fact]
    public async Task ValidateRegexAsync_MatchingPattern_ReturnsValidResult()
    {
        // Arrange
        var pattern = @"^\d{3}-\d{3}-\d{4}$";
        var input = "123-456-7890";

        // Act
        var result = await _validationService.ValidateRegexAsync(pattern, input);

        // Assert
        Assert.True(result.IsValid);
        Assert.Empty(result.Errors);
    }

    /// <summary>
    /// 测试正则表达式验证 - 不匹配
    /// </summary>
    [Fact]
    public async Task ValidateRegexAsync_NonMatchingPattern_ReturnsInvalidResult()
    {
        // Arrange
        var pattern = @"^\d{3}-\d{3}-\d{4}$";
        var input = "123-45-6789"; // Wrong format

        // Act
        var result = await _validationService.ValidateRegexAsync(pattern, input);

        // Assert
        Assert.False(result.IsValid);
        Assert.NotEmpty(result.Errors);
    }

    /// <summary>
    /// 测试自定义验证器注册
    /// </summary>
    [Fact]
    public void RegisterValidator_ValidValidator_RegistersSuccessfully()
    {
        // Arrange
        var testValidator = new TestModelValidator();

        // Act
        _validationService.RegisterValidator(testValidator);
        var retrievedValidator = _validationService.GetValidator<TestValidationModel>();

        // Assert
        Assert.NotNull(retrievedValidator);
        Assert.Equal("TestValidator", retrievedValidator.ValidatorName);
        Assert.Equal("1.0.0", retrievedValidator.Version);
    }

    /// <summary>
    /// 测试自定义验证器移除
    /// </summary>
    [Fact]
    public void RemoveValidator_ExistingValidator_RemovesSuccessfully()
    {
        // Arrange
        var testValidator = new TestModelValidator();
        _validationService.RegisterValidator(testValidator);

        // Act
        var removed = _validationService.RemoveValidator<TestValidationModel>();
        var retrievedValidator = _validationService.GetValidator<TestValidationModel>();

        // Assert
        Assert.True(removed);
        Assert.Null(retrievedValidator);
    }

    public void Dispose()
    {
        _serviceProvider?.Dispose();
    }
}

/// <summary>
/// 测试验证模型
/// </summary>
public class TestValidationModel
{
    [Required]
    [StringLength(100, MinimumLength = 1)]
    public string Name { get; set; } = string.Empty;

    [Required]
    [EmailAddress]
    public string Email { get; set; } = string.Empty;

    [Range(0, 150)]
    public int Age { get; set; }
}

/// <summary>
/// 测试模型验证器
/// </summary>
public class TestModelValidator : IValidator<TestValidationModel>
{
    public string ValidatorName => "TestValidator";
    public string Version => "1.0.0";

    public FlowCustomV1.Core.Models.Workflow.ValidationResult Validate(TestValidationModel model)
    {
        var result = new FlowCustomV1.Core.Models.Workflow.ValidationResult();

        if (string.IsNullOrEmpty(model.Name))
        {
            result.Errors.Add("Name is required");
            result.IsValid = false;
        }

        if (model.Age < 0)
        {
            result.Errors.Add("Age must be non-negative");
            result.IsValid = false;
        }

        return result;
    }

    public Task<FlowCustomV1.Core.Models.Workflow.ValidationResult> ValidateAsync(TestValidationModel model, CancellationToken cancellationToken = default)
    {
        return Task.FromResult(Validate(model));
    }
}
