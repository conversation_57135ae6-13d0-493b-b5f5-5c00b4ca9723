{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "FlowCustomV1": "Debug", "System.Net.Http.HttpClient": "Warning"}, "Console": {"IncludeScopes": true, "TimestampFormat": "yyyy-MM-dd HH:mm:ss.fff ", "LogToStandardErrorThreshold": "Error"}}, "AllowedHosts": "*", "Nats": {"Servers": ["nats://nats:4222"], "ConnectionName": "FlowCustomV1-Test-Node", "Username": "<PERSON><PERSON><PERSON>", "Password": "flowcustom_password", "ConnectionTimeoutSeconds": 30, "ReconnectIntervalSeconds": 2, "MaxReconnectAttempts": 10, "EnableAutoReconnect": true, "PingIntervalSeconds": 30, "MaxPingsOutstanding": 3, "EnableVerboseLogging": false, "JetStream": {"Enabled": true, "Domain": "flowcustom-test", "ApiPrefix": "$JS.API", "DefaultStream": {"Name": "FLOWCUSTOM", "Subjects": ["flowcustom.>"], "Storage": "file", "MaxMessages": 100000, "MaxBytes": 104857600, "MaxAgeSeconds": 3600, "Replicas": 1}}, "Streams": [{"Name": "FLOWCUSTOM", "Subjects": ["flowcustom.>"], "Storage": "file", "MaxMessages": 100000, "MaxBytes": 104857600, "MaxAgeSeconds": 3600, "Replicas": 1}]}, "NodeDiscovery": {"ClusterName": "FlowCustomV1-Test", "HeartbeatIntervalSeconds": 10, "NodeTimeoutSeconds": 30, "NodeCleanupIntervalSeconds": 60, "DiscoveryTimeoutSeconds": 5, "EnableAutoRegistration": true, "EnableHeartbeat": true, "EnableNodeCleanup": true, "MaxRetryAttempts": 3, "RetryIntervalSeconds": 2, "NodeRole": "TestNode", "NodeTags": ["test", "docker"], "CapabilityTags": ["workflow-execution", "task-processing"], "EnableVerboseLogging": true}, "TaskDistribution": {"MaxCandidateNodes": 10, "MaxConcurrentDistributions": 5, "MinBalanceScore": 0.7, "MaxRebalancingOperations": 3, "NodeSelectionTimeoutMs": 3000, "TaskDistributionTimeoutMs": 10000, "AutoRebalancingEnabled": true, "RebalancingThreshold": 0.3, "RebalancingIntervalSeconds": 60, "EnablePerformanceMonitoring": true, "EnableDetailedLogging": true, "StatisticsRetentionDays": 1, "PredictionHistoryWindowSize": 100, "HealthCheckWeight": 0.3, "LoadWeight": 0.3, "PerformanceWeight": 0.25, "GeographyWeight": 0.15, "DefaultStrategy": "SmartLoad", "EnableFailover": true, "FailoverTimeoutMs": 5000, "MaxRetryAttempts": 3, "RetryDelayMs": 1000}, "TaskTracking": {"CleanupIntervalMinutes": 10, "StatisticsUpdateIntervalSeconds": 10, "MaxRecentCompletedTasks": 100, "AutoCleanupOnCompletion": true, "TaskStateRetentionDays": 1, "EnableDetailedLogging": true, "EnablePerformanceMonitoring": true, "MaxConcurrentTrackedTasks": 1000, "TimeoutCheckIntervalSeconds": 15, "EnableTimeoutCheck": true, "DefaultTaskTimeoutMs": 60000, "EnableProgressTracking": true, "ProgressUpdateMinIntervalMs": 500, "EnableResourceMonitoring": true, "ResourceMonitoringIntervalSeconds": 10, "EnableEventPublishing": true, "EventTopicPrefix": "task.tracking", "BatchOperationSize": 50, "EnableDependencyTracking": true, "MaxDependencyDepth": 5, "EnableRetryTracking": true, "MaxRetryHistoryCount": 5, "EnableExecutionLogging": true, "MaxExecutionLogCount": 100}, "WorkflowDesigner": {"EnableCollaboration": false, "MaxConcurrentDesigners": 5, "AutoSaveIntervalSeconds": 30, "VersionHistoryRetentionDays": 7, "MaxVersionsPerWorkflow": 10, "EnableRealTimeSync": false, "ConflictResolutionStrategy": "LastWriterWins", "EnableDesignValidation": true, "ValidationTimeoutMs": 5000, "EnableDesignTemplates": true, "TemplateStoragePath": "/app/data/templates"}, "WorkflowValidator": {"EnableDistributedValidation": false, "MaxConcurrentValidations": 3, "ValidationTimeoutMs": 15000, "EnablePerformanceAnalysis": true, "EnableSecurityValidation": true, "EnableComplianceCheck": false, "MaxValidationDepth": 10, "EnableCacheValidationResults": true, "ValidationCacheExpirationMinutes": 30, "EnableValidationMetrics": true, "MetricsRetentionDays": 1}, "TaskExecution": {"MaxConcurrentTasks": 5, "TaskTimeoutMs": 60000, "EnableResourceMonitoring": true, "ResourceMonitoringIntervalMs": 2000, "EnableTaskIsolation": false, "TaskWorkingDirectory": "/app/data/tasks", "EnableTaskLogging": true, "TaskLogRetentionDays": 1, "MaxTaskLogSizeMB": 10, "EnableTaskMetrics": true, "MetricsPublishIntervalMs": 5000}, "HealthChecks": {"EnableHealthChecks": true, "HealthCheckIntervalSeconds": 15, "HealthCheckTimeoutMs": 5000, "EnableDetailedHealthInfo": true, "EnableDependencyHealthChecks": true, "HealthCheckEndpoint": "/health", "ReadinessCheckEndpoint": "/ready", "LivenessCheckEndpoint": "/live"}, "Monitoring": {"EnableMetrics": true, "MetricsEndpoint": "/metrics", "EnableTracing": false, "TracingSamplingRate": 0.1, "EnableLogging": true, "LogLevel": "Information", "EnablePerformanceCounters": true, "MetricsRetentionDays": 1}, "Security": {"EnableAuthentication": false, "EnableAuthorization": false, "EnableHttps": false, "EnableCors": true, "AllowedOrigins": ["*"], "EnableRateLimiting": false, "RateLimitRequests": 1000, "RateLimitWindowMinutes": 1}, "Database": {"Provider": "MySQL", "ConnectionString": "Server=mysql;Database=flowcustom_test;Uid=flowcustom;Pwd=TestPassword123!;", "CommandTimeout": 30, "EnableRetryOnFailure": true, "MaxRetryCount": 3, "MaxRetryDelay": "00:00:10", "EnableSensitiveDataLogging": false, "EnableDetailedErrors": true, "MigrationsAssembly": "FlowCustomV1.Infrastructure"}, "Testing": {"Environment": "<PERSON>er", "EnableTestEndpoints": true, "TestDataPath": "/app/data/test", "EnableMockServices": false, "TestTimeoutMs": 30000, "EnableTestLogging": true, "TestLogLevel": "Debug", "EnablePerformanceTesting": true, "EnableLoadTesting": false, "MaxTestConcurrency": 5}}