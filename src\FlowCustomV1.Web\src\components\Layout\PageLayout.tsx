import React from 'react';
import { Space } from 'antd';

interface PageLayoutProps {
  /** 页面标题 */
  title: string;
  /** 页面描述 */
  description?: string;
  /** 页面标题图标 */
  icon?: React.ReactNode;
  /** 页面右侧操作按钮 */
  actions?: React.ReactNode;
  /** 页面内容 */
  children: React.ReactNode;
  /** 是否显示页面头部分隔线 */
  showHeaderDivider?: boolean;
  /** 自定义页面容器样式 */
  containerClassName?: string;
  /** 自定义页面头部样式 */
  headerClassName?: string;
}

/**
 * 标准页面布局组件
 * 
 * 提供统一的页面布局结构：
 * - 页面容器 (page-container)
 * - 页面头部 (page-header) 
 * - 页面标题和描述
 * - 页面操作按钮
 * - 页面内容区域
 * 
 * @example
 * ```tsx
 * <PageLayout
 *   title="工作流管理"
 *   description="管理和维护系统中的所有工作流"
 *   icon={<WorkflowOutlined />}
 *   actions={
 *     <Space>
 *       <Button>导入</Button>
 *       <Button type="primary">新建工作流</Button>
 *     </Space>
 *   }
 * >
 *   {/* 页面具体内容 *\/}
 * </PageLayout>
 * ```
 */
const PageLayout: React.FC<PageLayoutProps> = ({
  title,
  description,
  icon,
  actions,
  children,
  showHeaderDivider = true,
  containerClassName = '',
  headerClassName = '',
}) => {
  return (
    <div className={`page-container ${containerClassName}`}>
      {/* 页面头部 */}
      <div className={`${showHeaderDivider ? 'page-header' : 'mb-6'} ${headerClassName}`}>
        <div className="flex justify-between items-center">
          <div>
            <h1 className="page-title">
              {icon && <span className="mr-2">{icon}</span>}
              {title}
            </h1>
            {description && (
              <p className="page-description">{description}</p>
            )}
          </div>
          {actions && (
            <div className="flex-shrink-0">
              {React.isValidElement(actions) ? actions : <Space>{actions}</Space>}
            </div>
          )}
        </div>
      </div>

      {/* 页面内容 */}
      <div className="page-content">
        {children}
      </div>
    </div>
  );
};

export default PageLayout;
