/**
 * 基础布局样式 - 纯CSS版本
 *
 * 配合 layout-config.css 使用
 * 所有尺寸都通过CSS变量控制，实现一处配置全局生效
 */

/* ========== 基础重置 ========== */
html, body, #root {
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden; /* 禁止页面级别滚动 */
}

/* ========== ProLayout 根容器 ========== */
.ant-pro-layout {
  height: 100vh;
  overflow: hidden;
  position: relative;
}

/* ProLayout 内容区域 - 固定高度 + 滚动控制 */
.ant-pro-layout .ant-layout-content {
  height: var(--layout-content-height);
  max-height: var(--layout-content-height);
  overflow-y: auto;
  overflow-x: hidden;
  padding: var(--layout-content-padding);
  background: #f0f2f5;
  
  /* 智能滚动条 */
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
  transition: scrollbar-color 0.3s ease;
}

/* 悬停时显示滚动条 */
.ant-pro-layout .ant-layout-content:hover {
  scrollbar-color: var(--layout-scrollbar-color) #f1f1f1;
}

/* Webkit 滚动条样式 */
.ant-pro-layout .ant-layout-content::-webkit-scrollbar {
  width: var(--layout-scrollbar-width);
  background: transparent;
}

.ant-pro-layout .ant-layout-content::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

.ant-pro-layout .ant-layout-content::-webkit-scrollbar-thumb {
  background: transparent;
  border-radius: 3px;
  transition: background 0.3s ease;
}

.ant-pro-layout .ant-layout-content:hover::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.ant-pro-layout .ant-layout-content:hover::-webkit-scrollbar-thumb {
  background: var(--layout-scrollbar-color);
}

.ant-pro-layout .ant-layout-content:hover::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* ========== 页面容器 ========== */
.page-container {
  display: flex;
  flex-direction: column;
  min-height: 0;
  background: #fff;
  border-radius: 8px;
  padding: var(--layout-container-padding);
  margin-bottom: var(--layout-element-spacing);
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
}

/* ========== 页面头部 ========== */
.page-header {
  margin-bottom: var(--layout-page-header-margin);
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.page-title {
  font-size: 20px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
  margin: 0;
  display: flex;
  align-items: center;
}

.page-description {
  color: rgba(0, 0, 0, 0.45);
  margin-top: 8px;
  margin-bottom: 0;
  font-size: 14px;
  line-height: 1.5;
}

/* ========== 页面内容 ========== */
.page-content {
  flex: 1;
  min-height: 0;
}

/* ========== 工具栏 ========== */
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--layout-toolbar-margin);
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* ========== 表格容器 ========== */
.workflow-table-container {
  display: flex;
  flex-direction: column;
  min-height: 0;
  height: var(--layout-table-height);
  border: 1px solid #f0f0f0;
  border-radius: 6px;
}

/* ProTable 分页栏样式 */
.workflow-table-container .ant-pro-table .ant-table-pagination {
  height: var(--layout-table-pagination-height) !important;
  min-height: var(--layout-table-pagination-height) !important;
  margin: 8px 0 !important;
  padding: 0 16px !important;
  flex-shrink: 0; /* 防止分页栏被压缩 */
  background: #fff !important;
  border-top: 1px solid #f0f0f0 !important;
  position: relative !important;
  z-index: 10 !important;
  display: flex !important;
  align-items: center !important;
}

/* 确保表格内容区域正确滚动 */
.workflow-table-container .ant-pro-table .ant-table-container {
  flex: 1;
  overflow: auto;
}

/* 强制显示分页栏 */
.ant-pro-table .ant-table-pagination {
  display: flex !important;
  visibility: visible !important;
}

/* ========== 卡片布局 ========== */
.layout-card-grid {
  margin-bottom: var(--layout-card-margin);
}

.layout-card-statistic {
  margin-bottom: var(--layout-card-margin);
}

/* ========== 响应式工具类 ========== */
@media (max-width: 768px) {
  .toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .toolbar-left,
  .toolbar-right {
    justify-content: center;
  }
}

/* ========== 调试样式已移除 ========== */
/* 如需调试，可通过浏览器开发者工具临时添加边框样式 */
