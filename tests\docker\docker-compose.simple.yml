version: '3.8'

# FlowCustomV1 v0.0.1.7 Simple Test Environment
# Basic infrastructure and application nodes for testing

services:
  # ===========================================
  # Infrastructure - NATS Server
  # ===========================================
  nats:
    image: nats:2.11.8-alpine
    container_name: flowcustom-test-nats
    ports:
      - "4222:4222"
      - "8222:8222"
    command: ["-js", "-m", "8222"]
    networks:
      - flowcustom-test
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8222/healthz"]
      interval: 10s
      timeout: 5s
      retries: 3

  # ===========================================
  # Database - MySQL Database
  # ===========================================
  mysql:
    image: mysql:8.0
    container_name: flowcustom-test-mysql
    environment:
      MYSQL_ROOT_PASSWORD: TestPassword123!
      MYSQL_DATABASE: flowcustom_test
      MYSQL_USER: flowcustom
      MYSQL_PASSWORD: TestPassword123!
    ports:
      - "3306:3306"
    volumes:
      - mysql-test-data:/var/lib/mysql
    networks:
      - flowcustom-test
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-pTestPassword123!"]
      interval: 10s
      timeout: 5s
      retries: 5

  # ===========================================
  # Application - Master Node (Beijing)
  # ===========================================
  master-node:
    build:
      context: ../../
      dockerfile: tests/docker/Dockerfile.test-node
      args:
        NODE_ROLE: Master
        NODE_ID: master-beijing
        REGION: Beijing
    container_name: flowcustom-test-master
    environment:
      - NODE_ROLE=Master
      - NODE_ID=master-beijing
      - NODE_NAME=MasterBeijing
      - REGION=Beijing
      - DATACENTER=Beijing-DC1
      - ASPNETCORE_ENVIRONMENT=Test
      - ASPNETCORE_URLS=http://+:5000
      # 统一配置系统环境变量
      - Nats__Servers__0=nats://nats:4222
      - Nats__ConnectionName=FlowCustomV1-Master-Beijing
      - Database__ConnectionString=Server=mysql;Database=flowcustom_test;Uid=flowcustom;Pwd=TestPassword123!;
      - NodeDiscovery__NodeId=master-beijing
      - NodeDiscovery__NodeName=MasterBeijing
      - NodeDiscovery__NodeRole=Master
      - NodeDiscovery__Region=Beijing
      - NodeDiscovery__DataCenter=Beijing-DC1
    ports:
      - "5001:5000"
    depends_on:
      nats:
        condition: service_healthy
      mysql:
        condition: service_healthy
    networks:
      - flowcustom-test
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 15s
      timeout: 10s
      retries: 3

  # ===========================================
  # Application - Worker Node (Beijing)
  # ===========================================
  worker-node:
    build:
      context: ../../
      dockerfile: tests/docker/Dockerfile.test-node
      args:
        NODE_ROLE: Worker
        NODE_ID: worker-beijing-1
        REGION: Beijing
    container_name: flowcustom-test-worker
    environment:
      - NODE_ROLE=Worker
      - NODE_ID=worker-beijing-1
      - NODE_NAME=WorkerBeijing1
      - REGION=Beijing
      - DATACENTER=Beijing-DC1
      - ASPNETCORE_ENVIRONMENT=Test
      - ASPNETCORE_URLS=http://+:5000
      - WORKER_MAX_TASKS=20
      - WORKER_CPU_CORES=4
      - WORKER_MEMORY_MB=8192
      # 统一配置系统环境变量
      - Nats__Servers__0=nats://nats:4222
      - Nats__ConnectionName=FlowCustomV1-Worker-Beijing-1
      - Database__ConnectionString=Server=mysql;Database=flowcustom_test;Uid=flowcustom;Pwd=TestPassword123!;
      - NodeDiscovery__NodeId=worker-beijing-1
      - NodeDiscovery__NodeName=WorkerBeijing1
      - NodeDiscovery__NodeRole=Worker
      - NodeDiscovery__Region=Beijing
      - NodeDiscovery__DataCenter=Beijing-DC1
    ports:
      - "5011:5000"
    depends_on:
      - master-node
    networks:
      - flowcustom-test
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 15s
      timeout: 10s
      retries: 3

# ===========================================
# Networks
# ===========================================
networks:
  flowcustom-test:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# ===========================================
# Volumes
# ===========================================
volumes:
  mysql-test-data:
    driver: local
