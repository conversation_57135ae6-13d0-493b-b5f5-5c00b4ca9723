#!/usr/bin/env python3
"""
FlowCustomV1 API接口测试脚本
测试所有API端点的功能和响应
"""

import requests
import json
import time
import sys
from datetime import datetime
from typing import Dict, Any, Optional

class FlowCustomAPITester:
    def __init__(self, base_url: str = "http://localhost:5257"):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })
        self.test_results = []
        
    def log_test(self, test_name: str, success: bool, details: str = ""):
        """记录测试结果"""
        result = {
            'test_name': test_name,
            'success': success,
            'details': details,
            'timestamp': datetime.now().isoformat()
        }
        self.test_results.append(result)
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}: {details}")
        
    def test_api_health(self) -> bool:
        """测试API服务是否正常运行"""
        try:
            response = self.session.get(f"{self.base_url}/api/workflows")
            if response.status_code == 200:
                self.log_test("API Health Check", True, f"Status: {response.status_code}")
                return True
            else:
                self.log_test("API Health Check", False, f"Status: {response.status_code}")
                return False
        except Exception as e:
            self.log_test("API Health Check", False, f"Connection error: {str(e)}")
            return False
    
    def test_get_all_workflows(self) -> Optional[list]:
        """测试获取所有工作流"""
        try:
            response = self.session.get(f"{self.base_url}/api/workflows")
            if response.status_code == 200:
                workflows = response.json()
                self.log_test("GET /api/workflows", True, f"返回 {len(workflows)} 个工作流")
                return workflows
            else:
                self.log_test("GET /api/workflows", False, f"Status: {response.status_code}")
                return None
        except Exception as e:
            self.log_test("GET /api/workflows", False, f"Error: {str(e)}")
            return None
    
    def test_create_workflow(self) -> Optional[str]:
        """测试创建工作流"""
        # 生成唯一的工作流ID
        workflow_id = f"test-workflow-{int(time.time())}"

        test_workflow = {
            "workflowId": workflow_id,  # 必需字段
            "name": "API测试工作流",
            "description": "通过Python脚本创建的测试工作流",
            "version": "1.0.0",  # 使用标准版本格式
            "author": "API Tester",
            "nodes": [
                {
                    "nodeId": "start",
                    "nodeType": "Start",
                    "name": "开始节点",
                    "position": {"x": 100, "y": 100}
                },
                {
                    "nodeId": "task1",
                    "nodeType": "Task",
                    "name": "任务节点",
                    "position": {"x": 300, "y": 100},
                    "configuration": {
                        "parameters": {  # 使用parameters而不是settings
                            "TaskType": "data_processing",
                            "ProcessingTime": "1000"
                        }
                    }
                },
                {
                    "nodeId": "end",
                    "nodeType": "End",
                    "name": "结束节点",
                    "position": {"x": 500, "y": 100}
                }
            ],
            "connections": [
                {
                    "connectionId": "conn1",  # 必需字段
                    "sourceNodeId": "start",
                    "targetNodeId": "task1"
                },
                {
                    "connectionId": "conn2",  # 必需字段
                    "sourceNodeId": "task1",
                    "targetNodeId": "end"
                }
            ]
        }
        
        try:
            response = self.session.post(f"{self.base_url}/api/workflows", json=test_workflow)
            if response.status_code == 201:
                created_workflow = response.json()
                workflow_id = created_workflow.get('workflowId')
                self.log_test("POST /api/workflows", True, f"创建成功，ID: {workflow_id}")
                return workflow_id
            else:
                error_detail = response.text[:200] if response.text else "No error details"
                self.log_test("POST /api/workflows", False, f"Status: {response.status_code}, Error: {error_detail}")
                return None
        except Exception as e:
            self.log_test("POST /api/workflows", False, f"Error: {str(e)}")
            return None
    
    def test_get_workflow_by_id(self, workflow_id: str) -> bool:
        """测试根据ID获取工作流"""
        try:
            response = self.session.get(f"{self.base_url}/api/workflows/{workflow_id}")
            if response.status_code == 200:
                workflow = response.json()
                self.log_test("GET /api/workflows/{id}", True, f"获取工作流: {workflow.get('name', 'Unknown')}")
                return True
            elif response.status_code == 404:
                self.log_test("GET /api/workflows/{id}", False, "工作流不存在")
                return False
            else:
                self.log_test("GET /api/workflows/{id}", False, f"Status: {response.status_code}")
                return False
        except Exception as e:
            self.log_test("GET /api/workflows/{id}", False, f"Error: {str(e)}")
            return False
    
    def test_validate_workflow(self, workflow_id: str) -> bool:
        """测试工作流验证"""
        # 首先获取工作流定义
        try:
            get_response = self.session.get(f"{self.base_url}/api/workflows/{workflow_id}")
            if get_response.status_code != 200:
                self.log_test("POST /api/workflows/validate", False, "无法获取工作流定义")
                return False
                
            workflow = get_response.json()
            
            # 验证工作流
            validate_response = self.session.post(f"{self.base_url}/api/workflows/validate", json=workflow)
            if validate_response.status_code == 200:
                self.log_test("POST /api/workflows/validate", True, "工作流验证通过")
                return True
            else:
                error_detail = validate_response.text[:200] if validate_response.text else "No error details"
                self.log_test("POST /api/workflows/validate", False, f"验证失败: {error_detail}")
                return False
        except Exception as e:
            self.log_test("POST /api/workflows/validate", False, f"Error: {str(e)}")
            return False
    
    def test_execute_workflow(self, workflow_id: str) -> Optional[str]:
        """测试执行工作流"""
        try:
            response = self.session.post(f"{self.base_url}/api/executions/start/{workflow_id}")
            if response.status_code == 200:
                execution_result = response.json()
                execution_id = execution_result.get('executionId', 'Unknown')
                success = execution_result.get('isSuccess', False)
                status = "成功" if success else "失败"
                self.log_test("POST /api/executions/start/{workflowId}", True, f"执行{status}，ID: {execution_id}")
                return execution_id
            else:
                error_detail = response.text[:200] if response.text else "No error details"
                self.log_test("POST /api/executions/start/{workflowId}", False, f"Status: {response.status_code}, Error: {error_detail}")
                return None
        except Exception as e:
            self.log_test("POST /api/executions/start/{workflowId}", False, f"Error: {str(e)}")
            return None
    
    def test_get_execution_result(self, execution_id: str) -> bool:
        """测试获取执行结果"""
        try:
            response = self.session.get(f"{self.base_url}/api/executions/{execution_id}")
            if response.status_code == 200:
                execution = response.json()
                status = execution.get('status', 'Unknown')
                self.log_test("GET /api/executions/{executionId}", True, f"执行状态: {status}")
                return True
            elif response.status_code == 404:
                self.log_test("GET /api/executions/{executionId}", False, "执行记录不存在")
                return False
            else:
                self.log_test("GET /api/executions/{executionId}", False, f"Status: {response.status_code}")
                return False
        except Exception as e:
            self.log_test("GET /api/executions/{executionId}", False, f"Error: {str(e)}")
            return False
    
    def test_get_workflow_executions(self, workflow_id: str) -> bool:
        """测试获取工作流执行历史"""
        try:
            response = self.session.get(f"{self.base_url}/api/executions/workflow/{workflow_id}")
            if response.status_code == 200:
                executions = response.json()
                self.log_test("GET /api/executions/workflow/{workflowId}", True, f"返回 {len(executions)} 条执行记录")
                return True
            else:
                self.log_test("GET /api/executions/workflow/{workflowId}", False, f"Status: {response.status_code}")
                return False
        except Exception as e:
            self.log_test("GET /api/executions/workflow/{workflowId}", False, f"Error: {str(e)}")
            return False
    
    def test_update_workflow(self, workflow_id: str) -> bool:
        """测试更新工作流"""
        # 首先获取现有工作流
        try:
            get_response = self.session.get(f"{self.base_url}/api/workflows/{workflow_id}")
            if get_response.status_code != 200:
                self.log_test("PUT /api/workflows/{id}", False, "无法获取工作流定义")
                return False
                
            workflow = get_response.json()
            workflow['description'] = "更新后的工作流描述 - " + datetime.now().isoformat()
            
            # 更新工作流
            update_response = self.session.put(f"{self.base_url}/api/workflows/{workflow_id}", json=workflow)
            if update_response.status_code == 204:
                self.log_test("PUT /api/workflows/{id}", True, "工作流更新成功")
                return True
            else:
                error_detail = update_response.text[:200] if update_response.text else "No error details"
                self.log_test("PUT /api/workflows/{id}", False, f"Status: {update_response.status_code}, Error: {error_detail}")
                return False
        except Exception as e:
            self.log_test("PUT /api/workflows/{id}", False, f"Error: {str(e)}")
            return False
    
    def test_delete_workflow(self, workflow_id: str) -> bool:
        """测试删除工作流"""
        try:
            response = self.session.delete(f"{self.base_url}/api/workflows/{workflow_id}")
            if response.status_code == 204:
                self.log_test("DELETE /api/workflows/{id}", True, "工作流删除成功")
                return True
            elif response.status_code == 404:
                self.log_test("DELETE /api/workflows/{id}", False, "工作流不存在")
                return False
            else:
                self.log_test("DELETE /api/workflows/{id}", False, f"Status: {response.status_code}")
                return False
        except Exception as e:
            self.log_test("DELETE /api/workflows/{id}", False, f"Error: {str(e)}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始FlowCustomV1 API接口测试")
        print("=" * 60)
        
        # 1. 健康检查
        if not self.test_api_health():
            print("❌ API服务不可用，终止测试")
            return
        
        print("\n📋 开始工作流管理API测试...")
        
        # 2. 获取所有工作流
        workflows = self.test_get_all_workflows()
        
        # 3. 创建新工作流
        workflow_id = self.test_create_workflow()
        if not workflow_id:
            print("❌ 无法创建测试工作流，跳过后续测试")
            return
        
        # 4. 根据ID获取工作流
        self.test_get_workflow_by_id(workflow_id)
        
        # 5. 验证工作流
        self.test_validate_workflow(workflow_id)
        
        # 6. 更新工作流
        self.test_update_workflow(workflow_id)
        
        print("\n🔄 开始工作流执行API测试...")
        
        # 7. 执行工作流
        execution_id = self.test_execute_workflow(workflow_id)
        
        # 8. 获取执行结果
        if execution_id:
            time.sleep(2)  # 等待执行完成
            self.test_get_execution_result(execution_id)
        
        # 9. 获取工作流执行历史
        self.test_get_workflow_executions(workflow_id)
        
        print("\n🧹 清理测试数据...")
        
        # 10. 删除测试工作流
        self.test_delete_workflow(workflow_id)
        
        # 输出测试总结
        self.print_test_summary()
    
    def print_test_summary(self):
        """打印测试总结"""
        print("\n" + "=" * 60)
        print("📊 测试总结")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests
        
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests} ✅")
        print(f"失败: {failed_tests} ❌")
        print(f"成功率: {(passed_tests/total_tests*100):.1f}%")
        
        if failed_tests > 0:
            print("\n❌ 失败的测试:")
            for result in self.test_results:
                if not result['success']:
                    print(f"  - {result['test_name']}: {result['details']}")
        
        print("\n🎉 API接口测试完成!")

if __name__ == "__main__":
    # 检查命令行参数
    base_url = sys.argv[1] if len(sys.argv) > 1 else "http://localhost:5257"
    
    print(f"🔗 测试目标: {base_url}")
    
    # 创建测试器并运行测试
    tester = FlowCustomAPITester(base_url)
    tester.run_all_tests()
