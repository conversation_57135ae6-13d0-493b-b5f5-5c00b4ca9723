using FlowCustomV1.Core.Models.Workflow;
using FlowCustomV1.Core.Models.Designer;

namespace FlowCustomV1.Core.Interfaces.Services;

/// <summary>
/// 工作流设计服务接口
/// 提供分布式工作流设计功能
/// </summary>
public interface IWorkflowDesignerService
{
    #region 工作流设计
    
    /// <summary>
    /// 创建新工作流
    /// </summary>
    /// <param name="template">工作流模板</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>工作流定义</returns>
    Task<WorkflowDefinition> CreateWorkflowAsync(WorkflowTemplate template, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 更新工作流定义
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="workflow">工作流定义</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>更新结果</returns>
    Task<bool> UpdateWorkflowAsync(string workflowId, WorkflowDefinition workflow, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取工作流定义
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>工作流定义</returns>
    Task<WorkflowDefinition?> GetWorkflowAsync(string workflowId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 删除工作流
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>删除结果</returns>
    Task<bool> DeleteWorkflowAsync(string workflowId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取工作流列表
    /// </summary>
    /// <param name="query">查询条件</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>工作流列表</returns>
    Task<IReadOnlyList<WorkflowDefinition>> GetWorkflowsAsync(WorkflowQuery? query = null, CancellationToken cancellationToken = default);
    
    #endregion
    
    #region 模板管理
    
    /// <summary>
    /// 获取工作流模板列表
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>模板列表</returns>
    Task<IReadOnlyList<WorkflowTemplate>> GetTemplatesAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 创建工作流模板
    /// </summary>
    /// <param name="template">模板定义</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>创建结果</returns>
    Task<bool> CreateTemplateAsync(WorkflowTemplate template, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 更新工作流模板
    /// </summary>
    /// <param name="templateId">模板ID</param>
    /// <param name="template">模板定义</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>更新结果</returns>
    Task<bool> UpdateTemplateAsync(string templateId, WorkflowTemplate template, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 删除工作流模板
    /// </summary>
    /// <param name="templateId">模板ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>删除结果</returns>
    Task<bool> DeleteTemplateAsync(string templateId, CancellationToken cancellationToken = default);
    
    #endregion
    
    #region 版本控制
    
    /// <summary>
    /// 获取工作流版本历史
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>版本历史</returns>
    Task<IReadOnlyList<WorkflowVersion>> GetVersionHistoryAsync(string workflowId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 创建工作流版本
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="versionInfo">版本信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>版本ID</returns>
    Task<string> CreateVersionAsync(string workflowId, WorkflowVersionInfo versionInfo, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取指定版本的工作流定义
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="version">版本号</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>工作流定义</returns>
    Task<WorkflowDefinition?> GetWorkflowVersionAsync(string workflowId, string version, CancellationToken cancellationToken = default);
    
    #endregion
    
    #region 协作功能
    
    /// <summary>
    /// 广播设计变更
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="operation">设计操作</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>广播任务</returns>
    Task BroadcastDesignChangeAsync(string workflowId, DesignOperation operation, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取活跃协作者
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>协作者列表</returns>
    Task<IReadOnlyList<CollaboratorInfo>> GetActiveCollaboratorsAsync(string workflowId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 加入协作会话
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="collaborator">协作者信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>加入结果</returns>
    Task<bool> JoinCollaborationAsync(string workflowId, CollaboratorInfo collaborator, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 离开协作会话
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="collaboratorId">协作者ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>离开结果</returns>
    Task<bool> LeaveCollaborationAsync(string workflowId, string collaboratorId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 解决设计冲突
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="conflictResolution">冲突解决方案</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>解决结果</returns>
    Task<bool> ResolveDesignConflictAsync(string workflowId, ConflictResolution conflictResolution, CancellationToken cancellationToken = default);
    
    #endregion
    
    #region 事件
    
    /// <summary>
    /// 工作流创建事件
    /// </summary>
    event EventHandler<WorkflowCreatedEventArgs>? WorkflowCreated;
    
    /// <summary>
    /// 工作流更新事件
    /// </summary>
    event EventHandler<WorkflowUpdatedEventArgs>? WorkflowUpdated;
    
    /// <summary>
    /// 工作流删除事件
    /// </summary>
    event EventHandler<WorkflowDeletedEventArgs>? WorkflowDeleted;
    
    /// <summary>
    /// 设计协作事件
    /// </summary>
    event EventHandler<DesignCollaborationEventArgs>? DesignCollaborationChanged;
    
    /// <summary>
    /// 模板变更事件
    /// </summary>
    event EventHandler<TemplateChangedEventArgs>? TemplateChanged;
    
    #endregion
}
