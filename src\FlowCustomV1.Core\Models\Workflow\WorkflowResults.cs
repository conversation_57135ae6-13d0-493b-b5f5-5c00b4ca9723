namespace FlowCustomV1.Core.Models.Workflow;

/// <summary>
/// 工作流执行结果
/// </summary>
public class WorkflowExecutionResult
{
    /// <summary>
    /// 执行ID
    /// </summary>
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 工作流ID
    /// </summary>
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 工作流名称
    /// </summary>
    public string? WorkflowName { get; set; }

    /// <summary>
    /// 执行器节点ID
    /// </summary>
    public string? ExecutorNodeId { get; set; }

    /// <summary>
    /// 执行状态
    /// </summary>
    public WorkflowExecutionState State { get; set; } = WorkflowExecutionState.NotStarted;

    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; } = false;

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime StartedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 完成时间
    /// </summary>
    public DateTime? CompletedAt { get; set; }

    /// <summary>
    /// 执行时长
    /// </summary>
    public TimeSpan Duration => CompletedAt?.Subtract(StartedAt) ?? TimeSpan.Zero;

    /// <summary>
    /// 输出数据
    /// </summary>
    public Dictionary<string, object> OutputData { get; set; } = new();

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 执行消息（成功或失败的描述信息）
    /// </summary>
    public string? Message { get; set; }

    /// <summary>
    /// 异常详情
    /// </summary>
    public Exception? Exception { get; set; }

    /// <summary>
    /// 节点执行结果列表
    /// </summary>
    public List<NodeExecutionResult> NodeResults { get; set; } = new();

    /// <summary>
    /// 执行统计信息
    /// </summary>
    public WorkflowExecutionStats Stats { get; set; } = new();

    /// <summary>
    /// 执行元数据
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// 节点执行结果
/// </summary>
public class NodeExecutionResult
{
    /// <summary>
    /// 节点ID
    /// </summary>
    public string NodeId { get; set; } = string.Empty;

    /// <summary>
    /// 执行ID
    /// </summary>
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 执行状态
    /// </summary>
    public NodeExecutionState State { get; set; } = NodeExecutionState.NotStarted;

    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; } = false;

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime StartedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 完成时间
    /// </summary>
    public DateTime? CompletedAt { get; set; }

    /// <summary>
    /// 执行时长
    /// </summary>
    public TimeSpan Duration => CompletedAt?.Subtract(StartedAt) ?? TimeSpan.Zero;

    /// <summary>
    /// 输入数据
    /// </summary>
    public Dictionary<string, object> InputData { get; set; } = new();

    /// <summary>
    /// 输出数据
    /// </summary>
    public Dictionary<string, object> OutputData { get; set; } = new();

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 异常详情
    /// </summary>
    public Exception? Exception { get; set; }

    /// <summary>
    /// 重试次数
    /// </summary>
    public int RetryCount { get; set; } = 0;

    /// <summary>
    /// 执行日志
    /// </summary>
    public List<string> ExecutionLogs { get; set; } = new();

    /// <summary>
    /// 性能指标
    /// </summary>
    public Dictionary<string, double> PerformanceMetrics { get; set; } = new();

    /// <summary>
    /// 执行元数据
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// 验证结果基类
/// </summary>
public class ValidationResult
{
    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid { get; set; } = true;

    /// <summary>
    /// 错误列表
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// 警告列表
    /// </summary>
    public List<string> Warnings { get; set; } = new();

    /// <summary>
    /// 验证详情
    /// </summary>
    public List<ValidationDetail> Details { get; set; } = new();

    /// <summary>
    /// 验证时间
    /// </summary>
    public DateTime ValidatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 验证上下文
    /// </summary>
    public Dictionary<string, object> Context { get; set; } = new();

    /// <summary>
    /// 验证错误列表（详细错误信息）
    /// </summary>
    public List<ValidationError> ValidationErrors { get; set; } = new();

    /// <summary>
    /// 验证警告列表（详细警告信息）
    /// </summary>
    public List<ValidationWarning> ValidationWarnings { get; set; } = new();
}

/// <summary>
/// 工作流验证结果
/// </summary>
public class WorkflowValidationResult : ValidationResult
{
    /// <summary>
    /// 工作流ID
    /// </summary>
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 工作流名称
    /// </summary>
    public string WorkflowName { get; set; } = string.Empty;
}

/// <summary>
/// 节点验证结果
/// </summary>
public class NodeValidationResult : ValidationResult
{
    /// <summary>
    /// 节点ID
    /// </summary>
    public string NodeId { get; set; } = string.Empty;

    /// <summary>
    /// 节点类型
    /// </summary>
    public string NodeType { get; set; } = string.Empty;
}

/// <summary>
/// 验证详情
/// </summary>
public class ValidationDetail
{
    /// <summary>
    /// 验证项目
    /// </summary>
    public string Item { get; set; } = string.Empty;

    /// <summary>
    /// 验证类型
    /// </summary>
    public string Type { get; set; } = string.Empty;

    /// <summary>
    /// 验证消息
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 严重程度
    /// </summary>
    public ValidationSeverity Severity { get; set; } = ValidationSeverity.Info;

    /// <summary>
    /// 验证代码
    /// </summary>
    public string? Code { get; set; }
}



/// <summary>
/// 工作流执行统计
/// </summary>
public class WorkflowExecutionStats
{
    /// <summary>
    /// 总节点数
    /// </summary>
    public int TotalNodes { get; set; } = 0;

    /// <summary>
    /// 已执行节点数
    /// </summary>
    public int ExecutedNodes { get; set; } = 0;

    /// <summary>
    /// 成功节点数
    /// </summary>
    public int SuccessfulNodes { get; set; } = 0;

    /// <summary>
    /// 失败节点数
    /// </summary>
    public int FailedNodes { get; set; } = 0;

    /// <summary>
    /// 跳过节点数
    /// </summary>
    public int SkippedNodes { get; set; } = 0;

    /// <summary>
    /// 总重试次数
    /// </summary>
    public int TotalRetries { get; set; } = 0;

    /// <summary>
    /// 平均执行时间（毫秒）
    /// </summary>
    public double AverageExecutionTimeMs { get; set; } = 0;

    /// <summary>
    /// 最大执行时间（毫秒）
    /// </summary>
    public double MaxExecutionTimeMs { get; set; } = 0;

    /// <summary>
    /// 最小执行时间（毫秒）
    /// </summary>
    public double MinExecutionTimeMs { get; set; } = 0;

    /// <summary>
    /// 计算成功率
    /// </summary>
    /// <returns>成功率百分比</returns>
    public double GetSuccessRate()
    {
        if (ExecutedNodes == 0) return 0;
        return (double)SuccessfulNodes / ExecutedNodes * 100;
    }

    /// <summary>
    /// 计算完成率
    /// </summary>
    /// <returns>完成率百分比</returns>
    public double GetCompletionRate()
    {
        if (TotalNodes == 0) return 0;
        return (double)ExecutedNodes / TotalNodes * 100;
    }
}
