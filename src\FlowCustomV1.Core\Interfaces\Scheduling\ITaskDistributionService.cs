using FlowCustomV1.Core.Models.Cluster;
using FlowCustomV1.Core.Models.Scheduling;
using FlowCustomV1.Core.Models.Workflow;

namespace FlowCustomV1.Core.Interfaces.Scheduling;

/// <summary>
/// 任务分发服务接口
/// 提供智能任务分发和负载均衡功能，支持多种分发策略和节点选择算法
/// </summary>
public interface ITaskDistributionService
{
    #region 任务分发

    /// <summary>
    /// 分发单个任务到最佳节点
    /// </summary>
    /// <param name="task">要分发的任务</param>
    /// <param name="strategy">分发策略</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>分发结果</returns>
    Task<TaskDistributionResult> DistributeTaskAsync(
        DistributedTask task, 
        TaskDistributionStrategy strategy = TaskDistributionStrategy.SmartLoad,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 批量分发任务到集群节点
    /// </summary>
    /// <param name="tasks">要分发的任务列表</param>
    /// <param name="strategy">分发策略</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>批量分发结果</returns>
    Task<BatchTaskDistributionResult> DistributeTasksAsync(
        IReadOnlyList<DistributedTask> tasks,
        TaskDistributionStrategy strategy = TaskDistributionStrategy.SmartLoad,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 重新分发失败的任务
    /// </summary>
    /// <param name="failedTask">失败的任务</param>
    /// <param name="excludeNodes">要排除的节点ID列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>重新分发结果</returns>
    Task<TaskDistributionResult> RedistributeTaskAsync(
        DistributedTask failedTask,
        IReadOnlyList<string>? excludeNodes = null,
        CancellationToken cancellationToken = default);

    #endregion

    #region 节点选择

    /// <summary>
    /// 根据任务要求选择最佳节点
    /// </summary>
    /// <param name="taskRequirements">任务要求</param>
    /// <param name="strategy">选择策略</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>选择的节点信息</returns>
    Task<NodeInfo?> SelectBestNodeAsync(
        TaskRequirements taskRequirements,
        NodeSelectionStrategy strategy = NodeSelectionStrategy.SmartSelection,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取符合条件的可用节点列表
    /// </summary>
    /// <param name="taskRequirements">任务要求</param>
    /// <param name="maxNodes">最大节点数量</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>可用节点列表</returns>
    Task<IReadOnlyList<NodeInfo>> GetAvailableNodesAsync(
        TaskRequirements taskRequirements,
        int maxNodes = 10,
        CancellationToken cancellationToken = default);

    #endregion

    #region 负载均衡

    /// <summary>
    /// 获取集群负载均衡状态
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>负载均衡状态</returns>
    Task<LoadBalancingStatus> GetLoadBalancingStatusAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 触发负载重新平衡
    /// </summary>
    /// <param name="targetNodes">目标节点列表（可选）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>重新平衡结果</returns>
    Task<LoadRebalancingResult> RebalanceLoadAsync(
        IReadOnlyList<string>? targetNodes = null,
        CancellationToken cancellationToken = default);

    #endregion

    #region 统计和监控

    /// <summary>
    /// 获取任务分发统计信息
    /// </summary>
    /// <param name="timeRange">时间范围</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>分发统计信息</returns>
    Task<TaskDistributionStatistics> GetDistributionStatisticsAsync(
        TimeSpan? timeRange = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取节点性能指标
    /// </summary>
    /// <param name="nodeId">节点ID（可选，为空则返回所有节点）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>节点性能指标</returns>
    Task<IReadOnlyList<NodePerformanceMetrics>> GetNodePerformanceMetricsAsync(
        string? nodeId = null,
        CancellationToken cancellationToken = default);

    #endregion

    #region 事件

    /// <summary>
    /// 任务分发完成事件
    /// </summary>
    event EventHandler<TaskDistributedEventArgs>? TaskDistributed;

    /// <summary>
    /// 任务重新分发事件
    /// </summary>
    event EventHandler<TaskRedistributedEventArgs>? TaskRedistributed;

    /// <summary>
    /// 负载重新平衡事件
    /// </summary>
    event EventHandler<LoadRebalancedEventArgs>? LoadRebalanced;

    #endregion
}

/// <summary>
/// 任务分发策略枚举
/// </summary>
public enum TaskDistributionStrategy
{
    /// <summary>
    /// 智能负载分发（综合考虑负载、能力、地理位置等因素）
    /// </summary>
    SmartLoad,

    /// <summary>
    /// 最低负载优先
    /// </summary>
    LeastLoad,

    /// <summary>
    /// 最快响应时间
    /// </summary>
    FastestResponse,

    /// <summary>
    /// 轮询分发
    /// </summary>
    RoundRobin,

    /// <summary>
    /// 随机分发
    /// </summary>
    Random,

    /// <summary>
    /// 基于能力匹配
    /// </summary>
    CapabilityBased,

    /// <summary>
    /// 地理位置优先
    /// </summary>
    GeographyBased,

    /// <summary>
    /// 加权轮询
    /// </summary>
    WeightedRoundRobin
}

/// <summary>
/// 节点选择策略枚举
/// </summary>
public enum NodeSelectionStrategy
{
    /// <summary>
    /// 智能选择（综合评分）
    /// </summary>
    SmartSelection,

    /// <summary>
    /// 性能优先
    /// </summary>
    PerformanceBased,

    /// <summary>
    /// 可用性优先
    /// </summary>
    AvailabilityBased,

    /// <summary>
    /// 成本优先
    /// </summary>
    CostBased,

    /// <summary>
    /// 地理位置优先
    /// </summary>
    LocationBased
}
