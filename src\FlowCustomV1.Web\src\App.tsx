import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';

import BasicLayout from '@/components/Layout/BasicLayout';
import Dashboard from '@/pages/Dashboard';
import WorkflowList from '@/pages/Workflow/WorkflowList';
import WorkflowDetail from '@/pages/Workflow/WorkflowDetail';
import WorkflowCreate from '@/pages/Workflow/WorkflowCreate';
import WorkflowEdit from '@/pages/Workflow/WorkflowEdit';
import ApiTest from '@/pages/Test/ApiTest';
import WorkflowDesigner from '@/pages/Workflow/WorkflowDesigner';
import WorkflowDesignerTest from '@/pages/Workflow/WorkflowDesignerTest';
import WorkflowDesignerReactFlow from '@/pages/Workflow/WorkflowDesignerReactFlow';
import WorkflowDesignerReactFlowFixed from '@/pages/Workflow/WorkflowDesignerReactFlowFixed';

import WorkflowTemplates from '@/pages/Workflow/WorkflowTemplates';
import WorkflowDebugger from '@/pages/Workflow/WorkflowDebugger';
import ExecutionMonitor from '@/pages/Execution/ExecutionMonitor';
import ExecutionHistory from '@/pages/Execution/ExecutionHistory';
import ClusterOverview from '@/pages/Cluster/ClusterOverview';
import ClusterNodes from '@/pages/Cluster/ClusterNodes';
import NodeDesigner from '@/pages/Nodes/NodeDesigner';
import NodeValidator from '@/pages/Nodes/NodeValidator';
import NodeExecutor from '@/pages/Nodes/NodeExecutor';
import MonitoringDashboard from '@/pages/Monitoring/MonitoringDashboard';
import PluginsMarket from '@/pages/Plugins/PluginsMarket';
import UserManagement from '@/pages/Security/UserManagement';
import RolePermissions from '@/pages/Security/RolePermissions';
import SystemConfig from '@/pages/System/SystemConfig';

// 404 页面组件
const NotFound: React.FC = () => (
  <div className="flex items-center justify-center h-full">
    <div className="text-center">
      <h1 className="text-4xl font-bold text-gray-400 mb-4">404</h1>
      <p className="text-gray-500">页面不存在</p>
    </div>
  </div>
);

// 开发中页面组件
const UnderDevelopment: React.FC<{ title?: string }> = ({ title = '功能开发中' }) => (
  <div className="page-container">
    <div className="text-center py-20">
      <h2 className="text-2xl font-semibold text-gray-600 mb-4">{title}</h2>
      <p className="text-gray-500">该功能正在开发中，敬请期待...</p>
    </div>
  </div>
);

const App: React.FC = () => {
  return (
    <ConfigProvider locale={zhCN}>
      <Router>
        <BasicLayout>
            <Routes>
              {/* 默认重定向到仪表盘 */}
              <Route path="/" element={<Navigate to="/dashboard" replace />} />

              {/* 仪表盘 */}
              <Route path="/dashboard" element={<Dashboard />} />

              {/* 工作流管理 */}
              <Route path="/workflow/list" element={<WorkflowList />} />
              <Route path="/workflow/detail/:id" element={<WorkflowDetail />} />
              <Route path="/workflow/create" element={<WorkflowCreate />} />
              <Route path="/workflow/edit/:id" element={<WorkflowEdit />} />
              <Route path="/test/api" element={<ApiTest />} />
              <Route path="/workflow/designer" element={<WorkflowDesigner />} />
              <Route path="/workflow/designer/:id" element={<WorkflowDesigner />} />
              <Route path="/workflow/designer-test" element={<WorkflowDesignerTest />} />
              <Route path="/workflow/designer-reactflow" element={<WorkflowDesignerReactFlow />} />
              <Route path="/workflow/designer-reactflow-fixed" element={<WorkflowDesignerReactFlowFixed />} />
  

              <Route path="/workflow/templates" element={<WorkflowTemplates />} />
              <Route path="/workflow/debugger" element={<WorkflowDebugger />} />
              <Route path="/workflow/import-export" element={<UnderDevelopment title="导入导出功能" />} />

              {/* 执行管理 */}
              <Route path="/execution/monitor" element={<ExecutionMonitor />} />
              <Route path="/execution/history" element={<ExecutionHistory />} />
              <Route path="/execution/schedule" element={<UnderDevelopment title="定时调度功能" />} />
              <Route path="/execution/queue" element={<UnderDevelopment title="执行队列功能" />} />

              {/* 集群管理 */}
              <Route path="/cluster/overview" element={<ClusterOverview />} />
              <Route path="/cluster/nodes" element={<ClusterNodes />} />
              <Route path="/cluster/topology" element={<UnderDevelopment title="集群拓扑功能" />} />
              <Route path="/cluster/health" element={<UnderDevelopment title="健康检查功能" />} />

              {/* 节点服务 */}
              <Route path="/nodes/designer" element={<NodeDesigner />} />
              <Route path="/nodes/validator" element={<NodeValidator />} />
              <Route path="/nodes/executor" element={<NodeExecutor />} />
              <Route path="/nodes/monitor" element={<UnderDevelopment title="Monitor 监控器" />} />
              <Route path="/nodes/scheduler" element={<UnderDevelopment title="Scheduler 调度器" />} />
              <Route path="/nodes/storage" element={<UnderDevelopment title="Storage 存储器" />} />

              {/* 监控中心 */}
              <Route path="/monitoring/dashboard" element={<MonitoringDashboard />} />
              <Route path="/monitoring/metrics" element={<UnderDevelopment title="性能指标功能" />} />
              <Route path="/monitoring/alerts" element={<UnderDevelopment title="告警管理功能" />} />
              <Route path="/monitoring/logs" element={<UnderDevelopment title="日志中心功能" />} />

              {/* 插件管理 */}
              <Route path="/plugins/market" element={<PluginsMarket />} />
              <Route path="/plugins/installed" element={<UnderDevelopment title="已安装插件" />} />
              <Route path="/plugins/development" element={<UnderDevelopment title="插件开发功能" />} />
              <Route path="/plugins/natasha" element={<UnderDevelopment title="Natasha 编译器" />} />

              {/* 数据管理 */}
              <Route path="/data/sources" element={<UnderDevelopment title="数据源管理" />} />
              <Route path="/data/processing" element={<UnderDevelopment title="数据处理功能" />} />
              <Route path="/data/backup" element={<UnderDevelopment title="数据备份功能" />} />
              <Route path="/data/migration" element={<UnderDevelopment title="数据迁移功能" />} />

              {/* 系统集成 */}
              <Route path="/integration/apis" element={<UnderDevelopment title="API 管理" />} />
              <Route path="/integration/webhooks" element={<UnderDevelopment title="Webhook 管理" />} />
              <Route path="/integration/nats" element={<UnderDevelopment title="NATS 消息队列" />} />
              <Route path="/integration/external" element={<UnderDevelopment title="外部系统集成" />} />

              {/* 安全管理 */}
              <Route path="/security/users" element={<UserManagement />} />
              <Route path="/security/roles" element={<RolePermissions />} />
              <Route path="/security/audit" element={<UnderDevelopment title="审计日志" />} />
              <Route path="/security/tokens" element={<UnderDevelopment title="API 令牌管理" />} />

              {/* 开发工具 */}
              <Route path="/tools/debugger" element={<UnderDevelopment title="工作流调试器" />} />
              <Route path="/tools/testing" element={<UnderDevelopment title="测试工具" />} />
              <Route path="/tools/performance" element={<UnderDevelopment title="性能分析工具" />} />
              <Route path="/tools/code-generator" element={<UnderDevelopment title="代码生成器" />} />

              {/* 系统设置 */}
              <Route path="/system/config" element={<SystemConfig />} />
              <Route path="/system/environment" element={<UnderDevelopment title="环境管理" />} />
              <Route path="/system/maintenance" element={<UnderDevelopment title="系统维护" />} />
              <Route path="/system/about" element={<UnderDevelopment title="关于系统" />} />

              {/* 404 页面 */}
              <Route path="*" element={<NotFound />} />
            </Routes>
          </BasicLayout>
        </Router>
    </ConfigProvider>
  );
};

export default App;
