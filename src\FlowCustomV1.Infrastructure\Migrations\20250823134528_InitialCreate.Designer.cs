﻿// <auto-generated />
using System;
using FlowCustomV1.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace FlowCustomV1.Infrastructure.Migrations
{
    [DbContext(typeof(WorkflowDbContext))]
    [Migration("20250823134528_InitialCreate")]
    partial class InitialCreate
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder.HasAnnotation("ProductVersion", "8.0.8");

            modelBuilder.Entity("FlowCustomV1.Infrastructure.Entities.NodeExecutionEntity", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("CompletedAt")
                        .HasColumnType("TEXT")
                        .HasColumnName("CompletedAt");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT")
                        .HasColumnName("CreatedAt");

                    b.Property<string>("ErrorMessage")
                        .HasColumnType("TEXT")
                        .HasColumnName("ErrorMessage");

                    b.Property<string>("ExecutionId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT")
                        .HasColumnName("ExecutionId");

                    b.Property<string>("ExecutionLogs")
                        .HasColumnType("TEXT")
                        .HasColumnName("ExecutionLogs");

                    b.Property<string>("InputData")
                        .HasColumnType("TEXT")
                        .HasColumnName("InputData");

                    b.Property<string>("InstanceId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(255)");

                    b.Property<bool>("IsSuccess")
                        .HasColumnType("INTEGER")
                        .HasColumnName("IsSuccess");

                    b.Property<DateTime>("LastUpdatedAt")
                        .HasColumnType("TEXT")
                        .HasColumnName("LastUpdatedAt");

                    b.Property<string>("Metadata")
                        .HasColumnType("TEXT")
                        .HasColumnName("Metadata");

                    b.Property<string>("NodeId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("NodeType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT")
                        .HasColumnName("NodeType");

                    b.Property<string>("OutputData")
                        .HasColumnType("TEXT")
                        .HasColumnName("OutputData");

                    b.Property<string>("PerformanceMetrics")
                        .HasColumnType("TEXT")
                        .HasColumnName("PerformanceMetrics");

                    b.Property<int>("RetryCount")
                        .HasColumnType("INTEGER")
                        .HasColumnName("RetryCount");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("BLOB");

                    b.Property<DateTime>("StartedAt")
                        .HasColumnType("TEXT")
                        .HasColumnName("StartedAt");

                    b.Property<int>("State")
                        .HasColumnType("INTEGER")
                        .HasColumnName("State");

                    b.HasKey("Id");

                    b.HasIndex("ExecutionId")
                        .HasDatabaseName("IX_NodeExecutions_ExecutionId");

                    b.HasIndex("InstanceId")
                        .HasDatabaseName("IX_NodeExecutions_InstanceId");

                    b.HasIndex("StartedAt")
                        .HasDatabaseName("IX_NodeExecutions_StartedAt");

                    b.HasIndex("State")
                        .HasDatabaseName("IX_NodeExecutions_State");

                    b.HasIndex("InstanceId", "NodeId")
                        .HasDatabaseName("IX_NodeExecutions_InstanceId_NodeId");

                    b.ToTable("NodeExecutions");
                });

            modelBuilder.Entity("FlowCustomV1.Infrastructure.Entities.WorkflowDefinitionEntity", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Author")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT")
                        .HasColumnName("Author");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT")
                        .HasColumnName("CreatedAt");

                    b.Property<string>("DefinitionJson")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnName("DefinitionJson");

                    b.Property<string>("Description")
                        .HasColumnType("TEXT")
                        .HasColumnName("Description");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER")
                        .HasColumnName("IsActive");

                    b.Property<DateTime>("LastModifiedAt")
                        .HasColumnType("TEXT")
                        .HasColumnName("LastModifiedAt");

                    b.Property<string>("Metadata")
                        .HasColumnType("TEXT")
                        .HasColumnName("Metadata");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT")
                        .HasColumnName("Name");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("BLOB");

                    b.Property<string>("Tags")
                        .HasColumnType("TEXT")
                        .HasColumnName("Tags");

                    b.Property<string>("Version")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT")
                        .HasColumnName("Version");

                    b.Property<string>("WorkflowId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(255)");

                    b.HasKey("Id");

                    b.HasIndex("Author")
                        .HasDatabaseName("IX_WorkflowDefinitions_Author");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("IX_WorkflowDefinitions_CreatedAt");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_WorkflowDefinitions_IsActive");

                    b.HasIndex("WorkflowId")
                        .IsUnique()
                        .HasDatabaseName("IX_WorkflowDefinitions_WorkflowId");

                    b.HasIndex("Name", "Version")
                        .HasDatabaseName("IX_WorkflowDefinitions_Name_Version");

                    b.ToTable("WorkflowDefinitions");
                });

            modelBuilder.Entity("FlowCustomV1.Infrastructure.Entities.WorkflowInstanceEntity", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("CompletedAt")
                        .HasColumnType("TEXT")
                        .HasColumnName("CompletedAt");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT")
                        .HasColumnName("CreatedAt");

                    b.Property<string>("ErrorMessage")
                        .HasColumnType("TEXT")
                        .HasColumnName("ErrorMessage");

                    b.Property<string>("InputData")
                        .HasColumnType("TEXT")
                        .HasColumnName("InputData");

                    b.Property<string>("InstanceId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(255)");

                    b.Property<bool>("IsSuccess")
                        .HasColumnType("INTEGER")
                        .HasColumnName("IsSuccess");

                    b.Property<DateTime>("LastUpdatedAt")
                        .HasColumnType("TEXT")
                        .HasColumnName("LastUpdatedAt");

                    b.Property<string>("Message")
                        .HasColumnType("TEXT")
                        .HasColumnName("Message");

                    b.Property<string>("Metadata")
                        .HasColumnType("TEXT")
                        .HasColumnName("Metadata");

                    b.Property<string>("OutputData")
                        .HasColumnType("TEXT")
                        .HasColumnName("OutputData");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("BLOB");

                    b.Property<DateTime>("StartedAt")
                        .HasColumnType("TEXT")
                        .HasColumnName("StartedAt");

                    b.Property<int>("State")
                        .HasColumnType("INTEGER")
                        .HasColumnName("State");

                    b.Property<string>("Stats")
                        .HasColumnType("TEXT")
                        .HasColumnName("Stats");

                    b.Property<string>("WorkflowId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(255)");

                    b.HasKey("Id");

                    b.HasIndex("CompletedAt")
                        .HasDatabaseName("IX_WorkflowInstances_CompletedAt");

                    b.HasIndex("InstanceId")
                        .IsUnique()
                        .HasDatabaseName("IX_WorkflowInstances_InstanceId");

                    b.HasIndex("StartedAt")
                        .HasDatabaseName("IX_WorkflowInstances_StartedAt");

                    b.HasIndex("State")
                        .HasDatabaseName("IX_WorkflowInstances_State");

                    b.HasIndex("WorkflowId")
                        .HasDatabaseName("IX_WorkflowInstances_WorkflowId");

                    b.ToTable("WorkflowInstances");
                });

            modelBuilder.Entity("FlowCustomV1.Infrastructure.Entities.NodeExecutionEntity", b =>
                {
                    b.HasOne("FlowCustomV1.Infrastructure.Entities.WorkflowInstanceEntity", "WorkflowInstance")
                        .WithMany("NodeExecutions")
                        .HasForeignKey("InstanceId")
                        .HasPrincipalKey("InstanceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("WorkflowInstance");
                });

            modelBuilder.Entity("FlowCustomV1.Infrastructure.Entities.WorkflowInstanceEntity", b =>
                {
                    b.HasOne("FlowCustomV1.Infrastructure.Entities.WorkflowDefinitionEntity", "WorkflowDefinition")
                        .WithMany("Instances")
                        .HasForeignKey("WorkflowId")
                        .HasPrincipalKey("WorkflowId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("WorkflowDefinition");
                });

            modelBuilder.Entity("FlowCustomV1.Infrastructure.Entities.WorkflowDefinitionEntity", b =>
                {
                    b.Navigation("Instances");
                });

            modelBuilder.Entity("FlowCustomV1.Infrastructure.Entities.WorkflowInstanceEntity", b =>
                {
                    b.Navigation("NodeExecutions");
                });
#pragma warning restore 612, 618
        }
    }
}
