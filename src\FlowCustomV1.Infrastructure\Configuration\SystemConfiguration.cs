using System.ComponentModel.DataAnnotations;

namespace FlowCustomV1.Infrastructure.Configuration;

/// <summary>
/// 系统配置
/// 包含系统级别的参数配置，如端口号、超时时间、性能阈值等
/// </summary>
public class SystemConfiguration
{
    /// <summary>
    /// 配置节名称
    /// </summary>
    public const string SectionName = "System";

    /// <summary>
    /// 网络配置
    /// </summary>
    public NetworkConfiguration Network { get; set; } = new();

    /// <summary>
    /// 性能配置
    /// </summary>
    public PerformanceConfiguration Performance { get; set; } = new();

    /// <summary>
    /// 超时配置
    /// </summary>
    public TimeoutConfiguration Timeouts { get; set; } = new();

    /// <summary>
    /// 节点配置
    /// </summary>
    public SystemNodeConfiguration Node { get; set; } = new();

    /// <summary>
    /// 监控配置
    /// </summary>
    public MonitoringConfiguration Monitoring { get; set; } = new();
}

/// <summary>
/// 网络配置
/// </summary>
public class NetworkConfiguration
{
    /// <summary>
    /// HTTP API端口
    /// </summary>
    [Range(1024, 65535)]
    public int HttpPort { get; set; } = 5000;

    /// <summary>
    /// HTTPS API端口
    /// </summary>
    [Range(1024, 65535)]
    public int HttpsPort { get; set; } = 5001;

    /// <summary>
    /// WebSocket端口
    /// </summary>
    [Range(1024, 65535)]
    public int WebSocketPort { get; set; } = 8080;

    /// <summary>
    /// 管理端口
    /// </summary>
    [Range(1024, 65535)]
    public int ManagementPort { get; set; } = 8081;

    /// <summary>
    /// 绑定地址
    /// </summary>
    public string BindAddress { get; set; } = "0.0.0.0";

    /// <summary>
    /// 允许的主机列表
    /// </summary>
    public List<string> AllowedHosts { get; set; } = new() { "*" };

    /// <summary>
    /// CORS允许的源
    /// </summary>
    public List<string> CorsAllowedOrigins { get; set; } = new() { "*" };
}

/// <summary>
/// 性能配置
/// </summary>
public class PerformanceConfiguration
{
    /// <summary>
    /// 最大并发工作流数
    /// </summary>
    [Range(1, 10000)]
    public int MaxConcurrentWorkflows { get; set; } = 100;

    /// <summary>
    /// 最大并发节点数
    /// </summary>
    [Range(1, 1000)]
    public int MaxConcurrentNodes { get; set; } = 50;

    /// <summary>
    /// 工作线程池大小
    /// </summary>
    [Range(1, 100)]
    public int WorkerThreadPoolSize { get; set; } = Environment.ProcessorCount * 2;

    /// <summary>
    /// I/O线程池大小
    /// </summary>
    [Range(1, 100)]
    public int IoThreadPoolSize { get; set; } = Environment.ProcessorCount;

    /// <summary>
    /// 消息队列大小
    /// </summary>
    [Range(100, 100000)]
    public int MessageQueueSize { get; set; } = 10000;

    /// <summary>
    /// 批处理大小
    /// </summary>
    [Range(1, 1000)]
    public int BatchSize { get; set; } = 100;

    /// <summary>
    /// 内存使用阈值（MB）
    /// </summary>
    [Range(100, 32768)]
    public int MemoryThresholdMb { get; set; } = 2048;

    /// <summary>
    /// CPU使用阈值（百分比）
    /// </summary>
    [Range(10, 100)]
    public int CpuThresholdPercent { get; set; } = 80;
}

/// <summary>
/// 超时配置
/// </summary>
public class TimeoutConfiguration
{
    /// <summary>
    /// HTTP请求超时时间（秒）
    /// </summary>
    [Range(1, 300)]
    public int HttpRequestTimeoutSeconds { get; set; } = 30;

    /// <summary>
    /// 数据库操作超时时间（秒）
    /// </summary>
    [Range(1, 300)]
    public int DatabaseTimeoutSeconds { get; set; } = 30;

    /// <summary>
    /// 工作流执行超时时间（分钟）
    /// </summary>
    [Range(1, 1440)]
    public int WorkflowExecutionTimeoutMinutes { get; set; } = 60;

    /// <summary>
    /// 节点执行超时时间（秒）
    /// </summary>
    [Range(1, 3600)]
    public int NodeExecutionTimeoutSeconds { get; set; } = 300;

    /// <summary>
    /// 消息处理超时时间（秒）
    /// </summary>
    [Range(1, 60)]
    public int MessageProcessingTimeoutSeconds { get; set; } = 10;

    /// <summary>
    /// 健康检查超时时间（秒）
    /// </summary>
    [Range(1, 60)]
    public int HealthCheckTimeoutSeconds { get; set; } = 5;

    /// <summary>
    /// 关闭超时时间（秒）
    /// </summary>
    [Range(1, 60)]
    public int ShutdownTimeoutSeconds { get; set; } = 30;
}

/// <summary>
/// 系统节点配置
/// </summary>
public class SystemNodeConfiguration
{
    /// <summary>
    /// 节点ID
    /// </summary>
    public string NodeId { get; set; } = Environment.MachineName;

    /// <summary>
    /// 节点名称
    /// </summary>
    public string NodeName { get; set; } = Environment.MachineName;

    /// <summary>
    /// 节点IP地址
    /// </summary>
    public string IpAddress { get; set; } = "127.0.0.1";

    /// <summary>
    /// 节点角色列表
    /// </summary>
    public List<string> Roles { get; set; } = new() { "Worker" };

    /// <summary>
    /// 节点标签
    /// </summary>
    public Dictionary<string, string> Labels { get; set; } = new();

    /// <summary>
    /// 最大内存使用量（MB）
    /// </summary>
    [Range(128, 32768)]
    public int MaxMemoryMb { get; set; } = 1024;

    /// <summary>
    /// 最大CPU使用率（百分比）
    /// </summary>
    [Range(10, 100)]
    public int MaxCpuPercent { get; set; } = 80;

    /// <summary>
    /// 心跳间隔（秒）
    /// </summary>
    [Range(5, 300)]
    public int HeartbeatIntervalSeconds { get; set; } = 30;

    /// <summary>
    /// 是否启用自动发现
    /// </summary>
    public bool EnableAutoDiscovery { get; set; } = true;
}

/// <summary>
/// 监控配置
/// </summary>
public class MonitoringConfiguration
{
    /// <summary>
    /// 是否启用监控
    /// </summary>
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// 指标收集间隔（秒）
    /// </summary>
    [Range(1, 300)]
    public int MetricsCollectionIntervalSeconds { get; set; } = 60;

    /// <summary>
    /// 健康检查间隔（秒）
    /// </summary>
    [Range(5, 300)]
    public int HealthCheckIntervalSeconds { get; set; } = 30;

    /// <summary>
    /// 日志级别
    /// </summary>
    public string LogLevel { get; set; } = "Information";

    /// <summary>
    /// 是否启用性能计数器
    /// </summary>
    public bool EnablePerformanceCounters { get; set; } = true;

    /// <summary>
    /// 是否启用分布式追踪
    /// </summary>
    public bool EnableDistributedTracing { get; set; } = false;

    /// <summary>
    /// 追踪采样率（0.0-1.0）
    /// </summary>
    [Range(0.0, 1.0)]
    public double TracingSampleRate { get; set; } = 0.1;

    /// <summary>
    /// 指标保留天数
    /// </summary>
    [Range(1, 365)]
    public int MetricsRetentionDays { get; set; } = 30;
}
