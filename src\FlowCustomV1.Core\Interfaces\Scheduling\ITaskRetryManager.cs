using FlowCustomV1.Core.Models.Scheduling;

namespace FlowCustomV1.Core.Interfaces.Scheduling;

/// <summary>
/// 任务重试管理器接口
/// 提供任务失败后的智能重试和故障恢复功能
/// </summary>
public interface ITaskRetryManager
{
    #region 重试策略管理

    /// <summary>
    /// 注册重试策略
    /// </summary>
    /// <param name="strategy">重试策略</param>
    void RegisterRetryStrategy(IRetryStrategy strategy);

    /// <summary>
    /// 获取重试策略
    /// </summary>
    /// <param name="strategyName">策略名称</param>
    /// <returns>重试策略</returns>
    IRetryStrategy? GetRetryStrategy(string strategyName);

    /// <summary>
    /// 获取所有重试策略
    /// </summary>
    /// <returns>重试策略列表</returns>
    IReadOnlyList<IRetryStrategy> GetAllRetryStrategies();

    /// <summary>
    /// 移除重试策略
    /// </summary>
    /// <param name="strategyName">策略名称</param>
    /// <returns>是否成功移除</returns>
    bool RemoveRetryStrategy(string strategyName);

    #endregion

    #region 任务重试管理

    /// <summary>
    /// 计划任务重试
    /// </summary>
    /// <param name="failedTask">失败的任务</param>
    /// <param name="failureReason">失败原因</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>重试计划结果</returns>
    Task<TaskRetryPlanResult> PlanRetryAsync(
        DistributedTask failedTask,
        TaskFailureInfo failureReason,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 执行任务重试
    /// </summary>
    /// <param name="retryPlan">重试计划</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>重试执行结果</returns>
    Task<TaskRetryExecutionResult> ExecuteRetryAsync(
        TaskRetryPlan retryPlan,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 取消任务重试
    /// </summary>
    /// <param name="taskId">任务ID</param>
    /// <param name="reason">取消原因</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>取消结果</returns>
    Task<TaskRetryCancelResult> CancelRetryAsync(
        string taskId,
        string reason = "",
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取任务重试状态
    /// </summary>
    /// <param name="taskId">任务ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>重试状态</returns>
    Task<TaskRetryStatus?> GetRetryStatusAsync(
        string taskId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 批量获取任务重试状态
    /// </summary>
    /// <param name="taskIds">任务ID列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>重试状态列表</returns>
    Task<IReadOnlyList<TaskRetryStatus>> GetRetryStatusBatchAsync(
        IReadOnlyList<string> taskIds,
        CancellationToken cancellationToken = default);

    #endregion

    #region 故障恢复

    /// <summary>
    /// 分析任务失败模式
    /// </summary>
    /// <param name="failureHistory">失败历史</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>失败模式分析结果</returns>
    Task<FailurePatternAnalysis> AnalyzeFailurePatternsAsync(
        IReadOnlyList<TaskFailureInfo> failureHistory,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 生成故障恢复建议
    /// </summary>
    /// <param name="failureAnalysis">失败分析</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>恢复建议</returns>
    Task<RecoveryRecommendations> GenerateRecoveryRecommendationsAsync(
        FailurePatternAnalysis failureAnalysis,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 执行自动故障恢复
    /// </summary>
    /// <param name="recommendations">恢复建议</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>恢复执行结果</returns>
    Task<RecoveryExecutionResult> ExecuteAutoRecoveryAsync(
        RecoveryRecommendations recommendations,
        CancellationToken cancellationToken = default);

    #endregion

    #region 监控和统计

    /// <summary>
    /// 获取重试统计信息
    /// </summary>
    /// <param name="timeRange">时间范围</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>重试统计</returns>
    Task<RetryStatistics> GetRetryStatisticsAsync(
        TimeSpan? timeRange = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取失败模式统计
    /// </summary>
    /// <param name="timeRange">时间范围</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>失败模式统计</returns>
    Task<FailurePatternStatistics> GetFailurePatternStatisticsAsync(
        TimeSpan? timeRange = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取恢复效果统计
    /// </summary>
    /// <param name="timeRange">时间范围</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>恢复效果统计</returns>
    Task<RecoveryEffectivenessStatistics> GetRecoveryEffectivenessAsync(
        TimeSpan? timeRange = null,
        CancellationToken cancellationToken = default);

    #endregion

    #region 配置管理

    /// <summary>
    /// 更新重试配置
    /// </summary>
    /// <param name="taskType">任务类型</param>
    /// <param name="config">重试配置</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>更新结果</returns>
    Task<ConfigUpdateResult> UpdateRetryConfigAsync(
        string taskType,
        TaskRetryConfig config,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取重试配置
    /// </summary>
    /// <param name="taskType">任务类型</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>重试配置</returns>
    Task<TaskRetryConfig?> GetRetryConfigAsync(
        string taskType,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取所有重试配置
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>重试配置字典</returns>
    Task<IReadOnlyDictionary<string, TaskRetryConfig>> GetAllRetryConfigsAsync(
        CancellationToken cancellationToken = default);

    #endregion

    #region 事件

    /// <summary>
    /// 任务重试计划事件
    /// </summary>
    event EventHandler<TaskRetryPlannedEventArgs>? TaskRetryPlanned;

    /// <summary>
    /// 任务重试开始事件
    /// </summary>
    event EventHandler<TaskRetryStartedEventArgs>? TaskRetryStarted;

    /// <summary>
    /// 任务重试完成事件
    /// </summary>
    event EventHandler<TaskRetryCompletedEventArgs>? TaskRetryCompleted;

    /// <summary>
    /// 任务重试失败事件
    /// </summary>
    event EventHandler<TaskRetryFailedEventArgs>? TaskRetryFailed;

    /// <summary>
    /// 任务重试取消事件
    /// </summary>
    event EventHandler<TaskRetryCancelledEventArgs>? TaskRetryCancelled;

    /// <summary>
    /// 故障恢复执行事件
    /// </summary>
    event EventHandler<RecoveryExecutedEventArgs>? RecoveryExecuted;

    #endregion

    #region 健康检查

    /// <summary>
    /// 检查重试管理器健康状态
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>健康检查结果</returns>
    Task<HealthCheckResult> CheckHealthAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 清理过期的重试数据
    /// </summary>
    /// <param name="retentionPeriod">保留期间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>清理结果</returns>
    Task<CleanupResult> CleanupExpiredRetryDataAsync(
        TimeSpan retentionPeriod,
        CancellationToken cancellationToken = default);

    #endregion
}

/// <summary>
/// 重试策略接口
/// </summary>
public interface IRetryStrategy
{
    /// <summary>
    /// 策略名称
    /// </summary>
    string StrategyName { get; }

    /// <summary>
    /// 策略描述
    /// </summary>
    string Description { get; }

    /// <summary>
    /// 是否支持指定的失败类型
    /// </summary>
    /// <param name="failureInfo">失败信息</param>
    /// <returns>是否支持</returns>
    bool SupportsFailureType(TaskFailureInfo failureInfo);

    /// <summary>
    /// 计算重试延迟
    /// </summary>
    /// <param name="retryCount">重试次数</param>
    /// <param name="failureInfo">失败信息</param>
    /// <param name="config">重试配置</param>
    /// <returns>重试延迟（毫秒）</returns>
    long CalculateRetryDelay(int retryCount, TaskFailureInfo failureInfo, TaskRetryConfig config);

    /// <summary>
    /// 判断是否应该重试
    /// </summary>
    /// <param name="retryCount">当前重试次数</param>
    /// <param name="failureInfo">失败信息</param>
    /// <param name="config">重试配置</param>
    /// <returns>是否应该重试</returns>
    bool ShouldRetry(int retryCount, TaskFailureInfo failureInfo, TaskRetryConfig config);

    /// <summary>
    /// 获取重试建议
    /// </summary>
    /// <param name="failureInfo">失败信息</param>
    /// <param name="config">重试配置</param>
    /// <returns>重试建议</returns>
    RetryRecommendation GetRetryRecommendation(TaskFailureInfo failureInfo, TaskRetryConfig config);
}
