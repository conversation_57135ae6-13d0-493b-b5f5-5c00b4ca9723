# FlowCustomV1 v0.0.1.7 Docker集群测试运行脚本
# 使用真实的Docker多节点集群进行全面测试

param(
    [string]$TestScope = "All",
    [switch]$IncludePerformanceTests,
    [switch]$IncludeResilienceTests,
    [switch]$EnableMonitoring,
    [switch]$KeepClusterAlive,
    [switch]$CleanupBeforeStart,
    [string]$LogLevel = "Information"
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# 测试配置
$DockerComposeFile = "tests/docker/docker-compose.test-cluster.yml"
$TestResultsPath = "tests/docker/test-results"
$TestStartTime = Get-Date

Write-Host "================================================================" -ForegroundColor Green
Write-Host "FlowCustomV1 v0.0.1.7 Docker集群测试套件" -ForegroundColor Green
Write-Host "================================================================" -ForegroundColor Green
Write-Host "测试开始时间: $($TestStartTime.ToString('yyyy-MM-dd HH:mm:ss'))" -ForegroundColor Gray
Write-Host "测试范围: $TestScope" -ForegroundColor Gray
Write-Host "Docker Compose文件: $DockerComposeFile" -ForegroundColor Gray
Write-Host "测试结果路径: $TestResultsPath" -ForegroundColor Gray
Write-Host ""

# 检查Docker和Docker Compose
Write-Host "=== 检查环境依赖 ===" -ForegroundColor Yellow
try {
    $dockerVersion = docker --version
    Write-Host "✓ Docker: $dockerVersion" -ForegroundColor Green
    
    $composeVersion = docker compose version
    Write-Host "✓ Docker Compose: $composeVersion" -ForegroundColor Green
}
catch {
    Write-Host "✗ Docker或Docker Compose未安装或不可用" -ForegroundColor Red
    Write-Host "请确保Docker Desktop已安装并正在运行" -ForegroundColor Yellow
    exit 1
}

# 创建测试结果目录
if (!(Test-Path $TestResultsPath)) {
    New-Item -ItemType Directory -Path $TestResultsPath -Force | Out-Null
    Write-Host "✓ 创建测试结果目录: $TestResultsPath" -ForegroundColor Green
}

# 清理现有容器（如果需要）
if ($CleanupBeforeStart) {
    Write-Host "=== 清理现有容器 ===" -ForegroundColor Yellow
    try {
        docker compose -f $DockerComposeFile down --volumes --remove-orphans
        Write-Host "✓ 现有容器已清理" -ForegroundColor Green
    }
    catch {
        Write-Host "⚠ 清理容器时出现警告: $_" -ForegroundColor Yellow
    }
}

# 构建Docker镜像
Write-Host "=== 构建Docker镜像 ===" -ForegroundColor Yellow
try {
    Write-Host "构建测试节点镜像..." -ForegroundColor Cyan
    docker compose -f $DockerComposeFile build --no-cache
    
    if ($LASTEXITCODE -ne 0) {
        throw "Docker镜像构建失败"
    }
    Write-Host "✓ Docker镜像构建成功" -ForegroundColor Green
}
catch {
    Write-Host "✗ Docker镜像构建失败: $_" -ForegroundColor Red
    exit 1
}

# 启动基础设施服务
Write-Host "=== 启动基础设施服务 ===" -ForegroundColor Yellow
try {
    Write-Host "启动NATS集群和MySQL数据库..." -ForegroundColor Cyan
    docker compose -f $DockerComposeFile up -d nats-1 nats-2 nats-3 mysql-test
    
    # 等待基础设施服务就绪
    Write-Host "等待基础设施服务启动..." -ForegroundColor Cyan
    Start-Sleep -Seconds 30
    
    # 检查NATS集群状态
    $natsHealthy = $true
    for ($i = 1; $i -le 3; $i++) {
        try {
            $response = Invoke-RestMethod -Uri "http://localhost:1822$i/healthz" -TimeoutSec 5
            Write-Host "✓ NATS-$i 健康检查通过" -ForegroundColor Green
        }
        catch {
            Write-Host "✗ NATS-$i 健康检查失败" -ForegroundColor Red
            $natsHealthy = $false
        }
    }
    
    if (-not $natsHealthy) {
        throw "NATS集群启动失败"
    }
    
    # 检查MySQL状态
    try {
        $mysqlCheck = docker exec flowcustom-test-mysql mysqladmin ping -h localhost -u root -pTestPassword123!
        Write-Host "✓ MySQL数据库健康检查通过" -ForegroundColor Green
    }
    catch {
        Write-Host "✗ MySQL数据库健康检查失败" -ForegroundColor Red
        throw "MySQL数据库启动失败"
    }
    
    Write-Host "✓ 基础设施服务启动成功" -ForegroundColor Green
}
catch {
    Write-Host "✗ 基础设施服务启动失败: $_" -ForegroundColor Red
    Write-Host "查看服务日志:" -ForegroundColor Yellow
    docker compose -f $DockerComposeFile logs --tail=20
    exit 1
}

# 启动应用节点
Write-Host "=== 启动应用节点集群 ===" -ForegroundColor Yellow
try {
    Write-Host "启动Master节点..." -ForegroundColor Cyan
    docker compose -f $DockerComposeFile up -d master-node-1 master-node-2
    Start-Sleep -Seconds 20
    
    Write-Host "启动Worker节点..." -ForegroundColor Cyan
    docker compose -f $DockerComposeFile up -d worker-node-1 worker-node-2 worker-node-3
    Start-Sleep -Seconds 15
    
    Write-Host "启动Designer和Validator节点..." -ForegroundColor Cyan
    docker compose -f $DockerComposeFile up -d designer-node-1 designer-node-2 validator-node-1 validator-node-2
    Start-Sleep -Seconds 15
    
    Write-Host "✓ 应用节点集群启动成功" -ForegroundColor Green
}
catch {
    Write-Host "✗ 应用节点启动失败: $_" -ForegroundColor Red
    exit 1
}

# 启动监控服务（如果启用）
if ($EnableMonitoring) {
    Write-Host "=== 启动监控服务 ===" -ForegroundColor Yellow
    try {
        docker compose -f $DockerComposeFile --profile monitoring up -d prometheus grafana
        Write-Host "✓ 监控服务启动成功" -ForegroundColor Green
        Write-Host "  Prometheus: http://localhost:19090" -ForegroundColor Cyan
        Write-Host "  Grafana: http://localhost:13000 (admin/admin123)" -ForegroundColor Cyan
    }
    catch {
        Write-Host "⚠ 监控服务启动失败: $_" -ForegroundColor Yellow
    }
}

# 等待集群完全就绪
Write-Host "=== 等待集群就绪 ===" -ForegroundColor Yellow
Write-Host "等待所有节点启动并形成集群..." -ForegroundColor Cyan
Start-Sleep -Seconds 45

# 验证集群状态
Write-Host "验证集群健康状态..." -ForegroundColor Cyan
$clusterHealthy = $true
$nodeEndpoints = @(
    "http://localhost:15001", # master-1
    "http://localhost:15002", # master-2
    "http://localhost:15011", # worker-1
    "http://localhost:15012", # worker-2
    "http://localhost:15013", # worker-3
    "http://localhost:15021", # designer-1
    "http://localhost:15022", # designer-2
    "http://localhost:15031", # validator-1
    "http://localhost:15032"  # validator-2
)

foreach ($endpoint in $nodeEndpoints) {
    $nodeName = $endpoint.Split(':')[-1]
    try {
        $response = Invoke-RestMethod -Uri "$endpoint/health" -TimeoutSec 10
        Write-Host "  ✓ 节点 $nodeName 健康检查通过" -ForegroundColor Green
    }
    catch {
        Write-Host "  ✗ 节点 $nodeName 健康检查失败" -ForegroundColor Red
        $clusterHealthy = $false
    }
}

if (-not $clusterHealthy) {
    Write-Host "✗ 集群健康检查失败，查看容器状态:" -ForegroundColor Red
    docker compose -f $DockerComposeFile ps
    Write-Host "查看失败节点日志:" -ForegroundColor Yellow
    docker compose -f $DockerComposeFile logs --tail=50
    exit 1
}

Write-Host "✓ 集群健康检查通过，开始执行测试" -ForegroundColor Green

# 运行测试协调器
Write-Host "=== 执行分布式测试 ===" -ForegroundColor Yellow
try {
    $testEnvVars = @{
        "RUN_PERFORMANCE_TESTS" = if ($IncludePerformanceTests) { "true" } else { "false" }
        "RUN_RESILIENCE_TESTS" = if ($IncludeResilienceTests) { "true" } else { "false" }
        "TEST_SCOPE" = $TestScope
        "KEEP_ALIVE" = if ($KeepClusterAlive) { "true" } else { "false" }
    }
    
    # 设置环境变量
    foreach ($envVar in $testEnvVars.GetEnumerator()) {
        [Environment]::SetEnvironmentVariable($envVar.Key, $envVar.Value, "Process")
    }
    
    Write-Host "启动测试协调器..." -ForegroundColor Cyan
    docker compose -f $DockerComposeFile --profile test up test-coordinator
    
    $testExitCode = $LASTEXITCODE
    Write-Host "测试协调器执行完成，退出代码: $testExitCode" -ForegroundColor Gray
}
catch {
    Write-Host "✗ 测试执行失败: $_" -ForegroundColor Red
    $testExitCode = 1
}

# 收集测试结果
Write-Host "=== 收集测试结果 ===" -ForegroundColor Yellow
try {
    # 从测试协调器容器复制测试结果
    docker cp flowcustom-test-coordinator:/app/test-results/. $TestResultsPath/
    Write-Host "✓ 测试结果已收集到: $TestResultsPath" -ForegroundColor Green
    
    # 收集容器日志
    $logsPath = "$TestResultsPath/container-logs"
    if (!(Test-Path $logsPath)) {
        New-Item -ItemType Directory -Path $logsPath -Force | Out-Null
    }
    
    Write-Host "收集容器日志..." -ForegroundColor Cyan
    docker compose -f $DockerComposeFile logs --no-color > "$logsPath/all-containers.log"
    
    # 收集各个服务的单独日志
    $services = @("nats-1", "nats-2", "nats-3", "mysql-test", "master-node-1", "master-node-2", 
                  "worker-node-1", "worker-node-2", "worker-node-3", "designer-node-1", 
                  "designer-node-2", "validator-node-1", "validator-node-2")
    
    foreach ($service in $services) {
        docker compose -f $DockerComposeFile logs --no-color $service > "$logsPath/$service.log" 2>$null
    }
    
    Write-Host "✓ 容器日志已收集" -ForegroundColor Green
}
catch {
    Write-Host "⚠ 测试结果收集失败: $_" -ForegroundColor Yellow
}

# 显示测试结果摘要
Write-Host "=== 测试结果摘要 ===" -ForegroundColor Yellow
$testEndTime = Get-Date
$totalDuration = $testEndTime - $TestStartTime

Write-Host "测试完成时间: $($testEndTime.ToString('yyyy-MM-dd HH:mm:ss'))" -ForegroundColor Gray
Write-Host "总耗时: $([math]::Round($totalDuration.TotalMinutes, 2)) 分钟" -ForegroundColor Gray

# 尝试读取测试结果
$reportPath = "$TestResultsPath/comprehensive-test-report.html"
if (Test-Path $reportPath) {
    Write-Host "✓ 详细测试报告: $reportPath" -ForegroundColor Green
}

$verificationPath = "$TestResultsPath/cluster-verification-report.json"
if (Test-Path $verificationPath) {
    Write-Host "✓ 集群验证报告: $verificationPath" -ForegroundColor Green
}

# 清理资源（除非指定保持集群运行）
if (-not $KeepClusterAlive) {
    Write-Host "=== 清理测试环境 ===" -ForegroundColor Yellow
    try {
        docker compose -f $DockerComposeFile down --volumes
        Write-Host "✓ 测试环境已清理" -ForegroundColor Green
    }
    catch {
        Write-Host "⚠ 清理环境时出现警告: $_" -ForegroundColor Yellow
    }
} else {
    Write-Host "=== 集群保持运行 ===" -ForegroundColor Cyan
    Write-Host "集群节点访问地址:" -ForegroundColor White
    Write-Host "  Master-1: http://localhost:15001" -ForegroundColor Cyan
    Write-Host "  Master-2: http://localhost:15002" -ForegroundColor Cyan
    Write-Host "  Worker-1: http://localhost:15011" -ForegroundColor Cyan
    Write-Host "  Worker-2: http://localhost:15012" -ForegroundColor Cyan
    Write-Host "  Worker-3: http://localhost:15013" -ForegroundColor Cyan
    Write-Host "  Designer-1: http://localhost:15021" -ForegroundColor Cyan
    Write-Host "  Designer-2: http://localhost:15022" -ForegroundColor Cyan
    Write-Host "  Validator-1: http://localhost:15031" -ForegroundColor Cyan
    Write-Host "  Validator-2: http://localhost:15032" -ForegroundColor Cyan
    
    if ($EnableMonitoring) {
        Write-Host "监控服务:" -ForegroundColor White
        Write-Host "  Prometheus: http://localhost:19090" -ForegroundColor Cyan
        Write-Host "  Grafana: http://localhost:13000" -ForegroundColor Cyan
    }
    
    Write-Host ""
    Write-Host "使用以下命令停止集群:" -ForegroundColor Yellow
    Write-Host "  docker compose -f $DockerComposeFile down --volumes" -ForegroundColor Gray
}

# 最终结果
Write-Host "================================================================" -ForegroundColor Green
if ($testExitCode -eq 0) {
    Write-Host "🎉 Docker集群测试成功完成！" -ForegroundColor Green
    Write-Host "v0.0.1.7版本在真实分布式环境中表现良好" -ForegroundColor Green
} else {
    Write-Host "❌ Docker集群测试失败" -ForegroundColor Red
    Write-Host "请检查测试结果和日志文件" -ForegroundColor Red
}
Write-Host "================================================================" -ForegroundColor Green

exit $testExitCode
