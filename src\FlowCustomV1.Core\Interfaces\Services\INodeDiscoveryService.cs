using FlowCustomV1.Core.Models.Cluster;
using FlowCustomV1.Core.Models.Messages;

namespace FlowCustomV1.Core.Interfaces.Services;

/// <summary>
/// 节点服务发现接口
/// 提供分布式节点的自动注册、发现和状态管理功能
/// </summary>
public interface INodeDiscoveryService : IDisposable
{
    #region 生命周期管理

    /// <summary>
    /// 启动节点发现服务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>启动任务</returns>
    Task StartAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 停止节点发现服务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>停止任务</returns>
    Task StopAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 服务是否已启动
    /// </summary>
    bool IsStarted { get; }

    #endregion

    #region 节点注册管理

    /// <summary>
    /// 注册当前节点到集群
    /// </summary>
    /// <param name="nodeInfo">节点信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>注册任务</returns>
    Task RegisterNodeAsync(NodeInfo nodeInfo, CancellationToken cancellationToken = default);

    /// <summary>
    /// 注销当前节点
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>注销任务</returns>
    Task UnregisterNodeAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 更新当前节点信息
    /// </summary>
    /// <param name="nodeInfo">更新的节点信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>更新任务</returns>
    Task UpdateNodeInfoAsync(NodeInfo nodeInfo, CancellationToken cancellationToken = default);

    #endregion

    #region 心跳机制

    /// <summary>
    /// 发送心跳消息
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>心跳任务</returns>
    Task SendHeartbeatAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 心跳间隔时间（秒）
    /// </summary>
    int HeartbeatIntervalSeconds { get; set; }

    /// <summary>
    /// 节点超时时间（秒）
    /// </summary>
    int NodeTimeoutSeconds { get; set; }

    #endregion

    #region 服务发现

    /// <summary>
    /// 发现集群中的所有节点
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>节点列表</returns>
    Task<IReadOnlyList<NodeInfo>> DiscoverAllNodesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 根据角色发现节点
    /// </summary>
    /// <param name="role">节点角色</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>节点列表</returns>
    Task<IReadOnlyList<NodeInfo>> DiscoverNodesByRoleAsync(string role, CancellationToken cancellationToken = default);

    /// <summary>
    /// 根据查询条件发现节点
    /// </summary>
    /// <param name="query">查询条件</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>节点列表</returns>
    Task<IReadOnlyList<NodeInfo>> DiscoverNodesAsync(NodeDiscoveryQuery query, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取指定节点信息
    /// </summary>
    /// <param name="nodeId">节点ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>节点信息</returns>
    Task<NodeInfo?> GetNodeAsync(string nodeId, CancellationToken cancellationToken = default);

    #endregion

    #region 节点状态管理

    /// <summary>
    /// 获取当前节点信息
    /// </summary>
    NodeInfo CurrentNode { get; }

    /// <summary>
    /// 获取所有已知节点
    /// </summary>
    IReadOnlyList<NodeInfo> KnownNodes { get; }

    /// <summary>
    /// 获取在线节点数量
    /// </summary>
    int OnlineNodeCount { get; }

    /// <summary>
    /// 更新节点负载信息
    /// </summary>
    /// <param name="nodeId">节点ID</param>
    /// <param name="loadInfo">负载信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>更新任务</returns>
    Task UpdateNodeLoadAsync(string nodeId, NodeLoad loadInfo, CancellationToken cancellationToken = default);

    #endregion

    #region 事件

    /// <summary>
    /// 节点加入集群事件
    /// </summary>
    event EventHandler<NodeJoinedEventArgs>? NodeJoined;

    /// <summary>
    /// 节点离开集群事件
    /// </summary>
    event EventHandler<NodeLeftEventArgs>? NodeLeft;

    /// <summary>
    /// 节点状态变更事件
    /// </summary>
    event EventHandler<NodeStatusChangedEventArgs>? NodeStatusChanged;

    /// <summary>
    /// 心跳接收事件
    /// </summary>
    event EventHandler<HeartbeatReceivedEventArgs>? HeartbeatReceived;

    #endregion
}

#region 事件参数

/// <summary>
/// 节点加入事件参数
/// </summary>
public class NodeJoinedEventArgs : EventArgs
{
    /// <summary>
    /// 加入的节点信息
    /// </summary>
    public required NodeInfo Node { get; init; }

    /// <summary>
    /// 加入时间
    /// </summary>
    public DateTime JoinedAt { get; init; } = DateTime.UtcNow;
}

/// <summary>
/// 节点离开事件参数
/// </summary>
public class NodeLeftEventArgs : EventArgs
{
    /// <summary>
    /// 离开的节点ID
    /// </summary>
    public required string NodeId { get; init; }

    /// <summary>
    /// 离开原因
    /// </summary>
    public string Reason { get; init; } = string.Empty;

    /// <summary>
    /// 离开时间
    /// </summary>
    public DateTime LeftAt { get; init; } = DateTime.UtcNow;
}

/// <summary>
/// 节点状态变更事件参数
/// </summary>
public class NodeStatusChangedEventArgs : EventArgs
{
    /// <summary>
    /// 节点ID
    /// </summary>
    public required string NodeId { get; init; }

    /// <summary>
    /// 旧状态
    /// </summary>
    public required NodeStatus OldStatus { get; init; }

    /// <summary>
    /// 新状态
    /// </summary>
    public required NodeStatus NewStatus { get; init; }

    /// <summary>
    /// 变更时间
    /// </summary>
    public DateTime ChangedAt { get; init; } = DateTime.UtcNow;
}

/// <summary>
/// 心跳接收事件参数
/// </summary>
public class HeartbeatReceivedEventArgs : EventArgs
{
    /// <summary>
    /// 发送心跳的节点ID
    /// </summary>
    public required string NodeId { get; init; }

    /// <summary>
    /// 心跳消息
    /// </summary>
    public required NodeHeartbeatMessage HeartbeatMessage { get; init; }

    /// <summary>
    /// 接收时间
    /// </summary>
    public DateTime ReceivedAt { get; init; } = DateTime.UtcNow;
}

#endregion
