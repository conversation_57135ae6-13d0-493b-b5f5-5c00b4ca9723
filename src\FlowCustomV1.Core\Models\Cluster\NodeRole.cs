namespace FlowCustomV1.Core.Models.Cluster;

/// <summary>
/// 节点功能角色
/// 定义节点在分布式系统中承担的专业化功能角色
/// 支持多角色组合和动态切换
/// </summary>
[Flags]
public enum NodeRole
{
    /// <summary>
    /// 无角色 - 节点未分配任何功能角色
    /// </summary>
    None = 0,

    /// <summary>
    /// 设计器角色 - 工作流设计和编辑
    /// 负责工作流的创建、修改、版本管理和协作设计
    /// </summary>
    Designer = 1 << 0,

    /// <summary>
    /// 验证器角色 - 工作流验证和规则检查
    /// 负责工作流定义验证、规则检查、依赖分析等
    /// </summary>
    Validator = 1 << 1,

    /// <summary>
    /// 执行器角色 - 工作流执行和任务处理
    /// 负责工作流的实际执行、节点任务处理、资源管理等
    /// </summary>
    Executor = 1 << 2,

    /// <summary>
    /// 监控器角色 - 系统监控和指标收集
    /// 负责集群监控、性能指标收集、健康检查等
    /// </summary>
    Monitor = 1 << 3,

    /// <summary>
    /// 网关角色 - API网关和请求路由
    /// 负责外部API接入、请求路由、负载均衡等
    /// </summary>
    Gateway = 1 << 4,

    /// <summary>
    /// 存储角色 - 数据存储和管理
    /// 负责工作流数据存储、状态持久化、数据备份等
    /// </summary>
    Storage = 1 << 5,

    /// <summary>
    /// 调度器角色 - 任务调度和资源分配
    /// 负责任务调度、资源分配、负载均衡决策等
    /// </summary>
    Scheduler = 1 << 6,

    /// <summary>
    /// 全功能角色 - 支持所有功能角色
    /// 适用于小规模部署或开发环境
    /// </summary>
    All = Designer | Validator | Executor | Monitor | Gateway | Storage | Scheduler
}

/// <summary>
/// 节点角色扩展方法
/// </summary>
public static class NodeRoleExtensions
{
    /// <summary>
    /// 检查节点是否具有指定角色
    /// </summary>
    /// <param name="nodeRoles">节点角色</param>
    /// <param name="targetRole">目标角色</param>
    /// <returns>是否具有指定角色</returns>
    public static bool HasRole(this NodeRole nodeRoles, NodeRole targetRole)
    {
        return (nodeRoles & targetRole) == targetRole;
    }

    /// <summary>
    /// 添加角色
    /// </summary>
    /// <param name="nodeRoles">当前角色</param>
    /// <param name="roleToAdd">要添加的角色</param>
    /// <returns>添加角色后的结果</returns>
    public static NodeRole AddRole(this NodeRole nodeRoles, NodeRole roleToAdd)
    {
        return nodeRoles | roleToAdd;
    }

    /// <summary>
    /// 移除角色
    /// </summary>
    /// <param name="nodeRoles">当前角色</param>
    /// <param name="roleToRemove">要移除的角色</param>
    /// <returns>移除角色后的结果</returns>
    public static NodeRole RemoveRole(this NodeRole nodeRoles, NodeRole roleToRemove)
    {
        return nodeRoles & ~roleToRemove;
    }

    /// <summary>
    /// 获取所有激活的角色列表
    /// </summary>
    /// <param name="nodeRoles">节点角色</param>
    /// <returns>激活的角色列表</returns>
    public static IEnumerable<NodeRole> GetActiveRoles(this NodeRole nodeRoles)
    {
        var roles = new List<NodeRole>();
        
        foreach (NodeRole role in Enum.GetValues<NodeRole>())
        {
            if (role != NodeRole.None && role != NodeRole.All && nodeRoles.HasRole(role))
            {
                roles.Add(role);
            }
        }
        
        return roles;
    }

    /// <summary>
    /// 获取角色的显示名称
    /// </summary>
    /// <param name="role">节点角色</param>
    /// <returns>显示名称</returns>
    public static string GetDisplayName(this NodeRole role)
    {
        return role switch
        {
            NodeRole.None => "无角色",
            NodeRole.Designer => "设计器",
            NodeRole.Validator => "验证器",
            NodeRole.Executor => "执行器",
            NodeRole.Monitor => "监控器",
            NodeRole.Gateway => "网关",
            NodeRole.Storage => "存储",
            NodeRole.Scheduler => "调度器",
            NodeRole.All => "全功能",
            _ => role.ToString()
        };
    }

    /// <summary>
    /// 获取角色的描述
    /// </summary>
    /// <param name="role">节点角色</param>
    /// <returns>角色描述</returns>
    public static string GetDescription(this NodeRole role)
    {
        return role switch
        {
            NodeRole.None => "节点未分配任何功能角色",
            NodeRole.Designer => "负责工作流设计、编辑和版本管理",
            NodeRole.Validator => "负责工作流验证、规则检查和依赖分析",
            NodeRole.Executor => "负责工作流执行、任务处理和资源管理",
            NodeRole.Monitor => "负责系统监控、指标收集和健康检查",
            NodeRole.Gateway => "负责API网关、请求路由和负载均衡",
            NodeRole.Storage => "负责数据存储、状态持久化和数据备份",
            NodeRole.Scheduler => "负责任务调度、资源分配和负载均衡决策",
            NodeRole.All => "支持所有功能角色的全能节点",
            _ => "未知角色"
        };
    }

    /// <summary>
    /// 检查角色是否兼容Master-Worker模式
    /// </summary>
    /// <param name="role">节点角色</param>
    /// <returns>是否兼容</returns>
    public static bool IsCompatibleWithMasterWorker(this NodeRole role)
    {
        // Scheduler和Gateway角色类似Master功能
        // Executor角色类似Worker功能
        // 其他角色可以在两种模式下运行
        return true;
    }

    /// <summary>
    /// 将角色转换为字符串列表（用于配置）
    /// </summary>
    /// <param name="nodeRoles">节点角色</param>
    /// <returns>角色字符串列表</returns>
    public static List<string> ToStringList(this NodeRole nodeRoles)
    {
        return nodeRoles.GetActiveRoles().Select(r => r.ToString()).ToList();
    }

    /// <summary>
    /// 从字符串列表解析角色
    /// </summary>
    /// <param name="roleStrings">角色字符串列表</param>
    /// <returns>节点角色</returns>
    public static NodeRole FromStringList(IEnumerable<string> roleStrings)
    {
        var result = NodeRole.None;
        
        foreach (var roleString in roleStrings)
        {
            if (Enum.TryParse<NodeRole>(roleString, true, out var role))
            {
                result = result.AddRole(role);
            }
        }
        
        return result;
    }
}
