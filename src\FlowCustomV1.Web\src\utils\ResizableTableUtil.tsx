import React, { useState, useRef, useEffect } from 'react';

// 可拖拽的表头组件
const ResizableTitle = (props: any) => {
  const { onResize, width, ...restProps } = props;
  const [isResizing, setIsResizing] = useState(false);
  const [startX, setStartX] = useState(0);
  const [startWidth, setStartWidth] = useState(0);
  const thRef = useRef<HTMLTableHeaderCellElement>(null);

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isResizing) return;

      const newWidth = startWidth + (e.clientX - startX);
      if (newWidth > 50) { // 最小宽度限制
        onResize && onResize(newWidth);
      }
    };

    const handleMouseUp = () => {
      setIsResizing(false);
      document.body.style.cursor = '';
      document.body.style.userSelect = '';
    };

    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = 'col-resize';
      document.body.style.userSelect = 'none';
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isResizing, startX, startWidth, onResize]);

  const handleMouseDown = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsResizing(true);
    setStartX(e.clientX);
    setStartWidth(width || 100);
  };

  if (!width) {
    return <th {...restProps} ref={thRef} />;
  }

  return (
    <th
      {...restProps}
      ref={thRef}
      style={{
        ...restProps.style,
        position: 'relative',
        width: width
      }}
    >
      {restProps.children}
      <div
        style={{
          position: 'absolute',
          right: 0,
          top: 0,
          bottom: 0,
          width: '5px',
          cursor: 'col-resize',
          backgroundColor: 'transparent',
          zIndex: 1,
        }}
        onMouseDown={handleMouseDown}
      />
    </th>
  );
};

export const components = {
  header: {
    cell: ResizableTitle,
  },
};

export const getMergeColumns = (columns: any[], setColumns: (columns: any[]) => void) => {
  const mergeColumns = columns.map((col, index) => ({
    ...col,
    onHeaderCell: (column: any) => ({
      width: column.width,
      onResize: (newWidth: number) => {
        const newColumns = [...columns];
        newColumns[index] = {
          ...newColumns[index],
          width: newWidth,
        };
        setColumns(newColumns);
      },
    }),
  }));
  return mergeColumns;
};
