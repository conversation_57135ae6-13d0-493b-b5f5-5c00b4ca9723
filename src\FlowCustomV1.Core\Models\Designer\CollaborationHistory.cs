using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace FlowCustomV1.Core.Models.Designer;

/// <summary>
/// 协作历史条目
/// </summary>
public class CollaborationHistoryEntry
{
    /// <summary>
    /// 条目唯一标识符
    /// </summary>
    [JsonPropertyName("entryId")]
    public string EntryId { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 会话ID
    /// </summary>
    [Required]
    [JsonPropertyName("sessionId")]
    public string SessionId { get; set; } = string.Empty;

    /// <summary>
    /// 协作者ID
    /// </summary>
    [JsonPropertyName("collaboratorId")]
    public string CollaboratorId { get; set; } = string.Empty;

    /// <summary>
    /// 协作者名称
    /// </summary>
    [JsonPropertyName("collaboratorName")]
    public string CollaboratorName { get; set; } = string.Empty;

    /// <summary>
    /// 活动类型
    /// </summary>
    [JsonPropertyName("activityType")]
    public CollaborationActivityType ActivityType { get; set; }

    /// <summary>
    /// 活动描述
    /// </summary>
    [JsonPropertyName("description")]
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 相关的设计操作
    /// </summary>
    [JsonPropertyName("operation")]
    public DesignOperation? Operation { get; set; }

    /// <summary>
    /// 活动时间
    /// </summary>
    [JsonPropertyName("timestamp")]
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 活动数据
    /// </summary>
    [JsonPropertyName("data")]
    public Dictionary<string, object> Data { get; set; } = new();

    /// <summary>
    /// IP地址
    /// </summary>
    [JsonPropertyName("ipAddress")]
    public string IpAddress { get; set; } = string.Empty;

    /// <summary>
    /// 用户代理
    /// </summary>
    [JsonPropertyName("userAgent")]
    public string UserAgent { get; set; } = string.Empty;
}

/// <summary>
/// 协作历史查询条件
/// </summary>
public class CollaborationHistoryQuery
{
    /// <summary>
    /// 协作者ID过滤
    /// </summary>
    [JsonPropertyName("collaboratorId")]
    public string? CollaboratorId { get; set; }

    /// <summary>
    /// 活动类型过滤
    /// </summary>
    [JsonPropertyName("activityTypes")]
    public List<CollaborationActivityType>? ActivityTypes { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    [JsonPropertyName("startTime")]
    public DateTime? StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    [JsonPropertyName("endTime")]
    public DateTime? EndTime { get; set; }

    /// <summary>
    /// 分页大小
    /// </summary>
    [JsonPropertyName("pageSize")]
    public int PageSize { get; set; } = 50;

    /// <summary>
    /// 页码（从1开始）
    /// </summary>
    [JsonPropertyName("pageNumber")]
    public int PageNumber { get; set; } = 1;

    /// <summary>
    /// 排序方向
    /// </summary>
    [JsonPropertyName("sortDirection")]
    public SortDirection SortDirection { get; set; } = SortDirection.Descending;
}

/// <summary>
/// 协作活动
/// </summary>
public class CollaborationActivity
{
    /// <summary>
    /// 活动ID
    /// </summary>
    [JsonPropertyName("activityId")]
    public string ActivityId { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 协作者ID
    /// </summary>
    [Required]
    [JsonPropertyName("collaboratorId")]
    public string CollaboratorId { get; set; } = string.Empty;

    /// <summary>
    /// 协作者名称
    /// </summary>
    [JsonPropertyName("collaboratorName")]
    public string CollaboratorName { get; set; } = string.Empty;

    /// <summary>
    /// 活动类型
    /// </summary>
    [JsonPropertyName("activityType")]
    public CollaborationActivityType ActivityType { get; set; }

    /// <summary>
    /// 活动描述
    /// </summary>
    [JsonPropertyName("description")]
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 相关的设计操作
    /// </summary>
    [JsonPropertyName("operation")]
    public DesignOperation? Operation { get; set; }

    /// <summary>
    /// 活动时间
    /// </summary>
    [JsonPropertyName("timestamp")]
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 活动数据
    /// </summary>
    [JsonPropertyName("data")]
    public Dictionary<string, object> Data { get; set; } = new();

    /// <summary>
    /// 客户端信息
    /// </summary>
    [JsonPropertyName("clientInfo")]
    public ClientInfo? ClientInfo { get; set; }
}

/// <summary>
/// 协作活动类型枚举
/// </summary>
public enum CollaborationActivityType
{
    /// <summary>
    /// 加入会话
    /// </summary>
    JoinSession,

    /// <summary>
    /// 离开会话
    /// </summary>
    LeaveSession,

    /// <summary>
    /// 设计操作
    /// </summary>
    DesignOperation,

    /// <summary>
    /// 状态更新
    /// </summary>
    StatusUpdate,

    /// <summary>
    /// 光标移动
    /// </summary>
    CursorMove,

    /// <summary>
    /// 选择变更
    /// </summary>
    SelectionChange,

    /// <summary>
    /// 冲突检测
    /// </summary>
    ConflictDetected,

    /// <summary>
    /// 冲突解决
    /// </summary>
    ConflictResolved,

    /// <summary>
    /// 权限变更
    /// </summary>
    PermissionChange,

    /// <summary>
    /// 角色变更
    /// </summary>
    RoleChange,

    /// <summary>
    /// 聊天消息
    /// </summary>
    ChatMessage,

    /// <summary>
    /// 文件上传
    /// </summary>
    FileUpload,

    /// <summary>
    /// 会话暂停
    /// </summary>
    SessionPaused,

    /// <summary>
    /// 会话恢复
    /// </summary>
    SessionResumed,

    /// <summary>
    /// 会话结束
    /// </summary>
    SessionEnded
}

/// <summary>
/// 客户端信息
/// </summary>
public class ClientInfo
{
    /// <summary>
    /// IP地址
    /// </summary>
    [JsonPropertyName("ipAddress")]
    public string IpAddress { get; set; } = string.Empty;

    /// <summary>
    /// 用户代理
    /// </summary>
    [JsonPropertyName("userAgent")]
    public string UserAgent { get; set; } = string.Empty;

    /// <summary>
    /// 浏览器信息
    /// </summary>
    [JsonPropertyName("browser")]
    public string Browser { get; set; } = string.Empty;

    /// <summary>
    /// 操作系统信息
    /// </summary>
    [JsonPropertyName("operatingSystem")]
    public string OperatingSystem { get; set; } = string.Empty;

    /// <summary>
    /// 设备类型
    /// </summary>
    [JsonPropertyName("deviceType")]
    public string DeviceType { get; set; } = string.Empty;

    /// <summary>
    /// 屏幕分辨率
    /// </summary>
    [JsonPropertyName("screenResolution")]
    public string ScreenResolution { get; set; } = string.Empty;

    /// <summary>
    /// 时区
    /// </summary>
    [JsonPropertyName("timeZone")]
    public string TimeZone { get; set; } = string.Empty;
}

/// <summary>
/// 协作统计信息
/// </summary>
public class CollaborationStatistics
{
    /// <summary>
    /// 会话ID
    /// </summary>
    [JsonPropertyName("sessionId")]
    public string SessionId { get; set; } = string.Empty;

    /// <summary>
    /// 总协作者数量
    /// </summary>
    [JsonPropertyName("totalCollaborators")]
    public int TotalCollaborators { get; set; } = 0;

    /// <summary>
    /// 当前活跃协作者数量
    /// </summary>
    [JsonPropertyName("activeCollaborators")]
    public int ActiveCollaborators { get; set; } = 0;

    /// <summary>
    /// 总操作数量
    /// </summary>
    [JsonPropertyName("totalOperations")]
    public int TotalOperations { get; set; } = 0;

    /// <summary>
    /// 总冲突数量
    /// </summary>
    [JsonPropertyName("totalConflicts")]
    public int TotalConflicts { get; set; } = 0;

    /// <summary>
    /// 已解决冲突数量
    /// </summary>
    [JsonPropertyName("resolvedConflicts")]
    public int ResolvedConflicts { get; set; } = 0;

    /// <summary>
    /// 会话持续时间（分钟）
    /// </summary>
    [JsonPropertyName("sessionDurationMinutes")]
    public double SessionDurationMinutes { get; set; } = 0;

    /// <summary>
    /// 平均响应时间（毫秒）
    /// </summary>
    [JsonPropertyName("averageResponseTimeMs")]
    public double AverageResponseTimeMs { get; set; } = 0;

    /// <summary>
    /// 按协作者统计的操作数量
    /// </summary>
    [JsonPropertyName("operationsByCollaborator")]
    public Dictionary<string, int> OperationsByCollaborator { get; set; } = new();

    /// <summary>
    /// 按操作类型统计的数量
    /// </summary>
    [JsonPropertyName("operationsByType")]
    public Dictionary<string, int> OperationsByType { get; set; } = new();

    /// <summary>
    /// 每小时活动统计
    /// </summary>
    [JsonPropertyName("hourlyActivity")]
    public Dictionary<string, int> HourlyActivity { get; set; } = new();

    /// <summary>
    /// 协作效率评分（0-100）
    /// </summary>
    [JsonPropertyName("collaborationEfficiencyScore")]
    public double CollaborationEfficiencyScore { get; set; } = 0;

    /// <summary>
    /// 统计生成时间
    /// </summary>
    [JsonPropertyName("generatedAt")]
    public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
}
