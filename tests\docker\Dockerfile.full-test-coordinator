# FlowCustomV1 v0.0.1.7 全面功能测试协调器
# 负责执行所有功能的完整测试套件

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS base

# 安装必要的工具
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    jq \
    python3 \
    python3-pip \
    python3-venv \
    netcat-openbsd \
    mysql-client \
    redis-tools \
    && rm -rf /var/lib/apt/lists/*

# 安装Python测试依赖
RUN python3 -m pip install --upgrade pip
RUN pip3 install \
    requests \
    pytest \
    pytest-html \
    pytest-json-report \
    pytest-xdist \
    aiohttp \
    asyncio \
    mysql-connector-python \
    redis \
    nats-py \
    pydantic \
    rich \
    typer

WORKDIR /app

# 复制测试脚本和配置
COPY ["tests/docker/test-scripts/", "./test-scripts/"]
COPY ["tests/docker/test-data/", "./test-data/"]
COPY ["tests/docker/config/", "./config/"]

# 复制.NET测试项目
COPY ["tests/FlowCustomV1.Tests/", "./dotnet-tests/FlowCustomV1.Tests/"]
COPY ["tests/FlowCustomV1.Integration.Tests/", "./dotnet-tests/FlowCustomV1.Integration.Tests/"]
COPY ["tests/FlowCustomV1.Api.Tests/", "./dotnet-tests/FlowCustomV1.Api.Tests/"]
COPY ["tests/FlowCustomV1.Core.Tests/", "./dotnet-tests/FlowCustomV1.Core.Tests/"]
COPY ["tests/FlowCustomV1.Engine.Tests/", "./dotnet-tests/FlowCustomV1.Engine.Tests/"]

# 复制源代码（用于集成测试）
COPY ["src/", "./src/"]

# 设置权限
RUN chmod +x ./test-scripts/*.sh
RUN chmod +x ./test-scripts/*.py

# 创建测试结果目录
RUN mkdir -p /app/test-results /app/logs /app/reports

# 环境变量
ENV PYTHONPATH=/app/test-scripts
ENV DOTNET_ENVIRONMENT=FullTest
ENV TEST_PARALLEL_WORKERS=4
ENV TEST_TIMEOUT_MINUTES=60

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# 启动脚本
COPY ["tests/docker/scripts/start-full-test-coordinator.sh", "./start-full-test-coordinator.sh"]
RUN chmod +x ./start-full-test-coordinator.sh

ENTRYPOINT ["./start-full-test-coordinator.sh"]
