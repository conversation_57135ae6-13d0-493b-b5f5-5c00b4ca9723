using Microsoft.AspNetCore.Mvc;
using FlowCustomV1.Core.Interfaces.Validator;
using FlowCustomV1.Core.Models.Workflow;
using FlowCustomV1.Api.Models.Requests;
using FlowCustomV1.Api.Models.Responses;
using System.ComponentModel.DataAnnotations;
using CoreNodeValidationResult = FlowCustomV1.Core.Models.Workflow.NodeValidationResult;
using CoreBatchValidationResult = FlowCustomV1.Core.Interfaces.Validator.BatchValidationResult;
using CoreCyclicDependencyResult = FlowCustomV1.Core.Interfaces.Validator.CyclicDependencyResult;
using CorePerformanceAnalysisResult = FlowCustomV1.Core.Interfaces.Validator.PerformanceAnalysisResult;

namespace FlowCustomV1.Api.Controllers;

/// <summary>
/// 工作流验证器API控制器
/// 提供分布式工作流验证服务的REST API接口
/// </summary>
[ApiController]
[Route("api/v1/[controller]")]
[Produces("application/json")]
public class ValidatorController : ControllerBase
{
    private readonly IWorkflowValidatorService _validatorService;
    private readonly IDistributedValidationCache _validationCache;
    private readonly IDistributedValidationRuleEngine _ruleEngine;
    private readonly ILogger<ValidatorController> _logger;

    /// <summary>
    /// 构造函数
    /// </summary>
    public ValidatorController(
        IWorkflowValidatorService validatorService,
        IDistributedValidationCache validationCache,
        IDistributedValidationRuleEngine ruleEngine,
        ILogger<ValidatorController> logger)
    {
        _validatorService = validatorService ?? throw new ArgumentNullException(nameof(validatorService));
        _validationCache = validationCache ?? throw new ArgumentNullException(nameof(validationCache));
        _ruleEngine = ruleEngine ?? throw new ArgumentNullException(nameof(ruleEngine));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    #region 工作流验证

    /// <summary>
    /// 验证工作流定义
    /// </summary>
    /// <param name="request">验证请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    [HttpPost("validate/workflow")]
    [ProducesResponseType(typeof(ApiResponse<WorkflowValidationResult>), 200)]
    [ProducesResponseType(typeof(ApiResponse), 400)]
    [ProducesResponseType(typeof(ApiResponse), 500)]
    public async Task<ActionResult<ApiResponse<WorkflowValidationResult>>> ValidateWorkflowAsync(
        [FromBody] ValidateWorkflowRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Validating workflow: {WorkflowId}", request.WorkflowDefinition.WorkflowId);

            var result = await _validatorService.ValidateWorkflowAsync(request.WorkflowDefinition, cancellationToken);

            return Ok(ApiResponse<WorkflowValidationResult>.CreateSuccess(result, "Workflow validation completed"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating workflow: {WorkflowId}", request.WorkflowDefinition.WorkflowId);
            return StatusCode(500, ApiResponse.Error("Internal server error during workflow validation"));
        }
    }

    /// <summary>
    /// 验证节点配置
    /// </summary>
    /// <param name="request">节点验证请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    [HttpPost("validate/node")]
    [ProducesResponseType(typeof(ApiResponse<CoreNodeValidationResult>), 200)]
    [ProducesResponseType(typeof(ApiResponse), 400)]
    [ProducesResponseType(typeof(ApiResponse), 500)]
    public async Task<ActionResult<ApiResponse<CoreNodeValidationResult>>> ValidateNodeAsync(
        [FromBody] ValidateNodeRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Validating node: {NodeType}", request.NodeDefinition.NodeType);

            var result = await _validatorService.ValidateNodeAsync(request.NodeDefinition, cancellationToken);

            return Ok(ApiResponse<CoreNodeValidationResult>.CreateSuccess(result, "Node validation completed"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating node: {NodeType}", request.NodeDefinition.NodeType);
            return StatusCode(500, ApiResponse.Error("Internal server error during node validation"));
        }
    }

    /// <summary>
    /// 批量验证工作流
    /// </summary>
    /// <param name="request">批量验证请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>批量验证结果</returns>
    [HttpPost("validate/batch")]
    [ProducesResponseType(typeof(ApiResponse<BatchValidationResult>), 200)]
    [ProducesResponseType(typeof(ApiResponse), 400)]
    [ProducesResponseType(typeof(ApiResponse), 500)]
    public async Task<ActionResult<ApiResponse<BatchValidationResult>>> ValidateBatchAsync(
        [FromBody] ValidateBatchRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Batch validating {Count} workflows", request.WorkflowDefinitions.Count);

            var result = await _validatorService.ValidateBatchAsync(request.WorkflowDefinitions, cancellationToken);

            return Ok(ApiResponse<BatchValidationResult>.CreateSuccess(result, "Batch validation completed"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in batch validation");
            return StatusCode(500, ApiResponse.Error("Internal server error during batch validation"));
        }
    }

    /// <summary>
    /// 检查循环依赖
    /// </summary>
    /// <param name="request">循环依赖检查请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>循环依赖检查结果</returns>
    [HttpPost("validate/cyclic-dependency")]
    [ProducesResponseType(typeof(ApiResponse<CyclicDependencyResult>), 200)]
    [ProducesResponseType(typeof(ApiResponse), 400)]
    [ProducesResponseType(typeof(ApiResponse), 500)]
    public async Task<ActionResult<ApiResponse<CyclicDependencyResult>>> CheckCyclicDependencyAsync(
        [FromBody] ValidateWorkflowRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Checking cyclic dependencies for workflow: {WorkflowId}", 
                request.WorkflowDefinition.WorkflowId);

            var result = await _validatorService.CheckCyclicDependencyAsync(request.WorkflowDefinition, cancellationToken);

            return Ok(ApiResponse<CyclicDependencyResult>.CreateSuccess(result, "Cyclic dependency check completed"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking cyclic dependencies for workflow: {WorkflowId}", 
                request.WorkflowDefinition.WorkflowId);
            return StatusCode(500, ApiResponse.Error("Internal server error during cyclic dependency check"));
        }
    }

    /// <summary>
    /// 性能分析
    /// </summary>
    /// <param name="request">性能分析请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>性能分析结果</returns>
    [HttpPost("analyze/performance")]
    [ProducesResponseType(typeof(ApiResponse<PerformanceAnalysisResult>), 200)]
    [ProducesResponseType(typeof(ApiResponse), 400)]
    [ProducesResponseType(typeof(ApiResponse), 500)]
    public async Task<ActionResult<ApiResponse<PerformanceAnalysisResult>>> AnalyzePerformanceAsync(
        [FromBody] ValidateWorkflowRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Analyzing performance for workflow: {WorkflowId}", 
                request.WorkflowDefinition.WorkflowId);

            var result = await _validatorService.AnalyzePerformanceAsync(request.WorkflowDefinition, cancellationToken);

            return Ok(ApiResponse<PerformanceAnalysisResult>.CreateSuccess(result, "Performance analysis completed"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error analyzing performance for workflow: {WorkflowId}", 
                request.WorkflowDefinition.WorkflowId);
            return StatusCode(500, ApiResponse.Error("Internal server error during performance analysis"));
        }
    }

    #endregion

    #region 缓存管理

    /// <summary>
    /// 获取缓存的验证结果
    /// </summary>
    /// <param name="cacheKey">缓存键</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>缓存的验证结果</returns>
    [HttpGet("cache/{cacheKey}")]
    [ProducesResponseType(typeof(ApiResponse<WorkflowValidationResult>), 200)]
    [ProducesResponseType(typeof(ApiResponse), 404)]
    [ProducesResponseType(typeof(ApiResponse), 500)]
    public async Task<ActionResult<ApiResponse<WorkflowValidationResult>>> GetCachedValidationAsync(
        [FromRoute] string cacheKey,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var result = await _validationCache.GetAsync(cacheKey, cancellationToken);
            
            if (result == null)
            {
                return NotFound(ApiResponse.Error("Cached validation result not found"));
            }

            return Ok(ApiResponse<WorkflowValidationResult>.CreateSuccess(result, "Cached validation result retrieved"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving cached validation result: {CacheKey}", cacheKey);
            return StatusCode(500, ApiResponse.Error("Internal server error retrieving cached result"));
        }
    }

    /// <summary>
    /// 删除缓存项
    /// </summary>
    /// <param name="cacheKey">缓存键</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>删除结果</returns>
    [HttpDelete("cache/{cacheKey}")]
    [ProducesResponseType(typeof(ApiResponse<bool>), 200)]
    [ProducesResponseType(typeof(ApiResponse), 500)]
    public async Task<ActionResult<ApiResponse<bool>>> RemoveCachedValidationAsync(
        [FromRoute] string cacheKey,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var result = await _validationCache.RemoveAsync(cacheKey, cancellationToken);
            
            return Ok(ApiResponse<bool>.CreateSuccess(result,
                result ? "Cache item removed successfully" : "Cache item not found"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing cached validation result: {CacheKey}", cacheKey);
            return StatusCode(500, ApiResponse.Error("Internal server error removing cached result"));
        }
    }

    /// <summary>
    /// 获取缓存统计信息
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>缓存统计</returns>
    [HttpGet("cache/statistics")]
    [ProducesResponseType(typeof(ApiResponse<CacheStatistics>), 200)]
    [ProducesResponseType(typeof(ApiResponse), 500)]
    public async Task<ActionResult<ApiResponse<CacheStatistics>>> GetCacheStatisticsAsync(
        CancellationToken cancellationToken = default)
    {
        try
        {
            var statistics = await _validationCache.GetStatisticsAsync(cancellationToken);
            
            return Ok(ApiResponse<CacheStatistics>.CreateSuccess(statistics, "Cache statistics retrieved"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving cache statistics");
            return StatusCode(500, ApiResponse.Error("Internal server error retrieving cache statistics"));
        }
    }

    /// <summary>
    /// 清空缓存
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>清空结果</returns>
    [HttpDelete("cache")]
    [ProducesResponseType(typeof(ApiResponse), 200)]
    [ProducesResponseType(typeof(ApiResponse), 500)]
    public async Task<ActionResult<ApiResponse>> ClearCacheAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            await _validationCache.ClearAsync(cancellationToken);
            
            return Ok(ApiResponse.Ok("Cache cleared successfully"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error clearing cache");
            return StatusCode(500, ApiResponse.Error("Internal server error clearing cache"));
        }
    }

    #endregion

    #region 验证规则管理

    /// <summary>
    /// 获取所有验证规则
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证规则列表</returns>
    [HttpGet("rules")]
    [ProducesResponseType(typeof(ApiResponse<List<ValidationRule>>), 200)]
    [ProducesResponseType(typeof(ApiResponse), 500)]
    public async Task<ActionResult<ApiResponse<List<ValidationRule>>>> GetValidationRulesAsync(
        CancellationToken cancellationToken = default)
    {
        try
        {
            var rules = await _ruleEngine.GetAllRulesAsync(cancellationToken);

            // 转换为ValidationRule列表
            var validationRules = rules.Select(r => new ValidationRule
            {
                RuleId = r.RuleId,
                Name = r.RuleName,
                Description = r.Description,
                IsEnabled = r.IsEnabled,
                CreatedAt = r.CreatedAt,
                UpdatedAt = r.UpdatedAt,
                CreatedBy = r.CreatedBy,
                Configuration = r.Configuration,
                SupportedNodeTypes = r.SupportedNodeTypes,
                Priority = r.Priority,
                Version = r.Version
            }).ToList();

            return Ok(ApiResponse<List<ValidationRule>>.CreateSuccess(validationRules, "Validation rules retrieved"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving validation rules");
            return StatusCode(500, ApiResponse.Error("Internal server error retrieving validation rules"));
        }
    }

    /// <summary>
    /// 添加验证规则
    /// </summary>
    /// <param name="request">添加规则请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>添加结果</returns>
    [HttpPost("rules")]
    [ProducesResponseType(typeof(ApiResponse), 201)]
    [ProducesResponseType(typeof(ApiResponse), 400)]
    [ProducesResponseType(typeof(ApiResponse), 500)]
    public async Task<ActionResult<ApiResponse>> AddValidationRuleAsync(
        [FromBody] AddValidationRuleRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            await _ruleEngine.AddRuleAsync(request.Rule, cancellationToken);

            return CreatedAtAction(nameof(GetValidationRulesAsync),
                ApiResponse.Ok("Validation rule added successfully"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding validation rule: {RuleId}", request.Rule.RuleId);
            return StatusCode(500, ApiResponse.Error("Internal server error adding validation rule"));
        }
    }

    /// <summary>
    /// 更新验证规则
    /// </summary>
    /// <param name="ruleId">规则ID</param>
    /// <param name="request">更新规则请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>更新结果</returns>
    [HttpPut("rules/{ruleId}")]
    [ProducesResponseType(typeof(ApiResponse), 200)]
    [ProducesResponseType(typeof(ApiResponse), 404)]
    [ProducesResponseType(typeof(ApiResponse), 500)]
    public async Task<ActionResult<ApiResponse>> UpdateValidationRuleAsync(
        [FromRoute] string ruleId,
        [FromBody] UpdateValidationRuleRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var success = await _ruleEngine.UpdateRuleAsync(ruleId, request.Rule, cancellationToken);

            if (!success)
            {
                return NotFound(ApiResponse.Error("Validation rule not found"));
            }

            return Ok(ApiResponse.Ok("Validation rule updated successfully"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating validation rule: {RuleId}", ruleId);
            return StatusCode(500, ApiResponse.Error("Internal server error updating validation rule"));
        }
    }

    /// <summary>
    /// 删除验证规则
    /// </summary>
    /// <param name="ruleId">规则ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>删除结果</returns>
    [HttpDelete("rules/{ruleId}")]
    [ProducesResponseType(typeof(ApiResponse), 200)]
    [ProducesResponseType(typeof(ApiResponse), 404)]
    [ProducesResponseType(typeof(ApiResponse), 500)]
    public async Task<ActionResult<ApiResponse>> RemoveValidationRuleAsync(
        [FromRoute] string ruleId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var success = await _ruleEngine.RemoveRuleAsync(ruleId, cancellationToken);

            if (!success)
            {
                return NotFound(ApiResponse.Error("Validation rule not found"));
            }

            return Ok(ApiResponse.Ok("Validation rule removed successfully"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing validation rule: {RuleId}", ruleId);
            return StatusCode(500, ApiResponse.Error("Internal server error removing validation rule"));
        }
    }

    /// <summary>
    /// 启用/禁用验证规则
    /// </summary>
    /// <param name="ruleId">规则ID</param>
    /// <param name="request">启用/禁用请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    [HttpPatch("rules/{ruleId}/enabled")]
    [ProducesResponseType(typeof(ApiResponse), 200)]
    [ProducesResponseType(typeof(ApiResponse), 404)]
    [ProducesResponseType(typeof(ApiResponse), 500)]
    public async Task<ActionResult<ApiResponse>> SetRuleEnabledAsync(
        [FromRoute] string ruleId,
        [FromBody] SetRuleEnabledRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var success = await _ruleEngine.SetRuleEnabledAsync(ruleId, request.Enabled, cancellationToken);

            if (!success)
            {
                return NotFound(ApiResponse.Error("Validation rule not found"));
            }

            var action = request.Enabled ? "enabled" : "disabled";
            return Ok(ApiResponse.Ok($"Validation rule {action} successfully"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting rule enabled state: {RuleId}", ruleId);
            return StatusCode(500, ApiResponse.Error("Internal server error setting rule enabled state"));
        }
    }

    #endregion

    #region 健康检查

    /// <summary>
    /// 验证器健康检查
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>健康状态</returns>
    [HttpGet("health")]
    [ProducesResponseType(typeof(ApiResponse<ValidatorHealthStatus>), 200)]
    [ProducesResponseType(typeof(ApiResponse), 500)]
    public async Task<ActionResult<ApiResponse<ValidatorHealthStatus>>> GetHealthAsync(
        CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheStats = await _validationCache.GetStatisticsAsync(cancellationToken);
            var ruleCount = (await _ruleEngine.GetAllRulesAsync(cancellationToken)).Count();

            var healthStatus = new ValidatorHealthStatus
            {
                IsHealthy = true,
                Timestamp = DateTime.UtcNow,
                CacheStatistics = cacheStats,
                ActiveRuleCount = ruleCount,
                Version = "v0.0.1.5"
            };

            return Ok(ApiResponse<ValidatorHealthStatus>.CreateSuccess(healthStatus, "Validator health check completed"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during health check");

            var healthStatus = new ValidatorHealthStatus
            {
                IsHealthy = false,
                Timestamp = DateTime.UtcNow,
                ErrorMessage = ex.Message,
                Version = "v0.0.1.5"
            };

            return StatusCode(500, ApiResponse<ValidatorHealthStatus>.Error("Health check failed", healthStatus));
        }
    }

    #endregion
}
