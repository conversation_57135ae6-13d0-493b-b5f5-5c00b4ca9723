using FlowCustomV1.Core.Models.Common;
using FlowCustomV1.Core.Models.Executor;
using FlowCustomV1.Core.Models.Workflow;

namespace FlowCustomV1.Core.Interfaces.Repositories;

/// <summary>
/// 执行仓储接口（统一版本）
/// 负责工作流执行相关数据的持久化操作
/// 合并了原有的两个IExecutionRepository接口
/// </summary>
public interface IExecutionRepository
{
    /// <summary>
    /// 保存执行结果
    /// </summary>
    /// <param name="executionResult">执行结果</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>保存任务</returns>
    Task SaveExecutionResultAsync(WorkflowExecutionResult executionResult, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取执行结果
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>执行结果</returns>
    Task<WorkflowExecutionResult?> GetExecutionResultAsync(string executionId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取执行历史
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="pageIndex">页索引</param>
    /// <param name="pageSize">页大小</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>执行历史分页结果</returns>
    Task<PagedResult<ExecutionRecord>> GetExecutionHistoryAsync(string workflowId, int pageIndex, int pageSize, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取执行历史（返回WorkflowExecutionResult）
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="pageIndex">页索引</param>
    /// <param name="pageSize">页大小</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>执行历史分页结果</returns>
    Task<PagedResult<WorkflowExecutionResult>> GetExecutionHistoryResultsAsync(string workflowId, int pageIndex, int pageSize, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取正在运行的执行列表
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>正在运行的执行列表</returns>
    Task<IReadOnlyList<ExecutionRecord>> GetRunningExecutionsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 保存执行状态
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="state">执行状态</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>保存任务</returns>
    Task SaveExecutionStateAsync(string executionId, WorkflowExecutionState state, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取执行状态
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>执行状态</returns>
    Task<WorkflowExecutionState?> GetExecutionStateAsync(string executionId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 保存执行上下文
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="context">执行上下文</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>保存任务</returns>
    Task SaveExecutionContextAsync(string executionId, ExecutionContextSnapshot context, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取执行上下文
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>执行上下文</returns>
    Task<ExecutionContextSnapshot?> GetExecutionContextAsync(string executionId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 删除执行记录
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>删除任务</returns>
    Task DeleteExecutionAsync(string executionId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 批量删除执行记录
    /// </summary>
    /// <param name="executionIds">执行ID列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>删除任务</returns>
    Task DeleteExecutionsAsync(IEnumerable<string> executionIds, CancellationToken cancellationToken = default);

    /// <summary>
    /// 清理过期的执行记录
    /// </summary>
    /// <param name="expirationTime">过期时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>清理的记录数</returns>
    Task<int> CleanupExpiredExecutionsAsync(TimeSpan expirationTime, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取执行统计信息
    /// </summary>
    /// <param name="workflowId">工作流ID（可选）</param>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>执行统计</returns>
    Task<ExecutionStatistics> GetExecutionStatisticsAsync(string? workflowId, DateTime startTime, DateTime endTime, CancellationToken cancellationToken = default);

    // ========== 以下方法来自原FlowCustomV1.Core.Interfaces.IExecutionRepository ==========

    /// <summary>
    /// 保存工作流实例
    /// </summary>
    /// <param name="instance">工作流实例数据</param>
    /// <param name="inputData">输入数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>实例ID</returns>
    Task<string> SaveWorkflowInstanceAsync(WorkflowExecutionResult instance, Dictionary<string, object>? inputData = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 更新工作流实例
    /// </summary>
    /// <param name="instance">工作流实例数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>更新是否成功</returns>
    Task<bool> UpdateWorkflowInstanceAsync(WorkflowExecutionResult instance, CancellationToken cancellationToken = default);

    /// <summary>
    /// 根据实例ID获取工作流实例
    /// </summary>
    /// <param name="instanceId">实例ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>工作流实例</returns>
    Task<WorkflowExecutionResult?> GetWorkflowInstanceAsync(string instanceId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 保存节点执行记录
    /// </summary>
    /// <param name="execution">节点执行记录</param>
    /// <param name="instanceId">工作流实例ID</param>
    /// <param name="nodeType">节点类型</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>保存是否成功</returns>
    Task<bool> SaveNodeExecutionAsync(NodeExecutionResult execution, string instanceId, string nodeType, CancellationToken cancellationToken = default);

    /// <summary>
    /// 更新节点执行记录
    /// </summary>
    /// <param name="execution">节点执行记录</param>
    /// <param name="instanceId">工作流实例ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>更新是否成功</returns>
    Task<bool> UpdateNodeExecutionAsync(NodeExecutionResult execution, string instanceId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取工作流实例的所有节点执行记录
    /// </summary>
    /// <param name="instanceId">工作流实例ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>节点执行记录列表</returns>
    Task<IEnumerable<NodeExecutionResult>> GetNodeExecutionsAsync(string instanceId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取指定节点的执行记录
    /// </summary>
    /// <param name="instanceId">工作流实例ID</param>
    /// <param name="nodeId">节点ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>节点执行记录</returns>
    Task<NodeExecutionResult?> GetNodeExecutionAsync(string instanceId, string nodeId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 删除工作流实例及其相关数据
    /// </summary>
    /// <param name="instanceId">实例ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>删除是否成功</returns>
    Task<bool> DeleteWorkflowInstanceAsync(string instanceId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 搜索执行记录
    /// </summary>
    /// <param name="criteria">搜索条件</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>搜索结果</returns>
    Task<IEnumerable<WorkflowExecutionResult>> SearchExecutionsAsync(ExecutionSearchCriteria criteria, CancellationToken cancellationToken = default);

    /// <summary>
    /// 搜索执行记录
    /// </summary>
    /// <param name="query">查询条件</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>搜索结果</returns>
    Task<PagedResult<ExecutionRecord>> SearchExecutionsAsync(ExecutionSearchQuery query, CancellationToken cancellationToken = default);
}

/// <summary>
/// 执行记录
/// </summary>
public class ExecutionRecord
{
    /// <summary>
    /// 执行ID
    /// </summary>
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 工作流ID
    /// </summary>
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 工作流名称
    /// </summary>
    public string? WorkflowName { get; set; }

    /// <summary>
    /// 执行状态
    /// </summary>
    public WorkflowExecutionState State { get; set; }

    /// <summary>
    /// 执行器节点ID
    /// </summary>
    public string? ExecutorNodeId { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime StartedAt { get; set; }

    /// <summary>
    /// 完成时间
    /// </summary>
    public DateTime? CompletedAt { get; set; }

    /// <summary>
    /// 执行时长
    /// </summary>
    public TimeSpan? Duration => CompletedAt?.Subtract(StartedAt);

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 输入数据大小（字节）
    /// </summary>
    public long InputDataSize { get; set; }

    /// <summary>
    /// 输出数据大小（字节）
    /// </summary>
    public long OutputDataSize { get; set; }

    /// <summary>
    /// 执行优先级
    /// </summary>
    public ExecutionPriority Priority { get; set; } = ExecutionPriority.Normal;

    /// <summary>
    /// 执行标签
    /// </summary>
    public List<string> Tags { get; set; } = new();

    /// <summary>
    /// 执行元数据
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 执行搜索查询
/// </summary>
public class ExecutionSearchQuery : PagedQuery
{
    /// <summary>
    /// 工作流ID过滤器
    /// </summary>
    public string? WorkflowId { get; set; }

    /// <summary>
    /// 执行状态过滤器
    /// </summary>
    public List<WorkflowExecutionState>? States { get; set; }

    /// <summary>
    /// 执行器节点ID过滤器
    /// </summary>
    public string? ExecutorNodeId { get; set; }

    /// <summary>
    /// 开始时间范围（开始）
    /// </summary>
    public DateTime? StartedAfter { get; set; }

    /// <summary>
    /// 开始时间范围（结束）
    /// </summary>
    public DateTime? StartedBefore { get; set; }

    /// <summary>
    /// 完成时间范围（开始）
    /// </summary>
    public DateTime? CompletedAfter { get; set; }

    /// <summary>
    /// 完成时间范围（结束）
    /// </summary>
    public DateTime? CompletedBefore { get; set; }

    /// <summary>
    /// 标签过滤器
    /// </summary>
    public List<string>? Tags { get; set; }

    /// <summary>
    /// 优先级过滤器
    /// </summary>
    public List<ExecutionPriority>? Priorities { get; set; }

    /// <summary>
    /// 是否包含错误信息
    /// </summary>
    public bool? HasError { get; set; }

    /// <summary>
    /// 最小执行时长（毫秒）
    /// </summary>
    public long? MinDurationMs { get; set; }

    /// <summary>
    /// 最大执行时长（毫秒）
    /// </summary>
    public long? MaxDurationMs { get; set; }
}

/// <summary>
/// 执行搜索条件（来自原FlowCustomV1.Core.Interfaces.IExecutionRepository）
/// </summary>
public class ExecutionSearchCriteria
{
    /// <summary>
    /// 工作流ID
    /// </summary>
    public string? WorkflowId { get; set; }

    /// <summary>
    /// 执行状态
    /// </summary>
    public WorkflowExecutionState? State { get; set; }

    /// <summary>
    /// 是否成功
    /// </summary>
    public bool? IsSuccess { get; set; }

    /// <summary>
    /// 开始时间范围开始
    /// </summary>
    public DateTime? StartedAfter { get; set; }

    /// <summary>
    /// 开始时间范围结束
    /// </summary>
    public DateTime? StartedBefore { get; set; }

    /// <summary>
    /// 完成时间范围开始
    /// </summary>
    public DateTime? CompletedAfter { get; set; }

    /// <summary>
    /// 完成时间范围结束
    /// </summary>
    public DateTime? CompletedBefore { get; set; }

    /// <summary>
    /// 关键词搜索（在错误消息和消息中搜索）
    /// </summary>
    public string? Keyword { get; set; }

    /// <summary>
    /// 页大小
    /// </summary>
    public int PageSize { get; set; } = 50;

    /// <summary>
    /// 页码
    /// </summary>
    public int PageNumber { get; set; } = 1;

    /// <summary>
    /// 排序字段
    /// </summary>
    public string SortBy { get; set; } = "StartedAt";

    /// <summary>
    /// 是否降序排序
    /// </summary>
    public bool SortDescending { get; set; } = true;
}
