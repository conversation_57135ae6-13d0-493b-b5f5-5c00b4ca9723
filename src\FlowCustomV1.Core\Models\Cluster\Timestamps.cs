namespace FlowCustomV1.Core.Models.Cluster;

/// <summary>
/// 时间戳信息
/// 记录节点生命周期中的关键时间点
/// </summary>
public class Timestamps
{
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 注册时间 (向后兼容)
    /// </summary>
    public DateTime RegisteredAt 
    { 
        get => CreatedAt; 
        set => CreatedAt = value; 
    }

    /// <summary>
    /// 最后活跃时间
    /// </summary>
    public DateTime LastActiveAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 最后心跳时间 (向后兼容)
    /// </summary>
    public DateTime LastSeenAt 
    { 
        get => LastActiveAt; 
        set => LastActiveAt = value; 
    }

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime LastUpdatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 最后健康检查时间
    /// </summary>
    public DateTime? LastHealthCheckAt { get; set; }

    /// <summary>
    /// 最后错误时间
    /// </summary>
    public DateTime? LastErrorAt { get; set; }

    /// <summary>
    /// 最后警告时间
    /// </summary>
    public DateTime? LastWarningAt { get; set; }

    /// <summary>
    /// 启动时间
    /// </summary>
    public DateTime? StartedAt { get; set; }

    /// <summary>
    /// 停止时间
    /// </summary>
    public DateTime? StoppedAt { get; set; }

    /// <summary>
    /// 暂停时间
    /// </summary>
    public DateTime? PausedAt { get; set; }

    /// <summary>
    /// 恢复时间
    /// </summary>
    public DateTime? ResumedAt { get; set; }

    /// <summary>
    /// 维护开始时间
    /// </summary>
    public DateTime? MaintenanceStartedAt { get; set; }

    /// <summary>
    /// 维护结束时间
    /// </summary>
    public DateTime? MaintenanceEndedAt { get; set; }

    /// <summary>
    /// 最后备份时间
    /// </summary>
    public DateTime? LastBackupAt { get; set; }

    /// <summary>
    /// 最后重启时间
    /// </summary>
    public DateTime? LastRestartAt { get; set; }

    /// <summary>
    /// 最后配置更新时间
    /// </summary>
    public DateTime? LastConfigUpdateAt { get; set; }

    /// <summary>
    /// 最后性能检查时间
    /// </summary>
    public DateTime? LastPerformanceCheckAt { get; set; }

    /// <summary>
    /// 最后日志轮转时间
    /// </summary>
    public DateTime? LastLogRotationAt { get; set; }

    /// <summary>
    /// 时间戳元数据
    /// </summary>
    public Dictionary<string, DateTime> TimestampMetadata { get; set; } = new();

    /// <summary>
    /// 获取节点运行时长
    /// </summary>
    /// <returns>运行时长</returns>
    public TimeSpan GetUptime()
    {
        var startTime = StartedAt ?? CreatedAt;
        var endTime = StoppedAt ?? DateTime.UtcNow;
        return endTime - startTime;
    }

    /// <summary>
    /// 获取自创建以来的时长
    /// </summary>
    /// <returns>自创建以来的时长</returns>
    public TimeSpan GetAge()
    {
        return DateTime.UtcNow - CreatedAt;
    }

    /// <summary>
    /// 获取自最后活跃以来的时长
    /// </summary>
    /// <returns>自最后活跃以来的时长</returns>
    public TimeSpan GetTimeSinceLastActive()
    {
        return DateTime.UtcNow - LastActiveAt;
    }

    /// <summary>
    /// 获取自最后更新以来的时长
    /// </summary>
    /// <returns>自最后更新以来的时长</returns>
    public TimeSpan GetTimeSinceLastUpdate()
    {
        return DateTime.UtcNow - LastUpdatedAt;
    }

    /// <summary>
    /// 检查节点是否在指定时间内活跃
    /// </summary>
    /// <param name="timeSpan">时间范围</param>
    /// <returns>是否活跃</returns>
    public bool IsActiveWithin(TimeSpan timeSpan)
    {
        return GetTimeSinceLastActive() <= timeSpan;
    }

    /// <summary>
    /// 检查节点是否在指定时间内更新
    /// </summary>
    /// <param name="timeSpan">时间范围</param>
    /// <returns>是否更新</returns>
    public bool IsUpdatedWithin(TimeSpan timeSpan)
    {
        return GetTimeSinceLastUpdate() <= timeSpan;
    }

    /// <summary>
    /// 检查是否正在维护
    /// </summary>
    /// <returns>是否正在维护</returns>
    public bool IsInMaintenance()
    {
        return MaintenanceStartedAt.HasValue && 
               (!MaintenanceEndedAt.HasValue || MaintenanceStartedAt > MaintenanceEndedAt);
    }

    /// <summary>
    /// 检查是否已暂停
    /// </summary>
    /// <returns>是否已暂停</returns>
    public bool IsPaused()
    {
        return PausedAt.HasValue && 
               (!ResumedAt.HasValue || PausedAt > ResumedAt);
    }

    /// <summary>
    /// 检查是否正在运行
    /// </summary>
    /// <returns>是否正在运行</returns>
    public bool IsRunning()
    {
        return StartedAt.HasValue && 
               (!StoppedAt.HasValue || StartedAt > StoppedAt) &&
               !IsPaused() && 
               !IsInMaintenance();
    }

    /// <summary>
    /// 更新最后活跃时间
    /// </summary>
    public void UpdateLastActive()
    {
        LastActiveAt = DateTime.UtcNow;
        LastUpdatedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// 更新最后更新时间
    /// </summary>
    public void UpdateLastUpdated()
    {
        LastUpdatedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// 标记启动
    /// </summary>
    public void MarkStarted()
    {
        StartedAt = DateTime.UtcNow;
        StoppedAt = null;
        UpdateLastActive();
    }

    /// <summary>
    /// 标记停止
    /// </summary>
    public void MarkStopped()
    {
        StoppedAt = DateTime.UtcNow;
        UpdateLastUpdated();
    }

    /// <summary>
    /// 标记暂停
    /// </summary>
    public void MarkPaused()
    {
        PausedAt = DateTime.UtcNow;
        UpdateLastUpdated();
    }

    /// <summary>
    /// 标记恢复
    /// </summary>
    public void MarkResumed()
    {
        ResumedAt = DateTime.UtcNow;
        UpdateLastActive();
    }

    /// <summary>
    /// 标记维护开始
    /// </summary>
    public void MarkMaintenanceStarted()
    {
        MaintenanceStartedAt = DateTime.UtcNow;
        MaintenanceEndedAt = null;
        UpdateLastUpdated();
    }

    /// <summary>
    /// 标记维护结束
    /// </summary>
    public void MarkMaintenanceEnded()
    {
        MaintenanceEndedAt = DateTime.UtcNow;
        UpdateLastActive();
    }

    /// <summary>
    /// 标记健康检查
    /// </summary>
    public void MarkHealthCheck()
    {
        LastHealthCheckAt = DateTime.UtcNow;
        UpdateLastActive();
    }

    /// <summary>
    /// 标记错误
    /// </summary>
    public void MarkError()
    {
        LastErrorAt = DateTime.UtcNow;
        UpdateLastUpdated();
    }

    /// <summary>
    /// 标记警告
    /// </summary>
    public void MarkWarning()
    {
        LastWarningAt = DateTime.UtcNow;
        UpdateLastUpdated();
    }

    /// <summary>
    /// 标记重启
    /// </summary>
    public void MarkRestart()
    {
        LastRestartAt = DateTime.UtcNow;
        MarkStarted();
    }

    /// <summary>
    /// 标记配置更新
    /// </summary>
    public void MarkConfigUpdate()
    {
        LastConfigUpdateAt = DateTime.UtcNow;
        UpdateLastUpdated();
    }

    /// <summary>
    /// 标记性能检查
    /// </summary>
    public void MarkPerformanceCheck()
    {
        LastPerformanceCheckAt = DateTime.UtcNow;
        UpdateLastUpdated();
    }

    /// <summary>
    /// 标记备份
    /// </summary>
    public void MarkBackup()
    {
        LastBackupAt = DateTime.UtcNow;
        UpdateLastUpdated();
    }

    /// <summary>
    /// 标记日志轮转
    /// </summary>
    public void MarkLogRotation()
    {
        LastLogRotationAt = DateTime.UtcNow;
        UpdateLastUpdated();
    }

    /// <summary>
    /// 添加自定义时间戳
    /// </summary>
    /// <param name="key">时间戳键</param>
    /// <param name="timestamp">时间戳值</param>
    public void AddCustomTimestamp(string key, DateTime timestamp)
    {
        TimestampMetadata[key] = timestamp;
        UpdateLastUpdated();
    }

    /// <summary>
    /// 获取自定义时间戳
    /// </summary>
    /// <param name="key">时间戳键</param>
    /// <returns>时间戳值</returns>
    public DateTime? GetCustomTimestamp(string key)
    {
        return TimestampMetadata.TryGetValue(key, out var timestamp) ? timestamp : null;
    }

    /// <summary>
    /// 验证时间戳信息的有效性
    /// </summary>
    /// <returns>验证结果</returns>
    public bool IsValid()
    {
        // 检查基本时间戳的合理性
        if (CreatedAt > DateTime.UtcNow.AddMinutes(1)) // 允许1分钟的时钟偏差
            return false;
            
        if (LastActiveAt < CreatedAt)
            return false;
            
        if (LastUpdatedAt < CreatedAt)
            return false;
            
        // 检查启动/停止时间的逻辑性
        if (StartedAt.HasValue && StartedAt < CreatedAt)
            return false;
            
        if (StoppedAt.HasValue && StartedAt.HasValue && StoppedAt < StartedAt)
            return false;
            
        // 检查维护时间的逻辑性
        if (MaintenanceEndedAt.HasValue && MaintenanceStartedAt.HasValue && 
            MaintenanceEndedAt < MaintenanceStartedAt)
            return false;
            
        return true;
    }

    /// <summary>
    /// 创建时间戳信息的深拷贝
    /// </summary>
    /// <returns>时间戳信息的深拷贝</returns>
    public Timestamps Clone()
    {
        return new Timestamps
        {
            CreatedAt = CreatedAt,
            LastActiveAt = LastActiveAt,
            LastUpdatedAt = LastUpdatedAt,
            LastHealthCheckAt = LastHealthCheckAt,
            LastErrorAt = LastErrorAt,
            LastWarningAt = LastWarningAt,
            StartedAt = StartedAt,
            StoppedAt = StoppedAt,
            PausedAt = PausedAt,
            ResumedAt = ResumedAt,
            MaintenanceStartedAt = MaintenanceStartedAt,
            MaintenanceEndedAt = MaintenanceEndedAt,
            LastBackupAt = LastBackupAt,
            LastRestartAt = LastRestartAt,
            LastConfigUpdateAt = LastConfigUpdateAt,
            LastPerformanceCheckAt = LastPerformanceCheckAt,
            LastLogRotationAt = LastLogRotationAt,
            TimestampMetadata = new Dictionary<string, DateTime>(TimestampMetadata)
        };
    }

    /// <summary>
    /// 获取时间戳信息的简要描述
    /// </summary>
    /// <returns>时间戳信息简要描述</returns>
    public override string ToString()
    {
        var uptime = GetUptime();
        var status = IsRunning() ? "Running" : 
                    IsPaused() ? "Paused" : 
                    IsInMaintenance() ? "Maintenance" : "Stopped";
        
        return $"Timestamps[{status}] Created={CreatedAt:yyyy-MM-dd HH:mm:ss} " +
               $"Uptime={uptime.TotalHours:F1}h LastActive={LastActiveAt:HH:mm:ss}";
    }
}
