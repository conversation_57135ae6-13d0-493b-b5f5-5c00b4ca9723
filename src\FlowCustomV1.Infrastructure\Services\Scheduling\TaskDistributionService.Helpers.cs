using FlowCustomV1.Core.Models.Cluster;
using FlowCustomV1.Core.Models.Scheduling;
using FlowCustomV1.Core.Interfaces.Scheduling;

namespace FlowCustomV1.Infrastructure.Services.Scheduling;

/// <summary>
/// TaskDistributionService 辅助方法部分类
/// </summary>
public partial class TaskDistributionService
{
    #region 节点选择算法

    /// <summary>
    /// 智能负载选择
    /// </summary>
    private NodeInfo SelectSmartLoad(IReadOnlyList<NodeInfo> nodes, TaskRequirements requirements)
    {
        var scoredNodes = nodes.Select(node => new
        {
            Node = node,
            Score = CalculateNodeScore(node, requirements, TaskDistributionStrategy.SmartLoad)
        }).OrderByDescending(x => x.Score).ToList();

        return scoredNodes.First().Node;
    }

    /// <summary>
    /// 最低负载选择
    /// </summary>
    private NodeInfo SelectLeastLoad(IReadOnlyList<NodeInfo> nodes)
    {
        return nodes.OrderBy(n => n.Load.LoadScore).First();
    }

    /// <summary>
    /// 最快响应选择
    /// </summary>
    private NodeInfo SelectFastestResponse(IReadOnlyList<NodeInfo> nodes)
    {
        return nodes.OrderBy(n => n.Load.AverageResponseTimeMs).First();
    }

    /// <summary>
    /// 轮询选择
    /// </summary>
    private NodeInfo SelectRoundRobin(IReadOnlyList<NodeInfo> nodes)
    {
        var key = string.Join(",", nodes.Select(n => n.NodeId).OrderBy(id => id));
        var counter = _roundRobinCounters.AddOrUpdate(key, 0, (k, v) => (v + 1) % nodes.Count);
        return nodes[counter];
    }

    /// <summary>
    /// 随机选择
    /// </summary>
    private NodeInfo SelectRandom(IReadOnlyList<NodeInfo> nodes)
    {
        return nodes[_random.Next(nodes.Count)];
    }

    /// <summary>
    /// 基于能力选择
    /// </summary>
    private NodeInfo SelectCapabilityBased(IReadOnlyList<NodeInfo> nodes, TaskRequirements requirements)
    {
        var eligibleNodes = nodes.Where(n => IsNodeCapable(n, requirements)).ToList();
        if (eligibleNodes.Count == 0) return nodes.First();

        return eligibleNodes.OrderByDescending(n => CalculateCapabilityScore(n, requirements)).First();
    }

    /// <summary>
    /// 基于地理位置选择
    /// </summary>
    private NodeInfo SelectGeographyBased(IReadOnlyList<NodeInfo> nodes, TaskRequirements requirements)
    {
        if (requirements.GeographyRequirement == null)
            return SelectLeastLoad(nodes);

        var geographyNodes = nodes.Where(n => IsGeographyMatch(n, requirements.GeographyRequirement)).ToList();
        if (geographyNodes.Count == 0) return nodes.First();

        return SelectLeastLoad(geographyNodes);
    }

    /// <summary>
    /// 加权轮询选择
    /// </summary>
    private NodeInfo SelectWeightedRoundRobin(IReadOnlyList<NodeInfo> nodes)
    {
        var weights = nodes.Select(n => CalculateNodeWeight(n)).ToList();
        var totalWeight = weights.Sum();
        var randomValue = _random.NextDouble() * totalWeight;

        var cumulativeWeight = 0.0;
        for (int i = 0; i < nodes.Count; i++)
        {
            cumulativeWeight += weights[i];
            if (randomValue <= cumulativeWeight)
                return nodes[i];
        }

        return nodes.Last();
    }

    /// <summary>
    /// 基于性能选择
    /// </summary>
    private NodeInfo SelectPerformanceBased(IReadOnlyList<NodeInfo> nodes)
    {
        return nodes.OrderByDescending(n => CalculatePerformanceScore(n)).First();
    }

    /// <summary>
    /// 基于可用性选择
    /// </summary>
    private NodeInfo SelectAvailabilityBased(IReadOnlyList<NodeInfo> nodes)
    {
        return nodes.OrderByDescending(n => CalculateAvailabilityScore(n)).First();
    }

    /// <summary>
    /// 基于成本选择
    /// </summary>
    private NodeInfo SelectCostBased(IReadOnlyList<NodeInfo> nodes)
    {
        // 简化实现：选择负载最低的节点（成本最低）
        return SelectLeastLoad(nodes);
    }

    #endregion

    #region 评分计算

    /// <summary>
    /// 计算节点评分
    /// </summary>
    private double CalculateNodeScore(NodeInfo node, TaskRequirements requirements, TaskDistributionStrategy strategy)
    {
        var healthScore = node.Health.IsHealthy ? 100.0 : node.Health.HealthScore;
        var loadScore = 100.0 - node.Load.LoadScore; // 负载越低评分越高
        var performanceScore = CalculatePerformanceScore(node);
        var geographyScore = CalculateGeographyScore(node, requirements);

        // 根据配置权重计算综合评分
        var totalScore = healthScore * _config.HealthCheckWeight +
                        loadScore * _config.LoadWeight +
                        performanceScore * _config.PerformanceWeight +
                        geographyScore * _config.GeographyWeight;

        return Math.Max(0, Math.Min(100, totalScore));
    }

    /// <summary>
    /// 计算性能评分
    /// </summary>
    private double CalculatePerformanceScore(NodeInfo node)
    {
        var cpuScore = Math.Max(0, 100 - node.Load.CpuUsagePercentage);
        var memoryScore = Math.Max(0, 100 - node.Load.MemoryUsagePercentage);
        var capacityScore = node.Load.MaxTaskCapacity > 0 
            ? (1.0 - (double)node.Load.ActiveTaskCount / node.Load.MaxTaskCapacity) * 100 
            : 0;

        return (cpuScore + memoryScore + capacityScore) / 3.0;
    }

    /// <summary>
    /// 计算可用性评分
    /// </summary>
    private double CalculateAvailabilityScore(NodeInfo node)
    {
        var healthScore = node.Health.IsHealthy ? 100.0 : node.Health.HealthScore;
        var uptimeScore = CalculateUptimeScore(node);
        var responseScore = CalculateResponseScore(node);

        return (healthScore + uptimeScore + responseScore) / 3.0;
    }

    /// <summary>
    /// 计算可靠性评分
    /// </summary>
    private double CalculateReliabilityScore(NodeInfo node)
    {
        // 基于历史统计计算可靠性
        if (_nodeStats.TryGetValue(node.NodeId, out var stats))
        {
            return stats.CompletionRate * 100;
        }
        return 80.0; // 默认可靠性评分
    }

    /// <summary>
    /// 计算地理位置评分
    /// </summary>
    private double CalculateGeographyScore(NodeInfo node, TaskRequirements requirements)
    {
        if (requirements.GeographyRequirement == null)
            return 100.0;

        // 简化实现：基于网络延迟计算地理位置评分
        var latency = node.Network.Latency;
        var maxLatency = requirements.MaxNetworkLatencyMs;

        if (latency <= maxLatency)
        {
            return Math.Max(0, 100 - (latency / maxLatency * 50));
        }

        return 0; // 超过最大延迟要求
    }

    /// <summary>
    /// 计算节点权重
    /// </summary>
    private double CalculateNodeWeight(NodeInfo node)
    {
        // 基于节点性能和健康状态计算权重
        var performanceWeight = node.Capabilities.PerformanceLevel / 10.0;
        var healthWeight = node.Health.IsHealthy ? 1.0 : 0.5;
        var loadWeight = Math.Max(0.1, 1.0 - node.Load.LoadScore / 100.0);

        return performanceWeight * healthWeight * loadWeight;
    }

    /// <summary>
    /// 计算运行时间评分
    /// </summary>
    private double CalculateUptimeScore(NodeInfo node)
    {
        var uptime = DateTime.UtcNow - node.Timestamps.CreatedAt;
        var uptimeHours = uptime.TotalHours;

        // 运行时间越长评分越高，但有上限
        return Math.Min(100, uptimeHours / 24.0 * 10);
    }

    /// <summary>
    /// 计算响应评分
    /// </summary>
    private double CalculateResponseScore(NodeInfo node)
    {
        var responseTime = node.Load.AverageResponseTimeMs;
        if (responseTime <= 100) return 100;
        if (responseTime <= 500) return 80;
        if (responseTime <= 1000) return 60;
        if (responseTime <= 2000) return 40;
        return 20;
    }

    /// <summary>
    /// 计算能力评分
    /// </summary>
    private double CalculateCapabilityScore(NodeInfo node, TaskRequirements requirements)
    {
        var score = 0.0;
        var totalChecks = 0;

        // CPU核心数检查
        if (requirements.MinCpuCores > 0)
        {
            score += node.Capabilities.CpuCores >= requirements.MinCpuCores ? 25 : 0;
            totalChecks++;
        }

        // 内存检查
        if (requirements.MinMemoryMB > 0)
        {
            score += node.Capabilities.MemoryMb >= requirements.MinMemoryMB ? 25 : 0;
            totalChecks++;
        }

        // 磁盘空间检查
        if (requirements.MinDiskSpaceMB > 0)
        {
            score += node.Capabilities.DiskSpaceMb >= requirements.MinDiskSpaceMB ? 25 : 0;
            totalChecks++;
        }

        // 角色检查
        if (requirements.RequiredRoles.Count > 0)
        {
            var hasRequiredRole = requirements.RequiredRoles.Any(role => 
                Enum.TryParse<NodeRole>(role, out var nodeRole) && node.SupportsRole(nodeRole));
            score += hasRequiredRole ? 25 : 0;
            totalChecks++;
        }

        return totalChecks > 0 ? score / totalChecks * 4 : 100; // 转换为0-100分
    }

    #endregion

    #region 节点过滤

    /// <summary>
    /// 检查节点是否符合条件
    /// </summary>
    private bool IsNodeEligible(NodeInfo node, TaskRequirements requirements)
    {
        // 基本健康检查
        if (!node.Status.IsOnline() || !node.Health.IsHealthy)
            return false;

        // 黑名单检查
        if (_config.NodeBlacklist.Contains(node.NodeId))
            return false;

        // 白名单检查
        if (_config.NodeWhitelist.Count > 0 && !_config.NodeWhitelist.Contains(node.NodeId))
            return false;

        // 资源要求检查
        if (!IsNodeCapable(node, requirements))
            return false;

        // 亲和性规则检查
        if (!CheckAffinityRules(node, requirements))
            return false;

        // 反亲和性规则检查
        if (!CheckAntiAffinityRules(node, requirements))
            return false;

        return true;
    }

    /// <summary>
    /// 检查节点能力是否满足要求
    /// </summary>
    private bool IsNodeCapable(NodeInfo node, TaskRequirements requirements)
    {
        // CPU核心数检查
        if (requirements.MinCpuCores > 0 && node.Capabilities.CpuCores < requirements.MinCpuCores)
            return false;

        // 内存检查
        if (requirements.MinMemoryMB > 0 && node.Capabilities.MemoryMb < requirements.MinMemoryMB)
            return false;

        // 磁盘空间检查
        if (requirements.MinDiskSpaceMB > 0 && node.Capabilities.DiskSpaceMb < requirements.MinDiskSpaceMB)
            return false;

        // 角色检查
        if (requirements.RequiredRoles.Count > 0)
        {
            var hasRequiredRole = requirements.RequiredRoles.Any(role => 
                Enum.TryParse<NodeRole>(role, out var nodeRole) && node.SupportsRole(nodeRole));
            if (!hasRequiredRole)
                return false;
        }

        // GPU检查
        if (requirements.RequiresGpu && !node.Capabilities.HasGpu)
            return false;

        // 网络延迟检查
        if (node.Network.Latency > requirements.MaxNetworkLatencyMs)
            return false;

        return true;
    }

    /// <summary>
    /// 检查地理位置匹配
    /// </summary>
    private bool IsGeographyMatch(NodeInfo node, GeographyRequirement requirement)
    {
        // 简化实现：基于网络信息判断地理位置
        if (!string.IsNullOrEmpty(requirement.PreferredRegion))
        {
            // 这里应该有实际的地理位置匹配逻辑
            // 暂时基于IP地址前缀简单判断
            return true;
        }

        return true;
    }

    /// <summary>
    /// 检查亲和性规则
    /// </summary>
    private bool CheckAffinityRules(NodeInfo node, TaskRequirements requirements)
    {
        foreach (var rule in requirements.AffinityRules)
        {
            if (rule.IsRequired && !EvaluateAffinityRule(node, rule))
                return false;
        }
        return true;
    }

    /// <summary>
    /// 检查反亲和性规则
    /// </summary>
    private bool CheckAntiAffinityRules(NodeInfo node, TaskRequirements requirements)
    {
        foreach (var rule in requirements.AntiAffinityRules)
        {
            if (rule.IsRequired && EvaluateAntiAffinityRule(node, rule))
                return false;
        }
        return true;
    }

    /// <summary>
    /// 评估亲和性规则
    /// </summary>
    private bool EvaluateAffinityRule(NodeInfo node, AffinityRule rule)
    {
        // 简化实现：基于标签匹配
        foreach (var selector in rule.LabelSelector)
        {
            if (!node.Metadata.ContainsKey(selector.Key) || 
                !node.Metadata[selector.Key].ToString()!.Equals(selector.Value, StringComparison.OrdinalIgnoreCase))
            {
                return false;
            }
        }
        return true;
    }

    /// <summary>
    /// 评估反亲和性规则
    /// </summary>
    private bool EvaluateAntiAffinityRule(NodeInfo node, AntiAffinityRule rule)
    {
        // 简化实现：基于标签匹配
        foreach (var selector in rule.LabelSelector)
        {
            if (node.Metadata.ContainsKey(selector.Key) && 
                node.Metadata[selector.Key].ToString()!.Equals(selector.Value, StringComparison.OrdinalIgnoreCase))
            {
                return true; // 匹配反亲和性规则
            }
        }
        return false;
    }

    #endregion
}
