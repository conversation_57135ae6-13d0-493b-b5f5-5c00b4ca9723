{"Logging": {"LogLevel": {"Default": "Warning", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Error", "FlowCustomV1": "Information", "System": "Warning"}}, "AllowedHosts": "*", "System": {"Network": {"HttpPort": 15000, "HttpsPort": 15001, "WebSocketPort": 18080, "ManagementPort": 18081, "BindAddress": "0.0.0.0", "AllowedHosts": ["*"], "CorsAllowedOrigins": []}, "Performance": {"MaxConcurrentWorkflows": 500, "MaxConcurrentNodes": 200, "WorkerThreadPoolSize": 16, "IoThreadPoolSize": 8, "MessageQueueSize": 50000, "BatchSize": 200, "MemoryThresholdMB": 8192, "CpuThresholdPercent": 85}, "Timeouts": {"HttpRequestTimeoutSeconds": 30, "DatabaseTimeoutSeconds": 30, "WorkflowExecutionTimeoutMinutes": 120, "NodeExecutionTimeoutSeconds": 600, "MessageProcessingTimeoutSeconds": 10, "HealthCheckTimeoutSeconds": 5, "ShutdownTimeoutSeconds": 60}, "Node": {"NodeId": "flowcustom-prod-api-001", "NodeName": "FlowCustom Production API Node", "IpAddress": "0.0.0.0", "Roles": ["Api"], "Labels": {"environment": "production", "version": "v0.0.1.8", "datacenter": "primary"}, "MaxMemoryMb": 4096, "MaxCpuPercent": 85, "HeartbeatIntervalSeconds": 60, "EnableAutoDiscovery": true}, "Monitoring": {"Enabled": true, "MetricsCollectionIntervalSeconds": 300, "HealthCheckIntervalSeconds": 60, "LogLevel": "Warning", "EnablePerformanceCounters": true, "EnableDistributedTracing": true, "TracingSampleRate": 0.01, "MetricsRetentionDays": 90}}, "MessagingTopics": {"RootPrefix": "flowcustom-prod"}, "Database": {"Provider": "MySQL", "ConnectionString": "TO_BE_CONFIGURED_ON_DEPLOYMENT"}, "Nats": {"Servers": ["nats://nats-prod-server-1:14222", "nats://nats-prod-server-2:14223", "nats://nats-prod-server-3:14224"], "ConnectionName": "FlowCustomV1-Prod-Api", "Username": "TO_BE_CONFIGURED_ON_DEPLOYMENT", "Password": "TO_BE_CONFIGURED_ON_DEPLOYMENT", "ConnectionTimeoutSeconds": 30, "ReconnectIntervalSeconds": 10, "MaxReconnectAttempts": 5, "EnableAutoReconnect": true, "PingIntervalSeconds": 300, "MaxPingsOutstanding": 2, "EnableVerboseLogging": false, "JetStream": {"Enabled": true, "Domain": "flowcustom-prod-cluster", "ApiPrefix": "$JS.API", "DefaultStream": {"Name": "FLOWCUSTOM_PROD", "Subjects": ["flowcustom-prod.>"], "Storage": "file", "MaxMessages": 10000000, "MaxBytes": 10737418240, "MaxAgeSeconds": 604800, "Replicas": 3}}, "ConnectionPool": {"Enabled": true, "MinConnections": 5, "MaxConnections": 50, "IdleTimeoutSeconds": 600, "AcquireTimeoutSeconds": 30}, "Serialization": {"Type": "json", "EnableCompression": true, "CompressionType": "gzip", "Json": {"UseCamelCase": true, "IgnoreNullValues": true, "WriteIndented": false, "DateTimeFormat": "yyyy-MM-ddTHH:mm:ss.fffZ"}}}, "NodeDiscovery": {"ClusterName": "FlowCustomV1-Prod", "HeartbeatIntervalSeconds": 60, "NodeTimeoutSeconds": 300, "NodeCleanupIntervalSeconds": 180, "DiscoveryTimeoutSeconds": 10, "EnableAutoRegistration": true, "EnableHeartbeat": true, "EnableNodeCleanup": true, "MaxRetryAttempts": 3, "RetryIntervalSeconds": 10, "NodeRole": "Api", "NodeTags": ["api", "production"], "CapabilityTags": ["api-gateway", "http-api"], "EnableVerboseLogging": false}}