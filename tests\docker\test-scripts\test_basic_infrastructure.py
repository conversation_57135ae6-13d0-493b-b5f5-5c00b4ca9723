#!/usr/bin/env python3
"""
FlowCustomV1 v0.0.1.7 基础设施测试
测试NATS和MySQL的基础功能
"""

import asyncio
import json
import time
import os
from datetime import datetime
from typing import Dict, Any
import aiohttp

# 尝试导入NATS和MySQL客户端
try:
    import nats
    NATS_AVAILABLE = True
except ImportError:
    NATS_AVAILABLE = False
    print("⚠️ NATS客户端未安装，跳过NATS测试")

try:
    import mysql.connector
    MYSQL_AVAILABLE = True
except ImportError:
    MYSQL_AVAILABLE = False
    print("⚠️ MySQL客户端未安装，跳过MySQL测试")

class BasicInfrastructureTester:
    def __init__(self):
        self.nats_url = os.getenv("NATS_SERVERS", "nats://nats:4222")
        self.mysql_config = {
            'host': 'mysql',
            'database': 'flowcustom_simple_test',
            'user': 'flowcustom',
            'password': 'SimpleTestPassword123!'
        }
        
        self.test_results = {
            "test_suite": "Basic Infrastructure Tests",
            "start_time": datetime.utcnow().isoformat(),
            "tests": [],
            "summary": {
                "total": 0,
                "passed": 0,
                "failed": 0,
                "skipped": 0,
                "errors": []
            }
        }

    def log_test_result(self, test_name: str, status: str, details: Dict[str, Any] = None, error: str = None):
        """记录测试结果"""
        test_result = {
            "name": test_name,
            "status": status,
            "timestamp": datetime.utcnow().isoformat(),
            "details": details or {},
            "error": error
        }
        
        self.test_results["tests"].append(test_result)
        self.test_results["summary"]["total"] += 1
        
        if status == "passed":
            self.test_results["summary"]["passed"] += 1
            print(f"✅ {test_name}")
        elif status == "failed":
            self.test_results["summary"]["failed"] += 1
            self.test_results["summary"]["errors"].append(f"{test_name}: {error}")
            print(f"❌ {test_name}: {error}")
        elif status == "skipped":
            self.test_results["summary"]["skipped"] += 1
            print(f"⏭️ {test_name}: 跳过")

    async def test_nats_connection(self):
        """测试NATS连接"""
        if not NATS_AVAILABLE:
            self.log_test_result("NATS连接测试", "skipped", error="NATS客户端不可用")
            return

        try:
            nc = await nats.connect(self.nats_url, connect_timeout=10)

            # 获取连接状态信息
            is_connected = nc.is_connected
            connected_url = str(nc.connected_url) if nc.connected_url else "unknown"

            # 尝试获取服务器信息（如果可用）
            server_info = {}
            try:
                # 新版本NATS客户端可能有不同的API
                if hasattr(nc, 'server_info'):
                    server_info = nc.server_info
                elif hasattr(nc, '_server_info'):
                    server_info = nc._server_info
            except:
                server_info = {"note": "服务器信息不可用，但连接正常"}

            await nc.close()

            self.log_test_result("NATS连接测试", "passed", {
                "is_connected": is_connected,
                "connected_url": connected_url,
                "server_info": server_info
            })
        except Exception as e:
            self.log_test_result("NATS连接测试", "failed", error=str(e))

    async def test_nats_pub_sub(self):
        """测试NATS发布订阅"""
        if not NATS_AVAILABLE:
            self.log_test_result("NATS发布订阅测试", "skipped", error="NATS客户端不可用")
            return

        try:
            nc = await nats.connect(self.nats_url)
            
            received_messages = []
            test_subject = "test.basic.pubsub"
            test_message = "Hello FlowCustomV1!"
            
            async def message_handler(msg):
                received_messages.append(msg.data.decode())
            
            # 订阅
            await nc.subscribe(test_subject, cb=message_handler)
            await asyncio.sleep(0.1)  # 等待订阅生效
            
            # 发布
            await nc.publish(test_subject, test_message.encode())
            await nc.flush()
            await asyncio.sleep(0.5)  # 等待消息处理
            
            await nc.close()
            
            success = len(received_messages) > 0 and received_messages[0] == test_message
            
            self.log_test_result("NATS发布订阅测试", "passed" if success else "failed", {
                "sent_message": test_message,
                "received_messages": received_messages,
                "success": success
            }, None if success else "消息未正确接收")
            
        except Exception as e:
            self.log_test_result("NATS发布订阅测试", "failed", error=str(e))

    async def test_nats_jetstream(self):
        """测试NATS JetStream"""
        if not NATS_AVAILABLE:
            self.log_test_result("NATS JetStream测试", "skipped", error="NATS客户端不可用")
            return

        try:
            nc = await nats.connect(self.nats_url)
            js = nc.jetstream()
            
            stream_name = "TEST_STREAM"
            subject = "test.jetstream.basic"
            
            try:
                # 尝试创建流
                await js.add_stream(name=stream_name, subjects=[subject])
                
                # 发布消息
                test_message = "JetStream test message"
                ack = await js.publish(subject, test_message.encode())
                
                # 清理
                await js.delete_stream(stream_name)
                
                self.log_test_result("NATS JetStream测试", "passed", {
                    "stream_created": True,
                    "message_published": True,
                    "ack_received": ack is not None
                })
                
            except Exception as js_error:
                # JetStream可能未启用，这是可接受的
                self.log_test_result("NATS JetStream测试", "skipped", 
                                   error=f"JetStream不可用: {str(js_error)}")
            
            await nc.close()
            
        except Exception as e:
            self.log_test_result("NATS JetStream测试", "failed", error=str(e))

    def test_mysql_connection(self):
        """测试MySQL连接"""
        if not MYSQL_AVAILABLE:
            self.log_test_result("MySQL连接测试", "skipped", error="MySQL客户端不可用")
            return

        try:
            conn = mysql.connector.connect(**self.mysql_config)
            cursor = conn.cursor()
            
            # 测试基本查询
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()[0]
            
            cursor.close()
            conn.close()
            
            self.log_test_result("MySQL连接测试", "passed", {
                "mysql_version": version,
                "database": self.mysql_config['database']
            })
            
        except Exception as e:
            self.log_test_result("MySQL连接测试", "failed", error=str(e))

    def test_mysql_tables(self):
        """测试MySQL表结构"""
        if not MYSQL_AVAILABLE:
            self.log_test_result("MySQL表结构测试", "skipped", error="MySQL客户端不可用")
            return

        try:
            conn = mysql.connector.connect(**self.mysql_config)
            cursor = conn.cursor()
            
            # 检查表是否存在
            cursor.execute("SHOW TABLES")
            tables = [table[0] for table in cursor.fetchall()]
            
            expected_tables = ['cluster_nodes', 'workflow_definitions', 'workflow_executions', 'tasks', 'node_load_stats']
            missing_tables = [table for table in expected_tables if table not in tables]
            
            cursor.close()
            conn.close()
            
            success = len(missing_tables) == 0
            
            self.log_test_result("MySQL表结构测试", "passed" if success else "failed", {
                "existing_tables": tables,
                "expected_tables": expected_tables,
                "missing_tables": missing_tables
            }, None if success else f"缺少表: {missing_tables}")
            
        except Exception as e:
            self.log_test_result("MySQL表结构测试", "failed", error=str(e))

    async def test_nats_monitoring(self):
        """测试NATS监控接口"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get("http://nats:8222/varz", timeout=10) as response:
                    if response.status == 200:
                        data = await response.json()
                        self.log_test_result("NATS监控接口测试", "passed", {
                            "connections": data.get("connections", 0),
                            "in_msgs": data.get("in_msgs", 0),
                            "out_msgs": data.get("out_msgs", 0),
                            "uptime": data.get("uptime", "unknown")
                        })
                    else:
                        self.log_test_result("NATS监控接口测试", "failed", 
                                           error=f"HTTP状态码: {response.status}")
        except Exception as e:
            self.log_test_result("NATS监控接口测试", "failed", error=str(e))

    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始FlowCustomV1基础设施测试")
        print("=" * 50)
        
        # NATS测试
        await self.test_nats_connection()
        await self.test_nats_pub_sub()
        await self.test_nats_jetstream()
        await self.test_nats_monitoring()
        
        # MySQL测试
        self.test_mysql_connection()
        self.test_mysql_tables()
        
        # 完成测试
        self.test_results["end_time"] = datetime.utcnow().isoformat()
        
        # 输出摘要
        summary = self.test_results["summary"]
        print("\n" + "=" * 50)
        print(f"📊 测试完成: {summary['passed']}/{summary['total']} 通过")
        print(f"⏭️ 跳过: {summary['skipped']}")
        
        if summary["failed"] > 0:
            print("❌ 失败的测试:")
            for error in summary["errors"]:
                print(f"  - {error}")
        
        # 保存结果
        os.makedirs("/app/test-results", exist_ok=True)
        with open("/app/test-results/basic_infrastructure_test.json", 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, indent=2, ensure_ascii=False)
        
        print(f"\n📁 测试结果已保存到: /app/test-results/basic_infrastructure_test.json")
        
        return summary["failed"] == 0

async def main():
    tester = BasicInfrastructureTester()
    success = await tester.run_all_tests()
    
    if success:
        print("\n🎉 所有测试通过！基础设施运行正常。")
    else:
        print("\n⚠️ 部分测试失败，请检查基础设施配置。")
    
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
