{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "FlowCustomV1": "Debug"}}, "Database": {"ConnectionString": "Server=localhost;Port=3306;Database=flowcustom;Uid=flowcustom;Pwd=FlowCustom@2025;", "Provider": "MySQL", "AutoInitialize": true, "AutoMigrate": true, "BackupBeforeMigration": false, "FallbackToInMemory": false, "HealthCheckInterval": "00:01:00", "Migration": {"Timeout": "00:02:00", "RetryCount": 3, "CreateBackup": false, "BackupDirectory": "test_backups", "ValidateAfterMigration": true}, "ConnectionPool": {"MaxPoolSize": 20, "MinPoolSize": 2, "ConnectionTimeout": 30, "CommandTimeout": 60, "ConnectionLifetime": 300}}}