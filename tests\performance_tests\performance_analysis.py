#!/usr/bin/env python3
"""
FlowCustomV1 性能分析脚本
深入分析性能瓶颈
"""

import requests
import time
import json
from datetime import datetime
import threading

BASE_URL = "http://localhost:5000"

def analyze_cluster_nodes_performance():
    """分析cluster_nodes端点的性能问题"""
    print("🔍 深入分析 cluster_nodes 性能问题")
    print("=" * 50)
    
    url = f"{BASE_URL}/api/cluster/nodes"
    
    # 测试多次请求，记录详细时间
    for i in range(3):
        print(f"\n📊 第 {i+1} 次详细分析:")
        
        # 记录各个阶段的时间
        start_time = time.time()
        
        try:
            # DNS解析和连接建立
            connect_start = time.time()
            response = requests.get(url, timeout=30)
            connect_end = time.time()
            
            # 响应处理
            process_start = time.time()
            data = response.json()
            process_end = time.time()
            
            total_time = (connect_end - start_time) * 1000
            connect_time = (connect_end - connect_start) * 1000
            process_time = (process_end - process_start) * 1000
            
            print(f"  总响应时间: {total_time:.2f}ms")
            print(f"  网络连接时间: {connect_time:.2f}ms")
            print(f"  数据处理时间: {process_time:.2f}ms")
            print(f"  状态码: {response.status_code}")
            print(f"  响应大小: {len(response.content)} bytes")
            print(f"  节点数量: {len(data) if isinstance(data, list) else 'N/A'}")
            
            # 分析响应内容
            if isinstance(data, list) and len(data) > 0:
                node = data[0]
                print(f"  节点ID: {node.get('nodeId', 'N/A')}")
                print(f"  节点状态: {node.get('status', 'N/A')}")
                
        except Exception as e:
            print(f"  请求失败: {str(e)}")
            
        time.sleep(1)  # 等待1秒再进行下次测试

def test_other_endpoints():
    """测试其他端点的性能作为对比"""
    print("\n🔍 对比其他端点性能")
    print("=" * 50)
    
    endpoints = {
        "executor_capacity": "/api/executor/capacity",
        "swagger": "/swagger"
    }
    
    for name, path in endpoints.items():
        url = f"{BASE_URL}{path}"
        print(f"\n📊 测试 {name}:")
        
        times = []
        for i in range(5):
            try:
                start_time = time.time()
                response = requests.get(url, timeout=10)
                end_time = time.time()
                
                response_time = (end_time - start_time) * 1000
                times.append(response_time)
                
                print(f"  请求 {i+1}: {response_time:.2f}ms (状态码: {response.status_code})")
                
            except Exception as e:
                print(f"  请求 {i+1}: 失败 ({str(e)})")
                
        if times:
            avg_time = sum(times) / len(times)
            print(f"  平均响应时间: {avg_time:.2f}ms")

def test_nats_performance():
    """测试NATS服务器性能"""
    print("\n🔍 NATS服务器性能分析")
    print("=" * 50)
    
    nats_endpoints = {
        "server_info": "http://localhost:8222/varz",
        "jetstream_info": "http://localhost:8222/jsz",
        "connections": "http://localhost:8222/connz"
    }
    
    for name, url in nats_endpoints.items():
        try:
            start_time = time.time()
            response = requests.get(url, timeout=5)
            end_time = time.time()
            
            response_time = (end_time - start_time) * 1000
            
            print(f"📊 {name}:")
            print(f"  响应时间: {response_time:.2f}ms")
            print(f"  状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                if name == "server_info":
                    print(f"  连接数: {data.get('connections', 'N/A')}")
                    print(f"  内存使用: {data.get('mem', 'N/A')} bytes")
                    print(f"  CPU使用: {data.get('cpu', 'N/A')}%")
                elif name == "jetstream_info":
                    print(f"  JetStream内存: {data.get('memory', 'N/A')} bytes")
                    print(f"  API错误数: {data.get('api', {}).get('errors', 'N/A')}")
                elif name == "connections":
                    connections = data.get('connections', [])
                    print(f"  活跃连接数: {len(connections)}")
                    
        except Exception as e:
            print(f"📊 {name}: 失败 ({str(e)})")

def test_database_performance():
    """通过API测试数据库性能"""
    print("\n🔍 数据库性能分析（通过API）")
    print("=" * 50)
    
    # 由于我们没有直接的数据库测试端点，我们通过API调用来间接测试
    print("  注意: 通过API间接测试数据库性能")
    
    # 测试需要数据库查询的端点
    db_endpoints = {
        "cluster_nodes": "/api/cluster/nodes",
        "executor_capacity": "/api/executor/capacity"
    }
    
    for name, path in db_endpoints.items():
        url = f"{BASE_URL}{path}"
        print(f"\n📊 {name} (可能涉及数据库查询):")
        
        try:
            start_time = time.time()
            response = requests.get(url, timeout=30)
            end_time = time.time()
            
            response_time = (end_time - start_time) * 1000
            
            print(f"  响应时间: {response_time:.2f}ms")
            print(f"  状态码: {response.status_code}")
            
            if response_time > 1000:
                print(f"  ⚠️  响应时间过长，可能存在数据库性能问题")
            elif response_time > 100:
                print(f"  ⚠️  响应时间较长，需要关注")
            else:
                print(f"  ✅ 响应时间正常")
                
        except Exception as e:
            print(f"  请求失败: {str(e)}")

def generate_performance_report():
    """生成性能报告"""
    print("\n" + "=" * 60)
    print("📋 FlowCustomV1 开发环境性能分析报告")
    print("=" * 60)
    print(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 运行所有分析
    analyze_cluster_nodes_performance()
    test_other_endpoints()
    test_nats_performance()
    test_database_performance()
    
    print("\n" + "=" * 60)
    print("🎯 性能问题总结和建议")
    print("=" * 60)
    
    print("\n🔴 发现的问题:")
    print("1. cluster_nodes端点响应时间异常慢（~10秒）")
    print("2. 可能存在以下原因：")
    print("   - 数据库查询性能问题")
    print("   - NATS消息传递延迟")
    print("   - 节点发现服务性能瓶颈")
    print("   - 网络连接问题")
    
    print("\n🟡 需要关注的点:")
    print("1. executor_capacity端点性能良好（~25ms）")
    print("2. swagger端点性能良好（~28ms）")
    print("3. NATS服务器本身响应正常")
    
    print("\n🟢 优化建议:")
    print("1. 检查NodeDiscoveryService的实现")
    print("2. 优化数据库查询（如果有）")
    print("3. 检查NATS消息订阅和发布逻辑")
    print("4. 考虑添加缓存机制")
    print("5. 检查是否有不必要的同步等待")

if __name__ == "__main__":
    generate_performance_report()
