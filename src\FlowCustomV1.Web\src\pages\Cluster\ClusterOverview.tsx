import React, { useEffect, useState } from 'react';
import { Row, Col, Statistic, Progress, Tag, Space, Button, Alert } from 'antd';
import {
  ClusterOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ReloadOutlined,
  SettingOutlined,
  MonitorOutlined
} from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';
import { clusterApi } from '@/services/cluster';
import type { ClusterStats, NodeInfo } from '@/types/api';
import PageLayout from '@/components/Layout/PageLayout';

const ClusterOverview: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [clusterStats, setClusterStats] = useState<ClusterStats | null>(null);
  const [nodes, setNodes] = useState<NodeInfo[]>([]);
  const [healthChecks, setHealthChecks] = useState<any[]>([]);

  // 加载集群数据
  const loadClusterData = async () => {
    try {
      setLoading(true);
      const [stats, nodeList, health] = await Promise.all([
        clusterApi.getClusterStats(),
        clusterApi.getAllNodes(),
        clusterApi.getClusterHealth(),
      ]);
      
      setClusterStats(stats);
      setNodes(nodeList);
      setHealthChecks(health.checks || []);
    } catch (error) {
      console.error('加载集群数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadClusterData();
    // 设置定时刷新
    const interval = setInterval(loadClusterData, 10000);
    return () => clearInterval(interval);
  }, []);

  // 获取健康状态颜色
  const getHealthColor = (health: string) => {
    switch (health) {
      case 'Healthy': return '#52c41a';
      case 'Warning': return '#fa8c16';
      case 'Critical': return '#ff4d4f';
      default: return '#d9d9d9';
    }
  };

  // 获取节点状态标签
  const getNodeStatusTag = (status: string) => {
    const statusMap = {
      Online: { color: 'success', text: '在线' },
      Offline: { color: 'error', text: '离线' },
      Busy: { color: 'warning', text: '繁忙' },
      Maintenance: { color: 'default', text: '维护中' },
    };
    const config = statusMap[status as keyof typeof statusMap] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 获取节点角色标签
  const getRoleTags = (roles: string[]) => {
    const roleColors = {
      Api: 'blue',
      Designer: 'green',
      Validator: 'orange',
      Executor: 'purple',
      Monitor: 'cyan',
      Scheduler: 'magenta',
      Storage: 'red',
    };
    
    return roles.map(role => (
      <Tag key={role} color={roleColors[role as keyof typeof roleColors] || 'default'}>
        {role}
      </Tag>
    ));
  };

  if (!clusterStats) {
    return (
      <PageLayout
        title="集群概览"
        description="监控集群整体状态和节点健康情况"
        icon={<ClusterOutlined />}
      >
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="text-lg">加载集群数据中...</div>
          </div>
        </div>
      </PageLayout>
    );
  }

  return (
    <PageLayout
      title="集群概览"
      description="监控集群整体状态和节点健康情况"
      icon={<ClusterOutlined />}
      actions={
        <Space>
          <Button
            icon={<SettingOutlined />}
            onClick={() => window.open('/cluster/config', '_blank')}
          >
            集群配置
          </Button>
          <Button
            type="primary"
            icon={<ReloadOutlined />}
            loading={loading}
            onClick={loadClusterData}
          >
            刷新
          </Button>
        </Space>
      }
    >

        {/* 集群健康状态警告 */}
        {clusterStats.systemHealth !== 'Healthy' && (
          <Alert
            message={`集群健康状态: ${clusterStats.systemHealth === 'Warning' ? '警告' : '严重'}`}
            description="检测到集群存在问题，请及时处理"
            type={clusterStats.systemHealth === 'Warning' ? 'warning' : 'error'}
            showIcon
            className="mb-4"
          />
        )}

      {/* 集群统计 */}
      <Row gutter={[16, 16]} className="mb-6">
        <Col xs={24} sm={12} lg={6}>
          <ProCard>
            <Statistic
              title="集群名称"
              value={clusterStats.clusterName}
              prefix={<ClusterOutlined />}
              valueStyle={{ color: '#1890ff', fontSize: '18px' }}
            />
          </ProCard>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <ProCard>
            <Statistic
              title="在线节点"
              value={`${clusterStats.onlineNodes} / ${clusterStats.totalNodes}`}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
            <Progress 
              percent={Math.round((clusterStats.onlineNodes / clusterStats.totalNodes) * 100)}
              showInfo={false}
              strokeColor="#52c41a"
              className="mt-2"
            />
          </ProCard>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <ProCard>
            <Statistic
              title="运行中执行"
              value={clusterStats.runningExecutions}
              prefix={<MonitorOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </ProCard>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <ProCard>
            <Statistic
              title="系统健康度"
              value={clusterStats.systemHealth === 'Healthy' ? '健康' : 
                     clusterStats.systemHealth === 'Warning' ? '警告' : '严重'}
              prefix={<ExclamationCircleOutlined />}
              valueStyle={{ color: getHealthColor(clusterStats.systemHealth) }}
            />
          </ProCard>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        {/* 节点状态 */}
        <Col xs={24} lg={16}>
          <ProCard title="节点状态" loading={loading}>
            <div className="space-y-4">
              {nodes.map(node => (
                <div key={node.nodeId} className="border rounded-lg p-4 hover:bg-gray-50">
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <div className="font-semibold text-lg">{node.nodeName}</div>
                      <div className="text-sm text-gray-500">
                        {node.address}:{node.port} | {node.nodeType}
                      </div>
                    </div>
                    <div className="text-right">
                      {getNodeStatusTag(node.status)}
                      <div className="text-xs text-gray-500 mt-1">
                        {new Date(node.lastHeartbeat).toLocaleString()}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex justify-between items-center mb-3">
                    <div className="flex flex-wrap gap-1">
                      {getRoleTags(node.roles)}
                    </div>
                    <div className="text-sm text-gray-600">
                      v{node.version}
                    </div>
                  </div>

                  <Row gutter={16}>
                    <Col span={6}>
                      <div className="text-center">
                        <div className="text-xs text-gray-500 mb-1">CPU</div>
                        <Progress 
                          type="circle" 
                          size={40} 
                          percent={Math.round(node.loadInfo.cpuUsage)} 
                          format={(percent) => `${percent}%`}
                        />
                      </div>
                    </Col>
                    <Col span={6}>
                      <div className="text-center">
                        <div className="text-xs text-gray-500 mb-1">内存</div>
                        <Progress 
                          type="circle" 
                          size={40} 
                          percent={Math.round(node.loadInfo.memoryUsage)} 
                          format={(percent) => `${percent}%`}
                        />
                      </div>
                    </Col>
                    <Col span={6}>
                      <div className="text-center">
                        <div className="text-xs text-gray-500 mb-1">磁盘</div>
                        <Progress 
                          type="circle" 
                          size={40} 
                          percent={Math.round(node.loadInfo.diskUsage)} 
                          format={(percent) => `${percent}%`}
                        />
                      </div>
                    </Col>
                    <Col span={6}>
                      <div className="text-center">
                        <div className="text-xs text-gray-500 mb-1">执行</div>
                        <div className="text-sm font-semibold">
                          {node.loadInfo.activeExecutions}/{node.loadInfo.maxExecutions}
                        </div>
                      </div>
                    </Col>
                  </Row>
                </div>
              ))}
            </div>
          </ProCard>
        </Col>

        {/* 健康检查 */}
        <Col xs={24} lg={8}>
          <ProCard title="健康检查" loading={loading}>
            <div className="space-y-3">
              {healthChecks.map((check, index) => (
                <div key={index} className="flex justify-between items-center p-3 border rounded">
                  <div>
                    <div className="font-medium">{check.name}</div>
                    <div className="text-sm text-gray-500">{check.message}</div>
                    <div className="text-xs text-gray-400">
                      {new Date(check.lastCheck).toLocaleString()}
                    </div>
                  </div>
                  <Tag color={
                    check.status === 'Pass' ? 'success' : 
                    check.status === 'Warning' ? 'warning' : 'error'
                  }>
                    {check.status === 'Pass' ? '通过' : 
                     check.status === 'Warning' ? '警告' : '失败'}
                  </Tag>
                </div>
              ))}
              
              {healthChecks.length === 0 && (
                <div className="text-center text-gray-500 py-8">
                  <CheckCircleOutlined className="text-4xl mb-2" />
                  <p>所有检查项正常</p>
                </div>
              )}
            </div>
          </ProCard>

          {/* 执行统计 */}
          <ProCard title="执行统计" className="mt-4">
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span>总执行数</span>
                <span className="font-semibold">{clusterStats.totalExecutions}</span>
              </div>
              <div className="flex justify-between items-center">
                <span>运行中</span>
                <span className="font-semibold text-orange-600">{clusterStats.runningExecutions}</span>
              </div>
              <div className="flex justify-between items-center">
                <span>已完成</span>
                <span className="font-semibold text-green-600">{clusterStats.completedExecutions}</span>
              </div>
              <div className="flex justify-between items-center">
                <span>失败</span>
                <span className="font-semibold text-red-600">{clusterStats.failedExecutions}</span>
              </div>
              <div className="pt-2 border-t">
                <div className="flex justify-between items-center">
                  <span>平均执行时间</span>
                  <span className="font-semibold">
                    {Math.round(clusterStats.averageExecutionTime / 1000)}s
                  </span>
                </div>
              </div>
            </div>
          </ProCard>
        </Col>
      </Row>
    </PageLayout>
  );
};

export default ClusterOverview;
