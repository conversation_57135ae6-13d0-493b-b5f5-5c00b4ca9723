import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Card,
  Form,
  Input,
  Select,
  Button,
  Space,
  message,
  Radio,
  Switch,
  Divider,
  Alert,
  Spin
} from 'antd';
import {
  ArrowLeftOutlined,
  SaveOutlined,
  SettingOutlined,
  EyeOutlined
} from '@ant-design/icons';
import { workflowApi } from '@/services/workflow';
import type { WorkflowDefinition } from '@/types/api';
import { PublishStatus } from '@/types/api';

const { TextArea } = Input;
const { Option } = Select;

interface EditWorkflowForm {
  name: string;
  description?: string;
  category?: string;
  tags?: string[];
  publishStatus: PublishStatus;
  isTemplate: boolean;
  priority: number;
  timeout?: number;
  retryCount: number;
  enableLogging: boolean;
  enableNotification: boolean;
}

const WorkflowEdit: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [form] = Form.useForm<EditWorkflowForm>();
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [workflow, setWorkflow] = useState<WorkflowDefinition | null>(null);

  // 预定义的分类选项
  const categories = [
    '数据处理',
    '业务流程',
    '系统集成',
    '监控告警',
    '定时任务',
    '其他'
  ];

  // 预定义的标签选项
  const tagOptions = [
    '自动化', '数据同步', '报表生成', '邮件通知', 
    '文件处理', '数据库操作', 'API调用', '批处理',
    '实时处理', '定时执行', '条件触发', '人工审批'
  ];

  // 加载工作流数据
  const loadWorkflow = async () => {
    if (!id) return;

    try {
      setInitialLoading(true);
      const workflowData = await workflowApi.getWorkflow(id);
      setWorkflow(workflowData);

      // 设置表单初始值 - 从metadata中读取扩展字段
      const metadata = workflowData.metadata || {};
      form.setFieldsValue({
        name: workflowData.name,
        description: workflowData.description,
        category: metadata.category,
        tags: workflowData.tags,
        publishStatus: workflowData.publishStatus as any,
        isTemplate: metadata.isTemplate || false,
        priority: metadata.priority || 1,
        timeout: metadata.timeout ? Math.round(metadata.timeout / 1000) : undefined,
        retryCount: metadata.retryCount || 0,
        enableLogging: metadata.enableLogging !== false,
        enableNotification: metadata.enableNotification || false
      });
    } catch (error) {
      console.error('加载工作流失败:', error);
      message.error('加载工作流失败');
      navigate('/workflow/list');
    } finally {
      setInitialLoading(false);
    }
  };

  useEffect(() => {
    loadWorkflow();
  }, [id]);

  // 保存工作流
  const handleSubmit = async (values: EditWorkflowForm) => {
    if (!workflow) return;

    try {
      setLoading(true);

      // 构建更新数据 - 确保包含所有必需字段
      const updateData: WorkflowDefinition = {
        // 基本信息
        workflowId: workflow.workflowId,
        name: values.name,
        description: values.description || '',
        version: workflow.version,
        author: workflow.author || '',

        // 时间戳
        createdAt: workflow.createdAt,
        lastModifiedAt: new Date().toISOString(),

        // 状态字段
        isActive: workflow.isActive,
        isPublished: workflow.isPublished,
        publishedAt: workflow.publishedAt,
        publishedBy: workflow.publishedBy,
        publishStatus: values.publishStatus,
        createdBy: workflow.createdBy,
        lastModifiedBy: workflow.lastModifiedBy,

        // 保持原有的结构化数据
        nodes: workflow.nodes || [],
        connections: workflow.connections || [],
        inputParameters: workflow.inputParameters || [],
        outputParameters: workflow.outputParameters || [],
        configuration: workflow.configuration || {},

        // 更新的字段
        tags: values.tags || [],
        metadata: {
          ...workflow.metadata,
          // 添加编辑相关的元数据（这些字段不是WorkflowDefinition的直接属性）
          category: values.category,
          isTemplate: values.isTemplate,
          priority: values.priority,
          timeout: values.timeout ? values.timeout * 1000 : undefined,
          retryCount: values.retryCount,
          enableLogging: values.enableLogging,
          enableNotification: values.enableNotification,
          lastEditedAt: new Date().toISOString()
        }
      };

      await workflowApi.updateWorkflow(workflow.workflowId, updateData);
      message.success('工作流更新成功');
      
      // 重新加载数据
      await loadWorkflow();
    } catch (error) {
      console.error('更新工作流失败:', error);
      message.error('更新工作流失败');
    } finally {
      setLoading(false);
    }
  };

  if (initialLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Spin size="large" />
        <span className="ml-3 text-lg">加载工作流信息中...</span>
      </div>
    );
  }

  if (!workflow) {
    return (
      <div>
        <Alert
          message="工作流不存在"
          description="请检查工作流ID是否正确，或者该工作流可能已被删除。"
          type="error"
          showIcon
          action={
            <Button size="small" onClick={() => navigate('/workflow/list')}>
              返回列表
            </Button>
          }
        />
      </div>
    );
  }

  return (
    <div>
      {/* 页面头部 */}
      <div className="mb-6">
        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center">
            <Button 
              icon={<ArrowLeftOutlined />} 
              onClick={() => navigate('/workflow/list')}
              className="mr-3"
            >
              返回列表
            </Button>
            <div>
              <h1 className="text-2xl font-semibold text-gray-900 mb-1">
                编辑工作流
              </h1>
              <p className="text-gray-600 text-sm">修改工作流的基本信息和配置</p>
            </div>
          </div>
          <Space>
            <Button
              icon={<EyeOutlined />}
              onClick={() => navigate(`/workflow/detail/${workflow.workflowId}`)}
            >
              查看详情
            </Button>
            <Button
              icon={<SettingOutlined />}
              onClick={() => navigate(`/workflow/designer/${workflow.workflowId}`)}
            >
              设计器
            </Button>
          </Space>
        </div>

        <Alert
          message="编辑提示"
          description={`当前编辑的是工作流 "${workflow.name}" (版本 ${workflow.version})，修改后将立即生效。`}
          type="info"
          showIcon
          className="mb-6"
        />
      </div>

      <Card>
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <div className="grid grid-cols-2 gap-6">
            {/* 左侧：基本信息 */}
            <div>
              <h3 className="text-lg font-medium mb-4">基本信息</h3>
              
              <Form.Item
                label="工作流名称"
                name="name"
                rules={[
                  { required: true, message: '请输入工作流名称' },
                  { min: 2, max: 50, message: '名称长度应在2-50个字符之间' }
                ]}
              >
                <Input placeholder="请输入工作流名称" />
              </Form.Item>

              <Form.Item
                label="描述"
                name="description"
                rules={[
                  { max: 200, message: '描述长度不能超过200个字符' }
                ]}
              >
                <TextArea 
                  rows={3} 
                  placeholder="请输入工作流描述（可选）"
                  showCount
                  maxLength={200}
                />
              </Form.Item>

              <Form.Item
                label="分类"
                name="category"
              >
                <Select placeholder="请选择工作流分类" allowClear>
                  {categories.map(category => (
                    <Option key={category} value={category}>
                      {category}
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item
                label="标签"
                name="tags"
              >
                <Select
                  mode="multiple"
                  placeholder="请选择相关标签"
                  options={tagOptions.map(tag => ({ label: tag, value: tag }))}
                  maxTagCount={5}
                />
              </Form.Item>

              <Form.Item
                label="状态"
                name="publishStatus"
              >
                <Radio.Group>
                  <Radio value={PublishStatus.Draft}>草稿</Radio>
                  <Radio value={PublishStatus.Published}>已发布</Radio>
                  <Radio value={PublishStatus.Archived}>已归档</Radio>
                </Radio.Group>
              </Form.Item>

              <Form.Item
                label="设为模板"
                name="isTemplate"
                valuePropName="checked"
              >
                <Switch checkedChildren="是" unCheckedChildren="否" />
              </Form.Item>
            </div>

            {/* 右侧：高级配置 */}
            <div>
              <h3 className="text-lg font-medium mb-4">高级配置</h3>

              <Form.Item
                label="优先级"
                name="priority"
                help="数值越大优先级越高，范围：1-10"
              >
                <Select>
                  {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map(num => (
                    <Option key={num} value={num}>
                      {num} {num <= 3 ? '(低)' : num <= 7 ? '(中)' : '(高)'}
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item
                label="超时时间（秒）"
                name="timeout"
                help="工作流执行的最大时间限制，0表示无限制"
              >
                <Input 
                  type="number" 
                  min={0} 
                  max={86400}
                  placeholder="300"
                  addonAfter="秒"
                />
              </Form.Item>

              <Form.Item
                label="重试次数"
                name="retryCount"
                help="执行失败时的自动重试次数"
              >
                <Select>
                  {[0, 1, 2, 3, 5].map(num => (
                    <Option key={num} value={num}>
                      {num === 0 ? '不重试' : `${num}次`}
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              <Divider />

              <Form.Item
                label="启用日志记录"
                name="enableLogging"
                valuePropName="checked"
              >
                <Switch 
                  checkedChildren="开启" 
                  unCheckedChildren="关闭"
                />
              </Form.Item>

              <Form.Item
                label="启用通知"
                name="enableNotification"
                valuePropName="checked"
                help="执行完成后发送通知"
              >
                <Switch 
                  checkedChildren="开启" 
                  unCheckedChildren="关闭"
                />
              </Form.Item>

              {/* 版本信息 */}
              <Divider />
              <div className="bg-gray-50 p-3 rounded">
                <div className="text-sm text-gray-600">
                  <div>当前版本: {workflow.version}</div>
                  <div>创建时间: {new Date(workflow.createdAt).toLocaleString()}</div>
                  <div>更新时间: {new Date(workflow.lastModifiedAt).toLocaleString()}</div>
                </div>
              </div>
            </div>
          </div>

          <Divider />

          {/* 操作按钮 */}
          <div className="flex justify-end">
            <Space>
              <Button onClick={() => navigate(`/workflow/detail/${workflow.workflowId}`)}>
                取消
              </Button>
              <Button 
                type="primary" 
                htmlType="submit" 
                loading={loading}
                icon={<SaveOutlined />}
              >
                保存修改
              </Button>
            </Space>
          </div>
        </Form>
      </Card>
    </div>
  );
};

export default WorkflowEdit;
