# NATS Server Configuration - Development Environment
# Single Node for Local Development

# Server Identity
server_name: "flowcustom-dev-nats-1"

# Network Configuration - Development ports
port: 4222
http_port: 8222

# JetStream Configuration - Development optimized
jetstream {
    enabled: true
    store_dir: "/tmp/jetstream"
    
    # Development environment limits (small for local testing)
    max_memory_store: 128MB
    max_file_store: 512MB
    
    # Domain for development
    domain: "flowcustom-dev"
}

# Client Authentication - Development environment (simple)
authorization {
    users: [
        {
            user: "flowcustom"
            password: "flowcustom_password"
            permissions: {
                publish: {
                    allow: ["flowcustom-dev.>", "_INBOX.>"]
                }
                subscribe: {
                    allow: ["flowcustom-dev.>", "_INBOX.>", "$JS.>"]
                }
            }
        },
        {
            user: "dev_admin"
            password: "dev_admin_password"
            permissions: {
                publish: {
                    allow: [">"]
                }
                subscribe: {
                    allow: [">"]
                }
            }
        }
    ]
}

# Logging Configuration - Development environment (verbose)
log_file: "/var/log/nats/nats-server.log"
logtime: true
debug: true
trace: true

# Monitoring Configuration
monitor_port: 8222
server_tags: [
    "environment:development",
    "cluster:flowcustom-dev",
    "node:1",
    "version:v0.0.1.8",
    "developer:local"
]

# Performance Settings - Development optimized (relaxed limits)
max_connections: 100
max_subscriptions: 1000
max_payload: 1MB
max_pending: 32MB

# Write deadline for slow consumers
write_deadline: "5s"

# Ping settings
ping_interval: "1m"
ping_max: 2

# TLS Configuration (disabled for development)
# tls {
#     cert_file: "/etc/nats/certs/server.crt"
#     key_file: "/etc/nats/certs/server.key"
#     ca_file: "/etc/nats/certs/ca.crt"
#     verify: false
# }

# Development specific settings
# Allow connections from localhost only for security
# listen: "127.0.0.1:4222"
