# FlowCustomV1 API 文档

## 📋 文档信息

| 项目信息 | 详细内容 |
|---------|---------|
| **项目名称** | FlowCustomV1 工作流自动化系统 |
| **API版本** | v0.0.0.6 |
| **文档类型** | API接口文档 |
| **更新日期** | 2025-08-18 |
| **文档状态** | 核心接口定义完成 |

---

## 🔧 **核心服务接口**

### **1. IClusterService - 集群服务接口**

#### 基本信息
```csharp
namespace FlowCustomV1.Core.Interfaces;
public interface IClusterService
```

#### 属性
```csharp
NodeInfo CurrentNode { get; }           // 当前节点信息
bool IsInitialized { get; }             // 集群是否已初始化
string ClusterName { get; }             // 集群名称
```

#### 核心方法

##### 初始化和生命周期管理
```csharp
// 初始化集群服务
Task InitializeAsync(string clusterName, NodeInfo nodeInfo, CancellationToken cancellationToken = default);

// 启动集群服务
Task StartAsync(CancellationToken cancellationToken = default);

// 停止集群服务
Task StopAsync(CancellationToken cancellationToken = default);
```

##### 节点管理
```csharp
// 注册节点到集群
Task RegisterNodeAsync(NodeInfo nodeInfo, CancellationToken cancellationToken = default);

// 从集群注销节点
Task UnregisterNodeAsync(string nodeId, CancellationToken cancellationToken = default);

// 更新节点信息
Task UpdateNodeAsync(NodeInfo nodeInfo, CancellationToken cancellationToken = default);
```

##### 节点发现和查询
```csharp
// 发现集群节点
Task<IEnumerable<NodeInfo>> DiscoverNodesAsync(NodeDiscoveryQuery? query = null, CancellationToken cancellationToken = default);

// 获取所有集群节点
Task<IEnumerable<NodeInfo>> GetAllNodesAsync(CancellationToken cancellationToken = default);

// 获取指定节点信息
Task<NodeInfo?> GetNodeAsync(string nodeId, CancellationToken cancellationToken = default);

// 选择最佳节点执行任务
Task<NodeInfo?> SelectBestNodeAsync(NodeExecutionRequirements? requirements = null, CancellationToken cancellationToken = default);
```

##### 健康检查和监控
```csharp
// 发送心跳
Task SendHeartbeatAsync(CancellationToken cancellationToken = default);

// 检查节点健康状态
Task<ClusterHealthCheckResult> CheckNodeHealthAsync(string nodeId, CancellationToken cancellationToken = default);

// 获取集群统计信息
Task<ClusterStats> GetClusterStatsAsync(CancellationToken cancellationToken = default);
```

##### 消息通信
```csharp
// 广播消息到所有节点
Task BroadcastMessageAsync(ClusterMessage message, CancellationToken cancellationToken = default);

// 发送消息到指定节点
Task SendMessageToNodeAsync(string nodeId, ClusterMessage message, CancellationToken cancellationToken = default);
```

#### 事件
```csharp
event EventHandler<NodeJoinedEventArgs>? NodeJoined;                    // 节点加入事件
event EventHandler<NodeLeftEventArgs>? NodeLeft;                       // 节点离开事件
event EventHandler<NodeStatusChangedEventArgs>? NodeStatusChanged;     // 节点状态变化事件
event EventHandler<MessageReceivedEventArgs>? MessageReceived;         // 消息接收事件
```

### **2. IWorkflowEngine - 工作流引擎接口**

#### 基本信息
```csharp
namespace FlowCustomV1.Core.Interfaces;
public interface IWorkflowEngine
```

#### 核心方法

##### 工作流执行
```csharp
// 执行工作流
Task<WorkflowExecutionResult> ExecuteWorkflowAsync(string workflowId, WorkflowExecutionContext context, CancellationToken cancellationToken = default);

// 暂停工作流执行
Task<WorkflowExecutionResult> PauseWorkflowAsync(string executionId, CancellationToken cancellationToken = default);

// 恢复工作流执行
Task<WorkflowExecutionResult> ResumeWorkflowAsync(string executionId, CancellationToken cancellationToken = default);

// 停止工作流执行
Task<WorkflowExecutionResult> StopWorkflowAsync(string executionId, CancellationToken cancellationToken = default);
```

##### 工作流管理
```csharp
// 注册工作流定义
Task RegisterWorkflowAsync(WorkflowDefinition workflow, CancellationToken cancellationToken = default);

// 注销工作流定义
Task UnregisterWorkflowAsync(string workflowId, CancellationToken cancellationToken = default);

// 获取工作流定义
Task<WorkflowDefinition?> GetWorkflowDefinitionAsync(string workflowId, CancellationToken cancellationToken = default);

// 获取所有工作流定义
Task<IEnumerable<WorkflowDefinition>> GetAllWorkflowDefinitionsAsync(CancellationToken cancellationToken = default);
```

##### 执行状态查询
```csharp
// 获取执行状态
Task<WorkflowExecutionStatus?> GetExecutionStatusAsync(string executionId, CancellationToken cancellationToken = default);

// 获取执行历史
Task<WorkflowExecutionHistory> GetExecutionHistoryAsync(string workflowId, int pageNumber = 1, int pageSize = 10, CancellationToken cancellationToken = default);

// 获取性能统计
Task<WorkflowPerformanceStats> GetPerformanceStatsAsync(string workflowId, TimeSpan? timeRange = null, CancellationToken cancellationToken = default);
```

#### 事件
```csharp
event EventHandler<WorkflowExecutionStartedEventArgs>? ExecutionStarted;    // 执行开始事件
event EventHandler<WorkflowExecutionCompletedEventArgs>? ExecutionCompleted; // 执行完成事件
event EventHandler<WorkflowExecutionFailedEventArgs>? ExecutionFailed;      // 执行失败事件
```

### **3. INodeExecutor - 节点执行器接口**

#### 基本信息
```csharp
namespace FlowCustomV1.Core.Interfaces;
public interface INodeExecutor
```

#### 核心方法

##### 节点执行
```csharp
// 执行节点
Task<NodeExecutionResult> ExecuteAsync(NodeExecutionContext context, CancellationToken cancellationToken = default);

// 验证节点配置
Task<NodeValidationResult> ValidateAsync(NodeConfiguration configuration, CancellationToken cancellationToken = default);

// 暂停节点执行
Task PauseAsync(string executionId, CancellationToken cancellationToken = default);

// 恢复节点执行
Task ResumeAsync(string executionId, CancellationToken cancellationToken = default);

// 停止节点执行
Task StopAsync(string executionId, CancellationToken cancellationToken = default);
```

##### 执行器管理
```csharp
// 注册执行器
Task RegisterExecutorAsync(string executorType, INodeExecutor executor, CancellationToken cancellationToken = default);

// 注销执行器
Task UnregisterExecutorAsync(string executorType, CancellationToken cancellationToken = default);

// 获取支持的执行器类型
Task<IEnumerable<string>> GetSupportedExecutorTypesAsync(CancellationToken cancellationToken = default);
```

##### 状态查询
```csharp
// 获取执行状态
Task<NodeExecutionState> GetExecutionStateAsync(string executionId, CancellationToken cancellationToken = default);

// 获取执行结果
Task<NodeExecutionResult?> GetExecutionResultAsync(string executionId, CancellationToken cancellationToken = default);
```

#### 事件
```csharp
event EventHandler<NodeExecutionStatusChangedEventArgs>? StatusChanged;     // 状态变化事件
event EventHandler<NodeExecutionCompletedEventArgs>? ExecutionCompleted;    // 执行完成事件
event EventHandler<NodeExecutionFailedEventArgs>? ExecutionFailed;          // 执行失败事件
```

---

## 🔧 **基础服务接口**

### **4. ILoggingService - 日志服务接口**

#### 基本信息
```csharp
namespace FlowCustomV1.Core.Interfaces;
public interface ILoggingService
```

#### 核心方法
```csharp
// 日志记录方法
void LogDebug(string message, params object[] args);
void LogInformation(string message, params object[] args);
void LogWarning(string message, params object[] args);
void LogError(string message, params object[] args);
void LogError(Exception exception, string message, params object[] args);
void LogCritical(string message, params object[] args);
void LogCritical(Exception exception, string message, params object[] args);

// 日志级别检查
bool IsEnabled(LogLevel logLevel);

// 作用域管理
IDisposable BeginScope<TState>(TState state);
```

### **5. IConfigurationService - 配置服务接口**

#### 基本信息
```csharp
namespace FlowCustomV1.Core.Interfaces;
public interface IConfigurationService
```

#### 核心方法
```csharp
// 配置值获取
string? GetValue(string key);
string GetValue(string key, string defaultValue);
T GetValue<T>(string key);
T GetValue<T>(string key, T defaultValue);

// 配置节操作
IConfigurationSection GetSection(string key);
IEnumerable<string> GetKeys();
IEnumerable<string> GetKeys(string prefix);

// 配置验证
bool Exists(string key);
ConfigurationValidationResult ValidateConfiguration(IEnumerable<string> requiredKeys);
```

---

## 📊 **数据模型**

### **核心模型类**

#### NodeInfo - 节点信息
```csharp
public class NodeInfo
{
    public string NodeId { get; set; }              // 节点ID
    public string NodeName { get; set; }            // 节点名称
    public string ClusterName { get; set; }         // 集群名称
    public NodeMode Mode { get; set; }              // 节点模式
    public NodeStatus Status { get; set; }          // 节点状态
    public NetworkInfo Network { get; set; }        // 网络信息
    public NodeCapabilities Capabilities { get; set; } // 节点能力
    public NodeLoad Load { get; set; }              // 节点负载
    public HealthStatus Health { get; set; }        // 健康状态
    public Timestamps Timestamps { get; set; }      // 时间戳
}
```

#### WorkflowDefinition - 工作流定义
```csharp
public class WorkflowDefinition
{
    public string Id { get; set; }                  // 工作流ID
    public string Name { get; set; }                // 工作流名称
    public string Description { get; set; }         // 描述
    public string Version { get; set; }             // 版本
    public List<WorkflowNode> Nodes { get; set; }   // 节点列表
    public List<WorkflowConnection> Connections { get; set; } // 连接列表
}
```

#### ClusterMessage - 集群消息
```csharp
public class ClusterMessage
{
    public string MessageId { get; set; }           // 消息ID
    public string MessageType { get; set; }         // 消息类型
    public string SenderId { get; set; }            // 发送者ID
    public string? TargetId { get; set; }           // 目标ID
    public MessagePriority Priority { get; set; }   // 消息优先级
    public DateTime CreatedAt { get; set; }         // 创建时间
}
```

---

## 🚀 **使用示例**

### **集群服务使用示例**
```csharp
// 初始化集群服务
var nodeInfo = new NodeInfo
{
    NodeId = "node-001",
    NodeName = "Worker-001",
    Mode = NodeMode.Worker,
    Status = NodeStatus.Healthy
};

await clusterService.InitializeAsync("my-cluster", nodeInfo);
await clusterService.StartAsync();

// 发现节点
var nodes = await clusterService.GetAllNodesAsync();
var bestNode = await clusterService.SelectBestNodeAsync();

// 发送心跳
await clusterService.SendHeartbeatAsync();
```

### **工作流引擎使用示例**
```csharp
// 注册工作流
var workflow = new WorkflowDefinition
{
    Id = "workflow-001",
    Name = "Sample Workflow",
    Nodes = new List<WorkflowNode> { /* 节点定义 */ }
};

await workflowEngine.RegisterWorkflowAsync(workflow);

// 执行工作流
var context = new WorkflowExecutionContext
{
    WorkflowId = "workflow-001",
    ExecutionId = Guid.NewGuid().ToString()
};

var result = await workflowEngine.ExecuteWorkflowAsync("workflow-001", context);
```

### **依赖注入配置示例**
```csharp
// 注册所有核心服务
services.AddFlowCustomV1Core(configuration);

// 或者单独注册服务
services.AddFlowCustomV1Logging();
services.AddFlowCustomV1Configuration(configuration);
services.AddFlowCustomV1ClusterService();
services.AddFlowCustomV1WorkflowEngine();
services.AddFlowCustomV1NodeExecutor();
```

---

## 🎨 **Designer节点服务接口 (v0.0.1.4新增)**

### **1. WorkflowDesignerController - 工作流设计API控制器**

#### 基本信息
```csharp
[ApiController]
[Route("api/[controller]")]
public class WorkflowDesignerController : ControllerBase
```

#### 工作流管理接口

##### 创建工作流
```csharp
[HttpPost]
public async Task<ActionResult<WorkflowDefinition>> CreateWorkflowAsync([FromBody] CreateWorkflowRequest request)
```
- **功能**: 创建新的工作流定义
- **参数**: CreateWorkflowRequest - 工作流创建请求
- **返回**: WorkflowDefinition - 创建的工作流定义
- **状态码**: 201 Created / 400 Bad Request

##### 获取工作流
```csharp
[HttpGet("{workflowId}")]
public async Task<ActionResult<WorkflowDefinition>> GetWorkflowAsync(string workflowId)
```
- **功能**: 根据ID获取工作流定义
- **参数**: workflowId - 工作流ID
- **返回**: WorkflowDefinition - 工作流定义
- **状态码**: 200 OK / 404 Not Found

##### 更新工作流
```csharp
[HttpPut("{workflowId}")]
public async Task<ActionResult<WorkflowDefinition>> UpdateWorkflowAsync(string workflowId, [FromBody] UpdateWorkflowRequest request)
```
- **功能**: 更新工作流定义
- **参数**: workflowId - 工作流ID, UpdateWorkflowRequest - 更新请求
- **返回**: WorkflowDefinition - 更新后的工作流定义
- **状态码**: 200 OK / 404 Not Found / 400 Bad Request

##### 删除工作流
```csharp
[HttpDelete("{workflowId}")]
public async Task<ActionResult> DeleteWorkflowAsync(string workflowId)
```
- **功能**: 删除工作流定义
- **参数**: workflowId - 工作流ID
- **返回**: 无内容
- **状态码**: 204 No Content / 404 Not Found

#### 版本管理接口

##### 获取工作流版本历史
```csharp
[HttpGet("{workflowId}/versions")]
public async Task<ActionResult<IReadOnlyList<WorkflowVersion>>> GetWorkflowVersionsAsync(string workflowId)
```
- **功能**: 获取工作流的版本历史
- **参数**: workflowId - 工作流ID
- **返回**: IReadOnlyList<WorkflowVersion> - 版本列表
- **状态码**: 200 OK / 404 Not Found

##### 创建工作流版本
```csharp
[HttpPost("{workflowId}/versions")]
public async Task<ActionResult<WorkflowVersion>> CreateVersionAsync(string workflowId, [FromBody] CreateVersionRequest request)
```
- **功能**: 为工作流创建新版本
- **参数**: workflowId - 工作流ID, CreateVersionRequest - 版本创建请求
- **返回**: WorkflowVersion - 创建的版本
- **状态码**: 201 Created / 404 Not Found / 400 Bad Request

### **2. CollaborationController - 协作API控制器**

#### 基本信息
```csharp
[ApiController]
[Route("api/[controller]")]
public class CollaborationController : ControllerBase
```

#### 协作会话管理

##### 创建协作会话
```csharp
[HttpPost("sessions")]
public async Task<ActionResult<CollaborationSession>> CreateSessionAsync([FromBody] CreateCollaborationSessionRequest request)
```
- **功能**: 创建新的协作会话
- **参数**: CreateCollaborationSessionRequest - 会话创建请求
- **返回**: CollaborationSession - 创建的协作会话
- **状态码**: 201 Created / 400 Bad Request

##### 加入协作会话
```csharp
[HttpPost("sessions/{sessionId}/join")]
public async Task<ActionResult<CollaboratorInfo>> JoinSessionAsync(string sessionId, [FromBody] JoinSessionRequest request)
```
- **功能**: 加入协作会话
- **参数**: sessionId - 会话ID, JoinSessionRequest - 加入请求
- **返回**: CollaboratorInfo - 协作者信息
- **状态码**: 200 OK / 404 Not Found / 400 Bad Request

##### 离开协作会话
```csharp
[HttpPost("sessions/{sessionId}/leave")]
public async Task<ActionResult> LeaveSessionAsync(string sessionId, [FromBody] LeaveSessionRequest request)
```
- **功能**: 离开协作会话
- **参数**: sessionId - 会话ID, LeaveSessionRequest - 离开请求
- **返回**: 无内容
- **状态码**: 204 No Content / 404 Not Found

#### 实时操作同步

##### 广播设计操作
```csharp
[HttpPost("sessions/{sessionId}/operations")]
public async Task<ActionResult> BroadcastOperationAsync(string sessionId, [FromBody] DesignOperation operation)
```
- **功能**: 广播设计操作到协作会话
- **参数**: sessionId - 会话ID, DesignOperation - 设计操作
- **返回**: 无内容
- **状态码**: 204 No Content / 404 Not Found

##### 检测操作冲突
```csharp
[HttpPost("sessions/{sessionId}/conflicts/detect")]
public async Task<ActionResult<ConflictDetectionResult>> DetectConflictAsync(string sessionId, [FromBody] DesignOperation operation)
```
- **功能**: 检测设计操作是否存在冲突
- **参数**: sessionId - 会话ID, DesignOperation - 设计操作
- **返回**: ConflictDetectionResult - 冲突检测结果
- **状态码**: 200 OK / 404 Not Found

### **3. 数据模型定义**

#### WorkflowDefinition - 工作流定义
```csharp
public class WorkflowDefinition
{
    public string WorkflowId { get; set; }              // 工作流ID
    public string Name { get; set; }                    // 工作流名称
    public string Description { get; set; }             // 工作流描述
    public string Version { get; set; }                 // 工作流版本
    public string Author { get; set; }                  // 工作流作者
    public List<WorkflowNode> Nodes { get; set; }       // 工作流节点列表
    public List<WorkflowConnection> Connections { get; set; } // 工作流连接列表
    public WorkflowConfiguration Configuration { get; set; } // 工作流配置
    public HashSet<string> Tags { get; set; }           // 工作流标签
    public Dictionary<string, object> Metadata { get; set; } // 工作流元数据
    public DateTime CreatedAt { get; set; }             // 创建时间
    public DateTime LastModifiedAt { get; set; }        // 最后修改时间
}
```

#### WorkflowTemplate - 工作流模板
```csharp
public class WorkflowTemplate
{
    public string TemplateId { get; set; }              // 模板ID
    public string Name { get; set; }                    // 模板名称
    public string Description { get; set; }             // 模板描述
    public string Category { get; set; }                // 模板分类
    public string Version { get; set; }                 // 模板版本
    public string Author { get; set; }                  // 模板作者
    public List<WorkflowNode> DefaultNodes { get; set; } // 默认节点列表
    public List<WorkflowConnection> DefaultConnections { get; set; } // 默认连接列表
    public HashSet<string> Tags { get; set; }           // 模板标签
    public bool IsPublic { get; set; }                  // 是否公共模板
    public bool IsBuiltIn { get; set; }                 // 是否内置模板
    public int UsageCount { get; set; }                 // 使用次数
    public double Rating { get; set; }                  // 模板评分
    public DateTime CreatedAt { get; set; }             // 创建时间
    public DateTime LastModifiedAt { get; set; }        // 最后修改时间
}
```

#### CollaborationSession - 协作会话
```csharp
public class CollaborationSession
{
    public string SessionId { get; set; }               // 会话ID
    public string WorkflowId { get; set; }              // 工作流ID
    public string Name { get; set; }                    // 会话名称
    public string Description { get; set; }             // 会话描述
    public SessionStatus Status { get; set; }           // 会话状态
    public List<CollaboratorInfo> Collaborators { get; set; } // 协作者列表
    public CollaborationSettings Settings { get; set; } // 协作设置
    public DateTime CreatedAt { get; set; }             // 创建时间
    public DateTime LastActivityAt { get; set; }        // 最后活动时间
}
```

---

**文档版本**: v0.0.1.4
**最后更新**: 2025-01-05
**下次更新**: 新增API接口时更新
