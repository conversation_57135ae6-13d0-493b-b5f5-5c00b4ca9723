using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;

namespace FlowCustomV1.Infrastructure.Data;

/// <summary>
/// 设计时数据库上下文工厂
/// 用于Entity Framework工具在设计时创建DbContext实例
/// </summary>
public class WorkflowDbContextFactory : IDesignTimeDbContextFactory<WorkflowDbContext>
{
    /// <summary>
    /// 创建数据库上下文
    /// </summary>
    /// <param name="args">命令行参数</param>
    /// <returns>数据库上下文</returns>
    public WorkflowDbContext CreateDbContext(string[] args)
    {
        var optionsBuilder = new DbContextOptionsBuilder<WorkflowDbContext>();

        // 默认使用SQLite进行迁移生成
        // 在实际运行时会根据配置选择不同的数据库
        optionsBuilder.UseSqlite("Data Source=workflow_design.db");

        return new WorkflowDbContext(optionsBuilder.Options);
    }
}
