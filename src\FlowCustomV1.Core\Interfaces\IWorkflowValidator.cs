using FlowCustomV1.Core.Models.Workflow;

namespace FlowCustomV1.Core.Interfaces;

/// <summary>
/// 工作流验证接口
/// 提供工作流定义和执行的验证功能
/// </summary>
public interface IWorkflowValidator
{
    /// <summary>
    /// 验证工作流定义
    /// </summary>
    /// <param name="workflowDefinition">工作流定义</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    Task<WorkflowValidationResult> ValidateWorkflowDefinitionAsync(WorkflowDefinition workflowDefinition, CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证节点定义
    /// </summary>
    /// <param name="nodeDefinition">节点定义</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    Task<NodeValidationResult> ValidateNodeDefinitionAsync(NodeDefinition nodeDefinition, CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证工作流节点
    /// </summary>
    /// <param name="workflowNode">工作流节点</param>
    /// <param name="nodeDefinition">节点定义</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    Task<NodeValidationResult> ValidateWorkflowNodeAsync(WorkflowNode workflowNode, NodeDefinition nodeDefinition, CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证工作流连接
    /// </summary>
    /// <param name="connection">工作流连接</param>
    /// <param name="sourceNode">源节点</param>
    /// <param name="targetNode">目标节点</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    Task<ConnectionValidationResult> ValidateWorkflowConnectionAsync(WorkflowConnection connection, WorkflowNode sourceNode, WorkflowNode targetNode, CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证工作流参数
    /// </summary>
    /// <param name="parameters">参数值</param>
    /// <param name="parameterDefinitions">参数定义</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    Task<ParameterValidationResult> ValidateParametersAsync(Dictionary<string, object> parameters, IEnumerable<WorkflowParameterDefinition> parameterDefinitions, CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证节点配置
    /// </summary>
    /// <param name="configuration">节点配置</param>
    /// <param name="nodeDefinition">节点定义</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    Task<ConfigurationValidationResult> ValidateNodeConfigurationAsync(NodeConfiguration configuration, NodeDefinition nodeDefinition, CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证工作流执行前置条件
    /// </summary>
    /// <param name="workflowDefinition">工作流定义</param>
    /// <param name="inputParameters">输入参数</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    Task<ExecutionValidationResult> ValidateExecutionPreconditionsAsync(WorkflowDefinition workflowDefinition, Dictionary<string, object> inputParameters, CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证工作流循环依赖
    /// </summary>
    /// <param name="workflowDefinition">工作流定义</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    Task<CyclicDependencyValidationResult> ValidateCyclicDependenciesAsync(WorkflowDefinition workflowDefinition, CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证节点资源需求
    /// </summary>
    /// <param name="resourceRequirements">资源需求</param>
    /// <param name="availableResources">可用资源</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    Task<ResourceValidationResult> ValidateResourceRequirementsAsync(NodeResourceRequirements resourceRequirements, Dictionary<string, object> availableResources, CancellationToken cancellationToken = default);

    /// <summary>
    /// 注册自定义验证规则
    /// </summary>
    /// <param name="ruleName">规则名称</param>
    /// <param name="validationRule">验证规则</param>
    void RegisterValidationRule(string ruleName, IValidationRule validationRule);

    /// <summary>
    /// 移除自定义验证规则
    /// </summary>
    /// <param name="ruleName">规则名称</param>
    /// <returns>是否成功移除</returns>
    bool RemoveValidationRule(string ruleName);

    /// <summary>
    /// 获取所有注册的验证规则
    /// </summary>
    /// <returns>验证规则字典</returns>
    IReadOnlyDictionary<string, IValidationRule> GetRegisteredValidationRules();
}

/// <summary>
/// 验证规则接口
/// </summary>
public interface IValidationRule
{
    /// <summary>
    /// 规则名称
    /// </summary>
    string RuleName { get; }

    /// <summary>
    /// 规则描述
    /// </summary>
    string Description { get; }

    /// <summary>
    /// 执行验证
    /// </summary>
    /// <param name="context">验证上下文</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    Task<ValidationResult> ValidateAsync(ValidationContext context, CancellationToken cancellationToken = default);
}

/// <summary>
/// 验证上下文
/// </summary>
public class ValidationContext
{
    /// <summary>
    /// 验证目标
    /// </summary>
    public object Target { get; set; } = null!;

    /// <summary>
    /// 验证类型
    /// </summary>
    public string ValidationType { get; set; } = string.Empty;

    /// <summary>
    /// 上下文数据
    /// </summary>
    public Dictionary<string, object> Data { get; set; } = new();

    /// <summary>
    /// 验证选项
    /// </summary>
    public ValidationOptions Options { get; set; } = new();
}

/// <summary>
/// 验证选项
/// </summary>
public class ValidationOptions
{
    /// <summary>
    /// 是否启用严格模式
    /// </summary>
    public bool StrictMode { get; set; } = false;

    /// <summary>
    /// 是否跳过警告
    /// </summary>
    public bool SkipWarnings { get; set; } = false;

    /// <summary>
    /// 最大验证深度
    /// </summary>
    public int MaxValidationDepth { get; set; } = 10;

    /// <summary>
    /// 自定义验证参数
    /// </summary>
    public Dictionary<string, object> CustomParameters { get; set; } = new();
}



/// <summary>
/// 连接验证结果
/// </summary>
public class ConnectionValidationResult : ValidationResult
{
    /// <summary>
    /// 连接ID
    /// </summary>
    public string ConnectionId { get; set; } = string.Empty;

    /// <summary>
    /// 源节点ID
    /// </summary>
    public string SourceNodeId { get; set; } = string.Empty;

    /// <summary>
    /// 目标节点ID
    /// </summary>
    public string TargetNodeId { get; set; } = string.Empty;
}

/// <summary>
/// 参数验证结果
/// </summary>
public class ParameterValidationResult : ValidationResult
{
    /// <summary>
    /// 无效参数列表
    /// </summary>
    public List<string> InvalidParameters { get; set; } = new();

    /// <summary>
    /// 缺失参数列表
    /// </summary>
    public List<string> MissingParameters { get; set; } = new();
}

/// <summary>
/// 配置验证结果
/// </summary>
public class ConfigurationValidationResult : ValidationResult
{
    /// <summary>
    /// 无效配置项
    /// </summary>
    public List<string> InvalidConfigurations { get; set; } = new();
}

/// <summary>
/// 执行验证结果
/// </summary>
public class ExecutionValidationResult : ValidationResult
{
    /// <summary>
    /// 是否可以执行
    /// </summary>
    public bool CanExecute { get; set; } = true;

    /// <summary>
    /// 阻塞原因
    /// </summary>
    public List<string> BlockingReasons { get; set; } = new();
}

/// <summary>
/// 循环依赖验证结果
/// </summary>
public class CyclicDependencyValidationResult : ValidationResult
{
    /// <summary>
    /// 是否存在循环依赖
    /// </summary>
    public bool HasCyclicDependency { get; set; } = false;

    /// <summary>
    /// 循环路径
    /// </summary>
    public List<List<string>> CyclicPaths { get; set; } = new();
}

/// <summary>
/// 资源验证结果
/// </summary>
public class ResourceValidationResult : ValidationResult
{
    /// <summary>
    /// 资源是否充足
    /// </summary>
    public bool ResourcesSufficient { get; set; } = true;

    /// <summary>
    /// 不足的资源
    /// </summary>
    public List<string> InsufficientResources { get; set; } = new();
}
