# FlowCustomV1 参数配置体系设计文档

## 📋 文档信息

| 项目信息 | 详细内容 |
|---------|---------|
| **项目名称** | FlowCustomV1 工作流自动化系统 |
| **文档类型** | 参数配置体系设计文档 |
| **创建日期** | 2025-09-07 |
| **版本** | v1.0.0 |
| **适用范围** | 开发、测试、生产环境 |

---

## 🎯 配置体系概述

FlowCustomV1 采用分层配置架构，支持多环境部署，完全消除硬编码配置，通过配置文件和环境变量实现灵活的参数管理。

### 核心设计原则

1. **无硬编码原则**：所有配置参数都通过配置文件或环境变量设置
2. **分层覆盖原则**：环境变量 > 环境配置文件 > 基础配置文件 > 默认值
3. **环境隔离原则**：测试、生产环境配置完全独立
4. **统一格式原则**：使用.NET配置系统标准格式

---

## 🏗️ 配置架构设计

### 配置层次结构

```
┌─────────────────────────────────────────────────────────────┐
│                    环境变量 (最高优先级)                      │
│              Docker Compose / Kubernetes 设置               │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                环境特定配置文件 (高优先级)                    │
│        appsettings.Test.json / appsettings.Production.json  │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    基础配置文件 (低优先级)                    │
│                    appsettings.json                         │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    代码默认值 (最低优先级)                    │
│                  配置类的初始化值                            │
└─────────────────────────────────────────────────────────────┘
```

### 配置文件组织

```
src/FlowCustomV1.Api/
├── appsettings.json                    # 基础配置（空配置，避免硬编码）
├── appsettings.Development.json        # 开发环境配置
└── appsettings.Production.json         # 生产环境配置（如果需要）

tests/docker/config/
├── appsettings.Test.json              # 测试环境配置
└── appsettings.Production.json        # 生产环境配置模板
```

---

## ⚙️ 配置参数分类

### 1. 消息系统配置 (NATS)

**配置类**：`NatsConfiguration`
**配置节**：`Nats`

| 参数名 | 环境变量格式 | 类型 | 说明 |
|--------|-------------|------|------|
| Servers | `Nats__Servers__0`, `Nats__Servers__1` | string[] | NATS服务器地址列表 |
| ConnectionName | `Nats__ConnectionName` | string | 连接名称 |
| Username | `Nats__Username` | string | 用户名 |
| Password | `Nats__Password` | string | 密码 |
| ConnectionTimeoutSeconds | `Nats__ConnectionTimeoutSeconds` | int | 连接超时时间 |
| ReconnectIntervalSeconds | `Nats__ReconnectIntervalSeconds` | int | 重连间隔 |
| MaxReconnectAttempts | `Nats__MaxReconnectAttempts` | int | 最大重连次数 |

**示例配置**：
```json
{
  "Nats": {
    "Servers": ["nats://nats:4222"],
    "ConnectionName": "FlowCustomV1-Master-Beijing",
    "ConnectionTimeoutSeconds": 30,
    "ReconnectIntervalSeconds": 2,
    "MaxReconnectAttempts": 10
  }
}
```

**环境变量示例**：
```yaml
- Nats__Servers__0=nats://nats:4222
- Nats__ConnectionName=FlowCustomV1-Master-Beijing
- Nats__ConnectionTimeoutSeconds=30
```

### 2. 数据库配置

**配置类**：`DatabaseConfiguration`
**配置节**：`Database`

| 参数名 | 环境变量格式 | 类型 | 说明 |
|--------|-------------|------|------|
| Provider | `Database__Provider` | string | 数据库提供程序 |
| ConnectionString | `Database__ConnectionString` | string | 连接字符串 |
| CommandTimeout | `Database__CommandTimeout` | int | 命令超时时间 |
| EnableRetryOnFailure | `Database__EnableRetryOnFailure` | bool | 启用失败重试 |
| MaxRetryCount | `Database__MaxRetryCount` | int | 最大重试次数 |

**示例配置**：
```json
{
  "Database": {
    "Provider": "MySQL",
    "ConnectionString": "Server=mysql;Database=flowcustom_test;Uid=flowcustom;Pwd=TestPassword123!;",
    "CommandTimeout": 30,
    "EnableRetryOnFailure": true,
    "MaxRetryCount": 3
  }
}
```

### 3. 节点发现配置

**配置类**：`NodeDiscoveryConfiguration`
**配置节**：`NodeDiscovery`

| 参数名 | 环境变量格式 | 类型 | 说明 |
|--------|-------------|------|------|
| ClusterName | `NodeDiscovery__ClusterName` | string | 集群名称 |
| NodeId | `NodeDiscovery__NodeId` | string | 节点ID |
| NodeName | `NodeDiscovery__NodeName` | string | 节点名称 |
| NodeRole | `NodeDiscovery__NodeRole` | string | 节点角色 |
| Region | `NodeDiscovery__Region` | string | 区域 |
| DataCenter | `NodeDiscovery__DataCenter` | string | 数据中心 |
| HeartbeatIntervalSeconds | `NodeDiscovery__HeartbeatIntervalSeconds` | int | 心跳间隔 |

**示例配置**：
```json
{
  "NodeDiscovery": {
    "ClusterName": "FlowCustomV1-Test",
    "NodeId": "master-beijing",
    "NodeName": "MasterBeijing",
    "NodeRole": "Master",
    "Region": "Beijing",
    "DataCenter": "Beijing-DC1",
    "HeartbeatIntervalSeconds": 10
  }
}
```

### 4. 任务分发配置

**配置类**：`TaskDistributionConfiguration`
**配置节**：`TaskDistribution`

| 参数名 | 环境变量格式 | 类型 | 说明 |
|--------|-------------|------|------|
| MaxCandidateNodes | `TaskDistribution__MaxCandidateNodes` | int | 最大候选节点数 |
| MaxConcurrentDistributions | `TaskDistribution__MaxConcurrentDistributions` | int | 最大并发分发数 |
| NodeSelectionTimeoutMs | `TaskDistribution__NodeSelectionTimeoutMs` | int | 节点选择超时 |
| AutoRebalancingEnabled | `TaskDistribution__AutoRebalancingEnabled` | bool | 启用自动重平衡 |
| DefaultStrategy | `TaskDistribution__DefaultStrategy` | string | 默认分发策略 |

### 5. 任务跟踪配置

**配置类**：`TaskTrackingConfiguration`
**配置节**：`TaskTracking`

| 参数名 | 环境变量格式 | 类型 | 说明 |
|--------|-------------|------|------|
| CleanupIntervalMinutes | `TaskTracking__CleanupIntervalMinutes` | int | 清理间隔（分钟） |
| MaxRecentCompletedTasks | `TaskTracking__MaxRecentCompletedTasks` | int | 最大最近完成任务数 |
| EnableDetailedLogging | `TaskTracking__EnableDetailedLogging` | bool | 启用详细日志 |
| TaskStateRetentionDays | `TaskTracking__TaskStateRetentionDays` | int | 任务状态保留天数 |

---

## 🌍 环境配置管理

### 测试环境配置

**文件**：`tests/docker/config/appsettings.Test.json`

**特点**：
- 单节点NATS服务器
- 本地MySQL数据库
- 详细日志记录
- 较短的超时时间
- 启用测试端点

**关键配置**：
```json
{
  "Nats": {
    "Servers": ["nats://nats:4222"],
    "ConnectionName": "FlowCustomV1-Test-Node"
  },
  "Database": {
    "ConnectionString": "Server=mysql;Database=flowcustom_test;Uid=flowcustom;Pwd=TestPassword123!;"
  },
  "NodeDiscovery": {
    "ClusterName": "FlowCustomV1-Test",
    "HeartbeatIntervalSeconds": 10
  },
  "Logging": {
    "LogLevel": {
      "FlowCustomV1": "Debug"
    }
  }
}
```

### 生产环境配置

**文件**：`tests/docker/config/appsettings.Production.json`

**特点**：
- 多节点NATS集群
- 高可用MySQL集群
- 优化的性能参数
- 安全认证启用
- 监控和指标收集

**关键配置**：
```json
{
  "Nats": {
    "Servers": [
      "nats://nats-1:4222",
      "nats://nats-2:4222", 
      "nats://nats-3:4222"
    ],
    "Username": "flowcustom",
    "Password": "flowcustom_production_password"
  },
  "Database": {
    "ConnectionString": "Server=mysql-cluster;Database=flowcustom_production;Uid=flowcustom;Pwd=${MYSQL_PASSWORD};"
  },
  "Security": {
    "EnableAuthentication": true,
    "EnableAuthorization": true,
    "EnableHttps": true
  }
}
```

---

## 🐳 Docker环境配置

### Docker Compose环境变量

**测试环境示例**：
```yaml
environment:
  - ASPNETCORE_ENVIRONMENT=Test
  - ASPNETCORE_URLS=http://+:5000
  # NATS配置
  - Nats__Servers__0=nats://nats:4222
  - Nats__ConnectionName=FlowCustomV1-Master-Beijing
  # 数据库配置
  - Database__ConnectionString=Server=mysql;Database=flowcustom_test;Uid=flowcustom;Pwd=TestPassword123!;
  # 节点发现配置
  - NodeDiscovery__NodeId=master-beijing
  - NodeDiscovery__NodeName=MasterBeijing
  - NodeDiscovery__NodeRole=Master
  - NodeDiscovery__Region=Beijing
  - NodeDiscovery__DataCenter=Beijing-DC1
```

### Kubernetes ConfigMap

**生产环境示例**：
```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: flowcustom-config
data:
  Nats__Servers__0: "nats://nats-1:4222"
  Nats__Servers__1: "nats://nats-2:4222"
  Nats__Servers__2: "nats://nats-3:4222"
  Nats__ConnectionName: "FlowCustomV1-Production-Node"
  Database__Provider: "MySQL"
  NodeDiscovery__ClusterName: "FlowCustomV1-Production"
```

---

## 🔧 配置验证和调试

### 配置验证机制

1. **启动时验证**：应用启动时验证关键配置参数
2. **运行时监控**：监控配置变更和有效性
3. **健康检查**：通过健康检查端点验证配置状态

### 调试工具

**配置调试脚本**：`tests/docker/scripts/debug-config.sh`
```bash
#!/bin/bash
echo "Environment Variables:"
echo "ASPNETCORE_ENVIRONMENT: $ASPNETCORE_ENVIRONMENT"
echo "Nats__Servers__0: $Nats__Servers__0"
echo "Database__ConnectionString: $Database__ConnectionString"

echo "Configuration Files:"
ls -la /app/*.json

echo "Network Connectivity:"
nc -zv nats 4222
nc -zv mysql 3306
```

### 常见问题排查

**问题1：NATS连接失败**
- 检查 `Nats__Servers__0` 环境变量
- 验证网络连通性
- 确认NATS服务器状态

**问题2：数据库连接失败**
- 检查 `Database__ConnectionString` 环境变量
- 验证数据库服务状态
- 确认认证信息正确

**问题3：配置不生效**
- 确认环境变量格式正确（使用双下划线 `__`）
- 检查配置文件加载顺序
- 验证 `ASPNETCORE_ENVIRONMENT` 设置

---

## 📚 最佳实践

### 1. 配置安全

- 敏感信息（密码、密钥）使用环境变量或密钥管理系统
- 生产环境配置文件不包含明文密码
- 使用配置加密和访问控制

### 2. 配置管理

- 版本控制配置文件模板
- 环境特定配置独立管理
- 配置变更需要审核和测试

### 3. 监控和告警

- 监控关键配置参数变更
- 配置错误时及时告警
- 记录配置加载和验证日志

### 4. 文档维护

- 及时更新配置文档
- 记录配置变更历史
- 提供配置示例和说明

---

## 🔄 配置变更流程

### 开发环境
1. 修改配置文件
2. 本地测试验证
3. 提交代码审查

### 测试环境
1. 更新测试配置文件
2. 重新构建Docker镜像
3. 部署和验证

### 生产环境
1. 准备生产配置
2. 灰度部署验证
3. 全量部署上线
4. 监控和回滚准备

---

## 📊 配置监控指标

- 配置加载成功率
- 配置验证失败次数
- 关键服务连接状态
- 配置热更新成功率
- 配置相关错误日志数量

---

---

## 🛠️ 配置工具和脚本

### 配置生成工具

**配置模板生成器**：`tools/config-generator.ps1`
```powershell
# 生成环境特定配置文件
param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("Test", "Staging", "Production")]
    [string]$Environment,

    [Parameter(Mandatory=$true)]
    [string]$OutputPath
)

# 根据环境生成配置文件
$template = Get-Content "templates/appsettings.template.json" | ConvertFrom-Json
$template.Nats.Servers = @("nats://nats-$Environment.example.com:4222")
$template | ConvertTo-Json -Depth 10 | Out-File "$OutputPath/appsettings.$Environment.json"
```

### 配置验证工具

**配置验证脚本**：`tools/validate-config.ps1`
```powershell
# 验证配置文件有效性
param([string]$ConfigPath)

$config = Get-Content $ConfigPath | ConvertFrom-Json

# 验证必需配置
$requiredPaths = @(
    "Nats.Servers",
    "Database.ConnectionString",
    "NodeDiscovery.ClusterName"
)

foreach ($path in $requiredPaths) {
    $value = $config
    foreach ($segment in $path.Split('.')) {
        $value = $value.$segment
    }
    if (-not $value) {
        Write-Error "Missing required configuration: $path"
    }
}
```

### 环境变量导出工具

**环境变量生成器**：`tools/export-env.sh`
```bash
#!/bin/bash
# 从配置文件生成环境变量

CONFIG_FILE=$1
ENVIRONMENT=$2

# 解析JSON并生成环境变量
jq -r '
  def flatten(prefix):
    . as $in
    | reduce keys[] as $key (
        {};
        . + (
          if ($in[$key] | type) == "object" then
            ($in[$key] | flatten(prefix + $key + "__"))
          else
            {(prefix + $key): $in[$key]}
          end
        )
      );
  flatten("") | to_entries[] | "\(.key)=\(.value)"
' "$CONFIG_FILE" > ".env.$ENVIRONMENT"
```

---

## 📋 配置检查清单

### 部署前检查

**测试环境部署检查清单**：
- [ ] 配置文件语法正确
- [ ] 必需配置项已设置
- [ ] 服务地址可访问
- [ ] 认证信息有效
- [ ] 日志级别适当
- [ ] 测试端点已启用

**生产环境部署检查清单**：
- [ ] 敏感信息已加密
- [ ] 性能参数已优化
- [ ] 安全功能已启用
- [ ] 监控配置已设置
- [ ] 备份策略已配置
- [ ] 回滚方案已准备

### 运行时检查

**健康检查项目**：
- [ ] NATS连接状态
- [ ] 数据库连接状态
- [ ] 节点发现功能
- [ ] 消息传递功能
- [ ] 任务分发功能
- [ ] 配置热更新功能

---

## 🔍 故障排查指南

### 配置相关故障分类

**1. 连接故障**
```
症状：服务无法连接到NATS或数据库
原因：配置错误、网络问题、服务未启动
排查：检查配置、测试连通性、查看服务状态
```

**2. 认证故障**
```
症状：认证失败、权限不足
原因：用户名密码错误、权限配置错误
排查：验证认证信息、检查权限设置
```

**3. 性能故障**
```
症状：响应慢、超时、资源不足
原因：配置参数不当、资源限制
排查：检查超时设置、资源配置、性能参数
```

### 故障排查步骤

**步骤1：收集信息**
```bash
# 查看应用日志
docker logs flowcustom-app --tail 100

# 查看配置信息
docker exec flowcustom-app cat /app/appsettings.Test.json

# 查看环境变量
docker exec flowcustom-app env | grep -E "(Nats|Database|NodeDiscovery)"
```

**步骤2：验证配置**
```bash
# 验证NATS连接
docker exec flowcustom-app nc -zv nats 4222

# 验证数据库连接
docker exec flowcustom-app nc -zv mysql 3306

# 验证配置文件格式
docker exec flowcustom-app python -m json.tool /app/appsettings.Test.json
```

**步骤3：测试功能**
```bash
# 测试健康检查
curl http://localhost:5001/health

# 测试API端点
curl http://localhost:5001/api/nodes

# 测试消息传递
curl -X POST http://localhost:5001/api/test/message
```

---

## 📈 配置优化建议

### 性能优化

**NATS配置优化**：
```json
{
  "Nats": {
    "ConnectionTimeoutSeconds": 30,
    "ReconnectIntervalSeconds": 2,
    "MaxReconnectAttempts": 10,
    "PingIntervalSeconds": 120,
    "MaxPingsOutstanding": 2
  }
}
```

**数据库配置优化**：
```json
{
  "Database": {
    "CommandTimeout": 60,
    "MaxRetryCount": 5,
    "MaxRetryDelay": "00:01:00",
    "EnableRetryOnFailure": true
  }
}
```

### 安全优化

**生产环境安全配置**：
```json
{
  "Security": {
    "EnableAuthentication": true,
    "EnableAuthorization": true,
    "EnableHttps": true,
    "EnableRateLimiting": true,
    "RateLimitRequests": 10000,
    "RateLimitWindowMinutes": 1
  }
}
```

### 监控优化

**监控配置建议**：
```json
{
  "Monitoring": {
    "EnableMetrics": true,
    "MetricsEndpoint": "/metrics",
    "EnableTracing": true,
    "TracingSamplingRate": 0.01,
    "MetricsRetentionDays": 30
  }
}
```

---

## 🔄 版本升级指南

### 配置兼容性

**v1.0.0 → v1.1.0**：
- 新增：`TaskExecution.EnableTaskIsolation` 配置项
- 变更：`Nats.ConnectionTimeout` 单位从毫秒改为秒
- 废弃：`NodeDiscovery.LegacyMode` 配置项

**升级步骤**：
1. 备份现有配置文件
2. 更新配置模板
3. 迁移配置参数
4. 验证配置有效性
5. 测试功能完整性

### 配置迁移工具

**配置迁移脚本**：`tools/migrate-config.ps1`
```powershell
# 配置文件版本迁移
param(
    [string]$SourceConfig,
    [string]$TargetVersion,
    [string]$OutputConfig
)

# 读取源配置
$config = Get-Content $SourceConfig | ConvertFrom-Json

# 根据目标版本进行迁移
switch ($TargetVersion) {
    "1.1.0" {
        # 添加新配置项
        $config.TaskExecution.EnableTaskIsolation = $false

        # 转换单位
        $config.Nats.ConnectionTimeoutSeconds = $config.Nats.ConnectionTimeout / 1000
        $config.Nats.PSObject.Properties.Remove('ConnectionTimeout')

        # 移除废弃配置
        $config.NodeDiscovery.PSObject.Properties.Remove('LegacyMode')
    }
}

# 保存迁移后的配置
$config | ConvertTo-Json -Depth 10 | Out-File $OutputConfig
```

---

**FlowCustomV1 参数配置体系为系统提供了灵活、安全、可维护的配置管理能力，支持多环境部署和运维需求。通过完善的工具链和最佳实践，确保配置管理的高效性和可靠性。**
