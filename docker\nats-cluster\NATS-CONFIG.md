# FlowCustomV1 NATS集群配置说明

## 📋 配置文件结构

```
docker/nats-cluster/config/
├── development/          # 开发环境配置
│   └── nats-1.conf      # 单节点NATS配置
├── testing/             # 测试环境配置
│   ├── nats-1.conf      # 测试集群节点1
│   ├── nats-2.conf      # 测试集群节点2
│   └── nats-3.conf      # 测试集群节点3
└── production/          # 生产环境配置
    ├── nats-1.conf      # 生产集群节点1
    ├── nats-2.conf      # 生产集群节点2
    └── nats-3.conf      # 生产集群节点3
```

## 🎯 环境配置特点

### 开发环境 (Development)
- **节点数量**: 1个单节点
- **NATS版本**: 2.11.8-alpine
- **端口**: 4222 (NATS), 8222 (监控)
- **JetStream**: 启用，内存存储128MB，文件存储512MB
- **认证**: 简单用户名密码认证
- **日志**: 详细调试日志 (debug: true, trace: true)
- **TLS**: 禁用
- **用途**: 本地开发和调试

### 测试环境 (Testing)
- **节点数量**: 3节点集群
- **NATS版本**: 2.11.8-alpine
- **端口**: 24222-24224 (NATS), 28222-28224 (监控) [+20000]
- **JetStream**: 启用，内存存储256MB，文件存储1GB
- **认证**: 测试用户认证
- **日志**: 调试日志 (debug: true, trace: false)
- **TLS**: 禁用
- **集群名**: flowcustom-test-cluster
- **域**: flowcustom-test
- **用途**: Docker集群测试和集成测试

### 生产环境 (Production)
- **节点数量**: 3节点集群
- **NATS版本**: 2.11.8-alpine
- **端口**: 4222 (NATS), 8222 (监控)
- **JetStream**: 启用，内存存储2GB，文件存储100GB
- **认证**: 安全认证 (待部署时配置)
- **日志**: 最小日志 (debug: false, trace: false)
- **TLS**: 必需 (证书待配置)
- **集群名**: flowcustom-prod-cluster
- **域**: flowcustom-prod
- **用途**: 生产部署

## 🚀 使用方法

### 开发环境
```bash
# 启动开发环境
python docker/development/start-dev-env.py start

# 连接NATS
nats://localhost:4222
```

### 测试环境
```bash
# 启动测试集群
python docker/cluster-test/start-cluster-test.py start

# 连接NATS集群
nats://localhost:24222,localhost:24223,localhost:24224
```

### 生产环境
```bash
# 生产环境需要先配置证书和密码
# 编辑 docker/nats-cluster/config/production/*.conf
# 替换所有 "TO_BE_CONFIGURED_ON_DEPLOYMENT" 占位符

# 然后启动生产集群
docker-compose -f docker/production/docker-compose.yml up -d
```

## 📊 性能配置对比

| 环境 | 最大连接数 | 最大订阅数 | 最大负载 | 内存存储 | 文件存储 |
|------|-----------|-----------|----------|----------|----------|
| **开发** | 100 | 1,000 | 1MB | 128MB | 512MB |
| **测试** | 1,000 | 10,000 | 1MB | 256MB | 1GB |
| **生产** | 10,000 | 100,000 | 8MB | 2GB | 100GB |

## 🔍 监控端点

- **开发环境**: http://localhost:8222
- **测试环境**: http://localhost:28222-28224
- **生产环境**: http://localhost:8222 (需要认证)

## 🔐 安全配置

### 开发环境
- 无TLS加密
- 简单密码认证
- 详细日志记录

### 测试环境
- 无TLS加密 (测试环境)
- 基本密码认证
- 调试日志记录

### 生产环境
- **必需TLS加密**
- 强密码认证
- 账户隔离
- 最小日志记录
- 证书验证

## 📝 配置更新日志

- **v0.0.1.8** (2025-09-07) - 创建多环境NATS配置
  - 开发环境: 单节点配置
  - 测试环境: 3节点集群，端口+20000
  - 生产环境: 3节点集群，TLS必需，安全配置
  - NATS版本: 统一使用2.11.8-alpine
