import React from 'react';
import { Tree, Button, Tag, Modal, Form, Input, message } from 'antd';
import { 
  SafetyOutlined, 
  PlusOutlined, 
  EditOutlined,
  DeleteOutlined,
  KeyOutlined
} from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';

// 模拟权限数据
const mockPermissions = [
  {
    title: '工作流管理',
    key: 'workflow',
    children: [
      { title: '查看工作流', key: 'workflow.view' },
      { title: '创建工作流', key: 'workflow.create' },
      { title: '编辑工作流', key: 'workflow.edit' },
      { title: '删除工作流', key: 'workflow.delete' },
      { title: '执行工作流', key: 'workflow.execute' },
    ],
  },
  {
    title: '执行管理',
    key: 'execution',
    children: [
      { title: '查看执行记录', key: 'execution.view' },
      { title: '控制执行', key: 'execution.control' },
      { title: '查看执行日志', key: 'execution.logs' },
    ],
  },
  {
    title: '集群管理',
    key: 'cluster',
    children: [
      { title: '查看集群状态', key: 'cluster.view' },
      { title: '管理节点', key: 'cluster.manage' },
      { title: '集群配置', key: 'cluster.config' },
    ],
  },
  {
    title: '系统管理',
    key: 'system',
    children: [
      { title: '用户管理', key: 'system.users' },
      { title: '角色管理', key: 'system.roles' },
      { title: '系统配置', key: 'system.config' },
      { title: '系统监控', key: 'system.monitor' },
    ],
  },
];

// 模拟角色数据
const mockRoles = [
  {
    id: '1',
    name: 'Admin',
    displayName: '系统管理员',
    description: '拥有系统所有权限',
    permissions: ['workflow.view', 'workflow.create', 'workflow.edit', 'workflow.delete', 'workflow.execute', 
                  'execution.view', 'execution.control', 'execution.logs',
                  'cluster.view', 'cluster.manage', 'cluster.config',
                  'system.users', 'system.roles', 'system.config', 'system.monitor'],
    userCount: 2,
  },
  {
    id: '2',
    name: 'Designer',
    displayName: '工作流设计师',
    description: '负责工作流设计和管理',
    permissions: ['workflow.view', 'workflow.create', 'workflow.edit', 'workflow.execute',
                  'execution.view', 'execution.logs'],
    userCount: 5,
  },
  {
    id: '3',
    name: 'Operator',
    displayName: '系统操作员',
    description: '负责系统运维和监控',
    permissions: ['workflow.view', 'workflow.execute',
                  'execution.view', 'execution.control', 'execution.logs',
                  'cluster.view', 'cluster.manage',
                  'system.monitor'],
    userCount: 3,
  },
  {
    id: '4',
    name: 'Viewer',
    displayName: '查看者',
    description: '只能查看系统信息',
    permissions: ['workflow.view', 'execution.view', 'cluster.view'],
    userCount: 8,
  },
];

const RolePermissions: React.FC = () => {
  const [roles, setRoles] = React.useState(mockRoles);
  const [selectedRole, setSelectedRole] = React.useState<any>(mockRoles[0]);
  const [modalVisible, setModalVisible] = React.useState(false);
  const [editingRole, setEditingRole] = React.useState<any>(null);
  const [form] = Form.useForm();

  const handleRoleSelect = (role: any) => {
    setSelectedRole(role);
  };

  const handleEditRole = (role: any) => {
    setEditingRole(role);
    form.setFieldsValue({
      name: role.name,
      displayName: role.displayName,
      description: role.description,
      permissions: role.permissions,
    });
    setModalVisible(true);
  };

  const handleDeleteRole = (role: any) => {
    if (role.userCount > 0) {
      message.warning('该角色下还有用户，无法删除');
      return;
    }

    Modal.confirm({
      title: '删除角色',
      content: `确定要删除角色 "${role.displayName}" 吗？`,
      okType: 'danger',
      onOk: () => {
        setRoles(prev => prev.filter(r => r.id !== role.id));
        if (selectedRole?.id === role.id) {
          setSelectedRole(roles[0]);
        }
        message.success('角色已删除');
      },
    });
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      if (editingRole) {
        // 更新角色
        setRoles(prev => prev.map(r => 
          r.id === editingRole.id ? { ...r, ...values } : r
        ));
        if (selectedRole?.id === editingRole.id) {
          setSelectedRole({ ...selectedRole, ...values });
        }
        message.success('角色信息已更新');
      } else {
        // 创建新角色
        const newRole = {
          id: Date.now().toString(),
          ...values,
          userCount: 0,
        };
        setRoles(prev => [...prev, newRole]);
        message.success('角色创建成功');
      }
      setModalVisible(false);
      setEditingRole(null);
      form.resetFields();
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  return (
    <div className="page-container">
      <div className="page-header">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="page-title">
              <SafetyOutlined className="mr-2" />
              角色权限管理
            </h1>
            <p className="page-description">管理系统角色和权限分配</p>
          </div>
          <Button 
            type="primary" 
            icon={<PlusOutlined />}
            onClick={() => {
              setEditingRole(null);
              form.resetFields();
              setModalVisible(true);
            }}
          >
            添加角色
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 角色列表 */}
        <div className="lg:col-span-1">
          <ProCard title="系统角色" className="h-full">
            <div className="space-y-3">
              {roles.map(role => (
                <div
                  key={role.id}
                  className={`p-3 border rounded cursor-pointer transition-colors ${
                    selectedRole?.id === role.id 
                      ? 'border-blue-500 bg-blue-50' 
                      : 'border-gray-200 hover:bg-gray-50'
                  }`}
                  onClick={() => handleRoleSelect(role)}
                >
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <div className="font-medium">{role.displayName}</div>
                      <div className="text-sm text-gray-500">{role.name}</div>
                    </div>
                    <Tag color="blue">{role.userCount} 用户</Tag>
                  </div>
                  <div className="text-sm text-gray-600 mb-2">
                    {role.description}
                  </div>
                  <div className="flex justify-end space-x-2">
                    <Button 
                      type="text" 
                      size="small" 
                      icon={<EditOutlined />}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleEditRole(role);
                      }}
                    />
                    <Button 
                      type="text" 
                      size="small" 
                      danger
                      icon={<DeleteOutlined />}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteRole(role);
                      }}
                    />
                  </div>
                </div>
              ))}
            </div>
          </ProCard>
        </div>

        {/* 权限详情 */}
        <div className="lg:col-span-2">
          <ProCard 
            title={
              <div className="flex items-center">
                <KeyOutlined className="mr-2" />
                {selectedRole ? `${selectedRole.displayName} 的权限` : '选择角色查看权限'}
              </div>
            }
            className="h-full"
          >
            {selectedRole && (
              <div>
                <div className="mb-4 p-4 bg-gray-50 rounded">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <div className="text-sm text-gray-500">角色名称</div>
                      <div className="font-medium">{selectedRole.displayName}</div>
                    </div>
                    <div>
                      <div className="text-sm text-gray-500">用户数量</div>
                      <div className="font-medium">{selectedRole.userCount} 个用户</div>
                    </div>
                    <div className="col-span-2">
                      <div className="text-sm text-gray-500">描述</div>
                      <div className="font-medium">{selectedRole.description}</div>
                    </div>
                  </div>
                </div>

                <div className="mb-4">
                  <h3 className="text-lg font-semibold mb-3">权限列表</h3>
                  <Tree
                    checkable
                    checkedKeys={selectedRole.permissions}
                    treeData={mockPermissions}
                    disabled
                    className="bg-gray-50 p-4 rounded"
                  />
                </div>

                <div className="text-center">
                  <Button 
                    type="primary" 
                    icon={<EditOutlined />}
                    onClick={() => handleEditRole(selectedRole)}
                  >
                    编辑角色权限
                  </Button>
                </div>
              </div>
            )}
          </ProCard>
        </div>
      </div>

      {/* 角色编辑模态框 */}
      <Modal
        title={editingRole ? '编辑角色' : '添加角色'}
        open={modalVisible}
        onOk={handleSubmit}
        onCancel={() => {
          setModalVisible(false);
          setEditingRole(null);
          form.resetFields();
        }}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            permissions: [],
          }}
        >
          <Form.Item
            label="角色标识"
            name="name"
            rules={[{ required: true, message: '请输入角色标识' }]}
          >
            <Input placeholder="请输入角色标识（如：Admin）" />
          </Form.Item>

          <Form.Item
            label="显示名称"
            name="displayName"
            rules={[{ required: true, message: '请输入显示名称' }]}
          >
            <Input placeholder="请输入显示名称（如：系统管理员）" />
          </Form.Item>

          <Form.Item
            label="描述"
            name="description"
          >
            <Input.TextArea 
              placeholder="请输入角色描述" 
              rows={3}
            />
          </Form.Item>

          <Form.Item
            label="权限分配"
            name="permissions"
            rules={[{ required: true, message: '请选择权限' }]}
          >
            <Tree
              checkable
              treeData={mockPermissions}
              className="border rounded p-4"
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default RolePermissions;
