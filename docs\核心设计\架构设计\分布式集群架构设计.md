# FlowCustomV1 分布式集群架构设计

## 📋 文档信息

| 文档信息 | 详细内容 |
|---------|---------|
| **文档名称** | 分布式集群架构设计 |
| **目标版本** | v0.0.1.0 |
| **设计理念** | 类似NATS和n8n的分布式架构 |
| **创建日期** | 2025-09-04 |
| **架构类型** | 去中心化分布式集群 |

---

## 🎯 设计目标

### 核心目标
1. **高可用性**: 无单点故障，支持节点故障自动转移
2. **水平扩展**: 支持动态添加/移除节点，线性扩展能力
3. **分布式执行**: 工作流可在任意节点执行，智能负载均衡
4. **数据一致性**: 强一致性保证，分布式状态同步
5. **运维友好**: 简化部署、监控和维护

### 性能目标
- **集群规模**: 支持10+节点集群
- **并发能力**: 1000+并发工作流执行
- **通信延迟**: 节点间通信 < 10ms
- **故障转移**: 故障检测和转移 < 5秒
- **数据同步**: 状态同步延迟 < 100ms

---

## 🏗️ 整体架构

### 分布式集群拓扑
```
┌─────────────────────────────────────────────────────────────┐
│                   用户界面层 (Frontend)                      │
│  ┌─────────────────────────────────────────────────────────┐│
│  │            工作流画布 (Workflow Canvas)                 ││  ← 工作流设计界面
│  │         (React + ReactFlow + NATS.ws)                  ││
│  └─────────────────────────────────────────────────────────┘│
│  ┌─────────────────────────────────────────────────────────┐│
│  │          集群管理控制台 (Cluster Console)               ││  ← 集群管理界面
│  │            (React + 实时监控 + NATS.ws)                 ││
│  └─────────────────────────────────────────────────────────┘│
└─────────────────────────────────────────────────────────────┘
                              │ NATS WebSocket
┌─────────────────────────────────────────────────────────────┐
│                    NATS JetStream Cluster                  │
│    (消息中间件 + 状态存储 + 服务发现 + 前后台通信)            │
└─────────────────────────────────────────────────────────────┘
                              │ NATS消息
        ┌─────────────────────┼─────────────────────┐
        │                     │                     │
┌───────▼──────┐    ┌────────▼──────┐    ┌────────▼──────┐
│ FlowCustom   │    │ FlowCustom    │    │ FlowCustom    │
│ Node 1       │    │ Node 2        │    │ Node 3        │
│ (Designer)   │    │ (Validator)   │    │ (Executor)    │  ← 角色分工
│              │    │               │    │               │
│ ┌──────────┐ │    │ ┌───────────┐ │    │ ┌───────────┐ │
│ │Workflow  │ │    │ │Workflow   │ │    │ │Workflow   │ │
│ │Designer  │ │    │ │Validator  │ │    │ │Executor   │ │  ← 专门服务
│ │Service   │ │    │ │Service    │ │    │ │Service    │ │
│ └──────────┘ │    │ └───────────┘ │    │ └───────────┘ │
│              │    │               │    │               │
│ ┌──────────┐ │    │ ┌───────────┐ │    │ ┌───────────┐ │
│ │NATS      │ │    │ │NATS       │ │    │ │NATS       │ │
│ │Client    │ │    │ │Client     │ │    │ │Client     │ │
│ └──────────┘ │    │ └───────────┘ │    │ └───────────┘ │
│              │    │               │    │               │
│ ┌──────────┐ │    │ ┌───────────┐ │    │ ┌───────────┐ │
│ │Local DB  │ │    │ │Local DB   │ │    │ │Local DB   │ │
│ └──────────┘ │    │ └───────────┘ │    │ └───────────┘ │
└──────────────┘    └───────────────┘    └───────────────┘
```

### 核心组件

#### 1. 用户界面层 (Frontend)
- **工作流画布 (Workflow Canvas)**
  - 基于React + ReactFlow的可视化工作流设计器
  - 支持拖拽式节点编辑和连接
  - 通过NATS WebSocket与后端实时通信
  - 支持多用户实时协作编辑

- **集群管理控制台 (Cluster Console)**
  - 集群状态监控和节点管理界面
  - 实时性能指标展示和告警通知
  - 工作流执行监控和管理
  - 集群配置和运维管理

#### 2. 消息通信层 (Message Layer)
- **NATS JetStream Cluster**
  - 统一的消息中间件和分布式状态存储
  - 支持WebSocket连接，实现前后台统一通信
  - 提供服务发现和消息路由功能
  - 保证消息持久化和高可用性

#### 3. 分布式节点层 (Node Layer)
- **FlowCustom Node**: 支持角色分工的分布式节点
  - **Designer Node**: 专门处理工作流设计和编辑
  - **Validator Node**: 专门处理工作流验证和规则检查
  - **Executor Node**: 专门处理工作流执行和资源管理

#### 4. 核心服务组件
- **NATS Client**: 每个节点的NATS客户端，处理消息通信
- **专门服务**: 根据节点角色提供专业化服务
- **Local Database**: 节点本地数据缓存和状态存储

---

## 🌐 NATS统一通信架构

### NATS JetStream特性
- **消息持久化**: 工作流事件和状态持久化存储
- **流式处理**: 支持事件流和状态流处理
- **分布式存储**: 多副本数据存储，保证数据安全
- **服务发现**: 基于NATS的服务注册和发现
- **WebSocket支持**: 支持前端直接通过WebSocket连接

### 统一消息主题设计
```
# 集群管理相关
flowcustom.cluster.nodes.{nodeId}           # 节点心跳和状态
flowcustom.cluster.discovery                # 服务发现
flowcustom.cluster.config                  # 集群配置更新

# 工作流核心功能
flowcustom.workflows.{workflowId}.events    # 工作流事件
flowcustom.workflows.{workflowId}.state     # 工作流状态
flowcustom.nodes.{nodeId}.tasks            # 节点任务分发
flowcustom.nodes.{nodeId}.health           # 节点健康检查

# 前端界面通信 (新增)
flowcustom.ui.designer.{workflowId}.operations  # 工作流设计操作
flowcustom.ui.designer.{workflowId}.state       # 设计状态同步
flowcustom.ui.cluster.status.request            # 集群状态请求
flowcustom.ui.cluster.status.response           # 集群状态响应
flowcustom.ui.cluster.metrics.stream            # 实时指标流
flowcustom.ui.notifications.alerts              # 告警通知流

# 角色分工路由 (新增)
flowcustom.designer.workflow.create         # 路由到Designer节点
flowcustom.validator.workflow.validate      # 路由到Validator节点
flowcustom.executor.workflow.start          # 路由到Executor节点
```

### 数据流设计
```
前端操作 → NATS WebSocket → 消息路由 → 目标角色节点 → 处理结果 → NATS → 前端更新
     ↓                                                              ↑
工作流提交 → NATS消息 → 智能路由 → 最优节点执行 → 状态更新 → NATS存储 ←─────┘
     ↓                     ↓                                        ↑
   负载均衡            角色分工路由                              结果通知
     ↓                     ↓                                        ↑
   最优节点 ← 节点选择算法 ← 角色节点池 ← 节点状态监控 ← NATS状态流 ←─────┘
```

---

## �️ 集群管理控制台设计

### 架构位置
集群管理控制台是一个**独立的Web应用**，部署在集群之上，通过以下方式与集群交互：

```
用户浏览器 → 集群管理控制台 → Leader节点API Gateway → NATS集群 → 所有节点
```

### 部署模式

#### 模式1: 集成部署 (推荐)
```
┌─────────────────────────────────────┐
│         Leader Node                 │
│  ┌─────────────────────────────────┐│
│  │    FlowCustomV1.ClusterUI      ││  ← 集成在Leader节点中
│  │    (React SPA)                 ││
│  └─────────────────────────────────┘│
│  ┌─────────────────────────────────┐│
│  │    FlowCustomV1.Api            ││  ← 扩展的API Gateway
│  │    + Cluster Management APIs   ││
│  └─────────────────────────────────┘│
│  ┌─────────────────────────────────┐│
│  │    Cluster Manager Service     ││
│  └─────────────────────────────────┘│
└─────────────────────────────────────┘
```

#### 模式2: 独立部署
```
┌─────────────────────────────────────┐
│    独立集群管理服务器                 │
│  ┌─────────────────────────────────┐│
│  │  FlowCustomV1.ClusterConsole   ││  ← 独立的管理控制台
│  │  (ASP.NET Core + React)        ││
│  └─────────────────────────────────┘│
└─────────────────────────────────────┘
                │ HTTP API调用
┌─────────────────────────────────────┐
│         FlowCustom Cluster          │
│    (通过任意节点的API访问)            │
└─────────────────────────────────────┘
```

### 核心功能模块

#### 1. 集群概览仪表板
```typescript
interface ClusterDashboard {
  clusterInfo: {
    totalNodes: number;
    activeNodes: number;
    failedNodes: number;
    clusterHealth: 'Healthy' | 'Warning' | 'Critical';
  };

  performanceMetrics: {
    totalWorkflows: number;
    runningWorkflows: number;
    completedWorkflows: number;
    failedWorkflows: number;
    avgExecutionTime: number;
    throughput: number; // workflows/minute
  };

  resourceUsage: {
    totalCpuUsage: number;
    totalMemoryUsage: number;
    totalDiskUsage: number;
    networkTraffic: number;
  };
}
```

#### 2. 节点管理界面
```typescript
interface NodeManagement {
  nodeList: NodeInfo[];

  actions: {
    addNode(nodeConfig: NodeConfig): Promise<boolean>;
    removeNode(nodeId: string): Promise<boolean>;
    restartNode(nodeId: string): Promise<boolean>;
    drainNode(nodeId: string): Promise<boolean>; // 优雅停机
    updateNodeConfig(nodeId: string, config: NodeConfig): Promise<boolean>;
  };

  monitoring: {
    getNodeMetrics(nodeId: string): Promise<NodeMetrics>;
    getNodeLogs(nodeId: string, options: LogOptions): Promise<LogEntry[]>;
    getNodeHealth(nodeId: string): Promise<HealthStatus>;
  };
}
```

#### 3. 工作流管理界面
```typescript
interface WorkflowManagement {
  workflowList: WorkflowInfo[];
  executionHistory: ExecutionInfo[];

  actions: {
    createWorkflow(definition: WorkflowDefinition): Promise<string>;
    updateWorkflow(id: string, definition: WorkflowDefinition): Promise<boolean>;
    deleteWorkflow(id: string): Promise<boolean>;
    executeWorkflow(id: string, input: any): Promise<string>;
    cancelExecution(executionId: string): Promise<boolean>;
    migrateExecution(executionId: string, targetNodeId: string): Promise<boolean>;
  };

  monitoring: {
    getExecutionStatus(executionId: string): Promise<ExecutionStatus>;
    getExecutionLogs(executionId: string): Promise<LogEntry[]>;
    getWorkflowMetrics(workflowId: string): Promise<WorkflowMetrics>;
  };
}
```

#### 4. 集群配置管理
```typescript
interface ClusterConfiguration {
  clusterSettings: {
    maxNodes: number;
    loadBalancingStrategy: 'RoundRobin' | 'LeastConnections' | 'WeightedRoundRobin';
    failoverTimeout: number;
    healthCheckInterval: number;
    messageRetentionPeriod: number;
  };

  natsConfiguration: {
    servers: string[];
    clusterId: string;
    streamConfig: StreamConfig;
    consumerConfig: ConsumerConfig;
  };

  securitySettings: {
    enableTLS: boolean;
    certificatePath: string;
    enableAuthentication: boolean;
    authenticationMethod: 'JWT' | 'Certificate' | 'UserPassword';
  };
}
```

### API接口设计

#### 集群管理API扩展
```csharp
[ApiController]
[Route("api/cluster")]
public class ClusterManagementController : ControllerBase
{
    // 集群状态
    [HttpGet("status")]
    public async Task<ClusterStatus> GetClusterStatusAsync();

    [HttpGet("health")]
    public async Task<ClusterHealth> GetClusterHealthAsync();

    [HttpGet("metrics")]
    public async Task<ClusterMetrics> GetClusterMetricsAsync();

    // 节点管理
    [HttpGet("nodes")]
    public async Task<List<NodeInfo>> GetNodesAsync();

    [HttpPost("nodes/{nodeId}/drain")]
    public async Task<bool> DrainNodeAsync(string nodeId);

    [HttpPost("nodes/{nodeId}/restart")]
    public async Task<bool> RestartNodeAsync(string nodeId);

    [HttpDelete("nodes/{nodeId}")]
    public async Task<bool> RemoveNodeAsync(string nodeId);

    // 工作流管理
    [HttpGet("workflows/running")]
    public async Task<List<ExecutionInfo>> GetRunningWorkflowsAsync();

    [HttpPost("workflows/{executionId}/migrate")]
    public async Task<bool> MigrateWorkflowAsync(string executionId, [FromBody] MigrateRequest request);

    [HttpPost("workflows/{executionId}/cancel")]
    public async Task<bool> CancelWorkflowAsync(string executionId);

    // 配置管理
    [HttpGet("config")]
    public async Task<ClusterConfiguration> GetClusterConfigAsync();

    [HttpPut("config")]
    public async Task<bool> UpdateClusterConfigAsync([FromBody] ClusterConfiguration config);
}
```

### 实时数据更新

#### WebSocket连接
```typescript
class ClusterWebSocketClient {
  private ws: WebSocket;

  connect() {
    this.ws = new WebSocket('ws://leader-node:5000/ws/cluster');

    this.ws.onmessage = (event) => {
      const message = JSON.parse(event.data);

      switch (message.type) {
        case 'NodeStatusUpdate':
          this.updateNodeStatus(message.data);
          break;
        case 'WorkflowStatusUpdate':
          this.updateWorkflowStatus(message.data);
          break;
        case 'ClusterMetricsUpdate':
          this.updateClusterMetrics(message.data);
          break;
        case 'AlertNotification':
          this.showAlert(message.data);
          break;
      }
    };
  }

  subscribeToUpdates(subscriptions: string[]) {
    this.ws.send(JSON.stringify({
      type: 'Subscribe',
      subscriptions: subscriptions
    }));
  }
}
```

---

## �🔧 核心服务设计

### 1. 分布式节点管理器 (DistributedNodeManager)
```csharp
public interface IDistributedNodeManager
{
    Task<bool> RegisterNodeAsync(NodeInfo nodeInfo);
    Task<bool> UnregisterNodeAsync(string nodeId);
    Task<List<NodeInfo>> GetActiveNodesAsync();
    Task<NodeInfo> SelectOptimalNodeAsync(WorkflowExecutionRequest request);
    Task UpdateNodeHealthAsync(string nodeId, NodeHealthInfo health);
    Task<bool> IsNodeHealthyAsync(string nodeId);
}
```

**核心功能**:
- 节点自动注册和注销
- 节点健康状态监控
- 智能节点选择算法
- 故障节点自动剔除

### 2. 分布式工作流调度器 (DistributedWorkflowScheduler)
```csharp
public interface IDistributedWorkflowScheduler
{
    Task<string> ScheduleWorkflowAsync(WorkflowDefinition workflow, Dictionary<string, object> input);
    Task<bool> MigrateWorkflowAsync(string executionId, string fromNodeId, string toNodeId);
    Task<WorkflowExecutionStatus> GetExecutionStatusAsync(string executionId);
    Task<bool> CancelExecutionAsync(string executionId);
}
```

**核心功能**:
- 分布式工作流调度
- 任务智能分发
- 工作流迁移和故障转移
- 执行状态全局管理

### 3. 分布式状态管理器 (DistributedStateManager)
```csharp
public interface IDistributedStateManager
{
    Task SetWorkflowStateAsync(string executionId, WorkflowState state);
    Task<WorkflowState> GetWorkflowStateAsync(string executionId);
    Task SetNodeStateAsync(string executionId, string nodeId, NodeExecutionState state);
    Task<Dictionary<string, NodeExecutionState>> GetAllNodeStatesAsync(string executionId);
    Task<bool> AcquireDistributedLockAsync(string lockKey, TimeSpan timeout);
    Task ReleaseLockAsync(string lockKey);
}
```

**核心功能**:
- 分布式状态同步
- 工作流状态一致性保证
- 分布式锁机制
- 状态冲突解决

---

## 📡 通信协议设计

### 节点间通信协议
```json
{
  "messageType": "WorkflowExecution",
  "sourceNodeId": "node-001",
  "targetNodeId": "node-002",
  "timestamp": "2025-09-04T10:00:00Z",
  "payload": {
    "executionId": "exec-12345",
    "workflowDefinition": {...},
    "inputData": {...},
    "executionContext": {...}
  }
}
```

### 状态同步协议
```json
{
  "messageType": "StateSync",
  "executionId": "exec-12345",
  "nodeId": "node-001",
  "timestamp": "2025-09-04T10:00:01Z",
  "stateData": {
    "workflowStatus": "Running",
    "currentNodeId": "task-001",
    "completedNodes": ["start"],
    "nodeStates": {...},
    "executionMetrics": {...}
  }
}
```

### 健康检查协议
```json
{
  "messageType": "HealthCheck",
  "nodeId": "node-001",
  "timestamp": "2025-09-04T10:00:00Z",
  "healthData": {
    "status": "Healthy",
    "cpuUsage": 45.2,
    "memoryUsage": 67.8,
    "activeWorkflows": 12,
    "queuedTasks": 3,
    "lastHeartbeat": "2025-09-04T09:59:58Z"
  }
}
```

---

## 🎯 节点角色分工设计

### 角色分离架构
在分布式集群中，不同节点承担不同的专门角色，实现**专业化分工**：

#### 三种核心角色
1. **Designer Node**: 专门处理工作流设计和编辑
2. **Validator Node**: 专门处理工作流验证和规则检查
3. **Executor Node**: 专门处理工作流执行和资源管理

#### 角色分工优势
- **专业化分工**: 每个角色专注于特定功能，提高效率
- **资源优化**: 根据角色特点优化硬件配置
- **独立扩展**: 根据负载情况独立扩展特定角色节点
- **故障隔离**: 角色间相互独立，提高系统稳定性

### Designer Node 服务
```csharp
public interface IWorkflowDesignerService
{
    // 工作流设计
    Task<WorkflowDefinition> CreateWorkflowAsync(WorkflowTemplate template);
    Task<bool> UpdateWorkflowAsync(string workflowId, WorkflowDefinition workflow);
    Task<List<WorkflowTemplate>> GetTemplatesAsync();

    // 可视化画布
    Task<CanvasLayout> GetCanvasLayoutAsync(string workflowId);
    Task<bool> SaveCanvasLayoutAsync(string workflowId, CanvasLayout layout);

    // 实时协作
    Task BroadcastDesignChangeAsync(string workflowId, DesignOperation operation);
    Task<List<CollaboratorInfo>> GetActiveCollaboratorsAsync(string workflowId);
}
```

### Validator Node 服务
```csharp
public interface IWorkflowValidatorService
{
    // 基础验证
    Task<ValidationResult> ValidateWorkflowAsync(WorkflowDefinition workflow);
    Task<ValidationResult> ValidateNodeConfigAsync(NodeConfiguration config);

    // 高级验证
    Task<CyclicDependencyResult> CheckCyclicDependencyAsync(WorkflowDefinition workflow);
    Task<PerformanceAnalysis> AnalyzePerformanceAsync(WorkflowDefinition workflow);

    // 验证缓存
    Task<ValidationResult> GetCachedValidationAsync(string workflowHash);
}
```

### Executor Node 服务
```csharp
public interface IWorkflowExecutorService
{
    // 执行管理
    Task<string> ExecuteWorkflowAsync(string workflowId, Dictionary<string, object> input);
    Task<ExecutionResult> GetExecutionResultAsync(string executionId);
    Task<bool> CancelExecutionAsync(string executionId);

    // 资源管理
    Task<ExecutionCapacity> GetExecutionCapacityAsync();
    Task<List<ExecutionInfo>> GetRunningExecutionsAsync();

    // 性能优化
    Task<bool> MigrateExecutionAsync(string executionId, string targetNodeId);
}
```

### 角色路由机制
```csharp
public class RoleBasedMessageRouter
{
    private readonly Dictionary<string, NodeRole> _routingTable = new()
    {
        { "flowcustom.designer.*", NodeRole.Designer },
        { "flowcustom.validator.*", NodeRole.Validator },
        { "flowcustom.executor.*", NodeRole.Executor }
    };

    public async Task RouteMessageAsync(string subject, object message)
    {
        var targetRole = GetTargetRole(subject);
        var availableNodes = await GetNodesByRoleAsync(targetRole);
        var selectedNode = SelectOptimalNode(availableNodes);

        await _natsConnection.PublishAsync($"{subject}.{selectedNode.NodeId}", message);
    }
}
```

---

## 🎨 工作流画布设计

### 技术架构
基于**React + ReactFlow**的现代化工作流设计器：

#### 核心组件
```typescript
// 主画布组件
const WorkflowCanvas: React.FC = () => {
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const natsClient = useNatsClient();

  // 通过NATS实时同步设计变更
  const onConnect = useCallback((params: Connection) => {
    setEdges((eds) => addEdge(params, eds));
    natsClient.broadcastDesignChange(workflowId, {
      type: 'CONNECTION_ADDED',
      data: params
    });
  }, []);

  return (
    <ReactFlow
      nodes={nodes}
      edges={edges}
      onNodesChange={onNodesChange}
      onEdgesChange={onEdgesChange}
      onConnect={onConnect}
      nodeTypes={customNodeTypes}
    />
  );
};
```

#### 实时协作功能
```typescript
class WorkflowCanvasNatsClient {
  // 订阅其他用户的设计操作
  setupCollaboration() {
    this.nc.subscribe(`flowcustom.ui.designer.${this.workflowId}.operations`, {
      callback: (err, msg) => {
        const operation = this.jc.decode(msg.data);
        this.applyRemoteOperation(operation);
      }
    });
  }

  // 广播本地设计变更
  broadcastDesignChange(operation: DesignOperation) {
    this.nc.publish(
      `flowcustom.ui.designer.${this.workflowId}.operations`,
      this.jc.encode(operation)
    );
  }
}
```

### 部署集成
工作流画布作为前端应用，通过NATS WebSocket与Designer节点通信：

```
用户浏览器 → 工作流画布 → NATS WebSocket → Designer Node → 工作流存储
```

---

## 🔄 故障转移机制

### 故障检测
1. **心跳监控**: 每30秒发送心跳消息
2. **健康检查**: 每分钟进行健康状态检查
3. **超时检测**: 3次心跳失败判定为故障
4. **网络分区检测**: 基于NATS连接状态

### 故障转移流程
```
故障检测 → 节点标记为不可用 → 获取故障节点任务列表 → 任务重新分发 → 状态恢复 → 继续执行
```

### 数据恢复策略
1. **状态恢复**: 从NATS JetStream恢复工作流状态
2. **任务重放**: 基于事件日志重放未完成任务
3. **数据一致性**: 通过分布式锁保证数据一致性
4. **冲突解决**: 基于时间戳的冲突解决机制

---

## 📊 智能负载均衡算法

### 角色感知的节点选择策略
基于节点角色的智能路由和负载均衡：

#### 1. 角色过滤策略
```csharp
public async Task<List<NodeInfo>> GetNodesByRoleAsync(NodeRole role)
{
    var allNodes = await GetActiveNodesAsync();
    return allNodes.Where(n => n.Roles.Contains(role) && n.IsHealthy).ToList();
}
```

#### 2. 多维度选择算法
1. **角色匹配**: 首先过滤支持目标角色的节点
2. **轮询算法**: 在角色节点内简单轮询分发
3. **加权轮询**: 基于节点性能和角色权重的分发
4. **最少连接**: 选择当前负载最低的角色节点
5. **响应时间**: 基于历史响应时间和角色性能选择
6. **资源使用率**: 基于CPU/内存使用率和角色特点选择

### 角色感知智能调度算法
```csharp
public class RoleAwareIntelligentNodeSelector
{
    public async Task<NodeInfo> SelectOptimalNodeAsync(
        string messageSubject,
        object request,
        NodeRole? preferredRole = null)
    {
        // 1. 确定目标角色
        var targetRole = preferredRole ?? DetermineRoleFromSubject(messageSubject);

        // 2. 获取角色节点
        var roleNodes = await GetNodesByRoleAsync(targetRole);
        var healthyNodes = roleNodes.Where(n => n.IsHealthy).ToList();

        if (!healthyNodes.Any())
            throw new NoAvailableNodesException($"No healthy {targetRole} nodes available");

        // 3. 计算角色感知权重
        var nodeWeights = await CalculateRoleAwareWeightsAsync(healthyNodes, targetRole, request);

        // 4. 选择最优节点
        return SelectByWeight(nodeWeights);
    }

    private async Task<Dictionary<NodeInfo, double>> CalculateRoleAwareWeightsAsync(
        List<NodeInfo> nodes, NodeRole role, object request)
    {
        var weights = new Dictionary<NodeInfo, double>();

        foreach (var node in nodes)
        {
            var weight = 0.0;

            // 基础资源权重 (50%)
            weight += CalculateResourceWeight(node) * 0.5;

            // 角色专业化权重 (30%)
            weight += CalculateRoleSpecializationWeight(node, role) * 0.3;

            // 历史性能权重 (20%)
            weight += await GetRolePerformanceScore(node.NodeId, role) * 0.2;

            weights[node] = weight;
        }

        return weights;
    }

    private double CalculateResourceWeight(NodeInfo node)
    {
        // CPU使用率权重 (40%)
        var cpuWeight = (100 - node.CpuUsage) * 0.4;

        // 内存使用率权重 (35%)
        var memoryWeight = (100 - node.MemoryUsage) * 0.35;

        // 当前负载权重 (25%)
        var loadWeight = (100 - node.ActiveTasks * 5) * 0.25;

        return cpuWeight + memoryWeight + loadWeight;
    }

    private double CalculateRoleSpecializationWeight(NodeInfo node, NodeRole role)
    {
        // 获取节点在该角色上的专业化程度
        var roleConfig = node.RoleConfigurations.GetValueOrDefault(role);
        if (roleConfig == null) return 0;

        // 角色权重 * 节点在该角色上的配置权重
        return roleConfig.Weight * roleConfig.Priority;
    }

    private NodeRole DetermineRoleFromSubject(string subject)
    {
        return subject switch
        {
            var s when s.Contains("designer") => NodeRole.Designer,
            var s when s.Contains("validator") => NodeRole.Validator,
            var s when s.Contains("executor") => NodeRole.Executor,
            _ => NodeRole.Executor // 默认执行角色
        };
    }
}
```

---

## 🔐 安全和一致性

### 数据一致性保证
1. **强一致性**: 关键状态更新使用强一致性
2. **最终一致性**: 非关键数据使用最终一致性
3. **冲突解决**: 基于向量时钟的冲突解决
4. **事务支持**: 分布式事务保证数据完整性

### 安全机制
1. **节点认证**: 基于证书的节点身份认证
2. **通信加密**: NATS TLS加密通信
3. **访问控制**: 基于角色的访问控制
4. **审计日志**: 完整的操作审计追踪

---

## 🚀 部署和运维

### 角色分工部署架构

#### 生产环境部署 (角色分离模式)
```
┌─────────────────────────────────────────────────────────────┐
│                    Load Balancer                           │
│              (角色感知路由 + 前端静态资源)                    │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    NATS JetStream Cluster                  │
│              (消息路由 + 状态存储 + WebSocket网关)            │
└─────────────────────────────────────────────────────────────┘
                              │
        ┌─────────────────────┼─────────────────────┐
        │                     │                     │
┌───────▼──────┐    ┌────────▼──────┐    ┌────────▼──────┐
│ Designer     │    │ Validator     │    │ Executor      │
│ Cluster      │    │ Cluster       │    │ Cluster       │
│              │    │               │    │               │
│ ┌──────────┐ │    │ ┌───────────┐ │    │ ┌───────────┐ │
│ │Designer  │ │    │ │Validator  │ │    │ │Executor   │ │
│ │Node x2   │ │    │ │Node x2    │ │    │ │Node x5    │ │
│ └──────────┘ │    │ └───────────┘ │    │ └───────────┘ │
│              │    │               │    │               │
│ 高内存配置    │    │ 高CPU配置     │    │ 高并发配置     │
│ 缓存模板布局  │    │ 复杂验证算法  │    │ 大量工作流执行 │
└──────────────┘    └───────────────┘    └───────────────┘
```

#### 开发环境部署 (混合角色模式)
```
┌─────────────────────────────────────────────────────────────┐
│                    NATS JetStream                          │
└─────────────────────────────────────────────────────────────┘
                              │
        ┌─────────────────────┼─────────────────────┐
        │                     │                     │
┌───────▼──────┐    ┌────────▼──────┐    ┌────────▼──────┐
│ FlowCustom   │    │ FlowCustom    │    │ FlowCustom    │
│ Node 1       │    │ Node 2        │    │ Node 3        │
│ (All Roles)  │    │ (All Roles)   │    │ (All Roles)   │
│              │    │               │    │               │
│ ┌──────────┐ │    │ ┌───────────┐ │    │ ┌───────────┐ │
│ │Designer  │ │    │ │Designer   │ │    │ │Designer   │ │
│ │Validator │ │    │ │Validator  │ │    │ │Validator  │ │
│ │Executor  │ │    │ │Executor   │ │    │ │Executor   │ │
│ └──────────┘ │    │ └───────────┘ │    │ └───────────┘ │
└──────────────┘    └───────────────┘    └───────────────┘
```

#### 动态角色切换部署
```csharp
// 支持运行时角色调整
public class DynamicRoleManager
{
    public async Task SwitchNodeRoleAsync(string nodeId, NodeRole newRole)
    {
        var node = await GetNodeAsync(nodeId);

        // 停止当前角色服务
        await StopRoleServicesAsync(node.CurrentRoles);

        // 启动新角色服务
        await StartRoleServicesAsync(new[] { newRole });

        // 更新路由表
        await UpdateRoutingTableAsync(nodeId, new[] { newRole });

        // 通知集群角色变更
        await BroadcastRoleChangeAsync(nodeId, newRole);
    }
}
```

### 监控指标
- **集群健康度**: 活跃节点数、故障节点数
- **性能指标**: 吞吐量、延迟、成功率
- **资源使用**: CPU、内存、网络、存储
- **业务指标**: 工作流执行数、成功率、平均执行时间

---

**设计版本**: v1.0  
**创建日期**: 2025-09-04  
**目标实现**: v0.0.1.0  
**架构负责人**: FlowCustomV1 开发团队
