using FlowCustomV1.Core.Interfaces.Messaging;
using FlowCustomV1.Infrastructure.Configuration;
using FlowCustomV1.Infrastructure.Services.Messaging;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace FlowCustomV1.Infrastructure.Extensions;

/// <summary>
/// NATS服务依赖注入扩展
/// </summary>
public static class NatsServiceCollectionExtensions
{
    /// <summary>
    /// 添加NATS消息服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">配置对象</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddNatsMessaging(this IServiceCollection services, IConfiguration configuration)
    {
        // 注册配置管理器
        services.AddSingleton<ConfigurationManagerService>();

        // 注册系统配置
        services.Configure<SystemConfiguration>(configuration.GetSection(SystemConfiguration.SectionName));

        // 注册NATS配置
        services.Configure<NatsConfiguration>(configuration.GetSection(NatsConfiguration.SectionName));

        // 注册消息主题配置
        services.Configure<MessagingTopicConfiguration>(configuration.GetSection(MessagingTopicConfiguration.SectionName));

        // 验证配置
        services.AddSingleton<IValidateOptions<NatsConfiguration>, NatsConfigurationValidator>();
        services.AddSingleton<IValidateOptions<SystemConfiguration>, SystemConfigurationValidator>();
        services.AddSingleton<IValidateOptions<MessagingTopicConfiguration>, MessagingTopicConfigurationValidator>();

        // 注册消息主题服务
        services.AddSingleton<IMessageTopicService, MessageTopicService>();

        // 注册连接状态管理器
        services.AddSingleton<ConnectionStateManager>();

        // 注册核心服务
        services.AddSingleton<INatsService, NatsService>();
        services.AddSingleton<INatsMessageRouter, NatsMessageRouter>();

        // 注册后台服务
        services.AddHostedService<NatsConnectionManagerService>();

        return services;
    }

    /// <summary>
    /// 添加NATS消息服务（使用自定义配置）
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configureOptions">配置选项</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddNatsMessaging(this IServiceCollection services, Action<NatsConfiguration> configureOptions)
    {
        // 注册NATS配置
        services.Configure(configureOptions);

        // 验证配置
        services.AddSingleton<IValidateOptions<NatsConfiguration>, NatsConfigurationValidator>();

        // 注册核心服务
        services.AddSingleton<INatsService, NatsService>();
        services.AddSingleton<INatsMessageRouter, NatsMessageRouter>();

        // 注册后台服务
        services.AddHostedService<NatsConnectionManagerService>();

        return services;
    }

    /// <summary>
    /// 添加NATS连接池支持
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddNatsConnectionPool(this IServiceCollection services)
    {
        // 注册连接池服务
        services.AddSingleton<INatsConnectionPool, NatsConnectionPool>();

        return services;
    }
}

/// <summary>
/// 系统配置验证器
/// </summary>
internal class SystemConfigurationValidator : IValidateOptions<SystemConfiguration>
{
    /// <inheritdoc />
    public ValidateOptionsResult Validate(string? name, SystemConfiguration options)
    {
        var errors = new List<string>();

        // 验证网络配置
        if (options.Network.HttpPort == options.Network.HttpsPort)
        {
            errors.Add("HTTP and HTTPS ports cannot be the same");
        }

        if (options.Network.HttpPort == options.Network.WebSocketPort ||
            options.Network.HttpsPort == options.Network.WebSocketPort)
        {
            errors.Add("WebSocket port cannot be the same as HTTP/HTTPS ports");
        }

        // 验证性能配置
        if (options.Performance.MaxConcurrentWorkflows < options.Performance.MaxConcurrentNodes)
        {
            errors.Add("MaxConcurrentWorkflows should be greater than or equal to MaxConcurrentNodes");
        }

        // 验证节点配置
        if (string.IsNullOrWhiteSpace(options.Node.NodeId))
        {
            errors.Add("Node ID cannot be null or empty");
        }

        if (options.Node.MaxMemoryMb < 128)
        {
            errors.Add("Node max memory should be at least 128MB");
        }

        return errors.Count > 0
            ? ValidateOptionsResult.Fail(errors)
            : ValidateOptionsResult.Success;
    }
}

/// <summary>
/// 消息主题配置验证器
/// </summary>
internal class MessagingTopicConfigurationValidator : IValidateOptions<MessagingTopicConfiguration>
{
    /// <inheritdoc />
    public ValidateOptionsResult Validate(string? name, MessagingTopicConfiguration options)
    {
        var errors = new List<string>();

        // 验证根前缀
        if (string.IsNullOrWhiteSpace(options.RootPrefix))
        {
            errors.Add("Root prefix cannot be null or empty");
        }
        else if (options.RootPrefix.Contains(' ') || options.RootPrefix.Contains('.'))
        {
            errors.Add("Root prefix cannot contain spaces or dots");
        }

        // 验证集群配置
        if (string.IsNullOrWhiteSpace(options.Cluster.Root))
        {
            errors.Add("Cluster root topic cannot be null or empty");
        }

        // 验证工作流配置
        if (string.IsNullOrWhiteSpace(options.Workflows.Root))
        {
            errors.Add("Workflows root topic cannot be null or empty");
        }

        return errors.Count > 0
            ? ValidateOptionsResult.Fail(errors)
            : ValidateOptionsResult.Success;
    }
}

/// <summary>
/// NATS配置验证器
/// </summary>
internal class NatsConfigurationValidator : IValidateOptions<NatsConfiguration>
{
    /// <inheritdoc />
    public ValidateOptionsResult Validate(string? name, NatsConfiguration options)
    {
        var errors = new List<string>();

        // 验证服务器地址
        if (options.Servers == null || !options.Servers.Any())
        {
            errors.Add("At least one NATS server must be configured");
        }
        else
        {
            foreach (var server in options.Servers)
            {
                if (string.IsNullOrWhiteSpace(server))
                {
                    errors.Add("NATS server URL cannot be null or empty");
                }
                else if (!Uri.TryCreate(server, UriKind.Absolute, out var uri) || 
                         (uri.Scheme != "nats" && uri.Scheme != "tls"))
                {
                    errors.Add($"Invalid NATS server URL: {server}");
                }
            }
        }

        // 验证连接名称
        if (string.IsNullOrWhiteSpace(options.ConnectionName))
        {
            errors.Add("Connection name cannot be null or empty");
        }

        // 验证超时配置
        if (options.ConnectionTimeoutSeconds <= 0)
        {
            errors.Add("Connection timeout must be greater than 0");
        }

        if (options.ReconnectIntervalSeconds <= 0)
        {
            errors.Add("Reconnect interval must be greater than 0");
        }

        if (options.MaxReconnectAttempts < 0)
        {
            errors.Add("Max reconnect attempts cannot be negative");
        }

        if (options.PingIntervalSeconds <= 0)
        {
            errors.Add("Ping interval must be greater than 0");
        }

        if (options.MaxPingsOutstanding <= 0)
        {
            errors.Add("Max pings outstanding must be greater than 0");
        }

        // 验证JetStream配置
        if (options.JetStream.Enabled)
        {
            if (options.JetStream.DefaultStream.MaxMessages <= 0)
            {
                errors.Add("JetStream max messages must be greater than 0");
            }

            if (options.JetStream.DefaultStream.MaxBytes <= 0)
            {
                errors.Add("JetStream max bytes must be greater than 0");
            }

            if (options.JetStream.DefaultStream.MaxAgeSeconds <= 0)
            {
                errors.Add("JetStream max age must be greater than 0");
            }

            if (options.JetStream.DefaultStream.Replicas <= 0 || options.JetStream.DefaultStream.Replicas > 5)
            {
                errors.Add("JetStream replicas must be between 1 and 5");
            }
        }

        // 验证连接池配置
        if (options.ConnectionPool.Enabled)
        {
            if (options.ConnectionPool.MinConnections <= 0)
            {
                errors.Add("Connection pool min connections must be greater than 0");
            }

            if (options.ConnectionPool.MaxConnections <= 0)
            {
                errors.Add("Connection pool max connections must be greater than 0");
            }

            if (options.ConnectionPool.MinConnections > options.ConnectionPool.MaxConnections)
            {
                errors.Add("Connection pool min connections cannot be greater than max connections");
            }

            if (options.ConnectionPool.IdleTimeoutSeconds <= 0)
            {
                errors.Add("Connection pool idle timeout must be greater than 0");
            }

            if (options.ConnectionPool.AcquireTimeoutSeconds <= 0)
            {
                errors.Add("Connection pool acquire timeout must be greater than 0");
            }
        }

        // 验证序列化配置
        var supportedSerializationTypes = new[] { "json", "messagepack", "protobuf" };
        if (!supportedSerializationTypes.Contains(options.Serialization.Type.ToLowerInvariant()))
        {
            errors.Add($"Unsupported serialization type: {options.Serialization.Type}. Supported types: {string.Join(", ", supportedSerializationTypes)}");
        }

        if (options.Serialization.EnableCompression)
        {
            var supportedCompressionTypes = new[] { "gzip", "deflate" };
            if (!supportedCompressionTypes.Contains(options.Serialization.CompressionType.ToLowerInvariant()))
            {
                errors.Add($"Unsupported compression type: {options.Serialization.CompressionType}. Supported types: {string.Join(", ", supportedCompressionTypes)}");
            }
        }

        return errors.Any() 
            ? ValidateOptionsResult.Fail(errors)
            : ValidateOptionsResult.Success;
    }
}

/// <summary>
/// NATS连接管理后台服务
/// </summary>
internal class NatsConnectionManagerService : BackgroundService
{
    private readonly INatsService _natsService;
    private readonly ILogger<NatsConnectionManagerService> _logger;
    private readonly NatsConfiguration _config;

    /// <summary>
    /// 构造函数
    /// </summary>
    public NatsConnectionManagerService(
        INatsService natsService, 
        IOptions<NatsConfiguration> config,
        ILogger<NatsConnectionManagerService> logger)
    {
        _natsService = natsService ?? throw new ArgumentNullException(nameof(natsService));
        _config = config.Value ?? throw new ArgumentNullException(nameof(config));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <inheritdoc />
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        try
        {
            _logger.LogInformation("Starting NATS connection manager");

            // 初始连接
            await _natsService.ConnectAsync(stoppingToken);

            // 监控连接状态
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    if (!_natsService.IsConnected)
                    {
                        _logger.LogWarning("NATS connection lost, attempting to reconnect...");
                        await _natsService.ConnectAsync(stoppingToken);
                    }

                    // 等待一段时间再检查
                    await Task.Delay(TimeSpan.FromSeconds(30), stoppingToken);
                }
                catch (OperationCanceledException)
                {
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error in NATS connection monitoring");
                    await Task.Delay(TimeSpan.FromSeconds(10), stoppingToken);
                }
            }
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("NATS connection manager stopped");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Fatal error in NATS connection manager");
        }
    }

    /// <inheritdoc />
    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Stopping NATS connection manager");

        try
        {
            await _natsService.DisconnectAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error disconnecting from NATS");
        }

        await base.StopAsync(cancellationToken);
    }
}
