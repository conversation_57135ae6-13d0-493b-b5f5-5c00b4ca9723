<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <!-- Furion框架包 - 增强API开发体验 -->
    <PackageReference Include="Furion" Version="4.9.7.119" />
    <PackageReference Include="Furion.Pure" Version="4.9.7.119" />

    <!-- 现有包保持不变，升级Swagger版本以兼容Furion -->
  </ItemGroup>

  <ItemGroup>
    <!-- API层只引用Infrastructure层，遵循清洁架构原则 -->
    <!-- Infrastructure层会传递性引用Engine和Core层 -->
    <ProjectReference Include="..\FlowCustomV1.Infrastructure\FlowCustomV1.Infrastructure.csproj" />
  </ItemGroup>

</Project>