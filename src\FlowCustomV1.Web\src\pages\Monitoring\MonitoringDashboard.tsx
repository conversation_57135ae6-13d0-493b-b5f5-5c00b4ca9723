import React from 'react';
import { Row, Col, Statistic, Progress, Tag, Alert } from 'antd';
import {
  MonitorOutlined,
  DashboardOutlined,
  AlertOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';
import PageLayout from '@/components/Layout/PageLayout';

const MonitoringDashboard: React.FC = () => {
  return (
    <PageLayout
      title="监控仪表盘"
      description="系统性能监控和告警管理"
      icon={<MonitorOutlined />}
    >
      <Alert
        message="监控系统状态"
        description="实时监控系统性能指标、资源使用情况和告警信息"
        type="info"
        showIcon
        className="layout-card-grid"
      />

      {/* 系统概览 */}
      <Row gutter={[16, 16]} className="layout-card-grid">
        <Col xs={24} sm={12} lg={6}>
          <ProCard>
            <Statistic
              title="系统健康度"
              value={95}
              suffix="%"
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </ProCard>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <ProCard>
            <Statistic
              title="活跃告警"
              value={3}
              prefix={<AlertOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </ProCard>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <ProCard>
            <Statistic
              title="监控指标"
              value={156}
              prefix={<DashboardOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </ProCard>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <ProCard>
            <Statistic
              title="数据点/分钟"
              value={2847}
              prefix={<MonitorOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </ProCard>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        {/* 系统性能 */}
        <Col xs={24} lg={12}>
          <ProCard title="系统性能指标">
            <div className="space-y-4">
              <div>
                <div className="flex justify-between items-center mb-2">
                  <span>CPU 使用率</span>
                  <span className="font-semibold">65%</span>
                </div>
                <Progress percent={65} strokeColor="#1890ff" />
              </div>
              
              <div>
                <div className="flex justify-between items-center mb-2">
                  <span>内存使用率</span>
                  <span className="font-semibold">78%</span>
                </div>
                <Progress percent={78} strokeColor="#52c41a" />
              </div>
              
              <div>
                <div className="flex justify-between items-center mb-2">
                  <span>磁盘使用率</span>
                  <span className="font-semibold">42%</span>
                </div>
                <Progress percent={42} strokeColor="#722ed1" />
              </div>
              
              <div>
                <div className="flex justify-between items-center mb-2">
                  <span>网络吞吐量</span>
                  <span className="font-semibold">156 MB/s</span>
                </div>
                <Progress percent={60} strokeColor="#fa8c16" />
              </div>
              
              <div>
                <div className="flex justify-between items-center mb-2">
                  <span>数据库连接</span>
                  <span className="font-semibold">45 / 100</span>
                </div>
                <Progress percent={45} strokeColor="#13c2c2" />
              </div>
            </div>
          </ProCard>
        </Col>

        {/* 告警信息 */}
        <Col xs={24} lg={12}>
          <ProCard title="活跃告警">
            <div className="space-y-3">
              <div className="border rounded p-3 border-l-4 border-l-red-500">
                <div className="flex justify-between items-start mb-2">
                  <div className="font-medium text-red-600">高内存使用率</div>
                  <Tag color="error">严重</Tag>
                </div>
                <div className="text-sm text-gray-500 mb-2">
                  节点 worker-03 内存使用率超过 90%
                </div>
                <div className="text-xs text-gray-400">
                  2025-01-13 15:30:25
                </div>
              </div>
              
              <div className="border rounded p-3 border-l-4 border-l-orange-500">
                <div className="flex justify-between items-start mb-2">
                  <div className="font-medium text-orange-600">执行队列积压</div>
                  <Tag color="warning">警告</Tag>
                </div>
                <div className="text-sm text-gray-500 mb-2">
                  执行队列中有 25 个待处理任务
                </div>
                <div className="text-xs text-gray-400">
                  2025-01-13 15:28:12
                </div>
              </div>
              
              <div className="border rounded p-3 border-l-4 border-l-yellow-500">
                <div className="flex justify-between items-start mb-2">
                  <div className="font-medium text-yellow-600">磁盘空间不足</div>
                  <Tag color="warning">警告</Tag>
                </div>
                <div className="text-sm text-gray-500 mb-2">
                  存储节点磁盘使用率达到 85%
                </div>
                <div className="text-xs text-gray-400">
                  2025-01-13 15:25:08
                </div>
              </div>
            </div>
          </ProCard>
        </Col>
      </Row>

      {/* 服务状态 */}
      <Row className="mt-6">
        <Col span={24}>
          <ProCard title="服务状态监控">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="border rounded p-4 text-center">
                <CheckCircleOutlined className="text-4xl text-green-500 mb-2" />
                <div className="font-medium">API 服务</div>
                <div className="text-sm text-gray-500">正常运行</div>
                <Tag color="success" className="mt-2">在线</Tag>
              </div>
              
              <div className="border rounded p-4 text-center">
                <CheckCircleOutlined className="text-4xl text-green-500 mb-2" />
                <div className="font-medium">数据库</div>
                <div className="text-sm text-gray-500">连接正常</div>
                <Tag color="success" className="mt-2">在线</Tag>
              </div>
              
              <div className="border rounded p-4 text-center">
                <ExclamationCircleOutlined className="text-4xl text-orange-500 mb-2" />
                <div className="font-medium">消息队列</div>
                <div className="text-sm text-gray-500">负载较高</div>
                <Tag color="warning" className="mt-2">警告</Tag>
              </div>
              
              <div className="border rounded p-4 text-center">
                <CheckCircleOutlined className="text-4xl text-green-500 mb-2" />
                <div className="font-medium">缓存服务</div>
                <div className="text-sm text-gray-500">运行正常</div>
                <Tag color="success" className="mt-2">在线</Tag>
              </div>
            </div>
          </ProCard>
        </Col>
      </Row>

      {/* 监控指标概览 */}
      <Row className="mt-6">
        <Col span={24}>
          <ProCard title="监控指标概览">
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">2,847</div>
                <div className="text-sm text-gray-500">每分钟请求数</div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">99.9%</div>
                <div className="text-sm text-gray-500">系统可用性</div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">156ms</div>
                <div className="text-sm text-gray-500">平均响应时间</div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">3</div>
                <div className="text-sm text-gray-500">活跃告警</div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-cyan-600">15</div>
                <div className="text-sm text-gray-500">在线节点</div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-pink-600">42</div>
                <div className="text-sm text-gray-500">运行中任务</div>
              </div>
            </div>
          </ProCard>
        </Col>
      </Row>
    </PageLayout>
  );
};

export default MonitoringDashboard;
