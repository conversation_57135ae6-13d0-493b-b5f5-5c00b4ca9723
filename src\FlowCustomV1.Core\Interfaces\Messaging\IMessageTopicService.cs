namespace FlowCustomV1.Core.Interfaces.Messaging;

/// <summary>
/// 消息主题服务接口
/// 提供动态生成消息主题名称的功能
/// </summary>
public interface IMessageTopicService
{
    #region 集群管理主题

    /// <summary>
    /// 获取集群管理根主题
    /// </summary>
    /// <returns>集群管理根主题</returns>
    string GetClusterRootTopic();

    /// <summary>
    /// 获取节点心跳主题
    /// </summary>
    /// <param name="nodeId">节点ID</param>
    /// <returns>节点心跳主题</returns>
    string GetNodeHeartbeatTopic(string nodeId);

    /// <summary>
    /// 获取服务发现主题
    /// </summary>
    /// <returns>服务发现主题</returns>
    string GetServiceDiscoveryTopic();

    /// <summary>
    /// 获取集群配置更新主题
    /// </summary>
    /// <returns>集群配置更新主题</returns>
    string GetClusterConfigTopic();

    /// <summary>
    /// 获取节点注册主题
    /// </summary>
    /// <returns>节点注册主题</returns>
    string GetNodeRegisterTopic();

    /// <summary>
    /// 获取节点下线主题
    /// </summary>
    /// <returns>节点下线主题</returns>
    string GetNodeUnregisterTopic();

    #endregion

    #region 工作流核心功能主题

    /// <summary>
    /// 获取工作流根主题
    /// </summary>
    /// <returns>工作流根主题</returns>
    string GetWorkflowRootTopic();

    /// <summary>
    /// 获取工作流事件主题
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <returns>工作流事件主题</returns>
    string GetWorkflowEventsTopic(string workflowId);

    /// <summary>
    /// 获取工作流状态主题
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <returns>工作流状态主题</returns>
    string GetWorkflowStateTopic(string workflowId);

    /// <summary>
    /// 获取工作流执行主题
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="executionId">执行ID</param>
    /// <returns>工作流执行主题</returns>
    string GetWorkflowExecutionTopic(string workflowId, string executionId);

    #endregion

    #region 节点任务分发主题

    /// <summary>
    /// 获取节点根主题
    /// </summary>
    /// <returns>节点根主题</returns>
    string GetNodesRootTopic();

    /// <summary>
    /// 获取节点任务主题
    /// </summary>
    /// <param name="nodeId">节点ID</param>
    /// <returns>节点任务主题</returns>
    string GetNodeTasksTopic(string nodeId);

    /// <summary>
    /// 获取节点健康检查主题
    /// </summary>
    /// <param name="nodeId">节点ID</param>
    /// <returns>节点健康检查主题</returns>
    string GetNodeHealthTopic(string nodeId);

    /// <summary>
    /// 获取节点状态主题
    /// </summary>
    /// <param name="nodeId">节点ID</param>
    /// <returns>节点状态主题</returns>
    string GetNodeStatusTopic(string nodeId);

    #endregion

    #region 任务调度主题

    /// <summary>
    /// 获取任务调度根主题
    /// </summary>
    /// <returns>任务调度根主题</returns>
    string GetTasksRootTopic();

    /// <summary>
    /// 获取高优先级任务队列主题
    /// </summary>
    /// <returns>高优先级任务队列主题</returns>
    string GetHighPriorityTasksTopic();

    /// <summary>
    /// 获取普通优先级任务队列主题
    /// </summary>
    /// <returns>普通优先级任务队列主题</returns>
    string GetNormalPriorityTasksTopic();

    /// <summary>
    /// 获取低优先级任务队列主题
    /// </summary>
    /// <returns>低优先级任务队列主题</returns>
    string GetLowPriorityTasksTopic();

    #endregion

    #region 角色专业化主题

    /// <summary>
    /// 获取Designer节点专用主题
    /// </summary>
    /// <returns>Designer节点专用主题</returns>
    string GetDesignerRootTopic();

    /// <summary>
    /// 获取Validator节点专用主题
    /// </summary>
    /// <returns>Validator节点专用主题</returns>
    string GetValidatorRootTopic();

    /// <summary>
    /// 获取Executor节点专用主题
    /// </summary>
    /// <returns>Executor节点专用主题</returns>
    string GetExecutorRootTopic();

    #endregion

    #region UI通信主题

    /// <summary>
    /// 获取UI根主题
    /// </summary>
    /// <returns>UI根主题</returns>
    string GetUiRootTopic();

    /// <summary>
    /// 获取UI实时更新主题
    /// </summary>
    /// <returns>UI实时更新主题</returns>
    string GetUiUpdatesTopic();

    /// <summary>
    /// 获取用户通知主题
    /// </summary>
    /// <returns>用户通知主题</returns>
    string GetUiNotificationsTopic();

    /// <summary>
    /// 获取UI系统事件主题
    /// </summary>
    /// <returns>UI系统事件主题</returns>
    string GetUiEventsTopic();

    #endregion

    #region Executor专用主题

    /// <summary>
    /// 获取执行请求主题
    /// </summary>
    /// <returns>执行请求主题</returns>
    string GetExecutionRequestTopic();

    /// <summary>
    /// 获取执行控制主题
    /// </summary>
    /// <returns>执行控制主题</returns>
    string GetExecutionControlTopic();

    /// <summary>
    /// 获取执行控制响应主题
    /// </summary>
    /// <returns>执行控制响应主题</returns>
    string GetExecutionControlResponseTopic();

    /// <summary>
    /// 获取执行迁移主题
    /// </summary>
    /// <returns>执行迁移主题</returns>
    string GetExecutionMigrationTopic();

    /// <summary>
    /// 获取执行迁移主题（指定目标节点）
    /// </summary>
    /// <param name="targetNodeId">目标节点ID</param>
    /// <returns>执行迁移主题</returns>
    string GetExecutionMigrationTopic(string targetNodeId);

    /// <summary>
    /// 获取执行迁移响应主题
    /// </summary>
    /// <returns>执行迁移响应主题</returns>
    string GetExecutionMigrationResponseTopic();

    /// <summary>
    /// 获取容量查询主题
    /// </summary>
    /// <returns>容量查询主题</returns>
    string GetCapacityQueryTopic();

    /// <summary>
    /// 获取容量响应主题
    /// </summary>
    /// <returns>容量响应主题</returns>
    string GetCapacityResponseTopic();

    /// <summary>
    /// 获取节点负载更新主题
    /// </summary>
    /// <returns>节点负载更新主题</returns>
    string GetNodeLoadUpdateTopic();

    /// <summary>
    /// 获取执行状态主题
    /// </summary>
    /// <returns>执行状态主题</returns>
    string GetExecutionStatusTopic();

    /// <summary>
    /// 获取执行结果主题
    /// </summary>
    /// <returns>执行结果主题</returns>
    string GetExecutionResultTopic();

    /// <summary>
    /// 获取节点状态主题
    /// </summary>
    /// <returns>节点状态主题</returns>
    string GetNodeStateTopic();

    /// <summary>
    /// 获取执行状态查询主题
    /// </summary>
    /// <returns>执行状态查询主题</returns>
    string GetExecutionStateQueryTopic();

    /// <summary>
    /// 获取执行状态响应主题
    /// </summary>
    /// <returns>执行状态响应主题</returns>
    string GetExecutionStateResponseTopic();

    /// <summary>
    /// 获取执行事件主题
    /// </summary>
    /// <returns>执行事件主题</returns>
    string GetExecutionEventTopic();

    /// <summary>
    /// 获取执行上下文主题
    /// </summary>
    /// <returns>执行上下文主题</returns>
    string GetExecutionContextTopic();

    /// <summary>
    /// 获取执行上下文查询主题
    /// </summary>
    /// <returns>执行上下文查询主题</returns>
    string GetExecutionContextQueryTopic();

    /// <summary>
    /// 获取执行上下文响应主题
    /// </summary>
    /// <returns>执行上下文响应主题</returns>
    string GetExecutionContextResponseTopic();

    #endregion

    #region 监控主题

    /// <summary>
    /// 获取监控根主题
    /// </summary>
    /// <returns>监控根主题</returns>
    string GetMonitoringRootTopic();

    /// <summary>
    /// 获取性能指标主题
    /// </summary>
    /// <returns>性能指标主题</returns>
    string GetMonitoringMetricsTopic();

    /// <summary>
    /// 获取健康检查主题
    /// </summary>
    /// <returns>健康检查主题</returns>
    string GetMonitoringHealthTopic();

    /// <summary>
    /// 获取告警主题
    /// </summary>
    /// <returns>告警主题</returns>
    string GetMonitoringAlertsTopic();

    /// <summary>
    /// 获取日志主题
    /// </summary>
    /// <returns>日志主题</returns>
    string GetMonitoringLogsTopic();

    #endregion
}
