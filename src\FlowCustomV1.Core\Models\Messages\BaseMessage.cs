using System.Text.Json.Serialization;
using FlowCustomV1.Core.Interfaces.Messaging;

namespace FlowCustomV1.Core.Models.Messages;

/// <summary>
/// 基础消息类
/// 定义所有消息的通用属性
/// </summary>
public abstract class BaseMessage
{
    /// <summary>
    /// 消息唯一标识
    /// </summary>
    [JsonPropertyName("messageId")]
    public string MessageId { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 消息类型
    /// </summary>
    [JsonPropertyName("messageType")]
    public string MessageType { get; set; } = string.Empty;

    /// <summary>
    /// 发送者节点ID
    /// </summary>
    [JsonPropertyName("senderId")]
    public string SenderId { get; set; } = string.Empty;

    /// <summary>
    /// 目标节点ID（可选，用于点对点消息）
    /// </summary>
    [JsonPropertyName("targetId")]
    public string? TargetId { get; set; }

    /// <summary>
    /// 消息创建时间
    /// </summary>
    [JsonPropertyName("createdAt")]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 消息过期时间
    /// </summary>
    [JsonPropertyName("expiresAt")]
    public DateTime? ExpiresAt { get; set; }

    /// <summary>
    /// 消息优先级
    /// </summary>
    [JsonPropertyName("priority")]
    public MessagePriority Priority { get; set; } = MessagePriority.Normal;

    /// <summary>
    /// 消息元数据
    /// </summary>
    [JsonPropertyName("metadata")]
    public Dictionary<string, object> Metadata { get; set; } = new();

    /// <summary>
    /// 重试次数
    /// </summary>
    [JsonPropertyName("retryCount")]
    public int RetryCount { get; set; } = 0;

    /// <summary>
    /// 最大重试次数
    /// </summary>
    [JsonPropertyName("maxRetries")]
    public int MaxRetries { get; set; } = 3;
}


