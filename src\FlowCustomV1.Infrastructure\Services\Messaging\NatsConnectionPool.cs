using System.Collections.Concurrent;
using FlowCustomV1.Core.Interfaces.Messaging;
using FlowCustomV1.Infrastructure.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace FlowCustomV1.Infrastructure.Services.Messaging;

/// <summary>
/// NATS连接池实现
/// </summary>
public class NatsConnectionPool : INatsConnectionPool
{
    private readonly ILogger<NatsConnectionPool> _logger;
    private readonly NatsConfiguration _config;
    private readonly ConcurrentQueue<PooledNatsConnection> _availableConnections;
    private readonly ConcurrentDictionary<string, PooledNatsConnection> _allConnections;
    private readonly SemaphoreSlim _connectionSemaphore;
    private readonly Timer _cleanupTimer;
    private bool _initialized;
    private bool _disposed;

    /// <summary>
    /// 构造函数
    /// </summary>
    public NatsConnectionPool(IOptions<NatsConfiguration> config, ILogger<NatsConnectionPool> logger)
    {
        _config = config.Value ?? throw new ArgumentNullException(nameof(config));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        
        _availableConnections = new ConcurrentQueue<PooledNatsConnection>();
        _allConnections = new ConcurrentDictionary<string, PooledNatsConnection>();
        _connectionSemaphore = new SemaphoreSlim(_config.ConnectionPool.MaxConnections, _config.ConnectionPool.MaxConnections);
        
        // 设置清理定时器
        _cleanupTimer = new Timer(CleanupCallback, null, TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(5));
    }

    #region 连接池状态

    /// <inheritdoc />
    public bool IsInitialized => _initialized;

    /// <inheritdoc />
    public int ActiveConnections => _allConnections.Count - _availableConnections.Count;

    /// <inheritdoc />
    public int IdleConnections => _availableConnections.Count;

    /// <inheritdoc />
    public int MaxConnections => _config.ConnectionPool.MaxConnections;

    #endregion

    #region 连接管理

    /// <inheritdoc />
    public async Task InitializeAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(NatsConnectionPool));

        if (_initialized)
            return;

        try
        {
            _logger.LogInformation("Initializing NATS connection pool with {MinConnections}-{MaxConnections} connections", 
                _config.ConnectionPool.MinConnections, _config.ConnectionPool.MaxConnections);

            // 创建最小数量的连接
            for (int i = 0; i < _config.ConnectionPool.MinConnections; i++)
            {
                var connection = await CreateConnectionAsync(cancellationToken);
                _availableConnections.Enqueue(connection);
            }

            _initialized = true;
            _logger.LogInformation("NATS connection pool initialized successfully");
            
            OnStatusChanged("Uninitialized", "Initialized");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize NATS connection pool");
            throw;
        }
    }

    /// <inheritdoc />
    public async Task<IPooledNatsConnection> AcquireConnectionAsync(TimeSpan? timeout = null, CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(NatsConnectionPool));

        if (!_initialized)
            throw new InvalidOperationException("Connection pool is not initialized");

        var acquireTimeout = timeout ?? TimeSpan.FromSeconds(_config.ConnectionPool.AcquireTimeoutSeconds);
        
        try
        {
            // 等待获取连接许可
            if (!await _connectionSemaphore.WaitAsync(acquireTimeout, cancellationToken))
            {
                throw new TimeoutException($"Failed to acquire connection within {acquireTimeout.TotalSeconds} seconds");
            }

            // 尝试从池中获取可用连接
            if (_availableConnections.TryDequeue(out var connection))
            {
                if (connection.IsValid)
                {
                    connection.MarkAsUsed();
                    _logger.LogDebug("Acquired existing connection {ConnectionId} from pool", connection.ConnectionId);
                    return connection;
                }
                else
                {
                    // 连接无效，销毁并创建新连接
                    await DestroyConnectionAsync(connection, "Invalid connection");
                }
            }

            // 创建新连接
            var newConnection = await CreateConnectionAsync(cancellationToken);
            newConnection.MarkAsUsed();
            _logger.LogDebug("Created and acquired new connection {ConnectionId}", newConnection.ConnectionId);
            return newConnection;
        }
        catch (Exception ex)
        {
            _connectionSemaphore.Release();
            _logger.LogError(ex, "Failed to acquire connection from pool");
            throw;
        }
    }

    /// <inheritdoc />
    public async Task ReleaseConnectionAsync(IPooledNatsConnection connection, CancellationToken cancellationToken = default)
    {
        if (_disposed || connection == null)
            return;

        try
        {
            var pooledConnection = (PooledNatsConnection)connection;
            
            if (pooledConnection.IsValid && 
                DateTime.UtcNow - pooledConnection.CreatedAt < TimeSpan.FromSeconds(_config.ConnectionPool.IdleTimeoutSeconds))
            {
                // 重置连接状态并放回池中
                await pooledConnection.ResetAsync(cancellationToken);
                _availableConnections.Enqueue(pooledConnection);
                _logger.LogDebug("Released connection {ConnectionId} back to pool", pooledConnection.ConnectionId);
            }
            else
            {
                // 连接过期或无效，销毁连接
                await DestroyConnectionAsync(pooledConnection, "Connection expired or invalid");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error releasing connection {ConnectionId}", connection.ConnectionId);
        }
        finally
        {
            _connectionSemaphore.Release();
        }
    }

    /// <inheritdoc />
    public async Task ShutdownAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed)
            return;

        try
        {
            _logger.LogInformation("Shutting down NATS connection pool");

            // 停止清理定时器
            _cleanupTimer?.Dispose();

            // 销毁所有连接
            var connections = _allConnections.Values.ToList();
            var tasks = connections.Select(conn => DestroyConnectionAsync(conn, "Pool shutdown"));
            await Task.WhenAll(tasks);

            _initialized = false;
            _logger.LogInformation("NATS connection pool shut down successfully");
            
            OnStatusChanged("Initialized", "Shutdown");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during connection pool shutdown");
            throw;
        }
    }

    #endregion

    #region 健康检查

    /// <inheritdoc />
    public async Task<ConnectionPoolHealthStatus> CheckHealthAsync(CancellationToken cancellationToken = default)
    {
        var healthStatus = new ConnectionPoolHealthStatus
        {
            TotalConnections = _allConnections.Count,
            ActiveConnections = ActiveConnections,
            IdleConnections = IdleConnections
        };

        try
        {
            var validConnections = 0;
            var invalidConnections = 0;
            var totalResponseTime = 0.0;
            var responseTimeCount = 0;

            foreach (var connection in _allConnections.Values)
            {
                if (connection.IsValid)
                {
                    validConnections++;
                    
                    // 简单的ping测试
                    var startTime = DateTime.UtcNow;
                    try
                    {
                        // 这里可以添加实际的ping测试
                        await Task.Delay(1, cancellationToken);
                        var responseTime = (DateTime.UtcNow - startTime).TotalMilliseconds;
                        totalResponseTime += responseTime;
                        responseTimeCount++;
                    }
                    catch
                    {
                        invalidConnections++;
                    }
                }
                else
                {
                    invalidConnections++;
                }
            }

            healthStatus.InvalidConnections = invalidConnections;
            healthStatus.AverageResponseTime = responseTimeCount > 0 ? totalResponseTime / responseTimeCount : 0;
            healthStatus.IsHealthy = invalidConnections == 0 && validConnections > 0;

            healthStatus.Details["ValidConnections"] = validConnections;
            healthStatus.Details["PoolUtilization"] = _allConnections.Count > 0 ? (double)ActiveConnections / _allConnections.Count * 100 : 0;
        }
        catch (Exception ex)
        {
            healthStatus.IsHealthy = false;
            healthStatus.Details["Error"] = ex.Message;
        }

        return healthStatus;
    }

    /// <inheritdoc />
    public async Task CleanupInvalidConnectionsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var invalidConnections = _allConnections.Values
                .Where(conn => !conn.IsValid || 
                              DateTime.UtcNow - conn.LastUsedAt > TimeSpan.FromSeconds(_config.ConnectionPool.IdleTimeoutSeconds))
                .ToList();

            if (invalidConnections.Any())
            {
                _logger.LogDebug("Cleaning up {Count} invalid connections", invalidConnections.Count);
                
                var tasks = invalidConnections.Select(conn => DestroyConnectionAsync(conn, "Cleanup - invalid or expired"));
                await Task.WhenAll(tasks);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during connection cleanup");
        }
    }

    #endregion

    #region 事件

    /// <inheritdoc />
    public event EventHandler<ConnectionPoolStatusChangedEventArgs>? StatusChanged;

    /// <inheritdoc />
    public event EventHandler<ConnectionCreatedEventArgs>? ConnectionCreated;

    /// <inheritdoc />
    public event EventHandler<ConnectionDestroyedEventArgs>? ConnectionDestroyed;

    #endregion

    #region 私有方法

    private Task<PooledNatsConnection> CreateConnectionAsync(CancellationToken cancellationToken)
    {
        var connectionId = Guid.NewGuid().ToString();
        
        try
        {
            var loggerFactory = Microsoft.Extensions.Logging.LoggerFactory.Create(builder => { });
            var connectionStateManager = new ConnectionStateManager(Options.Create(_config), loggerFactory.CreateLogger<ConnectionStateManager>());
            var natsService = new NatsService(Options.Create(_config), connectionStateManager, loggerFactory.CreateLogger<NatsService>());
            // NatsClient 自动连接，无需显式调用 ConnectAsync
            // await natsService.ConnectAsync(cancellationToken);

            var connection = new PooledNatsConnection(connectionId, natsService);
            _allConnections.TryAdd(connectionId, connection);

            _logger.LogDebug("Created new NATS connection {ConnectionId}", connectionId);
            OnConnectionCreated(connectionId);

            return Task.FromResult(connection);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create NATS connection {ConnectionId}", connectionId);
            throw;
        }
    }

    private Task DestroyConnectionAsync(PooledNatsConnection connection, string reason)
    {
        try
        {
            _allConnections.TryRemove(connection.ConnectionId, out _);
            
            var lifetime = DateTime.UtcNow - connection.CreatedAt;
            var useCount = connection.UseCount;
            
            connection.Dispose();
            
            _logger.LogDebug("Destroyed connection {ConnectionId}: {Reason}", connection.ConnectionId, reason);
            OnConnectionDestroyed(connection.ConnectionId, reason, lifetime, useCount);

            return Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error destroying connection {ConnectionId}", connection.ConnectionId);
            return Task.CompletedTask;
        }
    }

    private void CleanupCallback(object? state)
    {
        try
        {
            _ = Task.Run(async () => await CleanupInvalidConnectionsAsync());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in cleanup timer callback");
        }
    }

    private void OnStatusChanged(string oldStatus, string newStatus)
    {
        StatusChanged?.Invoke(this, new ConnectionPoolStatusChangedEventArgs
        {
            OldStatus = oldStatus,
            NewStatus = newStatus,
            ActiveConnections = ActiveConnections,
            IdleConnections = IdleConnections
        });
    }

    private void OnConnectionCreated(string connectionId)
    {
        ConnectionCreated?.Invoke(this, new ConnectionCreatedEventArgs
        {
            ConnectionId = connectionId
        });
    }

    private void OnConnectionDestroyed(string connectionId, string reason, TimeSpan lifetime, int useCount)
    {
        ConnectionDestroyed?.Invoke(this, new ConnectionDestroyedEventArgs
        {
            ConnectionId = connectionId,
            Reason = reason,
            Lifetime = lifetime,
            UseCount = useCount
        });
    }

    #endregion

    #region IDisposable

    /// <inheritdoc />
    public void Dispose()
    {
        if (_disposed)
            return;

        try
        {
            ShutdownAsync().GetAwaiter().GetResult();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during disposal");
        }

        _connectionSemaphore?.Dispose();
        _cleanupTimer?.Dispose();
        _disposed = true;
    }

    #endregion
}

/// <summary>
/// 池化NATS连接实现
/// </summary>
internal class PooledNatsConnection : IPooledNatsConnection
{
    private readonly INatsService _natsService;
    private bool _disposed;

    /// <summary>
    /// 构造函数
    /// </summary>
    public PooledNatsConnection(string connectionId, INatsService natsService)
    {
        ConnectionId = connectionId;
        _natsService = natsService ?? throw new ArgumentNullException(nameof(natsService));
        CreatedAt = DateTime.UtcNow;
        LastUsedAt = DateTime.UtcNow;
    }

    /// <inheritdoc />
    public string ConnectionId { get; }

    /// <inheritdoc />
    public INatsService NatsService => _natsService;

    /// <inheritdoc />
    public bool IsValid => !_disposed && _natsService.IsConnected;

    /// <inheritdoc />
    public DateTime CreatedAt { get; }

    /// <inheritdoc />
    public DateTime LastUsedAt { get; private set; }

    /// <inheritdoc />
    public int UseCount { get; private set; }

    /// <inheritdoc />
    public void MarkAsUsed()
    {
        LastUsedAt = DateTime.UtcNow;
        UseCount++;
    }

    /// <inheritdoc />
    public Task ResetAsync(CancellationToken cancellationToken = default)
    {
        // 这里可以添加连接重置逻辑
        // 例如清理订阅、重置状态等
        return Task.CompletedTask;
    }

    /// <inheritdoc />
    public void Dispose()
    {
        if (_disposed)
            return;

        try
        {
            _natsService?.Dispose();
        }
        catch
        {
            // 忽略异常
        }

        _disposed = true;
    }
}
