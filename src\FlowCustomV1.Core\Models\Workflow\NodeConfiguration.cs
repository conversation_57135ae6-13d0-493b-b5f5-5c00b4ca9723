using System.ComponentModel.DataAnnotations;

namespace FlowCustomV1.Core.Models.Workflow;

/// <summary>
/// 节点配置
/// 包含节点执行所需的配置参数和设置
/// </summary>
public class NodeConfiguration
{
    /// <summary>
    /// 配置参数字典
    /// </summary>
    public Dictionary<string, object> Parameters { get; set; } = new();

    /// <summary>
    /// 配置设置（Parameters的别名，用于兼容性）
    /// </summary>
    public Dictionary<string, object> Settings => Parameters;

    /// <summary>
    /// 输入映射配置
    /// 定义如何从工作流上下文映射输入数据
    /// </summary>
    public Dictionary<string, string> InputMappings { get; set; } = new();

    /// <summary>
    /// 输出映射配置
    /// 定义如何将节点输出映射到工作流上下文
    /// </summary>
    public Dictionary<string, string> OutputMappings { get; set; } = new();

    /// <summary>
    /// 条件表达式
    /// 用于控制节点是否执行
    /// </summary>
    public string? ConditionExpression { get; set; }

    /// <summary>
    /// 环境变量
    /// </summary>
    public Dictionary<string, string> EnvironmentVariables { get; set; } = new();

    /// <summary>
    /// 资源限制
    /// </summary>
    public NodeResourceLimits ResourceLimits { get; set; } = new();

    /// <summary>
    /// 安全设置
    /// </summary>
    public NodeSecuritySettings SecuritySettings { get; set; } = new();

    /// <summary>
    /// 缓存设置
    /// </summary>
    public NodeCacheSettings CacheSettings { get; set; } = new();

    /// <summary>
    /// 监控设置
    /// </summary>
    public NodeMonitoringSettings MonitoringSettings { get; set; } = new();

    /// <summary>
    /// 配置元数据
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();

    /// <summary>
    /// 获取配置参数值
    /// </summary>
    /// <typeparam name="T">参数类型</typeparam>
    /// <param name="key">参数键</param>
    /// <param name="defaultValue">默认值</param>
    /// <returns>参数值</returns>
    public T GetParameter<T>(string key, T defaultValue = default!)
    {
        if (Parameters.TryGetValue(key, out var value))
        {
            try
            {
                if (value is T directValue)
                    return directValue;

                return (T)Convert.ChangeType(value, typeof(T));
            }
            catch
            {
                return defaultValue;
            }
        }
        return defaultValue;
    }

    /// <summary>
    /// 设置配置参数值
    /// </summary>
    /// <param name="key">参数键</param>
    /// <param name="value">参数值</param>
    public void SetParameter(string key, object value)
    {
        Parameters[key] = value;
    }

    /// <summary>
    /// 检查是否包含指定参数
    /// </summary>
    /// <param name="key">参数键</param>
    /// <returns>是否包含</returns>
    public bool HasParameter(string key)
    {
        return Parameters.ContainsKey(key);
    }

    /// <summary>
    /// 移除配置参数
    /// </summary>
    /// <param name="key">参数键</param>
    /// <returns>是否成功移除</returns>
    public bool RemoveParameter(string key)
    {
        return Parameters.Remove(key);
    }

    /// <summary>
    /// 获取输入映射
    /// </summary>
    /// <param name="inputName">输入名称</param>
    /// <returns>映射表达式</returns>
    public string? GetInputMapping(string inputName)
    {
        return InputMappings.TryGetValue(inputName, out var mapping) ? mapping : null;
    }

    /// <summary>
    /// 设置输入映射
    /// </summary>
    /// <param name="inputName">输入名称</param>
    /// <param name="mappingExpression">映射表达式</param>
    public void SetInputMapping(string inputName, string mappingExpression)
    {
        InputMappings[inputName] = mappingExpression;
    }

    /// <summary>
    /// 获取输出映射
    /// </summary>
    /// <param name="outputName">输出名称</param>
    /// <returns>映射表达式</returns>
    public string? GetOutputMapping(string outputName)
    {
        return OutputMappings.TryGetValue(outputName, out var mapping) ? mapping : null;
    }

    /// <summary>
    /// 设置输出映射
    /// </summary>
    /// <param name="outputName">输出名称</param>
    /// <param name="mappingExpression">映射表达式</param>
    public void SetOutputMapping(string outputName, string mappingExpression)
    {
        OutputMappings[outputName] = mappingExpression;
    }

    /// <summary>
    /// 验证配置的有效性
    /// </summary>
    /// <returns>是否有效</returns>
    public bool IsValid()
    {
        // 基本验证
        if (Parameters == null || InputMappings == null || OutputMappings == null)
            return false;

        // 验证资源限制
        if (!ResourceLimits.IsValid())
            return false;

        // 验证条件表达式（如果存在）
        if (!string.IsNullOrWhiteSpace(ConditionExpression))
        {
            // 这里可以添加表达式语法验证
            // 暂时简单检查不为空
        }

        return true;
    }

    /// <summary>
    /// 合并另一个配置
    /// </summary>
    /// <param name="other">要合并的配置</param>
    public void Merge(NodeConfiguration other)
    {
        if (other == null) return;

        // 合并参数
        foreach (var kvp in other.Parameters)
        {
            Parameters[kvp.Key] = kvp.Value;
        }

        // 合并输入映射
        foreach (var kvp in other.InputMappings)
        {
            InputMappings[kvp.Key] = kvp.Value;
        }

        // 合并输出映射
        foreach (var kvp in other.OutputMappings)
        {
            OutputMappings[kvp.Key] = kvp.Value;
        }

        // 合并环境变量
        foreach (var kvp in other.EnvironmentVariables)
        {
            EnvironmentVariables[kvp.Key] = kvp.Value;
        }

        // 合并元数据
        foreach (var kvp in other.Metadata)
        {
            Metadata[kvp.Key] = kvp.Value;
        }

        // 更新其他设置
        if (!string.IsNullOrWhiteSpace(other.ConditionExpression))
            ConditionExpression = other.ConditionExpression;

        ResourceLimits.Merge(other.ResourceLimits);
        SecuritySettings.Merge(other.SecuritySettings);
        CacheSettings.Merge(other.CacheSettings);
        MonitoringSettings.Merge(other.MonitoringSettings);
    }

    /// <summary>
    /// 创建配置的深拷贝
    /// </summary>
    /// <returns>配置的深拷贝</returns>
    public NodeConfiguration Clone()
    {
        return new NodeConfiguration
        {
            Parameters = new Dictionary<string, object>(Parameters),
            InputMappings = new Dictionary<string, string>(InputMappings),
            OutputMappings = new Dictionary<string, string>(OutputMappings),
            ConditionExpression = ConditionExpression,
            EnvironmentVariables = new Dictionary<string, string>(EnvironmentVariables),
            ResourceLimits = ResourceLimits.Clone(),
            SecuritySettings = SecuritySettings.Clone(),
            CacheSettings = CacheSettings.Clone(),
            MonitoringSettings = MonitoringSettings.Clone(),
            Metadata = new Dictionary<string, object>(Metadata)
        };
    }

    /// <summary>
    /// 获取配置的字符串表示
    /// </summary>
    /// <returns>配置字符串</returns>
    public override string ToString()
    {
        return $"NodeConfiguration[{Parameters.Count} params, {InputMappings.Count} inputs, {OutputMappings.Count} outputs]";
    }
}
