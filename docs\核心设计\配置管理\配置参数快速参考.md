# FlowCustomV1 配置参数快速参考

## 📋 环境变量格式速查

### NATS 消息系统
```bash
# 服务器地址（支持多个）
Nats__Servers__0=nats://nats:4222
Nats__Servers__1=nats://nats-2:4222
Nats__Servers__2=nats://nats-3:4222

# 连接配置
Nats__ConnectionName=FlowCustomV1-Node-Name
Nats__Username=flowcustom
Nats__Password=your_********
Nats__ConnectionTimeoutSeconds=30
Nats__ReconnectIntervalSeconds=2
Nats__MaxReconnectAttempts=10

# JetStream配置
Nats__JetStream__Enabled=true
Nats__JetStream__Domain=flowcustom-cluster
```

### 数据库配置
```bash
# 基础配置
Database__Provider=MySQL
Database__ConnectionString=Server=mysql;Database=flowcustom;Uid=user;Pwd=********;
Database__CommandTimeout=30

# 重试配置
Database__EnableRetryOnFailure=true
Database__MaxRetryCount=3
Database__MaxRetryDelay=00:00:30
```

### 节点发现配置
```bash
# 节点标识
NodeDiscovery__ClusterName=FlowCustomV1-Cluster
NodeDiscovery__NodeId=node-001
NodeDiscovery__NodeName=MasterNode001
NodeDiscovery__NodeRole=Master
NodeDiscovery__Region=Beijing
NodeDiscovery__DataCenter=Beijing-DC1

# 心跳配置
NodeDiscovery__HeartbeatIntervalSeconds=10
NodeDiscovery__NodeTimeoutSeconds=30
NodeDiscovery__EnableAutoRegistration=true
```

### 任务分发配置
```bash
# 分发策略
TaskDistribution__MaxCandidateNodes=10
TaskDistribution__MaxConcurrentDistributions=5
TaskDistribution__DefaultStrategy=SmartLoad
TaskDistribution__AutoRebalancingEnabled=true

# 超时配置
TaskDistribution__NodeSelectionTimeoutMs=3000
TaskDistribution__TaskDistributionTimeoutMs=10000
```

### 任务跟踪配置
```bash
# 清理配置
TaskTracking__CleanupIntervalMinutes=10
TaskTracking__MaxRecentCompletedTasks=100
TaskTracking__TaskStateRetentionDays=7

# 监控配置
TaskTracking__EnableDetailedLogging=true
TaskTracking__EnablePerformanceMonitoring=true
TaskTracking__MaxConcurrentTrackedTasks=1000
```

### 日志配置
```bash
# 日志级别
Logging__LogLevel__Default=Information
Logging__LogLevel__FlowCustomV1=Debug
Logging__LogLevel__Microsoft=Warning

# 控制台日志
Logging__Console__IncludeScopes=true
Logging__Console__TimestampFormat=yyyy-MM-dd HH:mm:ss.fff
```

---

## 🌍 环境特定配置模板

### 测试环境 (Test)
```yaml
environment:
  - ASPNETCORE_ENVIRONMENT=Test
  - ASPNETCORE_URLS=http://+:5000
  
  # NATS - 单节点配置
  - Nats__Servers__0=nats://nats:4222
  - Nats__ConnectionName=FlowCustomV1-Test-Node
  - Nats__ConnectionTimeoutSeconds=30
  
  # 数据库 - 测试数据库
  - Database__ConnectionString=Server=mysql;Database=flowcustom_test;Uid=flowcustom;Pwd=TestPassword123!;
  - Database__CommandTimeout=30
  
  # 节点发现 - 测试集群
  - NodeDiscovery__ClusterName=FlowCustomV1-Test
  - NodeDiscovery__HeartbeatIntervalSeconds=10
  - NodeDiscovery__EnableAutoRegistration=true
  
  # 日志 - 详细日志
  - Logging__LogLevel__FlowCustomV1=Debug
  - Logging__Console__IncludeScopes=true
```

### 生产环境 (Production)
```yaml
environment:
  - ASPNETCORE_ENVIRONMENT=Production
  - ASPNETCORE_URLS=http://+:5000
  
  # NATS - 集群配置
  - Nats__Servers__0=nats://nats-1:4222
  - Nats__Servers__1=nats://nats-2:4222
  - Nats__Servers__2=nats://nats-3:4222
  - Nats__ConnectionName=FlowCustomV1-Production-Node
  - Nats__Username=flowcustom
  - Nats__Password=${NATS_PASSWORD}
  
  # 数据库 - 生产数据库
  - Database__ConnectionString=Server=mysql-cluster;Database=flowcustom_production;Uid=flowcustom;Pwd=${MYSQL_PASSWORD};
  - Database__CommandTimeout=60
  - Database__MaxRetryCount=5
  
  # 节点发现 - 生产集群
  - NodeDiscovery__ClusterName=FlowCustomV1-Production
  - NodeDiscovery__HeartbeatIntervalSeconds=30
  - NodeDiscovery__NodeTimeoutSeconds=120
  
  # 安全配置
  - Security__EnableAuthentication=true
  - Security__EnableAuthorization=true
  - Security__EnableHttps=true
  
  # 日志 - 生产日志
  - Logging__LogLevel__Default=Information
  - Logging__LogLevel__FlowCustomV1=Information
```

---

## 🔧 Docker Compose 配置示例

### 简单测试环境
```yaml
version: '3.8'
services:
  master-node:
    image: flowcustom:latest
    environment:
      - ASPNETCORE_ENVIRONMENT=Test
      - Nats__Servers__0=nats://nats:4222
      - Database__ConnectionString=Server=mysql;Database=flowcustom_test;Uid=flowcustom;Pwd=TestPassword123!;
      - NodeDiscovery__NodeId=master-001
      - NodeDiscovery__NodeRole=Master
    depends_on:
      - nats
      - mysql
    ports:
      - "5001:5000"

  worker-node:
    image: flowcustom:latest
    environment:
      - ASPNETCORE_ENVIRONMENT=Test
      - Nats__Servers__0=nats://nats:4222
      - Database__ConnectionString=Server=mysql;Database=flowcustom_test;Uid=flowcustom;Pwd=TestPassword123!;
      - NodeDiscovery__NodeId=worker-001
      - NodeDiscovery__NodeRole=Worker
    depends_on:
      - nats
      - mysql
    ports:
      - "5011:5000"
```

### 高可用生产环境
```yaml
version: '3.8'
services:
  master-node-1:
    image: flowcustom:latest
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - Nats__Servers__0=nats://nats-1:4222
      - Nats__Servers__1=nats://nats-2:4222
      - Nats__Servers__2=nats://nats-3:4222
      - Database__ConnectionString=Server=mysql-cluster;Database=flowcustom_production;Uid=flowcustom;Pwd=${MYSQL_PASSWORD};
      - NodeDiscovery__NodeId=master-001
      - NodeDiscovery__NodeRole=Master
      - NodeDiscovery__Region=Beijing
      - NodeDiscovery__DataCenter=Beijing-DC1
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
```

---

## 🚀 Kubernetes 配置示例

### ConfigMap
```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: flowcustom-config
  namespace: flowcustom
data:
  # NATS配置
  Nats__Servers__0: "nats://nats-1.nats:4222"
  Nats__Servers__1: "nats://nats-2.nats:4222"
  Nats__Servers__2: "nats://nats-3.nats:4222"
  Nats__ConnectionTimeoutSeconds: "30"
  
  # 数据库配置
  Database__Provider: "MySQL"
  Database__CommandTimeout: "60"
  Database__MaxRetryCount: "5"
  
  # 节点发现配置
  NodeDiscovery__ClusterName: "FlowCustomV1-K8s"
  NodeDiscovery__HeartbeatIntervalSeconds: "30"
  NodeDiscovery__EnableAutoRegistration: "true"
```

### Secret
```yaml
apiVersion: v1
kind: Secret
metadata:
  name: flowcustom-secrets
  namespace: flowcustom
type: Opaque
data:
  # Base64编码的敏感信息
  Database__ConnectionString: ************************************************************************************************
  Nats__Username: Zmxvd2N1c3RvbQ==
  Nats__Password: cHJvZHVjdGlvbl9wYXNzd29yZA==
```

### Deployment
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: flowcustom-master
  namespace: flowcustom
spec:
  replicas: 3
  selector:
    matchLabels:
      app: flowcustom-master
  template:
    metadata:
      labels:
        app: flowcustom-master
    spec:
      containers:
      - name: flowcustom
        image: flowcustom:latest
        env:
        - name: ASPNETCORE_ENVIRONMENT
          value: "Production"
        - name: NodeDiscovery__NodeRole
          value: "Master"
        - name: NodeDiscovery__NodeId
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        envFrom:
        - configMapRef:
            name: flowcustom-config
        - secretRef:
            name: flowcustom-secrets
```

---

## 🔍 配置验证命令

### 基础验证
```bash
# 检查配置文件语法
python -m json.tool appsettings.Test.json

# 验证环境变量
env | grep -E "(Nats|Database|NodeDiscovery)" | sort

# 测试网络连通性
nc -zv nats 4222
nc -zv mysql 3306
```

### 应用验证
```bash
# 检查应用配置加载
curl http://localhost:5001/api/config/status

# 验证NATS连接
curl http://localhost:5001/api/health/nats

# 验证数据库连接
curl http://localhost:5001/api/health/database

# 验证节点发现
curl http://localhost:5001/api/nodes
```

---

## ⚠️ 常见配置错误

### 1. 环境变量格式错误
```bash
# ❌ 错误：使用单下划线
Nats_Servers_0=nats://nats:4222

# ✅ 正确：使用双下划线
Nats__Servers__0=nats://nats:4222
```

### 2. 数组索引错误
```bash
# ❌ 错误：索引不连续
Nats__Servers__0=nats://nats-1:4222
Nats__Servers__2=nats://nats-3:4222

# ✅ 正确：索引连续
Nats__Servers__0=nats://nats-1:4222
Nats__Servers__1=nats://nats-2:4222
```

### 3. 类型转换错误
```bash
# ❌ 错误：布尔值使用字符串
NodeDiscovery__EnableAutoRegistration=yes

# ✅ 正确：布尔值使用true/false
NodeDiscovery__EnableAutoRegistration=true
```

### 4. 连接字符串格式错误
```bash
# ❌ 错误：缺少分号分隔符
Database__ConnectionString=Server=mysql Database=flowcustom

# ✅ 正确：使用分号分隔
Database__ConnectionString=Server=mysql;Database=flowcustom;Uid=user;Pwd=****;
```

---

**此快速参考文档提供了FlowCustomV1系统配置的常用参数和示例，便于快速查阅和配置部署。**
