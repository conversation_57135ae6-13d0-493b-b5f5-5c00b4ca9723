using FlowCustomV1.Core.Models.Common;

namespace FlowCustomV1.Core.Interfaces.Services;

/// <summary>
/// 认证服务接口
/// </summary>
public interface IAuthenticationService
{
    /// <summary>
    /// 用户登录
    /// </summary>
    /// <param name="username">用户名</param>
    /// <param name="password">密码</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>认证结果</returns>
    Task<AuthenticationResult> LoginAsync(string username, string password, CancellationToken cancellationToken = default);

    /// <summary>
    /// 用户登出
    /// </summary>
    /// <param name="token">访问令牌</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>登出结果</returns>
    Task<bool> LogoutAsync(string token, CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证令牌
    /// </summary>
    /// <param name="token">访问令牌</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    Task<TokenValidationResult> ValidateTokenAsync(string token, CancellationToken cancellationToken = default);

    /// <summary>
    /// 刷新令牌
    /// </summary>
    /// <param name="refreshToken">刷新令牌</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>新的访问令牌</returns>
    Task<AuthenticationResult> RefreshTokenAsync(string refreshToken, CancellationToken cancellationToken = default);
}

/// <summary>
/// 授权服务接口
/// </summary>
public interface IAuthorizationService
{
    /// <summary>
    /// 检查权限
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="resource">资源</param>
    /// <param name="action">操作</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否有权限</returns>
    Task<bool> HasPermissionAsync(string userId, string resource, string action, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取用户权限
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>权限列表</returns>
    Task<IReadOnlyList<Permission>> GetUserPermissionsAsync(string userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 分配权限
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="permissions">权限列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>分配结果</returns>
    Task<bool> AssignPermissionsAsync(string userId, IEnumerable<Permission> permissions, CancellationToken cancellationToken = default);

    /// <summary>
    /// 撤销权限
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="permissions">权限列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>撤销结果</returns>
    Task<bool> RevokePermissionsAsync(string userId, IEnumerable<Permission> permissions, CancellationToken cancellationToken = default);
}

/// <summary>
/// 安全服务接口
/// </summary>
public interface ISecurityService
{
    /// <summary>
    /// 加密数据
    /// </summary>
    /// <param name="data">原始数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>加密后的数据</returns>
    Task<string> EncryptAsync(string data, CancellationToken cancellationToken = default);

    /// <summary>
    /// 解密数据
    /// </summary>
    /// <param name="encryptedData">加密的数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>解密后的数据</returns>
    Task<string> DecryptAsync(string encryptedData, CancellationToken cancellationToken = default);

    /// <summary>
    /// 生成哈希
    /// </summary>
    /// <param name="data">原始数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>哈希值</returns>
    Task<string> GenerateHashAsync(string data, CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证哈希
    /// </summary>
    /// <param name="data">原始数据</param>
    /// <param name="hash">哈希值</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    Task<bool> VerifyHashAsync(string data, string hash, CancellationToken cancellationToken = default);

    /// <summary>
    /// 审计操作
    /// </summary>
    /// <param name="auditLog">审计日志</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>审计结果</returns>
    Task<bool> AuditOperationAsync(AuditLog auditLog, CancellationToken cancellationToken = default);
}

/// <summary>
/// 用户管理服务接口
/// </summary>
public interface IUserManagementService
{
    /// <summary>
    /// 创建用户
    /// </summary>
    /// <param name="user">用户信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>创建结果</returns>
    Task<string> CreateUserAsync(User user, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取用户
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>用户信息</returns>
    Task<User?> GetUserAsync(string userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 更新用户
    /// </summary>
    /// <param name="user">用户信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>更新结果</returns>
    Task<bool> UpdateUserAsync(User user, CancellationToken cancellationToken = default);

    /// <summary>
    /// 删除用户
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>删除结果</returns>
    Task<bool> DeleteUserAsync(string userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 搜索用户
    /// </summary>
    /// <param name="searchTerm">搜索词</param>
    /// <param name="pageIndex">页索引</param>
    /// <param name="pageSize">页大小</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>用户列表</returns>
    Task<PagedResult<User>> SearchUsersAsync(string searchTerm, int pageIndex = 0, int pageSize = 20, CancellationToken cancellationToken = default);
}

/// <summary>
/// 认证结果
/// </summary>
public class AuthenticationResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccessful { get; set; }

    /// <summary>
    /// 访问令牌
    /// </summary>
    public string? AccessToken { get; set; }

    /// <summary>
    /// 刷新令牌
    /// </summary>
    public string? RefreshToken { get; set; }

    /// <summary>
    /// 令牌过期时间
    /// </summary>
    public DateTime? ExpiresAt { get; set; }

    /// <summary>
    /// 用户信息
    /// </summary>
    public User? User { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// 令牌验证结果
/// </summary>
public class TokenValidationResult
{
    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 用户ID
    /// </summary>
    public string? UserId { get; set; }

    /// <summary>
    /// 用户名
    /// </summary>
    public string? Username { get; set; }

    /// <summary>
    /// 令牌过期时间
    /// </summary>
    public DateTime? ExpiresAt { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// 权限
/// </summary>
public class Permission
{
    /// <summary>
    /// 权限ID
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// 权限名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 资源
    /// </summary>
    public string Resource { get; set; } = string.Empty;

    /// <summary>
    /// 操作
    /// </summary>
    public string Action { get; set; } = string.Empty;

    /// <summary>
    /// 描述
    /// </summary>
    public string? Description { get; set; }
}

/// <summary>
/// 用户
/// </summary>
public class User
{
    /// <summary>
    /// 用户ID
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// 用户名
    /// </summary>
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// 邮箱
    /// </summary>
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// 显示名称
    /// </summary>
    public string DisplayName { get; set; } = string.Empty;

    /// <summary>
    /// 是否激活
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 最后登录时间
    /// </summary>
    public DateTime? LastLoginAt { get; set; }

    /// <summary>
    /// 角色列表
    /// </summary>
    public List<string> Roles { get; set; } = new();

    /// <summary>
    /// 用户属性
    /// </summary>
    public Dictionary<string, object> Attributes { get; set; } = new();
}

/// <summary>
/// 审计日志
/// </summary>
public class AuditLog
{
    /// <summary>
    /// 日志ID
    /// </summary>
    public string Id { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 用户ID
    /// </summary>
    public string? UserId { get; set; }

    /// <summary>
    /// 操作
    /// </summary>
    public string Operation { get; set; } = string.Empty;

    /// <summary>
    /// 资源
    /// </summary>
    public string Resource { get; set; } = string.Empty;

    /// <summary>
    /// 操作时间
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// IP地址
    /// </summary>
    public string? IpAddress { get; set; }

    /// <summary>
    /// 用户代理
    /// </summary>
    public string? UserAgent { get; set; }

    /// <summary>
    /// 操作结果
    /// </summary>
    public bool IsSuccessful { get; set; }

    /// <summary>
    /// 详细信息
    /// </summary>
    public Dictionary<string, object> Details { get; set; } = new();
}


