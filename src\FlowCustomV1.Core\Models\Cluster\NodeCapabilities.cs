using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace FlowCustomV1.Core.Models.Cluster;

/// <summary>
/// 节点能力信息
/// 描述节点的处理能力和支持的功能
/// </summary>
public class NodeCapabilities
{
    /// <summary>
    /// 支持的节点执行器类型列表
    /// </summary>
    public List<string> SupportedExecutorTypes { get; set; } = new();

    /// <summary>
    /// 支持的节点类型 (向后兼容)
    /// </summary>
    public List<string> SupportedNodeTypes 
    { 
        get => SupportedExecutorTypes; 
        set => SupportedExecutorTypes = value; 
    }

    /// <summary>
    /// 最大并发执行数
    /// </summary>
    [Range(1, int.MaxValue)]
    public int MaxConcurrentExecutions { get; set; } = 10;

    /// <summary>
    /// 性能等级 (1-10, 10为最高)
    /// </summary>
    [Range(1, 10)]
    public int PerformanceLevel { get; set; } = 5;

    /// <summary>
    /// CPU核心数
    /// </summary>
    [Range(1, int.MaxValue)]
    public int CpuCores { get; set; } = 1;

    /// <summary>
    /// 内存大小 (MB)
    /// </summary>
    [Range(1, long.MaxValue)]
    public long MemoryMb { get; set; } = 1024;



    /// <summary>
    /// 磁盘空间 (MB)
    /// </summary>
    [Range(1, long.MaxValue)]
    public long DiskSpaceMb { get; set; } = 10240;

    /// <summary>
    /// 支持的工作流版本
    /// </summary>
    public List<string> SupportedWorkflowVersions { get; set; } = new();

    /// <summary>
    /// 支持的功能特性
    /// </summary>
    public HashSet<string> SupportedFeatures { get; set; } = new();

    /// <summary>
    /// 能力标签
    /// </summary>
    public HashSet<string> Tags { get; set; } = new();

    /// <summary>
    /// 支持的协议版本
    /// </summary>
    public List<string> SupportedProtocolVersions { get; set; } = new() { "1.0" };

    /// <summary>
    /// 最大内存使用率 (0-100)
    /// </summary>
    [Range(0, 100)]
    public double MaxMemoryUsagePercentage { get; set; } = 80.0;

    /// <summary>
    /// 最大CPU使用率 (0-100)
    /// </summary>
    [Range(0, 100)]
    public double MaxCpuUsagePercentage { get; set; } = 80.0;

    /// <summary>
    /// 最大磁盘使用率 (0-100)
    /// </summary>
    [Range(0, 100)]
    public double MaxDiskUsagePercentage { get; set; } = 90.0;

    /// <summary>
    /// 支持的编程语言
    /// </summary>
    public List<string> SupportedLanguages { get; set; } = new();

    /// <summary>
    /// 支持的运行时环境
    /// </summary>
    public List<string> SupportedRuntimes { get; set; } = new();

    /// <summary>
    /// 网络带宽 (Mbps)
    /// </summary>
    public double NetworkBandwidthMbps { get; set; } = 100.0;

    /// <summary>
    /// 是否支持GPU加速
    /// </summary>
    public bool SupportsGpuAcceleration { get; set; } = false;

    /// <summary>
    /// 是否有GPU（SupportsGpuAcceleration的别名，保持向后兼容）
    /// </summary>
    public bool HasGpu
    {
        get => SupportsGpuAcceleration;
        set => SupportsGpuAcceleration = value;
    }

    /// <summary>
    /// GPU信息
    /// </summary>
    public GpuInfo? GpuInfo { get; set; }

    /// <summary>
    /// 能力元数据
    /// </summary>
    public Dictionary<string, object> CapabilityMetadata { get; set; } = new();

    /// <summary>
    /// 添加支持的执行器类型
    /// </summary>
    /// <param name="executorType">执行器类型</param>
    public void AddSupportedExecutorType(string executorType)
    {
        if (!string.IsNullOrWhiteSpace(executorType) && !SupportedExecutorTypes.Contains(executorType))
        {
            SupportedExecutorTypes.Add(executorType);
        }
    }

    /// <summary>
    /// 添加能力标签
    /// </summary>
    /// <param name="tag">标签</param>
    public void AddTag(string tag)
    {
        if (!string.IsNullOrWhiteSpace(tag))
        {
            Tags.Add(tag);
        }
    }

    /// <summary>
    /// 检查是否支持指定的执行器类型
    /// </summary>
    /// <param name="executorType">执行器类型</param>
    /// <returns>是否支持</returns>
    public bool SupportsExecutorType(string executorType)
    {
        return SupportedExecutorTypes.Contains(executorType);
    }

    /// <summary>
    /// 检查是否包含指定标签
    /// </summary>
    /// <param name="tag">标签</param>
    /// <returns>是否包含</returns>
    public bool HasTag(string tag)
    {
        return Tags.Contains(tag);
    }

    /// <summary>
    /// 检查是否包含所有指定标签
    /// </summary>
    /// <param name="requiredTags">必需标签</param>
    /// <returns>是否包含所有标签</returns>
    public bool HasAllTags(IEnumerable<string> requiredTags)
    {
        return requiredTags.All(tag => Tags.Contains(tag));
    }

    /// <summary>
    /// 计算能力评分
    /// </summary>
    /// <returns>能力评分 (0-100)</returns>
    public double CalculateCapabilityScore()
    {
        var score = 0.0;
        
        // 基础性能评分 (40%)
        score += PerformanceLevel * 4.0;
        
        // CPU评分 (20%)
        score += Math.Min(CpuCores * 2.0, 20.0);
        
        // 内存评分 (20%)
        score += Math.Min(MemoryMb / 1024.0 * 2.0, 20.0);
        
        // 并发能力评分 (10%)
        score += Math.Min(MaxConcurrentExecutions * 1.0, 10.0);
        
        // 功能丰富度评分 (10%)
        score += Math.Min(SupportedExecutorTypes.Count * 2.0, 10.0);
        
        return Math.Min(score, 100.0);
    }

    /// <summary>
    /// 验证能力信息的有效性
    /// </summary>
    /// <returns>验证结果</returns>
    public bool IsValid()
    {
        return CpuCores > 0 &&
               MemoryMb > 0 &&
               DiskSpaceMb > 0 &&
               MaxConcurrentExecutions > 0 &&
               PerformanceLevel >= 1 && PerformanceLevel <= 10 &&
               MaxMemoryUsagePercentage >= 0 && MaxMemoryUsagePercentage <= 100 &&
               MaxCpuUsagePercentage >= 0 && MaxCpuUsagePercentage <= 100 &&
               MaxDiskUsagePercentage >= 0 && MaxDiskUsagePercentage <= 100;
    }

    /// <summary>
    /// 创建节点能力信息的深拷贝
    /// </summary>
    /// <returns>节点能力信息的深拷贝</returns>
    public NodeCapabilities Clone()
    {
        return new NodeCapabilities
        {
            SupportedExecutorTypes = new List<string>(SupportedExecutorTypes),
            MaxConcurrentExecutions = MaxConcurrentExecutions,
            PerformanceLevel = PerformanceLevel,
            CpuCores = CpuCores,
            MemoryMb = MemoryMb,
            DiskSpaceMb = DiskSpaceMb,
            SupportedWorkflowVersions = new List<string>(SupportedWorkflowVersions),
            SupportedFeatures = new HashSet<string>(SupportedFeatures),
            Tags = new HashSet<string>(Tags),
            SupportedProtocolVersions = new List<string>(SupportedProtocolVersions),
            MaxMemoryUsagePercentage = MaxMemoryUsagePercentage,
            MaxCpuUsagePercentage = MaxCpuUsagePercentage,
            MaxDiskUsagePercentage = MaxDiskUsagePercentage,
            SupportedLanguages = new List<string>(SupportedLanguages),
            SupportedRuntimes = new List<string>(SupportedRuntimes),
            NetworkBandwidthMbps = NetworkBandwidthMbps,
            SupportsGpuAcceleration = SupportsGpuAcceleration,
            GpuInfo = GpuInfo?.Clone(),
            CapabilityMetadata = new Dictionary<string, object>(CapabilityMetadata)
        };
    }

    /// <summary>
    /// 获取能力信息的简要描述
    /// </summary>
    /// <returns>能力信息简要描述</returns>
    public override string ToString()
    {
        return $"Capabilities[Level={PerformanceLevel}] CPU={CpuCores} Memory={MemoryMb}MB " +
               $"MaxConcurrent={MaxConcurrentExecutions} Executors={SupportedExecutorTypes.Count}";
    }
}

/// <summary>
/// GPU信息
/// </summary>
public class GpuInfo
{
    /// <summary>
    /// GPU名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// GPU内存大小 (MB)
    /// </summary>
    public long MemoryMb { get; set; } = 0;

    /// <summary>
    /// 计算能力版本
    /// </summary>
    public string ComputeCapability { get; set; } = string.Empty;

    /// <summary>
    /// 是否可用
    /// </summary>
    public bool IsAvailable { get; set; } = false;

    /// <summary>
    /// 创建GPU信息的深拷贝
    /// </summary>
    /// <returns>GPU信息的深拷贝</returns>
    public GpuInfo Clone()
    {
        return new GpuInfo
        {
            Name = Name,
            MemoryMb = MemoryMb,
            ComputeCapability = ComputeCapability,
            IsAvailable = IsAvailable
        };
    }
}
