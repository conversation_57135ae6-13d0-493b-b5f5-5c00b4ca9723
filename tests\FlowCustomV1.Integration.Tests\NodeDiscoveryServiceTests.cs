using FlowCustomV1.Core.Interfaces.Services;
using FlowCustomV1.Core.Interfaces.Messaging;
using FlowCustomV1.Core.Models.Cluster;
using FlowCustomV1.Infrastructure.Configuration;
using FlowCustomV1.Infrastructure.Services.Cluster;
using FlowCustomV1.Infrastructure.Services.Messaging;
using FlowCustomV1.Infrastructure.Extensions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Xunit;
using Xunit.Abstractions;

namespace FlowCustomV1.Integration.Tests;

/// <summary>
/// 节点发现服务集成测试
/// </summary>
public class NodeDiscoveryServiceTests : IDisposable
{
    private readonly ITestOutputHelper _output;
    private readonly ServiceProvider _serviceProvider;
    private readonly INodeDiscoveryService _nodeDiscoveryService;

    public NodeDiscoveryServiceTests(ITestOutputHelper output)
    {
        _output = output;

        // 创建配置
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["Nats:Servers:0"] = "nats://localhost:24222",
                ["Nats:Servers:1"] = "nats://localhost:24223",
                ["Nats:Servers:2"] = "nats://localhost:24224",
                ["Nats:Username"] = "flowcustom",
                ["Nats:Password"] = "flowcustom_password",
                ["Nats:ConnectionTimeout"] = "5000",
                ["Nats:ReconnectWait"] = "2000",
                ["Nats:MaxReconnectAttempts"] = "10",
                ["Nats:JetStream:Enabled"] = "true",
                ["Nats:JetStream:Domain"] = "flowcustom-cluster",
                ["MessagingTopic:RootPrefix"] = "flowcustom",
                ["MessagingTopic:Cluster:Root"] = "cluster",
                ["MessagingTopic:Cluster:NodeHeartbeat"] = "nodes.{0}.heartbeat",
                ["MessagingTopic:Cluster:ServiceDiscovery"] = "discovery",
                ["MessagingTopic:Cluster:Config"] = "config",
                ["MessagingTopic:Cluster:NodeRegister"] = "nodes.register"
            })
            .Build();

        // 配置服务
        var services = new ServiceCollection();

        // 添加日志
        services.AddLogging(builder =>
        {
            builder.AddConsole();
            builder.SetMinimumLevel(LogLevel.Debug);
        });

        // 添加配置
        services.Configure<NodeDiscoveryConfiguration>(options =>
        {
            options.ClusterName = "TestCluster";
            options.HeartbeatIntervalSeconds = 5;
            options.NodeTimeoutSeconds = 30;
            options.NodeCleanupIntervalSeconds = 10;
            options.DiscoveryTimeoutSeconds = 2;
            options.EnableAutoRegistration = true;
            options.EnableHeartbeat = true;
            options.EnableNodeCleanup = true;
            options.NodeRole = "TestNode";
            options.NodeTags = new List<string> { "test", "integration" };
            options.CapabilityTags = new List<string> { "test-capability" };
        });

        // 使用真实的NATS服务和主题服务
        services.AddNatsMessaging(configuration);

        // 添加节点发现服务
        services.AddSingleton<INodeDiscoveryService, NodeDiscoveryService>();

        _serviceProvider = services.BuildServiceProvider();
        _nodeDiscoveryService = _serviceProvider.GetRequiredService<INodeDiscoveryService>();
    }

    [Fact]
    public async Task StartAsync_ShouldInitializeService()
    {
        // Act
        await _nodeDiscoveryService.StartAsync();

        // Assert
        Assert.True(_nodeDiscoveryService.IsStarted);
        Assert.NotNull(_nodeDiscoveryService.CurrentNode);
        Assert.NotEmpty(_nodeDiscoveryService.CurrentNode.NodeId);
        
        _output.WriteLine($"Service started with node ID: {_nodeDiscoveryService.CurrentNode.NodeId}");
    }

    [Fact]
    public async Task RegisterNodeAsync_ShouldAddNodeToKnownNodes()
    {
        // Arrange
        await _nodeDiscoveryService.StartAsync();
        
        var testNode = new FlowCustomV1.Core.Models.Cluster.NodeInfo
        {
            NodeId = "test-node-001",
            NodeName = "Test Node",
            ClusterName = "TestCluster",
            Mode = NodeMode.Worker,
            Status = NodeStatus.Healthy,
            Network = new NetworkInfo
            {
                IpAddress = "*************",
                HttpPort = 5000,
                NatsPort = 4222,
                ManagementPort = 8080,
                HostName = "test-host"
            },
            Capabilities = new NodeCapabilities
            {
                CpuCores = 4,
                MemoryMb = 8192,
                MaxConcurrentExecutions = 10,
                PerformanceLevel = 7
            },
            Load = new NodeLoad
            {
                MaxTaskCapacity = 10
            },
            Health = new HealthStatus(),
            Timestamps = new Timestamps
            {
                CreatedAt = DateTime.UtcNow,
                LastActiveAt = DateTime.UtcNow
            }
        };

        // Act
        await _nodeDiscoveryService.RegisterNodeAsync(testNode);

        // Assert
        var knownNodes = _nodeDiscoveryService.KnownNodes;
        Assert.Contains(knownNodes, n => n.NodeId == testNode.NodeId);
        
        _output.WriteLine($"Node registered: {testNode.NodeId}");
        _output.WriteLine($"Total known nodes: {knownNodes.Count}");
    }

    [Fact]
    public async Task DiscoverAllNodesAsync_ShouldReturnKnownNodes()
    {
        // Arrange
        await _nodeDiscoveryService.StartAsync();

        // Act
        var discoveredNodes = await _nodeDiscoveryService.DiscoverAllNodesAsync();

        // Assert
        Assert.NotNull(discoveredNodes);
        Assert.NotEmpty(discoveredNodes);
        
        // 至少应该包含当前节点
        Assert.Contains(discoveredNodes, n => n.NodeId == _nodeDiscoveryService.CurrentNode.NodeId);
        
        _output.WriteLine($"Discovered {discoveredNodes.Count} nodes:");
        foreach (var node in discoveredNodes)
        {
            _output.WriteLine($"  - {node.NodeId} ({node.NodeName}) - {node.Status}");
        }
    }

    [Fact]
    public async Task UpdateNodeLoadAsync_ShouldUpdateNodeLoad()
    {
        // Arrange
        await _nodeDiscoveryService.StartAsync();
        var currentNodeId = _nodeDiscoveryService.CurrentNode.NodeId;
        
        var newLoadInfo = new NodeLoad
        {
            CpuUsagePercentage = 75.5,
            MemoryUsagePercentage = 60.2,
            ActiveTaskCount = 5,
            QueuedTaskCount = 2,
            MaxTaskCapacity = 10,
            LoadScore = 67.8
        };

        // Act
        await _nodeDiscoveryService.UpdateNodeLoadAsync(currentNodeId, newLoadInfo);

        // Assert
        var updatedNode = _nodeDiscoveryService.KnownNodes.FirstOrDefault(n => n.NodeId == currentNodeId);
        Assert.NotNull(updatedNode);
        Assert.Equal(newLoadInfo.CpuUsagePercentage, updatedNode.Load.CpuUsagePercentage);
        Assert.Equal(newLoadInfo.MemoryUsagePercentage, updatedNode.Load.MemoryUsagePercentage);
        Assert.Equal(newLoadInfo.ActiveTaskCount, updatedNode.Load.ActiveTaskCount);
        
        _output.WriteLine($"Node load updated: CPU={newLoadInfo.CpuUsagePercentage}%, Memory={newLoadInfo.MemoryUsagePercentage}%");
    }

    [Fact]
    public async Task StopAsync_ShouldStopService()
    {
        // Arrange
        await _nodeDiscoveryService.StartAsync();
        Assert.True(_nodeDiscoveryService.IsStarted);

        // Act
        await _nodeDiscoveryService.StopAsync();

        // Assert
        Assert.False(_nodeDiscoveryService.IsStarted);
        
        _output.WriteLine("Service stopped successfully");
    }

    [Fact]
    public void CurrentNode_ShouldHaveValidProperties()
    {
        // Act
        var currentNode = _nodeDiscoveryService.CurrentNode;

        // Assert
        Assert.NotNull(currentNode);
        Assert.NotEmpty(currentNode.NodeId);
        Assert.NotEmpty(currentNode.NodeName);
        Assert.NotEmpty(currentNode.ClusterName);
        Assert.True(currentNode.IsValid());
        
        _output.WriteLine($"Current node: {currentNode.NodeId} ({currentNode.NodeName})");
        _output.WriteLine($"Cluster: {currentNode.ClusterName}");
        _output.WriteLine($"Mode: {currentNode.Mode}");
        _output.WriteLine($"Status: {currentNode.Status}");
    }

    public void Dispose()
    {
        try
        {
            if (_nodeDiscoveryService.IsStarted)
            {
                _nodeDiscoveryService.StopAsync().GetAwaiter().GetResult();
            }
        }
        catch (Exception ex)
        {
            _output.WriteLine($"Error stopping service: {ex.Message}");
        }

        _serviceProvider?.Dispose();
    }
}
