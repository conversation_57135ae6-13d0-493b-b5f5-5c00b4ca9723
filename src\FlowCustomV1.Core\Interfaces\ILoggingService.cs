using Microsoft.Extensions.Logging;

namespace FlowCustomV1.Core.Interfaces;

/// <summary>
/// 统一日志服务接口
/// 提供结构化日志记录功能，支持不同日志级别和上下文信息
/// </summary>
public interface ILoggingService
{
    /// <summary>
    /// 记录调试级别日志
    /// </summary>
    /// <param name="message">日志消息</param>
    /// <param name="args">格式化参数</param>
    void LogDebug(string message, params object[] args);

    /// <summary>
    /// 记录信息级别日志
    /// </summary>
    /// <param name="message">日志消息</param>
    /// <param name="args">格式化参数</param>
    void LogInformation(string message, params object[] args);

    /// <summary>
    /// 记录警告级别日志
    /// </summary>
    /// <param name="message">日志消息</param>
    /// <param name="args">格式化参数</param>
    void LogWarning(string message, params object[] args);

    /// <summary>
    /// 记录错误级别日志
    /// </summary>
    /// <param name="message">日志消息</param>
    /// <param name="args">格式化参数</param>
    void LogError(string message, params object[] args);

    /// <summary>
    /// 记录错误级别日志（包含异常信息）
    /// </summary>
    /// <param name="exception">异常对象</param>
    /// <param name="message">日志消息</param>
    /// <param name="args">格式化参数</param>
    void LogError(Exception exception, string message, params object[] args);

    /// <summary>
    /// 记录严重错误级别日志
    /// </summary>
    /// <param name="message">日志消息</param>
    /// <param name="args">格式化参数</param>
    void LogCritical(string message, params object[] args);

    /// <summary>
    /// 记录严重错误级别日志（包含异常信息）
    /// </summary>
    /// <param name="exception">异常对象</param>
    /// <param name="message">日志消息</param>
    /// <param name="args">格式化参数</param>
    void LogCritical(Exception exception, string message, params object[] args);

    /// <summary>
    /// 创建日志作用域
    /// </summary>
    /// <param name="state">作用域状态</param>
    /// <returns>可释放的作用域对象</returns>
    IDisposable BeginScope<TState>(TState state) where TState : notnull;

    /// <summary>
    /// 检查指定日志级别是否启用
    /// </summary>
    /// <param name="logLevel">日志级别</param>
    /// <returns>如果启用返回true，否则返回false</returns>
    bool IsEnabled(LogLevel logLevel);

    /// <summary>
    /// 记录结构化日志
    /// </summary>
    /// <param name="logLevel">日志级别</param>
    /// <param name="eventId">事件ID</param>
    /// <param name="state">日志状态</param>
    /// <param name="exception">异常对象（可选）</param>
    /// <param name="formatter">格式化函数</param>
    void Log<TState>(LogLevel logLevel, EventId eventId, TState state, Exception? exception, Func<TState, Exception?, string> formatter);
}
