using FlowCustomV1.Core.Models.Cluster;
using FlowCustomV1.Core.Interfaces.Scheduling;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace FlowCustomV1.Core.Models.Scheduling;

/// <summary>
/// 重新平衡类型枚举
/// </summary>
public enum RebalancingType
{
    /// <summary>
    /// 任务迁移
    /// </summary>
    TaskMigration,

    /// <summary>
    /// 节点扩容
    /// </summary>
    NodeScaleUp,

    /// <summary>
    /// 节点缩容
    /// </summary>
    NodeScaleDown,

    /// <summary>
    /// 负载重分配
    /// </summary>
    LoadRedistribution
}

/// <summary>
/// 负载重新平衡结果
/// </summary>
public class LoadRebalancingResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 重新平衡开始时间
    /// </summary>
    public DateTime StartTime { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 重新平衡结束时间
    /// </summary>
    public DateTime EndTime { get; set; }

    /// <summary>
    /// 总耗时（毫秒）
    /// </summary>
    public long TotalTimeMs => (long)(EndTime - StartTime).TotalMilliseconds;

    /// <summary>
    /// 执行的重新平衡操作
    /// </summary>
    public List<RebalancingOperation> Operations { get; set; } = new();

    /// <summary>
    /// 重新平衡前的负载状态
    /// </summary>
    public LoadBalancingStatus? BeforeStatus { get; set; }

    /// <summary>
    /// 重新平衡后的负载状态
    /// </summary>
    public LoadBalancingStatus? AfterStatus { get; set; }

    /// <summary>
    /// 改善程度（0-1）
    /// </summary>
    public double ImprovementScore { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// 警告信息
    /// </summary>
    public List<string> Warnings { get; set; } = new();
}

/// <summary>
/// 重新平衡操作
/// </summary>
public class RebalancingOperation
{
    /// <summary>
    /// 操作ID
    /// </summary>
    public string OperationId { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 操作类型
    /// </summary>
    public RebalancingType Type { get; set; }

    /// <summary>
    /// 源节点ID
    /// </summary>
    public string SourceNodeId { get; set; } = string.Empty;

    /// <summary>
    /// 目标节点ID
    /// </summary>
    public string TargetNodeId { get; set; } = string.Empty;

    /// <summary>
    /// 涉及的任务ID列表
    /// </summary>
    public List<string> TaskIds { get; set; } = new();

    /// <summary>
    /// 操作状态
    /// </summary>
    public OperationStatus Status { get; set; } = OperationStatus.Pending;

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 操作详情
    /// </summary>
    public Dictionary<string, object> Details { get; set; } = new();
}

/// <summary>
/// 操作状态枚举
/// </summary>
public enum OperationStatus
{
    /// <summary>
    /// 等待中
    /// </summary>
    Pending,

    /// <summary>
    /// 执行中
    /// </summary>
    Running,

    /// <summary>
    /// 已完成
    /// </summary>
    Completed,

    /// <summary>
    /// 已失败
    /// </summary>
    Failed,

    /// <summary>
    /// 已取消
    /// </summary>
    Cancelled
}

/// <summary>
/// 任务分发统计信息
/// </summary>
public class TaskDistributionStatistics
{
    /// <summary>
    /// 统计时间范围开始
    /// </summary>
    public DateTime StartTime { get; set; }

    /// <summary>
    /// 统计时间范围结束
    /// </summary>
    public DateTime EndTime { get; set; }

    /// <summary>
    /// 总分发任务数
    /// </summary>
    public long TotalDistributedTasks { get; set; }

    /// <summary>
    /// 成功分发任务数
    /// </summary>
    public long SuccessfulDistributions { get; set; }

    /// <summary>
    /// 失败分发任务数
    /// </summary>
    public long FailedDistributions { get; set; }

    /// <summary>
    /// 成功率
    /// </summary>
    public double SuccessRate => TotalDistributedTasks > 0 ? (double)SuccessfulDistributions / TotalDistributedTasks : 0;

    /// <summary>
    /// 平均分发时间（毫秒）
    /// </summary>
    public double AverageDistributionTimeMs { get; set; }

    /// <summary>
    /// 最大分发时间（毫秒）
    /// </summary>
    public long MaxDistributionTimeMs { get; set; }

    /// <summary>
    /// 最小分发时间（毫秒）
    /// </summary>
    public long MinDistributionTimeMs { get; set; }

    /// <summary>
    /// 按策略分组的统计
    /// </summary>
    public Dictionary<TaskDistributionStrategy, StrategyStatistics> StrategyStats { get; set; } = new();

    /// <summary>
    /// 按节点分组的统计
    /// </summary>
    public Dictionary<string, NodeDistributionStatistics> NodeStats { get; set; } = new();

    /// <summary>
    /// 按优先级分组的统计
    /// </summary>
    public Dictionary<int, PriorityStatistics> PriorityStats { get; set; } = new();

    /// <summary>
    /// 重新分发统计
    /// </summary>
    public RedistributionStatistics RedistributionStats { get; set; } = new();
}

/// <summary>
/// 策略统计信息
/// </summary>
public class StrategyStatistics
{
    /// <summary>
    /// 使用次数
    /// </summary>
    public long UsageCount { get; set; }

    /// <summary>
    /// 成功次数
    /// </summary>
    public long SuccessCount { get; set; }

    /// <summary>
    /// 失败次数
    /// </summary>
    public long FailureCount { get; set; }

    /// <summary>
    /// 成功率
    /// </summary>
    public double SuccessRate => UsageCount > 0 ? (double)SuccessCount / UsageCount : 0;

    /// <summary>
    /// 平均分发时间（毫秒）
    /// </summary>
    public double AverageTimeMs { get; set; }

    /// <summary>
    /// 平均选择评分
    /// </summary>
    public double AverageSelectionScore { get; set; }
}

/// <summary>
/// 节点分发统计信息
/// </summary>
public class NodeDistributionStatistics
{
    /// <summary>
    /// 节点ID
    /// </summary>
    public string NodeId { get; set; } = string.Empty;

    /// <summary>
    /// 节点名称
    /// </summary>
    public string NodeName { get; set; } = string.Empty;

    /// <summary>
    /// 分配的任务数
    /// </summary>
    public long AssignedTasks { get; set; }

    /// <summary>
    /// 完成的任务数
    /// </summary>
    public long CompletedTasks { get; set; }

    /// <summary>
    /// 失败的任务数
    /// </summary>
    public long FailedTasks { get; set; }

    /// <summary>
    /// 任务完成率
    /// </summary>
    public double CompletionRate => AssignedTasks > 0 ? (double)CompletedTasks / AssignedTasks : 0;

    /// <summary>
    /// 平均任务执行时间（毫秒）
    /// </summary>
    public double AverageExecutionTimeMs { get; set; }

    /// <summary>
    /// 平均负载评分
    /// </summary>
    public double AverageLoadScore { get; set; }

    /// <summary>
    /// 选择次数
    /// </summary>
    public long SelectionCount { get; set; }

    /// <summary>
    /// 选择评分总和
    /// </summary>
    public double TotalSelectionScore { get; set; }

    /// <summary>
    /// 平均选择评分
    /// </summary>
    public double AverageSelectionScore => SelectionCount > 0 ? TotalSelectionScore / SelectionCount : 0;
}



/// <summary>
/// 重新分发统计信息
/// </summary>
public class RedistributionStatistics
{
    /// <summary>
    /// 重新分发次数
    /// </summary>
    public long RedistributionCount { get; set; }

    /// <summary>
    /// 重新分发成功次数
    /// </summary>
    public long SuccessfulRedistributions { get; set; }

    /// <summary>
    /// 重新分发失败次数
    /// </summary>
    public long FailedRedistributions { get; set; }

    /// <summary>
    /// 重新分发成功率
    /// </summary>
    public double RedistributionSuccessRate => RedistributionCount > 0 ? (double)SuccessfulRedistributions / RedistributionCount : 0;

    /// <summary>
    /// 平均重新分发时间（毫秒）
    /// </summary>
    public double AverageRedistributionTimeMs { get; set; }

    /// <summary>
    /// 按原因分组的重新分发统计
    /// </summary>
    public Dictionary<string, long> RedistributionReasons { get; set; } = new();
}

/// <summary>
/// 节点性能指标
/// </summary>
public class NodePerformanceMetrics
{
    /// <summary>
    /// 节点ID
    /// </summary>
    public string NodeId { get; set; } = string.Empty;

    /// <summary>
    /// 节点名称
    /// </summary>
    public string NodeName { get; set; } = string.Empty;

    /// <summary>
    /// 节点角色
    /// </summary>
    public string NodeRole { get; set; } = string.Empty;

    /// <summary>
    /// 测量时间
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// CPU性能指标
    /// </summary>
    public CpuMetrics CpuMetrics { get; set; } = new();

    /// <summary>
    /// 内存性能指标
    /// </summary>
    public MemoryMetrics MemoryMetrics { get; set; } = new();

    /// <summary>
    /// 磁盘性能指标
    /// </summary>
    public DiskMetrics DiskMetrics { get; set; } = new();

    /// <summary>
    /// 网络性能指标
    /// </summary>
    public NetworkMetrics NetworkMetrics { get; set; } = new();

    /// <summary>
    /// 任务执行指标
    /// </summary>
    public TaskExecutionMetrics TaskMetrics { get; set; } = new();

    /// <summary>
    /// 系统负载指标
    /// </summary>
    public SystemLoadMetrics SystemMetrics { get; set; } = new();

    /// <summary>
    /// 健康状态
    /// </summary>
    public string HealthStatus { get; set; } = "Unknown";

    /// <summary>
    /// 性能评分（0-100）
    /// </summary>
    public double PerformanceScore { get; set; }

    /// <summary>
    /// 可用性评分（0-100）
    /// </summary>
    public double AvailabilityScore { get; set; }

    /// <summary>
    /// 可靠性评分（0-100）
    /// </summary>
    public double ReliabilityScore { get; set; }

    /// <summary>
    /// 综合评分（0-100）
    /// </summary>
    public double OverallScore { get; set; }
}

/// <summary>
/// CPU性能指标
/// </summary>
public class CpuMetrics
{
    /// <summary>
    /// CPU使用率（0-100）
    /// </summary>
    [Range(0, 100)]
    public double UsagePercent { get; set; }

    /// <summary>
    /// CPU核心数
    /// </summary>
    public int CoreCount { get; set; }

    /// <summary>
    /// CPU频率（MHz）
    /// </summary>
    public double FrequencyMHz { get; set; }

    /// <summary>
    /// 1分钟平均负载
    /// </summary>
    public double LoadAverage1Min { get; set; }

    /// <summary>
    /// 5分钟平均负载
    /// </summary>
    public double LoadAverage5Min { get; set; }

    /// <summary>
    /// 15分钟平均负载
    /// </summary>
    public double LoadAverage15Min { get; set; }

    /// <summary>
    /// 用户态CPU时间百分比
    /// </summary>
    public double UserTimePercent { get; set; }

    /// <summary>
    /// 系统态CPU时间百分比
    /// </summary>
    public double SystemTimePercent { get; set; }

    /// <summary>
    /// 空闲CPU时间百分比
    /// </summary>
    public double IdleTimePercent { get; set; }

    /// <summary>
    /// I/O等待时间百分比
    /// </summary>
    public double IoWaitPercent { get; set; }
}

/// <summary>
/// 内存性能指标
/// </summary>
public class MemoryMetrics
{
    /// <summary>
    /// 内存使用率（0-100）
    /// </summary>
    [Range(0, 100)]
    public double UsagePercent { get; set; }

    /// <summary>
    /// 总内存（MB）
    /// </summary>
    public long TotalMB { get; set; }

    /// <summary>
    /// 已使用内存（MB）
    /// </summary>
    public long UsedMB { get; set; }

    /// <summary>
    /// 可用内存（MB）
    /// </summary>
    public long AvailableMB { get; set; }

    /// <summary>
    /// 缓存内存（MB）
    /// </summary>
    public long CachedMB { get; set; }

    /// <summary>
    /// 缓冲区内存（MB）
    /// </summary>
    public long BuffersMB { get; set; }

    /// <summary>
    /// 交换分区总大小（MB）
    /// </summary>
    public long SwapTotalMB { get; set; }

    /// <summary>
    /// 交换分区已使用（MB）
    /// </summary>
    public long SwapUsedMB { get; set; }

    /// <summary>
    /// 内存分页错误率
    /// </summary>
    public double PageFaultRate { get; set; }
}

/// <summary>
/// 磁盘性能指标
/// </summary>
public class DiskMetrics
{
    /// <summary>
    /// 磁盘使用率（0-100）
    /// </summary>
    [Range(0, 100)]
    public double UsagePercent { get; set; }

    /// <summary>
    /// 总磁盘空间（MB）
    /// </summary>
    public long TotalMB { get; set; }

    /// <summary>
    /// 已使用磁盘空间（MB）
    /// </summary>
    public long UsedMB { get; set; }

    /// <summary>
    /// 可用磁盘空间（MB）
    /// </summary>
    public long AvailableMB { get; set; }

    /// <summary>
    /// 磁盘读取速度（MB/s）
    /// </summary>
    public double ReadSpeedMBps { get; set; }

    /// <summary>
    /// 磁盘写入速度（MB/s）
    /// </summary>
    public double WriteSpeedMBps { get; set; }

    /// <summary>
    /// 磁盘I/O使用率（0-100）
    /// </summary>
    [Range(0, 100)]
    public double IoUtilizationPercent { get; set; }

    /// <summary>
    /// 平均I/O等待时间（毫秒）
    /// </summary>
    public double AvgIoWaitMs { get; set; }

    /// <summary>
    /// 磁盘队列长度
    /// </summary>
    public double QueueLength { get; set; }
}

/// <summary>
/// 网络性能指标
/// </summary>
public class NetworkMetrics
{
    /// <summary>
    /// 网络使用率（0-100）
    /// </summary>
    [Range(0, 100)]
    public double UsagePercent { get; set; }

    /// <summary>
    /// 网络带宽（Mbps）
    /// </summary>
    public double BandwidthMbps { get; set; }

    /// <summary>
    /// 入站流量速度（MB/s）
    /// </summary>
    public double InboundSpeedMBps { get; set; }

    /// <summary>
    /// 出站流量速度（MB/s）
    /// </summary>
    public double OutboundSpeedMBps { get; set; }

    /// <summary>
    /// 网络延迟（毫秒）
    /// </summary>
    public double LatencyMs { get; set; }

    /// <summary>
    /// 丢包率（0-100）
    /// </summary>
    [Range(0, 100)]
    public double PacketLossPercent { get; set; }

    /// <summary>
    /// 连接数
    /// </summary>
    public int ConnectionCount { get; set; }

    /// <summary>
    /// 最大连接数
    /// </summary>
    public int MaxConnections { get; set; }
}

/// <summary>
/// 任务执行指标
/// </summary>
public class TaskExecutionMetrics
{
    /// <summary>
    /// 活跃任务数
    /// </summary>
    public int ActiveTasks { get; set; }

    /// <summary>
    /// 最大任务数
    /// </summary>
    public int MaxTasks { get; set; }

    /// <summary>
    /// 队列中的任务数
    /// </summary>
    public int QueuedTasks { get; set; }

    /// <summary>
    /// 已完成任务数
    /// </summary>
    public long CompletedTasks { get; set; }

    /// <summary>
    /// 失败任务数
    /// </summary>
    public long FailedTasks { get; set; }

    /// <summary>
    /// 平均执行时间（毫秒）
    /// </summary>
    public double AverageExecutionTimeMs { get; set; }

    /// <summary>
    /// 最大执行时间（毫秒）
    /// </summary>
    public long MaxExecutionTimeMs { get; set; }

    /// <summary>
    /// 最小执行时间（毫秒）
    /// </summary>
    public long MinExecutionTimeMs { get; set; }

    /// <summary>
    /// 任务成功率（0-100）
    /// </summary>
    [Range(0, 100)]
    public double SuccessRate { get; set; }

    /// <summary>
    /// 任务吞吐量（任务/秒）
    /// </summary>
    public double ThroughputPerSecond { get; set; }

    /// <summary>
    /// 平均等待时间（毫秒）
    /// </summary>
    public double AverageWaitTimeMs { get; set; }
}

/// <summary>
/// 系统负载指标
/// </summary>
public class SystemLoadMetrics
{
    /// <summary>
    /// 系统负载评分（0-100）
    /// </summary>
    [Range(0, 100)]
    public double LoadScore { get; set; }

    /// <summary>
    /// 1分钟平均负载
    /// </summary>
    public double LoadAverage1Min { get; set; }

    /// <summary>
    /// 5分钟平均负载
    /// </summary>
    public double LoadAverage5Min { get; set; }

    /// <summary>
    /// 15分钟平均负载
    /// </summary>
    public double LoadAverage15Min { get; set; }

    /// <summary>
    /// 进程数
    /// </summary>
    public int ProcessCount { get; set; }

    /// <summary>
    /// 线程数
    /// </summary>
    public int ThreadCount { get; set; }

    /// <summary>
    /// 文件描述符使用数
    /// </summary>
    public int FileDescriptorCount { get; set; }

    /// <summary>
    /// 最大文件描述符数
    /// </summary>
    public int MaxFileDescriptors { get; set; }

    /// <summary>
    /// 系统启动时间
    /// </summary>
    public DateTime SystemStartTime { get; set; }

    /// <summary>
    /// 系统运行时间（秒）
    /// </summary>
    public long UptimeSeconds { get; set; }
}
