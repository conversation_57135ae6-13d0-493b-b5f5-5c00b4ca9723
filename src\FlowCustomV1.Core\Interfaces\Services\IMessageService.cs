using FlowCustomV1.Core.Models.Messages;

namespace FlowCustomV1.Core.Interfaces.Services;

/// <summary>
/// 消息服务接口
/// 提供消息发送、接收和路由功能
/// </summary>
public interface IMessageService
{
    /// <summary>
    /// 发送消息
    /// </summary>
    /// <param name="message">消息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送结果</returns>
    Task<bool> SendMessageAsync(BaseMessage message, CancellationToken cancellationToken = default);

    /// <summary>
    /// 发送消息到指定主题
    /// </summary>
    /// <param name="topic">主题</param>
    /// <param name="message">消息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送结果</returns>
    Task<bool> SendMessageAsync(string topic, BaseMessage message, CancellationToken cancellationToken = default);

    /// <summary>
    /// 订阅消息
    /// </summary>
    /// <param name="topic">主题</param>
    /// <param name="handler">消息处理器</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>订阅ID</returns>
    Task<string> SubscribeAsync(string topic, Func<BaseMessage, Task> handler, CancellationToken cancellationToken = default);

    /// <summary>
    /// 取消订阅
    /// </summary>
    /// <param name="subscriptionId">订阅ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>取消结果</returns>
    Task<bool> UnsubscribeAsync(string subscriptionId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 广播消息
    /// </summary>
    /// <param name="message">消息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>广播结果</returns>
    Task<bool> BroadcastMessageAsync(BaseMessage message, CancellationToken cancellationToken = default);
}

/// <summary>
/// 消息路由服务接口
/// </summary>
public interface IMessageRoutingService
{
    /// <summary>
    /// 路由消息到目标节点
    /// </summary>
    /// <param name="message">消息</param>
    /// <param name="targetNodeId">目标节点ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>路由结果</returns>
    Task<bool> RouteMessageAsync(BaseMessage message, string targetNodeId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 根据负载均衡策略路由消息
    /// </summary>
    /// <param name="message">消息</param>
    /// <param name="nodeType">节点类型</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>路由结果</returns>
    Task<bool> RouteMessageWithLoadBalancingAsync(BaseMessage message, string nodeType, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取消息路由统计
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>路由统计</returns>
    Task<MessageRoutingStatistics> GetRoutingStatisticsAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// 消息路由统计
/// </summary>
public class MessageRoutingStatistics
{
    /// <summary>
    /// 总消息数
    /// </summary>
    public long TotalMessages { get; set; }

    /// <summary>
    /// 成功路由数
    /// </summary>
    public long SuccessfulRoutes { get; set; }

    /// <summary>
    /// 失败路由数
    /// </summary>
    public long FailedRoutes { get; set; }

    /// <summary>
    /// 平均路由时间（毫秒）
    /// </summary>
    public double AverageRoutingTimeMs { get; set; }

    /// <summary>
    /// 按节点类型统计
    /// </summary>
    public Dictionary<string, long> MessagesByNodeType { get; set; } = new();

    /// <summary>
    /// 统计时间
    /// </summary>
    public DateTime StatisticsTime { get; set; } = DateTime.UtcNow;
}
