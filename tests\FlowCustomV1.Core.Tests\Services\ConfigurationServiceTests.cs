using Microsoft.Extensions.Configuration;
using FlowCustomV1.Core.Services;

namespace FlowCustomV1.Core.Tests.Services;

/// <summary>
/// 配置服务测试
/// 验证配置服务的功能正确性
/// </summary>
public class ConfigurationServiceTests
{
    private readonly IConfiguration _configuration;
    private readonly ConfigurationService _configurationService;

    public ConfigurationServiceTests()
    {
        _configuration = CreateTestConfiguration();
        _configurationService = new ConfigurationService(_configuration);
    }

    /// <summary>
    /// 测试获取字符串值
    /// </summary>
    [Fact]
    public void GetValue_WithExistingKey_ShouldReturnValue()
    {
        // Arrange
        const string key = "TestKey";
        const string expectedValue = "TestValue";

        // Act
        var result = _configurationService.GetValue(key);

        // Assert
        Assert.Equal(expectedValue, result);
    }

    /// <summary>
    /// 测试获取不存在的键
    /// </summary>
    [Fact]
    public void GetValue_WithNonExistingKey_ShouldReturnNull()
    {
        // Arrange
        const string key = "NonExistingKey";

        // Act
        var result = _configurationService.GetValue(key);

        // Assert
        Assert.Null(result);
    }

    /// <summary>
    /// 测试获取带默认值的字符串值
    /// </summary>
    [Fact]
    public void GetValue_WithDefaultValue_ShouldReturnValue()
    {
        // Arrange
        const string key = "TestKey";
        const string defaultValue = "DefaultValue";
        const string expectedValue = "TestValue";

        // Act
        var result = _configurationService.GetValue(key, defaultValue);

        // Assert
        Assert.Equal(expectedValue, result);
    }

    /// <summary>
    /// 测试获取不存在键的默认值
    /// </summary>
    [Fact]
    public void GetValue_WithNonExistingKeyAndDefaultValue_ShouldReturnDefaultValue()
    {
        // Arrange
        const string key = "NonExistingKey";
        const string defaultValue = "DefaultValue";

        // Act
        var result = _configurationService.GetValue(key, defaultValue);

        // Assert
        Assert.Equal(defaultValue, result);
    }

    /// <summary>
    /// 测试获取泛型值
    /// </summary>
    [Fact]
    public void GetValue_Generic_WithExistingKey_ShouldReturnTypedValue()
    {
        // Arrange
        const string key = "IntValue";
        const int expectedValue = 42;

        // Act
        var result = _configurationService.GetValue<int>(key);

        // Assert
        Assert.Equal(expectedValue, result);
    }

    /// <summary>
    /// 测试获取泛型值带默认值
    /// </summary>
    [Fact]
    public void GetValue_Generic_WithDefaultValue_ShouldReturnValue()
    {
        // Arrange
        const string key = "IntValue";
        const int defaultValue = 100;
        const int expectedValue = 42;

        // Act
        var result = _configurationService.GetValue(key, defaultValue);

        // Assert
        Assert.Equal(expectedValue, result);
    }

    /// <summary>
    /// 测试获取不存在键的泛型默认值
    /// </summary>
    [Fact]
    public void GetValue_Generic_WithNonExistingKeyAndDefaultValue_ShouldReturnDefaultValue()
    {
        // Arrange
        const string key = "NonExistingIntValue";
        const int defaultValue = 100;

        // Act
        var result = _configurationService.GetValue(key, defaultValue);

        // Assert
        Assert.Equal(defaultValue, result);
    }

    /// <summary>
    /// 测试获取配置节
    /// </summary>
    [Fact]
    public void GetSection_WithExistingSection_ShouldReturnSection()
    {
        // Arrange
        const string sectionName = "Logging";

        // Act
        var section = _configurationService.GetSection(sectionName);

        // Assert
        Assert.NotNull(section);
        Assert.Equal(sectionName, section.Key);
    }

    /// <summary>
    /// 测试获取配置键
    /// </summary>
    [Fact]
    public void GetKeys_ShouldReturnKeys()
    {
        // Act
        var keys = _configurationService.GetKeys();

        // Assert
        Assert.NotNull(keys);
        Assert.NotEmpty(keys);
        Assert.Contains("TestKey", keys);
    }

    /// <summary>
    /// 测试配置存在性检查
    /// </summary>
    [Fact]
    public void Exists_WithExistingKey_ShouldReturnTrue()
    {
        // Arrange
        const string key = "TestKey";

        // Act
        var result = _configurationService.Exists(key);

        // Assert
        Assert.True(result);
    }

    /// <summary>
    /// 测试配置不存在性检查
    /// </summary>
    [Fact]
    public void Exists_WithNonExistingKey_ShouldReturnFalse()
    {
        // Arrange
        const string key = "NonExistingKey";

        // Act
        var result = _configurationService.Exists(key);

        // Assert
        Assert.False(result);
    }

    /// <summary>
    /// 测试获取带前缀的配置键
    /// </summary>
    [Fact]
    public void GetKeys_WithPrefix_ShouldReturnMatchingKeys()
    {
        // Arrange
        const string prefix = "Logging";

        // Act
        var keys = _configurationService.GetKeys(prefix);

        // Assert
        Assert.NotNull(keys);
        Assert.NotEmpty(keys);
        Assert.All(keys, key => Assert.StartsWith(prefix, key));
    }

    /// <summary>
    /// 测试配置验证
    /// </summary>
    [Fact]
    public void ValidateConfiguration_WithAllRequiredKeys_ShouldReturnValid()
    {
        // Arrange
        var requiredKeys = new[] { "TestKey", "IntValue" };

        // Act
        var result = _configurationService.ValidateConfiguration(requiredKeys);

        // Assert
        Assert.True(result.IsValid);
        Assert.Empty(result.InvalidConfigurations);
    }

    /// <summary>
    /// 测试空键异常
    /// </summary>
    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    public void GetValue_WithInvalidKey_ShouldThrowArgumentException(string key)
    {
        // Act & Assert
        Assert.Throws<ArgumentException>(() => _configurationService.GetValue(key));
    }

    /// <summary>
    /// 测试null键异常
    /// </summary>
    [Fact]
    public void GetValue_WithNullKey_ShouldThrowArgumentNullException()
    {
        // Act & Assert
        Assert.Throws<ArgumentNullException>(() => _configurationService.GetValue(null!));
    }

    /// <summary>
    /// 测试构造函数空参数异常
    /// </summary>
    [Fact]
    public void Constructor_WithNullConfiguration_ShouldThrowArgumentNullException()
    {
        // Arrange
        IConfiguration? configuration = null;

        // Act & Assert
        Assert.Throws<ArgumentNullException>(() => new ConfigurationService(configuration!));
    }

    /// <summary>
    /// 创建测试配置
    /// </summary>
    /// <returns>测试配置对象</returns>
    private static IConfiguration CreateTestConfiguration()
    {
        var configurationBuilder = new ConfigurationBuilder();
        configurationBuilder.AddInMemoryCollection(new Dictionary<string, string?>
        {
            ["TestKey"] = "TestValue",
            ["IntValue"] = "42",
            ["BoolValue"] = "true",
            ["Logging:LogLevel:Default"] = "Information",
            ["Logging:LogLevel:Microsoft"] = "Warning",
            ["ConnectionStrings:DefaultConnection"] = "Server=localhost;Database=TestDb;"
        });
        
        return configurationBuilder.Build();
    }
}
