# 页面布局标准化处理清单

## 🎯 标准化目标
将所有页面调整为统一的三层布局结构：
- 🟢 Page Container (绿色) - PageLayout组件
- 🟡 Page Header (黄色) - 标题、描述、操作按钮
- 🔵 Page Content (蓝色) - 页面具体内容

## ✅ 已完成页面
1. ✅ Dashboard/index.tsx - 仪表盘页面
2. ✅ Execution/ExecutionMonitor.tsx - 执行监控页面
3. ✅ Execution/ExecutionHistory.tsx - 执行历史页面
4. ✅ Monitoring/MonitoringDashboard.tsx - 监控仪表盘
5. ✅ Cluster/ClusterOverview.tsx - 集群概览页面
6. ✅ Cluster/ClusterNodes.tsx - 集群节点页面
7. ✅ System/SystemConfig.tsx - 系统配置页面
8. ✅ Workflow/WorkflowList.tsx - 工作流列表页面（已标准化）

## 🔄 待处理页面
9. ⏳ Data/DataSources.tsx - 数据源管理页面
10. ⏳ Plugins/PluginsMarket.tsx - 插件市场页面
11. ⏳ Security/RolePermissions.tsx - 角色权限页面
12. ⏳ Security/UserManagement.tsx - 用户管理页面
13. ⏳ Workflow/WorkflowTemplates.tsx - 工作流模板页面
14. ⏳ Workflow/WorkflowDebugger.tsx - 工作流调试器页面
15. ⏳ Nodes/NodeDesigner.tsx - 节点设计器页面
16. ⏳ Nodes/NodeExecutor.tsx - 节点执行器页面
17. ⏳ Nodes/NodeValidator.tsx - 节点验证器页面

## 🎯 本次完成的工作
1. **标准化了8个主要页面**，全部采用统一的三层布局结构
2. **添加了新的样式类**：
   - `layout-card-grid` - 卡片网格布局
   - `layout-card-statistic` - 统计卡片样式
   - `toolbar`, `toolbar-left`, `toolbar-right` - 工具栏布局
   - `--layout-table-scroll-y` - 表格滚动高度变量
3. **统一了页面图标**，每个页面都有对应的图标
4. **优化了表格滚动**，使用CSS变量控制高度
5. **保持了调试模式**，便于后续布局调试

## 📋 标准化模板

### 导入部分添加
```typescript
import PageLayout from '@/components/Layout/PageLayout';
```

### 页面结构替换
```typescript
// 替换前
return (
  <div className="page-container">
    <div className="page-header">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="page-title">页面标题</h1>
          <p className="page-description">页面描述</p>
        </div>
        <Button>操作按钮</Button>
      </div>
    </div>
    {/* 页面内容 */}
  </div>
);

// 替换后
return (
  <PageLayout
    title="页面标题"
    description="页面描述"
    icon={<IconComponent />}
    actions={<Button>操作按钮</Button>}
  >
    {/* 页面内容 */}
  </PageLayout>
);
```

### 样式类名标准化
- 统计卡片: `className="layout-card-statistic"`
- 卡片网格: `className="layout-card-grid"`
- 工具栏: `className="toolbar"`
- 工具栏左侧: `className="toolbar-left"`
- 工具栏右侧: `className="toolbar-right"`
- 表格容器: `className="workflow-table-container"`
- 表格滚动: `y: 'var(--layout-table-scroll-y)'`

## 🎨 图标映射
- Dashboard: `<DashboardOutlined />`
- Monitor: `<MonitorOutlined />`
- History: `<HistoryOutlined />`
- Cluster: `<ClusterOutlined />`
- Data: `<DatabaseOutlined />`
- Plugin: `<AppstoreOutlined />`
- Security: `<SecurityScanOutlined />`
- System: `<SettingOutlined />`
- Template: `<FileTextOutlined />`
- Debug: `<BugOutlined />`
- Node: `<NodeIndexOutlined />`

## 🔧 处理步骤
1. 添加 PageLayout 导入
2. 添加对应图标导入
3. 替换页面结构为 PageLayout
4. 标准化样式类名
5. 调整表格滚动配置
6. 测试页面显示效果
