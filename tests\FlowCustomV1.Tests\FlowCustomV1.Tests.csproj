<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
    <GenerateDocumentationFile>false</GenerateDocumentationFile>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)'=='Debug'">
    <DefineConstants>DEBUG;TRACE</DefineConstants>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)'=='Release'">
    <DefineConstants>RELEASE;TRACE</DefineConstants>
    <Optimize>true</Optimize>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.8.0" />
    <PackageReference Include="xunit" Version="2.6.2" />
    <PackageReference Include="xunit.runner.visualstudio" Version="2.5.3">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="coverlet.collector" Version="6.0.0">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.8" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.8" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\FlowCustomV1.Core\FlowCustomV1.Core.csproj" />
    <ProjectReference Include="..\..\src\FlowCustomV1.Api\FlowCustomV1.Api.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Include="xunit.runner.json" CopyToOutputDirectory="PreserveNewest" />
    <None Include="appsettings.test.json" CopyToOutputDirectory="PreserveNewest" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="TestResults\" />
    <Folder Include="TestData\" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="TestData\**\*" CopyToOutputDirectory="PreserveNewest" />
  </ItemGroup>

  <!-- 测试运行时设置 -->
  <ItemGroup>
    <AssemblyAttribute Include="System.Reflection.AssemblyMetadata">
      <_Parameter1>TestFramework</_Parameter1>
      <_Parameter2>xUnit.net</_Parameter2>
    </AssemblyAttribute>
    <AssemblyAttribute Include="System.Reflection.AssemblyMetadata">
      <_Parameter1>TestVersion</_Parameter1>
      <_Parameter2>v0.0.1.7</_Parameter2>
    </AssemblyAttribute>
  </ItemGroup>

  <!-- 代码覆盖率设置 -->
  <PropertyGroup>
    <CollectCoverage>true</CollectCoverage>
    <CoverletOutputFormat>opencover,cobertura,json</CoverletOutputFormat>
    <CoverletOutput>TestResults/coverage</CoverletOutput>
    <Exclude>[*.Tests]*,[*]*.Program,[*]*.Startup</Exclude>
    <ExcludeByFile>**/Migrations/**/*.cs</ExcludeByFile>
    <Include>[FlowCustomV1.Core]*,[FlowCustomV1.Infrastructure]*,[FlowCustomV1.Api]*</Include>
  </PropertyGroup>

  <!-- 测试数据和配置文件 -->
  <ItemGroup>
    <EmbeddedResource Include="TestData\SampleWorkflows\*.json" />
    <EmbeddedResource Include="TestData\SampleTasks\*.json" />
    <EmbeddedResource Include="TestData\SampleNodes\*.json" />
  </ItemGroup>

  <!-- 条件编译符号 -->
  <PropertyGroup Condition="'$(BuildingInsideVisualStudio)' == 'true'">
    <DefineConstants>$(DefineConstants);VISUAL_STUDIO</DefineConstants>
  </PropertyGroup>

  <PropertyGroup Condition="'$(CI)' == 'true'">
    <DefineConstants>$(DefineConstants);CONTINUOUS_INTEGRATION</DefineConstants>
  </PropertyGroup>

  <!-- 测试超时设置 -->
  <PropertyGroup>
    <VSTestTimeout>300000</VSTestTimeout> <!-- 5分钟超时 -->
  </PropertyGroup>

  <!-- 并行测试设置 -->
  <PropertyGroup>
    <VSTestParallel>true</VSTestParallel>
    <VSTestMaxCpuCount>0</VSTestMaxCpuCount> <!-- 使用所有可用CPU -->
  </PropertyGroup>

  <!-- 测试结果输出 -->
  <PropertyGroup>
    <VSTestResultsDirectory>TestResults</VSTestResultsDirectory>
    <VSTestLogger>trx;LogFileName=TestResults.trx</VSTestLogger>
  </PropertyGroup>

  <!-- 性能测试特殊设置 -->
  <PropertyGroup Condition="'$(TestCategory)' == 'Performance'">
    <VSTestTimeout>600000</VSTestTimeout> <!-- 性能测试10分钟超时 -->
    <VSTestParallel>false</VSTestParallel> <!-- 性能测试不并行 -->
  </PropertyGroup>

  <!-- 弹性测试特殊设置 -->
  <PropertyGroup Condition="'$(TestCategory)' == 'Resilience'">
    <VSTestTimeout>900000</VSTestTimeout> <!-- 弹性测试15分钟超时 -->
  </PropertyGroup>

</Project>
