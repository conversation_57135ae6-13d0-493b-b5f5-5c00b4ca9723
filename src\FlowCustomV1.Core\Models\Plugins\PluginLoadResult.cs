using FlowCustomV1.Core.Interfaces;

namespace FlowCustomV1.Core.Models.Plugins;

/// <summary>
/// 插件加载结果
/// </summary>
public class PluginLoadResult
{
    /// <summary>
    /// 是否加载成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 节点类型
    /// </summary>
    public string NodeType { get; set; } = string.Empty;

    /// <summary>
    /// 加载的插件执行器
    /// </summary>
    public INodeExecutor? Executor { get; set; }

    /// <summary>
    /// 插件信息
    /// </summary>
    public PluginInfo? PluginInfo { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 异常信息
    /// </summary>
    public Exception? Exception { get; set; }

    /// <summary>
    /// 加载时间（毫秒）
    /// </summary>
    public long LoadTimeMs { get; set; }

    /// <summary>
    /// 加载开始时间
    /// </summary>
    public DateTime StartedAt { get; set; }

    /// <summary>
    /// 加载完成时间
    /// </summary>
    public DateTime CompletedAt { get; set; }

    /// <summary>
    /// 加载的依赖项
    /// </summary>
    public List<string> LoadedDependencies { get; set; } = new();

    /// <summary>
    /// 警告信息
    /// </summary>
    public List<string> Warnings { get; set; } = new();
}

/// <summary>
/// 插件卸载结果
/// </summary>
public class PluginUnloadResult
{
    /// <summary>
    /// 是否卸载成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 节点类型
    /// </summary>
    public string NodeType { get; set; } = string.Empty;

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 异常信息
    /// </summary>
    public Exception? Exception { get; set; }

    /// <summary>
    /// 卸载时间（毫秒）
    /// </summary>
    public long UnloadTimeMs { get; set; }

    /// <summary>
    /// 卸载开始时间
    /// </summary>
    public DateTime StartedAt { get; set; }

    /// <summary>
    /// 卸载完成时间
    /// </summary>
    public DateTime CompletedAt { get; set; }

    /// <summary>
    /// 清理的资源
    /// </summary>
    public List<string> CleanedResources { get; set; } = new();

    /// <summary>
    /// 警告信息
    /// </summary>
    public List<string> Warnings { get; set; } = new();
}

/// <summary>
/// 插件验证结果
/// </summary>
public class PluginValidationResult
{
    /// <summary>
    /// 是否验证通过
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 插件路径
    /// </summary>
    public string PluginPath { get; set; } = string.Empty;

    /// <summary>
    /// 验证错误
    /// </summary>
    public List<ValidationError> Errors { get; set; } = new();

    /// <summary>
    /// 验证警告
    /// </summary>
    public List<ValidationWarning> Warnings { get; set; } = new();

    /// <summary>
    /// 验证时间（毫秒）
    /// </summary>
    public long ValidationTimeMs { get; set; }

    /// <summary>
    /// 验证开始时间
    /// </summary>
    public DateTime StartedAt { get; set; }

    /// <summary>
    /// 验证完成时间
    /// </summary>
    public DateTime CompletedAt { get; set; }

    /// <summary>
    /// 检查的项目
    /// </summary>
    public List<string> CheckedItems { get; set; } = new();
}

/// <summary>
/// 验证错误
/// </summary>
public class ValidationError
{
    /// <summary>
    /// 错误代码
    /// </summary>
    public string Code { get; set; } = string.Empty;

    /// <summary>
    /// 错误消息
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 错误类型
    /// </summary>
    public ValidationErrorType Type { get; set; }

    /// <summary>
    /// 严重程度
    /// </summary>
    public ValidationSeverity Severity { get; set; }
}

/// <summary>
/// 验证警告
/// </summary>
public class ValidationWarning
{
    /// <summary>
    /// 警告代码
    /// </summary>
    public string Code { get; set; } = string.Empty;

    /// <summary>
    /// 警告消息
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 警告类型
    /// </summary>
    public ValidationWarningType Type { get; set; }
}

/// <summary>
/// 验证错误类型
/// </summary>
public enum ValidationErrorType
{
    /// <summary>
    /// 文件不存在
    /// </summary>
    FileNotFound,

    /// <summary>
    /// 格式错误
    /// </summary>
    InvalidFormat,

    /// <summary>
    /// 依赖缺失
    /// </summary>
    MissingDependency,

    /// <summary>
    /// 版本不兼容
    /// </summary>
    IncompatibleVersion,

    /// <summary>
    /// 安全检查失败
    /// </summary>
    SecurityCheckFailed,

    /// <summary>
    /// 签名验证失败
    /// </summary>
    SignatureVerificationFailed
}

/// <summary>
/// 验证警告类型
/// </summary>
public enum ValidationWarningType
{
    /// <summary>
    /// 版本过旧
    /// </summary>
    OutdatedVersion,

    /// <summary>
    /// 性能问题
    /// </summary>
    PerformanceIssue,

    /// <summary>
    /// 兼容性问题
    /// </summary>
    CompatibilityIssue,

    /// <summary>
    /// 配置建议
    /// </summary>
    ConfigurationSuggestion
}

/// <summary>
/// 验证严重程度
/// </summary>
public enum ValidationSeverity
{
    /// <summary>
    /// 低
    /// </summary>
    Low,

    /// <summary>
    /// 中
    /// </summary>
    Medium,

    /// <summary>
    /// 高
    /// </summary>
    High,

    /// <summary>
    /// 严重
    /// </summary>
    Critical
}
