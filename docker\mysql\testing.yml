services:
  mysql-test:
    image: mysql:8.0
    container_name: mysql-test
    hostname: mysql-test
    restart: unless-stopped
    ports:
      - "23306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_DATABASE: flowcustom_test
      MYSQL_USER: flowcustom
      MYSQL_PASSWORD: FlowCustom@2025
      MYSQL_CHARACTER_SET_SERVER: utf8mb4
      MYSQL_COLLATION_SERVER: utf8mb4_unicode_ci
    command: [
      "mysqld",
      "--lower-case-table-names=1",
      "--character-set-server=utf8mb4",
      "--collation-server=utf8mb4_unicode_ci",
      "--default-authentication-plugin=mysql_native_password"
    ]
    volumes:
      - mysql_test_data:/var/lib/mysql
      - ./init:/docker-entrypoint-initdb.d
    networks:
      - flowcustom-test
    healthcheck:
      test: ["<PERSON><PERSON>", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-proot_password"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

volumes:
  mysql_test_data:
    driver: local

networks:
  flowcustom-test:
    driver: bridge
