using FlowCustomV1.Core.Models.Cluster;
using FlowCustomV1.Infrastructure.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace FlowCustomV1.Examples;

/// <summary>
/// 节点发现功能演示程序
/// </summary>
public class Program
{
    public static async Task Main(string[] args)
    {
        Console.WriteLine("🚀 FlowCustomV1 v0.0.1.3 - 节点发现功能演示");
        Console.WriteLine("==========================================");
        Console.WriteLine();

        try
        {
            // 演示1：配置管理
            await DemoConfiguration();
            
            // 演示2：数据模型
            await DemoDataModels();
            
            // 演示3：集群拓扑
            await DemoClusterTopology();
            
            Console.WriteLine("✅ 演示完成！");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 演示失败: {ex.Message}");
        }

        Console.WriteLine("\n按任意键退出...");
        Console.ReadKey();
    }

    /// <summary>
    /// 演示配置管理
    /// </summary>
    private static async Task DemoConfiguration()
    {
        Console.WriteLine("📍 演示1: 节点发现配置管理");
        Console.WriteLine("---------------------------");

        // 创建默认配置
        var config = new NodeDiscoveryConfiguration();
        
        Console.WriteLine($"集群名称: {config.ClusterName}");
        Console.WriteLine($"心跳间隔: {config.HeartbeatIntervalSeconds}秒");
        Console.WriteLine($"节点超时: {config.NodeTimeoutSeconds}秒");
        Console.WriteLine($"清理间隔: {config.NodeCleanupIntervalSeconds}秒");
        Console.WriteLine($"发现超时: {config.DiscoveryTimeoutSeconds}秒");
        Console.WriteLine($"自动注册: {config.EnableAutoRegistration}");
        Console.WriteLine($"启用心跳: {config.EnableHeartbeat}");
        Console.WriteLine($"启用清理: {config.EnableNodeCleanup}");
        Console.WriteLine($"配置有效: {config.IsValid()}");
        
        Console.WriteLine($"\n配置摘要: {config}");
        Console.WriteLine();
        
        await Task.Delay(1000);
    }

    /// <summary>
    /// 演示数据模型
    /// </summary>
    private static async Task DemoDataModels()
    {
        Console.WriteLine("📍 演示2: 节点信息数据模型");
        Console.WriteLine("-------------------------");

        // 创建节点信息
        var nodeInfo = CreateSampleNodeInfo();
        
        Console.WriteLine($"节点ID: {nodeInfo.NodeId}");
        Console.WriteLine($"节点名称: {nodeInfo.NodeName}");
        Console.WriteLine($"集群名称: {nodeInfo.ClusterName}");
        Console.WriteLine($"节点模式: {nodeInfo.Mode} ({nodeInfo.Mode.GetDescription()})");
        Console.WriteLine($"节点状态: {nodeInfo.Status}");
        Console.WriteLine($"是否有效: {nodeInfo.IsValid()}");
        
        Console.WriteLine("\n网络信息:");
        Console.WriteLine($"  IP地址: {nodeInfo.Network.IpAddress}");
        Console.WriteLine($"  HTTP端口: {nodeInfo.Network.HttpPort}");
        Console.WriteLine($"  NATS端口: {nodeInfo.Network.NatsPort}");
        Console.WriteLine($"  管理端口: {nodeInfo.Network.ManagementPort}");
        Console.WriteLine($"  主机名: {nodeInfo.Network.HostName}");
        
        Console.WriteLine("\n节点能力:");
        Console.WriteLine($"  CPU核心: {nodeInfo.Capabilities.CpuCores}");
        Console.WriteLine($"  内存: {nodeInfo.Capabilities.MemoryMb} MB");
        Console.WriteLine($"  最大并发: {nodeInfo.Capabilities.MaxConcurrentExecutions}");
        Console.WriteLine($"  性能等级: {nodeInfo.Capabilities.PerformanceLevel}");
        Console.WriteLine($"  标签: {string.Join(", ", nodeInfo.Capabilities.Tags)}");
        
        Console.WriteLine("\n负载信息:");
        Console.WriteLine($"  CPU使用率: {nodeInfo.Load.CpuUsagePercentage:F1}%");
        Console.WriteLine($"  内存使用率: {nodeInfo.Load.MemoryUsagePercentage:F1}%");
        Console.WriteLine($"  活跃任务: {nodeInfo.Load.ActiveTaskCount}");
        Console.WriteLine($"  队列任务: {nodeInfo.Load.QueuedTaskCount}");
        Console.WriteLine($"  最大容量: {nodeInfo.Load.MaxTaskCapacity}");
        Console.WriteLine($"  负载评分: {nodeInfo.Load.CalculateLoadScore():F1}");
        
        Console.WriteLine("\n健康状态:");
        Console.WriteLine($"  是否健康: {nodeInfo.Health.IsHealthy}");
        Console.WriteLine($"  健康评分: {nodeInfo.Health.HealthScore:F1}");
        
        Console.WriteLine($"\n节点摘要: {nodeInfo}");
        Console.WriteLine();
        
        await Task.Delay(1000);
    }

    /// <summary>
    /// 演示集群拓扑
    /// </summary>
    private static async Task DemoClusterTopology()
    {
        Console.WriteLine("📍 演示3: 集群拓扑管理");
        Console.WriteLine("---------------------");

        // 创建集群拓扑
        var topology = CreateSampleClusterTopology();
        
        Console.WriteLine($"集群名称: {topology.ClusterName}");
        Console.WriteLine($"总节点数: {topology.Nodes.Count}");
        Console.WriteLine($"在线节点: {topology.OnlineNodeCount}");
        Console.WriteLine($"离线节点: {topology.OfflineNodeCount}");
        Console.WriteLine($"活跃连接: {topology.ActiveConnectionCount}");
        Console.WriteLine($"拓扑版本: {topology.TopologyVersion}");
        Console.WriteLine($"最后更新: {topology.LastUpdatedAt:yyyy-MM-dd HH:mm:ss}");
        
        Console.WriteLine("\n按角色分组:");
        foreach (var kvp in topology.NodesByRole)
        {
            Console.WriteLine($"  {kvp.Key}: {kvp.Value}个节点");
        }
        
        Console.WriteLine("\n按状态分组:");
        foreach (var kvp in topology.NodesByStatus)
        {
            Console.WriteLine($"  {kvp.Key}: {kvp.Value}个节点");
        }
        
        Console.WriteLine("\n节点详情:");
        foreach (var node in topology.Nodes)
        {
            Console.WriteLine($"  📍 {node.NodeId} ({node.NodeName})");
            Console.WriteLine($"     模式: {node.Mode}, 状态: {node.Status}");
            Console.WriteLine($"     负载: {node.Load.CalculateLoadScore():F1}, 健康: {node.Health.HealthScore:F1}");
        }
        
        Console.WriteLine("\n连接信息:");
        foreach (var connection in topology.NodeConnections)
        {
            var status = connection.IsActive ? "✅" : "❌";
            Console.WriteLine($"  {status} {connection.SourceNodeId} → {connection.TargetNodeId} ({connection.ConnectionType})");
            Console.WriteLine($"     质量: {connection.QualityScore:F1}, 延迟: {connection.LatencyMs:F1}ms");
        }
        
        // 演示集群概览
        var overview = CreateClusterOverview(topology);
        Console.WriteLine("\n集群概览:");
        Console.WriteLine($"  总CPU核心: {overview.TotalCpuCores}");
        Console.WriteLine($"  已用CPU: {overview.UsedCpuCores} ({overview.CpuUsagePercentage:F1}%)");
        Console.WriteLine($"  总内存: {overview.TotalMemoryMB} MB");
        Console.WriteLine($"  已用内存: {overview.UsedMemoryMB} MB ({overview.MemoryUsagePercentage:F1}%)");
        Console.WriteLine($"  活跃任务: {overview.ActiveTasks}");
        Console.WriteLine($"  队列任务: {overview.QueuedTasks}");
        Console.WriteLine($"  平均负载: {overview.AverageLoadScore:F1}");
        Console.WriteLine($"  健康评分: {overview.ClusterHealthScore:F1}");
        Console.WriteLine($"  集群健康: {(overview.IsHealthy ? "✅ 健康" : "❌ 不健康")}");
        
        Console.WriteLine();
        await Task.Delay(1000);
    }

    /// <summary>
    /// 创建示例节点信息
    /// </summary>
    private static NodeInfo CreateSampleNodeInfo()
    {
        return new NodeInfo
        {
            NodeId = "demo-node-001",
            NodeName = "Demo Worker Node",
            ClusterName = "FlowCustomV1-Demo",
            Mode = NodeMode.Worker,
            Status = NodeStatus.Healthy,
            Network = new NetworkInfo
            {
                IpAddress = "*************",
                HttpPort = 5000,
                NatsPort = 4222,
                ManagementPort = 8080,
                HostName = "demo-worker-01"
            },
            Capabilities = new NodeCapabilities
            {
                CpuCores = 8,
                MemoryMb = 16384,
                MaxConcurrentExecutions = 20,
                PerformanceLevel = 8,
                Tags = new HashSet<string> { "demo", "worker", "high-performance" }
            },
            Load = new NodeLoad
            {
                CpuUsagePercentage = 45.5,
                MemoryUsagePercentage = 62.3,
                ActiveTaskCount = 8,
                QueuedTaskCount = 2,
                MaxTaskCapacity = 20
            },
            Health = new HealthStatus
            {
                IsHealthy = true,
                HealthScore = 92.5
            },
            Timestamps = new Timestamps
            {
                CreatedAt = DateTime.UtcNow.AddHours(-2),
                LastActiveAt = DateTime.UtcNow
            }
        };
    }

    /// <summary>
    /// 创建示例集群拓扑
    /// </summary>
    private static ClusterTopology CreateSampleClusterTopology()
    {
        var nodes = new List<NodeInfo>
        {
            new NodeInfo
            {
                NodeId = "master-node-001",
                NodeName = "Master Node 1",
                Mode = NodeMode.Master,
                Status = NodeStatus.Healthy,
                Network = new NetworkInfo { IpAddress = "************", HttpPort = 5000 },
                Capabilities = new NodeCapabilities { CpuCores = 4, MemoryMb = 8192 },
                Load = new NodeLoad { CpuUsagePercentage = 25.0, MemoryUsagePercentage = 35.0 },
                Health = new HealthStatus { IsHealthy = true, HealthScore = 95.0 },
                Timestamps = new Timestamps { CreatedAt = DateTime.UtcNow.AddHours(-1), LastActiveAt = DateTime.UtcNow }
            },
            new NodeInfo
            {
                NodeId = "worker-node-001",
                NodeName = "Worker Node 1",
                Mode = NodeMode.Worker,
                Status = NodeStatus.Healthy,
                Network = new NetworkInfo { IpAddress = "************", HttpPort = 5001 },
                Capabilities = new NodeCapabilities { CpuCores = 8, MemoryMb = 16384 },
                Load = new NodeLoad { CpuUsagePercentage = 65.0, MemoryUsagePercentage = 70.0 },
                Health = new HealthStatus { IsHealthy = true, HealthScore = 88.0 },
                Timestamps = new Timestamps { CreatedAt = DateTime.UtcNow.AddMinutes(-30), LastActiveAt = DateTime.UtcNow }
            },
            new NodeInfo
            {
                NodeId = "worker-node-002",
                NodeName = "Worker Node 2",
                Mode = NodeMode.Worker,
                Status = NodeStatus.Degraded,
                Network = new NetworkInfo { IpAddress = "************", HttpPort = 5002 },
                Capabilities = new NodeCapabilities { CpuCores = 8, MemoryMb = 16384 },
                Load = new NodeLoad { CpuUsagePercentage = 85.0, MemoryUsagePercentage = 90.0 },
                Health = new HealthStatus { IsHealthy = false, HealthScore = 65.0 },
                Timestamps = new Timestamps { CreatedAt = DateTime.UtcNow.AddMinutes(-45), LastActiveAt = DateTime.UtcNow.AddMinutes(-2) }
            }
        };

        var connections = new List<NodeConnection>
        {
            new NodeConnection
            {
                SourceNodeId = "master-node-001",
                TargetNodeId = "nats-cluster",
                ConnectionType = "NATS",
                IsActive = true,
                QualityScore = 98.5,
                LatencyMs = 2.3
            },
            new NodeConnection
            {
                SourceNodeId = "worker-node-001",
                TargetNodeId = "nats-cluster",
                ConnectionType = "NATS",
                IsActive = true,
                QualityScore = 95.2,
                LatencyMs = 3.1
            },
            new NodeConnection
            {
                SourceNodeId = "worker-node-002",
                TargetNodeId = "nats-cluster",
                ConnectionType = "NATS",
                IsActive = false,
                QualityScore = 45.0,
                LatencyMs = 15.7
            }
        };

        return new ClusterTopology
        {
            ClusterName = "FlowCustomV1-Demo",
            Nodes = nodes,
            NodeConnections = connections,
            TopologyVersion = "ABC123",
            LastUpdatedAt = DateTime.UtcNow
        };
    }

    /// <summary>
    /// 创建集群概览
    /// </summary>
    private static ClusterOverview CreateClusterOverview(ClusterTopology topology)
    {
        var totalCpu = topology.Nodes.Sum(n => n.Capabilities.CpuCores);
        var totalMemory = topology.Nodes.Sum(n => n.Capabilities.MemoryMb);
        var usedCpu = topology.Nodes.Sum(n => (int)(n.Capabilities.CpuCores * n.Load.CpuUsagePercentage / 100));
        var usedMemory = topology.Nodes.Sum(n => (long)(n.Capabilities.MemoryMb * n.Load.MemoryUsagePercentage / 100));
        var activeTasks = topology.Nodes.Sum(n => n.Load.ActiveTaskCount);
        var queuedTasks = topology.Nodes.Sum(n => n.Load.QueuedTaskCount);
        var avgLoad = topology.Nodes.Average(n => n.Load.CalculateLoadScore());
        var healthScore = topology.Nodes.Average(n => n.Health.HealthScore);

        return new ClusterOverview
        {
            ClusterName = topology.ClusterName,
            TotalNodes = topology.Nodes.Count,
            OnlineNodes = topology.OnlineNodeCount,
            OfflineNodes = topology.OfflineNodeCount,
            TotalCpuCores = totalCpu,
            UsedCpuCores = usedCpu,
            TotalMemoryMB = totalMemory,
            UsedMemoryMB = usedMemory,
            ActiveTasks = activeTasks,
            QueuedTasks = queuedTasks,
            AverageLoadScore = avgLoad,
            ClusterHealthScore = healthScore,
            LastUpdatedAt = DateTime.UtcNow
        };
    }
}
