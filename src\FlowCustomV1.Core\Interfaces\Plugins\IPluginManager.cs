using FlowCustomV1.Core.Models.Plugins;

namespace FlowCustomV1.Core.Interfaces.Plugins;

/// <summary>
/// 插件管理器接口
/// 统一管理三种插件类型：内置插件、JSON配置插件、DLL预编译插件
/// </summary>
public interface IPluginManager
{
    /// <summary>
    /// 初始化插件管理器
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>初始化任务</returns>
    Task InitializeAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 加载所有插件
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>加载任务</returns>
    Task LoadAllPluginsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 根据节点类型获取插件执行器
    /// </summary>
    /// <param name="nodeType">节点类型</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>插件执行器</returns>
    Task<INodeExecutor?> GetPluginExecutorAsync(string nodeType, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取所有可用的插件信息
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>插件信息列表</returns>
    Task<IReadOnlyList<PluginInfo>> GetAvailablePluginsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 注册内置插件
    /// </summary>
    /// <param name="pluginInfo">插件信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>注册任务</returns>
    Task RegisterBuiltinPluginAsync(PluginInfo pluginInfo, CancellationToken cancellationToken = default);

    /// <summary>
    /// 加载JSON配置插件
    /// </summary>
    /// <param name="configPath">配置文件路径</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>加载任务</returns>
    Task LoadJsonPluginAsync(string configPath, CancellationToken cancellationToken = default);

    /// <summary>
    /// 加载DLL插件
    /// </summary>
    /// <param name="dllPath">DLL文件路径</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>加载任务</returns>
    Task LoadDllPluginAsync(string dllPath, CancellationToken cancellationToken = default);

    /// <summary>
    /// 卸载插件
    /// </summary>
    /// <param name="nodeType">节点类型</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>卸载任务</returns>
    Task UnloadPluginAsync(string nodeType, CancellationToken cancellationToken = default);

    /// <summary>
    /// 重新加载插件
    /// </summary>
    /// <param name="nodeType">节点类型</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>重新加载任务</returns>
    Task ReloadPluginAsync(string nodeType, CancellationToken cancellationToken = default);

    /// <summary>
    /// 检查插件是否已加载
    /// </summary>
    /// <param name="nodeType">节点类型</param>
    /// <returns>是否已加载</returns>
    bool IsPluginLoaded(string nodeType);

    /// <summary>
    /// 获取插件统计信息
    /// </summary>
    /// <returns>插件统计信息</returns>
    PluginStatistics GetPluginStatistics();

    /// <summary>
    /// 插件加载事件
    /// </summary>
    event EventHandler<PluginLoadedEventArgs> PluginLoaded;

    /// <summary>
    /// 插件卸载事件
    /// </summary>
    event EventHandler<PluginUnloadedEventArgs> PluginUnloaded;

    /// <summary>
    /// 插件错误事件
    /// </summary>
    event EventHandler<PluginErrorEventArgs> PluginError;
}
