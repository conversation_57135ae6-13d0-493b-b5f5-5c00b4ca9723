# FlowCustomV1 v0.0.1.7 快速测试脚本
# 快速启动和验证Docker测试环境

param(
    [Parameter(Mandatory=$false)]
    [switch]$SkipBuild,

    [Parameter(Mandatory=$false)]
    [switch]$QuickTest,

    [Parameter(Mandatory=$false)]
    [switch]$ShowLogs
)

$ErrorActionPreference = "Stop"
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path

Write-Host "🚀 FlowCustomV1 v0.0.1.7 快速测试启动" -ForegroundColor Green
Write-Host "=================================" -ForegroundColor Green

# 检查Docker是否运行
try {
    docker version | Out-Null
    Write-Host "✅ Docker 运行正常" -ForegroundColor Green
}
catch {
    Write-Host "❌ Docker 未运行或未安装" -ForegroundColor Red
    exit 1
}

# 检查Docker Compose
try {
    docker-compose version | Out-Null
    Write-Host "✅ Docker Compose 可用" -ForegroundColor Green
}
catch {
    Write-Host "❌ Docker Compose 未安装" -ForegroundColor Red
    exit 1
}

# 进入Docker目录
Push-Location (Join-Path $ScriptDir "docker")

try {
    # 清理旧环境
    Write-Host "🧹 清理旧环境..." -ForegroundColor Yellow
    docker-compose -f docker-compose.full-test.yml down -v 2>$null

    # 构建镜像（如果需要）
    if (-not $SkipBuild) {
        Write-Host "🔨 构建测试镜像..." -ForegroundColor Yellow
        docker-compose -f docker-compose.full-test.yml build
    }

    # 启动基础设施
    Write-Host "🏗️ 启动基础设施..." -ForegroundColor Yellow
    docker-compose -f docker-compose.full-test.yml up -d nats-1 nats-2 nats-3 mysql-full-test

    # 等待基础设施就绪
    Write-Host "⏳ 等待基础设施就绪..." -ForegroundColor Yellow
    Start-Sleep -Seconds 30

    # 启动应用节点
    Write-Host "🚀 启动应用节点..." -ForegroundColor Yellow
    docker-compose -f docker-compose.full-test.yml up -d `
        master-node-beijing master-node-shanghai `
        worker-node-beijing-1 worker-node-beijing-2 worker-node-shanghai-1 `
        designer-node-beijing designer-node-shanghai `
        validator-node-beijing validator-node-shanghai `
        executor-node-beijing executor-node-shanghai

    # 等待应用节点就绪
    Write-Host "⏳ 等待应用节点就绪..." -ForegroundColor Yellow
    Start-Sleep -Seconds 60

    # 验证服务状态
    Write-Host "🔍 验证服务状态..." -ForegroundColor Yellow
    
    $services = @(
        @{ Name = "NATS-1"; Url = "http://localhost:28222/healthz" },
        @{ Name = "NATS-2"; Url = "http://localhost:28223/healthz" },
        @{ Name = "NATS-3"; Url = "http://localhost:28224/healthz" },
        @{ Name = "Master-Beijing"; Url = "http://localhost:25001/health" },
        @{ Name = "Master-Shanghai"; Url = "http://localhost:25002/health" },
        @{ Name = "Worker-Beijing-1"; Url = "http://localhost:25011/health" },
        @{ Name = "Worker-Beijing-2"; Url = "http://localhost:25012/health" },
        @{ Name = "Worker-Shanghai-1"; Url = "http://localhost:25013/health" },
        @{ Name = "Designer-Beijing"; Url = "http://localhost:25021/health" },
        @{ Name = "Designer-Shanghai"; Url = "http://localhost:25022/health" },
        @{ Name = "Validator-Beijing"; Url = "http://localhost:25031/health" },
        @{ Name = "Validator-Shanghai"; Url = "http://localhost:25032/health" },
        @{ Name = "Executor-Beijing"; Url = "http://localhost:25041/health" },
        @{ Name = "Executor-Shanghai"; Url = "http://localhost:25042/health" }
    )

    $healthyServices = 0
    $totalServices = $services.Count

    foreach ($service in $services) {
        try {
            $response = Invoke-WebRequest -Uri $service.Url -TimeoutSec 5 -UseBasicParsing
            if ($response.StatusCode -eq 200) {
                Write-Host "  ✅ $($service.Name)" -ForegroundColor Green
                $healthyServices++
            } else {
                Write-Host "  ❌ $($service.Name) (HTTP $($response.StatusCode))" -ForegroundColor Red
            }
        } catch {
            Write-Host "  ❌ $($service.Name) (连接失败)" -ForegroundColor Red
        }
    }

    Write-Host ""
    Write-Host "📊 服务状态总结: $healthyServices/$totalServices 健康" -ForegroundColor $(if ($healthyServices -eq $totalServices) { "Green" } else { "Yellow" })

    if ($healthyServices -eq $totalServices) {
        Write-Host "🎉 所有服务启动成功！" -ForegroundColor Green
        
        # 显示访问信息
        Write-Host ""
        Write-Host "🌐 访问地址:" -ForegroundColor Cyan
        Write-Host "  NATS监控: http://localhost:28222/varz" -ForegroundColor White
        Write-Host "  Master北京: http://localhost:25001/health" -ForegroundColor White
        Write-Host "  Master上海: http://localhost:25002/health" -ForegroundColor White
        Write-Host "  Worker节点: http://localhost:25011/health (北京1)" -ForegroundColor White
        Write-Host "  Designer节点: http://localhost:25021/health (北京)" -ForegroundColor White
        Write-Host "  Validator节点: http://localhost:25031/health (北京)" -ForegroundColor White
        Write-Host "  Executor节点: http://localhost:25041/health (北京)" -ForegroundColor White

        # 快速测试
        if ($QuickTest) {
            Write-Host ""
            Write-Host "🧪 执行快速测试..." -ForegroundColor Yellow
            
            # 测试NATS连接
            try {
                $natsResponse = Invoke-WebRequest -Uri "http://localhost:28222/varz" -UseBasicParsing
                $natsData = $natsResponse.Content | ConvertFrom-Json
                Write-Host "  ✅ NATS集群连接数: $($natsData.connections)" -ForegroundColor Green
            } catch {
                Write-Host "  ❌ NATS集群测试失败" -ForegroundColor Red
            }

            # 测试Master节点API
            try {
                $masterResponse = Invoke-WebRequest -Uri "http://localhost:25001/api/cluster/nodes" -UseBasicParsing
                Write-Host "  ✅ Master节点API响应正常" -ForegroundColor Green
            } catch {
                Write-Host "  ❌ Master节点API测试失败" -ForegroundColor Red
            }
        }

        Write-Host ""
        Write-Host "🎯 下一步操作:" -ForegroundColor Cyan
        Write-Host "  1. 运行完整测试: .\run-full-docker-tests.ps1" -ForegroundColor White
        Write-Host "  2. 查看服务日志: docker-compose -f docker-compose.full-test.yml logs [service-name]" -ForegroundColor White
        Write-Host "  3. 停止环境: docker-compose -f docker-compose.full-test.yml down" -ForegroundColor White

    } else {
        Write-Host "⚠️ 部分服务启动失败，请检查日志" -ForegroundColor Yellow
        
        if ($ShowLogs) {
            Write-Host ""
            Write-Host "📋 显示服务日志..." -ForegroundColor Yellow
            docker-compose -f docker-compose.full-test.yml logs --tail=50
        }
    }

} catch {
    Write-Host "❌ 测试环境启动失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
} finally {
    Pop-Location
}

Write-Host ""
Write-Host "✨ 快速测试完成！" -ForegroundColor Green
