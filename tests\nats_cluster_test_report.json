{"timestamp": "2025-09-04T22:23:15.904945", "summary": {"total": 5, "passed": 5, "failed": 0, "success_rate": 100.0}, "results": [{"test_name": "基础连接测试", "success": true, "duration": 0.5109755992889404, "message": "连接和消息传递正常", "details": {"received_messages": 1}}, {"test_name": "JetStream功能测试", "success": true, "duration": 0.019419431686401367, "message": "JetStream消息存储和检索正常", "details": {"stream_name": "FLOWCUSTOM_TEST", "ack_sequence": 2, "received_workflow_id": "test-workflow-001"}}, {"test_name": "集群监控测试", "success": true, "duration": 0.08961105346679688, "message": "集群监控正常，3/3 节点健康", "details": {"nats-server-1": {"server_name": "nats-server-1", "version": "2.11.8", "connections": 0, "in_msgs": 950, "out_msgs": 952}, "nats-server-2": {"server_name": "nats-server-2", "version": "2.11.8", "connections": 0, "in_msgs": 566, "out_msgs": 568}, "nats-server-3": {"server_name": "nats-server-3", "version": "2.11.8", "connections": 0, "in_msgs": 555, "out_msgs": 541}}}, {"test_name": "故障转移测试", "success": true, "duration": 1.0919244289398193, "message": "连接保持稳定，故障转移机制就绪", "details": {"connection_events": ["disconnected"]}}, {"test_name": "性能基准测试", "success": true, "duration": 0.009789466857910156, "message": "性能达到基准要求", "details": {"messages_per_second": 312657.77, "throughput_mbps": 305.33, "total_messages": 1000, "message_size_bytes": 1024}}]}