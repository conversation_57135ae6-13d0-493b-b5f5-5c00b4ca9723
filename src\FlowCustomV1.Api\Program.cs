using FlowCustomV1.Api.Services;
using FlowCustomV1.Infrastructure;
using System.Text.Json.Serialization;

// 解析命令行参数获取环境配置
var environment = GetEnvironmentFromArgs(args);
Environment.SetEnvironmentVariable("ASPNETCORE_ENVIRONMENT", environment);

Console.WriteLine($"🚀 启动FlowCustomV1 API - 环境: {environment}");

try
{
    var builder = WebApplication.CreateBuilder(args);

    // 配置多环境配置文件加载
    ConfigureEnvironmentSettings(builder, environment);

    // TODO: 稍后集成 Furion 框架
    // builder.Inject();

    // Add services to the container.
    builder.Services.AddControllers()
        .AddJsonOptions(options =>
        {
            options.JsonSerializerOptions.Converters.Add(new JsonStringEnumConverter());
        });
    // Learn more about configuring OpenAPI at https://aka.ms/aspnet/openapi
    builder.Services.AddEndpointsApiExplorer();
    builder.Services.AddSwaggerGen();

    // 统一的服务注册 - 只通过Infrastructure层注册所有服务
    // Infrastructure层会自动注册Engine层和Core层的所有服务
    builder.Services.AddInfrastructure(builder.Configuration);

    // 添加后台服务
    builder.Services.AddHostedService<WorkflowEngineHostedService>();
    builder.Services.AddHostedService<NodeDiscoveryHostedService>();
    builder.Services.AddHostedService<ExecutorHostedService>();

    var app = builder.Build();

    // Initialize database
    try
    {
        var success = await app.Services.InitializeDatabaseAsync();
        if (!success)
        {
            Console.WriteLine("数据库初始化失败，但应用程序将继续运行");
        }
        else
        {
            Console.WriteLine("数据库初始化成功");
        }
    }
    catch (Exception ex)
    {
        Console.WriteLine($"数据库初始化过程中发生错误: {ex.Message}");
    }

    // Configure the HTTP request pipeline.
    if (app.Environment.IsDevelopment())
    {
        app.UseSwagger();
        app.UseSwaggerUI();
    }

    // 移除 HTTPS 重定向以避免在没有配置 HTTPS 的情况下卡死
    // app.UseHttpsRedirection();
    app.UseAuthorization();
    app.MapControllers();



    app.Run();
}
catch (Exception ex)
{
    Console.WriteLine($"应用程序启动时发生错误: {ex}");
    throw;
}

/// <summary>
/// 从命令行参数解析环境配置
/// </summary>
/// <param name="args">命令行参数</param>
/// <returns>环境名称</returns>
static string GetEnvironmentFromArgs(string[] args)
{
    // 检查命令行参数
    for (int i = 0; i < args.Length; i++)
    {
        if (args[i].Equals("--environment", StringComparison.OrdinalIgnoreCase) ||
            args[i].Equals("--env", StringComparison.OrdinalIgnoreCase) ||
            args[i].Equals("-e", StringComparison.OrdinalIgnoreCase))
        {
            if (i + 1 < args.Length)
            {
                var env = args[i + 1].ToLowerInvariant();
                return env switch
                {
                    "prod" or "production" => "Production",
                    "dev" or "development" => "Development",
                    "test" or "testing" => "Testing",
                    _ => "Development" // 默认开发环境
                };
            }
        }
    }

    // 检查环境变量
    var envVar = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
    if (!string.IsNullOrEmpty(envVar))
    {
        return envVar;
    }

    // 默认返回开发环境
    return "Development";
}

/// <summary>
/// 配置环境特定的设置
/// </summary>
/// <param name="builder">WebApplication构建器</param>
/// <param name="environment">环境名称</param>
static void ConfigureEnvironmentSettings(WebApplicationBuilder builder, string environment)
{
    // 清除默认配置提供程序
    builder.Configuration.Sources.Clear();

    // 添加基础配置文件
    builder.Configuration.AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);

    // 根据环境添加特定配置文件
    var environmentConfigFile = environment.ToLowerInvariant() switch
    {
        "production" => "appsettings.Production.json",
        "development" => "appsettings.Development.json",
        "testing" => "appsettings.Testing.json",
        _ => "appsettings.Development.json"
    };

    builder.Configuration.AddJsonFile(environmentConfigFile, optional: false, reloadOnChange: true);

    // 添加环境变量
    builder.Configuration.AddEnvironmentVariables();

    // 添加命令行参数
    if (builder.Environment.IsDevelopment())
    {
        builder.Configuration.AddCommandLine(Environment.GetCommandLineArgs());
    }

    Console.WriteLine($"📁 加载配置文件: {environmentConfigFile}");
}

/// <summary>
/// Public partial Program class to make the auto-generated Program class accessible for integration tests.
/// </summary>
public partial class Program { }