using System.ComponentModel.DataAnnotations;
using FlowCustomV1.Core.Models.Cluster;

namespace FlowCustomV1.Infrastructure.Configuration;

/// <summary>
/// 节点发现服务配置
/// </summary>
public class NodeDiscoveryConfiguration
{
    /// <summary>
    /// 配置节名称
    /// </summary>
    public const string SectionName = "NodeDiscovery";

    /// <summary>
    /// 集群名称
    /// </summary>
    [Required]
    public string ClusterName { get; set; } = "FlowCustomV1";

    /// <summary>
    /// 心跳间隔时间（秒）
    /// </summary>
    [Range(5, 300)]
    public int HeartbeatIntervalSeconds { get; set; } = 30;

    /// <summary>
    /// 节点超时时间（秒）
    /// </summary>
    [Range(30, 1800)]
    public int NodeTimeoutSeconds { get; set; } = 120;

    /// <summary>
    /// 节点清理间隔时间（秒）
    /// </summary>
    [Range(30, 600)]
    public int NodeCleanupIntervalSeconds { get; set; } = 60;

    /// <summary>
    /// 服务发现超时时间（秒）
    /// </summary>
    [Range(1, 30)]
    public int DiscoveryTimeoutSeconds { get; set; } = 5;

    /// <summary>
    /// 是否启用自动节点注册
    /// </summary>
    public bool EnableAutoRegistration { get; set; } = true;

    /// <summary>
    /// 是否启用心跳机制
    /// </summary>
    public bool EnableHeartbeat { get; set; } = true;

    /// <summary>
    /// 是否启用节点清理
    /// </summary>
    public bool EnableNodeCleanup { get; set; } = true;

    /// <summary>
    /// 最大重试次数
    /// </summary>
    [Range(0, 10)]
    public int MaxRetryAttempts { get; set; } = 3;

    /// <summary>
    /// 重试间隔时间（秒）
    /// </summary>
    [Range(1, 60)]
    public int RetryIntervalSeconds { get; set; } = 5;

    /// <summary>
    /// 节点角色（可选，用于角色特化）
    /// 保持向后兼容性，同时支持新的角色配置
    /// </summary>
    public string? NodeRole { get; set; }

    /// <summary>
    /// 节点功能角色列表（新的角色化配置）
    /// 支持多角色配置，如 ["Designer", "Validator"]
    /// </summary>
    public List<string> NodeRoles { get; set; } = new();

    /// <summary>
    /// 集群架构模式
    /// 定义集群使用的架构模式：MasterWorker、RoleBased、Hybrid、Adaptive
    /// </summary>
    public string ArchitectureMode { get; set; } = "RoleBased";

    /// <summary>
    /// 是否启用传统模式兼容
    /// 当启用时，节点会同时支持Master-Worker模式
    /// </summary>
    public bool EnableLegacyMode { get; set; } = false;

    /// <summary>
    /// 是否支持动态角色切换
    /// 当启用时，节点可以在运行时动态调整角色
    /// </summary>
    public bool EnableDynamicRoleSwitching { get; set; } = true;

    /// <summary>
    /// 节点标签（用于分类和过滤）
    /// </summary>
    public List<string> NodeTags { get; set; } = new();

    /// <summary>
    /// 节点能力标签（用于任务匹配）
    /// </summary>
    public List<string> CapabilityTags { get; set; } = new();

    /// <summary>
    /// 是否启用详细日志
    /// </summary>
    public bool EnableVerboseLogging { get; set; } = false;

    /// <summary>
    /// 获取节点角色配置
    /// 将配置转换为NodeRoleConfiguration对象
    /// </summary>
    /// <returns>节点角色配置</returns>
    public NodeRoleConfiguration GetNodeRoleConfiguration()
    {
        var config = new NodeRoleConfiguration
        {
            EnableLegacyMode = EnableLegacyMode,
            EnableDynamicRoleSwitching = EnableDynamicRoleSwitching
        };

        // 解析架构模式
        if (Enum.TryParse<ClusterArchitectureMode>(ArchitectureMode, true, out var mode))
        {
            config.ArchitectureMode = mode;
        }

        // 解析节点角色
        var roles = Core.Models.Cluster.NodeRole.None;

        // 优先使用新的NodeRoles配置
        if (NodeRoles.Any())
        {
            roles = NodeRoleExtensions.FromStringList(NodeRoles);
        }
        // 如果没有新配置，尝试解析旧的NodeRole配置
        else if (!string.IsNullOrWhiteSpace(NodeRole))
        {
            // 将传统角色映射到新角色
            roles = MapLegacyRoleToNewRoles(NodeRole);
        }

        // 如果都没有配置，使用默认角色
        if (roles == Core.Models.Cluster.NodeRole.None)
        {
            roles = Core.Models.Cluster.NodeRole.All; // 默认支持所有角色
        }

        config.Roles = roles;
        return config;
    }

    /// <summary>
    /// 将传统角色映射到新的角色系统
    /// </summary>
    /// <param name="legacyRole">传统角色字符串</param>
    /// <returns>新的节点角色</returns>
    private static Core.Models.Cluster.NodeRole MapLegacyRoleToNewRoles(string legacyRole)
    {
        return legacyRole.ToLowerInvariant() switch
        {
            "master" => Core.Models.Cluster.NodeRole.Scheduler | Core.Models.Cluster.NodeRole.Gateway | Core.Models.Cluster.NodeRole.Monitor,
            "worker" => Core.Models.Cluster.NodeRole.Executor,
            "api" => Core.Models.Cluster.NodeRole.Gateway | Core.Models.Cluster.NodeRole.Executor,
            "designer" => Core.Models.Cluster.NodeRole.Designer,
            "validator" => Core.Models.Cluster.NodeRole.Validator,
            "executor" => Core.Models.Cluster.NodeRole.Executor,
            "monitor" => Core.Models.Cluster.NodeRole.Monitor,
            "storage" => Core.Models.Cluster.NodeRole.Storage,
            "all" => Core.Models.Cluster.NodeRole.All,
            _ => Core.Models.Cluster.NodeRole.All // 未知角色默认为全功能
        };
    }

    /// <summary>
    /// 验证配置的有效性
    /// </summary>
    /// <returns>验证结果和错误信息</returns>
    public (bool IsValid, List<string> Errors) ValidateConfiguration()
    {
        var errors = new List<string>();

        // 验证基本配置
        if (HeartbeatIntervalSeconds <= 0)
        {
            errors.Add("心跳间隔必须大于0");
        }

        if (NodeTimeoutSeconds <= HeartbeatIntervalSeconds)
        {
            errors.Add("节点超时时间必须大于心跳间隔");
        }

        if (string.IsNullOrWhiteSpace(ClusterName))
        {
            errors.Add("集群名称不能为空");
        }

        // 验证架构模式
        if (!Enum.TryParse<ClusterArchitectureMode>(ArchitectureMode, true, out _))
        {
            errors.Add($"无效的架构模式: {ArchitectureMode}");
        }

        // 验证角色配置
        var roleConfig = GetNodeRoleConfiguration();
        var roleValidation = roleConfig.Validate();
        if (!roleValidation.IsValid)
        {
            errors.AddRange(roleValidation.Errors);
        }

        return (errors.Count == 0, errors);
    }

    /// <summary>
    /// 验证配置有效性
    /// </summary>
    /// <returns>验证结果</returns>
    public bool IsValid()
    {
        return !string.IsNullOrWhiteSpace(ClusterName) &&
               HeartbeatIntervalSeconds > 0 &&
               NodeTimeoutSeconds > HeartbeatIntervalSeconds &&
               NodeCleanupIntervalSeconds > 0 &&
               DiscoveryTimeoutSeconds > 0 &&
               MaxRetryAttempts >= 0 &&
               RetryIntervalSeconds > 0;
    }

    /// <summary>
    /// 获取配置摘要
    /// </summary>
    /// <returns>配置摘要字符串</returns>
    public override string ToString()
    {
        return $"NodeDiscovery[Cluster={ClusterName}, Heartbeat={HeartbeatIntervalSeconds}s, Timeout={NodeTimeoutSeconds}s, Role={NodeRole ?? "None"}]";
    }
}
