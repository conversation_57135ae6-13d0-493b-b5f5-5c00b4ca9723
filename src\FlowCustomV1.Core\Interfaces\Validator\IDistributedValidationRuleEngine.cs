using FlowCustomV1.Core.Models.Workflow;

namespace FlowCustomV1.Core.Interfaces.Validator;

/// <summary>
/// 分布式验证规则引擎接口
/// 支持跨节点的验证规则管理和执行
/// </summary>
public interface IDistributedValidationRuleEngine
{
    /// <summary>
    /// 执行验证规则
    /// </summary>
    /// <param name="context">验证上下文</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    Task<Models.Workflow.ValidationResult> ExecuteRulesAsync(ValidationContext context, CancellationToken cancellationToken = default);

    /// <summary>
    /// 注册验证规则
    /// </summary>
    /// <param name="rule">验证规则</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>注册结果</returns>
    Task<bool> RegisterRuleAsync(IDistributedValidationRule rule, CancellationToken cancellationToken = default);

    /// <summary>
    /// 添加验证规则
    /// </summary>
    /// <param name="rule">验证规则</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>添加结果</returns>
    Task AddRuleAsync(ValidationRule rule, CancellationToken cancellationToken = default);

    /// <summary>
    /// 更新验证规则
    /// </summary>
    /// <param name="ruleId">规则ID</param>
    /// <param name="rule">验证规则</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>更新结果</returns>
    Task<bool> UpdateRuleAsync(string ruleId, ValidationRule rule, CancellationToken cancellationToken = default);

    /// <summary>
    /// 设置规则启用状态
    /// </summary>
    /// <param name="ruleId">规则ID</param>
    /// <param name="enabled">是否启用</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>设置结果</returns>
    Task<bool> SetRuleEnabledAsync(string ruleId, bool enabled, CancellationToken cancellationToken = default);

    /// <summary>
    /// 移除验证规则
    /// </summary>
    /// <param name="ruleId">规则ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>移除结果</returns>
    Task<bool> RemoveRuleAsync(string ruleId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取所有规则
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>规则列表</returns>
    Task<IReadOnlyList<IDistributedValidationRule>> GetAllRulesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 按类别获取规则
    /// </summary>
    /// <param name="category">规则类别</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>规则列表</returns>
    Task<IReadOnlyList<IDistributedValidationRule>> GetRulesByCategoryAsync(ValidationRuleCategory category, CancellationToken cancellationToken = default);

    /// <summary>
    /// 同步规则到其他节点
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>同步任务</returns>
    Task SyncRulesToNodesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 从其他节点同步规则
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>同步任务</returns>
    Task SyncRulesFromNodesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 启用规则
    /// </summary>
    /// <param name="ruleId">规则ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>启用结果</returns>
    Task<bool> EnableRuleAsync(string ruleId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 禁用规则
    /// </summary>
    /// <param name="ruleId">规则ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>禁用结果</returns>
    Task<bool> DisableRuleAsync(string ruleId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取规则执行统计
    /// </summary>
    /// <param name="ruleId">规则ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>执行统计</returns>
    Task<RuleExecutionStatistics> GetRuleStatisticsAsync(string ruleId, CancellationToken cancellationToken = default);
}

/// <summary>
/// 分布式验证规则接口
/// </summary>
public interface IDistributedValidationRule : IValidationRule
{
    /// <summary>
    /// 规则ID
    /// </summary>
    string RuleId { get; }

    /// <summary>
    /// 规则版本
    /// </summary>
    string Version { get; }

    /// <summary>
    /// 规则类别
    /// </summary>
    ValidationRuleCategory Category { get; }

    /// <summary>
    /// 规则优先级
    /// </summary>
    int Priority { get; }

    /// <summary>
    /// 是否启用
    /// </summary>
    bool IsEnabled { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    DateTime CreatedAt { get; }

    /// <summary>
    /// 更新时间
    /// </summary>
    DateTime UpdatedAt { get; set; }

    /// <summary>
    /// 创建者
    /// </summary>
    string CreatedBy { get; }

    /// <summary>
    /// 规则配置
    /// </summary>
    Dictionary<string, object> Configuration { get; set; }

    /// <summary>
    /// 支持的节点类型
    /// </summary>
    HashSet<string> SupportedNodeTypes { get; }

    /// <summary>
    /// 是否支持并行执行
    /// </summary>
    bool SupportsParallelExecution { get; }

    /// <summary>
    /// 预估执行时间（毫秒）
    /// </summary>
    int EstimatedExecutionTimeMs { get; }

    /// <summary>
    /// 克隆规则
    /// </summary>
    /// <returns>规则副本</returns>
    IDistributedValidationRule Clone();

    /// <summary>
    /// 序列化规则
    /// </summary>
    /// <returns>序列化数据</returns>
    string Serialize();

    /// <summary>
    /// 反序列化规则
    /// </summary>
    /// <param name="data">序列化数据</param>
    void Deserialize(string data);
}

/// <summary>
/// 验证规则类别
/// </summary>
public enum ValidationRuleCategory
{
    /// <summary>
    /// 结构验证
    /// </summary>
    Structure,

    /// <summary>
    /// 语法验证
    /// </summary>
    Syntax,

    /// <summary>
    /// 语义验证
    /// </summary>
    Semantic,

    /// <summary>
    /// 性能验证
    /// </summary>
    Performance,

    /// <summary>
    /// 安全验证
    /// </summary>
    Security,

    /// <summary>
    /// 业务规则验证
    /// </summary>
    Business,

    /// <summary>
    /// 自定义验证
    /// </summary>
    Custom
}

/// <summary>
/// 规则执行统计
/// </summary>
public class RuleExecutionStatistics
{
    /// <summary>
    /// 规则ID
    /// </summary>
    public string RuleId { get; set; } = string.Empty;

    /// <summary>
    /// 总执行次数
    /// </summary>
    public long TotalExecutions { get; set; }

    /// <summary>
    /// 成功执行次数
    /// </summary>
    public long SuccessfulExecutions { get; set; }

    /// <summary>
    /// 失败执行次数
    /// </summary>
    public long FailedExecutions { get; set; }

    /// <summary>
    /// 平均执行时间（毫秒）
    /// </summary>
    public double AverageExecutionTimeMs { get; set; }

    /// <summary>
    /// 最小执行时间（毫秒）
    /// </summary>
    public long MinExecutionTimeMs { get; set; }

    /// <summary>
    /// 最大执行时间（毫秒）
    /// </summary>
    public long MaxExecutionTimeMs { get; set; }

    /// <summary>
    /// 最后执行时间
    /// </summary>
    public DateTime? LastExecutionTime { get; set; }

    /// <summary>
    /// 最后执行结果
    /// </summary>
    public bool? LastExecutionResult { get; set; }

    /// <summary>
    /// 错误消息列表
    /// </summary>
    public List<string> RecentErrors { get; set; } = new();
}

/// <summary>
/// 验证上下文
/// </summary>
public class ValidationContext
{
    /// <summary>
    /// 工作流定义
    /// </summary>
    public WorkflowDefinition? WorkflowDefinition { get; set; }

    /// <summary>
    /// 节点定义
    /// </summary>
    public NodeDefinition? NodeDefinition { get; set; }

    /// <summary>
    /// 验证类型
    /// </summary>
    public ValidationType ValidationType { get; set; }

    /// <summary>
    /// 验证参数
    /// </summary>
    public Dictionary<string, object> Parameters { get; set; } = new();

    /// <summary>
    /// 验证选项
    /// </summary>
    public ValidationOptions Options { get; set; } = new();

    /// <summary>
    /// 上下文元数据
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// 验证类型
/// </summary>
public enum ValidationType
{
    /// <summary>
    /// 工作流验证
    /// </summary>
    Workflow,

    /// <summary>
    /// 节点验证
    /// </summary>
    Node,

    /// <summary>
    /// 连接验证
    /// </summary>
    Connection,

    /// <summary>
    /// 参数验证
    /// </summary>
    Parameter,

    /// <summary>
    /// 完整验证
    /// </summary>
    Full
}

/// <summary>
/// 验证选项
/// </summary>
public class ValidationOptions
{
    /// <summary>
    /// 是否启用缓存
    /// </summary>
    public bool EnableCache { get; set; } = true;

    /// <summary>
    /// 是否并行执行
    /// </summary>
    public bool EnableParallelExecution { get; set; } = true;

    /// <summary>
    /// 最大并行度
    /// </summary>
    public int MaxParallelism { get; set; } = Environment.ProcessorCount;

    /// <summary>
    /// 验证超时时间（毫秒）
    /// </summary>
    public int TimeoutMs { get; set; } = 30000;

    /// <summary>
    /// 是否详细模式
    /// </summary>
    public bool VerboseMode { get; set; } = false;

    /// <summary>
    /// 跳过的规则类别
    /// </summary>
    public HashSet<ValidationRuleCategory> SkippedCategories { get; set; } = new();
}
