using FlowCustomV1.Core.Models.Common;
using FlowCustomV1.Core.Models.Workflow;
using FlowCustomV1.Core.Models.Executor;

namespace FlowCustomV1.Core.Interfaces.UseCases;

/// <summary>
/// 管理执行用例接口
/// 定义执行管理的业务契约
/// </summary>
public interface IManageExecutionUseCase
{
    /// <summary>
    /// 获取执行结果
    /// </summary>
    /// <param name="request">获取请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>执行结果</returns>
    Task<GetExecutionResultResponse> GetExecutionResultAsync(GetExecutionResultRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// 控制执行
    /// </summary>
    /// <param name="request">控制请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>控制响应</returns>
    Task<ControlExecutionResponse> ControlExecutionAsync(ControlExecutionRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// 查询执行列表
    /// </summary>
    /// <param name="request">查询请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>执行列表</returns>
    Task<QueryExecutionsResponse> QueryExecutionsAsync(QueryExecutionsRequest request, CancellationToken cancellationToken = default);
}

/// <summary>
/// 获取执行结果请求
/// </summary>
public class GetExecutionResultRequest
{
    /// <summary>
    /// 执行ID
    /// </summary>
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 请求者ID
    /// </summary>
    public string RequesterId { get; set; } = string.Empty;

    /// <summary>
    /// 是否等待完成
    /// </summary>
    public bool WaitForCompletion { get; set; } = false;

    /// <summary>
    /// 等待超时时间（毫秒）
    /// </summary>
    public long WaitTimeoutMs { get; set; } = 30000;
}

/// <summary>
/// 获取执行结果响应
/// </summary>
public class GetExecutionResultResponse
{
    /// <summary>
    /// 执行结果
    /// </summary>
    public WorkflowExecutionResult? Result { get; set; }

    /// <summary>
    /// 是否找到
    /// </summary>
    public bool Found { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// 控制执行请求
/// </summary>
public class ControlExecutionRequest
{
    /// <summary>
    /// 执行ID
    /// </summary>
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 控制操作
    /// </summary>
    public ExecutionControlOperation Operation { get; set; }

    /// <summary>
    /// 操作参数
    /// </summary>
    public Dictionary<string, object> Parameters { get; set; } = new();

    /// <summary>
    /// 请求者ID
    /// </summary>
    public string RequesterId { get; set; } = string.Empty;
}

/// <summary>
/// 控制执行响应
/// </summary>
public class ControlExecutionResponse
{
    /// <summary>
    /// 操作是否成功
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 操作结果数据
    /// </summary>
    public Dictionary<string, object> ResultData { get; set; } = new();
}

/// <summary>
/// 查询执行列表请求
/// </summary>
public class QueryExecutionsRequest : PagedQuery
{
    /// <summary>
    /// 工作流ID过滤器
    /// </summary>
    public string? WorkflowId { get; set; }

    /// <summary>
    /// 执行状态过滤器
    /// </summary>
    public List<WorkflowExecutionState>? States { get; set; }

    /// <summary>
    /// 执行器节点ID过滤器
    /// </summary>
    public string? ExecutorNodeId { get; set; }

    /// <summary>
    /// 开始时间范围
    /// </summary>
    public DateTimeRange? StartedTimeRange { get; set; }

    /// <summary>
    /// 完成时间范围
    /// </summary>
    public DateTimeRange? CompletedTimeRange { get; set; }

    /// <summary>
    /// 标签过滤器
    /// </summary>
    public List<string>? Tags { get; set; }

    /// <summary>
    /// 优先级过滤器
    /// </summary>
    public List<ExecutionPriority>? Priorities { get; set; }

    /// <summary>
    /// 是否包含错误信息
    /// </summary>
    public bool? HasError { get; set; }

    /// <summary>
    /// 执行时长范围
    /// </summary>
    public TimeSpanRange? DurationRange { get; set; }
}

/// <summary>
/// 查询执行列表响应
/// </summary>
public class QueryExecutionsResponse
{
    /// <summary>
    /// 分页结果
    /// </summary>
    public PagedResult<ExecutionSummary> PagedResult { get; set; } = PagedResult<ExecutionSummary>.Empty();

    /// <summary>
    /// 查询统计信息
    /// </summary>
    public QueryStatistics Statistics { get; set; } = new();
}

/// <summary>
/// 执行摘要信息
/// </summary>
public class ExecutionSummary
{
    /// <summary>
    /// 执行ID
    /// </summary>
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 工作流ID
    /// </summary>
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 工作流名称
    /// </summary>
    public string WorkflowName { get; set; } = string.Empty;

    /// <summary>
    /// 执行状态
    /// </summary>
    public WorkflowExecutionState State { get; set; }

    /// <summary>
    /// 执行器节点ID
    /// </summary>
    public string ExecutorNodeId { get; set; } = string.Empty;

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime StartedAt { get; set; }

    /// <summary>
    /// 完成时间
    /// </summary>
    public DateTime? CompletedAt { get; set; }

    /// <summary>
    /// 执行时长
    /// </summary>
    public TimeSpan? Duration => CompletedAt?.Subtract(StartedAt);

    /// <summary>
    /// 执行进度（0-100）
    /// </summary>
    public double Progress { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 执行优先级
    /// </summary>
    public ExecutionPriority Priority { get; set; } = ExecutionPriority.Normal;

    /// <summary>
    /// 执行标签
    /// </summary>
    public List<string> Tags { get; set; } = new();
}

/// <summary>
/// 执行控制操作
/// </summary>
public enum ExecutionControlOperation
{
    /// <summary>
    /// 取消执行
    /// </summary>
    Cancel,

    /// <summary>
    /// 暂停执行
    /// </summary>
    Pause,

    /// <summary>
    /// 恢复执行
    /// </summary>
    Resume,

    /// <summary>
    /// 重启执行
    /// </summary>
    Restart,

    /// <summary>
    /// 查询状态
    /// </summary>
    QueryStatus
}

/// <summary>
/// 查询统计信息
/// </summary>
public class QueryStatistics
{
    /// <summary>
    /// 查询耗时（毫秒）
    /// </summary>
    public long QueryTimeMs { get; set; }

    /// <summary>
    /// 匹配的总记录数
    /// </summary>
    public long TotalMatched { get; set; }

    /// <summary>
    /// 过滤的记录数
    /// </summary>
    public long FilteredCount { get; set; }
}

/// <summary>
/// 日期时间范围
/// </summary>
public class DateTimeRange
{
    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime? Start { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime? End { get; set; }
}

/// <summary>
/// 时间跨度范围
/// </summary>
public class TimeSpanRange
{
    /// <summary>
    /// 最小时长
    /// </summary>
    public TimeSpan? Min { get; set; }

    /// <summary>
    /// 最大时长
    /// </summary>
    public TimeSpan? Max { get; set; }
}
