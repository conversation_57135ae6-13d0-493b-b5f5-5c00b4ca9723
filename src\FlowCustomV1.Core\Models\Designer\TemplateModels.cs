using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace FlowCustomV1.Core.Models.Designer;

/// <summary>
/// 模板查询条件
/// </summary>
public class TemplateQuery
{
    /// <summary>
    /// 模板名称（模糊匹配）
    /// </summary>
    [JsonPropertyName("name")]
    public string? Name { get; set; }

    /// <summary>
    /// 模板分类
    /// </summary>
    [JsonPropertyName("category")]
    public string? Category { get; set; }

    /// <summary>
    /// 作者ID
    /// </summary>
    [JsonPropertyName("author")]
    public string? Author { get; set; }

    /// <summary>
    /// 标签过滤
    /// </summary>
    [JsonPropertyName("tags")]
    public List<string>? Tags { get; set; }

    /// <summary>
    /// 是否只查询公共模板
    /// </summary>
    [JsonPropertyName("publicOnly")]
    public bool PublicOnly { get; set; } = false;

    /// <summary>
    /// 是否只查询内置模板
    /// </summary>
    [JsonPropertyName("builtInOnly")]
    public bool BuiltInOnly { get; set; } = false;

    /// <summary>
    /// 最小评分
    /// </summary>
    [JsonPropertyName("minRating")]
    public double? MinRating { get; set; }

    /// <summary>
    /// 创建时间范围（开始）
    /// </summary>
    [JsonPropertyName("createdAfter")]
    public DateTime? CreatedAfter { get; set; }

    /// <summary>
    /// 创建时间范围（结束）
    /// </summary>
    [JsonPropertyName("createdBefore")]
    public DateTime? CreatedBefore { get; set; }

    /// <summary>
    /// 分页大小
    /// </summary>
    [JsonPropertyName("pageSize")]
    public int PageSize { get; set; } = 20;

    /// <summary>
    /// 页码（从1开始）
    /// </summary>
    [JsonPropertyName("pageNumber")]
    public int PageNumber { get; set; } = 1;

    /// <summary>
    /// 排序字段
    /// </summary>
    [JsonPropertyName("sortBy")]
    public string SortBy { get; set; } = "CreatedAt";

    /// <summary>
    /// 排序方向
    /// </summary>
    [JsonPropertyName("sortDirection")]
    public SortDirection SortDirection { get; set; } = SortDirection.Descending;
}

/// <summary>
/// 模板版本信息
/// </summary>
public class TemplateVersion
{
    /// <summary>
    /// 版本ID
    /// </summary>
    [Required]
    [JsonPropertyName("versionId")]
    public string VersionId { get; set; } = string.Empty;

    /// <summary>
    /// 模板ID
    /// </summary>
    [Required]
    [JsonPropertyName("templateId")]
    public string TemplateId { get; set; } = string.Empty;

    /// <summary>
    /// 版本号
    /// </summary>
    [Required]
    [JsonPropertyName("version")]
    public string Version { get; set; } = string.Empty;

    /// <summary>
    /// 版本描述
    /// </summary>
    [JsonPropertyName("description")]
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 模板定义快照
    /// </summary>
    [JsonPropertyName("template")]
    public WorkflowTemplate Template { get; set; } = new();

    /// <summary>
    /// 是否为活跃版本
    /// </summary>
    [JsonPropertyName("isActive")]
    public bool IsActive { get; set; } = false;

    /// <summary>
    /// 创建时间
    /// </summary>
    [JsonPropertyName("createdAt")]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 创建者ID
    /// </summary>
    [JsonPropertyName("createdBy")]
    public string CreatedBy { get; set; } = string.Empty;

    /// <summary>
    /// 创建者名称
    /// </summary>
    [JsonPropertyName("createdByName")]
    public string CreatedByName { get; set; } = string.Empty;

    /// <summary>
    /// 变更摘要
    /// </summary>
    [JsonPropertyName("changeSummary")]
    public List<string> ChangeSummary { get; set; } = new();
}

/// <summary>
/// 模板版本信息（用于创建版本）
/// </summary>
public class TemplateVersionInfo
{
    /// <summary>
    /// 版本号
    /// </summary>
    [Required]
    [JsonPropertyName("version")]
    public string Version { get; set; } = string.Empty;

    /// <summary>
    /// 版本描述
    /// </summary>
    [JsonPropertyName("description")]
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 是否设为活跃版本
    /// </summary>
    [JsonPropertyName("setAsActive")]
    public bool SetAsActive { get; set; } = false;

    /// <summary>
    /// 创建者ID
    /// </summary>
    [JsonPropertyName("createdBy")]
    public string CreatedBy { get; set; } = string.Empty;

    /// <summary>
    /// 创建者名称
    /// </summary>
    [JsonPropertyName("createdByName")]
    public string CreatedByName { get; set; } = string.Empty;

    /// <summary>
    /// 变更摘要
    /// </summary>
    [JsonPropertyName("changeSummary")]
    public List<string> ChangeSummary { get; set; } = new();
}

/// <summary>
/// 模板使用统计
/// </summary>
public class TemplateUsageStatistics
{
    /// <summary>
    /// 模板ID
    /// </summary>
    [JsonPropertyName("templateId")]
    public string TemplateId { get; set; } = string.Empty;

    /// <summary>
    /// 总使用次数
    /// </summary>
    [JsonPropertyName("totalUsageCount")]
    public int TotalUsageCount { get; set; } = 0;

    /// <summary>
    /// 本月使用次数
    /// </summary>
    [JsonPropertyName("monthlyUsageCount")]
    public int MonthlyUsageCount { get; set; } = 0;

    /// <summary>
    /// 本周使用次数
    /// </summary>
    [JsonPropertyName("weeklyUsageCount")]
    public int WeeklyUsageCount { get; set; } = 0;

    /// <summary>
    /// 今日使用次数
    /// </summary>
    [JsonPropertyName("dailyUsageCount")]
    public int DailyUsageCount { get; set; } = 0;

    /// <summary>
    /// 平均评分
    /// </summary>
    [JsonPropertyName("averageRating")]
    public double AverageRating { get; set; } = 0.0;

    /// <summary>
    /// 评分次数
    /// </summary>
    [JsonPropertyName("ratingCount")]
    public int RatingCount { get; set; } = 0;

    /// <summary>
    /// 最后使用时间
    /// </summary>
    [JsonPropertyName("lastUsedAt")]
    public DateTime? LastUsedAt { get; set; }

    /// <summary>
    /// 使用者数量
    /// </summary>
    [JsonPropertyName("uniqueUserCount")]
    public int UniqueUserCount { get; set; } = 0;

    /// <summary>
    /// 每日使用统计（最近30天）
    /// </summary>
    [JsonPropertyName("dailyUsageHistory")]
    public Dictionary<string, int> DailyUsageHistory { get; set; } = new();
}

/// <summary>
/// 模板兼容性检查结果
/// </summary>
public class TemplateCompatibilityResult
{
    /// <summary>
    /// 是否兼容
    /// </summary>
    [JsonPropertyName("isCompatible")]
    public bool IsCompatible { get; set; } = true;

    /// <summary>
    /// 兼容性级别
    /// </summary>
    [JsonPropertyName("compatibilityLevel")]
    public CompatibilityLevel CompatibilityLevel { get; set; } = CompatibilityLevel.FullyCompatible;

    /// <summary>
    /// 不兼容的原因列表
    /// </summary>
    [JsonPropertyName("incompatibilityReasons")]
    public List<string> IncompatibilityReasons { get; set; } = new();

    /// <summary>
    /// 警告信息列表
    /// </summary>
    [JsonPropertyName("warnings")]
    public List<string> Warnings { get; set; } = new();

    /// <summary>
    /// 建议的迁移步骤
    /// </summary>
    [JsonPropertyName("migrationSteps")]
    public List<string> MigrationSteps { get; set; } = new();

    /// <summary>
    /// 检查时间
    /// </summary>
    [JsonPropertyName("checkedAt")]
    public DateTime CheckedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 兼容性级别枚举
/// </summary>
public enum CompatibilityLevel
{
    /// <summary>
    /// 完全兼容
    /// </summary>
    FullyCompatible,

    /// <summary>
    /// 基本兼容（有警告）
    /// </summary>
    MostlyCompatible,

    /// <summary>
    /// 部分兼容（需要修改）
    /// </summary>
    PartiallyCompatible,

    /// <summary>
    /// 不兼容
    /// </summary>
    Incompatible
}
