﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FlowCustomV1.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class IncreaseIdFieldLengthsForLongNames : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "WorkflowId",
                table: "WorkflowInstances",
                type: "varchar(255)",
                maxLength: 50,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "TEXT",
                oldMaxLength: 50);

            migrationBuilder.AlterColumn<string>(
                name: "InstanceId",
                table: "WorkflowInstances",
                type: "varchar(255)",
                maxLength: 50,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "TEXT",
                oldMaxLength: 50);

            migrationBuilder.AlterColumn<string>(
                name: "WorkflowId",
                table: "WorkflowDefinitions",
                type: "varchar(255)",
                maxLength: 50,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "TEXT",
                oldMaxLength: 50);

            migrationBuilder.AlterColumn<string>(
                name: "NodeId",
                table: "NodeExecutions",
                type: "varchar(255)",
                maxLength: 50,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "TEXT",
                oldMaxLength: 50);

            migrationBuilder.AlterColumn<string>(
                name: "InstanceId",
                table: "NodeExecutions",
                type: "varchar(255)",
                maxLength: 50,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "TEXT",
                oldMaxLength: 50);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "WorkflowId",
                table: "WorkflowInstances",
                type: "TEXT",
                maxLength: 50,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "varchar(255)",
                oldMaxLength: 50);

            migrationBuilder.AlterColumn<string>(
                name: "InstanceId",
                table: "WorkflowInstances",
                type: "TEXT",
                maxLength: 50,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "varchar(255)",
                oldMaxLength: 50);

            migrationBuilder.AlterColumn<string>(
                name: "WorkflowId",
                table: "WorkflowDefinitions",
                type: "TEXT",
                maxLength: 50,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "varchar(255)",
                oldMaxLength: 50);

            migrationBuilder.AlterColumn<string>(
                name: "NodeId",
                table: "NodeExecutions",
                type: "TEXT",
                maxLength: 50,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "varchar(255)",
                oldMaxLength: 50);

            migrationBuilder.AlterColumn<string>(
                name: "InstanceId",
                table: "NodeExecutions",
                type: "TEXT",
                maxLength: 50,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "varchar(255)",
                oldMaxLength: 50);
        }
    }
}
