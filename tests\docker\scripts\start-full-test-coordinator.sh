#!/bin/bash

# FlowCustomV1 v0.0.1.7 全面功能测试协调器启动脚本
# 执行所有功能的完整测试套件

set -e

echo "🚀 FlowCustomV1 v0.0.1.7 全面功能测试协调器启动中..."

# 环境变量设置
export TEST_START_TIME=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
export TEST_RESULTS_DIR="/app/test-results"
export TEST_LOGS_DIR="/app/logs"
export TEST_REPORTS_DIR="/app/reports"

# 创建必要的目录
mkdir -p "$TEST_RESULTS_DIR" "$TEST_LOGS_DIR" "$TEST_REPORTS_DIR"

# 日志函数
log_info() {
    echo "[$(date -u +"%Y-%m-%d %H:%M:%S")] [INFO] $1" | tee -a "$TEST_LOGS_DIR/coordinator.log"
}

log_error() {
    echo "[$(date -u +"%Y-%m-%d %H:%M:%S")] [ERROR] $1" | tee -a "$TEST_LOGS_DIR/coordinator.log"
}

log_success() {
    echo "[$(date -u +"%Y-%m-%d %H:%M:%S")] [SUCCESS] $1" | tee -a "$TEST_LOGS_DIR/coordinator.log"
}

# 等待所有服务就绪
wait_for_services() {
    log_info "等待所有服务就绪..."
    
    # 等待NATS集群
    log_info "等待NATS集群..."
    for i in {1..3}; do
        until curl -f "http://nats-$i:8222/healthz" >/dev/null 2>&1; do
            log_info "等待NATS-$i..."
            sleep 5
        done
        log_success "NATS-$i 就绪"
    done
    
    # 等待MySQL
    log_info "等待MySQL数据库..."
    until mysqladmin ping -h mysql-full-test -u root -pFullTestPassword123! >/dev/null 2>&1; do
        log_info "等待MySQL..."
        sleep 5
    done
    log_success "MySQL 就绪"
    
    # 等待所有应用节点
    local nodes=(
        "master-node-beijing:5000"
        "master-node-shanghai:5000"
        "worker-node-beijing-1:5000"
        "worker-node-beijing-2:5000"
        "worker-node-shanghai-1:5000"
        "designer-node-beijing:5000"
        "designer-node-shanghai:5000"
        "validator-node-beijing:5000"
        "validator-node-shanghai:5000"
        "executor-node-beijing:5000"
        "executor-node-shanghai:5000"
    )
    
    for node in "${nodes[@]}"; do
        log_info "等待节点 $node..."
        until curl -f "http://$node/health" >/dev/null 2>&1; do
            log_info "等待 $node..."
            sleep 10
        done
        log_success "节点 $node 就绪"
    done
    
    log_success "所有服务已就绪！"
}

# 执行基础设施测试
run_infrastructure_tests() {
    log_info "🔧 执行基础设施测试..."
    
    cd /app/test-scripts
    
    # NATS集群测试
    log_info "执行NATS集群测试..."
    python3 test_nats_cluster.py --output "$TEST_RESULTS_DIR/nats_cluster_test.json" || {
        log_error "NATS集群测试失败"
        return 1
    }
    
    # MySQL数据库测试
    log_info "执行MySQL数据库测试..."
    python3 test_mysql_database.py --output "$TEST_RESULTS_DIR/mysql_test.json" || {
        log_error "MySQL数据库测试失败"
        return 1
    }
    
    log_success "基础设施测试完成"
}

# 执行节点发现和注册测试
run_node_discovery_tests() {
    log_info "🔍 执行节点发现和注册测试..."
    
    cd /app/test-scripts
    
    # 节点发现测试
    python3 test_node_discovery.py --output "$TEST_RESULTS_DIR/node_discovery_test.json" || {
        log_error "节点发现测试失败"
        return 1
    }
    
    # 集群拓扑测试
    python3 test_cluster_topology.py --output "$TEST_RESULTS_DIR/cluster_topology_test.json" || {
        log_error "集群拓扑测试失败"
        return 1
    }
    
    log_success "节点发现和注册测试完成"
}

# 执行专业化节点服务测试
run_specialized_node_tests() {
    log_info "🎨 执行专业化节点服务测试..."
    
    cd /app/test-scripts
    
    # Designer节点测试
    log_info "执行Designer节点测试..."
    python3 test_designer_services.py --output "$TEST_RESULTS_DIR/designer_test.json" || {
        log_error "Designer节点测试失败"
        return 1
    }
    
    # Validator节点测试
    log_info "执行Validator节点测试..."
    python3 test_validator_services.py --output "$TEST_RESULTS_DIR/validator_test.json" || {
        log_error "Validator节点测试失败"
        return 1
    }
    
    # Executor节点测试
    log_info "执行Executor节点测试..."
    python3 test_executor_services.py --output "$TEST_RESULTS_DIR/executor_test.json" || {
        log_error "Executor节点测试失败"
        return 1
    }
    
    log_success "专业化节点服务测试完成"
}

# 执行分布式任务调度测试
run_distributed_scheduling_tests() {
    log_info "⚡ 执行分布式任务调度测试..."
    
    cd /app/test-scripts
    
    # 任务分发测试
    python3 test_task_distribution.py --output "$TEST_RESULTS_DIR/task_distribution_test.json" || {
        log_error "任务分发测试失败"
        return 1
    }
    
    # 负载均衡测试
    python3 test_load_balancing.py --output "$TEST_RESULTS_DIR/load_balancing_test.json" || {
        log_error "负载均衡测试失败"
        return 1
    }
    
    # 故障转移测试
    python3 test_failover.py --output "$TEST_RESULTS_DIR/failover_test.json" || {
        log_error "故障转移测试失败"
        return 1
    }
    
    log_success "分布式任务调度测试完成"
}

# 执行工作流端到端测试
run_workflow_e2e_tests() {
    log_info "🔄 执行工作流端到端测试..."
    
    cd /app/test-scripts
    
    # 简单工作流测试
    python3 test_simple_workflow.py --output "$TEST_RESULTS_DIR/simple_workflow_test.json" || {
        log_error "简单工作流测试失败"
        return 1
    }
    
    # 复杂工作流测试
    python3 test_complex_workflow.py --output "$TEST_RESULTS_DIR/complex_workflow_test.json" || {
        log_error "复杂工作流测试失败"
        return 1
    }
    
    # 并发工作流测试
    python3 test_concurrent_workflows.py --output "$TEST_RESULTS_DIR/concurrent_workflows_test.json" || {
        log_error "并发工作流测试失败"
        return 1
    }
    
    log_success "工作流端到端测试完成"
}

# 执行性能和压力测试
run_performance_tests() {
    log_info "🚀 执行性能和压力测试..."
    
    cd /app/test-scripts
    
    # 性能基准测试
    python3 test_performance_benchmark.py --output "$TEST_RESULTS_DIR/performance_test.json" || {
        log_error "性能基准测试失败"
        return 1
    }
    
    # 压力测试
    python3 test_stress.py --output "$TEST_RESULTS_DIR/stress_test.json" || {
        log_error "压力测试失败"
        return 1
    }
    
    log_success "性能和压力测试完成"
}

# 生成测试报告
generate_test_report() {
    log_info "📊 生成测试报告..."
    
    cd /app/test-scripts
    python3 generate_test_report.py \
        --results-dir "$TEST_RESULTS_DIR" \
        --output "$TEST_REPORTS_DIR/full_test_report.html" \
        --json-output "$TEST_REPORTS_DIR/full_test_report.json"
    
    log_success "测试报告已生成"
}

# 主测试流程
main() {
    log_info "开始FlowCustomV1 v0.0.1.7全面功能测试"
    
    # 等待服务就绪
    wait_for_services
    
    # 执行测试套件
    run_infrastructure_tests
    run_node_discovery_tests
    run_specialized_node_tests
    run_distributed_scheduling_tests
    run_workflow_e2e_tests
    run_performance_tests
    
    # 生成报告
    generate_test_report
    
    export TEST_END_TIME=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    log_success "FlowCustomV1 v0.0.1.7全面功能测试完成！"
    log_info "测试开始时间: $TEST_START_TIME"
    log_info "测试结束时间: $TEST_END_TIME"
    log_info "测试结果目录: $TEST_RESULTS_DIR"
    log_info "测试报告目录: $TEST_REPORTS_DIR"
    
    # 保持容器运行以便查看结果
    tail -f "$TEST_LOGS_DIR/coordinator.log"
}

# 信号处理
trap 'log_info "收到终止信号，正在清理..."; exit 0' SIGTERM SIGINT

# 启动主流程
main "$@"
