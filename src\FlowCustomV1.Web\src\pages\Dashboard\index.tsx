import React, { useEffect, useState } from 'react';
import { Row, Col, Statistic, Progress, Table, Tag, Space, Button } from 'antd';
import {
  PlayCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ClusterOutlined,
  ReloadOutlined,
  EyeOutlined,
  DashboardOutlined
} from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';
import { workflowApi, executionApi } from '@/services/workflow';
import { clusterApi } from '@/services/cluster';
import type { WorkflowExecution, ClusterStats } from '@/types/api';
import PageLayout from '@/components/Layout/PageLayout';

const Dashboard: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalWorkflows: 0,
    totalExecutions: 0,
    runningExecutions: 0,
    completedExecutions: 0,
    failedExecutions: 0,
  });
  const [clusterStats, setClusterStats] = useState<ClusterStats | null>(null);
  const [recentExecutions, setRecentExecutions] = useState<WorkflowExecution[]>([]);

  // 加载仪表盘数据
  const loadDashboardData = async () => {
    try {
      setLoading(true);
      
      // 并行加载数据
      const [workflows, executionStats, cluster, executions] = await Promise.all([
        workflowApi.getWorkflows().catch(() => ({ data: [], totalCount: 0, page: 1, pageSize: 10, totalPages: 0 })),
        executionApi.getExecutionStats().catch(() => ({
          total: 0,
          running: 0,
          completed: 0,
          failed: 0,
          averageDuration: 0,
        })),
        clusterApi.getClusterStats().catch(() => null),
        executionApi.getAllExecutions({ pageIndex: 0, pageSize: 10 }).catch(() => ({ data: [], totalCount: 0, page: 1, pageSize: 10, totalPages: 0 })),
      ]);

      setStats({
        totalWorkflows: workflows.data?.length || 0,
        totalExecutions: executionStats.total,
        runningExecutions: executionStats.running,
        completedExecutions: executionStats.completed,
        failedExecutions: executionStats.failed,
      });

      setClusterStats(cluster);
      setRecentExecutions(executions.data?.slice(0, 10) || []);
    } catch (error) {
      console.error('加载仪表盘数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadDashboardData();
  }, []);

  // 执行状态标签
  const getStatusTag = (status: string) => {
    const statusMap = {
      Running: { color: 'processing', text: '运行中' },
      Completed: { color: 'success', text: '已完成' },
      Failed: { color: 'error', text: '失败' },
      Pending: { color: 'default', text: '等待中' },
      Cancelled: { color: 'warning', text: '已取消' },
      Paused: { color: 'warning', text: '已暂停' },
    };
    const config = statusMap[status as keyof typeof statusMap] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 最近执行表格列定义
  const executionColumns = [
    {
      title: '工作流名称',
      dataIndex: 'workflowName',
      key: 'workflowName',
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => getStatusTag(status),
    },
    {
      title: '开始时间',
      dataIndex: 'startedAt',
      key: 'startedAt',
      render: (time: string) => new Date(time).toLocaleString(),
    },
    {
      title: '持续时间',
      dataIndex: 'duration',
      key: 'duration',
      render: (duration: number) => duration ? `${Math.round(duration / 1000)}s` : '-',
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: WorkflowExecution) => (
        <Space size="small">
          <Button 
            type="link" 
            size="small" 
            icon={<EyeOutlined />}
            onClick={() => window.open(`/execution/monitor?id=${record.executionId}`, '_blank')}
          >
            查看
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <PageLayout
      title="系统仪表盘"
      description="FlowCustomV1 工作流自动化系统运行概览"
      icon={<DashboardOutlined />}
      actions={
        <Button
          type="primary"
          icon={<ReloadOutlined />}
          loading={loading}
          onClick={loadDashboardData}
        >
          刷新数据
        </Button>
      }
    >
      {/* 统计卡片 */}
      <Row gutter={[16, 16]} className="layout-card-grid">
        <Col xs={24} sm={12} lg={6}>
          <ProCard className="layout-card-statistic">
            <Statistic
              title="工作流总数"
              value={stats.totalWorkflows}
              prefix={<PlayCircleOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </ProCard>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <ProCard className="layout-card-statistic">
            <Statistic
              title="执行总数"
              value={stats.totalExecutions}
              prefix={<PlayCircleOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </ProCard>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <ProCard className="layout-card-statistic">
            <Statistic
              title="运行中"
              value={stats.runningExecutions}
              prefix={<PlayCircleOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </ProCard>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <ProCard className="layout-card-statistic">
            <Statistic
              title="成功率"
              value={stats.totalExecutions > 0 ?
                Math.round((stats.completedExecutions / stats.totalExecutions) * 100) : 0
              }
              suffix="%"
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </ProCard>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        {/* 集群状态 */}
        <Col xs={24} lg={8}>
          <ProCard title="集群状态" loading={loading}>
            {clusterStats ? (
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span>集群名称</span>
                  <Tag color="blue">{clusterStats.clusterName}</Tag>
                </div>
                <div className="flex justify-between items-center">
                  <span>在线节点</span>
                  <span className="font-semibold">
                    {clusterStats.onlineNodes} / {clusterStats.totalNodes}
                  </span>
                </div>
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <span>系统健康度</span>
                    <Tag color={clusterStats.systemHealth === 'Healthy' ? 'green' : 
                      clusterStats.systemHealth === 'Warning' ? 'orange' : 'red'}>
                      {clusterStats.systemHealth === 'Healthy' ? '健康' :
                       clusterStats.systemHealth === 'Warning' ? '警告' : '严重'}
                    </Tag>
                  </div>
                  <Progress 
                    percent={clusterStats.systemHealth === 'Healthy' ? 100 : 
                      clusterStats.systemHealth === 'Warning' ? 70 : 30}
                    status={clusterStats.systemHealth === 'Healthy' ? 'success' : 
                      clusterStats.systemHealth === 'Warning' ? 'active' : 'exception'}
                    showInfo={false}
                  />
                </div>
              </div>
            ) : (
              <div className="text-center text-gray-500 py-8">
                <ClusterOutlined className="text-4xl mb-2" />
                <p>集群数据加载中...</p>
              </div>
            )}
          </ProCard>
        </Col>

        {/* 执行统计 */}
        <Col xs={24} lg={8}>
          <ProCard title="执行统计" loading={loading}>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="flex items-center">
                  <CheckCircleOutlined className="text-green-500 mr-2" />
                  成功执行
                </span>
                <span className="font-semibold text-green-600">{stats.completedExecutions}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="flex items-center">
                  <PlayCircleOutlined className="text-blue-500 mr-2" />
                  运行中
                </span>
                <span className="font-semibold text-blue-600">{stats.runningExecutions}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="flex items-center">
                  <CloseCircleOutlined className="text-red-500 mr-2" />
                  失败执行
                </span>
                <span className="font-semibold text-red-600">{stats.failedExecutions}</span>
              </div>
              <div className="pt-2 border-t">
                <Progress
                  percent={stats.totalExecutions > 0 ? 
                    Math.round((stats.completedExecutions / stats.totalExecutions) * 100) : 0}
                  strokeColor="#52c41a"
                  format={(percent) => `成功率 ${percent}%`}
                />
              </div>
            </div>
          </ProCard>
        </Col>

        {/* 快速操作 */}
        <Col xs={24} lg={8}>
          <ProCard title="快速操作">
            <div className="space-y-3">
              <Button 
                type="primary" 
                block 
                onClick={() => window.open('/workflow/designer', '_blank')}
              >
                创建新工作流
              </Button>
              <Button 
                block 
                onClick={() => window.open('/workflow/list', '_blank')}
              >
                管理工作流
              </Button>
              <Button 
                block 
                onClick={() => window.open('/execution/monitor', '_blank')}
              >
                执行监控
              </Button>
              <Button 
                block 
                onClick={() => window.open('/cluster/overview', '_blank')}
              >
                集群管理
              </Button>
            </div>
          </ProCard>
        </Col>
      </Row>

      {/* 最近执行 */}
      <Row className="mt-6">
        <Col span={24}>
          <ProCard title="最近执行记录" loading={loading}>
            <Table
              columns={executionColumns}
              dataSource={recentExecutions}
              rowKey="executionId"
              pagination={false}
              size="small"
              locale={{ emptyText: '暂无执行记录' }}
            />
          </ProCard>
        </Col>
      </Row>
    </PageLayout>
  );
};

export default Dashboard;
