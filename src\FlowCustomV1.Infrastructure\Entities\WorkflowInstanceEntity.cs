using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using FlowCustomV1.Core.Models.Workflow;

namespace FlowCustomV1.Infrastructure.Entities;

/// <summary>
/// 工作流实例实体
/// 对应数据库中的WorkflowInstances表
/// </summary>
[Table("WorkflowInstances")]
public class WorkflowInstanceEntity
{
    /// <summary>
    /// 主键ID
    /// </summary>
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public long Id { get; set; }

    /// <summary>
    /// 实例唯一标识符
    /// </summary>
    [Column(TypeName = "varchar(255)")]
    public string InstanceId { get; set; } = string.Empty;

    /// <summary>
    /// 工作流定义标识符
    /// </summary>
    [Column(TypeName = "varchar(255)")]
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 工作流名称
    /// </summary>
    [Column(TypeName = "varchar(500)")]
    public string WorkflowName { get; set; } = string.Empty;

    /// <summary>
    /// 执行节点ID
    /// </summary>
    [Column(TypeName = "varchar(255)")]
    public string ExecutorNodeId { get; set; } = string.Empty;

    /// <summary>
    /// 执行状态
    /// </summary>
    [Required]
    [Column("State")]
    public WorkflowExecutionState State { get; set; } = WorkflowExecutionState.NotStarted;

    /// <summary>
    /// 是否成功
    /// </summary>
    [Column("IsSuccess")]
    public bool IsSuccess { get; set; } = false;

    /// <summary>
    /// 开始时间
    /// </summary>
    [Required]
    [Column("StartedAt")]
    public DateTime StartedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 完成时间
    /// </summary>
    [Column("CompletedAt")]
    public DateTime? CompletedAt { get; set; }

    /// <summary>
    /// 输入数据（JSON格式）
    /// </summary>
    [Column("InputData", TypeName = "TEXT")]
    public string? InputData { get; set; }

    /// <summary>
    /// 输出数据（JSON格式）
    /// </summary>
    [Column("OutputData", TypeName = "TEXT")]
    public string? OutputData { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    [Column("ErrorMessage", TypeName = "TEXT")]
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 执行消息
    /// </summary>
    [Column("Message", TypeName = "TEXT")]
    public string? Message { get; set; }

    /// <summary>
    /// 执行统计信息（JSON格式）
    /// </summary>
    [Column("Stats", TypeName = "TEXT")]
    public string? Stats { get; set; }

    /// <summary>
    /// 元数据（JSON格式）
    /// </summary>
    [Column("Metadata", TypeName = "TEXT")]
    public string? Metadata { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    [Required]
    [Column("CreatedAt")]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 最后更新时间
    /// </summary>
    [Required]
    [Column("LastUpdatedAt")]
    public DateTime LastUpdatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 最后修改时间（别名属性）
    /// </summary>
    public DateTime LastModifiedAt
    {
        get => LastUpdatedAt;
        set => LastUpdatedAt = value;
    }

    /// <summary>
    /// 关联的工作流定义
    /// </summary>
    [ForeignKey(nameof(WorkflowId))]
    public virtual WorkflowDefinitionEntity? WorkflowDefinition { get; set; }

    /// <summary>
    /// 关联的节点执行记录
    /// </summary>
    public virtual ICollection<NodeExecutionEntity> NodeExecutions { get; set; } = new List<NodeExecutionEntity>();

    /// <summary>
    /// 版本戳
    /// </summary>
    [Timestamp]
    public byte[]? RowVersion { get; set; }

    /// <summary>
    /// 计算执行时长
    /// </summary>
    public TimeSpan Duration => CompletedAt?.Subtract(StartedAt) ?? TimeSpan.Zero;
}
