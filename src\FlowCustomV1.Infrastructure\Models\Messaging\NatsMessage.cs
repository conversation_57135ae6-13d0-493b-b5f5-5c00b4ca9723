using System.Text.Json.Serialization;
using FlowCustomV1.Core.Interfaces.Messaging;

namespace FlowCustomV1.Infrastructure.Models.Messaging;

/// <summary>
/// NATS消息具体实现
/// 包含NATS特定的技术实现细节
/// </summary>
public class NatsMessage : IMessage
{
    /// <summary>
    /// 消息唯一标识
    /// </summary>
    [JsonPropertyName("messageId")]
    public string MessageId { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 消息类型
    /// </summary>
    [JsonPropertyName("messageType")]
    public string MessageType { get; set; } = string.Empty;

    /// <summary>
    /// 发送者节点ID
    /// </summary>
    [JsonPropertyName("senderId")]
    public string SenderId { get; set; } = string.Empty;

    /// <summary>
    /// 目标节点ID（可选，用于点对点消息）
    /// </summary>
    [JsonPropertyName("targetId")]
    public string? TargetId { get; set; }

    /// <summary>
    /// 消息创建时间
    /// </summary>
    [JsonPropertyName("createdAt")]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 消息过期时间
    /// </summary>
    [JsonPropertyName("expiresAt")]
    public DateTime? ExpiresAt { get; set; }

    /// <summary>
    /// 消息优先级
    /// </summary>
    [JsonPropertyName("priority")]
    public MessagePriority Priority { get; set; } = MessagePriority.Normal;

    /// <summary>
    /// 消息内容
    /// </summary>
    [JsonPropertyName("payload")]
    public object? Payload { get; set; }

    /// <summary>
    /// 消息元数据
    /// </summary>
    [JsonPropertyName("metadata")]
    public Dictionary<string, object> MetadataDict { get; set; } = new();

    /// <summary>
    /// 消息元数据（只读）
    /// </summary>
    [JsonIgnore]
    public IReadOnlyDictionary<string, object> Metadata => MetadataDict.AsReadOnly();

    /// <summary>
    /// 消息主题
    /// </summary>
    [JsonPropertyName("subject")]
    public string Subject { get; set; } = string.Empty;

    /// <summary>
    /// 回复主题
    /// </summary>
    [JsonPropertyName("replyTo")]
    public string? ReplyTo { get; set; }

    /// <summary>
    /// 消息头信息
    /// </summary>
    [JsonPropertyName("headers")]
    public Dictionary<string, string> Headers { get; set; } = new();

    /// <summary>
    /// 是否需要确认
    /// </summary>
    [JsonPropertyName("requiresAck")]
    public bool RequiresAck { get; set; } = false;

    /// <summary>
    /// 重试次数
    /// </summary>
    [JsonPropertyName("retryCount")]
    public int RetryCount { get; set; } = 0;

    /// <summary>
    /// 最大重试次数
    /// </summary>
    [JsonPropertyName("maxRetries")]
    public int MaxRetries { get; set; } = 3;

    /// <summary>
    /// NATS消息序列号
    /// </summary>
    [JsonPropertyName("sequence")]
    public ulong Sequence { get; set; }

    /// <summary>
    /// NATS流名称
    /// </summary>
    [JsonPropertyName("stream")]
    public string? Stream { get; set; }

    /// <summary>
    /// 消息大小（字节）
    /// </summary>
    [JsonPropertyName("size")]
    public int Size { get; set; }
}

/// <summary>
/// 泛型NATS消息实现
/// </summary>
/// <typeparam name="T">消息内容类型</typeparam>
public class NatsMessage<T> : NatsMessage, IMessage<T>
{
    /// <summary>
    /// 强类型消息内容
    /// </summary>
    [JsonPropertyName("typedPayload")]
    public new T? Payload { get; set; }

    /// <summary>
    /// 基类消息内容（用于序列化兼容）
    /// </summary>
    [JsonIgnore]
    object? IMessage.Payload => Payload;
}

/// <summary>
/// NATS消息构建器
/// </summary>
public class NatsMessageBuilder : IMessageBuilder
{
    private readonly NatsMessage _message = new();

    /// <inheritdoc />
    public IMessageBuilder WithType(string messageType)
    {
        _message.MessageType = messageType;
        return this;
    }

    /// <inheritdoc />
    public IMessageBuilder WithSender(string senderId)
    {
        _message.SenderId = senderId;
        return this;
    }

    /// <inheritdoc />
    public IMessageBuilder WithTarget(string targetId)
    {
        _message.TargetId = targetId;
        return this;
    }

    /// <inheritdoc />
    public IMessageBuilder WithPriority(MessagePriority priority)
    {
        _message.Priority = priority;
        return this;
    }

    /// <inheritdoc />
    public IMessageBuilder WithExpiration(DateTime expiresAt)
    {
        _message.ExpiresAt = expiresAt;
        return this;
    }

    /// <inheritdoc />
    public IMessageBuilder WithPayload(object payload)
    {
        _message.Payload = payload;
        return this;
    }

    /// <inheritdoc />
    public IMessageBuilder WithMetadata(string key, object value)
    {
        _message.MetadataDict[key] = value;
        return this;
    }

    /// <summary>
    /// 设置NATS主题
    /// </summary>
    /// <param name="subject">主题</param>
    /// <returns>消息构建器</returns>
    public NatsMessageBuilder WithSubject(string subject)
    {
        _message.Subject = subject;
        return this;
    }

    /// <summary>
    /// 设置回复主题
    /// </summary>
    /// <param name="replyTo">回复主题</param>
    /// <returns>消息构建器</returns>
    public NatsMessageBuilder WithReplyTo(string replyTo)
    {
        _message.ReplyTo = replyTo;
        return this;
    }

    /// <summary>
    /// 添加消息头
    /// </summary>
    /// <param name="key">头键</param>
    /// <param name="value">头值</param>
    /// <returns>消息构建器</returns>
    public NatsMessageBuilder WithHeader(string key, string value)
    {
        _message.Headers[key] = value;
        return this;
    }

    /// <summary>
    /// 设置是否需要确认
    /// </summary>
    /// <param name="requiresAck">是否需要确认</param>
    /// <returns>消息构建器</returns>
    public NatsMessageBuilder WithAck(bool requiresAck = true)
    {
        _message.RequiresAck = requiresAck;
        return this;
    }

    /// <inheritdoc />
    public IMessage Build()
    {
        return _message;
    }

    /// <inheritdoc />
    public IMessage<T> Build<T>()
    {
        return new NatsMessage<T>
        {
            MessageId = _message.MessageId,
            MessageType = _message.MessageType,
            SenderId = _message.SenderId,
            TargetId = _message.TargetId,
            CreatedAt = _message.CreatedAt,
            ExpiresAt = _message.ExpiresAt,
            Priority = _message.Priority,
            Payload = (T?)_message.Payload,
            MetadataDict = _message.MetadataDict,
            Subject = _message.Subject,
            ReplyTo = _message.ReplyTo,
            Headers = _message.Headers,
            RequiresAck = _message.RequiresAck,
            RetryCount = _message.RetryCount,
            MaxRetries = _message.MaxRetries
        };
    }
}
