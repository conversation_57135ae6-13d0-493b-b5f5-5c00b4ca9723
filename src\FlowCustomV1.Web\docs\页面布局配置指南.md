# 页面布局配置指南

## 📋 概述

为了解决不同页面表格高度不一致的问题，我们为每个页面单独设置了布局配置常量。这样可以根据每个页面的具体内容（是否有统计卡片、工具栏复杂度等）来精确调整表格高度。

## 🎯 设计原则

### 1. **页面独立配置**
- 每个页面在文件开头定义自己的 `PAGE_LAYOUT_CONFIG` 常量
- 根据页面具体内容调整参数
- 便于查找和维护

### 2. **简化配置结构**
所有页面使用简化的配置结构，只保留必要参数：

```tsx
// ========== 页面布局配置 ==========
const PAGE_LAYOUT_CONFIG = {
  // 表格滚动高度 - 根据页面类型调整
  tableScrollY: 'calc(100vh - 355px)' // 或 520px
};
```

### 3. **共享基础框架**
🟢🟡🔵 绿、黄、蓝三个框架参数在 `layout-config.css` 中统一配置，页面特定参数在各页面单独设置。

### 4. **统计卡片容器**
有统计卡片的页面需要添加橙色边框容器，便于调试和布局控制：

```tsx
{/* 统计卡片容器 */}
<div
  className="stats-cards-container"
  style={{
    marginBottom: '16px',
    border: '2px solid #ff9800', // 橙色调试边框
    borderRadius: '6px',
    padding: '16px',
    background: '#fff'
  }}
>
  {/* 统计卡片内容 */}
</div>
```

## 📊 页面分类和推荐配置

### 类型A：简单列表页面（无统计卡片）
**特点**：只有工具栏和表格
**推荐配置**：
```tsx
const PAGE_LAYOUT_CONFIG = {
  tableScrollY: 'calc(100vh - 355px)' // 只有工具栏的简单页面
};
```
**适用页面**：工作流列表

### 类型B：带统计卡片的页面
**特点**：有统计卡片容器 + 工具栏 + 表格
**推荐配置**：
```tsx
const PAGE_LAYOUT_CONFIG = {
  tableScrollY: 'calc(100vh - 520px)' // 有统计卡片容器 + 工具栏的页面
};
```
**适用页面**：执行监控、执行历史、集群节点、数据源管理、用户管理

## 🔧 实施步骤

### 1. 添加配置常量
在页面文件开头，import语句之后添加：

```tsx
import PageLayout from '@/components/Layout/PageLayout';

// ========== 页面布局配置 ==========
const PAGE_LAYOUT_CONFIG = {
  // 根据页面类型选择合适的配置
  tableScrollY: 'calc(100vh - 355px)', // 或 450px
  toolbarHeight: '60px',
  statsCardHeight: '120px', // 或 0px
  hasStatsCards: false, // 或 true
  hasComplexToolbar: true
};

const MyPage: React.FC = () => {
  // 页面组件代码
};
```

### 2. 使用配置常量
在ProTable的scroll属性中使用：

```tsx
<ProTable
  // ... 其他属性
  scroll={{
    x: 'max-content', // 或具体数值如 1200
    y: PAGE_LAYOUT_CONFIG.tableScrollY // 使用配置常量
  }}
/>
```

### 3. 调整和优化
根据实际显示效果微调 `tableScrollY` 的数值：
- 如果分页栏被遮挡，增加偏移量（如从355px改为380px）
- 如果空白空间太大，减少偏移量（如从450px改为420px）

## 📋 已配置页面清单

### ✅ 已完成配置
1. **工作流列表** (`WorkflowList.tsx`) - 355px偏移
2. **执行历史** (`ExecutionHistory.tsx`) - 520px偏移
3. **执行监控** (`ExecutionMonitor.tsx`) - 520px偏移
4. **集群节点** (`ClusterNodes.tsx`) - 520px偏移
5. **数据源管理** (`DataSources.tsx`) - 520px偏移
6. **用户管理** (`UserManagement.tsx`) - 520px偏移

### 🔄 待配置页面
根据需要为其他使用ProTable的页面添加类似配置。

## 🎯 配置原则

### 高度计算公式
```
表格滚动高度 = 100vh - (页面头部 + 工具栏 + 统计卡片 + 分页栏 + 边距)
```

### 推荐数值
- **无统计卡片页面**：`calc(100vh - 355px)`
- **有统计卡片页面**：`calc(100vh - 520px)`
- **复杂布局页面**：根据实际情况调整

### 调试技巧
1. 开启布局调试模式查看各区域边界
2. 逐步调整数值，每次调整20-30px
3. 确保分页栏完全可见且无多余空白

## 🔄 维护指南

### 新增页面时
1. 根据页面类型选择合适的配置模板
2. 添加配置常量到文件开头
3. 在ProTable中使用配置常量
4. 测试并微调数值

### 修改现有页面时
1. 只需修改对应页面的 `PAGE_LAYOUT_CONFIG`
2. 不影响其他页面的布局
3. 便于独立调试和优化

这个系统确保了每个页面都有最适合的表格高度，同时保持了配置的一致性和可维护性。
