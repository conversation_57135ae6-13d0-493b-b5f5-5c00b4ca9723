using FlowCustomV1.Core.Interfaces.Messaging;

namespace FlowCustomV1.Core.Models.Messages;

/// <summary>
/// 集群消息基类
/// 所有集群消息的基础类，确保类型安全和一致性
/// </summary>
public abstract class ClusterMessage
{
    /// <summary>
    /// 消息唯一标识符
    /// </summary>
    public string MessageId { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 消息类型标识符
    /// </summary>
    public abstract string MessageType { get; }

    /// <summary>
    /// 消息版本
    /// </summary>
    public string Version { get; set; } = "1.0";

    /// <summary>
    /// 发送者节点ID
    /// </summary>
    public string SenderId { get; set; } = string.Empty;

    /// <summary>
    /// 目标节点ID（可选，用于点对点消息）
    /// </summary>
    public string? TargetId { get; set; }

    /// <summary>
    /// 消息创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 消息时间戳 (向后兼容)
    /// </summary>
    public DateTime Timestamp 
    { 
        get => CreatedAt; 
        set => CreatedAt = value; 
    }

    /// <summary>
    /// 消息过期时间（可选）
    /// </summary>
    public DateTime? ExpiresAt { get; set; }

    /// <summary>
    /// 消息优先级
    /// </summary>
    public MessagePriority Priority { get; set; } = MessagePriority.Normal;

    /// <summary>
    /// 是否需要确认
    /// </summary>
    public bool RequiresAck { get; set; } = false;

    /// <summary>
    /// 关联ID（用于请求-响应模式）
    /// </summary>
    public string? CorrelationId { get; set; }

    /// <summary>
    /// 消息元数据
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();

    /// <summary>
    /// 检查消息是否已过期
    /// </summary>
    /// <returns>是否已过期</returns>
    public bool IsExpired()
    {
        return ExpiresAt.HasValue && DateTime.UtcNow > ExpiresAt.Value;
    }

    /// <summary>
    /// 设置消息过期时间
    /// </summary>
    /// <param name="timeToLive">生存时间</param>
    public void SetTimeToLive(TimeSpan timeToLive)
    {
        ExpiresAt = DateTime.UtcNow.Add(timeToLive);
    }

    /// <summary>
    /// 添加元数据
    /// </summary>
    /// <param name="key">键</param>
    /// <param name="value">值</param>
    public void AddMetadata(string key, object value)
    {
        Metadata[key] = value;
    }

    /// <summary>
    /// 获取元数据
    /// </summary>
    /// <typeparam name="T">值类型</typeparam>
    /// <param name="key">键</param>
    /// <returns>值</returns>
    public T? GetMetadata<T>(string key)
    {
        if (Metadata.TryGetValue(key, out var value) && value is T typedValue)
        {
            return typedValue;
        }
        return default;
    }

    /// <summary>
    /// 验证消息的有效性
    /// </summary>
    /// <returns>验证结果</returns>
    public virtual bool IsValid()
    {
        return !string.IsNullOrWhiteSpace(MessageId) &&
               !string.IsNullOrWhiteSpace(MessageType) &&
               !string.IsNullOrWhiteSpace(SenderId) &&
               !IsExpired();
    }

    /// <summary>
    /// 获取消息的字符串表示
    /// </summary>
    /// <returns>消息字符串</returns>
    public override string ToString()
    {
        return $"Message[{MessageType}] {MessageId} from {SenderId} at {CreatedAt:yyyy-MM-dd HH:mm:ss}";
    }
}



/// <summary>
/// 消息优先级扩展方法
/// </summary>
public static class MessagePriorityExtensions
{
    /// <summary>
    /// 获取优先级的数值权重
    /// </summary>
    /// <param name="priority">优先级</param>
    /// <returns>数值权重</returns>
    public static int GetWeight(this MessagePriority priority)
    {
        return priority switch
        {
            MessagePriority.Low => 1,
            MessagePriority.Normal => 5,
            MessagePriority.High => 10,
            MessagePriority.Critical => 20,
            _ => 1
        };
    }

    /// <summary>
    /// 获取优先级的描述
    /// </summary>
    /// <param name="priority">优先级</param>
    /// <returns>描述</returns>
    public static string GetDescription(this MessagePriority priority)
    {
        return priority switch
        {
            MessagePriority.Low => "低优先级",
            MessagePriority.Normal => "普通优先级",
            MessagePriority.High => "高优先级",
            MessagePriority.Critical => "紧急优先级",
            _ => "未知优先级"
        };
    }
}
