using System.ComponentModel.DataAnnotations;

namespace FlowCustomV1.Core.Models.Cluster;

/// <summary>
/// 健康状态信息
/// 记录节点的健康检查结果和历史数据
/// </summary>
public class HealthStatus
{
    /// <summary>
    /// 是否健康
    /// </summary>
    public bool IsHealthy { get; set; } = true;

    /// <summary>
    /// 健康评分 (0-100)
    /// </summary>
    [Range(0, 100)]
    public double HealthScore { get; set; } = 100.0;

    /// <summary>
    /// 最后健康检查时间
    /// </summary>
    public DateTime LastHealthCheckAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 健康检查间隔 (秒)
    /// </summary>
    public int HealthCheckIntervalSeconds { get; set; } = 30;

    /// <summary>
    /// 连续健康检查失败次数
    /// </summary>
    public int ConsecutiveFailures { get; set; } = 0;

    /// <summary>
    /// 最大允许连续失败次数
    /// </summary>
    public int MaxConsecutiveFailures { get; set; } = 3;

    /// <summary>
    /// 健康检查详情列表
    /// </summary>
    public List<HealthCheckDetail> CheckDetails { get; set; } = new();

    /// <summary>
    /// 健康问题列表
    /// </summary>
    public List<string> HealthIssues { get; set; } = new();

    /// <summary>
    /// 健康警告列表
    /// </summary>
    public List<string> HealthWarnings { get; set; } = new();

    /// <summary>
    /// 系统指标
    /// </summary>
    public SystemMetrics SystemMetrics { get; set; } = new();

    /// <summary>
    /// 服务状态
    /// </summary>
    public Dictionary<string, ServiceHealthStatus> ServiceStatuses { get; set; } = new();

    /// <summary>
    /// 依赖服务状态
    /// </summary>
    public Dictionary<string, DependencyHealthStatus> DependencyStatuses { get; set; } = new();

    /// <summary>
    /// 健康历史记录
    /// </summary>
    public List<HealthHistoryPoint> HealthHistory { get; set; } = new();

    /// <summary>
    /// 健康元数据
    /// </summary>
    public Dictionary<string, object> HealthMetadata { get; set; } = new();

    /// <summary>
    /// 添加健康检查详情
    /// </summary>
    /// <param name="detail">检查详情</param>
    public void AddCheckDetail(HealthCheckDetail detail)
    {
        CheckDetails.Add(detail);
    }

    /// <summary>
    /// 添加健康问题
    /// </summary>
    /// <param name="issue">健康问题</param>
    public void AddHealthIssue(string issue)
    {
        if (!string.IsNullOrWhiteSpace(issue) && !HealthIssues.Contains(issue))
        {
            HealthIssues.Add(issue);
        }
    }

    /// <summary>
    /// 添加健康警告
    /// </summary>
    /// <param name="warning">健康警告</param>
    public void AddHealthWarning(string warning)
    {
        if (!string.IsNullOrWhiteSpace(warning) && !HealthWarnings.Contains(warning))
        {
            HealthWarnings.Add(warning);
        }
    }

    /// <summary>
    /// 更新服务状态
    /// </summary>
    /// <param name="serviceName">服务名称</param>
    /// <param name="status">服务状态</param>
    public void UpdateServiceStatus(string serviceName, ServiceHealthStatus status)
    {
        ServiceStatuses[serviceName] = status;
    }

    /// <summary>
    /// 更新依赖服务状态
    /// </summary>
    /// <param name="dependencyName">依赖服务名称</param>
    /// <param name="status">依赖服务状态</param>
    public void UpdateDependencyStatus(string dependencyName, DependencyHealthStatus status)
    {
        DependencyStatuses[dependencyName] = status;
    }

    /// <summary>
    /// 计算综合健康评分
    /// </summary>
    /// <returns>健康评分 (0-100)</returns>
    public double CalculateHealthScore()
    {
        var score = 100.0;
        
        // 系统指标影响 (40%)
        if (SystemMetrics.CpuUsagePercentage > 90) score -= 15;
        else if (SystemMetrics.CpuUsagePercentage > 80) score -= 10;
        else if (SystemMetrics.CpuUsagePercentage > 70) score -= 5;
        
        if (SystemMetrics.MemoryUsagePercentage > 90) score -= 15;
        else if (SystemMetrics.MemoryUsagePercentage > 80) score -= 10;
        else if (SystemMetrics.MemoryUsagePercentage > 70) score -= 5;
        
        if (SystemMetrics.DiskUsagePercentage > 95) score -= 20;
        else if (SystemMetrics.DiskUsagePercentage > 90) score -= 10;
        else if (SystemMetrics.DiskUsagePercentage > 80) score -= 5;
        
        // 服务状态影响 (30%)
        var unhealthyServices = ServiceStatuses.Values.Count(s => !s.IsHealthy);
        if (unhealthyServices > 0)
        {
            score -= Math.Min(unhealthyServices * 10, 30);
        }
        
        // 依赖服务状态影响 (20%)
        var unhealthyDependencies = DependencyStatuses.Values.Count(d => !d.IsHealthy);
        if (unhealthyDependencies > 0)
        {
            score -= Math.Min(unhealthyDependencies * 5, 20);
        }
        
        // 连续失败影响 (10%)
        if (ConsecutiveFailures > 0)
        {
            score -= Math.Min(ConsecutiveFailures * 5, 10);
        }
        
        HealthScore = Math.Max(score, 0);
        IsHealthy = HealthScore >= 70; // 健康阈值
        
        return HealthScore;
    }

    /// <summary>
    /// 执行健康检查
    /// </summary>
    /// <returns>健康检查结果</returns>
    public bool PerformHealthCheck()
    {
        LastHealthCheckAt = DateTime.UtcNow;
        CheckDetails.Clear();
        HealthIssues.Clear();
        HealthWarnings.Clear();
        
        try
        {
            // 更新系统指标
            SystemMetrics.UpdateMetrics();
            
            // 检查系统资源
            CheckSystemResources();
            
            // 检查服务状态
            CheckServices();
            
            // 检查依赖服务
            CheckDependencies();
            
            // 计算健康评分
            CalculateHealthScore();
            
            // 记录健康历史
            AddHealthHistoryPoint();
            
            if (IsHealthy)
            {
                ConsecutiveFailures = 0;
            }
            else
            {
                ConsecutiveFailures++;
            }
            
            return IsHealthy;
        }
        catch (Exception ex)
        {
            AddHealthIssue($"Health check failed: {ex.Message}");
            ConsecutiveFailures++;
            IsHealthy = false;
            HealthScore = 0;
            return false;
        }
    }

    /// <summary>
    /// 检查系统资源
    /// </summary>
    private void CheckSystemResources()
    {
        if (SystemMetrics.CpuUsagePercentage > 95)
        {
            AddHealthIssue("CPU usage is critically high");
        }
        else if (SystemMetrics.CpuUsagePercentage > 85)
        {
            AddHealthWarning("CPU usage is high");
        }
        
        if (SystemMetrics.MemoryUsagePercentage > 95)
        {
            AddHealthIssue("Memory usage is critically high");
        }
        else if (SystemMetrics.MemoryUsagePercentage > 85)
        {
            AddHealthWarning("Memory usage is high");
        }
        
        if (SystemMetrics.DiskUsagePercentage > 98)
        {
            AddHealthIssue("Disk usage is critically high");
        }
        else if (SystemMetrics.DiskUsagePercentage > 90)
        {
            AddHealthWarning("Disk usage is high");
        }
    }

    /// <summary>
    /// 检查服务状态
    /// </summary>
    private void CheckServices()
    {
        foreach (var service in ServiceStatuses.Values)
        {
            if (!service.IsHealthy)
            {
                AddHealthIssue($"Service {service.ServiceName} is unhealthy: {service.ErrorMessage}");
            }
        }
    }

    /// <summary>
    /// 检查依赖服务
    /// </summary>
    private void CheckDependencies()
    {
        foreach (var dependency in DependencyStatuses.Values)
        {
            if (!dependency.IsHealthy)
            {
                AddHealthWarning($"Dependency {dependency.DependencyName} is unhealthy: {dependency.ErrorMessage}");
            }
        }
    }

    /// <summary>
    /// 添加健康历史点
    /// </summary>
    private void AddHealthHistoryPoint()
    {
        HealthHistory.Add(new HealthHistoryPoint
        {
            Timestamp = LastHealthCheckAt,
            IsHealthy = IsHealthy,
            HealthScore = HealthScore,
            IssueCount = HealthIssues.Count,
            WarningCount = HealthWarnings.Count
        });
        
        // 保持最近100个历史点
        if (HealthHistory.Count > 100)
        {
            HealthHistory.RemoveAt(0);
        }
    }

    /// <summary>
    /// 获取健康趋势
    /// </summary>
    /// <param name="minutes">分钟数</param>
    /// <returns>健康趋势 (正数表示改善，负数表示恶化)</returns>
    public double GetHealthTrend(int minutes = 10)
    {
        var cutoffTime = DateTime.UtcNow.AddMinutes(-minutes);
        var recentPoints = HealthHistory
            .Where(p => p.Timestamp >= cutoffTime)
            .OrderBy(p => p.Timestamp)
            .ToList();
            
        if (recentPoints.Count < 2)
            return 0;
            
        var firstScore = recentPoints.First().HealthScore;
        var lastScore = recentPoints.Last().HealthScore;
        
        return lastScore - firstScore;
    }

    /// <summary>
    /// 验证健康状态信息的有效性
    /// </summary>
    /// <returns>验证结果</returns>
    public bool IsValid()
    {
        return HealthScore >= 0 && HealthScore <= 100 &&
               HealthCheckIntervalSeconds > 0 &&
               ConsecutiveFailures >= 0 &&
               MaxConsecutiveFailures > 0;
    }

    /// <summary>
    /// 创建健康状态信息的深拷贝
    /// </summary>
    /// <returns>健康状态信息的深拷贝</returns>
    public HealthStatus Clone()
    {
        return new HealthStatus
        {
            IsHealthy = IsHealthy,
            HealthScore = HealthScore,
            LastHealthCheckAt = LastHealthCheckAt,
            HealthCheckIntervalSeconds = HealthCheckIntervalSeconds,
            ConsecutiveFailures = ConsecutiveFailures,
            MaxConsecutiveFailures = MaxConsecutiveFailures,
            CheckDetails = CheckDetails.Select(d => d.Clone()).ToList(),
            HealthIssues = new List<string>(HealthIssues),
            HealthWarnings = new List<string>(HealthWarnings),
            SystemMetrics = SystemMetrics.Clone(),
            ServiceStatuses = ServiceStatuses.ToDictionary(
                kvp => kvp.Key, 
                kvp => kvp.Value.Clone()),
            DependencyStatuses = DependencyStatuses.ToDictionary(
                kvp => kvp.Key, 
                kvp => kvp.Value.Clone()),
            HealthHistory = HealthHistory.Select(h => h.Clone()).ToList(),
            HealthMetadata = new Dictionary<string, object>(HealthMetadata)
        };
    }

    /// <summary>
    /// 获取健康状态的简要描述
    /// </summary>
    /// <returns>健康状态简要描述</returns>
    public override string ToString()
    {
        var status = IsHealthy ? "Healthy" : "Unhealthy";
        var issues = HealthIssues.Count > 0 ? $" Issues={HealthIssues.Count}" : "";
        var warnings = HealthWarnings.Count > 0 ? $" Warnings={HealthWarnings.Count}" : "";
        
        return $"Health[{status}] Score={HealthScore:F1}{issues}{warnings}";
    }
}

/// <summary>
/// 系统指标
/// </summary>
public class SystemMetrics
{
    /// <summary>
    /// CPU使用率 (0-100)
    /// </summary>
    public double CpuUsagePercentage { get; set; } = 0;

    /// <summary>
    /// 内存使用率 (0-100)
    /// </summary>
    public double MemoryUsagePercentage { get; set; } = 0;

    /// <summary>
    /// 磁盘使用率 (0-100)
    /// </summary>
    public double DiskUsagePercentage { get; set; } = 0;

    /// <summary>
    /// 网络延迟 (毫秒)
    /// </summary>
    public double NetworkLatencyMs { get; set; } = 0;

    /// <summary>
    /// 系统正常运行时间 (秒)
    /// </summary>
    public long SystemUptimeSeconds { get; set; } = 0;

    /// <summary>
    /// 更新系统指标
    /// </summary>
    public void UpdateMetrics()
    {
        // 这里应该实现实际的系统指标收集逻辑
        // 目前使用模拟数据
        var random = new Random();
        CpuUsagePercentage = random.NextDouble() * 100;
        MemoryUsagePercentage = random.NextDouble() * 100;
        DiskUsagePercentage = random.NextDouble() * 100;
        NetworkLatencyMs = random.NextDouble() * 100;
    }

    /// <summary>
    /// 创建系统指标的深拷贝
    /// </summary>
    /// <returns>系统指标的深拷贝</returns>
    public SystemMetrics Clone()
    {
        return new SystemMetrics
        {
            CpuUsagePercentage = CpuUsagePercentage,
            MemoryUsagePercentage = MemoryUsagePercentage,
            DiskUsagePercentage = DiskUsagePercentage,
            NetworkLatencyMs = NetworkLatencyMs,
            SystemUptimeSeconds = SystemUptimeSeconds
        };
    }
}

/// <summary>
/// 服务健康状态
/// </summary>
public class ServiceHealthStatus
{
    /// <summary>
    /// 服务名称
    /// </summary>
    public string ServiceName { get; set; } = string.Empty;

    /// <summary>
    /// 是否健康
    /// </summary>
    public bool IsHealthy { get; set; } = true;

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 最后检查时间
    /// </summary>
    public DateTime LastCheckTime { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 创建服务健康状态的深拷贝
    /// </summary>
    /// <returns>服务健康状态的深拷贝</returns>
    public ServiceHealthStatus Clone()
    {
        return new ServiceHealthStatus
        {
            ServiceName = ServiceName,
            IsHealthy = IsHealthy,
            ErrorMessage = ErrorMessage,
            LastCheckTime = LastCheckTime
        };
    }
}

/// <summary>
/// 依赖服务健康状态
/// </summary>
public class DependencyHealthStatus
{
    /// <summary>
    /// 依赖服务名称
    /// </summary>
    public string DependencyName { get; set; } = string.Empty;

    /// <summary>
    /// 是否健康
    /// </summary>
    public bool IsHealthy { get; set; } = true;

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 最后检查时间
    /// </summary>
    public DateTime LastCheckTime { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 创建依赖服务健康状态的深拷贝
    /// </summary>
    /// <returns>依赖服务健康状态的深拷贝</returns>
    public DependencyHealthStatus Clone()
    {
        return new DependencyHealthStatus
        {
            DependencyName = DependencyName,
            IsHealthy = IsHealthy,
            ErrorMessage = ErrorMessage,
            LastCheckTime = LastCheckTime
        };
    }
}

/// <summary>
/// 健康历史点
/// </summary>
public class HealthHistoryPoint
{
    /// <summary>
    /// 时间戳
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 是否健康
    /// </summary>
    public bool IsHealthy { get; set; } = true;

    /// <summary>
    /// 健康评分
    /// </summary>
    public double HealthScore { get; set; } = 100;

    /// <summary>
    /// 问题数量
    /// </summary>
    public int IssueCount { get; set; } = 0;

    /// <summary>
    /// 警告数量
    /// </summary>
    public int WarningCount { get; set; } = 0;

    /// <summary>
    /// 创建健康历史点的深拷贝
    /// </summary>
    /// <returns>健康历史点的深拷贝</returns>
    public HealthHistoryPoint Clone()
    {
        return new HealthHistoryPoint
        {
            Timestamp = Timestamp,
            IsHealthy = IsHealthy,
            HealthScore = HealthScore,
            IssueCount = IssueCount,
            WarningCount = WarningCount
        };
    }
}
