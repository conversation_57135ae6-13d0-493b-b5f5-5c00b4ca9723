using System.Text.Json.Serialization;
using FlowCustomV1.Core.Interfaces.Messaging;

namespace FlowCustomV1.Core.Models.Messaging;

/// <summary>
/// 工作流相关消息模型
/// 这是业务层的消息模型，不依赖具体的消息中间件实现
/// </summary>
public class WorkflowMessage : IMessage
{
    /// <summary>
    /// 消息唯一标识
    /// </summary>
    [JsonPropertyName("messageId")]
    public string MessageId { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 消息类型
    /// </summary>
    [JsonPropertyName("messageType")]
    public string MessageType { get; set; } = "WorkflowMessage";

    /// <summary>
    /// 发送者标识
    /// </summary>
    [JsonPropertyName("senderId")]
    public string SenderId { get; set; } = string.Empty;

    /// <summary>
    /// 目标标识
    /// </summary>
    [JsonPropertyName("targetId")]
    public string? TargetId { get; set; }

    /// <summary>
    /// 消息创建时间
    /// </summary>
    [JsonPropertyName("createdAt")]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 消息过期时间
    /// </summary>
    [JsonPropertyName("expiresAt")]
    public DateTime? ExpiresAt { get; set; }

    /// <summary>
    /// 消息优先级
    /// </summary>
    [JsonPropertyName("priority")]
    public MessagePriority Priority { get; set; } = MessagePriority.Normal;

    /// <summary>
    /// 消息内容（工作流相关数据）
    /// </summary>
    [JsonIgnore]
    public object? Payload => this;

    /// <summary>
    /// 消息元数据
    /// </summary>
    [JsonPropertyName("metadata")]
    public Dictionary<string, object> MetadataDict { get; set; } = new();

    /// <summary>
    /// 消息元数据（只读）
    /// </summary>
    [JsonIgnore]
    public IReadOnlyDictionary<string, object> Metadata => MetadataDict.AsReadOnly();

    /// <summary>
    /// 工作流ID
    /// </summary>
    [JsonPropertyName("workflowId")]
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 执行ID
    /// </summary>
    [JsonPropertyName("executionId")]
    public string? ExecutionId { get; set; }

    /// <summary>
    /// 节点ID（可选，用于节点级别的消息）
    /// </summary>
    [JsonPropertyName("nodeId")]
    public string? NodeId { get; set; }

    /// <summary>
    /// 工作流状态
    /// </summary>
    [JsonPropertyName("status")]
    public string? Status { get; set; }

    /// <summary>
    /// 执行上下文数据
    /// </summary>
    [JsonPropertyName("context")]
    public Dictionary<string, object> Context { get; set; } = new();

    /// <summary>
    /// 错误信息（如果有）
    /// </summary>
    [JsonPropertyName("error")]
    public string? Error { get; set; }

    /// <summary>
    /// 执行结果数据
    /// </summary>
    [JsonPropertyName("result")]
    public object? Result { get; set; }
}

/// <summary>
/// 节点心跳消息
/// </summary>
public class HeartbeatMessage : IMessage
{
    /// <summary>
    /// 消息唯一标识
    /// </summary>
    [JsonPropertyName("messageId")]
    public string MessageId { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 消息类型
    /// </summary>
    [JsonPropertyName("messageType")]
    public string MessageType { get; set; } = "HeartbeatMessage";

    /// <summary>
    /// 发送者标识
    /// </summary>
    [JsonPropertyName("senderId")]
    public string SenderId { get; set; } = string.Empty;

    /// <summary>
    /// 目标标识
    /// </summary>
    [JsonPropertyName("targetId")]
    public string? TargetId { get; set; }

    /// <summary>
    /// 消息创建时间
    /// </summary>
    [JsonPropertyName("createdAt")]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 消息过期时间
    /// </summary>
    [JsonPropertyName("expiresAt")]
    public DateTime? ExpiresAt { get; set; }

    /// <summary>
    /// 消息优先级
    /// </summary>
    [JsonPropertyName("priority")]
    public MessagePriority Priority { get; set; } = MessagePriority.Normal;

    /// <summary>
    /// 消息内容（心跳相关数据）
    /// </summary>
    [JsonIgnore]
    public object? Payload => this;

    /// <summary>
    /// 消息元数据
    /// </summary>
    [JsonPropertyName("metadata")]
    public Dictionary<string, object> MetadataDict { get; set; } = new();

    /// <summary>
    /// 消息元数据（只读）
    /// </summary>
    [JsonIgnore]
    public IReadOnlyDictionary<string, object> Metadata => MetadataDict.AsReadOnly();

    /// <summary>
    /// 节点ID
    /// </summary>
    [JsonPropertyName("nodeId")]
    public string NodeId { get; set; } = string.Empty;

    /// <summary>
    /// 节点角色
    /// </summary>
    [JsonPropertyName("nodeRole")]
    public string NodeRole { get; set; } = string.Empty;

    /// <summary>
    /// 节点状态
    /// </summary>
    [JsonPropertyName("nodeStatus")]
    public string NodeStatus { get; set; } = string.Empty;

    /// <summary>
    /// 节点负载信息
    /// </summary>
    [JsonPropertyName("loadInfo")]
    public NodeLoadInfo LoadInfo { get; set; } = new();

    /// <summary>
    /// 节点能力信息
    /// </summary>
    [JsonPropertyName("capabilities")]
    public List<string> Capabilities { get; set; } = new();
}

/// <summary>
/// 节点负载信息
/// </summary>
public class NodeLoadInfo
{
    /// <summary>
    /// CPU使用率 (0-100)
    /// </summary>
    [JsonPropertyName("cpuUsage")]
    public double CpuUsage { get; set; }

    /// <summary>
    /// 内存使用率 (0-100)
    /// </summary>
    [JsonPropertyName("memoryUsage")]
    public double MemoryUsage { get; set; }

    /// <summary>
    /// 当前活跃任务数
    /// </summary>
    [JsonPropertyName("activeTasks")]
    public int ActiveTasks { get; set; }

    /// <summary>
    /// 最大任务容量
    /// </summary>
    [JsonPropertyName("maxTasks")]
    public int MaxTasks { get; set; }

    /// <summary>
    /// 网络延迟 (毫秒)
    /// </summary>
    [JsonPropertyName("networkLatency")]
    public double NetworkLatency { get; set; }
}

/// <summary>
/// 任务分发消息
/// </summary>
public class TaskMessage : IMessage
{
    /// <summary>
    /// 消息唯一标识
    /// </summary>
    [JsonPropertyName("messageId")]
    public string MessageId { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 消息类型
    /// </summary>
    [JsonPropertyName("messageType")]
    public string MessageType { get; set; } = "TaskMessage";

    /// <summary>
    /// 发送者标识
    /// </summary>
    [JsonPropertyName("senderId")]
    public string SenderId { get; set; } = string.Empty;

    /// <summary>
    /// 目标标识
    /// </summary>
    [JsonPropertyName("targetId")]
    public string? TargetId { get; set; }

    /// <summary>
    /// 消息创建时间
    /// </summary>
    [JsonPropertyName("createdAt")]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 消息过期时间
    /// </summary>
    [JsonPropertyName("expiresAt")]
    public DateTime? ExpiresAt { get; set; }

    /// <summary>
    /// 消息优先级
    /// </summary>
    [JsonPropertyName("priority")]
    public MessagePriority Priority { get; set; } = MessagePriority.Normal;

    /// <summary>
    /// 消息内容（任务相关数据）
    /// </summary>
    [JsonIgnore]
    public object? Payload => this;

    /// <summary>
    /// 消息元数据
    /// </summary>
    [JsonPropertyName("metadata")]
    public Dictionary<string, object> MetadataDict { get; set; } = new();

    /// <summary>
    /// 消息元数据（只读）
    /// </summary>
    [JsonIgnore]
    public IReadOnlyDictionary<string, object> Metadata => MetadataDict.AsReadOnly();

    /// <summary>
    /// 任务ID
    /// </summary>
    [JsonPropertyName("taskId")]
    public string TaskId { get; set; } = string.Empty;

    /// <summary>
    /// 工作流ID
    /// </summary>
    [JsonPropertyName("workflowId")]
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 执行ID
    /// </summary>
    [JsonPropertyName("executionId")]
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 节点ID
    /// </summary>
    [JsonPropertyName("nodeId")]
    public string NodeId { get; set; } = string.Empty;

    /// <summary>
    /// 任务类型
    /// </summary>
    [JsonPropertyName("taskType")]
    public string TaskType { get; set; } = string.Empty;

    /// <summary>
    /// 任务配置
    /// </summary>
    [JsonPropertyName("taskConfig")]
    public Dictionary<string, object> TaskConfig { get; set; } = new();

    /// <summary>
    /// 输入数据
    /// </summary>
    [JsonPropertyName("inputData")]
    public Dictionary<string, object> InputData { get; set; } = new();

    /// <summary>
    /// 预期的目标节点角色
    /// </summary>
    [JsonPropertyName("targetRole")]
    public string? TargetRole { get; set; }
}
