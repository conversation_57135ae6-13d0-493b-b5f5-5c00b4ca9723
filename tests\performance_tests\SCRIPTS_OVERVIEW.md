# FlowCustomV1 性能测试脚本清单

## 📁 文件结构

```
performance_tests/
├── README.md                      # 详细使用指南
├── SCRIPTS_OVERVIEW.md           # 脚本清单（本文件）
├── run_tests.py                   # 🎯 主入口脚本
├── test_config.py                 # ⚙️ 配置管理
├── test_reporter.py               # 📊 报告生成器
├── quick_performance_test.py      # ⚡ 快速性能验证
├── performance_test.py            # 📈 综合性能测试
├── performance_analysis.py        # 🔍 深度性能分析
├── infrastructure_stress_test.py  # 🔥 基础设施压力测试
└── extreme_stress_test.py         # 💥 极限压力测试
```

## 🎯 核心脚本

### `run_tests.py` - 主入口脚本
**作用**：统一管理和执行所有性能测试
- 🎮 交互式菜单界面
- 📋 测试套件管理
- ⏱️ 执行时间统计
- 📊 成功率统计

**使用方式**：
```bash
# 交互式运行
python tests/performance_tests/run_tests.py

# 命令行运行
python tests/performance_tests/run_tests.py quick
python tests/performance_tests/run_tests.py daily
python tests/performance_tests/run_tests.py all
```

### `test_config.py` - 配置管理
**作用**：统一管理所有测试配置参数
- 🔧 基础配置（URL、数据库连接）
- 📊 性能基准定义
- 🎯 测试参数配置
- 📈 评级标准定义

### `test_reporter.py` - 报告生成器
**作用**：收集、分析和生成性能测试报告
- 📊 结果收集和分析
- 📋 控制台报告输出
- 💾 JSON格式报告保存
- 💡 优化建议生成

## ⚡ 测试脚本

### 1. `quick_performance_test.py` - 快速验证
- **目标**：30秒内完成基本性能检查
- **场景**：开发过程中快速验证、CI/CD流水线
- **测试内容**：
  - cluster_nodes端点响应时间
  - executor_capacity端点对比
  - 基本可用性验证

### 2. `performance_test.py` - 综合测试
- **目标**：2-3分钟内完成全面性能评估
- **场景**：日常性能监控、版本发布前验证
- **测试内容**：
  - 所有API端点性能测试
  - 单请求和并发性能测试
  - 系统资源监控
  - 详细性能统计

### 3. `performance_analysis.py` - 深度分析
- **目标**：1-2分钟内完成性能问题诊断
- **场景**：性能问题排查、优化效果验证
- **测试内容**：
  - 请求时间详细分解
  - 多次测试对比分析
  - 性能瓶颈识别
  - 其他端点对比

### 4. `infrastructure_stress_test.py` - 基础设施压力测试
- **目标**：3-5分钟内完成基础设施性能评估
- **场景**：基础设施性能评估、容量规划
- **测试内容**：
  - NATS服务器性能测试（5-50并发）
  - MySQL数据库性能测试（5-20并发）
  - 系统资源使用监控
  - 中等强度压力测试

### 5. `extreme_stress_test.py` - 极限压力测试
- **目标**：5-10分钟内找到系统性能极限
- **场景**：系统极限评估、崩溃点分析
- **测试内容**：
  - NATS极限负载测试（100-1000并发）
  - MySQL极限负载测试（50-500并发）
  - 系统崩溃点测试
  - 性能边界分析

## 🎮 测试套件

### `daily` - 日常开发套件
**包含**：quick + comprehensive
**时间**：约3分钟
**用途**：日常开发使用

### `diagnosis` - 问题诊断套件
**包含**：analysis + comprehensive
**时间**：约4分钟
**用途**：性能问题排查

### `capacity` - 容量评估套件
**包含**：infrastructure + extreme
**时间**：约10分钟
**用途**：容量规划评估

### `all` - 完整测试套件
**包含**：所有测试脚本
**时间**：约15分钟
**用途**：全面性能评估

## 📊 性能基准

### API性能基准
- 🟢 **优秀**：< 50ms
- 🟡 **良好**：< 200ms
- 🟠 **一般**：< 1000ms
- 🔴 **较差**：> 1000ms

### 吞吐量基准
- **API**: 100+ req/s (优秀), 50+ req/s (良好)
- **NATS**: 200+ req/s (优秀), 100+ req/s (良好)
- **MySQL**: 1000+ queries/s (优秀), 500+ queries/s (良好)

### 系统极限基准
- **最大稳定负载**：1300 并发请求
- **系统崩溃点**：1500 并发请求
- **警戒阈值**：1000 并发请求

## 🚀 快速开始

### 1. 环境准备
```bash
# 安装依赖
pip install requests mysql-connector-python

# 确保服务运行
docker-compose -f docker/development/docker-compose.yml up -d
```

### 2. 快速测试
```bash
# 快速验证系统性能
python tests/performance_tests/run_tests.py quick

# 或直接运行
python tests/performance_tests/quick_performance_test.py
```

### 3. 日常开发测试
```bash
# 运行日常测试套件
python tests/performance_tests/run_tests.py daily
```

### 4. 完整性能评估
```bash
# 运行完整测试套件
python tests/performance_tests/run_tests.py all
```

## ⚠️ 注意事项

### 测试环境
- 确保FlowCustomV1系统正在运行
- 建议在专用测试环境中运行压力测试
- 极限测试可能影响系统稳定性

### 执行建议
- 日常开发：使用 `quick` 或 `daily` 套件
- 问题排查：使用 `diagnosis` 套件
- 容量规划：使用 `capacity` 套件
- 全面评估：使用 `all` 套件

### 结果解读
- 关注性能趋势比绝对数值更重要
- 定期执行测试以监控性能退化
- 结合系统资源使用情况分析结果

---

**最后更新**：2025-09-08
**版本**：FlowCustomV1 v0.0.1.0
