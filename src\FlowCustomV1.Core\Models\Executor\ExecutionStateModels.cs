using FlowCustomV1.Core.Models.Workflow;
using System.Text.Json.Serialization;

namespace FlowCustomV1.Core.Models.Executor;

/// <summary>
/// 执行状态信息
/// </summary>
public class ExecutionStateInfo
{
    /// <summary>
    /// 执行ID
    /// </summary>
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 工作流ID
    /// </summary>
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 当前状态
    /// </summary>
    public WorkflowExecutionState State { get; set; }

    /// <summary>
    /// 执行节点ID
    /// </summary>
    public string ExecutorNodeId { get; set; } = string.Empty;

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime StartedAt { get; set; }

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 当前执行的节点ID
    /// </summary>
    public string? CurrentNodeId { get; set; }

    /// <summary>
    /// 已完成的节点数
    /// </summary>
    public int CompletedNodes { get; set; }

    /// <summary>
    /// 总节点数
    /// </summary>
    public int TotalNodes { get; set; }

    /// <summary>
    /// 执行进度（0-100）
    /// </summary>
    public double Progress => TotalNodes > 0 ? (double)CompletedNodes / TotalNodes * 100 : 0;

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 节点状态映射
    /// </summary>
    public Dictionary<string, NodeExecutionState> NodeStates { get; set; } = new();

    /// <summary>
    /// 执行上下文数据
    /// </summary>
    public Dictionary<string, object> ContextData { get; set; } = new();
}

/// <summary>
/// 执行上下文快照
/// </summary>
public class ExecutionContextSnapshot
{
    /// <summary>
    /// 执行ID
    /// </summary>
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 工作流ID
    /// </summary>
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 快照时间
    /// </summary>
    public DateTime SnapshotTime { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 执行状态
    /// </summary>
    public WorkflowExecutionState State { get; set; }

    /// <summary>
    /// 工作流定义
    /// </summary>
    public WorkflowDefinition WorkflowDefinition { get; set; } = new();

    /// <summary>
    /// 输入数据
    /// </summary>
    public Dictionary<string, object> InputData { get; set; } = new();

    /// <summary>
    /// 工作流上下文数据
    /// </summary>
    public Dictionary<string, object> WorkflowData { get; set; } = new();

    /// <summary>
    /// 节点状态映射
    /// </summary>
    public Dictionary<string, NodeExecutionState> NodeStates { get; set; } = new();

    /// <summary>
    /// 节点执行结果映射
    /// </summary>
    public Dictionary<string, NodeExecutionResult> NodeResults { get; set; } = new();

    /// <summary>
    /// 执行统计信息
    /// </summary>
    public WorkflowExecutionStats Stats { get; set; } = new();

    /// <summary>
    /// 执行元数据
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// 执行迁移上下文
/// </summary>
public class ExecutionMigrationContext
{
    /// <summary>
    /// 执行ID
    /// </summary>
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 源节点ID
    /// </summary>
    public string SourceNodeId { get; set; } = string.Empty;

    /// <summary>
    /// 目标节点ID
    /// </summary>
    public string TargetNodeId { get; set; } = string.Empty;

    /// <summary>
    /// 迁移时间
    /// </summary>
    public DateTime MigrationTime { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 迁移原因
    /// </summary>
    public string Reason { get; set; } = string.Empty;

    /// <summary>
    /// 执行上下文快照
    /// </summary>
    public ExecutionContextSnapshot ContextSnapshot { get; set; } = new();

    /// <summary>
    /// 迁移状态
    /// </summary>
    public MigrationStatus Status { get; set; } = MigrationStatus.Pending;

    /// <summary>
    /// 迁移错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 迁移元数据
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// 状态同步统计
/// </summary>
public class StateSyncStatistics
{
    /// <summary>
    /// 统计开始时间
    /// </summary>
    public DateTime StartTime { get; set; }

    /// <summary>
    /// 统计结束时间
    /// </summary>
    public DateTime EndTime { get; set; }

    /// <summary>
    /// 总同步次数
    /// </summary>
    public long TotalSyncs { get; set; }

    /// <summary>
    /// 成功同步次数
    /// </summary>
    public long SuccessfulSyncs { get; set; }

    /// <summary>
    /// 失败同步次数
    /// </summary>
    public long FailedSyncs { get; set; }

    /// <summary>
    /// 平均同步时间（毫秒）
    /// </summary>
    public double AverageSyncTimeMs { get; set; }

    /// <summary>
    /// 最大同步时间（毫秒）
    /// </summary>
    public long MaxSyncTimeMs { get; set; }

    /// <summary>
    /// 最小同步时间（毫秒）
    /// </summary>
    public long MinSyncTimeMs { get; set; }

    /// <summary>
    /// 同步成功率（0-100）
    /// </summary>
    public double SuccessRate => TotalSyncs > 0 ? (double)SuccessfulSyncs / TotalSyncs * 100 : 0;

    /// <summary>
    /// 按同步类型分组的统计
    /// </summary>
    public Dictionary<StateSyncType, SyncTypeStatistics> SyncTypeStats { get; set; } = new();
}

/// <summary>
/// 同步类型统计
/// </summary>
public class SyncTypeStatistics
{
    /// <summary>
    /// 同步类型
    /// </summary>
    public StateSyncType SyncType { get; set; }

    /// <summary>
    /// 同步次数
    /// </summary>
    public long Count { get; set; }

    /// <summary>
    /// 成功次数
    /// </summary>
    public long SuccessCount { get; set; }

    /// <summary>
    /// 失败次数
    /// </summary>
    public long FailureCount { get; set; }

    /// <summary>
    /// 平均同步时间（毫秒）
    /// </summary>
    public double AverageTimeMs { get; set; }

    /// <summary>
    /// 成功率（0-100）
    /// </summary>
    public double SuccessRate => Count > 0 ? (double)SuccessCount / Count * 100 : 0;
}

/// <summary>
/// 状态同步类型
/// </summary>
[JsonConverter(typeof(JsonStringEnumConverter))]
public enum StateSyncType
{
    /// <summary>
    /// 执行状态同步
    /// </summary>
    ExecutionState,

    /// <summary>
    /// 节点状态同步
    /// </summary>
    NodeState,

    /// <summary>
    /// 执行结果同步
    /// </summary>
    ExecutionResult,

    /// <summary>
    /// 执行上下文同步
    /// </summary>
    ExecutionContext,

    /// <summary>
    /// 事件广播
    /// </summary>
    EventBroadcast
}

/// <summary>
/// 迁移状态
/// </summary>
[JsonConverter(typeof(JsonStringEnumConverter))]
public enum MigrationStatus
{
    /// <summary>
    /// 等待中
    /// </summary>
    Pending,

    /// <summary>
    /// 进行中
    /// </summary>
    InProgress,

    /// <summary>
    /// 已完成
    /// </summary>
    Completed,

    /// <summary>
    /// 失败
    /// </summary>
    Failed,

    /// <summary>
    /// 已取消
    /// </summary>
    Cancelled
}
