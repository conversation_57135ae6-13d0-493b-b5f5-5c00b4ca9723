using FlowCustomV1.Core.Models.Cluster;

namespace FlowCustomV1.Core.Interfaces.Cluster;

/// <summary>
/// 节点角色管理器接口
/// 负责节点角色的动态管理和切换
/// </summary>
public interface INodeRoleManager
{
    /// <summary>
    /// 当前节点的角色配置
    /// </summary>
    NodeRoleConfiguration CurrentConfiguration { get; }

    /// <summary>
    /// 当前节点ID
    /// </summary>
    string NodeId { get; }

    /// <summary>
    /// 角色变更事件
    /// </summary>
    event EventHandler<NodeRoleChangedEventArgs>? RoleChanged;

    /// <summary>
    /// 初始化角色管理器
    /// </summary>
    /// <param name="nodeId">节点ID</param>
    /// <param name="configuration">初始角色配置</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>初始化任务</returns>
    Task InitializeAsync(string nodeId, NodeRoleConfiguration configuration, CancellationToken cancellationToken = default);

    /// <summary>
    /// 检查节点是否支持指定角色
    /// </summary>
    /// <param name="role">目标角色</param>
    /// <returns>是否支持</returns>
    bool SupportsRole(NodeRole role);

    /// <summary>
    /// 获取当前激活的角色列表
    /// </summary>
    /// <returns>激活的角色列表</returns>
    IEnumerable<NodeRole> GetActiveRoles();

    /// <summary>
    /// 获取按优先级排序的角色列表
    /// </summary>
    /// <returns>按优先级排序的角色列表</returns>
    IEnumerable<NodeRole> GetRolesByPriority();

    /// <summary>
    /// 添加角色
    /// </summary>
    /// <param name="role">要添加的角色</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>添加任务</returns>
    Task<bool> AddRoleAsync(NodeRole role, CancellationToken cancellationToken = default);

    /// <summary>
    /// 移除角色
    /// </summary>
    /// <param name="role">要移除的角色</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>移除任务</returns>
    Task<bool> RemoveRoleAsync(NodeRole role, CancellationToken cancellationToken = default);

    /// <summary>
    /// 切换到指定角色配置
    /// </summary>
    /// <param name="newConfiguration">新的角色配置</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>切换任务</returns>
    Task<bool> SwitchToConfigurationAsync(NodeRoleConfiguration newConfiguration, CancellationToken cancellationToken = default);

    /// <summary>
    /// 启用指定角色的服务
    /// </summary>
    /// <param name="role">目标角色</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>启用任务</returns>
    Task<bool> EnableRoleServiceAsync(NodeRole role, CancellationToken cancellationToken = default);

    /// <summary>
    /// 禁用指定角色的服务
    /// </summary>
    /// <param name="role">目标角色</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>禁用任务</returns>
    Task<bool> DisableRoleServiceAsync(NodeRole role, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取角色服务状态
    /// </summary>
    /// <param name="role">目标角色</param>
    /// <returns>服务状态</returns>
    RoleServiceStatus GetRoleServiceStatus(NodeRole role);

    /// <summary>
    /// 获取所有角色的服务状态
    /// </summary>
    /// <returns>角色服务状态字典</returns>
    Dictionary<NodeRole, RoleServiceStatus> GetAllRoleServiceStatuses();

    /// <summary>
    /// 验证角色配置的有效性
    /// </summary>
    /// <param name="configuration">要验证的配置</param>
    /// <returns>验证结果</returns>
    ValidationResult ValidateConfiguration(NodeRoleConfiguration configuration);

    /// <summary>
    /// 获取推荐的角色配置
    /// </summary>
    /// <param name="nodeCount">集群节点数量</param>
    /// <param name="expectedLoad">预期负载</param>
    /// <returns>推荐的角色配置</returns>
    NodeRoleConfiguration GetRecommendedConfiguration(int nodeCount, int expectedLoad = 100);
}

/// <summary>
/// 节点角色变更事件参数
/// </summary>
public class NodeRoleChangedEventArgs : EventArgs
{
    /// <summary>
    /// 节点ID
    /// </summary>
    public string NodeId { get; set; } = string.Empty;

    /// <summary>
    /// 变更类型
    /// </summary>
    public RoleChangeType ChangeType { get; set; }

    /// <summary>
    /// 变更的角色
    /// </summary>
    public NodeRole Role { get; set; }

    /// <summary>
    /// 旧的角色配置
    /// </summary>
    public NodeRoleConfiguration? OldConfiguration { get; set; }

    /// <summary>
    /// 新的角色配置
    /// </summary>
    public NodeRoleConfiguration? NewConfiguration { get; set; }

    /// <summary>
    /// 变更时间
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 变更原因
    /// </summary>
    public string? Reason { get; set; }
}

/// <summary>
/// 角色变更类型
/// </summary>
public enum RoleChangeType
{
    /// <summary>
    /// 添加角色
    /// </summary>
    Added,

    /// <summary>
    /// 移除角色
    /// </summary>
    Removed,

    /// <summary>
    /// 角色配置更新
    /// </summary>
    Updated,

    /// <summary>
    /// 完全切换配置
    /// </summary>
    Switched
}

/// <summary>
/// 角色服务状态
/// </summary>
public enum RoleServiceStatus
{
    /// <summary>
    /// 未知状态
    /// </summary>
    Unknown,

    /// <summary>
    /// 未启用
    /// </summary>
    Disabled,

    /// <summary>
    /// 正在启动
    /// </summary>
    Starting,

    /// <summary>
    /// 运行中
    /// </summary>
    Running,

    /// <summary>
    /// 正在停止
    /// </summary>
    Stopping,

    /// <summary>
    /// 已停止
    /// </summary>
    Stopped,

    /// <summary>
    /// 错误状态
    /// </summary>
    Error
}
