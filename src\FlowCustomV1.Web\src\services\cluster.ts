import { request } from './api';
import type { NodeInfo, ClusterStats } from '@/types/api';

// 集群管理 API
export const clusterApi = {
  // 获取集群概览
  getClusterOverview: (): Promise<{
    clusterName: string;
    totalNodes: number;
    onlineNodes: number;
    systemHealth: 'Healthy' | 'Warning' | 'Critical';
    lastUpdated: string;
  }> =>
    request.get('/cluster/overview'),

  // 获取集群统计信息
  getClusterStats: (): Promise<ClusterStats> =>
    request.get('/cluster/stats'),

  // 获取集群健康状态
  getClusterHealth: (): Promise<{
    status: 'Healthy' | 'Warning' | 'Critical';
    checks: Array<{
      name: string;
      status: 'Pass' | 'Fail' | 'Warning';
      message: string;
      lastCheck: string;
    }>;
  }> =>
    request.get('/cluster/health'),

  // 获取所有节点信息
  getAllNodes: (): Promise<NodeInfo[]> =>
    request.get('/cluster/nodes'),

  // 获取指定节点信息
  getNode: (nodeId: string): Promise<NodeInfo> =>
    request.get(`/cluster/nodes/${nodeId}`),

  // 按角色查询节点
  getNodesByRole: (role: string): Promise<NodeInfo[]> =>
    request.get(`/cluster/nodes/role/${role}`),

  // 获取当前节点信息
  getCurrentNode: (): Promise<NodeInfo> =>
    request.get('/cluster/nodes/current'),

  // 获取集群拓扑
  getClusterTopology: (): Promise<{
    clusterName: string;
    nodes: NodeInfo[];
    nodeConnections: Array<{
      sourceNodeId: string;
      targetNodeId: string;
      connectionType: string;
      status: 'Connected' | 'Disconnected';
      latency?: number;
    }>;
    lastUpdatedAt: string;
    topologyVersion: string;
  }> =>
    request.get('/cluster/topology'),

  // 节点操作
  restartNode: (nodeId: string): Promise<void> =>
    request.post(`/cluster/nodes/${nodeId}/restart`),

  shutdownNode: (nodeId: string): Promise<void> =>
    request.post(`/cluster/nodes/${nodeId}/shutdown`),

  // 获取节点日志
  getNodeLogs: (nodeId: string, lines?: number): Promise<string[]> =>
    request.get(`/cluster/nodes/${nodeId}/logs`, { params: { lines } }),

  // 获取节点性能指标
  getNodeMetrics: (nodeId: string, timeRange?: string): Promise<{
    cpu: Array<{ timestamp: string; value: number }>;
    memory: Array<{ timestamp: string; value: number }>;
    disk: Array<{ timestamp: string; value: number }>;
    network: Array<{ timestamp: string; value: number }>;
  }> =>
    request.get(`/cluster/nodes/${nodeId}/metrics`, { params: { timeRange } }),

  // 集群配置管理
  getClusterConfig: (): Promise<Record<string, any>> =>
    request.get('/cluster/config'),

  updateClusterConfig: (config: Record<string, any>): Promise<void> =>
    request.put('/cluster/config', config),

  // 集群备份和恢复
  createBackup: (): Promise<{ backupId: string; createdAt: string }> =>
    request.post('/cluster/backup'),

  getBackups: (): Promise<Array<{
    backupId: string;
    createdAt: string;
    size: number;
    description?: string;
  }>> =>
    request.get('/cluster/backups'),

  restoreBackup: (backupId: string): Promise<void> =>
    request.post(`/cluster/backup/${backupId}/restore`),

  deleteBackup: (backupId: string): Promise<void> =>
    request.delete(`/cluster/backup/${backupId}`),
};
