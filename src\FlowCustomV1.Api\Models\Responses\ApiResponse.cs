using System.Text.Json.Serialization;

namespace FlowCustomV1.Api.Models.Responses;

/// <summary>
/// 通用API响应模型
/// </summary>
public class ApiResponse
{
    /// <summary>
    /// 是否成功
    /// </summary>
    [JsonPropertyName("success")]
    public bool Success { get; set; }

    /// <summary>
    /// 响应消息
    /// </summary>
    [JsonPropertyName("message")]
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 错误代码
    /// </summary>
    [JsonPropertyName("errorCode")]
    public string? ErrorCode { get; set; }

    /// <summary>
    /// 时间戳
    /// </summary>
    [JsonPropertyName("timestamp")]
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 请求ID（用于追踪）
    /// </summary>
    [JsonPropertyName("requestId")]
    public string? RequestId { get; set; }

    /// <summary>
    /// 创建成功响应
    /// </summary>
    /// <param name="message">成功消息</param>
    /// <returns>成功响应</returns>
    public static ApiResponse Ok(string message = "Operation completed successfully")
    {
        return new ApiResponse
        {
            Success = true,
            Message = message
        };
    }

    /// <summary>
    /// 创建错误响应
    /// </summary>
    /// <param name="message">错误消息</param>
    /// <param name="errorCode">错误代码</param>
    /// <returns>错误响应</returns>
    public static ApiResponse Error(string message, string? errorCode = null)
    {
        return new ApiResponse
        {
            Success = false,
            Message = message,
            ErrorCode = errorCode
        };
    }
}

/// <summary>
/// 带数据的API响应模型
/// </summary>
/// <typeparam name="T">数据类型</typeparam>
public class ApiResponse<T> : ApiResponse
{
    /// <summary>
    /// 响应数据
    /// </summary>
    [JsonPropertyName("data")]
    public T? Data { get; set; }

    /// <summary>
    /// 创建成功响应
    /// </summary>
    /// <param name="data">响应数据</param>
    /// <param name="message">成功消息</param>
    /// <returns>成功响应</returns>
    public static ApiResponse<T> CreateSuccess(T data, string message = "Operation completed successfully")
    {
        return new ApiResponse<T>
        {
            Success = true,
            Message = message,
            Data = data
        };
    }

    /// <summary>
    /// 创建错误响应
    /// </summary>
    /// <param name="message">错误消息</param>
    /// <param name="data">错误数据</param>
    /// <param name="errorCode">错误代码</param>
    /// <returns>错误响应</returns>
    public static ApiResponse<T> Error(string message, T? data = default, string? errorCode = null)
    {
        return new ApiResponse<T>
        {
            Success = false,
            Message = message,
            Data = data,
            ErrorCode = errorCode
        };
    }
}

/// <summary>
/// 分页响应模型
/// </summary>
/// <typeparam name="T">数据类型</typeparam>
public class PagedApiResponse<T> : ApiResponse<IEnumerable<T>>
{
    /// <summary>
    /// 分页信息
    /// </summary>
    [JsonPropertyName("pagination")]
    public PaginationInfo Pagination { get; set; } = new();

    /// <summary>
    /// 创建分页成功响应
    /// </summary>
    /// <param name="data">响应数据</param>
    /// <param name="pagination">分页信息</param>
    /// <param name="message">成功消息</param>
    /// <returns>分页成功响应</returns>
    public static PagedApiResponse<T> CreateSuccess(IEnumerable<T> data, PaginationInfo pagination, string message = "Operation completed successfully")
    {
        return new PagedApiResponse<T>
        {
            Success = true,
            Message = message,
            Data = data,
            Pagination = pagination
        };
    }

    /// <summary>
    /// 创建分页错误响应
    /// </summary>
    /// <param name="message">错误消息</param>
    /// <param name="errorCode">错误代码</param>
    /// <returns>分页错误响应</returns>
    public static PagedApiResponse<T> Error(string message, string? errorCode = null)
    {
        return new PagedApiResponse<T>
        {
            Success = false,
            Message = message,
            ErrorCode = errorCode,
            Data = Enumerable.Empty<T>(),
            Pagination = new PaginationInfo()
        };
    }
}

/// <summary>
/// 分页信息
/// </summary>
public class PaginationInfo
{
    /// <summary>
    /// 当前页码（从1开始）
    /// </summary>
    [JsonPropertyName("currentPage")]
    public int CurrentPage { get; set; } = 1;

    /// <summary>
    /// 每页大小
    /// </summary>
    [JsonPropertyName("pageSize")]
    public int PageSize { get; set; } = 20;

    /// <summary>
    /// 总记录数
    /// </summary>
    [JsonPropertyName("totalCount")]
    public long TotalCount { get; set; }

    /// <summary>
    /// 总页数
    /// </summary>
    [JsonPropertyName("totalPages")]
    public int TotalPages => PageSize > 0 ? (int)Math.Ceiling((double)TotalCount / PageSize) : 0;

    /// <summary>
    /// 是否有上一页
    /// </summary>
    [JsonPropertyName("hasPreviousPage")]
    public bool HasPreviousPage => CurrentPage > 1;

    /// <summary>
    /// 是否有下一页
    /// </summary>
    [JsonPropertyName("hasNextPage")]
    public bool HasNextPage => CurrentPage < TotalPages;

    /// <summary>
    /// 当前页起始记录索引（从0开始）
    /// </summary>
    [JsonPropertyName("startIndex")]
    public long StartIndex => (CurrentPage - 1) * PageSize;

    /// <summary>
    /// 当前页结束记录索引（从0开始）
    /// </summary>
    [JsonPropertyName("endIndex")]
    public long EndIndex => Math.Min(StartIndex + PageSize - 1, TotalCount - 1);
}

/// <summary>
/// 批量操作响应模型
/// </summary>
/// <typeparam name="T">数据类型</typeparam>
public class BatchApiResponse<T> : ApiResponse<IEnumerable<T>>
{
    /// <summary>
    /// 批量操作信息
    /// </summary>
    [JsonPropertyName("batchInfo")]
    public BatchOperationInfo BatchInfo { get; set; } = new();

    /// <summary>
    /// 创建批量成功响应
    /// </summary>
    /// <param name="data">响应数据</param>
    /// <param name="batchInfo">批量操作信息</param>
    /// <param name="message">成功消息</param>
    /// <returns>批量成功响应</returns>
    public static BatchApiResponse<T> CreateSuccess(IEnumerable<T> data, BatchOperationInfo batchInfo, string message = "Batch operation completed")
    {
        return new BatchApiResponse<T>
        {
            Success = true,
            Message = message,
            Data = data,
            BatchInfo = batchInfo
        };
    }

    /// <summary>
    /// 创建批量错误响应
    /// </summary>
    /// <param name="message">错误消息</param>
    /// <param name="batchInfo">批量操作信息</param>
    /// <param name="errorCode">错误代码</param>
    /// <returns>批量错误响应</returns>
    public static BatchApiResponse<T> Error(string message, BatchOperationInfo? batchInfo = null, string? errorCode = null)
    {
        return new BatchApiResponse<T>
        {
            Success = false,
            Message = message,
            ErrorCode = errorCode,
            Data = Enumerable.Empty<T>(),
            BatchInfo = batchInfo ?? new BatchOperationInfo()
        };
    }
}

/// <summary>
/// 批量操作信息
/// </summary>
public class BatchOperationInfo
{
    /// <summary>
    /// 总数量
    /// </summary>
    [JsonPropertyName("totalCount")]
    public int TotalCount { get; set; }

    /// <summary>
    /// 成功数量
    /// </summary>
    [JsonPropertyName("successCount")]
    public int SuccessCount { get; set; }

    /// <summary>
    /// 失败数量
    /// </summary>
    [JsonPropertyName("failureCount")]
    public int FailureCount { get; set; }

    /// <summary>
    /// 跳过数量
    /// </summary>
    [JsonPropertyName("skippedCount")]
    public int SkippedCount { get; set; }

    /// <summary>
    /// 成功率
    /// </summary>
    [JsonPropertyName("successRate")]
    public double SuccessRate => TotalCount > 0 ? (double)SuccessCount / TotalCount : 0;

    /// <summary>
    /// 处理耗时（毫秒）
    /// </summary>
    [JsonPropertyName("processingTimeMs")]
    public long ProcessingTimeMs { get; set; }

    /// <summary>
    /// 错误详情
    /// </summary>
    [JsonPropertyName("errors")]
    public List<BatchOperationError> Errors { get; set; } = new();
}

/// <summary>
/// 批量操作错误
/// </summary>
public class BatchOperationError
{
    /// <summary>
    /// 项目索引
    /// </summary>
    [JsonPropertyName("itemIndex")]
    public int ItemIndex { get; set; }

    /// <summary>
    /// 项目标识符
    /// </summary>
    [JsonPropertyName("itemId")]
    public string? ItemId { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    [JsonPropertyName("errorMessage")]
    public string ErrorMessage { get; set; } = string.Empty;

    /// <summary>
    /// 错误代码
    /// </summary>
    [JsonPropertyName("errorCode")]
    public string? ErrorCode { get; set; }

    /// <summary>
    /// 异常详情
    /// </summary>
    [JsonPropertyName("exceptionDetails")]
    public string? ExceptionDetails { get; set; }
}
