/**
 * 全局布局配置系统
 * 
 * 一个文件控制所有布局参数
 * 修改这里的值，整个应用的布局都会相应调整
 */

// 🎯 核心布局配置 - 只需要修改这里！
export const LAYOUT_CONFIG = {
  // 基础高度设置
  headerHeight: 64,        // ProLayout头部高度(px)
  
  // 间距设置
  contentPadding: 8,       // 内容区域内边距(px)
  containerPadding: 8,     // 页面容器内边距(px)
  elementSpacing: 4,       // 元素间距(px)
  
  // 页面布局
  pageHeaderMargin: 12,    // 页面头部下边距(px)
  toolbarMargin: 16,       // 工具栏下边距(px)
  
  // 表格布局
  tableOffset: 320,        // 表格距离顶部偏移(px) - 影响表格高度
  
  // 卡片布局
  cardGutter: 16,          // 卡片间距(px)
  cardMargin: 6,           // 卡片下边距(px)
  
  // 滚动条设置
  scrollbarWidth: 6,       // 滚动条宽度(px)
  scrollbarColor: '#c1c1c1', // 滚动条颜色
  
  // 响应式断点
  mobileBreakpoint: 768,   // 移动端断点(px)
  tabletBreakpoint: 1024,  // 平板端断点(px)
};

// 🔧 自动生成CSS变量
export const generateLayoutCSS = () => {
  const config = LAYOUT_CONFIG;
  
  return `
    :root {
      /* 基础尺寸 */
      --layout-header-height: ${config.headerHeight}px;
      --layout-content-height: calc(100vh - ${config.headerHeight}px);
      
      /* 间距控制 */
      --layout-content-padding: ${config.contentPadding}px;
      --layout-container-padding: ${config.containerPadding}px;
      --layout-element-spacing: ${config.elementSpacing}px;
      
      /* 页面布局 */
      --layout-page-header-margin: ${config.pageHeaderMargin}px;
      --layout-toolbar-margin: ${config.toolbarMargin}px;
      
      /* 表格布局 */
      --layout-table-offset: ${config.tableOffset}px;
      --layout-table-height: calc(100vh - ${config.tableOffset}px);
      --layout-table-scroll-y: calc(100vh - ${config.tableOffset}px);
      
      /* 卡片布局 */
      --layout-card-gutter: ${config.cardGutter}px;
      --layout-card-margin: ${config.cardMargin}px;
      
      /* 滚动条 */
      --layout-scrollbar-width: ${config.scrollbarWidth}px;
      --layout-scrollbar-color: ${config.scrollbarColor};
    }
    
    /* 移动端适配 */
    @media (max-width: ${config.mobileBreakpoint}px) {
      :root {
        --layout-content-padding: 16px;
        --layout-container-padding: 16px;
        --layout-table-offset: 280px;
        --layout-toolbar-margin: 12px;
        --layout-card-gutter: 12px;
      }
    }
    
    /* 平板端适配 */
    @media (min-width: ${config.mobileBreakpoint + 1}px) and (max-width: ${config.tabletBreakpoint}px) {
      :root {
        --layout-content-padding: 12px;
        --layout-container-padding: 12px;
        --layout-table-offset: 300px;
      }
    }
  `;
};

// 🚀 应用配置到页面
export const applyLayoutConfig = () => {
  // 创建或更新style标签
  let styleElement = document.getElementById('layout-config-styles');
  if (!styleElement) {
    styleElement = document.createElement('style');
    styleElement.id = 'layout-config-styles';
    document.head.appendChild(styleElement);
  }
  
  // 应用生成的CSS
  styleElement.textContent = generateLayoutCSS();
  
  console.log('✅ 布局配置已应用:', LAYOUT_CONFIG);
};

// 🔄 动态更新配置
export const updateLayoutConfig = (newConfig) => {
  Object.assign(LAYOUT_CONFIG, newConfig);
  applyLayoutConfig();
  console.log('🔄 布局配置已更新:', LAYOUT_CONFIG);
};

// 📱 获取当前屏幕类型
export const getScreenType = () => {
  const width = window.innerWidth;
  if (width <= LAYOUT_CONFIG.mobileBreakpoint) return 'mobile';
  if (width <= LAYOUT_CONFIG.tabletBreakpoint) return 'tablet';
  return 'desktop';
};

// 📏 获取计算后的尺寸
export const getComputedSizes = () => {
  const config = LAYOUT_CONFIG;
  return {
    contentHeight: window.innerHeight - config.headerHeight,
    tableHeight: window.innerHeight - config.tableOffset,
    screenType: getScreenType(),
  };
};

// 🎨 预设配置方案
export const LAYOUT_PRESETS = {
  // 紧凑布局
  compact: {
    contentPadding: 4,
    containerPadding: 6,
    elementSpacing: 2,
    pageHeaderMargin: 8,
    toolbarMargin: 12,
    tableOffset: 280,
    cardGutter: 12,
  },
  
  // 标准布局（默认）
  standard: {
    contentPadding: 8,
    containerPadding: 8,
    elementSpacing: 4,
    pageHeaderMargin: 12,
    toolbarMargin: 16,
    tableOffset: 320,
    cardGutter: 16,
  },
  
  // 宽松布局
  spacious: {
    contentPadding: 16,
    containerPadding: 16,
    elementSpacing: 8,
    pageHeaderMargin: 20,
    toolbarMargin: 24,
    tableOffset: 360,
    cardGutter: 24,
  },
};

// 🔧 应用预设配置
export const applyPreset = (presetName) => {
  const preset = LAYOUT_PRESETS[presetName];
  if (preset) {
    updateLayoutConfig(preset);
    console.log(`🎨 已应用 ${presetName} 布局预设`);
  } else {
    console.warn(`❌ 未找到预设: ${presetName}`);
  }
};

// 💾 保存配置到本地存储
export const saveLayoutConfig = () => {
  localStorage.setItem('layout-config', JSON.stringify(LAYOUT_CONFIG));
  console.log('💾 布局配置已保存到本地');
};

// 📂 从本地存储加载配置
export const loadLayoutConfig = () => {
  try {
    const saved = localStorage.getItem('layout-config');
    if (saved) {
      const config = JSON.parse(saved);
      updateLayoutConfig(config);
      console.log('📂 已从本地加载布局配置');
      return true;
    }
  } catch (error) {
    console.warn('❌ 加载本地配置失败:', error);
  }
  return false;
};
