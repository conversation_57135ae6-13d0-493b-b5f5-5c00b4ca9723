using FlowCustomV1.Core.Models.Workflow;

namespace FlowCustomV1.Core.Models.Plugins;

/// <summary>
/// 内置插件定义
/// 用于定义通过Natasha动态编译生成的内置插件
/// </summary>
public class BuiltinPluginDefinition
{
    /// <summary>
    /// 节点类型标识符
    /// </summary>
    public string NodeType { get; set; } = string.Empty;

    /// <summary>
    /// 插件名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 插件显示名称
    /// </summary>
    public string DisplayName { get; set; } = string.Empty;

    /// <summary>
    /// 插件描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 插件版本
    /// </summary>
    public string Version { get; set; } = "1.0.0";

    /// <summary>
    /// 插件作者
    /// </summary>
    public string Author { get; set; } = "FlowCustomV1";

    /// <summary>
    /// 插件标签
    /// </summary>
    public List<string> Tags { get; set; } = new();

    /// <summary>
    /// 是否支持异步执行
    /// </summary>
    public bool SupportsAsync { get; set; } = true;

    /// <summary>
    /// 是否有状态节点
    /// </summary>
    public bool IsStateful { get; set; } = false;

    /// <summary>
    /// 执行逻辑模板
    /// </summary>
    public ExecutionLogicTemplate ExecutionLogic { get; set; } = new();

    /// <summary>
    /// 输入参数定义
    /// </summary>
    public List<NodeParameterDefinition> InputParameters { get; set; } = new();

    /// <summary>
    /// 输出参数定义
    /// </summary>
    public List<NodeParameterDefinition> OutputParameters { get; set; } = new();

    /// <summary>
    /// 配置参数定义
    /// </summary>
    public List<NodeParameterDefinition> ConfigurationParameters { get; set; } = new();

    /// <summary>
    /// 资源需求
    /// </summary>
    public NodeResourceRequirements ResourceRequirements { get; set; } = new();

    /// <summary>
    /// 预估执行时间（毫秒）
    /// </summary>
    public int EstimatedExecutionTimeMs { get; set; } = 1000;

    /// <summary>
    /// 插件依赖
    /// </summary>
    public List<string> Dependencies { get; set; } = new();

    /// <summary>
    /// 自定义属性
    /// </summary>
    public Dictionary<string, object> CustomProperties { get; set; } = new();
}

/// <summary>
/// 执行逻辑模板
/// </summary>
public class ExecutionLogicTemplate
{
    /// <summary>
    /// 执行逻辑类型
    /// </summary>
    public ExecutionLogicType Type { get; set; }

    /// <summary>
    /// C#代码模板（用于Code类型）
    /// </summary>
    public string? CodeTemplate { get; set; }

    /// <summary>
    /// 表达式模板（用于Expression类型）
    /// </summary>
    public string? ExpressionTemplate { get; set; }

    /// <summary>
    /// 配置模板（用于Configuration类型）
    /// </summary>
    public Dictionary<string, object>? ConfigurationTemplate { get; set; }

    /// <summary>
    /// 脚本模板（用于Script类型）
    /// </summary>
    public ScriptTemplate? ScriptTemplate { get; set; }

    /// <summary>
    /// 预处理步骤
    /// </summary>
    public List<ProcessingStep> PreProcessingSteps { get; set; } = new();

    /// <summary>
    /// 后处理步骤
    /// </summary>
    public List<ProcessingStep> PostProcessingSteps { get; set; } = new();

    /// <summary>
    /// 错误处理策略
    /// </summary>
    public ErrorHandlingStrategy ErrorHandling { get; set; } = new();
}

/// <summary>
/// 执行逻辑类型
/// </summary>
public enum ExecutionLogicType
{
    /// <summary>
    /// C#代码
    /// </summary>
    Code,

    /// <summary>
    /// 表达式
    /// </summary>
    Expression,

    /// <summary>
    /// 配置驱动
    /// </summary>
    Configuration,

    /// <summary>
    /// 脚本
    /// </summary>
    Script,

    /// <summary>
    /// 混合模式
    /// </summary>
    Hybrid
}

/// <summary>
/// 脚本模板
/// </summary>
public class ScriptTemplate
{
    /// <summary>
    /// 脚本语言
    /// </summary>
    public string Language { get; set; } = "csharp";

    /// <summary>
    /// 脚本内容
    /// </summary>
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// 脚本参数
    /// </summary>
    public Dictionary<string, object> Parameters { get; set; } = new();

    /// <summary>
    /// 超时时间（毫秒）
    /// </summary>
    public int TimeoutMs { get; set; } = 30000;
}

/// <summary>
/// 处理步骤
/// </summary>
public class ProcessingStep
{
    /// <summary>
    /// 步骤名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 步骤类型
    /// </summary>
    public ProcessingStepType Type { get; set; }

    /// <summary>
    /// 步骤配置
    /// </summary>
    public Dictionary<string, object> Configuration { get; set; } = new();

    /// <summary>
    /// 是否必需
    /// </summary>
    public bool IsRequired { get; set; } = true;

    /// <summary>
    /// 执行顺序
    /// </summary>
    public int Order { get; set; }
}

/// <summary>
/// 处理步骤类型
/// </summary>
public enum ProcessingStepType
{
    /// <summary>
    /// 数据验证
    /// </summary>
    DataValidation,

    /// <summary>
    /// 数据转换
    /// </summary>
    DataTransformation,

    /// <summary>
    /// 数据映射
    /// </summary>
    DataMapping,

    /// <summary>
    /// 条件检查
    /// </summary>
    ConditionCheck,

    /// <summary>
    /// 日志记录
    /// </summary>
    Logging,

    /// <summary>
    /// 指标收集
    /// </summary>
    MetricsCollection,

    /// <summary>
    /// 自定义处理
    /// </summary>
    CustomProcessing
}

/// <summary>
/// 错误处理策略
/// </summary>
public class ErrorHandlingStrategy
{
    /// <summary>
    /// 重试策略
    /// </summary>
    public RetryStrategy RetryStrategy { get; set; } = new();

    /// <summary>
    /// 超时策略
    /// </summary>
    public TimeoutStrategy TimeoutStrategy { get; set; } = new();

    /// <summary>
    /// 错误回调
    /// </summary>
    public string? ErrorCallback { get; set; }

    /// <summary>
    /// 是否继续执行
    /// </summary>
    public bool ContinueOnError { get; set; } = false;

    /// <summary>
    /// 默认返回值
    /// </summary>
    public object? DefaultReturnValue { get; set; }
}

/// <summary>
/// 重试策略
/// </summary>
public class RetryStrategy
{
    /// <summary>
    /// 最大重试次数
    /// </summary>
    public int MaxRetries { get; set; } = 3;

    /// <summary>
    /// 重试间隔（毫秒）
    /// </summary>
    public int RetryIntervalMs { get; set; } = 1000;

    /// <summary>
    /// 是否使用指数退避
    /// </summary>
    public bool UseExponentialBackoff { get; set; } = true;

    /// <summary>
    /// 退避倍数
    /// </summary>
    public double BackoffMultiplier { get; set; } = 2.0;
}

/// <summary>
/// 超时策略
/// </summary>
public class TimeoutStrategy
{
    /// <summary>
    /// 执行超时时间（毫秒）
    /// </summary>
    public int ExecutionTimeoutMs { get; set; } = 30000;

    /// <summary>
    /// 是否启用超时
    /// </summary>
    public bool EnableTimeout { get; set; } = true;

    /// <summary>
    /// 超时后的操作
    /// </summary>
    public TimeoutAction TimeoutAction { get; set; } = TimeoutAction.ThrowException;
}

/// <summary>
/// 超时后的操作
/// </summary>
public enum TimeoutAction
{
    /// <summary>
    /// 抛出异常
    /// </summary>
    ThrowException,

    /// <summary>
    /// 返回默认值
    /// </summary>
    ReturnDefault,

    /// <summary>
    /// 继续执行
    /// </summary>
    Continue,

    /// <summary>
    /// 取消执行
    /// </summary>
    Cancel
}
