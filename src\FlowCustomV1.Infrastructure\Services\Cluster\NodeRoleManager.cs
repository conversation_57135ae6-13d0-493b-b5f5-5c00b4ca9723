using System.Collections.Concurrent;
using FlowCustomV1.Core.Interfaces.Cluster;
using FlowCustomV1.Core.Models.Cluster;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace FlowCustomV1.Infrastructure.Services.Cluster;

/// <summary>
/// 节点角色管理器实现
/// 负责节点角色的动态管理和切换
/// </summary>
public class NodeRoleManager : INodeRoleManager
{
    private readonly ILogger<NodeRoleManager> _logger;
    private readonly IServiceProvider _serviceProvider;
    private readonly ConcurrentDictionary<NodeRole, RoleServiceStatus> _roleServiceStatuses;
    private readonly ConcurrentDictionary<NodeRole, object> _roleServices;
    private readonly object _lockObject = new();

    private NodeRoleConfiguration _currentConfiguration;
    private string _nodeId = string.Empty;
    private bool _isInitialized = false;

    /// <summary>
    /// 构造函数
    /// </summary>
    public NodeRoleManager(
        ILogger<NodeRoleManager> logger,
        IServiceProvider serviceProvider)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
        _roleServiceStatuses = new ConcurrentDictionary<NodeRole, RoleServiceStatus>();
        _roleServices = new ConcurrentDictionary<NodeRole, object>();
        _currentConfiguration = new NodeRoleConfiguration();
    }

    /// <inheritdoc />
    public NodeRoleConfiguration CurrentConfiguration => _currentConfiguration;

    /// <inheritdoc />
    public string NodeId => _nodeId;

    /// <inheritdoc />
    public event EventHandler<NodeRoleChangedEventArgs>? RoleChanged;

    /// <inheritdoc />
    public async Task InitializeAsync(string nodeId, NodeRoleConfiguration configuration, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(nodeId);
        ArgumentNullException.ThrowIfNull(configuration);

        lock (_lockObject)
        {
            if (_isInitialized)
            {
                _logger.LogWarning("NodeRoleManager is already initialized for node {NodeId}", _nodeId);
                return;
            }

            _nodeId = nodeId;
            _currentConfiguration = configuration.Clone();
            _isInitialized = true;
        }

        _logger.LogInformation("Initializing NodeRoleManager for node {NodeId} with roles: {Roles}", 
            nodeId, string.Join(", ", configuration.GetRolesByPriority().Select(r => r.GetDisplayName())));

        // 验证配置
        var validation = ValidateConfiguration(configuration);
        if (!validation.IsValid)
        {
            var errors = string.Join(", ", validation.Errors);
            _logger.LogError("Invalid role configuration for node {NodeId}: {Errors}", nodeId, errors);
            throw new InvalidOperationException($"Invalid role configuration: {errors}");
        }

        // 初始化所有角色的服务状态
        foreach (var role in configuration.GetRolesByPriority())
        {
            _roleServiceStatuses[role] = RoleServiceStatus.Disabled;
        }

        // 启动配置中启用的角色服务
        await StartConfiguredRoleServicesAsync(cancellationToken);

        _logger.LogInformation("NodeRoleManager initialized successfully for node {NodeId}", nodeId);
    }

    /// <inheritdoc />
    public bool SupportsRole(NodeRole role)
    {
        return _currentConfiguration.SupportsRole(role);
    }

    /// <inheritdoc />
    public IEnumerable<NodeRole> GetActiveRoles()
    {
        return _currentConfiguration.GetRolesByPriority();
    }

    /// <inheritdoc />
    public IEnumerable<NodeRole> GetRolesByPriority()
    {
        return _currentConfiguration.GetRolesByPriority();
    }

    /// <inheritdoc />
    public async Task<bool> AddRoleAsync(NodeRole role, CancellationToken cancellationToken = default)
    {
        if (!_isInitialized)
        {
            _logger.LogError("NodeRoleManager is not initialized");
            return false;
        }

        if (_currentConfiguration.SupportsRole(role))
        {
            _logger.LogWarning("Role {Role} is already supported by node {NodeId}", role.GetDisplayName(), _nodeId);
            return true;
        }

        _logger.LogInformation("Adding role {Role} to node {NodeId}", role.GetDisplayName(), _nodeId);

        var oldConfiguration = _currentConfiguration.Clone();
        
        lock (_lockObject)
        {
            _currentConfiguration.AddRole(role);
        }

        // 启用新角色的服务
        var success = await EnableRoleServiceAsync(role, cancellationToken);
        if (!success)
        {
            // 回滚配置
            lock (_lockObject)
            {
                _currentConfiguration = oldConfiguration;
            }
            _logger.LogError("Failed to enable service for role {Role}, rolling back", role.GetDisplayName());
            return false;
        }

        // 触发角色变更事件
        OnRoleChanged(new NodeRoleChangedEventArgs
        {
            NodeId = _nodeId,
            ChangeType = RoleChangeType.Added,
            Role = role,
            OldConfiguration = oldConfiguration,
            NewConfiguration = _currentConfiguration.Clone(),
            Reason = "Role added via API"
        });

        _logger.LogInformation("Successfully added role {Role} to node {NodeId}", role.GetDisplayName(), _nodeId);
        return true;
    }

    /// <inheritdoc />
    public async Task<bool> RemoveRoleAsync(NodeRole role, CancellationToken cancellationToken = default)
    {
        if (!_isInitialized)
        {
            _logger.LogError("NodeRoleManager is not initialized");
            return false;
        }

        if (!_currentConfiguration.SupportsRole(role))
        {
            _logger.LogWarning("Role {Role} is not supported by node {NodeId}", role.GetDisplayName(), _nodeId);
            return true;
        }

        // 检查是否是最后一个角色
        var activeRoles = _currentConfiguration.GetRolesByPriority().ToList();
        if (activeRoles.Count == 1 && activeRoles[0] == role)
        {
            _logger.LogError("Cannot remove the last role {Role} from node {NodeId}", role.GetDisplayName(), _nodeId);
            return false;
        }

        _logger.LogInformation("Removing role {Role} from node {NodeId}", role.GetDisplayName(), _nodeId);

        var oldConfiguration = _currentConfiguration.Clone();

        // 禁用角色服务
        var success = await DisableRoleServiceAsync(role, cancellationToken);
        if (!success)
        {
            _logger.LogError("Failed to disable service for role {Role}", role.GetDisplayName());
            return false;
        }

        lock (_lockObject)
        {
            _currentConfiguration.RemoveRole(role);
        }

        // 触发角色变更事件
        OnRoleChanged(new NodeRoleChangedEventArgs
        {
            NodeId = _nodeId,
            ChangeType = RoleChangeType.Removed,
            Role = role,
            OldConfiguration = oldConfiguration,
            NewConfiguration = _currentConfiguration.Clone(),
            Reason = "Role removed via API"
        });

        _logger.LogInformation("Successfully removed role {Role} from node {NodeId}", role.GetDisplayName(), _nodeId);
        return true;
    }

    /// <inheritdoc />
    public async Task<bool> SwitchToConfigurationAsync(NodeRoleConfiguration newConfiguration, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(newConfiguration);

        if (!_isInitialized)
        {
            _logger.LogError("NodeRoleManager is not initialized");
            return false;
        }

        // 验证新配置
        var validation = ValidateConfiguration(newConfiguration);
        if (!validation.IsValid)
        {
            var errors = string.Join(", ", validation.Errors);
            _logger.LogError("Invalid new configuration for node {NodeId}: {Errors}", _nodeId, errors);
            return false;
        }

        _logger.LogInformation("Switching node {NodeId} to new configuration with roles: {Roles}", 
            _nodeId, string.Join(", ", newConfiguration.GetRolesByPriority().Select(r => r.GetDisplayName())));

        var oldConfiguration = _currentConfiguration.Clone();

        try
        {
            // 停止所有当前角色服务
            await StopAllRoleServicesAsync(cancellationToken);

            // 更新配置
            lock (_lockObject)
            {
                _currentConfiguration = newConfiguration.Clone();
            }

            // 启动新配置的角色服务
            await StartConfiguredRoleServicesAsync(cancellationToken);

            // 触发角色变更事件
            OnRoleChanged(new NodeRoleChangedEventArgs
            {
                NodeId = _nodeId,
                ChangeType = RoleChangeType.Switched,
                Role = NodeRole.All, // 表示整体配置切换
                OldConfiguration = oldConfiguration,
                NewConfiguration = _currentConfiguration.Clone(),
                Reason = "Configuration switched via API"
            });

            _logger.LogInformation("Successfully switched node {NodeId} to new configuration", _nodeId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to switch configuration for node {NodeId}, rolling back", _nodeId);
            
            // 回滚配置
            lock (_lockObject)
            {
                _currentConfiguration = oldConfiguration;
            }

            // 尝试恢复旧配置的服务
            try
            {
                await StartConfiguredRoleServicesAsync(cancellationToken);
            }
            catch (Exception rollbackEx)
            {
                _logger.LogError(rollbackEx, "Failed to rollback configuration for node {NodeId}", _nodeId);
            }

            return false;
        }
    }

    /// <inheritdoc />
    public async Task<bool> EnableRoleServiceAsync(NodeRole role, CancellationToken cancellationToken = default)
    {
        if (!_currentConfiguration.SupportsRole(role))
        {
            _logger.LogWarning("Role {Role} is not supported by node {NodeId}", role.GetDisplayName(), _nodeId);
            return false;
        }

        var currentStatus = GetRoleServiceStatus(role);
        if (currentStatus == RoleServiceStatus.Running)
        {
            _logger.LogDebug("Service for role {Role} is already running on node {NodeId}", role.GetDisplayName(), _nodeId);
            return true;
        }

        _logger.LogInformation("Enabling service for role {Role} on node {NodeId}", role.GetDisplayName(), _nodeId);

        try
        {
            _roleServiceStatuses[role] = RoleServiceStatus.Starting;

            // 根据角色类型启动相应的服务
            var service = await CreateRoleServiceAsync(role, cancellationToken);
            if (service != null)
            {
                _roleServices[role] = service;
                _roleServiceStatuses[role] = RoleServiceStatus.Running;

                _logger.LogInformation("Successfully enabled service for role {Role} on node {NodeId}",
                    role.GetDisplayName(), _nodeId);
                return true;
            }
            else
            {
                _roleServiceStatuses[role] = RoleServiceStatus.Error;
                _logger.LogError("Failed to create service for role {Role} on node {NodeId}",
                    role.GetDisplayName(), _nodeId);
                return false;
            }
        }
        catch (Exception ex)
        {
            _roleServiceStatuses[role] = RoleServiceStatus.Error;
            _logger.LogError(ex, "Error enabling service for role {Role} on node {NodeId}",
                role.GetDisplayName(), _nodeId);
            return false;
        }
    }

    /// <inheritdoc />
    public async Task<bool> DisableRoleServiceAsync(NodeRole role, CancellationToken cancellationToken = default)
    {
        var currentStatus = GetRoleServiceStatus(role);
        if (currentStatus == RoleServiceStatus.Disabled || currentStatus == RoleServiceStatus.Stopped)
        {
            _logger.LogDebug("Service for role {Role} is already disabled on node {NodeId}", role.GetDisplayName(), _nodeId);
            return true;
        }

        _logger.LogInformation("Disabling service for role {Role} on node {NodeId}", role.GetDisplayName(), _nodeId);

        try
        {
            _roleServiceStatuses[role] = RoleServiceStatus.Stopping;

            // 停止角色服务
            if (_roleServices.TryRemove(role, out var service))
            {
                await StopRoleServiceAsync(service, cancellationToken);
            }

            _roleServiceStatuses[role] = RoleServiceStatus.Disabled;

            _logger.LogInformation("Successfully disabled service for role {Role} on node {NodeId}",
                role.GetDisplayName(), _nodeId);
            return true;
        }
        catch (Exception ex)
        {
            _roleServiceStatuses[role] = RoleServiceStatus.Error;
            _logger.LogError(ex, "Error disabling service for role {Role} on node {NodeId}",
                role.GetDisplayName(), _nodeId);
            return false;
        }
    }

    /// <inheritdoc />
    public RoleServiceStatus GetRoleServiceStatus(NodeRole role)
    {
        return _roleServiceStatuses.TryGetValue(role, out var status) ? status : RoleServiceStatus.Unknown;
    }

    /// <inheritdoc />
    public Dictionary<NodeRole, RoleServiceStatus> GetAllRoleServiceStatuses()
    {
        return new Dictionary<NodeRole, RoleServiceStatus>(_roleServiceStatuses);
    }

    /// <inheritdoc />
    public ValidationResult ValidateConfiguration(NodeRoleConfiguration configuration)
    {
        ArgumentNullException.ThrowIfNull(configuration);
        return configuration.Validate();
    }

    /// <inheritdoc />
    public NodeRoleConfiguration GetRecommendedConfiguration(int nodeCount, int expectedLoad = 100)
    {
        var architectureMode = ClusterArchitectureModeExtensions.RecommendArchitectureMode(nodeCount, expectedLoad);
        var recommendedRoles = architectureMode.GetRecommendedRoles(nodeCount);

        return new NodeRoleConfiguration
        {
            Roles = recommendedRoles,
            ArchitectureMode = architectureMode,
            EnableDynamicRoleSwitching = nodeCount > 1,
            EnableLegacyMode = architectureMode.SupportsMasterWorker()
        };
    }

    /// <summary>
    /// 触发角色变更事件
    /// </summary>
    /// <param name="args">事件参数</param>
    protected virtual void OnRoleChanged(NodeRoleChangedEventArgs args)
    {
        RoleChanged?.Invoke(this, args);
    }

    /// <summary>
    /// 启动配置中启用的角色服务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>启动任务</returns>
    private async Task StartConfiguredRoleServicesAsync(CancellationToken cancellationToken)
    {
        var rolesToStart = _currentConfiguration.GetRolesByPriority()
            .Where(role => _currentConfiguration.RoleServiceConfigurations.TryGetValue(role, out var config)
                          && config.Enabled)
            .ToList();

        foreach (var role in rolesToStart)
        {
            await EnableRoleServiceAsync(role, cancellationToken);
        }
    }

    /// <summary>
    /// 停止所有角色服务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>停止任务</returns>
    private async Task StopAllRoleServicesAsync(CancellationToken cancellationToken)
    {
        var tasks = _roleServices.Keys.Select(role => DisableRoleServiceAsync(role, cancellationToken));
        await Task.WhenAll(tasks);
    }

    /// <summary>
    /// 根据角色创建相应的服务
    /// </summary>
    /// <param name="role">节点角色</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>创建的服务对象</returns>
    private async Task<object?> CreateRoleServiceAsync(NodeRole role, CancellationToken cancellationToken)
    {
        try
        {
            return role switch
            {
                NodeRole.Designer => await CreateDesignerServiceAsync(cancellationToken),
                NodeRole.Validator => await CreateValidatorServiceAsync(cancellationToken),
                NodeRole.Executor => await CreateExecutorServiceAsync(cancellationToken),
                NodeRole.Monitor => await CreateMonitorServiceAsync(cancellationToken),
                NodeRole.Gateway => await CreateGatewayServiceAsync(cancellationToken),
                NodeRole.Storage => await CreateStorageServiceAsync(cancellationToken),
                NodeRole.Scheduler => await CreateSchedulerServiceAsync(cancellationToken),
                _ => null
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create service for role {Role}", role.GetDisplayName());
            return null;
        }
    }

    /// <summary>
    /// 停止角色服务
    /// </summary>
    /// <param name="service">服务对象</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>停止任务</returns>
    private async Task StopRoleServiceAsync(object service, CancellationToken cancellationToken)
    {
        try
        {
            // 如果服务实现了IDisposable，则释放资源
            if (service is IDisposable disposable)
            {
                disposable.Dispose();
            }

            // 如果服务实现了IAsyncDisposable，则异步释放资源
            if (service is IAsyncDisposable asyncDisposable)
            {
                await asyncDisposable.DisposeAsync();
            }

            // 如果服务有Stop方法，则调用
            var stopMethod = service.GetType().GetMethod("StopAsync");
            if (stopMethod != null)
            {
                var task = stopMethod.Invoke(service, new object[] { cancellationToken }) as Task;
                if (task != null)
                {
                    await task;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error stopping role service {ServiceType}", service.GetType().Name);
        }
    }

    // 各个角色服务的创建方法
    private async Task<object?> CreateDesignerServiceAsync(CancellationToken cancellationToken)
    {
        _logger.LogDebug("Creating Designer service");
        // 这里将在后续实现中创建Designer专业化服务
        // 目前返回一个占位符，表示服务已创建
        await Task.Delay(100, cancellationToken); // 模拟异步初始化
        return new { ServiceType = "Designer", Status = "Running" };
    }

    private async Task<object?> CreateValidatorServiceAsync(CancellationToken cancellationToken)
    {
        _logger.LogDebug("Creating Validator service");
        // 这里将创建Validator专业化服务
        await Task.Delay(100, cancellationToken);
        return new { ServiceType = "Validator", Status = "Running" };
    }

    private async Task<object?> CreateExecutorServiceAsync(CancellationToken cancellationToken)
    {
        _logger.LogDebug("Creating Executor service");
        // 这里将创建Executor专业化服务
        await Task.Delay(100, cancellationToken);
        return new { ServiceType = "Executor", Status = "Running" };
    }

    private async Task<object?> CreateMonitorServiceAsync(CancellationToken cancellationToken)
    {
        _logger.LogDebug("Creating Monitor service");
        await Task.Delay(100, cancellationToken);
        return new { ServiceType = "Monitor", Status = "Running" };
    }

    private async Task<object?> CreateGatewayServiceAsync(CancellationToken cancellationToken)
    {
        _logger.LogDebug("Creating Gateway service");
        await Task.Delay(100, cancellationToken);
        return new { ServiceType = "Gateway", Status = "Running" };
    }

    private async Task<object?> CreateStorageServiceAsync(CancellationToken cancellationToken)
    {
        _logger.LogDebug("Creating Storage service");
        await Task.Delay(100, cancellationToken);
        return new { ServiceType = "Storage", Status = "Running" };
    }

    private async Task<object?> CreateSchedulerServiceAsync(CancellationToken cancellationToken)
    {
        _logger.LogDebug("Creating Scheduler service");
        await Task.Delay(100, cancellationToken);
        return new { ServiceType = "Scheduler", Status = "Running" };
    }
}
