using FlowCustomV1.Core.Models.Workflow;
using FlowCustomV1.Core.Models.Common;

namespace FlowCustomV1.Core.Interfaces;

/// <summary>
/// 工作流存储接口
/// 提供工作流定义的持久化和查询功能
/// </summary>
public interface IWorkflowRepository
{
    /// <summary>
    /// 保存工作流定义
    /// </summary>
    /// <param name="workflowDefinition">工作流定义</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>保存结果</returns>
    Task<bool> SaveWorkflowDefinitionAsync(WorkflowDefinition workflowDefinition, CancellationToken cancellationToken = default);

    /// <summary>
    /// 根据ID获取工作流定义
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>工作流定义</returns>
    Task<WorkflowDefinition?> GetWorkflowDefinitionAsync(string workflowId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 根据ID获取工作流定义（别名方法）
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>工作流定义</returns>
    Task<WorkflowDefinition?> GetByIdAsync(string workflowId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 根据名称和版本获取工作流定义
    /// </summary>
    /// <param name="name">工作流名称</param>
    /// <param name="version">工作流版本</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>工作流定义</returns>
    Task<WorkflowDefinition?> GetWorkflowDefinitionAsync(string name, string version, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取所有工作流定义
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>工作流定义列表</returns>
    Task<IEnumerable<WorkflowDefinition>> GetAllWorkflowDefinitionsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 根据标签查询工作流定义
    /// </summary>
    /// <param name="tags">标签列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>工作流定义列表</returns>
    Task<IEnumerable<WorkflowDefinition>> GetWorkflowDefinitionsByTagsAsync(IEnumerable<string> tags, CancellationToken cancellationToken = default);

    /// <summary>
    /// 根据作者查询工作流定义
    /// </summary>
    /// <param name="author">作者</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>工作流定义列表</returns>
    Task<IEnumerable<WorkflowDefinition>> GetWorkflowDefinitionsByAuthorAsync(string author, CancellationToken cancellationToken = default);

    /// <summary>
    /// 删除工作流定义
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>删除结果</returns>
    Task<bool> DeleteWorkflowDefinitionAsync(string workflowId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 检查工作流定义是否存在
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否存在</returns>
    Task<bool> ExistsAsync(string workflowId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取工作流定义的版本历史
    /// </summary>
    /// <param name="name">工作流名称</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>版本历史列表</returns>
    Task<IEnumerable<WorkflowDefinition>> GetWorkflowVersionHistoryAsync(string name, CancellationToken cancellationToken = default);

    /// <summary>
    /// 搜索工作流定义
    /// </summary>
    /// <param name="searchCriteria">搜索条件</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>搜索结果</returns>
    Task<IEnumerable<WorkflowDefinition>> SearchWorkflowDefinitionsAsync(WorkflowSearchCriteria searchCriteria, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取工作流定义统计信息
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>统计信息</returns>
    Task<WorkflowRepositoryStatistics> GetStatisticsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 分页获取工作流定义
    /// </summary>
    /// <param name="pageIndex">页索引</param>
    /// <param name="pageSize">页大小</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>分页结果</returns>
    Task<IReadOnlyList<WorkflowDefinition>> GetWorkflowDefinitionsAsync(int pageIndex, int pageSize, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取工作流总数
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>工作流总数</returns>
    Task<long> GetWorkflowCountAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 搜索工作流定义（支持分页）
    /// </summary>
    /// <param name="searchTerm">搜索词</param>
    /// <param name="pageIndex">页索引</param>
    /// <param name="pageSize">页大小</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>搜索结果</returns>
    Task<IReadOnlyList<WorkflowDefinition>> SearchWorkflowDefinitionsAsync(string searchTerm, int pageIndex, int pageSize, CancellationToken cancellationToken = default);

    /// <summary>
    /// 根据搜索条件获取工作流总数
    /// </summary>
    /// <param name="searchTerm">搜索词</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>匹配的工作流总数</returns>
    Task<long> GetWorkflowCountBySearchAsync(string searchTerm, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取工作流版本列表
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>版本列表</returns>
    Task<IReadOnlyList<string>> GetWorkflowVersionsAsync(string workflowId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 根据版本获取工作流定义
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="version">版本号</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>工作流定义</returns>
    Task<WorkflowDefinition?> GetWorkflowDefinitionByVersionAsync(string workflowId, string version, CancellationToken cancellationToken = default);
}

/// <summary>
/// 工作流搜索条件
/// </summary>
public class WorkflowSearchCriteria
{
    /// <summary>
    /// 关键词
    /// </summary>
    public string? Keyword { get; set; }

    /// <summary>
    /// 标签
    /// </summary>
    public List<string> Tags { get; set; } = new();

    /// <summary>
    /// 作者
    /// </summary>
    public string? Author { get; set; }

    /// <summary>
    /// 创建时间范围开始
    /// </summary>
    public DateTime? CreatedAfter { get; set; }

    /// <summary>
    /// 创建时间范围结束
    /// </summary>
    public DateTime? CreatedBefore { get; set; }

    /// <summary>
    /// 分页大小
    /// </summary>
    public int PageSize { get; set; } = 50;

    /// <summary>
    /// 页码
    /// </summary>
    public int PageNumber { get; set; } = 1;

    /// <summary>
    /// 排序字段
    /// </summary>
    public string SortBy { get; set; } = "CreatedAt";

    /// <summary>
    /// 排序方向
    /// </summary>
    public bool SortDescending { get; set; } = true;
}

/// <summary>
/// 工作流存储统计信息
/// </summary>
public class WorkflowRepositoryStatistics
{
    /// <summary>
    /// 工作流总数
    /// </summary>
    public int TotalWorkflows { get; set; }

    /// <summary>
    /// 活跃工作流数
    /// </summary>
    public int ActiveWorkflows { get; set; }

    /// <summary>
    /// 作者总数
    /// </summary>
    public int TotalAuthors { get; set; }

    /// <summary>
    /// 标签总数
    /// </summary>
    public int TotalTags { get; set; }

    /// <summary>
    /// 最近创建的工作流
    /// </summary>
    public DateTime? LastCreatedAt { get; set; }

    /// <summary>
    /// 最近修改的工作流
    /// </summary>
    public DateTime? LastModifiedAt { get; set; }

    /// <summary>
    /// 存储大小（字节）
    /// </summary>
    public long StorageSizeBytes { get; set; }
}
