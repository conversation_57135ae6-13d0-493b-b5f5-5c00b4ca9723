using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using FlowCustomV1.Core.Models.Cluster;
using FlowCustomV1.Core.Models.Workflow;
using FlowCustomV1.Core.Interfaces.Scheduling;

namespace FlowCustomV1.Core.Models.Scheduling;

/// <summary>
/// 分布式任务模型
/// </summary>
public class DistributedTask
{
    /// <summary>
    /// 任务ID
    /// </summary>
    [Required]
    public string TaskId { get; set; } = string.Empty;

    /// <summary>
    /// 工作流ID
    /// </summary>
    [Required]
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 执行ID
    /// </summary>
    [Required]
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 节点ID（工作流中的节点）
    /// </summary>
    [Required]
    public string NodeId { get; set; } = string.Empty;

    /// <summary>
    /// 任务类型
    /// </summary>
    [Required]
    public string TaskType { get; set; } = string.Empty;

    /// <summary>
    /// 任务优先级
    /// </summary>
    [Range(1, 10)]
    public int Priority { get; set; } = 5;

    /// <summary>
    /// 任务要求
    /// </summary>
    public TaskRequirements Requirements { get; set; } = new();

    /// <summary>
    /// 输入数据
    /// </summary>
    public Dictionary<string, object> InputData { get; set; } = new();

    /// <summary>
    /// 任务配置
    /// </summary>
    public Dictionary<string, object> Configuration { get; set; } = new();

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 预期执行时间（毫秒）
    /// </summary>
    public long EstimatedExecutionTimeMs { get; set; }

    /// <summary>
    /// 超时时间（毫秒）
    /// </summary>
    public long TimeoutMs { get; set; } = 300000; // 5分钟默认超时

    /// <summary>
    /// 重试次数
    /// </summary>
    public int RetryCount { get; set; } = 0;

    /// <summary>
    /// 最大重试次数
    /// </summary>
    public int MaxRetries { get; set; } = 3;

    /// <summary>
    /// 任务标签
    /// </summary>
    public Dictionary<string, string> Labels { get; set; } = new();

    /// <summary>
    /// 任务元数据
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// 任务要求
/// </summary>
public class TaskRequirements
{
    /// <summary>
    /// 所需的最小CPU核心数
    /// </summary>
    public int MinCpuCores { get; set; } = 1;

    /// <summary>
    /// 所需的最小内存（MB）
    /// </summary>
    public long MinMemoryMB { get; set; } = 512;

    /// <summary>
    /// 所需的最小磁盘空间（MB）
    /// </summary>
    public long MinDiskSpaceMB { get; set; } = 100;

    /// <summary>
    /// 所需的节点角色
    /// </summary>
    public List<string> RequiredRoles { get; set; } = new();

    /// <summary>
    /// 所需的节点能力
    /// </summary>
    public List<string> RequiredCapabilities { get; set; } = new();

    /// <summary>
    /// 所需的节点标签
    /// </summary>
    public Dictionary<string, string> RequiredLabels { get; set; } = new();

    /// <summary>
    /// 地理位置要求
    /// </summary>
    public GeographyRequirement? GeographyRequirement { get; set; }

    /// <summary>
    /// 网络延迟要求（毫秒）
    /// </summary>
    public double MaxNetworkLatencyMs { get; set; } = 1000;

    /// <summary>
    /// 是否需要GPU
    /// </summary>
    public bool RequiresGpu { get; set; } = false;

    /// <summary>
    /// 是否需要专用节点
    /// </summary>
    public bool RequiresDedicatedNode { get; set; } = false;

    /// <summary>
    /// 亲和性规则
    /// </summary>
    public List<AffinityRule> AffinityRules { get; set; } = new();

    /// <summary>
    /// 反亲和性规则
    /// </summary>
    public List<AntiAffinityRule> AntiAffinityRules { get; set; } = new();
}

/// <summary>
/// 地理位置要求
/// </summary>
public class GeographyRequirement
{
    /// <summary>
    /// 首选区域
    /// </summary>
    public string? PreferredRegion { get; set; }

    /// <summary>
    /// 首选可用区
    /// </summary>
    public string? PreferredAvailabilityZone { get; set; }

    /// <summary>
    /// 允许的区域列表
    /// </summary>
    public List<string> AllowedRegions { get; set; } = new();

    /// <summary>
    /// 禁止的区域列表
    /// </summary>
    public List<string> ForbiddenRegions { get; set; } = new();

    /// <summary>
    /// 最大距离（公里）
    /// </summary>
    public double? MaxDistanceKm { get; set; }
}

/// <summary>
/// 亲和性规则
/// </summary>
public class AffinityRule
{
    /// <summary>
    /// 规则类型
    /// </summary>
    public AffinityType Type { get; set; } = AffinityType.NodeAffinity;

    /// <summary>
    /// 标签选择器
    /// </summary>
    public Dictionary<string, string> LabelSelector { get; set; } = new();

    /// <summary>
    /// 权重（1-100）
    /// </summary>
    [Range(1, 100)]
    public int Weight { get; set; } = 50;

    /// <summary>
    /// 是否为硬性要求
    /// </summary>
    public bool IsRequired { get; set; } = false;
}

/// <summary>
/// 反亲和性规则
/// </summary>
public class AntiAffinityRule
{
    /// <summary>
    /// 规则类型
    /// </summary>
    public AffinityType Type { get; set; } = AffinityType.NodeAntiAffinity;

    /// <summary>
    /// 标签选择器
    /// </summary>
    public Dictionary<string, string> LabelSelector { get; set; } = new();

    /// <summary>
    /// 是否为硬性要求
    /// </summary>
    public bool IsRequired { get; set; } = true;
}

/// <summary>
/// 亲和性类型枚举
/// </summary>
public enum AffinityType
{
    /// <summary>
    /// 节点亲和性
    /// </summary>
    NodeAffinity,

    /// <summary>
    /// 节点反亲和性
    /// </summary>
    NodeAntiAffinity,

    /// <summary>
    /// 任务亲和性
    /// </summary>
    TaskAffinity,

    /// <summary>
    /// 任务反亲和性
    /// </summary>
    TaskAntiAffinity
}

/// <summary>
/// 任务分发结果
/// </summary>
public class TaskDistributionResult
{
    /// <summary>
    /// 任务ID
    /// </summary>
    public string TaskId { get; set; } = string.Empty;

    /// <summary>
    /// 是否分发成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 分配的节点信息
    /// </summary>
    public NodeInfo? AssignedNode { get; set; }

    /// <summary>
    /// 分发策略
    /// </summary>
    public TaskDistributionStrategy Strategy { get; set; }

    /// <summary>
    /// 分发时间
    /// </summary>
    public DateTime DistributedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 分发耗时（毫秒）
    /// </summary>
    public long DistributionTimeMs { get; set; }

    /// <summary>
    /// 候选节点数量
    /// </summary>
    public int CandidateNodeCount { get; set; }

    /// <summary>
    /// 选择评分
    /// </summary>
    public double SelectionScore { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 分发详情
    /// </summary>
    public Dictionary<string, object> Details { get; set; } = new();
}

/// <summary>
/// 批量任务分发结果
/// </summary>
public class BatchTaskDistributionResult
{
    /// <summary>
    /// 总任务数
    /// </summary>
    public int TotalTasks { get; set; }

    /// <summary>
    /// 成功分发的任务数
    /// </summary>
    public int SuccessfulTasks { get; set; }

    /// <summary>
    /// 失败的任务数
    /// </summary>
    public int FailedTasks { get; set; }

    /// <summary>
    /// 成功率
    /// </summary>
    public double SuccessRate => TotalTasks > 0 ? (double)SuccessfulTasks / TotalTasks : 0;

    /// <summary>
    /// 分发开始时间
    /// </summary>
    public DateTime StartTime { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 分发结束时间
    /// </summary>
    public DateTime EndTime { get; set; }

    /// <summary>
    /// 总耗时（毫秒）
    /// </summary>
    public long TotalTimeMs => (long)(EndTime - StartTime).TotalMilliseconds;

    /// <summary>
    /// 每个任务的分发结果
    /// </summary>
    public List<TaskDistributionResult> TaskResults { get; set; } = new();

    /// <summary>
    /// 节点分发统计
    /// </summary>
    public Dictionary<string, int> NodeDistributionStats { get; set; } = new();

    /// <summary>
    /// 错误汇总
    /// </summary>
    public List<string> ErrorSummary { get; set; } = new();
}

/// <summary>
/// 负载均衡状态
/// </summary>
public class LoadBalancingStatus
{
    /// <summary>
    /// 集群总节点数
    /// </summary>
    public int TotalNodes { get; set; }

    /// <summary>
    /// 活跃节点数
    /// </summary>
    public int ActiveNodes { get; set; }

    /// <summary>
    /// 平均负载
    /// </summary>
    public double AverageLoad { get; set; }

    /// <summary>
    /// 负载标准差
    /// </summary>
    public double LoadStandardDeviation { get; set; }

    /// <summary>
    /// 负载均衡度（0-1，1表示完全均衡）
    /// </summary>
    public double BalanceScore { get; set; }

    /// <summary>
    /// 最高负载节点
    /// </summary>
    public NodeLoadSummary? HighestLoadNode { get; set; }

    /// <summary>
    /// 最低负载节点
    /// </summary>
    public NodeLoadSummary? LowestLoadNode { get; set; }

    /// <summary>
    /// 节点负载分布
    /// </summary>
    public List<NodeLoadSummary> NodeLoadDistribution { get; set; } = new();

    /// <summary>
    /// 是否需要重新平衡
    /// </summary>
    public bool NeedsRebalancing { get; set; }

    /// <summary>
    /// 重新平衡建议
    /// </summary>
    public List<RebalancingRecommendation> RebalancingRecommendations { get; set; } = new();

    /// <summary>
    /// 统计时间
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 节点负载摘要
/// </summary>
public class NodeLoadSummary
{
    /// <summary>
    /// 节点ID
    /// </summary>
    public string NodeId { get; set; } = string.Empty;

    /// <summary>
    /// 节点名称
    /// </summary>
    public string NodeName { get; set; } = string.Empty;

    /// <summary>
    /// 节点角色
    /// </summary>
    public string NodeRole { get; set; } = string.Empty;

    /// <summary>
    /// 负载评分（0-100）
    /// </summary>
    public double LoadScore { get; set; }

    /// <summary>
    /// CPU使用率
    /// </summary>
    public double CpuUsage { get; set; }

    /// <summary>
    /// 内存使用率
    /// </summary>
    public double MemoryUsage { get; set; }

    /// <summary>
    /// 活跃任务数
    /// </summary>
    public int ActiveTasks { get; set; }

    /// <summary>
    /// 最大任务容量
    /// </summary>
    public int MaxTasks { get; set; }

    /// <summary>
    /// 容量利用率
    /// </summary>
    public double CapacityUtilization => MaxTasks > 0 ? (double)ActiveTasks / MaxTasks : 0;

    /// <summary>
    /// 健康状态
    /// </summary>
    public string HealthStatus { get; set; } = "Unknown";

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 重新平衡建议
/// </summary>
public class RebalancingRecommendation
{
    /// <summary>
    /// 建议类型
    /// </summary>
    public RebalancingType Type { get; set; }

    /// <summary>
    /// 源节点ID
    /// </summary>
    public string SourceNodeId { get; set; } = string.Empty;

    /// <summary>
    /// 目标节点ID
    /// </summary>
    public string TargetNodeId { get; set; } = string.Empty;

    /// <summary>
    /// 建议迁移的任务数量
    /// </summary>
    public int TaskCount { get; set; }

    /// <summary>
    /// 预期改善的负载差异
    /// </summary>
    public double ExpectedImprovement { get; set; }

    /// <summary>
    /// 建议优先级（1-10）
    /// </summary>
    [Range(1, 10)]
    public int Priority { get; set; } = 5;

    /// <summary>
    /// 建议原因
    /// </summary>
    public string Reason { get; set; } = string.Empty;

    /// <summary>
    /// 预估执行时间（毫秒）
    /// </summary>
    public long EstimatedExecutionTimeMs { get; set; }
}
