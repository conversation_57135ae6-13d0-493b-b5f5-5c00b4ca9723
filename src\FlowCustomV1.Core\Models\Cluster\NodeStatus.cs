namespace FlowCustomV1.Core.Models.Cluster;

/// <summary>
/// 节点状态
/// 定义节点在运行过程中的各种状态
/// </summary>
public enum NodeStatus
{
    /// <summary>
    /// 未知状态 - 节点状态无法确定
    /// </summary>
    Unknown = 0,

    /// <summary>
    /// 初始化中 - 节点正在启动和初始化
    /// </summary>
    Initializing = 1,

    /// <summary>
    /// 健康运行 - 节点正常运行，可以接受任务
    /// </summary>
    Healthy = 2,

    /// <summary>
    /// 性能降级 - 节点运行但性能下降
    /// </summary>
    Degraded = 3,

    /// <summary>
    /// 过载状态 - 节点负载过高，暂时无法接受新任务
    /// </summary>
    Overloaded = 4,

    /// <summary>
    /// 维护模式 - 节点处于维护状态，不接受新任务
    /// </summary>
    Maintenance = 5,

    /// <summary>
    /// 故障状态 - 节点出现故障，无法正常工作
    /// </summary>
    Faulty = 6,

    /// <summary>
    /// 离线状态 - 节点已离线，无法通信
    /// </summary>
    Offline = 7,

    /// <summary>
    /// 正在关闭 - 节点正在优雅关闭
    /// </summary>
    Shutting_Down = 8
}

/// <summary>
/// NodeStatus 扩展方法
/// </summary>
public static class NodeStatusExtensions
{
    /// <summary>
    /// 判断节点是否可以接受新任务
    /// </summary>
    /// <param name="status">节点状态</param>
    /// <returns>是否可以接受新任务</returns>
    public static bool CanAcceptTasks(this NodeStatus status)
    {
        return status == NodeStatus.Healthy;
    }

    /// <summary>
    /// 判断节点是否在线
    /// </summary>
    /// <param name="status">节点状态</param>
    /// <returns>是否在线</returns>
    public static bool IsOnline(this NodeStatus status)
    {
        return status != NodeStatus.Offline &&
               status != NodeStatus.Unknown &&
               status != NodeStatus.Shutting_Down;
    }

    /// <summary>
    /// 判断节点是否健康
    /// </summary>
    /// <param name="status">节点状态</param>
    /// <returns>是否健康</returns>
    public static bool IsHealthy(this NodeStatus status)
    {
        return status == NodeStatus.Healthy ||
               status == NodeStatus.Degraded;
    }

    /// <summary>
    /// 判断节点是否需要关注
    /// </summary>
    /// <param name="status">节点状态</param>
    /// <returns>是否需要关注</returns>
    public static bool RequiresAttention(this NodeStatus status)
    {
        return status == NodeStatus.Degraded ||
               status == NodeStatus.Overloaded ||
               status == NodeStatus.Faulty;
    }

    /// <summary>
    /// 获取状态的严重程度级别
    /// </summary>
    /// <param name="status">节点状态</param>
    /// <returns>严重程度级别 (0-4, 0最轻，4最严重)</returns>
    public static int GetSeverityLevel(this NodeStatus status)
    {
        return status switch
        {
            NodeStatus.Healthy => 0,
            NodeStatus.Initializing => 1,
            NodeStatus.Degraded => 2,
            NodeStatus.Overloaded => 2,
            NodeStatus.Maintenance => 2,
            NodeStatus.Faulty => 3,
            NodeStatus.Offline => 4,
            NodeStatus.Shutting_Down => 3,
            NodeStatus.Unknown => 4,
            _ => 4
        };
    }

    /// <summary>
    /// 获取状态的描述
    /// </summary>
    /// <param name="status">节点状态</param>
    /// <returns>状态描述</returns>
    public static string GetDescription(this NodeStatus status)
    {
        return status switch
        {
            NodeStatus.Unknown => "未知状态",
            NodeStatus.Initializing => "初始化中",
            NodeStatus.Healthy => "健康运行",
            NodeStatus.Degraded => "性能降级",
            NodeStatus.Overloaded => "过载状态",
            NodeStatus.Maintenance => "维护模式",
            NodeStatus.Faulty => "故障状态",
            NodeStatus.Offline => "离线状态",
            NodeStatus.Shutting_Down => "正在关闭",
            _ => "未定义状态"
        };
    }
}
