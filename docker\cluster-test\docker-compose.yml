services:
  # NATS集群 (3节点) - 测试环境端口 +20000
  nats-server-1:
    image: nats:2.11.8-alpine
    container_name: nats-test-server-1
    ports:
      - "24222:4222"
      - "28222:8222"
    volumes:
      - ../testing/nats/nats-1.conf:/nats-server.conf:ro
      - nats_data_1:/data
    command: ["-c", "/nats-server.conf"]
    networks:
      - flowcustom-test

  nats-server-2:
    image: nats:2.11.8-alpine
    container_name: nats-test-server-2
    ports:
      - "24223:4222"
      - "28223:8222"
    volumes:
      - ../testing/nats/nats-2.conf:/nats-server.conf:ro
      - nats_data_2:/data
    command: ["-c", "/nats-server.conf"]
    networks:
      - flowcustom-test
    depends_on:
      - nats-server-1

  nats-server-3:
    image: nats:2.11.8-alpine
    container_name: nats-test-server-3
    ports:
      - "24224:4222"
      - "28224:8222"
    volumes:
      - ../testing/nats/nats-3.conf:/nats-server.conf:ro
      - nats_data_3:/data
    command: ["-c", "/nats-server.conf"]
    networks:
      - flowcustom-test
    depends_on:
      - nats-server-1

  # MySQL数据库 - 测试环境端口 +20000
  mysql:
    image: mysql:8.0
    container_name: mysql-test
    environment:
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_DATABASE: flowcustom_test
      MYSQL_USER: flowcustom
      MYSQL_PASSWORD: FlowCustom@2025
    ports:
      - "23306:3306"
    volumes:
      - mysql_test_data:/var/lib/mysql
      - ../mysql/init:/docker-entrypoint-initdb.d
    networks:
      - flowcustom-test

  # API节点 (单角色) - 测试环境端口 +20000
  api-node:
    build:
      context: ../../
      dockerfile: docker/cluster-test/Dockerfile.api
    container_name: flowcustom-api-node
    environment:
      - ASPNETCORE_ENVIRONMENT=Testing
      - ASPNETCORE_URLS=http://+:5000
    ports:
      - "25000:5000"
    networks:
      - flowcustom-test
    depends_on:
      - nats-server-1
      - nats-server-2
      - nats-server-3
      - mysql

  # Worker节点 (单角色)
  worker-node:
    build:
      context: ../../
      dockerfile: docker/cluster-test/Dockerfile.worker
    container_name: flowcustom-worker-node
    environment:
      - ASPNETCORE_ENVIRONMENT=Testing
    networks:
      - flowcustom-test
    depends_on:
      - nats-server-1
      - nats-server-2
      - nats-server-3
      - mysql

  # Designer节点 (单角色)
  designer-node:
    build:
      context: ../../
      dockerfile: docker/cluster-test/Dockerfile.designer
    container_name: flowcustom-designer-node
    environment:
      - ASPNETCORE_ENVIRONMENT=Testing
    networks:
      - flowcustom-test
    depends_on:
      - nats-server-1
      - nats-server-2
      - nats-server-3
      - mysql

  # Validator节点 (单角色)
  validator-node:
    build:
      context: ../../
      dockerfile: docker/cluster-test/Dockerfile.validator
    container_name: flowcustom-validator-node
    environment:
      - ASPNETCORE_ENVIRONMENT=Testing
    networks:
      - flowcustom-test
    depends_on:
      - nats-server-1
      - nats-server-2
      - nats-server-3
      - mysql

  # Executor节点 (单角色)
  executor-node:
    build:
      context: ../../
      dockerfile: docker/cluster-test/Dockerfile.executor
    container_name: flowcustom-executor-node
    environment:
      - ASPNETCORE_ENVIRONMENT=Testing
    networks:
      - flowcustom-test
    depends_on:
      - nats-server-1
      - nats-server-2
      - nats-server-3
      - mysql

  # Monitor节点 (单角色)
  monitor-node:
    build:
      context: ../../
      dockerfile: docker/cluster-test/Dockerfile.monitor
    container_name: flowcustom-monitor-node
    environment:
      - ASPNETCORE_ENVIRONMENT=Testing
    networks:
      - flowcustom-test
    depends_on:
      - nats-server-1
      - nats-server-2
      - nats-server-3
      - mysql

  # Scheduler节点 (单角色)
  scheduler-node:
    build:
      context: ../../
      dockerfile: docker/cluster-test/Dockerfile.scheduler
    container_name: flowcustom-scheduler-node
    environment:
      - ASPNETCORE_ENVIRONMENT=Testing
    networks:
      - flowcustom-test
    depends_on:
      - nats-server-1
      - nats-server-2
      - nats-server-3
      - mysql

  # 多角色节点 (Api + Worker + Designer) - 测试环境端口 +20000
  multi-role-node:
    build:
      context: ../../
      dockerfile: docker/cluster-test/Dockerfile.multi
    container_name: flowcustom-multi-node
    environment:
      - ASPNETCORE_ENVIRONMENT=Testing
      - ASPNETCORE_URLS=http://+:5000
    ports:
      - "25001:5000"
    networks:
      - flowcustom-test
    depends_on:
      - nats-server-1
      - nats-server-2
      - nats-server-3
      - mysql

volumes:
  mysql_test_data:
  nats_data_1:
  nats_data_2:
  nats_data_3:

networks:
  flowcustom-test:
    driver: bridge
