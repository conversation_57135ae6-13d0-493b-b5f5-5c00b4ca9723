import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  Form,
  Input,
  Select,
  Button,
  Space,
  message,
  Radio,
  Switch,
  Divider,
  Alert
} from 'antd';
import {
  ArrowLeftOutlined,

  SettingOutlined,
  CheckOutlined
} from '@ant-design/icons';
import { workflowApi } from '@/services/workflow';
import type { WorkflowDefinition } from '@/types/api';
import { PublishStatus } from '@/types/api';

const { TextArea } = Input;
const { Option } = Select;

interface CreateWorkflowForm {
  name: string;
  description?: string;
  category?: string;
  tags?: string[];
  publishStatus: PublishStatus;
  isTemplate: boolean;
  priority: number;
  timeout?: number;
  retryCount: number;
  enableLogging: boolean;
  enableNotification: boolean;
}

const WorkflowCreate: React.FC = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm<CreateWorkflowForm>();
  const [loading, setLoading] = useState(false);
  const [createAndEdit, setCreateAndEdit] = useState(false);

  // 预定义的分类选项
  const categories = [
    '数据处理',
    '业务流程',
    '系统集成',
    '监控告警',
    '定时任务',
    '其他'
  ];

  // 预定义的标签选项
  const tagOptions = [
    '自动化', '数据同步', '报表生成', '邮件通知', 
    '文件处理', '数据库操作', 'API调用', '批处理',
    '实时处理', '定时执行', '条件触发', '人工审批'
  ];

  // 创建工作流
  const handleSubmit = async (values: CreateWorkflowForm) => {
    try {
      setLoading(true);

      // 构建工作流定义
      const workflowData: Partial<WorkflowDefinition> = {
        name: values.name,
        description: values.description,

        tags: values.tags,
        publishStatus: values.publishStatus,






        version: '1.0.0',

      };

      const createdWorkflow = await workflowApi.createWorkflow(workflowData);
      message.success('工作流创建成功');

      if (createAndEdit) {
        // 创建后直接进入设计器
        navigate(`/workflow/designer/${createdWorkflow.workflowId}`);
      } else {
        // 返回列表页面
        navigate('/workflow/list');
      }
    } catch (error) {
      console.error('创建工作流失败:', error);
      message.error('创建工作流失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      {/* 页面头部 */}
      <div className="mb-6">
        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center">
            <Button 
              icon={<ArrowLeftOutlined />} 
              onClick={() => navigate('/workflow/list')}
              className="mr-3"
            >
              返回列表
            </Button>
            <div>
              <h1 className="text-2xl font-semibold text-gray-900 mb-1">
                创建工作流
              </h1>
              <p className="text-gray-600 text-sm">填写基本信息创建新的工作流</p>
            </div>
          </div>
        </div>

        <Alert
          message="创建提示"
          description="创建工作流后，您可以选择直接进入可视化设计器进行流程设计，或稍后在列表中编辑。"
          type="info"
          showIcon
          className="mb-6"
        />
      </div>

      <Card>
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            publishStatus: PublishStatus.Draft,
            isTemplate: false,
            priority: 1,
            retryCount: 0,
            enableLogging: true,
            enableNotification: false
          }}
        >
          <div className="grid grid-cols-2 gap-6">
            {/* 左侧：基本信息 */}
            <div>
              <h3 className="text-lg font-medium mb-4">基本信息</h3>
              
              <Form.Item
                label="工作流名称"
                name="name"
                rules={[
                  { required: true, message: '请输入工作流名称' },
                  { min: 2, max: 50, message: '名称长度应在2-50个字符之间' }
                ]}
              >
                <Input placeholder="请输入工作流名称" />
              </Form.Item>

              <Form.Item
                label="描述"
                name="description"
                rules={[
                  { max: 200, message: '描述长度不能超过200个字符' }
                ]}
              >
                <TextArea 
                  rows={3} 
                  placeholder="请输入工作流描述（可选）"
                  showCount
                  maxLength={200}
                />
              </Form.Item>

              <Form.Item
                label="分类"
                name="category"
              >
                <Select placeholder="请选择工作流分类" allowClear>
                  {categories.map(category => (
                    <Option key={category} value={category}>
                      {category}
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item
                label="标签"
                name="tags"
              >
                <Select
                  mode="multiple"
                  placeholder="请选择相关标签"
                  options={tagOptions.map(tag => ({ label: tag, value: tag }))}
                  maxTagCount={5}
                />
              </Form.Item>

              <Form.Item
                label="状态"
                name="publishStatus"
              >
                <Radio.Group>
                  <Radio value={PublishStatus.Draft}>草稿</Radio>
                  <Radio value={PublishStatus.Published}>已发布</Radio>
                </Radio.Group>
              </Form.Item>

              <Form.Item
                label="设为模板"
                name="isTemplate"
                valuePropName="checked"
              >
                <Switch checkedChildren="是" unCheckedChildren="否" />
              </Form.Item>
            </div>

            {/* 右侧：高级配置 */}
            <div>
              <h3 className="text-lg font-medium mb-4">高级配置</h3>

              <Form.Item
                label="优先级"
                name="priority"
                help="数值越大优先级越高，范围：1-10"
              >
                <Select>
                  {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map(num => (
                    <Option key={num} value={num}>
                      {num} {num <= 3 ? '(低)' : num <= 7 ? '(中)' : '(高)'}
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item
                label="超时时间（秒）"
                name="timeout"
                help="工作流执行的最大时间限制，0表示无限制"
              >
                <Input 
                  type="number" 
                  min={0} 
                  max={86400}
                  placeholder="300"
                  addonAfter="秒"
                />
              </Form.Item>

              <Form.Item
                label="重试次数"
                name="retryCount"
                help="执行失败时的自动重试次数"
              >
                <Select>
                  {[0, 1, 2, 3, 5].map(num => (
                    <Option key={num} value={num}>
                      {num === 0 ? '不重试' : `${num}次`}
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              <Divider />

              <Form.Item
                label="启用日志记录"
                name="enableLogging"
                valuePropName="checked"
              >
                <Switch 
                  checkedChildren="开启" 
                  unCheckedChildren="关闭"
                />
              </Form.Item>

              <Form.Item
                label="启用通知"
                name="enableNotification"
                valuePropName="checked"
                help="执行完成后发送通知"
              >
                <Switch 
                  checkedChildren="开启" 
                  unCheckedChildren="关闭"
                />
              </Form.Item>
            </div>
          </div>

          <Divider />

          {/* 操作按钮 */}
          <div className="flex justify-between items-center">
            <div>
              <Switch
                checked={createAndEdit}
                onChange={setCreateAndEdit}
                checkedChildren="创建后进入设计器"
                unCheckedChildren="创建后返回列表"
              />
            </div>
            <Space>
              <Button onClick={() => navigate('/workflow/list')}>
                取消
              </Button>
              <Button 
                type="primary" 
                htmlType="submit" 
                loading={loading}
                icon={createAndEdit ? <SettingOutlined /> : <CheckOutlined />}
              >
                {createAndEdit ? '创建并设计' : '创建工作流'}
              </Button>
            </Space>
          </div>
        </Form>
      </Card>
    </div>
  );
};

export default WorkflowCreate;
