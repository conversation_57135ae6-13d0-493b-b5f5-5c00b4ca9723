#!/usr/bin/env python3
"""
重新组织Docker配置文件结构
将NATS和MySQL配置移动到各环境目录中
"""

import os
import shutil
from pathlib import Path

def reorganize_docker_structure():
    """重新组织Docker目录结构"""
    print("🔄 重新组织Docker配置文件结构...")
    
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    docker_dir = project_root / "docker"
    
    # 创建新的目录结构
    environments = ["development", "testing", "production"]
    
    for env in environments:
        env_dir = docker_dir / env
        
        # 创建配置目录
        nats_config_dir = env_dir / "nats"
        mysql_config_dir = env_dir / "mysql"
        
        nats_config_dir.mkdir(exist_ok=True)
        mysql_config_dir.mkdir(exist_ok=True)
        
        print(f"✅ 创建 {env} 环境配置目录")
        
        # 移动NATS配置
        old_nats_config = docker_dir / "nats-cluster" / "config" / env
        if old_nats_config.exists():
            for config_file in old_nats_config.glob("*.conf"):
                dest_file = nats_config_dir / config_file.name
                shutil.copy2(config_file, dest_file)
                print(f"   📁 复制NATS配置: {config_file.name}")
        
        # 移动MySQL配置
        mysql_init_dir = mysql_config_dir / "init"
        mysql_init_dir.mkdir(exist_ok=True)
        
        old_mysql_init = docker_dir / "mysql" / "init"
        if old_mysql_init.exists():
            for init_file in old_mysql_init.glob("*"):
                if init_file.is_file():
                    dest_file = mysql_init_dir / init_file.name
                    shutil.copy2(init_file, dest_file)
                    print(f"   📁 复制MySQL初始化脚本: {init_file.name}")
    
    print("✅ Docker配置文件重组完成")

def update_docker_compose_files():
    """更新Docker Compose文件中的路径引用"""
    print("\n🔄 更新Docker Compose文件路径...")
    
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    docker_dir = project_root / "docker"
    
    environments = ["development", "testing", "production"]
    
    for env in environments:
        compose_file = docker_dir / env / "docker-compose.yml"
        if not compose_file.exists():
            continue
            
        print(f"🔄 更新 {env} 环境Docker Compose...")
        
        # 读取文件内容
        with open(compose_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 更新NATS配置路径
        if env == "development":
            content = content.replace(
                "../nats-cluster/config/development/nats-1.conf:/nats-server.conf:ro",
                "./nats/nats-1.conf:/nats-server.conf:ro"
            )
        else:
            for i in range(1, 4):
                content = content.replace(
                    f"../nats-cluster/config/{env}/nats-{i}.conf:/nats-server.conf:ro",
                    f"./nats/nats-{i}.conf:/nats-server.conf:ro"
                )
        
        # 更新MySQL配置路径
        content = content.replace(
            "../mysql/init:/docker-entrypoint-initdb.d",
            "./mysql/init:/docker-entrypoint-initdb.d"
        )
        
        # 写回文件
        with open(compose_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ {env} 环境Docker Compose更新完成")

def cleanup_old_structure():
    """清理旧的目录结构"""
    print("\n🧹 清理旧的目录结构...")
    
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    docker_dir = project_root / "docker"
    
    # 删除旧的nats-cluster目录
    old_nats_dir = docker_dir / "nats-cluster"
    if old_nats_dir.exists():
        shutil.rmtree(old_nats_dir)
        print("✅ 删除旧的nats-cluster目录")
    
    # 删除旧的mysql目录
    old_mysql_dir = docker_dir / "mysql"
    if old_mysql_dir.exists():
        shutil.rmtree(old_mysql_dir)
        print("✅ 删除旧的mysql目录")

def main():
    """主函数"""
    print("🚀 Docker配置文件重组")
    print("=" * 40)
    
    try:
        # 重组目录结构
        reorganize_docker_structure()
        
        # 更新Docker Compose文件
        update_docker_compose_files()
        
        # 清理旧结构
        cleanup_old_structure()
        
        print("\n🎉 Docker配置重组完成!")
        print("\n📁 新的目录结构:")
        print("docker/")
        print("├── development/")
        print("│   ├── nats/")
        print("│   ├── mysql/")
        print("│   └── docker-compose.yml")
        print("├── testing/")
        print("│   ├── nats/")
        print("│   ├── mysql/")
        print("│   └── docker-compose.yml")
        print("└── production/")
        print("    ├── nats/")
        print("    ├── mysql/")
        print("    └── docker-compose.yml")
        
    except Exception as e:
        print(f"❌ 重组过程中出现错误: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
