namespace FlowCustomV1.Core.Models.Cluster;

/// <summary>
/// 节点运行模式
/// 定义节点在集群中的角色和运行方式
/// 支持传统Master-Worker模式和新的角色化模式
/// </summary>
public enum NodeMode
{
    /// <summary>
    /// 独立模式 - 单节点运行，不参与集群
    /// 适用于开发环境或小规模部署
    /// </summary>
    Standalone = 0,

    /// <summary>
    /// 主节点模式 - 集群管理节点
    /// 负责集群协调、任务调度和状态管理
    /// </summary>
    Master = 1,

    /// <summary>
    /// 工作节点模式 - 纯执行节点
    /// 专门负责执行工作流任务，不参与集群管理
    /// </summary>
    Worker = 2,

    /// <summary>
    /// 混合模式 - 既是主节点又是工作节点
    /// 同时具备管理和执行能力，适用于中小规模集群
    /// </summary>
    Hybrid = 3,

    /// <summary>
    /// 代理模式 - 负载均衡和路由
    /// 负责请求路由、负载均衡和服务发现
    /// </summary>
    Proxy = 4,

    /// <summary>
    /// 角色化模式 - 基于功能角色的节点模式
    /// 节点根据配置的功能角色提供专业化服务
    /// </summary>
    RoleBased = 5
}

/// <summary>
/// NodeMode 扩展方法
/// </summary>
public static class NodeModeExtensions
{
    /// <summary>
    /// 判断节点是否可以执行任务
    /// </summary>
    /// <param name="mode">节点模式</param>
    /// <returns>是否可以执行任务</returns>
    public static bool CanExecuteTasks(this NodeMode mode)
    {
        return mode == NodeMode.Standalone ||
               mode == NodeMode.Worker ||
               mode == NodeMode.Hybrid;
    }

    /// <summary>
    /// 判断节点是否可以管理集群
    /// </summary>
    /// <param name="mode">节点模式</param>
    /// <returns>是否可以管理集群</returns>
    public static bool CanManageCluster(this NodeMode mode)
    {
        return mode == NodeMode.Master ||
               mode == NodeMode.Hybrid;
    }

    /// <summary>
    /// 判断节点是否可以路由请求
    /// </summary>
    /// <param name="mode">节点模式</param>
    /// <returns>是否可以路由请求</returns>
    public static bool CanRouteRequests(this NodeMode mode)
    {
        return mode == NodeMode.Proxy ||
               mode == NodeMode.Master ||
               mode == NodeMode.Hybrid;
    }

    /// <summary>
    /// 获取节点模式的描述
    /// </summary>
    /// <param name="mode">节点模式</param>
    /// <returns>模式描述</returns>
    public static string GetDescription(this NodeMode mode)
    {
        return mode switch
        {
            NodeMode.Standalone => "独立模式 - 单节点运行",
            NodeMode.Master => "主节点模式 - 集群管理",
            NodeMode.Worker => "工作节点模式 - 任务执行",
            NodeMode.Hybrid => "混合模式 - 管理+执行",
            NodeMode.Proxy => "代理模式 - 负载均衡",
            _ => "未知模式"
        };
    }
}
