using Microsoft.EntityFrameworkCore;
using FlowCustomV1.Infrastructure.Entities;

namespace FlowCustomV1.Infrastructure.Data;

/// <summary>
/// 工作流数据库上下文
/// </summary>
public class WorkflowDbContext : DbContext
{
    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="options">数据库上下文选项</param>
    public WorkflowDbContext(DbContextOptions<WorkflowDbContext> options) : base(options)
    {
    }

    /// <summary>
    /// 工作流定义表
    /// </summary>
    public DbSet<WorkflowDefinitionEntity> WorkflowDefinitions { get; set; } = null!;

    /// <summary>
    /// 工作流实例表
    /// </summary>
    public DbSet<WorkflowInstanceEntity> WorkflowInstances { get; set; } = null!;

    /// <summary>
    /// 节点执行记录表
    /// </summary>
    public DbSet<NodeExecutionEntity> NodeExecutions { get; set; } = null!;

    /// <summary>
    /// 配置模型
    /// </summary>
    /// <param name="modelBuilder">模型构建器</param>
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // 配置工作流定义实体
        ConfigureWorkflowDefinition(modelBuilder);

        // 配置工作流实例实体
        ConfigureWorkflowInstance(modelBuilder);

        // 配置节点执行实体
        ConfigureNodeExecution(modelBuilder);
    }

    /// <summary>
    /// 配置工作流定义实体
    /// </summary>
    /// <param name="modelBuilder">模型构建器</param>
    private static void ConfigureWorkflowDefinition(ModelBuilder modelBuilder)
    {
        var entity = modelBuilder.Entity<WorkflowDefinitionEntity>();

        // 主键
        entity.HasKey(e => e.Id);

        // 唯一索引
        entity.HasIndex(e => e.WorkflowId)
            .IsUnique()
            .HasDatabaseName("IX_WorkflowDefinitions_WorkflowId");

        // 复合索引
        entity.HasIndex(e => new { e.Name, e.Version })
            .HasDatabaseName("IX_WorkflowDefinitions_Name_Version");

        entity.HasIndex(e => e.Author)
            .HasDatabaseName("IX_WorkflowDefinitions_Author");

        entity.HasIndex(e => e.CreatedAt)
            .HasDatabaseName("IX_WorkflowDefinitions_CreatedAt");

        entity.HasIndex(e => e.IsActive)
            .HasDatabaseName("IX_WorkflowDefinitions_IsActive");

        // 属性配置
        entity.Property(e => e.WorkflowId)
            .IsRequired()
            .HasMaxLength(50);

        entity.Property(e => e.Name)
            .IsRequired()
            .HasMaxLength(200);

        entity.Property(e => e.Version)
            .IsRequired()
            .HasMaxLength(20);

        entity.Property(e => e.Author)
            .HasMaxLength(100);

        entity.Property(e => e.DefinitionJson)
            .IsRequired();

        entity.Property(e => e.CreatedAt)
            .IsRequired();

        entity.Property(e => e.LastModifiedAt)
            .IsRequired();
    }

    /// <summary>
    /// 配置工作流实例实体
    /// </summary>
    /// <param name="modelBuilder">模型构建器</param>
    private static void ConfigureWorkflowInstance(ModelBuilder modelBuilder)
    {
        var entity = modelBuilder.Entity<WorkflowInstanceEntity>();

        // 主键
        entity.HasKey(e => e.Id);

        // 唯一索引
        entity.HasIndex(e => e.InstanceId)
            .IsUnique()
            .HasDatabaseName("IX_WorkflowInstances_InstanceId");

        // 外键索引
        entity.HasIndex(e => e.WorkflowId)
            .HasDatabaseName("IX_WorkflowInstances_WorkflowId");

        entity.HasIndex(e => e.State)
            .HasDatabaseName("IX_WorkflowInstances_State");

        entity.HasIndex(e => e.StartedAt)
            .HasDatabaseName("IX_WorkflowInstances_StartedAt");

        entity.HasIndex(e => e.CompletedAt)
            .HasDatabaseName("IX_WorkflowInstances_CompletedAt");

        // 外键关系
        entity.HasOne(e => e.WorkflowDefinition)
            .WithMany(d => d.Instances)
            .HasForeignKey(e => e.WorkflowId)
            .HasPrincipalKey(d => d.WorkflowId)
            .OnDelete(DeleteBehavior.Cascade);

        // 属性配置
        entity.Property(e => e.InstanceId)
            .IsRequired()
            .HasMaxLength(50);

        entity.Property(e => e.WorkflowId)
            .IsRequired()
            .HasMaxLength(50);

        entity.Property(e => e.State)
            .IsRequired()
            .HasConversion<int>();

        entity.Property(e => e.StartedAt)
            .IsRequired();
    }

    /// <summary>
    /// 配置节点执行实体
    /// </summary>
    /// <param name="modelBuilder">模型构建器</param>
    private static void ConfigureNodeExecution(ModelBuilder modelBuilder)
    {
        var entity = modelBuilder.Entity<NodeExecutionEntity>();

        // 主键
        entity.HasKey(e => e.Id);

        // 索引
        entity.HasIndex(e => e.ExecutionId)
            .HasDatabaseName("IX_NodeExecutions_ExecutionId");

        entity.HasIndex(e => e.InstanceId)
            .HasDatabaseName("IX_NodeExecutions_InstanceId");

        entity.HasIndex(e => new { e.InstanceId, e.NodeId })
            .HasDatabaseName("IX_NodeExecutions_InstanceId_NodeId");

        entity.HasIndex(e => e.State)
            .HasDatabaseName("IX_NodeExecutions_State");

        entity.HasIndex(e => e.StartedAt)
            .HasDatabaseName("IX_NodeExecutions_StartedAt");

        // 外键关系
        entity.HasOne(e => e.WorkflowInstance)
            .WithMany(i => i.NodeExecutions)
            .HasForeignKey(e => e.InstanceId)
            .HasPrincipalKey(i => i.InstanceId)
            .OnDelete(DeleteBehavior.Cascade);

        // 属性配置
        entity.Property(e => e.ExecutionId)
            .IsRequired()
            .HasMaxLength(50);

        entity.Property(e => e.InstanceId)
            .IsRequired()
            .HasMaxLength(50);

        entity.Property(e => e.NodeId)
            .IsRequired()
            .HasMaxLength(50);

        entity.Property(e => e.NodeType)
            .IsRequired()
            .HasMaxLength(50);

        entity.Property(e => e.State)
            .IsRequired()
            .HasConversion<int>();

        entity.Property(e => e.StartedAt)
            .IsRequired();
    }
}
