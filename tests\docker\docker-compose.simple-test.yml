# FlowCustomV1 v******* 简化测试环境
# 单NATS节点 + MySQL + 基础应用节点

services:
  # ===========================================
  # 基础设施层
  # ===========================================
  nats:
    image: nats:2.11.8-alpine
    container_name: flowcustom-simple-test-nats
    ports:
      - "24222:4222"
      - "28222:8222"
    command: ["-js", "-m", "8222"]
    networks:
      - flowcustom-simple-test
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8222/healthz"]
      interval: 10s
      timeout: 5s
      retries: 3

  mysql:
    image: mysql:8.0
    container_name: flowcustom-simple-test-mysql
    environment:
      MYSQL_ROOT_PASSWORD: SimpleTestPassword123!
      MYSQL_DATABASE: flowcustom_simple_test
      MYSQL_USER: flowcustom
      MYSQL_PASSWORD: SimpleTestPassword123!
    ports:
      - "23307:3306"
    volumes:
      - mysql-simple-test-data:/var/lib/mysql
      - ./mysql-config/init-full-test.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - flowcustom-simple-test
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-pSimpleTestPassword123!"]
      interval: 10s
      timeout: 5s
      retries: 5

  # ===========================================
  # 测试协调器 - 执行基础测试
  # ===========================================
  test-coordinator:
    image: python:3.11-slim
    container_name: flowcustom-simple-test-coordinator
    working_dir: /app
    environment:
      - TEST_ENVIRONMENT=SimpleTest
      - NATS_SERVERS=nats://nats:4222
      - MYSQL_CONNECTION=Server=mysql;Database=flowcustom_simple_test;Uid=flowcustom;Pwd=SimpleTestPassword123!;
    volumes:
      - ./test-scripts:/app/test-scripts
      - ./test-results:/app/test-results
    command: >
      bash -c "
        pip install requests aiohttp asyncio nats-py mysql-connector-python &&
        echo '🚀 开始FlowCustomV1基础设施测试...' &&
        python /app/test-scripts/test_basic_infrastructure.py
      "
    depends_on:
      nats:
        condition: service_healthy
      mysql:
        condition: service_healthy
    networks:
      - flowcustom-simple-test

networks:
  flowcustom-simple-test:
    driver: bridge

volumes:
  mysql-simple-test-data:
    driver: local
