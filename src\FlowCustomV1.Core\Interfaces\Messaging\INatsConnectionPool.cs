namespace FlowCustomV1.Core.Interfaces.Messaging;

/// <summary>
/// NATS连接池接口
/// 提供连接池管理和连接复用功能
/// </summary>
public interface INatsConnectionPool : IDisposable
{
    #region 连接池状态

    /// <summary>
    /// 连接池是否已初始化
    /// </summary>
    bool IsInitialized { get; }

    /// <summary>
    /// 当前活跃连接数
    /// </summary>
    int ActiveConnections { get; }

    /// <summary>
    /// 空闲连接数
    /// </summary>
    int IdleConnections { get; }

    /// <summary>
    /// 最大连接数
    /// </summary>
    int MaxConnections { get; }

    #endregion

    #region 连接管理

    /// <summary>
    /// 初始化连接池
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>初始化任务</returns>
    Task InitializeAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取连接
    /// </summary>
    /// <param name="timeout">获取超时时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>连接对象</returns>
    Task<IPooledNatsConnection> AcquireConnectionAsync(TimeSpan? timeout = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 释放连接回池中
    /// </summary>
    /// <param name="connection">连接对象</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>释放任务</returns>
    Task ReleaseConnectionAsync(IPooledNatsConnection connection, CancellationToken cancellationToken = default);

    /// <summary>
    /// 关闭连接池
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>关闭任务</returns>
    Task ShutdownAsync(CancellationToken cancellationToken = default);

    #endregion

    #region 健康检查

    /// <summary>
    /// 检查连接池健康状态
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>健康检查结果</returns>
    Task<ConnectionPoolHealthStatus> CheckHealthAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 清理无效连接
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>清理任务</returns>
    Task CleanupInvalidConnectionsAsync(CancellationToken cancellationToken = default);

    #endregion

    #region 事件

    /// <summary>
    /// 连接池状态变更事件
    /// </summary>
    event EventHandler<ConnectionPoolStatusChangedEventArgs>? StatusChanged;

    /// <summary>
    /// 连接创建事件
    /// </summary>
    event EventHandler<ConnectionCreatedEventArgs>? ConnectionCreated;

    /// <summary>
    /// 连接销毁事件
    /// </summary>
    event EventHandler<ConnectionDestroyedEventArgs>? ConnectionDestroyed;

    #endregion
}

/// <summary>
/// 池化NATS连接接口
/// </summary>
public interface IPooledNatsConnection : IDisposable
{
    /// <summary>
    /// 连接ID
    /// </summary>
    string ConnectionId { get; }

    /// <summary>
    /// 底层NATS服务
    /// </summary>
    INatsService NatsService { get; }

    /// <summary>
    /// 连接是否有效
    /// </summary>
    bool IsValid { get; }

    /// <summary>
    /// 创建时间
    /// </summary>
    DateTime CreatedAt { get; }

    /// <summary>
    /// 最后使用时间
    /// </summary>
    DateTime LastUsedAt { get; }

    /// <summary>
    /// 使用次数
    /// </summary>
    int UseCount { get; }

    /// <summary>
    /// 标记连接为已使用
    /// </summary>
    void MarkAsUsed();

    /// <summary>
    /// 重置连接状态
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>重置任务</returns>
    Task ResetAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// 连接池健康状态
/// </summary>
public class ConnectionPoolHealthStatus
{
    /// <summary>
    /// 是否健康
    /// </summary>
    public bool IsHealthy { get; set; }

    /// <summary>
    /// 总连接数
    /// </summary>
    public int TotalConnections { get; set; }

    /// <summary>
    /// 活跃连接数
    /// </summary>
    public int ActiveConnections { get; set; }

    /// <summary>
    /// 空闲连接数
    /// </summary>
    public int IdleConnections { get; set; }

    /// <summary>
    /// 无效连接数
    /// </summary>
    public int InvalidConnections { get; set; }

    /// <summary>
    /// 平均响应时间（毫秒）
    /// </summary>
    public double AverageResponseTime { get; set; }

    /// <summary>
    /// 检查时间
    /// </summary>
    public DateTime CheckedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 详细信息
    /// </summary>
    public Dictionary<string, object> Details { get; set; } = new();
}

/// <summary>
/// 连接池状态变更事件参数
/// </summary>
public class ConnectionPoolStatusChangedEventArgs : EventArgs
{
    /// <summary>
    /// 旧状态
    /// </summary>
    public string OldStatus { get; set; } = string.Empty;

    /// <summary>
    /// 新状态
    /// </summary>
    public string NewStatus { get; set; } = string.Empty;

    /// <summary>
    /// 活跃连接数
    /// </summary>
    public int ActiveConnections { get; set; }

    /// <summary>
    /// 空闲连接数
    /// </summary>
    public int IdleConnections { get; set; }

    /// <summary>
    /// 变更时间
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 连接创建事件参数
/// </summary>
public class ConnectionCreatedEventArgs : EventArgs
{
    /// <summary>
    /// 连接ID
    /// </summary>
    public string ConnectionId { get; set; } = string.Empty;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 连接销毁事件参数
/// </summary>
public class ConnectionDestroyedEventArgs : EventArgs
{
    /// <summary>
    /// 连接ID
    /// </summary>
    public string ConnectionId { get; set; } = string.Empty;

    /// <summary>
    /// 销毁原因
    /// </summary>
    public string Reason { get; set; } = string.Empty;

    /// <summary>
    /// 连接存活时间
    /// </summary>
    public TimeSpan Lifetime { get; set; }

    /// <summary>
    /// 使用次数
    /// </summary>
    public int UseCount { get; set; }

    /// <summary>
    /// 销毁时间
    /// </summary>
    public DateTime DestroyedAt { get; set; } = DateTime.UtcNow;
}
