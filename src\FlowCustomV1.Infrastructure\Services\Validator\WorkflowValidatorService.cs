using System.Collections.Concurrent;
using System.Diagnostics;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using FlowCustomV1.Core.Interfaces.Validator;
using FlowCustomV1.Core.Interfaces;
using FlowCustomV1.Core.Models.Workflow;
using Microsoft.Extensions.Logging;

namespace FlowCustomV1.Infrastructure.Services.Validator;

/// <summary>
/// 工作流验证器服务实现
/// 提供分布式工作流验证功能，支持验证规则引擎和结果缓存
/// </summary>
public class WorkflowValidatorService : IWorkflowValidatorService
{
    private readonly ILogger<WorkflowValidatorService> _logger;
    private readonly IDistributedValidationRuleEngine _ruleEngine;
    private readonly IWorkflowValidator _workflowValidator;
    private readonly ConcurrentDictionary<string, WorkflowValidationResult> _validationCache;
    private readonly ConcurrentDictionary<string, IValidationRule> _customRules;
    private readonly ValidationStatistics _statistics;
    private readonly object _lockObject = new();

    private ValidatorServiceStatus _status = ValidatorServiceStatus.NotStarted;
    private CancellationTokenSource? _cancellationTokenSource;

    /// <summary>
    /// 构造函数
    /// </summary>
    public WorkflowValidatorService(
        ILogger<WorkflowValidatorService> logger,
        IDistributedValidationRuleEngine ruleEngine,
        IWorkflowValidator workflowValidator)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _ruleEngine = ruleEngine ?? throw new ArgumentNullException(nameof(ruleEngine));
        _workflowValidator = workflowValidator ?? throw new ArgumentNullException(nameof(workflowValidator));
        
        _validationCache = new ConcurrentDictionary<string, WorkflowValidationResult>();
        _customRules = new ConcurrentDictionary<string, IValidationRule>();
        _statistics = new ValidationStatistics();
    }

    /// <inheritdoc />
    public async Task<WorkflowValidationResult> ValidateWorkflowAsync(WorkflowDefinition workflowDefinition, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(workflowDefinition);

        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            _logger.LogDebug("Starting workflow validation for {WorkflowId}", workflowDefinition.WorkflowId);

            // 计算工作流哈希值
            var workflowHash = ComputeWorkflowHash(workflowDefinition);

            // 尝试从缓存获取结果
            var cachedResult = await GetCachedValidationAsync(workflowHash, cancellationToken);
            if (cachedResult != null)
            {
                _logger.LogDebug("Using cached validation result for workflow {WorkflowId}", workflowDefinition.WorkflowId);
                UpdateStatistics(true, stopwatch.ElapsedMilliseconds, true);
                return cachedResult;
            }

            // 执行验证
            var validationResult = await PerformValidationAsync(workflowDefinition, cancellationToken);

            // 缓存结果
            await CacheValidationResultAsync(workflowHash, validationResult, cancellationToken);

            UpdateStatistics(validationResult.IsValid, stopwatch.ElapsedMilliseconds, false);

            _logger.LogInformation("Workflow validation completed for {WorkflowId}: {IsValid}", 
                workflowDefinition.WorkflowId, validationResult.IsValid);

            return validationResult;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating workflow {WorkflowId}", workflowDefinition.WorkflowId);
            UpdateStatistics(false, stopwatch.ElapsedMilliseconds, false);
            
            return new WorkflowValidationResult
            {
                IsValid = false,
                WorkflowId = workflowDefinition.WorkflowId,
                ValidationErrors = new List<ValidationError>
                {
                    new ValidationError
                    {
                        Code = "VALIDATION_ERROR",
                        Message = $"Validation failed: {ex.Message}",
                        Severity = ValidationSeverity.Error
                    }
                },
                ValidatedAt = DateTime.UtcNow
            };
        }
    }

    /// <inheritdoc />
    public async Task<NodeValidationResult> ValidateNodeAsync(NodeDefinition nodeDefinition, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(nodeDefinition);

        var stopwatch = Stopwatch.StartNew();

        try
        {
            _logger.LogDebug("Starting node validation for {NodeType}", nodeDefinition.NodeType);

            // 使用基础验证器进行节点验证
            var result = await _workflowValidator.ValidateNodeDefinitionAsync(nodeDefinition, cancellationToken);

            // 执行分布式验证规则
            var context = new Core.Interfaces.Validator.ValidationContext
            {
                NodeDefinition = nodeDefinition,
                ValidationType = ValidationType.Node
            };

            var ruleResult = await _ruleEngine.ExecuteRulesAsync(context, cancellationToken);
            
            // 合并验证结果
            if (!ruleResult.IsValid)
            {
                result.IsValid = false;
                result.ValidationErrors.AddRange(ruleResult.ValidationErrors);
            }

            _logger.LogDebug("Node validation completed for {NodeType}: {IsValid}",
                nodeDefinition.NodeType, result.IsValid);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating node {NodeType}", nodeDefinition.NodeType);
            
            return new NodeValidationResult
            {
                IsValid = false,
                NodeId = nodeDefinition.NodeType, // 使用NodeType作为标识
                ValidationErrors = new List<ValidationError>
                {
                    new ValidationError
                    {
                        Code = "NODE_VALIDATION_ERROR",
                        Message = $"Node validation failed: {ex.Message}",
                        Severity = ValidationSeverity.Error
                    }
                }
            };
        }
    }

    /// <inheritdoc />
    public async Task<BatchValidationResult> ValidateBatchAsync(IEnumerable<WorkflowDefinition> workflowDefinitions, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(workflowDefinitions);

        var workflows = workflowDefinitions.ToList();
        var stopwatch = Stopwatch.StartNew();
        var result = new BatchValidationResult
        {
            TotalCount = workflows.Count
        };

        _logger.LogInformation("Starting batch validation for {Count} workflows", workflows.Count);

        try
        {
            // 并行验证工作流
            var validationTasks = workflows.Select(async workflow =>
            {
                try
                {
                    var validationResult = await ValidateWorkflowAsync(workflow, cancellationToken);
                    return new { WorkflowId = workflow.WorkflowId, Result = validationResult };
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error in batch validation for workflow {WorkflowId}", workflow.WorkflowId);
                    return new
                    {
                        WorkflowId = workflow.WorkflowId,
                        Result = new WorkflowValidationResult
                        {
                            IsValid = false,
                            WorkflowId = workflow.WorkflowId,
                            ValidationErrors = new List<ValidationError>
                            {
                                new ValidationError
                                {
                                    Code = "BATCH_VALIDATION_ERROR",
                                    Message = $"Batch validation failed: {ex.Message}",
                                    Severity = ValidationSeverity.Error
                                }
                            }
                        }
                    };
                }
            });

            var validationResults = await Task.WhenAll(validationTasks);

            // 汇总结果
            foreach (var validationResult in validationResults)
            {
                result.Results[validationResult.WorkflowId] = validationResult.Result;
                
                if (validationResult.Result.IsValid)
                {
                    result.SuccessCount++;
                }
                else
                {
                    result.FailureCount++;
                }
            }

            stopwatch.Stop();
            result.TotalDurationMs = stopwatch.ElapsedMilliseconds;
            result.Summary = $"Validated {result.TotalCount} workflows: {result.SuccessCount} successful, {result.FailureCount} failed";

            _logger.LogInformation("Batch validation completed: {Summary}", result.Summary);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in batch validation");
            
            result.FailureCount = result.TotalCount;
            result.Summary = $"Batch validation failed: {ex.Message}";
            result.TotalDurationMs = stopwatch.ElapsedMilliseconds;
            
            return result;
        }
    }

    /// <inheritdoc />
    public async Task<CyclicDependencyResult> CheckCyclicDependencyAsync(WorkflowDefinition workflowDefinition, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(workflowDefinition);

        try
        {
            _logger.LogDebug("Checking cyclic dependencies for workflow {WorkflowId}", workflowDefinition.WorkflowId);

            // 使用基础验证器检查循环依赖
            var cyclicResult = await _workflowValidator.ValidateCyclicDependenciesAsync(workflowDefinition, cancellationToken);

            return new CyclicDependencyResult
            {
                HasCyclicDependency = cyclicResult.HasCyclicDependency,
                CyclicPaths = cyclicResult.CyclicPaths,
                Details = cyclicResult.HasCyclicDependency 
                    ? $"Found {cyclicResult.CyclicPaths.Count} cyclic dependencies"
                    : "No cyclic dependencies found"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking cyclic dependencies for workflow {WorkflowId}", workflowDefinition.WorkflowId);
            
            return new CyclicDependencyResult
            {
                HasCyclicDependency = false,
                Details = $"Error checking cyclic dependencies: {ex.Message}"
            };
        }
    }

    /// <inheritdoc />
    public async Task<PerformanceAnalysisResult> AnalyzePerformanceAsync(WorkflowDefinition workflowDefinition, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(workflowDefinition);

        try
        {
            _logger.LogDebug("Analyzing performance for workflow {WorkflowId}", workflowDefinition.WorkflowId);

            var result = new PerformanceAnalysisResult();

            // 分析节点数量和复杂度
            var nodeCount = workflowDefinition.Nodes?.Count ?? 0;
            var connectionCount = workflowDefinition.Connections?.Count ?? 0;

            // 计算复杂度评分 (1-10)
            result.ComplexityScore = Math.Min(10, Math.Max(1, (nodeCount + connectionCount) / 10));

            // 预估执行时间 (基于节点数量和类型)
            result.EstimatedExecutionTimeMs = nodeCount * 100 + connectionCount * 50;

            // 预估内存使用
            result.EstimatedMemoryUsageMb = Math.Max(64, nodeCount * 10);

            // 识别潜在瓶颈节点
            if (workflowDefinition.Nodes != null)
            {
                foreach (var node in workflowDefinition.Nodes)
                {
                    // 简单的瓶颈检测逻辑
                    if (node.NodeType?.Contains("Database") == true ||
                        node.NodeType?.Contains("Http") == true ||
                        node.NodeType?.Contains("File") == true)
                    {
                        result.BottleneckNodes.Add(node.NodeId);
                    }
                }
            }

            // 生成优化建议
            if (result.ComplexityScore > 7)
            {
                result.OptimizationSuggestions.Add("Consider breaking down the workflow into smaller sub-workflows");
            }

            if (result.BottleneckNodes.Count > 0)
            {
                result.OptimizationSuggestions.Add("Consider optimizing I/O operations in bottleneck nodes");
            }

            if (nodeCount > 50)
            {
                result.OptimizationSuggestions.Add("Consider using parallel execution for independent nodes");
            }

            _logger.LogDebug("Performance analysis completed for workflow {WorkflowId}: Score={Score}, Time={Time}ms",
                workflowDefinition.WorkflowId, result.ComplexityScore, result.EstimatedExecutionTimeMs);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error analyzing performance for workflow {WorkflowId}", workflowDefinition.WorkflowId);

            return new PerformanceAnalysisResult
            {
                ComplexityScore = 1,
                EstimatedExecutionTimeMs = 1000,
                EstimatedMemoryUsageMb = 64,
                OptimizationSuggestions = { $"Performance analysis failed: {ex.Message}" }
            };
        }
    }

    /// <inheritdoc />
    public async Task<WorkflowValidationResult?> GetCachedValidationAsync(string workflowHash, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(workflowHash);

        await Task.CompletedTask; // 保持异步接口一致性

        if (_validationCache.TryGetValue(workflowHash, out var cachedResult))
        {
            // 检查缓存是否过期 (假设缓存有效期为1小时)
            if (DateTime.UtcNow - cachedResult.ValidatedAt < TimeSpan.FromHours(1))
            {
                return cachedResult;
            }
            else
            {
                // 移除过期缓存
                _validationCache.TryRemove(workflowHash, out _);
            }
        }

        return null;
    }

    /// <inheritdoc />
    public async Task CacheValidationResultAsync(string workflowHash, WorkflowValidationResult validationResult, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(workflowHash);
        ArgumentNullException.ThrowIfNull(validationResult);

        await Task.CompletedTask; // 保持异步接口一致性

        // 限制缓存大小
        if (_validationCache.Count > 1000)
        {
            // 移除最旧的缓存项
            var oldestKey = _validationCache
                .OrderBy(kvp => kvp.Value.ValidatedAt)
                .FirstOrDefault().Key;

            if (!string.IsNullOrEmpty(oldestKey))
            {
                _validationCache.TryRemove(oldestKey, out _);
            }
        }

        _validationCache[workflowHash] = validationResult;
        _logger.LogDebug("Cached validation result for hash {Hash}", workflowHash);
    }

    /// <inheritdoc />
    public async Task<bool> RegisterValidationRuleAsync(string ruleName, IValidationRule rule)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(ruleName);
        ArgumentNullException.ThrowIfNull(rule);

        try
        {
            _customRules[ruleName] = rule;
            _logger.LogInformation("Registered custom validation rule: {RuleName}", ruleName);

            await Task.CompletedTask;
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error registering validation rule {RuleName}", ruleName);
            return false;
        }
    }

    /// <inheritdoc />
    public async Task<bool> RemoveValidationRuleAsync(string ruleName)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(ruleName);

        try
        {
            var removed = _customRules.TryRemove(ruleName, out _);
            if (removed)
            {
                _logger.LogInformation("Removed custom validation rule: {RuleName}", ruleName);
            }

            await Task.CompletedTask;
            return removed;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing validation rule {RuleName}", ruleName);
            return false;
        }
    }

    /// <inheritdoc />
    public async Task<IReadOnlyDictionary<string, IValidationRule>> GetValidationRulesAsync()
    {
        await Task.CompletedTask;
        return _customRules;
    }

    /// <inheritdoc />
    public async Task StartAsync(CancellationToken cancellationToken = default)
    {
        lock (_lockObject)
        {
            if (_status != ValidatorServiceStatus.NotStarted)
            {
                _logger.LogWarning("WorkflowValidatorService is already started or starting");
                return;
            }

            _status = ValidatorServiceStatus.Starting;
        }

        try
        {
            _logger.LogInformation("Starting WorkflowValidatorService");

            _cancellationTokenSource = new CancellationTokenSource();

            // 初始化统计信息
            _statistics.ServiceStartTime = DateTime.UtcNow;

            lock (_lockObject)
            {
                _status = ValidatorServiceStatus.Running;
            }

            _logger.LogInformation("WorkflowValidatorService started successfully");
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting WorkflowValidatorService");

            lock (_lockObject)
            {
                _status = ValidatorServiceStatus.Error;
            }

            throw;
        }
    }

    /// <inheritdoc />
    public async Task StopAsync(CancellationToken cancellationToken = default)
    {
        lock (_lockObject)
        {
            if (_status != ValidatorServiceStatus.Running)
            {
                _logger.LogWarning("WorkflowValidatorService is not running");
                return;
            }

            _status = ValidatorServiceStatus.Stopping;
        }

        try
        {
            _logger.LogInformation("Stopping WorkflowValidatorService");

            // 取消所有正在进行的操作
            _cancellationTokenSource?.Cancel();

            // 清理缓存
            _validationCache.Clear();
            _customRules.Clear();

            lock (_lockObject)
            {
                _status = ValidatorServiceStatus.Stopped;
            }

            _logger.LogInformation("WorkflowValidatorService stopped successfully");
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error stopping WorkflowValidatorService");

            lock (_lockObject)
            {
                _status = ValidatorServiceStatus.Error;
            }

            throw;
        }
        finally
        {
            _cancellationTokenSource?.Dispose();
            _cancellationTokenSource = null;
        }
    }

    /// <inheritdoc />
    public ValidatorServiceStatus GetStatus()
    {
        lock (_lockObject)
        {
            return _status;
        }
    }

    /// <inheritdoc />
    public ValidationStatistics GetStatistics()
    {
        lock (_lockObject)
        {
            return new ValidationStatistics
            {
                TotalValidations = _statistics.TotalValidations,
                SuccessfulValidations = _statistics.SuccessfulValidations,
                FailedValidations = _statistics.FailedValidations,
                CacheHits = _statistics.CacheHits,
                CacheMisses = _statistics.CacheMisses,
                AverageValidationTimeMs = _statistics.AverageValidationTimeMs,
                LastValidationTime = _statistics.LastValidationTime,
                ServiceStartTime = _statistics.ServiceStartTime
            };
        }
    }

    /// <summary>
    /// 执行实际的工作流验证
    /// </summary>
    /// <param name="workflowDefinition">工作流定义</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    private async Task<WorkflowValidationResult> PerformValidationAsync(WorkflowDefinition workflowDefinition, CancellationToken cancellationToken)
    {
        // 使用基础验证器进行验证
        var baseResult = await _workflowValidator.ValidateWorkflowDefinitionAsync(workflowDefinition, cancellationToken);

        // 执行分布式验证规则
        var context = new Core.Interfaces.Validator.ValidationContext
        {
            WorkflowDefinition = workflowDefinition,
            ValidationType = ValidationType.Workflow
        };

        var ruleResult = await _ruleEngine.ExecuteRulesAsync(context, cancellationToken);

        // 合并验证结果
        if (!ruleResult.IsValid)
        {
            baseResult.IsValid = false;
            baseResult.ValidationErrors.AddRange(ruleResult.ValidationErrors);
        }

        // 执行自定义验证规则
        foreach (var customRule in _customRules.Values)
        {
            try
            {
                // 创建适配的验证上下文
                var adaptedContext = new Core.Interfaces.ValidationContext
                {
                    Target = context.WorkflowDefinition ?? new object(),
                    ValidationType = context.ValidationType.ToString(),
                    Data = new Dictionary<string, object>(context.Parameters)
                };

                var customResult = await customRule.ValidateAsync(adaptedContext, cancellationToken);
                if (!customResult.IsValid)
                {
                    baseResult.IsValid = false;
                    baseResult.ValidationErrors.AddRange(customResult.ValidationErrors);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing custom validation rule {RuleName}", customRule.RuleName);
                baseResult.ValidationErrors.Add(new ValidationError
                {
                    Code = "CUSTOM_RULE_ERROR",
                    Message = $"Custom rule '{customRule.RuleName}' failed: {ex.Message}",
                    Severity = ValidationSeverity.Warning
                });
            }
        }

        return baseResult;
    }

    /// <summary>
    /// 计算工作流哈希值
    /// </summary>
    /// <param name="workflowDefinition">工作流定义</param>
    /// <returns>哈希值</returns>
    private static string ComputeWorkflowHash(WorkflowDefinition workflowDefinition)
    {
        try
        {
            var json = JsonSerializer.Serialize(workflowDefinition, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = false
            });

            using var sha256 = SHA256.Create();
            var hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(json));
            return Convert.ToBase64String(hashBytes);
        }
        catch
        {
            // 如果序列化失败，使用工作流ID和时间戳作为备用哈希
            return $"{workflowDefinition.WorkflowId}_{DateTime.UtcNow:yyyyMMddHHmmss}";
        }
    }

    /// <summary>
    /// 更新统计信息
    /// </summary>
    /// <param name="isValid">验证是否成功</param>
    /// <param name="durationMs">验证耗时</param>
    /// <param name="isCacheHit">是否缓存命中</param>
    private void UpdateStatistics(bool isValid, long durationMs, bool isCacheHit)
    {
        lock (_lockObject)
        {
            _statistics.TotalValidations++;
            _statistics.LastValidationTime = DateTime.UtcNow;

            if (isValid)
            {
                _statistics.SuccessfulValidations++;
            }
            else
            {
                _statistics.FailedValidations++;
            }

            if (isCacheHit)
            {
                _statistics.CacheHits++;
            }
            else
            {
                _statistics.CacheMisses++;
            }

            // 更新平均验证时间
            var totalTime = _statistics.AverageValidationTimeMs * (_statistics.TotalValidations - 1) + durationMs;
            _statistics.AverageValidationTimeMs = totalTime / _statistics.TotalValidations;
        }
    }
}
