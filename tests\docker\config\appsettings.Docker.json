{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "FlowCustomV1": "Debug", "System.Net.Http.HttpClient": "Warning"}, "Console": {"IncludeScopes": true, "TimestampFormat": "yyyy-MM-dd HH:mm:ss.fff ", "LogToStandardErrorThreshold": "Error"}}, "AllowedHosts": "*", "NATS": {"Servers": ["nats://nats:4222"], "ConnectionName": "FlowCustomV1-Docker-Node", "MaxReconnectAttempts": 10, "ReconnectWaitMs": 2000, "PingIntervalMs": 30000, "MaxPingsOut": 3, "EnableJetStream": true, "JetStreamDomain": "flowcustom-test", "EnableTLS": false, "EnableAuth": true, "Username": "<PERSON><PERSON><PERSON>", "Password": "flowcustom_password", "EnableVerbose": false, "EnablePedantic": false, "DrainTimeoutMs": 10000, "FlushTimeoutMs": 5000, "RequestTimeoutMs": 30000, "EnableReconnectJitter": true, "ReconnectJitterMs": 1000, "MaxReconnectDelayMs": 30000}, "NodeDiscovery": {"NodeId": "docker-node", "NodeName": "DockerTestNode", "NodeRole": "Worker", "Region": "<PERSON>er", "DataCenter": "Docker-DC1", "HeartbeatIntervalMs": 10000, "NodeRegistrationTimeoutMs": 30000, "HealthCheckIntervalMs": 15000, "NodeExpirationTimeoutMs": 60000, "EnableAutoRegistration": true, "EnableHeartbeat": true, "EnableHealthCheck": true, "DiscoveryTopicPrefix": "cluster.discovery", "HeartbeatTopicPrefix": "cluster.heartbeat", "HealthCheckTopicPrefix": "cluster.health", "NodeCapabilities": {"CpuCores": 4, "MemoryMb": 8192, "DiskSpaceMb": 100000, "PerformanceLevel": 8, "HasGpu": false, "SupportedTaskTypes": ["*"], "MaxConcurrentTasks": 10}}, "TaskDistribution": {"MaxCandidateNodes": 20, "MaxConcurrentDistributions": 10, "MinBalanceScore": 0.7, "MaxRebalancingOperations": 5, "NodeSelectionTimeoutMs": 5000, "TaskDistributionTimeoutMs": 15000, "AutoRebalancingEnabled": true, "RebalancingThreshold": 0.3, "RebalancingIntervalSeconds": 300, "EnablePerformanceMonitoring": true, "EnableDetailedLogging": true, "StatisticsRetentionDays": 7, "PredictionHistoryWindowSize": 1000, "HealthCheckWeight": 0.3, "LoadWeight": 0.3, "PerformanceWeight": 0.25, "GeographyWeight": 0.15, "DefaultStrategy": "SmartLoad", "EnableFailover": true, "FailoverTimeoutMs": 10000, "MaxRetryAttempts": 3, "RetryDelayMs": 2000}, "TaskTracking": {"CleanupIntervalMinutes": 30, "StatisticsUpdateIntervalSeconds": 15, "MaxRecentCompletedTasks": 1000, "AutoCleanupOnCompletion": false, "TaskStateRetentionDays": 7, "EnableDetailedLogging": true, "EnablePerformanceMonitoring": true, "MaxConcurrentTrackedTasks": 5000, "TimeoutCheckIntervalSeconds": 30, "EnableTimeoutCheck": true, "DefaultTaskTimeoutMs": 300000, "EnableProgressTracking": true, "ProgressUpdateMinIntervalMs": 1000, "EnableResourceMonitoring": true, "ResourceMonitoringIntervalSeconds": 15, "EnableEventPublishing": true, "EventTopicPrefix": "task.tracking", "BatchOperationSize": 100, "EnableDependencyTracking": true, "MaxDependencyDepth": 10, "EnableRetryTracking": true, "MaxRetryHistoryCount": 10, "EnableExecutionLogging": true, "MaxExecutionLogCount": 1000}, "WorkflowDesigner": {"EnableCollaboration": true, "MaxConcurrentDesigners": 10, "AutoSaveIntervalSeconds": 30, "VersionHistoryRetentionDays": 30, "MaxVersionsPerWorkflow": 50, "EnableRealTimeSync": true, "ConflictResolutionStrategy": "LastWriterWins", "EnableDesignValidation": true, "ValidationTimeoutMs": 10000, "EnableDesignTemplates": true, "TemplateStoragePath": "/app/data/templates"}, "WorkflowValidator": {"EnableDistributedValidation": true, "MaxConcurrentValidations": 8, "ValidationTimeoutMs": 30000, "EnablePerformanceAnalysis": true, "EnableSecurityValidation": true, "EnableComplianceCheck": true, "MaxValidationDepth": 20, "EnableCacheValidationResults": true, "ValidationCacheExpirationMinutes": 60, "EnableValidationMetrics": true, "MetricsRetentionDays": 7}, "TaskExecution": {"MaxConcurrentTasks": 10, "TaskTimeoutMs": 300000, "EnableResourceMonitoring": true, "ResourceMonitoringIntervalMs": 5000, "EnableTaskIsolation": true, "TaskWorkingDirectory": "/app/data/tasks", "EnableTaskLogging": true, "TaskLogRetentionDays": 7, "MaxTaskLogSizeMB": 100, "EnableTaskMetrics": true, "MetricsPublishIntervalMs": 10000}, "HealthChecks": {"EnableHealthChecks": true, "HealthCheckIntervalSeconds": 30, "HealthCheckTimeoutMs": 10000, "EnableDetailedHealthInfo": true, "EnableDependencyHealthChecks": true, "HealthCheckEndpoint": "/health", "ReadinessCheckEndpoint": "/ready", "LivenessCheckEndpoint": "/live"}, "Monitoring": {"EnableMetrics": true, "MetricsEndpoint": "/metrics", "EnableTracing": false, "TracingSamplingRate": 0.1, "EnableLogging": true, "LogLevel": "Information", "EnablePerformanceCounters": true, "MetricsRetentionDays": 7}, "Security": {"EnableAuthentication": false, "EnableAuthorization": false, "EnableHttps": false, "EnableCors": true, "AllowedOrigins": ["*"], "EnableRateLimiting": false, "RateLimitRequests": 1000, "RateLimitWindowMinutes": 1}, "Database": {"Provider": "MySQL", "ConnectionString": "Server=mysql;Database=flowcustom_test;Uid=flowcustom;Pwd=TestPassword123!;", "CommandTimeout": 30, "EnableRetryOnFailure": true, "MaxRetryCount": 3, "MaxRetryDelay": "00:00:30", "EnableSensitiveDataLogging": false, "EnableDetailedErrors": true, "MigrationsAssembly": "FlowCustomV1.Infrastructure"}, "Testing": {"Environment": "<PERSON>er", "EnableTestEndpoints": true, "TestDataPath": "/app/data/test", "EnableMockServices": false, "TestTimeoutMs": 60000, "EnableTestLogging": true, "TestLogLevel": "Debug", "EnablePerformanceTesting": true, "EnableLoadTesting": true, "MaxTestConcurrency": 20}}