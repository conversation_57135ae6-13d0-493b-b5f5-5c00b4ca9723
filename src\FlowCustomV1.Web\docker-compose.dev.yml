version: '3.8'

services:
  # FlowCustomV1 Web Frontend (开发模式)
  flowcustomv1-web-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: flowcustomv1-web-dev
    ports:
      - "3000:3000"
    volumes:
      - .:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - VITE_API_BASE_URL=http://localhost:5000/api
    networks:
      - flowcustom-dev-network
    restart: unless-stopped
    stdin_open: true
    tty: true

  # 开发数据库
  db-dev:
    image: mcr.microsoft.com/mssql/server:2022-latest
    container_name: flowcustomv1-db-dev
    environment:
      - ACCEPT_EULA=Y
      - SA_PASSWORD=DevPassword123!
      - MSSQL_PID=Express
    ports:
      - "1434:1433"
    volumes:
      - db_dev_data:/var/opt/mssql
    networks:
      - flowcustom-dev-network
    restart: unless-stopped

  # 开发 NATS
  nats-dev:
    image: nats:alpine
    container_name: flowcustomv1-nats-dev
    command: 
      - "--cluster_name=flowcustom-dev-cluster"
      - "--jetstream"
      - "--store_dir=/data"
    ports:
      - "4223:4222"
      - "8223:8222"
    volumes:
      - nats_dev_data:/data
    networks:
      - flowcustom-dev-network
    restart: unless-stopped

  # 开发 Redis
  redis-dev:
    image: redis:alpine
    container_name: flowcustomv1-redis-dev
    ports:
      - "6380:6379"
    volumes:
      - redis_dev_data:/data
    networks:
      - flowcustom-dev-network
    restart: unless-stopped
    command: redis-server --appendonly yes

networks:
  flowcustom-dev-network:
    driver: bridge

volumes:
  db_dev_data:
    driver: local
  nats_dev_data:
    driver: local
  redis_dev_data:
    driver: local
