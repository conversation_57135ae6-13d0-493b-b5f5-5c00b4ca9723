using FlowCustomV1.Core.Models.Workflow;

namespace FlowCustomV1.Core.Models.Cluster;

/// <summary>
/// 节点发现查询
/// </summary>
public class NodeDiscoveryQuery
{
    /// <summary>
    /// 查询节点模式
    /// </summary>
    public NodeMode? NodeMode { get; set; }

    /// <summary>
    /// 查询节点状态
    /// </summary>
    public NodeStatus? NodeStatus { get; set; }

    /// <summary>
    /// 最小性能等级
    /// </summary>
    public int? MinPerformanceLevel { get; set; }

    /// <summary>
    /// 最大负载评分
    /// </summary>
    public double? MaxLoadScore { get; set; }

    /// <summary>
    /// 必需的能力标签
    /// </summary>
    public HashSet<string> RequiredCapabilityTags { get; set; } = new();

    /// <summary>
    /// 支持的执行器类型
    /// </summary>
    public List<string> SupportedExecutorTypes { get; set; } = new();

    /// <summary>
    /// 集群名称过滤
    /// </summary>
    public string? ClusterNameFilter { get; set; }

    /// <summary>
    /// 查询元数据
    /// </summary>
    public Dictionary<string, object> QueryMetadata { get; set; } = new();
}

/// <summary>
/// 节点执行要求
/// </summary>
public class NodeExecutionRequirements
{
    /// <summary>
    /// 所需执行器类型
    /// </summary>
    public string? RequiredExecutorType { get; set; }

    /// <summary>
    /// 最小CPU核心数
    /// </summary>
    public int? MinCpuCores { get; set; }

    /// <summary>
    /// 最小内存(MB)
    /// </summary>
    public long? MinMemoryMb { get; set; }

    /// <summary>
    /// 最小磁盘空间(MB)
    /// </summary>
    public long? MinDiskSpaceMb { get; set; }

    /// <summary>
    /// 必需的能力标签
    /// </summary>
    public HashSet<string> RequiredTags { get; set; } = new();

    /// <summary>
    /// 优先级权重
    /// </summary>
    public Dictionary<string, double> PriorityWeights { get; set; } = new();

    /// <summary>
    /// 超时时间(秒)
    /// </summary>
    public int TimeoutSeconds { get; set; } = 300;

    /// <summary>
    /// 重试次数
    /// </summary>
    public int MaxRetries { get; set; } = 3;

    /// <summary>
    /// 执行要求元数据
    /// </summary>
    public Dictionary<string, object> RequirementMetadata { get; set; } = new();
}

/// <summary>
/// 集群健康检查结果
/// </summary>
public class ClusterHealthCheckResult
{
    /// <summary>
    /// 节点ID
    /// </summary>
    public string NodeId { get; set; } = string.Empty;

    /// <summary>
    /// 检查时间
    /// </summary>
    public DateTime CheckTime { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 是否健康
    /// </summary>
    public bool IsHealthy { get; set; } = true;

    /// <summary>
    /// 健康评分 (0-100)
    /// </summary>
    public double HealthScore { get; set; } = 100.0;

    /// <summary>
    /// 检查详情
    /// </summary>
    public List<HealthCheckDetail> Details { get; set; } = new();

    /// <summary>
    /// 问题列表
    /// </summary>
    public List<string> Issues { get; set; } = new();

    /// <summary>
    /// 建议操作
    /// </summary>
    public List<string> Recommendations { get; set; } = new();

    /// <summary>
    /// 检查元数据
    /// </summary>
    public Dictionary<string, object> CheckMetadata { get; set; } = new();
}

/// <summary>
/// 健康检查详情
/// </summary>
public class HealthCheckDetail
{
    /// <summary>
    /// 检查项目
    /// </summary>
    public string CheckName { get; set; } = string.Empty;

    /// <summary>
    /// 检查结果
    /// </summary>
    public bool IsPass { get; set; } = true;

    /// <summary>
    /// 检查值
    /// </summary>
    public object? CheckValue { get; set; }

    /// <summary>
    /// 期望值
    /// </summary>
    public object? ExpectedValue { get; set; }

    /// <summary>
    /// 检查消息
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 检查时间
    /// </summary>
    public DateTime CheckTime { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 创建健康检查详情的深拷贝
    /// </summary>
    /// <returns>健康检查详情的深拷贝</returns>
    public HealthCheckDetail Clone()
    {
        return new HealthCheckDetail
        {
            CheckName = CheckName,
            IsPass = IsPass,
            CheckValue = CheckValue,
            ExpectedValue = ExpectedValue,
            Message = Message,
            CheckTime = CheckTime
        };
    }
}

/// <summary>
/// 集群统计
/// </summary>
public class ClusterStats
{
    /// <summary>
    /// 集群名称
    /// </summary>
    public string ClusterName { get; set; } = string.Empty;

    /// <summary>
    /// 总节点数
    /// </summary>
    public int TotalNodes { get; set; } = 0;

    /// <summary>
    /// 在线节点数
    /// </summary>
    public int OnlineNodes { get; set; } = 0;

    /// <summary>
    /// 离线节点数
    /// </summary>
    public int OfflineNodes { get; set; } = 0;

    /// <summary>
    /// 按模式分组的节点统计
    /// </summary>
    public Dictionary<NodeMode, int> NodesByMode { get; set; } = new();

    /// <summary>
    /// 按状态分组的节点统计
    /// </summary>
    public Dictionary<NodeStatus, int> NodesByStatus { get; set; } = new();

    /// <summary>
    /// 平均负载评分
    /// </summary>
    public double AverageLoadScore { get; set; } = 0;

    /// <summary>
    /// 集群健康评分
    /// </summary>
    public double ClusterHealthScore { get; set; } = 100.0;

    /// <summary>
    /// 总CPU核心数
    /// </summary>
    public int TotalCpuCores { get; set; } = 0;

    /// <summary>
    /// 总内存(MB)
    /// </summary>
    public long TotalMemoryMB { get; set; } = 0;

    /// <summary>
    /// 已使用CPU核心数
    /// </summary>
    public int UsedCpuCores { get; set; } = 0;

    /// <summary>
    /// 已使用内存(MB)
    /// </summary>
    public long UsedMemoryMB { get; set; } = 0;

    /// <summary>
    /// 活跃任务数
    /// </summary>
    public int ActiveTasks { get; set; } = 0;

    /// <summary>
    /// 队列任务数
    /// </summary>
    public int QueuedTasks { get; set; } = 0;

    /// <summary>
    /// 完成任务数
    /// </summary>
    public long CompletedTasks { get; set; } = 0;

    /// <summary>
    /// 失败任务数
    /// </summary>
    public long FailedTasks { get; set; } = 0;

    /// <summary>
    /// 统计时间
    /// </summary>
    public DateTime StatisticsTime { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime LastUpdatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 统计元数据
    /// </summary>
    public Dictionary<string, object> StatisticsMetadata { get; set; } = new();
}

/// <summary>
/// 工作流执行状态
/// </summary>
public class WorkflowExecutionStatus
{
    /// <summary>
    /// 执行ID
    /// </summary>
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 工作流ID
    /// </summary>
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 工作流名称
    /// </summary>
    public string WorkflowName { get; set; } = string.Empty;

    /// <summary>
    /// 执行状态
    /// </summary>
    public WorkflowExecutionState Status { get; set; } = WorkflowExecutionState.NotStarted;

    /// <summary>
    /// 执行状态 (向后兼容)
    /// </summary>
    public WorkflowExecutionState State
    {
        get => Status;
        set => Status = value;
    }

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime StartTime { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 开始时间 (向后兼容)
    /// </summary>
    public DateTime StartedAt
    {
        get => StartTime;
        set => StartTime = value;
    }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }

    /// <summary>
    /// 完成时间 (向后兼容)
    /// </summary>
    public DateTime? CompletedAt
    {
        get => EndTime;
        set => EndTime = value;
    }

    /// <summary>
    /// 执行进度 (0-100)
    /// </summary>
    public double Progress { get; set; } = 0;

    /// <summary>
    /// 当前执行节点
    /// </summary>
    public string? CurrentNodeId { get; set; }

    /// <summary>
    /// 执行节点ID
    /// </summary>
    public string? ExecutorNodeId { get; set; }

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime LastUpdatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 状态消息
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 执行结果
    /// </summary>
    public Dictionary<string, object> Results { get; set; } = new();

    /// <summary>
    /// 执行元数据
    /// </summary>
    public Dictionary<string, object> ExecutionMetadata { get; set; } = new();

    /// <summary>
    /// 节点执行状态（兼容性属性）
    /// </summary>
    public Dictionary<string, NodeExecutionState> NodeStates { get; set; } = new();

    /// <summary>
    /// 元数据（兼容性属性）
    /// </summary>
    public Dictionary<string, object> Metadata
    {
        get => ExecutionMetadata;
        set => ExecutionMetadata = value;
    }
}

/// <summary>
/// 工作流执行历史
/// </summary>
public class WorkflowExecutionHistory
{
    /// <summary>
    /// 工作流ID
    /// </summary>
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 总执行次数
    /// </summary>
    public int TotalExecutions { get; set; } = 0;

    /// <summary>
    /// 成功执行次数
    /// </summary>
    public int SuccessfulExecutions { get; set; } = 0;

    /// <summary>
    /// 失败执行次数
    /// </summary>
    public int FailedExecutions { get; set; } = 0;

    /// <summary>
    /// 平均执行时间(秒)
    /// </summary>
    public double AverageExecutionTimeSeconds { get; set; } = 0;

    /// <summary>
    /// 最后执行时间
    /// </summary>
    public DateTime? LastExecutionTime { get; set; }

    /// <summary>
    /// 执行记录列表
    /// </summary>
    public List<WorkflowExecutionRecord> Executions { get; set; } = new();

    /// <summary>
    /// 分页信息
    /// </summary>
    public PaginationInfo Pagination { get; set; } = new();

    /// <summary>
    /// 总记录数（兼容性属性）
    /// </summary>
    public int TotalCount
    {
        get => Pagination.TotalCount;
        set => Pagination.TotalCount = value;
    }

    /// <summary>
    /// 页面大小（兼容性属性）
    /// </summary>
    public int PageSize
    {
        get => Pagination.PageSize;
        set => Pagination.PageSize = value;
    }

    /// <summary>
    /// 页码（兼容性属性）
    /// </summary>
    public int PageNumber
    {
        get => Pagination.PageNumber;
        set => Pagination.PageNumber = value;
    }
}

/// <summary>
/// 工作流执行记录
/// </summary>
public class WorkflowExecutionRecord
{
    /// <summary>
    /// 执行ID
    /// </summary>
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 工作流ID
    /// </summary>
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 执行状态
    /// </summary>
    public WorkflowExecutionState Status { get; set; } = WorkflowExecutionState.NotStarted;

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime StartTime { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }

    /// <summary>
    /// 执行时长(秒)
    /// </summary>
    public double ExecutionTimeSeconds { get; set; } = 0;

    /// <summary>
    /// 执行节点ID
    /// </summary>
    public string? ExecutorNodeId { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 执行结果摘要
    /// </summary>
    public string? ResultSummary { get; set; }

    /// <summary>
    /// 记录元数据
    /// </summary>
    public Dictionary<string, object> RecordMetadata { get; set; } = new();
}

/// <summary>
/// 分页信息
/// </summary>
public class PaginationInfo
{
    /// <summary>
    /// 页码
    /// </summary>
    public int PageNumber { get; set; } = 1;

    /// <summary>
    /// 页大小
    /// </summary>
    public int PageSize { get; set; } = 20;

    /// <summary>
    /// 总记录数
    /// </summary>
    public int TotalCount { get; set; } = 0;

    /// <summary>
    /// 总页数
    /// </summary>
    public int TotalPages => PageSize > 0 ? (int)Math.Ceiling((double)TotalCount / PageSize) : 0;

    /// <summary>
    /// 是否有下一页
    /// </summary>
    public bool HasNextPage => PageNumber < TotalPages;

    /// <summary>
    /// 是否有上一页
    /// </summary>
    public bool HasPreviousPage => PageNumber > 1;
}

/// <summary>
/// 工作流性能统计
/// </summary>
public class WorkflowPerformanceStats
{
    /// <summary>
    /// 工作流ID
    /// </summary>
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 统计时间范围
    /// </summary>
    public TimeRange TimeRange { get; set; } = new();

    /// <summary>
    /// 总执行次数
    /// </summary>
    public int TotalExecutions { get; set; } = 0;

    /// <summary>
    /// 成功执行次数
    /// </summary>
    public int SuccessfulExecutions { get; set; } = 0;

    /// <summary>
    /// 失败执行次数
    /// </summary>
    public int FailedExecutions { get; set; } = 0;

    /// <summary>
    /// 平均执行时间(秒)
    /// </summary>
    public double AverageExecutionTime { get; set; } = 0;

    /// <summary>
    /// 最快执行时间(秒)
    /// </summary>
    public double FastestExecutionTime { get; set; } = 0;

    /// <summary>
    /// 最慢执行时间(秒)
    /// </summary>
    public double SlowestExecutionTime { get; set; } = 0;

    /// <summary>
    /// 成功率(%)
    /// </summary>
    public double SuccessRate { get; set; } = 0;

    /// <summary>
    /// 吞吐量(每小时执行次数)
    /// </summary>
    public double ThroughputPerHour { get; set; } = 0;

    /// <summary>
    /// 性能趋势数据
    /// </summary>
    public List<PerformanceTrendPoint> TrendData { get; set; } = new();

    /// <summary>
    /// 性能元数据
    /// </summary>
    public Dictionary<string, object> PerformanceMetadata { get; set; } = new();

    /// <summary>
    /// 最小执行时间（兼容性属性）
    /// </summary>
    public double MinExecutionTime
    {
        get => FastestExecutionTime;
        set => FastestExecutionTime = value;
    }

    /// <summary>
    /// 最大执行时间（兼容性属性）
    /// </summary>
    public double MaxExecutionTime
    {
        get => SlowestExecutionTime;
        set => SlowestExecutionTime = value;
    }

    /// <summary>
    /// 错误率（兼容性属性）
    /// </summary>
    public double ErrorRate
    {
        get => 100.0 - SuccessRate;
        set => SuccessRate = 100.0 - value;
    }

    /// <summary>
    /// 统计生成时间
    /// </summary>
    public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 时间范围
/// </summary>
public class TimeRange
{
    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime StartTime { get; set; } = DateTime.UtcNow.AddDays(-7);

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime EndTime { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 时间范围描述
    /// </summary>
    public string Description { get; set; } = "Last 7 days";
}

/// <summary>
/// 性能趋势点
/// </summary>
public class PerformanceTrendPoint
{
    /// <summary>
    /// 时间点
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 执行时间(秒)
    /// </summary>
    public double ExecutionTime { get; set; } = 0;

    /// <summary>
    /// 执行次数
    /// </summary>
    public int ExecutionCount { get; set; } = 0;

    /// <summary>
    /// 平均执行时间(毫秒) (向后兼容)
    /// </summary>
    public double AverageExecutionTimeMs
    {
        get => ExecutionTime * 1000;
        set => ExecutionTime = value / 1000;
    }

    /// <summary>
    /// 成功率(%)
    /// </summary>
    public double SuccessRate { get; set; } = 0;

    /// <summary>
    /// 吞吐量
    /// </summary>
    public double Throughput { get; set; } = 0;
}
