using System.ComponentModel.DataAnnotations;

namespace FlowCustomV1.Core.Models.Cluster;

/// <summary>
/// 节点负载信息
/// 记录节点当前的资源使用情况和性能指标
/// </summary>
public class NodeLoad
{
    /// <summary>
    /// CPU使用率 (0-100)
    /// </summary>
    [Range(0, 100)]
    public double CpuUsagePercentage { get; set; } = 0;

    /// <summary>
    /// 内存使用率 (0-100)
    /// </summary>
    [Range(0, 100)]
    public double MemoryUsagePercentage { get; set; } = 0;

    /// <summary>
    /// 磁盘使用率 (0-100)
    /// </summary>
    [Range(0, 100)]
    public double DiskUsagePercentage { get; set; } = 0;

    /// <summary>
    /// 网络使用率 (0-100)
    /// </summary>
    [Range(0, 100)]
    public double NetworkUsagePercentage { get; set; } = 0;

    /// <summary>
    /// 当前活跃任务数
    /// </summary>
    public int ActiveTaskCount { get; set; } = 0;

    /// <summary>
    /// 队列中的任务数
    /// </summary>
    public int QueuedTaskCount { get; set; } = 0;

    /// <summary>
    /// 最大任务容量
    /// </summary>
    public int MaxTaskCapacity { get; set; } = 10;

    /// <summary>
    /// 平均响应时间 (毫秒)
    /// </summary>
    public double AverageResponseTimeMs { get; set; } = 0;

    /// <summary>
    /// 吞吐量 (每秒处理的任务数)
    /// </summary>
    public double ThroughputPerSecond { get; set; } = 0;

    /// <summary>
    /// 错误率 (0-100)
    /// </summary>
    [Range(0, 100)]
    public double ErrorRatePercentage { get; set; } = 0;

    /// <summary>
    /// 负载评分 (0-100, 100表示满负载)
    /// </summary>
    [Range(0, 100)]
    public double LoadScore { get; set; } = 0;

    /// <summary>
    /// 系统负载平均值 (1分钟)
    /// </summary>
    public double SystemLoadAverage1Min { get; set; } = 0;

    /// <summary>
    /// 系统负载平均值 (5分钟)
    /// </summary>
    public double SystemLoadAverage5Min { get; set; } = 0;

    /// <summary>
    /// 系统负载平均值 (15分钟)
    /// </summary>
    public double SystemLoadAverage15Min { get; set; } = 0;

    /// <summary>
    /// 已使用内存 (MB)
    /// </summary>
    public long UsedMemoryMb { get; set; } = 0;

    /// <summary>
    /// 可用内存 (MB)
    /// </summary>
    public long AvailableMemoryMb { get; set; } = 0;

    /// <summary>
    /// 已使用磁盘空间 (MB)
    /// </summary>
    public long UsedDiskSpaceMb { get; set; } = 0;

    /// <summary>
    /// 可用磁盘空间 (MB)
    /// </summary>
    public long AvailableDiskSpaceMb { get; set; } = 0;

    /// <summary>
    /// 网络入站流量 (KB/s)
    /// </summary>
    public double NetworkInboundKbps { get; set; } = 0;

    /// <summary>
    /// 网络出站流量 (KB/s)
    /// </summary>
    public double NetworkOutboundKbps { get; set; } = 0;

    /// <summary>
    /// 进程数
    /// </summary>
    public int ProcessCount { get; set; } = 0;

    /// <summary>
    /// 线程数
    /// </summary>
    public int ThreadCount { get; set; } = 0;

    /// <summary>
    /// 文件描述符使用数
    /// </summary>
    public int FileDescriptorCount { get; set; } = 0;

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime LastUpdatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 负载历史数据
    /// </summary>
    public List<LoadHistoryPoint> LoadHistory { get; set; } = new();

    /// <summary>
    /// 负载元数据
    /// </summary>
    public Dictionary<string, object> LoadMetadata { get; set; } = new();

    /// <summary>
    /// 计算综合负载评分
    /// </summary>
    /// <returns>负载评分 (0-100)</returns>
    public double CalculateLoadScore()
    {
        var score = 0.0;
        
        // CPU负载权重 30%
        score += CpuUsagePercentage * 0.3;
        
        // 内存负载权重 25%
        score += MemoryUsagePercentage * 0.25;
        
        // 磁盘负载权重 15%
        score += DiskUsagePercentage * 0.15;
        
        // 任务负载权重 20%
        var taskLoadPercentage = MaxTaskCapacity > 0 ? 
            (double)ActiveTaskCount / MaxTaskCapacity * 100 : 0;
        score += taskLoadPercentage * 0.2;
        
        // 网络负载权重 10%
        score += NetworkUsagePercentage * 0.1;
        
        LoadScore = Math.Min(score, 100.0);
        return LoadScore;
    }

    /// <summary>
    /// 检查是否过载
    /// </summary>
    /// <param name="threshold">过载阈值 (默认80%)</param>
    /// <returns>是否过载</returns>
    public bool IsOverloaded(double threshold = 80.0)
    {
        return CalculateLoadScore() > threshold;
    }

    /// <summary>
    /// 检查是否可以接受新任务
    /// </summary>
    /// <returns>是否可以接受新任务</returns>
    public bool CanAcceptNewTask()
    {
        return ActiveTaskCount < MaxTaskCapacity &&
               CpuUsagePercentage < 90 &&
               MemoryUsagePercentage < 90 &&
               !IsOverloaded();
    }

    /// <summary>
    /// 添加负载历史点
    /// </summary>
    /// <param name="historyPoint">历史点</param>
    public void AddLoadHistoryPoint(LoadHistoryPoint historyPoint)
    {
        LoadHistory.Add(historyPoint);
        
        // 保持最近100个历史点
        if (LoadHistory.Count > 100)
        {
            LoadHistory.RemoveAt(0);
        }
    }

    /// <summary>
    /// 获取负载趋势
    /// </summary>
    /// <param name="minutes">分钟数</param>
    /// <returns>负载趋势 (正数表示上升，负数表示下降)</returns>
    public double GetLoadTrend(int minutes = 5)
    {
        var cutoffTime = DateTime.UtcNow.AddMinutes(-minutes);
        var recentPoints = LoadHistory
            .Where(p => p.Timestamp >= cutoffTime)
            .OrderBy(p => p.Timestamp)
            .ToList();
            
        if (recentPoints.Count < 2)
            return 0;
            
        var firstScore = recentPoints.First().LoadScore;
        var lastScore = recentPoints.Last().LoadScore;
        
        return lastScore - firstScore;
    }

    /// <summary>
    /// 更新负载信息
    /// </summary>
    public void UpdateLoadInfo()
    {
        LastUpdatedAt = DateTime.UtcNow;
        CalculateLoadScore();
        
        // 添加当前负载到历史记录
        AddLoadHistoryPoint(new LoadHistoryPoint
        {
            Timestamp = LastUpdatedAt,
            CpuUsage = CpuUsagePercentage,
            MemoryUsage = MemoryUsagePercentage,
            DiskUsage = DiskUsagePercentage,
            NetworkUsage = NetworkUsagePercentage,
            ActiveTasks = ActiveTaskCount,
            LoadScore = LoadScore
        });
    }

    /// <summary>
    /// 验证负载信息的有效性
    /// </summary>
    /// <returns>验证结果</returns>
    public bool IsValid()
    {
        return CpuUsagePercentage >= 0 && CpuUsagePercentage <= 100 &&
               MemoryUsagePercentage >= 0 && MemoryUsagePercentage <= 100 &&
               DiskUsagePercentage >= 0 && DiskUsagePercentage <= 100 &&
               NetworkUsagePercentage >= 0 && NetworkUsagePercentage <= 100 &&
               ActiveTaskCount >= 0 &&
               QueuedTaskCount >= 0 &&
               MaxTaskCapacity > 0 &&
               ErrorRatePercentage >= 0 && ErrorRatePercentage <= 100;
    }

    /// <summary>
    /// 创建节点负载信息的深拷贝
    /// </summary>
    /// <returns>节点负载信息的深拷贝</returns>
    public NodeLoad Clone()
    {
        return new NodeLoad
        {
            CpuUsagePercentage = CpuUsagePercentage,
            MemoryUsagePercentage = MemoryUsagePercentage,
            DiskUsagePercentage = DiskUsagePercentage,
            NetworkUsagePercentage = NetworkUsagePercentage,
            ActiveTaskCount = ActiveTaskCount,
            QueuedTaskCount = QueuedTaskCount,
            MaxTaskCapacity = MaxTaskCapacity,
            AverageResponseTimeMs = AverageResponseTimeMs,
            ThroughputPerSecond = ThroughputPerSecond,
            ErrorRatePercentage = ErrorRatePercentage,
            LoadScore = LoadScore,
            SystemLoadAverage1Min = SystemLoadAverage1Min,
            SystemLoadAverage5Min = SystemLoadAverage5Min,
            SystemLoadAverage15Min = SystemLoadAverage15Min,
            UsedMemoryMb = UsedMemoryMb,
            AvailableMemoryMb = AvailableMemoryMb,
            UsedDiskSpaceMb = UsedDiskSpaceMb,
            AvailableDiskSpaceMb = AvailableDiskSpaceMb,
            NetworkInboundKbps = NetworkInboundKbps,
            NetworkOutboundKbps = NetworkOutboundKbps,
            ProcessCount = ProcessCount,
            ThreadCount = ThreadCount,
            FileDescriptorCount = FileDescriptorCount,
            LastUpdatedAt = LastUpdatedAt,
            LoadHistory = LoadHistory.Select(h => h.Clone()).ToList(),
            LoadMetadata = new Dictionary<string, object>(LoadMetadata)
        };
    }

    /// <summary>
    /// 获取负载信息的简要描述
    /// </summary>
    /// <returns>负载信息简要描述</returns>
    public override string ToString()
    {
        return $"Load[Score={LoadScore:F1}] CPU={CpuUsagePercentage:F1}% " +
               $"Memory={MemoryUsagePercentage:F1}% Tasks={ActiveTaskCount}/{MaxTaskCapacity}";
    }
}

/// <summary>
/// 负载历史点
/// </summary>
public class LoadHistoryPoint
{
    /// <summary>
    /// 时间戳
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// CPU使用率
    /// </summary>
    public double CpuUsage { get; set; } = 0;

    /// <summary>
    /// 内存使用率
    /// </summary>
    public double MemoryUsage { get; set; } = 0;

    /// <summary>
    /// 磁盘使用率
    /// </summary>
    public double DiskUsage { get; set; } = 0;

    /// <summary>
    /// 网络使用率
    /// </summary>
    public double NetworkUsage { get; set; } = 0;

    /// <summary>
    /// 活跃任务数
    /// </summary>
    public int ActiveTasks { get; set; } = 0;

    /// <summary>
    /// 负载评分
    /// </summary>
    public double LoadScore { get; set; } = 0;

    /// <summary>
    /// 创建负载历史点的深拷贝
    /// </summary>
    /// <returns>负载历史点的深拷贝</returns>
    public LoadHistoryPoint Clone()
    {
        return new LoadHistoryPoint
        {
            Timestamp = Timestamp,
            CpuUsage = CpuUsage,
            MemoryUsage = MemoryUsage,
            DiskUsage = DiskUsage,
            NetworkUsage = NetworkUsage,
            ActiveTasks = ActiveTasks,
            LoadScore = LoadScore
        };
    }
}
