# NATS Server Configuration for Test Cluster Node 2

# Server identity
server_name: "nats-test-2"

# Network configuration
host: "0.0.0.0"
port: 4222

# HTTP monitoring
http_port: 8222

# Cluster configuration
cluster {
  name: "flowcustom-test-cluster"
  host: "0.0.0.0"
  port: 6222
  
  # Routes to other cluster members
  routes: [
    "nats://nats-1:6222"
    "nats://nats-3:6222"
  ]
}

# JetStream configuration
jetstream {
  enabled: true
  store_dir: "/data/jetstream"
  max_memory_store: 1GB
  max_file_store: 10GB
  domain: "flowcustom-test"
}

# Logging configuration
log_file: "/var/log/nats/nats-server.log"
logtime: true
debug: false
trace: false

# Limits
max_connections: 1000
max_control_line: 4KB
max_payload: 1MB
max_pending: 64MB
max_subscriptions: 0

# Timeouts
ping_interval: 2m
ping_max: 2
write_deadline: 10s

# System account for monitoring
system_account: "$SYS"

# Accounts configuration
accounts {
  $SYS: {
    users: [
      {
        user: "sys"
        password: "sys_password"
      }
    ]
  }
  
  FLOWCUSTOM: {
    users: [
      {
        user: "flowcustom"
        password: "flowcustom_password"
        permissions: {
          publish: {
            allow: [
              "flowcustom.>"
              "cluster.>"
              "task.>"
              "workflow.>"
              "node.>"
            ]
          }
          subscribe: {
            allow: [
              "flowcustom.>"
              "cluster.>"
              "task.>"
              "workflow.>"
              "node.>"
            ]
          }
        }
      }
    ]
    
    jetstream: {
      max_memory_store: 512MB
      max_file_store: 5GB
      max_streams: 100
      max_consumers: 1000
    }
  }
}

# Default account
default_account: "FLOWCUSTOM"
