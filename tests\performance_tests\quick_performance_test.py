#!/usr/bin/env python3
"""
快速性能测试 - 验证优化效果
"""

import requests
import time
import statistics

BASE_URL = "http://localhost:5000"

def quick_test():
    print("🚀 快速性能测试 - 验证优化效果")
    print("=" * 50)
    
    # 等待容器启动
    print("等待API容器启动...")
    time.sleep(10)
    
    # 测试cluster_nodes端点
    print("\n📊 测试 cluster_nodes 端点 (优化后):")
    times = []
    
    for i in range(5):
        try:
            start_time = time.time()
            response = requests.get(f"{BASE_URL}/api/cluster/nodes", timeout=30)
            end_time = time.time()
            
            response_time = (end_time - start_time) * 1000
            times.append(response_time)
            
            print(f"  请求 {i+1}: {response_time:.2f}ms (状态码: {response.status_code})")
            
        except Exception as e:
            print(f"  请求 {i+1}: 失败 ({str(e)})")
            
    if times:
        avg_time = statistics.mean(times)
        min_time = min(times)
        max_time = max(times)
        
        print(f"\n📈 cluster_nodes 性能统计:")
        print(f"  平均响应时间: {avg_time:.2f}ms")
        print(f"  最快响应时间: {min_time:.2f}ms")
        print(f"  最慢响应时间: {max_time:.2f}ms")
        
        if avg_time < 2000:
            print("  ✅ 性能优化成功！响应时间大幅改善")
        else:
            print("  ❌ 性能仍有问题，需要进一步优化")
    
    # 对比测试其他端点
    print(f"\n📊 对比测试其他端点:")
    
    endpoints = {
        "executor_capacity": "/api/executor/capacity"
    }
    
    for name, path in endpoints.items():
        try:
            start_time = time.time()
            response = requests.get(f"{BASE_URL}{path}", timeout=10)
            end_time = time.time()
            
            response_time = (end_time - start_time) * 1000
            print(f"  {name}: {response_time:.2f}ms (状态码: {response.status_code})")
            
        except Exception as e:
            print(f"  {name}: 失败 ({str(e)})")

if __name__ == "__main__":
    quick_test()
