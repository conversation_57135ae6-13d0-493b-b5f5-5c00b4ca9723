using FlowCustomV1.Core.Models.Scheduling;

namespace FlowCustomV1.Core.Interfaces.Scheduling;

/// <summary>
/// 任务执行状态跟踪服务接口
/// 提供分布式任务执行状态的实时跟踪和监控功能
/// </summary>
public interface ITaskExecutionTracker
{
    #region 任务状态跟踪

    /// <summary>
    /// 开始跟踪任务执行
    /// </summary>
    /// <param name="taskExecution">任务执行信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>跟踪结果</returns>
    Task<TaskTrackingResult> StartTrackingAsync(
        TaskExecution taskExecution,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 更新任务执行状态
    /// </summary>
    /// <param name="taskId">任务ID</param>
    /// <param name="status">新状态</param>
    /// <param name="progress">进度信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>更新结果</returns>
    Task<TaskStatusUpdateResult> UpdateTaskStatusAsync(
        string taskId,
        TaskExecutionStatus status,
        TaskProgress? progress = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 停止跟踪任务执行
    /// </summary>
    /// <param name="taskId">任务ID</param>
    /// <param name="finalResult">最终执行结果</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>停止跟踪结果</returns>
    Task<TaskTrackingStopResult> StopTrackingAsync(
        string taskId,
        TaskExecutionResult finalResult,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取任务执行状态
    /// </summary>
    /// <param name="taskId">任务ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务执行状态</returns>
    Task<TaskExecutionState?> GetTaskStatusAsync(
        string taskId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 批量获取任务执行状态
    /// </summary>
    /// <param name="taskIds">任务ID列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务执行状态列表</returns>
    Task<IReadOnlyList<TaskExecutionState>> GetTaskStatusBatchAsync(
        IReadOnlyList<string> taskIds,
        CancellationToken cancellationToken = default);

    #endregion

    #region 任务查询

    /// <summary>
    /// 查询正在执行的任务
    /// </summary>
    /// <param name="nodeId">节点ID（可选）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>正在执行的任务列表</returns>
    Task<IReadOnlyList<TaskExecutionState>> GetRunningTasksAsync(
        string? nodeId = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 查询已完成的任务
    /// </summary>
    /// <param name="timeRange">时间范围</param>
    /// <param name="nodeId">节点ID（可选）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>已完成的任务列表</returns>
    Task<IReadOnlyList<TaskExecutionState>> GetCompletedTasksAsync(
        TimeSpan? timeRange = null,
        string? nodeId = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 查询失败的任务
    /// </summary>
    /// <param name="timeRange">时间范围</param>
    /// <param name="nodeId">节点ID（可选）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>失败的任务列表</returns>
    Task<IReadOnlyList<TaskExecutionState>> GetFailedTasksAsync(
        TimeSpan? timeRange = null,
        string? nodeId = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 搜索任务
    /// </summary>
    /// <param name="criteria">搜索条件</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>搜索结果</returns>
    Task<TaskSearchResult> SearchTasksAsync(
        TaskSearchCriteria criteria,
        CancellationToken cancellationToken = default);

    #endregion

    #region 实时监控

    /// <summary>
    /// 获取任务执行统计信息
    /// </summary>
    /// <param name="timeRange">时间范围</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>统计信息</returns>
    Task<TaskExecutionStatistics> GetExecutionStatisticsAsync(
        TimeSpan? timeRange = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取节点任务执行统计
    /// </summary>
    /// <param name="nodeId">节点ID</param>
    /// <param name="timeRange">时间范围</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>节点统计信息</returns>
    Task<NodeTaskStatistics> GetNodeStatisticsAsync(
        string nodeId,
        TimeSpan? timeRange = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取实时监控数据
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>实时监控数据</returns>
    Task<RealTimeMonitoringData> GetRealTimeDataAsync(CancellationToken cancellationToken = default);

    #endregion

    #region 任务生命周期管理

    /// <summary>
    /// 暂停任务执行
    /// </summary>
    /// <param name="taskId">任务ID</param>
    /// <param name="reason">暂停原因</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>暂停结果</returns>
    Task<TaskOperationResult> PauseTaskAsync(
        string taskId,
        string reason = "",
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 恢复任务执行
    /// </summary>
    /// <param name="taskId">任务ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>恢复结果</returns>
    Task<TaskOperationResult> ResumeTaskAsync(
        string taskId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 取消任务执行
    /// </summary>
    /// <param name="taskId">任务ID</param>
    /// <param name="reason">取消原因</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>取消结果</returns>
    Task<TaskOperationResult> CancelTaskAsync(
        string taskId,
        string reason = "",
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 重启任务执行
    /// </summary>
    /// <param name="taskId">任务ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>重启结果</returns>
    Task<TaskOperationResult> RestartTaskAsync(
        string taskId,
        CancellationToken cancellationToken = default);

    #endregion

    #region 事件

    /// <summary>
    /// 任务状态变更事件
    /// </summary>
    event EventHandler<TaskStatusChangedEventArgs>? TaskStatusChanged;

    /// <summary>
    /// 任务进度更新事件
    /// </summary>
    event EventHandler<TaskProgressUpdatedEventArgs>? TaskProgressUpdated;

    /// <summary>
    /// 任务执行完成事件
    /// </summary>
    event EventHandler<TaskExecutionCompletedEventArgs>? TaskExecutionCompleted;

    /// <summary>
    /// 任务执行失败事件
    /// </summary>
    event EventHandler<TaskExecutionFailedEventArgs>? TaskExecutionFailed;

    #endregion

    #region 健康检查

    /// <summary>
    /// 检查跟踪服务健康状态
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>健康检查结果</returns>
    Task<HealthCheckResult> CheckHealthAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 清理过期的跟踪数据
    /// </summary>
    /// <param name="retentionPeriod">保留期间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>清理结果</returns>
    Task<CleanupResult> CleanupExpiredDataAsync(
        TimeSpan retentionPeriod,
        CancellationToken cancellationToken = default);

    #endregion
}
