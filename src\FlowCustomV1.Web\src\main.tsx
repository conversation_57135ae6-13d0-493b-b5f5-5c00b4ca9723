import React from 'react'
import ReactDOM from 'react-dom/client'
import { ConfigProvider } from 'antd'
import zhCN from 'antd/locale/zh_CN'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import 'virtual:windi.css'
import './styles/global.css'
import './styles/layout-config.css'  // 纯CSS配置文件
import './styles/layout-base.css'

import App from './App'

// 设置dayjs中文
dayjs.locale('zh-cn')

// Ant Design 主题配置
const theme = {
  token: {
    colorPrimary: '#1890ff',
    borderRadius: 6,
    wireframe: false,
  },
  components: {
    Layout: {
      headerBg: '#001529',
      siderBg: '#001529',
    },
  },
}

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <ConfigProvider 
      locale={zhCN} 
      theme={theme}
    >
      <App />
    </ConfigProvider>
  </React.StrictMode>,
)
