using System.Collections.Concurrent;
using System.Diagnostics;
using System.Text.RegularExpressions;
using FlowCustomV1.Core.Interfaces.Messaging;
using FlowCustomV1.Core.Models.Cluster;
using FlowCustomV1.Core.Models.Messaging;
using Microsoft.Extensions.Logging;
using ClusterNodeInfo = FlowCustomV1.Core.Models.Cluster.NodeInfo;

namespace FlowCustomV1.Infrastructure.Services.Messaging;

/// <summary>
/// 基于角色的消息路由服务实现
/// 支持角色感知的智能消息路由和负载均衡
/// </summary>
public class RoleBasedMessageRouter : IRoleBasedMessageRouter
{
    private readonly INatsService _natsService;
    private readonly IMessageTopicService _topicService;
    private readonly ILogger<RoleBasedMessageRouter> _logger;
    
    private readonly ConcurrentDictionary<string, RoleRouteRule> _roleRoutes;
    private readonly ConcurrentDictionary<string, ClusterNodeInfo> _roleNodes;
    private readonly ConcurrentDictionary<NodeRole, RoleRoutingStatistics> _routingStatistics;
    private readonly ConcurrentDictionary<NodeRole, int> _roundRobinCounters;
    private readonly Random _random;
    private readonly object _lockObject = new();

    /// <summary>
    /// 构造函数
    /// </summary>
    public RoleBasedMessageRouter(
        INatsService natsService,
        IMessageTopicService topicService,
        ILogger<RoleBasedMessageRouter> logger)
    {
        _natsService = natsService ?? throw new ArgumentNullException(nameof(natsService));
        _topicService = topicService ?? throw new ArgumentNullException(nameof(topicService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        
        _roleRoutes = new ConcurrentDictionary<string, RoleRouteRule>();
        _roleNodes = new ConcurrentDictionary<string, ClusterNodeInfo>();
        _routingStatistics = new ConcurrentDictionary<NodeRole, RoleRoutingStatistics>();
        _roundRobinCounters = new ConcurrentDictionary<NodeRole, int>();
        _random = new Random();

        InitializeDefaultRoleRoutes();
        InitializeStatistics();
    }

    #region 角色路由规则管理

    /// <inheritdoc />
    public void RegisterRoleRoute(string pattern, NodeRole[] targetRoles, RoutingStrategy routingStrategy = RoutingStrategy.RoundRobin, int priority = 0)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(pattern);
        ArgumentNullException.ThrowIfNull(targetRoles);

        var route = new RoleRouteRule
        {
            Pattern = pattern,
            TargetRoles = targetRoles,
            Strategy = routingStrategy,
            Priority = priority,
            Description = $"Route for {string.Join(", ", targetRoles.Select(r => r.GetDisplayName()))}"
        };

        _roleRoutes.AddOrUpdate(pattern, route, (key, oldValue) => 
        {
            route.CreatedAt = oldValue.CreatedAt;
            return route;
        });

        _logger.LogInformation("Registered role route: {Pattern} -> [{Roles}] ({Strategy}, Priority: {Priority})", 
            pattern, string.Join(", ", targetRoles.Select(r => r.GetDisplayName())), routingStrategy, priority);
    }

    /// <inheritdoc />
    public void UnregisterRoleRoute(string pattern)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(pattern);

        if (_roleRoutes.TryRemove(pattern, out var route))
        {
            _logger.LogInformation("Unregistered role route: {Pattern} -> [{Roles}]", 
                pattern, string.Join(", ", route.TargetRoles.Select(r => r.GetDisplayName())));
        }
    }

    /// <inheritdoc />
    public IReadOnlyList<RoleRouteRule> GetRoleRoutes()
    {
        return _roleRoutes.Values
            .Where(r => r.Enabled)
            .OrderByDescending(r => r.Priority)
            .ThenBy(r => r.Pattern)
            .ToList();
    }

    /// <inheritdoc />
    public void SetRoleRouteEnabled(string pattern, bool enabled)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(pattern);

        if (_roleRoutes.TryGetValue(pattern, out var route))
        {
            route.Enabled = enabled;
            route.UpdatedAt = DateTime.UtcNow;
            
            _logger.LogInformation("Role route {Pattern} {Status}", 
                pattern, enabled ? "enabled" : "disabled");
        }
    }

    #endregion

    #region 角色感知消息路由

    /// <inheritdoc />
    public async Task RouteMessageAsync(IMessage message, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(message);

        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            var subject = GetSubjectFromMessage(message);
            _logger.LogDebug("Routing message {MessageId} with subject {Subject}", message.MessageId, subject);

            // 查找匹配的角色路由规则
            var matchedRoute = FindMatchingRoleRoute(subject);
            if (matchedRoute != null)
            {
                await RouteToRolesAsync(message, matchedRoute.TargetRoles, matchedRoute.Strategy, cancellationToken);
                return;
            }

            // 如果没有匹配的路由规则，尝试从消息中推断目标角色
            var inferredRole = InferTargetRoleFromMessage(message);
            if (inferredRole != NodeRole.None)
            {
                await RouteToRoleAsync(message, inferredRole, RoutingStrategy.RoundRobin, cancellationToken);
                return;
            }

            // 最后回退到直接发布
            await _natsService.PublishAsync(message, cancellationToken);
            _logger.LogDebug("Message {MessageId} routed to original subject: {Subject}", message.MessageId, subject);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error routing message {MessageId}", message.MessageId);
            throw;
        }
        finally
        {
            stopwatch.Stop();
            _logger.LogDebug("Message routing completed in {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);
        }
    }

    /// <inheritdoc />
    public async Task RouteToRoleAsync(IMessage message, NodeRole targetRole, RoutingStrategy strategy = RoutingStrategy.RoundRobin, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(message);

        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            var availableNodes = GetAvailableRoleNodes(targetRole);
            if (!availableNodes.Any())
            {
                var error = $"No available nodes found for role: {targetRole.GetDisplayName()}";
                _logger.LogWarning(error);
                OnRoleRoutingFailed(message.MessageId, targetRole, error);
                throw new InvalidOperationException(error);
            }

            var selectedNode = SelectNodeByStrategy(availableNodes, strategy, targetRole);
            var targetSubject = _topicService.GetNodeTasksTopic(selectedNode.NodeId);

            await _natsService.PublishAsync(targetSubject, message, cancellationToken);
            
            UpdateRoutingStatistics(targetRole, true, stopwatch.ElapsedMilliseconds);
            
            _logger.LogDebug("Message {MessageId} routed to {Role} node {NodeId} via subject {Subject}", 
                message.MessageId, targetRole.GetDisplayName(), selectedNode.NodeId, targetSubject);
        }
        catch (Exception ex)
        {
            UpdateRoutingStatistics(targetRole, false, stopwatch.ElapsedMilliseconds);
            OnRoleRoutingFailed(message.MessageId, targetRole, ex.Message, ex);
            throw;
        }
    }

    /// <inheritdoc />
    public async Task BroadcastToRoleAsync(IMessage message, NodeRole targetRole, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(message);

        var availableNodes = GetAvailableRoleNodes(targetRole);
        if (!availableNodes.Any())
        {
            var error = $"No available nodes found for role: {targetRole.GetDisplayName()}";
            _logger.LogWarning(error);
            OnRoleRoutingFailed(message.MessageId, targetRole, error);
            return;
        }

        var tasks = availableNodes.Select(async node =>
        {
            try
            {
                var targetSubject = _topicService.GetNodeTasksTopic(node.NodeId);
                await _natsService.PublishAsync(targetSubject, message, cancellationToken);
                _logger.LogDebug("Message {MessageId} broadcasted to {Role} node {NodeId}", 
                    message.MessageId, targetRole.GetDisplayName(), node.NodeId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to broadcast message {MessageId} to {Role} node {NodeId}", 
                    message.MessageId, targetRole.GetDisplayName(), node.NodeId);
            }
        });

        await Task.WhenAll(tasks);
        
        _logger.LogInformation("Message {MessageId} broadcasted to {Count} {Role} nodes", 
            message.MessageId, availableNodes.Count, targetRole.GetDisplayName());
    }

    /// <inheritdoc />
    public async Task MulticastToRolesAsync(IMessage message, NodeRole[] targetRoles, RoutingStrategy strategy = RoutingStrategy.RoundRobin, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(message);
        ArgumentNullException.ThrowIfNull(targetRoles);

        var tasks = targetRoles.Select(role => RouteToRoleAsync(message, role, strategy, cancellationToken));
        await Task.WhenAll(tasks);
        
        _logger.LogInformation("Message {MessageId} multicasted to roles: [{Roles}]", 
            message.MessageId, string.Join(", ", targetRoles.Select(r => r.GetDisplayName())));
    }

    #endregion

    #region 角色节点管理

    /// <inheritdoc />
    public void RegisterRoleNode(ClusterNodeInfo nodeInfo)
    {
        ArgumentNullException.ThrowIfNull(nodeInfo);
        ArgumentException.ThrowIfNullOrWhiteSpace(nodeInfo.NodeId);

        var oldNode = _roleNodes.TryGetValue(nodeInfo.NodeId, out var existingNode) ? existingNode : null;
        
        _roleNodes.AddOrUpdate(nodeInfo.NodeId, nodeInfo, (key, oldValue) =>
        {
            // 保留注册时间
            nodeInfo.Timestamps.RegisteredAt = oldValue.Timestamps.RegisteredAt;
            return nodeInfo;
        });

        var roles = nodeInfo.GetActiveRoles().ToList();
        _logger.LogInformation("Role node registered: {NodeId} with roles [{Roles}] - {Status}", 
            nodeInfo.NodeId, string.Join(", ", roles.Select(r => r.GetDisplayName())), nodeInfo.Status);

        // 更新统计信息
        foreach (var role in roles)
        {
            UpdateRoleNodeCount(role);
        }

        // 触发状态变更事件
        if (oldNode != null && oldNode.Status != nodeInfo.Status)
        {
            OnRoleNodeStatusChanged(nodeInfo.NodeId, nodeInfo.RoleConfiguration.Roles, oldNode.Status, nodeInfo.Status);
        }
    }

    /// <inheritdoc />
    public void UnregisterRoleNode(string nodeId)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(nodeId);

        if (_roleNodes.TryRemove(nodeId, out var node))
        {
            var roles = node.GetActiveRoles().ToList();
            _logger.LogInformation("Role node unregistered: {NodeId} with roles [{Roles}]", 
                nodeId, string.Join(", ", roles.Select(r => r.GetDisplayName())));

            // 更新统计信息
            foreach (var role in roles)
            {
                UpdateRoleNodeCount(role);
            }
        }
    }

    /// <inheritdoc />
    public void UpdateNodeRoles(string nodeId, NodeRoleConfiguration roleConfiguration)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(nodeId);
        ArgumentNullException.ThrowIfNull(roleConfiguration);

        if (_roleNodes.TryGetValue(nodeId, out var node))
        {
            var oldRoles = node.GetActiveRoles().ToList();
            node.RoleConfiguration = roleConfiguration.Clone();
            node.Timestamps.LastUpdatedAt = DateTime.UtcNow;

            var newRoles = node.GetActiveRoles().ToList();

            _logger.LogInformation("Updated roles for node {NodeId}: [{OldRoles}] -> [{NewRoles}]",
                nodeId,
                string.Join(", ", oldRoles.Select(r => r.GetDisplayName())),
                string.Join(", ", newRoles.Select(r => r.GetDisplayName())));

            // 更新统计信息
            foreach (var role in oldRoles.Union(newRoles))
            {
                UpdateRoleNodeCount(role);
            }
        }
    }

    /// <inheritdoc />
    public IReadOnlyList<ClusterNodeInfo> GetAvailableRoleNodes(NodeRole role)
    {
        return _roleNodes.Values
            .Where(n => n.SupportsRole(role) && n.Status == NodeStatus.Healthy)
            .OrderBy(n => n.Load.CpuUsagePercentage)
            .ThenBy(n => n.Load.ActiveTaskCount)
            .ToList();
    }

    /// <inheritdoc />
    public IReadOnlyDictionary<string, ClusterNodeInfo> GetAllRoleNodes()
    {
        return _roleNodes;
    }

    #endregion

    #region 路由统计和监控

    /// <inheritdoc />
    public RoleRoutingStatistics GetRoleRoutingStatistics(NodeRole role)
    {
        return _routingStatistics.TryGetValue(role, out var stats)
            ? stats
            : new RoleRoutingStatistics { Role = role };
    }

    /// <inheritdoc />
    public IReadOnlyDictionary<NodeRole, RoleRoutingStatistics> GetAllRoleRoutingStatistics()
    {
        return _routingStatistics;
    }

    /// <inheritdoc />
    public void ResetRoutingStatistics(NodeRole? role = null)
    {
        if (role.HasValue)
        {
            if (_routingStatistics.TryGetValue(role.Value, out var stats))
            {
                ResetStatistics(stats);
                _logger.LogInformation("Reset routing statistics for role: {Role}", role.Value.GetDisplayName());
            }
        }
        else
        {
            foreach (var stats in _routingStatistics.Values)
            {
                ResetStatistics(stats);
            }
            _logger.LogInformation("Reset all routing statistics");
        }
    }

    #endregion

    #region 事件

    /// <inheritdoc />
    public event EventHandler<RoleNodeStatusChangedEventArgs>? RoleNodeStatusChanged;

    /// <inheritdoc />
    public event EventHandler<RoleRoutingFailedEventArgs>? RoleRoutingFailed;

    /// <inheritdoc />
    public event EventHandler<RoleLoadChangedEventArgs>? RoleLoadChanged;

    #endregion

    #region 私有方法

    /// <summary>
    /// 初始化默认角色路由规则
    /// </summary>
    private void InitializeDefaultRoleRoutes()
    {
        // Designer角色路由
        RegisterRoleRoute("flowcustom.designer.*", new[] { NodeRole.Designer }, RoutingStrategy.LeastLoad, 10);
        RegisterRoleRoute("flowcustom.workflow.design.*", new[] { NodeRole.Designer }, RoutingStrategy.RoundRobin, 9);

        // Validator角色路由
        RegisterRoleRoute("flowcustom.validator.*", new[] { NodeRole.Validator }, RoutingStrategy.RoundRobin, 10);
        RegisterRoleRoute("flowcustom.workflow.validate.*", new[] { NodeRole.Validator }, RoutingStrategy.LeastLoad, 9);
        RegisterRoleRoute("flowcustom.validation.*", new[] { NodeRole.Validator }, RoutingStrategy.FastestResponse, 8);

        // Executor角色路由
        RegisterRoleRoute("flowcustom.executor.*", new[] { NodeRole.Executor }, RoutingStrategy.LeastLoad, 10);
        RegisterRoleRoute("flowcustom.workflow.execute.*", new[] { NodeRole.Executor }, RoutingStrategy.LeastLoad, 9);
        RegisterRoleRoute("flowcustom.tasks.high", new[] { NodeRole.Executor }, RoutingStrategy.FastestResponse, 8);
        RegisterRoleRoute("flowcustom.tasks.normal", new[] { NodeRole.Executor }, RoutingStrategy.LeastLoad, 7);
        RegisterRoleRoute("flowcustom.tasks.low", new[] { NodeRole.Executor }, RoutingStrategy.RoundRobin, 6);

        // Monitor角色路由
        RegisterRoleRoute("flowcustom.monitor.*", new[] { NodeRole.Monitor }, RoutingStrategy.RoundRobin, 10);
        RegisterRoleRoute("flowcustom.metrics.*", new[] { NodeRole.Monitor }, RoutingStrategy.RoundRobin, 9);

        // Gateway角色路由
        RegisterRoleRoute("flowcustom.gateway.*", new[] { NodeRole.Gateway }, RoutingStrategy.RoundRobin, 10);
        RegisterRoleRoute("flowcustom.api.*", new[] { NodeRole.Gateway }, RoutingStrategy.LeastLoad, 9);

        // 多角色路由
        RegisterRoleRoute("flowcustom.workflow.full.*", new[] { NodeRole.Designer, NodeRole.Validator, NodeRole.Executor }, RoutingStrategy.RoundRobin, 5);

        _logger.LogInformation("Default role routing rules initialized");
    }

    /// <summary>
    /// 初始化统计信息
    /// </summary>
    private void InitializeStatistics()
    {
        var allRoles = Enum.GetValues<NodeRole>().Where(r => r != NodeRole.None && r != NodeRole.All);

        foreach (var role in allRoles)
        {
            _routingStatistics[role] = new RoleRoutingStatistics { Role = role };
            _roundRobinCounters[role] = 0;
        }
    }

    /// <summary>
    /// 查找匹配的角色路由规则
    /// </summary>
    /// <param name="subject">消息主题</param>
    /// <returns>匹配的路由规则</returns>
    private RoleRouteRule? FindMatchingRoleRoute(string subject)
    {
        return _roleRoutes.Values
            .Where(r => r.Enabled && IsPatternMatch(r.Pattern, subject))
            .OrderByDescending(r => r.Priority)
            .ThenByDescending(r => r.Pattern.Length) // 优先匹配更具体的模式
            .FirstOrDefault();
    }

    /// <summary>
    /// 检查模式是否匹配
    /// </summary>
    /// <param name="pattern">路由模式</param>
    /// <param name="subject">消息主题</param>
    /// <returns>是否匹配</returns>
    private static bool IsPatternMatch(string pattern, string subject)
    {
        // 将NATS通配符模式转换为正则表达式
        var regexPattern = pattern
            .Replace(".", "\\.")
            .Replace("*", "[^.]*")
            .Replace(">", ".*");

        return Regex.IsMatch(subject, $"^{regexPattern}$", RegexOptions.IgnoreCase);
    }

    /// <summary>
    /// 从消息中推断目标角色
    /// </summary>
    /// <param name="message">消息对象</param>
    /// <returns>推断的角色</returns>
    private static NodeRole InferTargetRoleFromMessage(IMessage message)
    {
        var messageType = message.MessageType.ToLowerInvariant();

        return messageType switch
        {
            var type when type.Contains("design") || type.Contains("template") => NodeRole.Designer,
            var type when type.Contains("validate") || type.Contains("validation") => NodeRole.Validator,
            var type when type.Contains("execute") || type.Contains("task") => NodeRole.Executor,
            var type when type.Contains("monitor") || type.Contains("metric") => NodeRole.Monitor,
            var type when type.Contains("api") || type.Contains("gateway") => NodeRole.Gateway,
            _ => NodeRole.None
        };
    }

    /// <summary>
    /// 路由到多个角色
    /// </summary>
    /// <param name="message">消息对象</param>
    /// <param name="targetRoles">目标角色数组</param>
    /// <param name="strategy">路由策略</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>路由任务</returns>
    private async Task RouteToRolesAsync(IMessage message, NodeRole[] targetRoles, RoutingStrategy strategy, CancellationToken cancellationToken)
    {
        if (targetRoles.Length == 1)
        {
            await RouteToRoleAsync(message, targetRoles[0], strategy, cancellationToken);
        }
        else if (strategy == RoutingStrategy.RoundRobin)
        {
            // 轮询到所有角色
            var tasks = targetRoles.Select(role => BroadcastToRoleAsync(message, role, cancellationToken));
            await Task.WhenAll(tasks);
        }
        else
        {
            // 选择一个最佳角色进行路由
            var bestRole = SelectBestRole(targetRoles, strategy);
            await RouteToRoleAsync(message, bestRole, strategy, cancellationToken);
        }
    }

    /// <summary>
    /// 选择最佳角色
    /// </summary>
    /// <param name="targetRoles">目标角色数组</param>
    /// <param name="strategy">路由策略</param>
    /// <returns>选择的角色</returns>
    private NodeRole SelectBestRole(NodeRole[] targetRoles, RoutingStrategy strategy)
    {
        return strategy switch
        {
            RoutingStrategy.LeastLoad => targetRoles.OrderBy(role => GetRoleLoad(role)).First(),
            RoutingStrategy.FastestResponse => targetRoles.OrderBy(role => GetRoleAverageResponseTime(role)).First(),
            RoutingStrategy.Random => targetRoles[_random.Next(targetRoles.Length)],
            _ => targetRoles[0] // 默认选择第一个
        };
    }

    /// <summary>
    /// 根据策略选择节点
    /// </summary>
    /// <param name="nodes">可用节点列表</param>
    /// <param name="strategy">路由策略</param>
    /// <param name="role">目标角色</param>
    /// <returns>选择的节点</returns>
    private ClusterNodeInfo SelectNodeByStrategy(IReadOnlyList<ClusterNodeInfo> nodes, RoutingStrategy strategy, NodeRole role)
    {
        return strategy switch
        {
            RoutingStrategy.RoundRobin => SelectRoundRobin(nodes, role),
            RoutingStrategy.LeastLoad => SelectLeastLoad(nodes),
            RoutingStrategy.FastestResponse => SelectFastestResponse(nodes),
            RoutingStrategy.Random => SelectRandom(nodes),
            _ => nodes.First()
        };
    }

    /// <summary>
    /// 轮询选择节点
    /// </summary>
    /// <param name="nodes">节点列表</param>
    /// <param name="role">目标角色</param>
    /// <returns>选择的节点</returns>
    private ClusterNodeInfo SelectRoundRobin(IReadOnlyList<ClusterNodeInfo> nodes, NodeRole role)
    {
        var counter = _roundRobinCounters.AddOrUpdate(role, 0, (key, value) => (value + 1) % nodes.Count);
        return nodes[counter];
    }

    /// <summary>
    /// 选择负载最低的节点
    /// </summary>
    /// <param name="nodes">节点列表</param>
    /// <returns>选择的节点</returns>
    private static ClusterNodeInfo SelectLeastLoad(IReadOnlyList<ClusterNodeInfo> nodes)
    {
        return nodes.OrderBy(n => n.Load.CpuUsagePercentage)
                   .ThenBy(n => n.Load.MemoryUsagePercentage)
                   .ThenBy(n => n.Load.ActiveTaskCount)
                   .First();
    }

    /// <summary>
    /// 选择响应最快的节点
    /// </summary>
    /// <param name="nodes">节点列表</param>
    /// <returns>选择的节点</returns>
    private static ClusterNodeInfo SelectFastestResponse(IReadOnlyList<ClusterNodeInfo> nodes)
    {
        return nodes.OrderBy(n => n.Load.AverageResponseTimeMs).First();
    }

    /// <summary>
    /// 随机选择节点
    /// </summary>
    /// <param name="nodes">节点列表</param>
    /// <returns>选择的节点</returns>
    private ClusterNodeInfo SelectRandom(IReadOnlyList<ClusterNodeInfo> nodes)
    {
        return nodes[_random.Next(nodes.Count)];
    }

    /// <summary>
    /// 获取消息的主题
    /// </summary>
    /// <param name="message">消息对象</param>
    /// <returns>主题字符串</returns>
    private static string GetSubjectFromMessage(IMessage message)
    {
        return message.MessageType ?? "unknown";
    }

    /// <summary>
    /// 获取角色的总负载
    /// </summary>
    /// <param name="role">目标角色</param>
    /// <returns>总负载</returns>
    private double GetRoleLoad(NodeRole role)
    {
        var nodes = GetAvailableRoleNodes(role);
        return nodes.Any() ? nodes.Average(n => n.Load.CpuUsagePercentage + n.Load.MemoryUsagePercentage) : double.MaxValue;
    }

    /// <summary>
    /// 获取角色的平均响应时间
    /// </summary>
    /// <param name="role">目标角色</param>
    /// <returns>平均响应时间</returns>
    private double GetRoleAverageResponseTime(NodeRole role)
    {
        var stats = GetRoleRoutingStatistics(role);
        return stats.AverageRoutingTimeMs;
    }

    /// <summary>
    /// 更新路由统计信息
    /// </summary>
    /// <param name="role">目标角色</param>
    /// <param name="success">是否成功</param>
    /// <param name="durationMs">耗时</param>
    private void UpdateRoutingStatistics(NodeRole role, bool success, long durationMs)
    {
        if (_routingStatistics.TryGetValue(role, out var stats))
        {
            lock (_lockObject)
            {
                stats.TotalRoutes++;
                stats.LastRoutingTime = DateTime.UtcNow;

                if (success)
                {
                    stats.SuccessfulRoutes++;
                }
                else
                {
                    stats.FailedRoutes++;
                }

                // 更新平均路由时间
                var totalTime = stats.AverageRoutingTimeMs * (stats.TotalRoutes - 1) + durationMs;
                stats.AverageRoutingTimeMs = totalTime / stats.TotalRoutes;
            }
        }
    }

    /// <summary>
    /// 更新角色节点数量统计
    /// </summary>
    /// <param name="role">目标角色</param>
    private void UpdateRoleNodeCount(NodeRole role)
    {
        if (_routingStatistics.TryGetValue(role, out var stats))
        {
            var nodes = GetAvailableRoleNodes(role);

            lock (_lockObject)
            {
                var oldCount = stats.ActiveNodeCount;
                stats.ActiveNodeCount = nodes.Count;

                if (nodes.Any())
                {
                    stats.TotalLoad = nodes.Sum(n => n.Load.CpuUsagePercentage + n.Load.MemoryUsagePercentage);
                    stats.AverageLoad = stats.TotalLoad / nodes.Count;
                }
                else
                {
                    stats.TotalLoad = 0;
                    stats.AverageLoad = 0;
                }

                // 触发负载变更事件
                if (oldCount != stats.ActiveNodeCount)
                {
                    OnRoleLoadChanged(role, stats.AverageLoad, stats.AverageLoad);
                }
            }
        }
    }

    /// <summary>
    /// 重置统计信息
    /// </summary>
    /// <param name="stats">统计对象</param>
    private static void ResetStatistics(RoleRoutingStatistics stats)
    {
        stats.TotalRoutes = 0;
        stats.SuccessfulRoutes = 0;
        stats.FailedRoutes = 0;
        stats.AverageRoutingTimeMs = 0;
        stats.LastRoutingTime = null;
    }

    /// <summary>
    /// 触发角色节点状态变更事件
    /// </summary>
    /// <param name="nodeId">节点ID</param>
    /// <param name="roles">节点角色</param>
    /// <param name="oldStatus">旧状态</param>
    /// <param name="newStatus">新状态</param>
    private void OnRoleNodeStatusChanged(string nodeId, NodeRole roles, NodeStatus oldStatus, NodeStatus newStatus)
    {
        RoleNodeStatusChanged?.Invoke(this, new RoleNodeStatusChangedEventArgs
        {
            NodeId = nodeId,
            Roles = roles,
            OldStatus = oldStatus,
            NewStatus = newStatus
        });
    }

    /// <summary>
    /// 触发角色路由失败事件
    /// </summary>
    /// <param name="messageId">消息ID</param>
    /// <param name="targetRole">目标角色</param>
    /// <param name="reason">失败原因</param>
    /// <param name="exception">异常信息</param>
    private void OnRoleRoutingFailed(string messageId, NodeRole targetRole, string reason, Exception? exception = null)
    {
        RoleRoutingFailed?.Invoke(this, new RoleRoutingFailedEventArgs
        {
            MessageId = messageId,
            TargetRole = targetRole,
            Reason = reason,
            Exception = exception
        });
    }

    /// <summary>
    /// 触发角色负载变更事件
    /// </summary>
    /// <param name="role">角色</param>
    /// <param name="oldLoad">旧负载</param>
    /// <param name="newLoad">新负载</param>
    private void OnRoleLoadChanged(NodeRole role, double oldLoad, double newLoad)
    {
        RoleLoadChanged?.Invoke(this, new RoleLoadChangedEventArgs
        {
            Role = role,
            OldLoad = oldLoad,
            NewLoad = newLoad
        });
    }

    #endregion
}
