using FlowCustomV1.Core.Interfaces;
using FlowCustomV1.Core.Interfaces.Plugins;
using FlowCustomV1.Core.Models.Plugins;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Diagnostics;

namespace FlowCustomV1.Infrastructure.Services.Plugins;

/// <summary>
/// 统一插件管理器
/// 管理三种插件类型：内置插件、JSON配置插件、DLL预编译插件
/// </summary>
public class PluginManager : IPluginManager
{
    private readonly ILogger<PluginManager> _logger;
    private readonly IDynamicNodeCompiler _dynamicCompiler;
    private readonly IEnumerable<IPluginLoader> _pluginLoaders;
    private readonly ConcurrentDictionary<string, INodeExecutor> _loadedExecutors;
    private readonly ConcurrentDictionary<string, PluginInfo> _pluginInfos;
    private readonly PluginStatistics _statistics;
    private bool _isInitialized;

    public PluginManager(
        ILogger<PluginManager> logger,
        IDynamicNodeCompiler dynamicCompiler,
        IEnumerable<IPluginLoader> pluginLoaders)
    {
        _logger = logger;
        _dynamicCompiler = dynamicCompiler;
        _pluginLoaders = pluginLoaders;
        _loadedExecutors = new ConcurrentDictionary<string, INodeExecutor>();
        _pluginInfos = new ConcurrentDictionary<string, PluginInfo>();
        _statistics = new PluginStatistics();
    }

    /// <summary>
    /// 插件加载事件
    /// </summary>
    public event EventHandler<PluginLoadedEventArgs>? PluginLoaded;

    /// <summary>
    /// 插件卸载事件
    /// </summary>
    public event EventHandler<PluginUnloadedEventArgs>? PluginUnloaded;

    /// <summary>
    /// 插件错误事件
    /// </summary>
    public event EventHandler<PluginErrorEventArgs>? PluginError;

    /// <summary>
    /// 初始化插件管理器
    /// </summary>
    public async Task InitializeAsync(CancellationToken cancellationToken = default)
    {
        if (_isInitialized)
            return;

        try
        {
            _logger.LogInformation("正在初始化插件管理器...");

            // 初始化动态编译器
            await _dynamicCompiler.InitializeAsync(cancellationToken);

            // 初始化所有插件加载器
            var initTasks = _pluginLoaders.Select(loader => loader.InitializeAsync(cancellationToken));
            await Task.WhenAll(initTasks);

            // 注册内置插件
            await RegisterBuiltinPluginsAsync(cancellationToken);

            _isInitialized = true;
            _logger.LogInformation("插件管理器初始化完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "初始化插件管理器失败");
            throw;
        }
    }

    /// <summary>
    /// 加载所有插件
    /// </summary>
    public async Task LoadAllPluginsAsync(CancellationToken cancellationToken = default)
    {
        if (!_isInitialized)
            await InitializeAsync(cancellationToken);

        try
        {
            _logger.LogInformation("开始加载所有插件...");

            // 预编译内置插件
            await PrecompileBuiltinPluginsAsync(cancellationToken);

            // 发现并加载DLL插件
            await DiscoverAndLoadDllPluginsAsync(cancellationToken);

            // 发现并加载JSON配置插件
            await DiscoverAndLoadJsonPluginsAsync(cancellationToken);

            UpdateStatistics();
            _logger.LogInformation("所有插件加载完成，共加载 {Count} 个插件", _loadedExecutors.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载所有插件时发生异常");
            throw;
        }
    }

    /// <summary>
    /// 根据节点类型获取插件执行器
    /// </summary>
    public async Task<INodeExecutor?> GetPluginExecutorAsync(string nodeType, CancellationToken cancellationToken = default)
    {
        if (!_isInitialized)
            await InitializeAsync(cancellationToken);

        try
        {
            // 首先检查已加载的执行器
            if (_loadedExecutors.TryGetValue(nodeType, out var executor))
            {
                // 更新使用统计
                if (_pluginInfos.TryGetValue(nodeType, out var pluginInfo))
                {
                    pluginInfo.UsageCount++;
                    pluginInfo.LastUsedAt = DateTime.UtcNow;
                }

                return executor;
            }

            // 尝试动态加载插件
            var loadedExecutor = await TryLoadPluginAsync(nodeType, cancellationToken);
            if (loadedExecutor != null)
            {
                _loadedExecutors.TryAdd(nodeType, loadedExecutor);
                return loadedExecutor;
            }

            _logger.LogWarning("未找到节点类型 {NodeType} 的插件执行器", nodeType);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取插件执行器时发生异常: {NodeType}", nodeType);

            // 触发插件错误事件
            PluginError?.Invoke(this, new PluginErrorEventArgs
            {
                NodeType = nodeType,
                ErrorMessage = ex.Message,
                Exception = ex,
                ErrorType = PluginErrorType.ExecutionError,
                Severity = PluginErrorSeverity.Error
            });

            return null;
        }
    }

    /// <summary>
    /// 获取所有可用的插件信息
    /// </summary>
    public async Task<IReadOnlyList<PluginInfo>> GetAvailablePluginsAsync(CancellationToken cancellationToken = default)
    {
        if (!_isInitialized)
            await InitializeAsync(cancellationToken);

        var allPlugins = new List<PluginInfo>();

        try
        {
            // 获取已加载的插件信息
            allPlugins.AddRange(_pluginInfos.Values);

            // 发现未加载的插件
            var discoveryTasks = _pluginLoaders.Select(async loader =>
            {
                try
                {
                    var pluginPath = GetPluginSearchPath(loader.PluginType);
                    if (!string.IsNullOrEmpty(pluginPath))
                    {
                        var discoveredPlugins = await loader.DiscoverPluginsAsync(pluginPath, cancellationToken);
                        return discoveredPlugins.Where(p => !_pluginInfos.ContainsKey(p.NodeType)).ToList();
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "发现插件时发生异常: {PluginType}", loader.PluginType);
                }

                return new List<PluginInfo>();
            });

            var discoveryResults = await Task.WhenAll(discoveryTasks);
            foreach (var plugins in discoveryResults)
            {
                allPlugins.AddRange(plugins);
            }

            _logger.LogDebug("获取到 {Count} 个可用插件", allPlugins.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取可用插件信息时发生异常");
        }

        return allPlugins.AsReadOnly();
    }

    /// <summary>
    /// 注册内置插件
    /// </summary>
    public async Task RegisterBuiltinPluginAsync(PluginInfo pluginInfo, CancellationToken cancellationToken = default)
    {
        // 移除对InitializeAsync的调用，避免无限递归
        // RegisterBuiltinPluginAsync是在InitializeAsync过程中被调用的，不应该再次调用InitializeAsync

        try
        {
            _logger.LogDebug("注册内置插件: {NodeType}", pluginInfo.NodeType);

            if (_pluginInfos.ContainsKey(pluginInfo.NodeType))
            {
                _logger.LogWarning("内置插件 {NodeType} 已存在，跳过注册", pluginInfo.NodeType);
                return;
            }

            pluginInfo.PluginType = PluginType.Builtin;
            pluginInfo.IsLoaded = false;
            pluginInfo.LoadedAt = null;

            _pluginInfos.TryAdd(pluginInfo.NodeType, pluginInfo);

            _logger.LogInformation("内置插件注册成功: {NodeType}", pluginInfo.NodeType);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "注册内置插件失败: {NodeType}", pluginInfo.NodeType);
            throw;
        }
    }

    /// <summary>
    /// 加载JSON配置插件
    /// </summary>
    public async Task LoadJsonPluginAsync(string configPath, CancellationToken cancellationToken = default)
    {
        if (!_isInitialized)
            await InitializeAsync(cancellationToken);

        try
        {
            _logger.LogDebug("加载JSON配置插件: {ConfigPath}", configPath);

            if (!File.Exists(configPath))
            {
                throw new FileNotFoundException($"JSON配置文件不存在: {configPath}");
            }

            var jsonConfig = await File.ReadAllTextAsync(configPath, cancellationToken);
            var nodeType = Path.GetFileNameWithoutExtension(configPath);

            var compilationResult = await _dynamicCompiler.CompileFromJsonConfigAsync(jsonConfig, nodeType, cancellationToken);

            if (compilationResult.IsSuccess && compilationResult.Executor != null)
            {
                _loadedExecutors.TryAdd(nodeType, compilationResult.Executor);

                var pluginInfo = new PluginInfo
                {
                    NodeType = nodeType,
                    Name = nodeType,
                    DisplayName = nodeType,
                    Description = $"JSON配置插件: {nodeType}",
                    Version = "1.0.0",
                    Author = "FlowCustomV1",
                    PluginType = PluginType.JsonConfig,
                    PluginPath = configPath,
                    IsLoaded = true,
                    LoadedAt = DateTime.UtcNow
                };

                _pluginInfos.TryAdd(nodeType, pluginInfo);

                // 触发插件加载事件
                PluginLoaded?.Invoke(this, new PluginLoadedEventArgs
                {
                    PluginInfo = pluginInfo,
                    LoadTimeMs = compilationResult.CompilationTimeMs
                });

                _logger.LogInformation("JSON配置插件加载成功: {NodeType}", nodeType);
            }
            else
            {
                var errorMessage = string.Join("; ", compilationResult.Errors.Select(e => e.Message));
                throw new InvalidOperationException($"编译JSON配置插件失败: {errorMessage}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载JSON配置插件失败: {ConfigPath}", configPath);

            // 触发插件错误事件
            PluginError?.Invoke(this, new PluginErrorEventArgs
            {
                NodeType = Path.GetFileNameWithoutExtension(configPath),
                ErrorMessage = ex.Message,
                Exception = ex,
                ErrorType = PluginErrorType.LoadError,
                Severity = PluginErrorSeverity.Error
            });

            throw;
        }
    }

    /// <summary>
    /// 加载DLL插件
    /// </summary>
    public async Task LoadDllPluginAsync(string dllPath, CancellationToken cancellationToken = default)
    {
        if (!_isInitialized)
            await InitializeAsync(cancellationToken);

        try
        {
            _logger.LogDebug("加载DLL插件: {DllPath}", dllPath);

            var dllLoader = _pluginLoaders.FirstOrDefault(l => l.PluginType == PluginType.DllPrecompiled);
            if (dllLoader == null)
            {
                throw new InvalidOperationException("未找到DLL插件加载器");
            }

            var loadResult = await dllLoader.LoadPluginAsync(dllPath, cancellationToken);

            if (loadResult.IsSuccess && loadResult.Executor != null && loadResult.PluginInfo != null)
            {
                _loadedExecutors.TryAdd(loadResult.NodeType, loadResult.Executor);
                _pluginInfos.TryAdd(loadResult.NodeType, loadResult.PluginInfo);

                // 触发插件加载事件
                PluginLoaded?.Invoke(this, new PluginLoadedEventArgs
                {
                    PluginInfo = loadResult.PluginInfo,
                    LoadTimeMs = loadResult.LoadTimeMs
                });

                _logger.LogInformation("DLL插件加载成功: {NodeType}", loadResult.NodeType);
            }
            else
            {
                throw new InvalidOperationException($"加载DLL插件失败: {loadResult.ErrorMessage}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载DLL插件失败: {DllPath}", dllPath);

            // 触发插件错误事件
            PluginError?.Invoke(this, new PluginErrorEventArgs
            {
                NodeType = Path.GetFileNameWithoutExtension(dllPath),
                ErrorMessage = ex.Message,
                Exception = ex,
                ErrorType = PluginErrorType.LoadError,
                Severity = PluginErrorSeverity.Error
            });

            throw;
        }
    }

    /// <summary>
    /// 卸载插件
    /// </summary>
    public async Task UnloadPluginAsync(string nodeType, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("卸载插件: {NodeType}", nodeType);

            if (!_loadedExecutors.ContainsKey(nodeType))
            {
                _logger.LogWarning("插件 {NodeType} 未加载，无需卸载", nodeType);
                return;
            }

            // 移除执行器
            _loadedExecutors.TryRemove(nodeType, out _);

            // 获取插件信息
            _pluginInfos.TryGetValue(nodeType, out var pluginInfo);

            // 根据插件类型进行特定的卸载操作
            if (pluginInfo?.PluginType == PluginType.DllPrecompiled)
            {
                var dllLoader = _pluginLoaders.FirstOrDefault(l => l.PluginType == PluginType.DllPrecompiled);
                if (dllLoader != null)
                {
                    await dllLoader.UnloadPluginAsync(nodeType, cancellationToken);
                }
            }

            // 更新插件信息
            if (pluginInfo != null)
            {
                pluginInfo.IsLoaded = false;
                pluginInfo.LoadedAt = null;
            }

            // 触发插件卸载事件
            PluginUnloaded?.Invoke(this, new PluginUnloadedEventArgs
            {
                NodeType = nodeType,
                PluginInfo = pluginInfo,
                UnloadTimeMs = 0,
                Reason = "Manual unload"
            });

            _logger.LogInformation("插件卸载成功: {NodeType}", nodeType);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "卸载插件失败: {NodeType}", nodeType);

            // 触发插件错误事件
            PluginError?.Invoke(this, new PluginErrorEventArgs
            {
                NodeType = nodeType,
                ErrorMessage = ex.Message,
                Exception = ex,
                ErrorType = PluginErrorType.LoadError,
                Severity = PluginErrorSeverity.Error
            });

            throw;
        }
    }

    /// <summary>
    /// 重新加载插件
    /// </summary>
    public async Task ReloadPluginAsync(string nodeType, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("重新加载插件: {NodeType}", nodeType);

            // 获取插件信息
            if (!_pluginInfos.TryGetValue(nodeType, out var pluginInfo))
            {
                throw new InvalidOperationException($"未找到插件信息: {nodeType}");
            }

            // 先卸载
            await UnloadPluginAsync(nodeType, cancellationToken);

            // 等待一段时间确保资源释放
            await Task.Delay(100, cancellationToken);

            // 根据插件类型重新加载
            switch (pluginInfo.PluginType)
            {
                case PluginType.Builtin:
                    await LoadBuiltinPluginAsync(nodeType, cancellationToken);
                    break;
                case PluginType.JsonConfig:
                    await LoadJsonPluginAsync(pluginInfo.PluginPath, cancellationToken);
                    break;
                case PluginType.DllPrecompiled:
                    await LoadDllPluginAsync(pluginInfo.PluginPath, cancellationToken);
                    break;
                default:
                    throw new NotSupportedException($"不支持的插件类型: {pluginInfo.PluginType}");
            }

            _logger.LogInformation("插件重新加载成功: {NodeType}", nodeType);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "重新加载插件失败: {NodeType}", nodeType);
            throw;
        }
    }

    /// <summary>
    /// 检查插件是否已加载
    /// </summary>
    public bool IsPluginLoaded(string nodeType)
    {
        return _loadedExecutors.ContainsKey(nodeType);
    }

    /// <summary>
    /// 获取插件统计信息
    /// </summary>
    public PluginStatistics GetPluginStatistics()
    {
        UpdateStatistics();
        return _statistics;
    }

    #region 私有方法

    /// <summary>
    /// 加载内置插件
    /// </summary>
    private async Task LoadBuiltinPluginAsync(string nodeType, CancellationToken cancellationToken)
    {
        var builtinDefinition = GetBuiltinPluginDefinitions().FirstOrDefault(p => p.NodeType == nodeType);
        if (builtinDefinition == null)
        {
            throw new InvalidOperationException($"未找到内置插件定义: {nodeType}");
        }

        var compilationResult = await _dynamicCompiler.CompileFromBuiltinDefinitionAsync(builtinDefinition, cancellationToken);

        if (compilationResult.IsSuccess && compilationResult.Executor != null)
        {
            _loadedExecutors.TryAdd(nodeType, compilationResult.Executor);

            if (_pluginInfos.TryGetValue(nodeType, out var pluginInfo))
            {
                pluginInfo.IsLoaded = true;
                pluginInfo.LoadedAt = DateTime.UtcNow;

                // 触发插件加载事件
                PluginLoaded?.Invoke(this, new PluginLoadedEventArgs
                {
                    PluginInfo = pluginInfo,
                    LoadTimeMs = compilationResult.CompilationTimeMs
                });
            }
        }
        else
        {
            var errorMessage = string.Join("; ", compilationResult.Errors.Select(e => e.Message));
            throw new InvalidOperationException($"编译内置插件失败: {errorMessage}");
        }
    }

    /// <summary>
    /// 获取内置插件定义
    /// </summary>
    private List<BuiltinPluginDefinition> GetBuiltinPluginDefinitions()
    {
        var definitions = new List<BuiltinPluginDefinition>();

        // 基础控制节点
        definitions.AddRange(GetBasicControlNodes());

        // 触发器节点
        definitions.AddRange(GetTriggerNodes());

        // 动作节点
        definitions.AddRange(GetActionNodes());

        // 控制流节点
        definitions.AddRange(GetControlFlowNodes());

        // 数据转换节点
        definitions.AddRange(GetDataTransformNodes());

        // 外部服务集成节点
        definitions.AddRange(GetExternalServiceNodes());

        return definitions;
    }

    /// <summary>
    /// 获取基础控制节点定义
    /// </summary>
    private List<BuiltinPluginDefinition> GetBasicControlNodes()
    {
        return new List<BuiltinPluginDefinition>
        {
            new BuiltinPluginDefinition
            {
                NodeType = "Start",
                Name = "开始节点",
                DisplayName = "开始",
                Description = "工作流开始节点",
                Version = "1.0.0",
                Author = "FlowCustomV1",
                Tags = new List<string> { "builtin", "control", "start" },
                ExecutionLogic = new ExecutionLogicTemplate
                {
                    Type = ExecutionLogicType.Code,
                    CodeTemplate = @"
                        result.OutputData[""started""] = true;
                        result.OutputData[""startTime""] = DateTime.UtcNow;
                        result.OutputData[""workflowId""] = context.WorkflowId;
                        result.OutputData[""executionId""] = context.ExecutionId;
                        await Task.CompletedTask;
                    "
                }
            },
            new BuiltinPluginDefinition
            {
                NodeType = "End",
                Name = "结束节点",
                DisplayName = "结束",
                Description = "工作流结束节点",
                Version = "1.0.0",
                Author = "FlowCustomV1",
                Tags = new List<string> { "builtin", "control", "end" },
                ExecutionLogic = new ExecutionLogicTemplate
                {
                    Type = ExecutionLogicType.Code,
                    CodeTemplate = @"
                        result.OutputData[""completed""] = true;
                        result.OutputData[""endTime""] = DateTime.UtcNow;
                        result.OutputData[""duration""] = DateTime.UtcNow - context.StartedAt;

                        // 从输入数据中获取最终结果
                        if (context.InputData.ContainsKey(""finalResult""))
                        {
                            result.OutputData[""finalResult""] = context.InputData[""finalResult""];
                        }

                        await Task.CompletedTask;
                    "
                }
            },
            new BuiltinPluginDefinition
            {
                NodeType = "Task",
                Name = "任务节点",
                DisplayName = "任务",
                Description = "通用任务节点",
                Version = "1.0.0",
                Author = "FlowCustomV1",
                Tags = new List<string> { "builtin", "task", "general" },
                ExecutionLogic = new ExecutionLogicTemplate
                {
                    Type = ExecutionLogicType.Code,
                    CodeTemplate = @"
                        // 获取任务配置
                        var taskName = context.InputData.GetValueOrDefault(""taskName"", ""DefaultTask"").ToString();
                        var delay = Convert.ToInt32(context.InputData.GetValueOrDefault(""delay"", 100));

                        // 执行任务逻辑
                        await Task.Delay(delay, cancellationToken);

                        result.OutputData[""taskCompleted""] = true;
                        result.OutputData[""taskName""] = taskName;
                        result.OutputData[""executedAt""] = DateTime.UtcNow;
                        result.OutputData[""delay""] = delay;
                    "
                }
            }
        };
    }

    /// <summary>
    /// 注册内置插件
    /// </summary>
    private async Task RegisterBuiltinPluginsAsync(CancellationToken cancellationToken)
    {
        var builtinPlugins = GetBuiltinPluginDefinitions();

        foreach (var plugin in builtinPlugins)
        {
            var pluginInfo = new PluginInfo
            {
                NodeType = plugin.NodeType,
                Name = plugin.Name,
                DisplayName = plugin.DisplayName,
                Description = plugin.Description,
                Version = plugin.Version,
                Author = plugin.Author,
                PluginType = PluginType.Builtin,
                Tags = plugin.Tags,
                IsLoaded = false
            };

            await RegisterBuiltinPluginAsync(pluginInfo, cancellationToken);
        }
    }

    /// <summary>
    /// 预编译内置插件
    /// </summary>
    private async Task PrecompileBuiltinPluginsAsync(CancellationToken cancellationToken)
    {
        var builtinPlugins = _pluginInfos.Values.Where(p => p.PluginType == PluginType.Builtin).ToList();

        var compileTasks = builtinPlugins.Select(async pluginInfo =>
        {
            try
            {
                await LoadBuiltinPluginAsync(pluginInfo.NodeType, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "预编译内置插件失败: {NodeType}", pluginInfo.NodeType);
            }
        });

        await Task.WhenAll(compileTasks);
    }

    /// <summary>
    /// 发现并加载DLL插件
    /// </summary>
    private async Task DiscoverAndLoadDllPluginsAsync(CancellationToken cancellationToken)
    {
        var dllLoader = _pluginLoaders.FirstOrDefault(l => l.PluginType == PluginType.DllPrecompiled);
        if (dllLoader == null)
            return;

        var pluginPath = GetPluginSearchPath(PluginType.DllPrecompiled);
        if (string.IsNullOrEmpty(pluginPath) || !Directory.Exists(pluginPath))
            return;

        try
        {
            var discoveredPlugins = await dllLoader.DiscoverPluginsAsync(pluginPath, cancellationToken);

            var loadTasks = discoveredPlugins.Select(async plugin =>
            {
                try
                {
                    if (!_loadedExecutors.ContainsKey(plugin.NodeType))
                    {
                        await LoadDllPluginAsync(plugin.PluginPath, cancellationToken);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "加载DLL插件失败: {PluginPath}", plugin.PluginPath);
                }
            });

            await Task.WhenAll(loadTasks);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "发现DLL插件时发生异常");
        }
    }

    /// <summary>
    /// 发现并加载JSON配置插件
    /// </summary>
    private async Task DiscoverAndLoadJsonPluginsAsync(CancellationToken cancellationToken)
    {
        var pluginPath = GetPluginSearchPath(PluginType.JsonConfig);
        if (string.IsNullOrEmpty(pluginPath) || !Directory.Exists(pluginPath))
            return;

        try
        {
            var jsonFiles = Directory.GetFiles(pluginPath, "*.json", SearchOption.AllDirectories);

            var loadTasks = jsonFiles.Select(async jsonFile =>
            {
                try
                {
                    var nodeType = Path.GetFileNameWithoutExtension(jsonFile);
                    if (!_loadedExecutors.ContainsKey(nodeType))
                    {
                        await LoadJsonPluginAsync(jsonFile, cancellationToken);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "加载JSON配置插件失败: {JsonFile}", jsonFile);
                }
            });

            await Task.WhenAll(loadTasks);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "发现JSON配置插件时发生异常");
        }
    }

    /// <summary>
    /// 尝试加载插件
    /// </summary>
    private async Task<INodeExecutor?> TryLoadPluginAsync(string nodeType, CancellationToken cancellationToken)
    {
        // 尝试加载内置插件
        if (_pluginInfos.TryGetValue(nodeType, out var pluginInfo) && pluginInfo.PluginType == PluginType.Builtin)
        {
            try
            {
                await LoadBuiltinPluginAsync(nodeType, cancellationToken);
                return _loadedExecutors.GetValueOrDefault(nodeType);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "动态加载内置插件失败: {NodeType}", nodeType);
            }
        }

        return null;
    }

    /// <summary>
    /// 获取插件搜索路径
    /// </summary>
    private string GetPluginSearchPath(PluginType pluginType)
    {
        return pluginType switch
        {
            PluginType.DllPrecompiled => Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "plugins", "dll"),
            PluginType.JsonConfig => Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "plugins", "json"),
            _ => string.Empty
        };
    }

    /// <summary>
    /// 获取触发器节点定义
    /// </summary>
    private List<BuiltinPluginDefinition> GetTriggerNodes()
    {
        return new List<BuiltinPluginDefinition>
        {
            new BuiltinPluginDefinition
            {
                NodeType = "TimerTrigger",
                Name = "定时触发器",
                DisplayName = "定时触发",
                Description = "按指定时间间隔触发工作流",
                Version = "1.0.0",
                Author = "FlowCustomV1",
                Tags = new List<string> { "builtin", "trigger", "timer" },
                ExecutionLogic = new ExecutionLogicTemplate
                {
                    Type = ExecutionLogicType.Code,
                    CodeTemplate = @"
                        // 获取定时器配置
                        var intervalSeconds = Convert.ToInt32(context.InputData.GetValueOrDefault(""intervalSeconds"", 60));
                        var maxExecutions = Convert.ToInt32(context.InputData.GetValueOrDefault(""maxExecutions"", -1));
                        var currentExecution = Convert.ToInt32(context.InputData.GetValueOrDefault(""currentExecution"", 1));

                        // 检查是否达到最大执行次数
                        if (maxExecutions > 0 && currentExecution > maxExecutions)
                        {
                            result.OutputData[""triggered""] = false;
                            result.OutputData[""reason""] = ""Max executions reached"";
                            return;
                        }

                        // 模拟定时器触发
                        await Task.Delay(1000, cancellationToken); // 模拟触发检查

                        result.OutputData[""triggered""] = true;
                        result.OutputData[""triggerTime""] = DateTime.UtcNow;
                        result.OutputData[""intervalSeconds""] = intervalSeconds;
                        result.OutputData[""currentExecution""] = currentExecution;
                        result.OutputData[""nextExecution""] = currentExecution + 1;
                    "
                }
            },
            new BuiltinPluginDefinition
            {
                NodeType = "EventTrigger",
                Name = "事件触发器",
                DisplayName = "事件触发",
                Description = "监听特定事件并触发工作流",
                Version = "1.0.0",
                Author = "FlowCustomV1",
                Tags = new List<string> { "builtin", "trigger", "event" },
                ExecutionLogic = new ExecutionLogicTemplate
                {
                    Type = ExecutionLogicType.Code,
                    CodeTemplate = @"
                        // 获取事件配置
                        var eventType = context.InputData.GetValueOrDefault(""eventType"", ""default"").ToString();
                        var eventSource = context.InputData.GetValueOrDefault(""eventSource"", ""system"").ToString();
                        var eventData = context.InputData.GetValueOrDefault(""eventData"", new Dictionary<string, object>());

                        // 模拟事件监听
                        await Task.Delay(500, cancellationToken);

                        result.OutputData[""triggered""] = true;
                        result.OutputData[""eventType""] = eventType;
                        result.OutputData[""eventSource""] = eventSource;
                        result.OutputData[""eventData""] = eventData;
                        result.OutputData[""triggerTime""] = DateTime.UtcNow;
                        result.OutputData[""eventId""] = Guid.NewGuid().ToString();
                    "
                }
            },
            new BuiltinPluginDefinition
            {
                NodeType = "WebhookTrigger",
                Name = "Webhook触发器",
                DisplayName = "Webhook触发",
                Description = "接收HTTP Webhook请求并触发工作流",
                Version = "1.0.0",
                Author = "FlowCustomV1",
                Tags = new List<string> { "builtin", "trigger", "webhook", "http" },
                ExecutionLogic = new ExecutionLogicTemplate
                {
                    Type = ExecutionLogicType.Code,
                    CodeTemplate = @"
                        // 获取Webhook配置
                        var webhookUrl = context.InputData.GetValueOrDefault(""webhookUrl"", ""/webhook/default"").ToString();
                        var method = context.InputData.GetValueOrDefault(""method"", ""POST"").ToString();
                        var headers = context.InputData.GetValueOrDefault(""headers"", new Dictionary<string, string>());
                        var payload = context.InputData.GetValueOrDefault(""payload"", new Dictionary<string, object>());

                        // 模拟Webhook接收
                        await Task.Delay(200, cancellationToken);

                        result.OutputData[""triggered""] = true;
                        result.OutputData[""webhookUrl""] = webhookUrl;
                        result.OutputData[""method""] = method;
                        result.OutputData[""headers""] = headers;
                        result.OutputData[""payload""] = payload;
                        result.OutputData[""triggerTime""] = DateTime.UtcNow;
                        result.OutputData[""requestId""] = Guid.NewGuid().ToString();
                    "
                }
            }
        };
    }

    /// <summary>
    /// 获取动作节点定义
    /// </summary>
    private List<BuiltinPluginDefinition> GetActionNodes()
    {
        return new List<BuiltinPluginDefinition>
        {
            new BuiltinPluginDefinition
            {
                NodeType = "HttpRequest",
                Name = "HTTP请求",
                DisplayName = "HTTP请求",
                Description = "发送HTTP请求到指定URL",
                Version = "1.0.0",
                Author = "FlowCustomV1",
                Tags = new List<string> { "builtin", "action", "http", "api" },
                ExecutionLogic = new ExecutionLogicTemplate
                {
                    Type = ExecutionLogicType.Code,
                    CodeTemplate = @"
                        // 获取HTTP请求配置
                        var url = context.InputData.GetValueOrDefault(""url"", ""https://httpbin.org/get"").ToString();
                        var method = context.InputData.GetValueOrDefault(""method"", ""GET"").ToString();
                        var headers = context.InputData.GetValueOrDefault(""headers"", new Dictionary<string, string>()) as Dictionary<string, string> ?? new Dictionary<string, string>();
                        var body = context.InputData.GetValueOrDefault(""body"", """").ToString();
                        var timeout = Convert.ToInt32(context.InputData.GetValueOrDefault(""timeout"", 30000));

                        try
                        {
                            using var httpClient = new HttpClient();
                            httpClient.Timeout = TimeSpan.FromMilliseconds(timeout);

                            // 添加请求头
                            foreach (var header in headers)
                            {
                                httpClient.DefaultRequestHeaders.TryAddWithoutValidation(header.Key, header.Value);
                            }

                            HttpResponseMessage response;

                            // 根据方法发送请求
                            switch (method.ToUpper())
                            {
                                case ""GET"":
                                    response = await httpClient.GetAsync(url, cancellationToken);
                                    break;
                                case ""POST"":
                                    var content = new StringContent(body, System.Text.Encoding.UTF8, ""application/json"");
                                    response = await httpClient.PostAsync(url, content, cancellationToken);
                                    break;
                                case ""PUT"":
                                    var putContent = new StringContent(body, System.Text.Encoding.UTF8, ""application/json"");
                                    response = await httpClient.PutAsync(url, putContent, cancellationToken);
                                    break;
                                case ""DELETE"":
                                    response = await httpClient.DeleteAsync(url, cancellationToken);
                                    break;
                                default:
                                    throw new NotSupportedException($""HTTP method {method} is not supported"");
                            }

                            var responseBody = await response.Content.ReadAsStringAsync(cancellationToken);

                            result.OutputData[""success""] = true;
                            result.OutputData[""statusCode""] = (int)response.StatusCode;
                            result.OutputData[""statusText""] = response.StatusCode.ToString();
                            result.OutputData[""responseBody""] = responseBody;
                            result.OutputData[""responseHeaders""] = response.Headers.ToDictionary(h => h.Key, h => string.Join("","", h.Value));
                            result.OutputData[""requestTime""] = DateTime.UtcNow;
                        }
                        catch (Exception ex)
                        {
                            result.OutputData[""success""] = false;
                            result.OutputData[""error""] = ex.Message;
                            result.OutputData[""errorType""] = ex.GetType().Name;
                        }
                    "
                }
            },
            new BuiltinPluginDefinition
            {
                NodeType = "DataProcessor",
                Name = "数据处理器",
                DisplayName = "数据处理",
                Description = "对输入数据进行处理和转换",
                Version = "1.0.0",
                Author = "FlowCustomV1",
                Tags = new List<string> { "builtin", "action", "data", "processing" },
                ExecutionLogic = new ExecutionLogicTemplate
                {
                    Type = ExecutionLogicType.Code,
                    CodeTemplate = @"
                        // 获取数据处理配置
                        var operation = context.InputData.GetValueOrDefault(""operation"", ""passthrough"").ToString();
                        var inputData = context.InputData.GetValueOrDefault(""data"", new Dictionary<string, object>());
                        var processingRules = context.InputData.GetValueOrDefault(""rules"", new List<string>()) as List<string> ?? new List<string>();

                        var processedData = new Dictionary<string, object>();

                        try
                        {
                            switch (operation.ToLower())
                            {
                                case ""passthrough"":
                                    processedData = new Dictionary<string, object>(context.InputData);
                                    break;

                                case ""filter"":
                                    // 简单的数据过滤
                                    foreach (var kvp in context.InputData)
                                    {
                                        if (processingRules.Count == 0 || processingRules.Contains(kvp.Key))
                                        {
                                            processedData[kvp.Key] = kvp.Value;
                                        }
                                    }
                                    break;

                                case ""transform"":
                                    // 简单的数据转换
                                    foreach (var kvp in context.InputData)
                                    {
                                        if (kvp.Value is string strValue && double.TryParse(strValue, out double numValue))
                                        {
                                            processedData[kvp.Key] = numValue;
                                        }
                                        else
                                        {
                                            processedData[kvp.Key] = kvp.Value;
                                        }
                                    }
                                    break;

                                case ""aggregate"":
                                    // 简单的数据聚合
                                    var numericValues = context.InputData.Values
                                        .Where(v => v is double || v is int || v is float)
                                        .Select(v => Convert.ToDouble(v))
                                        .ToList();

                                    if (numericValues.Any())
                                    {
                                        processedData[""sum""] = numericValues.Sum();
                                        processedData[""average""] = numericValues.Average();
                                        processedData[""min""] = numericValues.Min();
                                        processedData[""max""] = numericValues.Max();
                                        processedData[""count""] = numericValues.Count;
                                    }
                                    break;

                                default:
                                    processedData = new Dictionary<string, object>(context.InputData);
                                    break;
                            }

                            result.OutputData[""success""] = true;
                            result.OutputData[""operation""] = operation;
                            result.OutputData[""processedData""] = processedData;
                            result.OutputData[""originalCount""] = context.InputData.Count;
                            result.OutputData[""processedCount""] = processedData.Count;
                            result.OutputData[""processedAt""] = DateTime.UtcNow;
                        }
                        catch (Exception ex)
                        {
                            result.OutputData[""success""] = false;
                            result.OutputData[""error""] = ex.Message;
                            result.OutputData[""errorType""] = ex.GetType().Name;
                        }
                    "
                }
            },
            new BuiltinPluginDefinition
            {
                NodeType = "NotificationSender",
                Name = "通知发送器",
                DisplayName = "发送通知",
                Description = "发送各种类型的通知消息",
                Version = "1.0.0",
                Author = "FlowCustomV1",
                Tags = new List<string> { "builtin", "action", "notification", "message" },
                ExecutionLogic = new ExecutionLogicTemplate
                {
                    Type = ExecutionLogicType.Code,
                    CodeTemplate = @"
                        // 获取通知配置
                        var notificationType = context.InputData.GetValueOrDefault(""type"", ""log"").ToString();
                        var message = context.InputData.GetValueOrDefault(""message"", ""Default notification message"").ToString();
                        var recipient = context.InputData.GetValueOrDefault(""recipient"", ""system"").ToString();
                        var priority = context.InputData.GetValueOrDefault(""priority"", ""normal"").ToString();
                        var metadata = context.InputData.GetValueOrDefault(""metadata"", new Dictionary<string, object>()) as Dictionary<string, object> ?? new Dictionary<string, object>();

                        try
                        {
                            var notificationId = Guid.NewGuid().ToString();
                            var sentAt = DateTime.UtcNow;

                            // 模拟不同类型的通知发送
                            switch (notificationType.ToLower())
                            {
                                case ""email"":
                                    // 模拟邮件发送
                                    await Task.Delay(1000, cancellationToken);
                                    result.OutputData[""emailSent""] = true;
                                    result.OutputData[""emailId""] = notificationId;
                                    break;

                                case ""sms"":
                                    // 模拟短信发送
                                    await Task.Delay(500, cancellationToken);
                                    result.OutputData[""smsSent""] = true;
                                    result.OutputData[""smsId""] = notificationId;
                                    break;

                                case ""webhook"":
                                    // 模拟Webhook通知
                                    await Task.Delay(300, cancellationToken);
                                    result.OutputData[""webhookSent""] = true;
                                    result.OutputData[""webhookId""] = notificationId;
                                    break;

                                case ""log"":
                                default:
                                    // 日志通知
                                    result.OutputData[""logWritten""] = true;
                                    result.OutputData[""logEntry""] = $""[{priority.ToUpper()}] {message}"";
                                    break;
                            }

                            result.OutputData[""success""] = true;
                            result.OutputData[""notificationType""] = notificationType;
                            result.OutputData[""message""] = message;
                            result.OutputData[""recipient""] = recipient;
                            result.OutputData[""priority""] = priority;
                            result.OutputData[""notificationId""] = notificationId;
                            result.OutputData[""sentAt""] = sentAt;
                            result.OutputData[""metadata""] = metadata;
                        }
                        catch (Exception ex)
                        {
                            result.OutputData[""success""] = false;
                            result.OutputData[""error""] = ex.Message;
                            result.OutputData[""errorType""] = ex.GetType().Name;
                        }
                    "
                }
            }
        };
    }

    /// <summary>
    /// 获取控制流节点定义
    /// </summary>
    private List<BuiltinPluginDefinition> GetControlFlowNodes()
    {
        return BuiltinNodeDefinitions.GetControlFlowNodes();
    }

    /// <summary>
    /// 获取数据转换节点定义
    /// </summary>
    private List<BuiltinPluginDefinition> GetDataTransformNodes()
    {
        return BuiltinNodeDefinitions.GetDataTransformNodes();
    }

    /// <summary>
    /// 获取外部服务集成节点定义
    /// </summary>
    private List<BuiltinPluginDefinition> GetExternalServiceNodes()
    {
        return BuiltinNodeDefinitions.GetExternalServiceNodes();
    }

    /// <summary>
    /// 更新统计信息
    /// </summary>
    private void UpdateStatistics()
    {
        _statistics.TotalPlugins = _pluginInfos.Count;
        _statistics.LoadedPlugins = _loadedExecutors.Count;
        _statistics.BuiltinPlugins = _pluginInfos.Values.Count(p => p.PluginType == PluginType.Builtin);
        _statistics.JsonConfigPlugins = _pluginInfos.Values.Count(p => p.PluginType == PluginType.JsonConfig);
        _statistics.DllPrecompiledPlugins = _pluginInfos.Values.Count(p => p.PluginType == PluginType.DllPrecompiled);
        _statistics.TotalUsageCount = _pluginInfos.Values.Sum(p => p.UsageCount);
        _statistics.LastUpdated = DateTime.UtcNow;
    }

    #endregion
}
