using System.Collections.Concurrent;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using FlowCustomV1.Core.Interfaces.Scheduling;
using FlowCustomV1.Core.Interfaces.Messaging;
using FlowCustomV1.Core.Models.Scheduling;
using FlowCustomV1.Infrastructure.Configuration;

namespace FlowCustomV1.Infrastructure.Services.Scheduling;

/// <summary>
/// 任务执行状态跟踪服务实现
/// 提供分布式任务执行状态的实时跟踪和监控功能
/// </summary>
public class TaskExecutionTracker : ITaskExecutionTracker, IDisposable
{
    private readonly ILogger<TaskExecutionTracker> _logger;
    private readonly INatsMessageRouter _messageRouter;
    private readonly TaskTrackingConfiguration _config;
    
    // 任务状态存储
    private readonly ConcurrentDictionary<string, TaskExecutionState> _taskStates = new();
    private readonly ConcurrentDictionary<string, TaskTrackingResult> _trackingResults = new();
    
    // 统计数据
    private readonly ConcurrentDictionary<string, NodeTaskStatistics> _nodeStatistics = new();
    private readonly ConcurrentQueue<TaskExecutionState> _recentCompletedTasks = new();
    
    // 定时器
    private readonly Timer _cleanupTimer;
    private readonly Timer _statisticsTimer;
    
    private bool _disposed = false;

    public TaskExecutionTracker(
        ILogger<TaskExecutionTracker> logger,
        INatsMessageRouter messageRouter,
        IOptions<TaskTrackingConfiguration> config)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _messageRouter = messageRouter ?? throw new ArgumentNullException(nameof(messageRouter));
        _config = config?.Value ?? throw new ArgumentNullException(nameof(config));

        // 初始化定时器
        _cleanupTimer = new Timer(CleanupExpiredDataCallback, null,
            (int)TimeSpan.FromMinutes(_config.CleanupIntervalMinutes).TotalMilliseconds,
            (int)TimeSpan.FromMinutes(_config.CleanupIntervalMinutes).TotalMilliseconds);

        _statisticsTimer = new Timer(UpdateStatisticsCallback, null,
            (int)TimeSpan.FromSeconds(_config.StatisticsUpdateIntervalSeconds).TotalMilliseconds,
            (int)TimeSpan.FromSeconds(_config.StatisticsUpdateIntervalSeconds).TotalMilliseconds);
    }

    #region 事件

    public event EventHandler<TaskStatusChangedEventArgs>? TaskStatusChanged;
    public event EventHandler<TaskProgressUpdatedEventArgs>? TaskProgressUpdated;
    public event EventHandler<TaskExecutionCompletedEventArgs>? TaskExecutionCompleted;
    public event EventHandler<TaskExecutionFailedEventArgs>? TaskExecutionFailed;

    #endregion

    #region 任务状态跟踪

    /// <inheritdoc />
    public async Task<TaskTrackingResult> StartTrackingAsync(
        TaskExecution taskExecution,
        CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(taskExecution);

        try
        {
            _logger.LogDebug("Starting tracking for task {TaskId}", taskExecution.TaskId);

            var trackingResult = new TaskTrackingResult
            {
                IsSuccess = true,
                TaskId = taskExecution.TaskId,
                StartTime = DateTime.UtcNow
            };

            var taskState = new TaskExecutionState
            {
                TaskId = taskExecution.TaskId,
                WorkflowId = taskExecution.WorkflowId,
                ExecutionId = taskExecution.ExecutionId,
                ExecutorNodeId = taskExecution.ExecutorNodeId,
                Status = TaskExecutionStatus.Pending,
                StatusChangedAt = DateTime.UtcNow,
                Progress = new TaskProgress(),
                LastUpdated = DateTime.UtcNow
            };

            // 添加初始状态历史
            taskState.StatusHistory.Add(new TaskStatusHistory
            {
                Status = TaskExecutionStatus.Pending,
                ChangedAt = DateTime.UtcNow,
                Reason = "Task tracking started"
            });

            // 存储任务状态
            _taskStates.TryAdd(taskExecution.TaskId, taskState);
            _trackingResults.TryAdd(taskExecution.TaskId, trackingResult);

            // 发布任务开始跟踪事件
            await PublishTaskEventAsync("task.tracking.started", taskState, cancellationToken);

            _logger.LogInformation("Started tracking task {TaskId} on node {NodeId}", 
                taskExecution.TaskId, taskExecution.ExecutorNodeId);

            return trackingResult;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start tracking task {TaskId}", taskExecution.TaskId);
            
            return new TaskTrackingResult
            {
                IsSuccess = false,
                TaskId = taskExecution.TaskId,
                ErrorMessage = ex.Message
            };
        }
    }

    /// <inheritdoc />
    public async Task<TaskStatusUpdateResult> UpdateTaskStatusAsync(
        string taskId,
        TaskExecutionStatus status,
        TaskProgress? progress = null,
        CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrEmpty(taskId);

        try
        {
            if (!_taskStates.TryGetValue(taskId, out var taskState))
            {
                var errorMsg = $"Task {taskId} not found in tracking system";
                _logger.LogWarning(errorMsg);
                
                return new TaskStatusUpdateResult
                {
                    IsSuccess = false,
                    TaskId = taskId,
                    ErrorMessage = errorMsg
                };
            }

            var oldStatus = taskState.Status;
            var updateTime = DateTime.UtcNow;

            // 更新状态
            taskState.PreviousStatus = oldStatus;
            taskState.Status = status;
            taskState.StatusChangedAt = updateTime;
            taskState.LastUpdated = updateTime;

            // 更新进度
            if (progress != null)
            {
                taskState.Progress = progress;
                taskState.Progress.LastUpdated = updateTime;
            }

            // 更新时间戳
            if (status == TaskExecutionStatus.Running && !taskState.StartTime.HasValue)
            {
                taskState.StartTime = updateTime;
            }
            else if (IsTerminalStatus(status) && !taskState.EndTime.HasValue)
            {
                taskState.EndTime = updateTime;
            }

            // 添加状态历史
            var previousHistory = taskState.StatusHistory.LastOrDefault();
            if (previousHistory != null && previousHistory.Status != status)
            {
                previousHistory.DurationMs = (long)(updateTime - previousHistory.ChangedAt).TotalMilliseconds;
            }

            taskState.StatusHistory.Add(new TaskStatusHistory
            {
                Status = status,
                ChangedAt = updateTime,
                Reason = GetStatusChangeReason(oldStatus, status)
            });

            var result = new TaskStatusUpdateResult
            {
                IsSuccess = true,
                TaskId = taskId,
                OldStatus = oldStatus,
                NewStatus = status,
                UpdateTime = updateTime
            };

            // 触发事件
            OnTaskStatusChanged(new TaskStatusChangedEventArgs
            {
                TaskId = taskId,
                OldStatus = oldStatus,
                NewStatus = status,
                TaskState = taskState,
                UpdateTime = updateTime
            });

            if (progress != null)
            {
                OnTaskProgressUpdated(new TaskProgressUpdatedEventArgs
                {
                    TaskId = taskId,
                    Progress = progress,
                    TaskState = taskState,
                    UpdateTime = updateTime
                });
            }

            // 处理终端状态
            if (IsTerminalStatus(status))
            {
                await HandleTerminalStatusAsync(taskId, status, cancellationToken);
            }

            // 发布状态更新事件
            await PublishTaskEventAsync("task.status.updated", taskState, cancellationToken);

            _logger.LogDebug("Updated task {TaskId} status from {OldStatus} to {NewStatus}", 
                taskId, oldStatus, status);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update task {TaskId} status to {Status}", taskId, status);
            
            return new TaskStatusUpdateResult
            {
                IsSuccess = false,
                TaskId = taskId,
                ErrorMessage = ex.Message
            };
        }
    }

    /// <inheritdoc />
    public async Task<TaskTrackingStopResult> StopTrackingAsync(
        string taskId,
        TaskExecutionResult finalResult,
        CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrEmpty(taskId);
        ArgumentNullException.ThrowIfNull(finalResult);

        try
        {
            if (!_taskStates.TryGetValue(taskId, out var taskState))
            {
                var errorMsg = $"Task {taskId} not found in tracking system";
                _logger.LogWarning(errorMsg);
                
                return new TaskTrackingStopResult
                {
                    IsSuccess = false,
                    TaskId = taskId,
                    ErrorMessage = errorMsg
                };
            }

            if (!_trackingResults.TryGetValue(taskId, out var trackingResult))
            {
                _logger.LogWarning("Tracking result not found for task {TaskId}", taskId);
                trackingResult = new TaskTrackingResult { TaskId = taskId };
            }

            var stopTime = DateTime.UtcNow;
            var totalTrackingTime = (long)(stopTime - trackingResult.StartTime).TotalMilliseconds;

            // 更新任务状态
            taskState.EndTime = finalResult.EndTime;
            taskState.LastUpdated = stopTime;
            
            if (finalResult.IsSuccess)
            {
                taskState.Status = TaskExecutionStatus.Completed;
            }
            else
            {
                taskState.Status = TaskExecutionStatus.Failed;
                taskState.ErrorMessage = finalResult.ErrorMessage;
            }

            // 设置资源使用情况
            taskState.ResourceUsage = ConvertResourceUsage(finalResult.ResourceUsage);

            // 创建停止结果
            var result = new TaskTrackingStopResult
            {
                IsSuccess = true,
                TaskId = taskId,
                TrackingId = trackingResult.TrackingId,
                StopTime = stopTime,
                TotalTrackingTimeMs = totalTrackingTime,
                FinalStatus = taskState.Status,
                TrackingSummary = ConvertTrackingSummaryToDictionary(CreateTrackingSummary(taskState))
            };

            // 移动到已完成任务队列
            _recentCompletedTasks.Enqueue(taskState);
            
            // 保持队列大小
            while (_recentCompletedTasks.Count > _config.MaxRecentCompletedTasks)
            {
                _recentCompletedTasks.TryDequeue(out _);
            }

            // 触发完成事件
            if (finalResult.IsSuccess)
            {
                OnTaskExecutionCompleted(new TaskExecutionCompletedEventArgs
                {
                    TaskId = taskId,
                    TaskState = taskState,
                    ExecutionResult = finalResult,
                    CompletedAt = stopTime
                });
            }
            else
            {
                OnTaskExecutionFailed(new TaskExecutionFailedEventArgs
                {
                    TaskId = taskId,
                    TaskState = taskState,
                    ExecutionResult = finalResult,
                    FailedAt = stopTime,
                    ErrorMessage = finalResult.ErrorMessage ?? "Unknown error"
                });
            }

            // 发布跟踪停止事件
            await PublishTaskEventAsync("task.tracking.stopped", taskState, cancellationToken);

            // 清理跟踪数据（如果配置允许）
            if (_config.AutoCleanupOnCompletion)
            {
                _taskStates.TryRemove(taskId, out _);
                _trackingResults.TryRemove(taskId, out _);
            }

            _logger.LogInformation("Stopped tracking task {TaskId} with final status {Status} " +
                                 "after {TrackingTime}ms", 
                taskId, taskState.Status, totalTrackingTime);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to stop tracking task {TaskId}", taskId);
            
            return new TaskTrackingStopResult
            {
                IsSuccess = false,
                TaskId = taskId,
                ErrorMessage = ex.Message
            };
        }
    }

    /// <inheritdoc />
    public Task<TaskExecutionState?> GetTaskStatusAsync(
        string taskId,
        CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrEmpty(taskId);

        _taskStates.TryGetValue(taskId, out var taskState);
        return Task.FromResult(taskState);
    }

    /// <inheritdoc />
    public Task<IReadOnlyList<TaskExecutionState>> GetTaskStatusBatchAsync(
        IReadOnlyList<string> taskIds,
        CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(taskIds);

        var results = new List<TaskExecutionState>();
        
        foreach (var taskId in taskIds)
        {
            if (_taskStates.TryGetValue(taskId, out var taskState))
            {
                results.Add(taskState);
            }
        }

        return Task.FromResult<IReadOnlyList<TaskExecutionState>>(results);
    }

    #endregion

    #region 任务查询

    /// <inheritdoc />
    public Task<IReadOnlyList<TaskExecutionState>> GetRunningTasksAsync(
        string? nodeId = null,
        CancellationToken cancellationToken = default)
    {
        var runningTasks = _taskStates.Values
            .Where(t => t.Status == TaskExecutionStatus.Running ||
                       t.Status == TaskExecutionStatus.Initializing)
            .Where(t => string.IsNullOrEmpty(nodeId) || t.ExecutorNodeId == nodeId)
            .OrderBy(t => t.StartTime)
            .ToList();

        return Task.FromResult<IReadOnlyList<TaskExecutionState>>(runningTasks);
    }

    /// <inheritdoc />
    public Task<IReadOnlyList<TaskExecutionState>> GetCompletedTasksAsync(
        TimeSpan? timeRange = null,
        string? nodeId = null,
        CancellationToken cancellationToken = default)
    {
        var cutoffTime = timeRange.HasValue
            ? DateTime.UtcNow - timeRange.Value
            : DateTime.UtcNow.AddDays(-1);

        var completedTasks = _recentCompletedTasks.ToList()
            .Where(t => t.Status == TaskExecutionStatus.Completed)
            .Where(t => t.EndTime >= cutoffTime)
            .Where(t => string.IsNullOrEmpty(nodeId) || t.ExecutorNodeId == nodeId)
            .OrderByDescending(t => t.EndTime)
            .ToList();

        return Task.FromResult<IReadOnlyList<TaskExecutionState>>(completedTasks);
    }

    /// <inheritdoc />
    public Task<IReadOnlyList<TaskExecutionState>> GetFailedTasksAsync(
        TimeSpan? timeRange = null,
        string? nodeId = null,
        CancellationToken cancellationToken = default)
    {
        var cutoffTime = timeRange.HasValue
            ? DateTime.UtcNow - timeRange.Value
            : DateTime.UtcNow.AddDays(-1);

        var failedTasks = _recentCompletedTasks.ToList()
            .Where(t => t.Status == TaskExecutionStatus.Failed ||
                       t.Status == TaskExecutionStatus.TimedOut)
            .Where(t => t.EndTime >= cutoffTime)
            .Where(t => string.IsNullOrEmpty(nodeId) || t.ExecutorNodeId == nodeId)
            .OrderByDescending(t => t.EndTime)
            .ToList();

        return Task.FromResult<IReadOnlyList<TaskExecutionState>>(failedTasks);
    }

    /// <inheritdoc />
    public Task<TaskSearchResult> SearchTasksAsync(
        TaskSearchCriteria criteria,
        CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(criteria);

        var startTime = DateTime.UtcNow;

        try
        {
            // 合并活跃任务和已完成任务
            var allTasks = _taskStates.Values
                .Concat(_recentCompletedTasks.ToList())
                .Distinct()
                .ToList();

            // 应用过滤条件
            var filteredTasks = ApplySearchFilters(allTasks, criteria);

            // 计算总数
            var totalCount = filteredTasks.Count();

            // 应用排序
            var sortedTasks = ApplySorting(filteredTasks, criteria);

            // 应用分页
            var results = sortedTasks
                .Skip((criteria.PageNumber - 1) * criteria.PageSize)
                .Take(criteria.PageSize)
                .ToList();

            var searchTime = (long)(DateTime.UtcNow - startTime).TotalMilliseconds;

            return Task.FromResult(new TaskSearchResult
            {
                Criteria = criteria,
                TotalCount = totalCount,
                Results = results,
                SearchTimeMs = searchTime,
                SearchTime = startTime,
                Aggregations = ConvertAggregationsToDictionary(CalculateSearchAggregations(filteredTasks.ToList()))
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to search tasks");

            return Task.FromResult(new TaskSearchResult
            {
                Criteria = criteria,
                TotalCount = 0,
                Results = new List<TaskExecutionState>(),
                SearchTimeMs = (long)(DateTime.UtcNow - startTime).TotalMilliseconds
            });
        }
    }

    #endregion

    #region 辅助方法

    /// <summary>
    /// 定时器回调：清理过期数据
    /// </summary>
    private void CleanupExpiredDataCallback(object? state)
    {
        try
        {
            var retentionPeriod = TimeSpan.FromDays(_config.DataRetentionDays);
            _ = CleanupExpiredDataAsync(retentionPeriod);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in cleanup timer callback");
        }
    }

    /// <summary>
    /// 定时器回调：更新统计信息
    /// </summary>
    private void UpdateStatisticsCallback(object? state)
    {
        try
        {
            UpdateNodeStatistics();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in statistics update timer callback");
        }
    }

    /// <summary>
    /// 更新节点统计信息
    /// </summary>
    private void UpdateNodeStatistics()
    {
        var nodeGroups = _taskStates.Values.GroupBy(t => t.NodeId);

        foreach (var nodeGroup in nodeGroups)
        {
            var nodeId = nodeGroup.Key;
            var tasks = nodeGroup.ToList();

            var statistics = new NodeTaskStatistics
            {
                NodeId = nodeId,
                TotalTasks = tasks.Count,
                RunningTasks = tasks.Count(t => t.Status == TaskExecutionStatus.Running),
                CompletedTasks = tasks.Count(t => t.Status == TaskExecutionStatus.Completed),
                FailedTasks = tasks.Count(t => t.Status == TaskExecutionStatus.Failed),
                LastUpdateTime = DateTime.UtcNow
            };

            _nodeStatistics.AddOrUpdate(nodeId, statistics, (key, old) => statistics);
        }
    }

    /// <summary>
    /// 转换资源使用情况类型
    /// </summary>
    private FlowCustomV1.Core.Models.Executor.ResourceUsage? ConvertResourceUsage(
        FlowCustomV1.Core.Interfaces.Scheduling.ResourceUsage? schedulingResourceUsage)
    {
        if (schedulingResourceUsage == null)
            return null;

        return new FlowCustomV1.Core.Models.Executor.ResourceUsage
        {
            CpuCores = schedulingResourceUsage.PeakCpuUsage / 100.0, // 转换百分比为核心数
            MemoryMB = schedulingResourceUsage.PeakMemoryUsageMB,
            DiskMB = schedulingResourceUsage.TotalDiskIOMB,
            NetworkMbps = schedulingResourceUsage.TotalNetworkIOMB / 1024.0, // 转换MB为Mbps
            EstimatedDurationMs = 0, // 调度层的ResourceUsage没有这个信息
            Weight = 1.0
        };
    }

    /// <summary>
    /// 发布任务事件
    /// </summary>
    private async Task PublishTaskEventAsync(string eventType, TaskExecutionState taskState, CancellationToken cancellationToken = default)
    {
        try
        {
            var eventData = new
            {
                EventType = eventType,
                TaskId = taskState.TaskId,
                Status = taskState.Status.ToString(),
                NodeId = taskState.NodeId,
                Timestamp = DateTime.UtcNow
            };

            await _messageRouter.PublishAsync($"task.events.{eventType}", eventData, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to publish task event {EventType} for task {TaskId}", eventType, taskState.TaskId);
        }
    }

    /// <summary>
    /// 检查是否为终端状态
    /// </summary>
    private static bool IsTerminalStatus(TaskExecutionStatus status)
    {
        return status == TaskExecutionStatus.Completed ||
               status == TaskExecutionStatus.Failed ||
               status == TaskExecutionStatus.Cancelled;
    }

    /// <summary>
    /// 获取状态变更原因
    /// </summary>
    private static string GetStatusChangeReason(TaskExecutionStatus oldStatus, TaskExecutionStatus newStatus)
    {
        return $"Status changed from {oldStatus} to {newStatus}";
    }

    /// <summary>
    /// 触发任务状态变更事件
    /// </summary>
    private void OnTaskStatusChanged(TaskStatusChangedEventArgs args)
    {
        TaskStatusChanged?.Invoke(this, args);
    }

    /// <summary>
    /// 触发任务进度更新事件
    /// </summary>
    private void OnTaskProgressUpdated(TaskProgressUpdatedEventArgs args)
    {
        TaskProgressUpdated?.Invoke(this, args);
    }

    /// <summary>
    /// 触发任务执行完成事件
    /// </summary>
    private void OnTaskExecutionCompleted(TaskExecutionCompletedEventArgs args)
    {
        TaskExecutionCompleted?.Invoke(this, args);
    }

    /// <summary>
    /// 触发任务执行失败事件
    /// </summary>
    private void OnTaskExecutionFailed(TaskExecutionFailedEventArgs args)
    {
        TaskExecutionFailed?.Invoke(this, args);
    }

    /// <summary>
    /// 处理终端状态
    /// </summary>
    private async Task HandleTerminalStatusAsync(string taskId, TaskExecutionStatus status, CancellationToken cancellationToken)
    {
        if (_taskStates.TryGetValue(taskId, out var taskState))
        {
            taskState.EndTime = DateTime.UtcNow;

            // 添加到最近完成任务队列
            _recentCompletedTasks.Enqueue(taskState);

            // 限制队列大小
            while (_recentCompletedTasks.Count > 1000)
            {
                _recentCompletedTasks.TryDequeue(out _);
            }
        }
    }

    /// <summary>
    /// 转换聚合结果为字典
    /// </summary>
    private Dictionary<string, object> ConvertAggregationsToDictionary(TaskSearchAggregations aggregations)
    {
        return new Dictionary<string, object>
        {
            ["StatusCounts"] = aggregations.StatusCounts,
            ["NodeCounts"] = aggregations.NodeCounts,
            ["WorkflowCounts"] = aggregations.WorkflowCounts
        };
    }

    /// <summary>
    /// 转换跟踪摘要为字典
    /// </summary>
    private Dictionary<string, object> ConvertTrackingSummaryToDictionary(TaskTrackingSummary summary)
    {
        return new Dictionary<string, object>
        {
            ["TaskId"] = summary.TaskId,
            ["Status"] = summary.Status.ToString(),
            ["StartTime"] = summary.StartTime,
            ["EndTime"] = summary.EndTime ?? (object)DBNull.Value,
            ["TotalTrackingTime"] = summary.TotalTrackingTime,
            ["NodeId"] = summary.NodeId,
            ["Progress"] = summary.Progress ?? (object)DBNull.Value
        };
    }

    /// <summary>
    /// 创建跟踪摘要
    /// </summary>
    private TaskTrackingSummary CreateTrackingSummary(TaskExecutionState taskState)
    {
        return new TaskTrackingSummary
        {
            TaskId = taskState.TaskId,
            Status = taskState.Status,
            StartTime = taskState.StartTime ?? DateTime.UtcNow,
            EndTime = taskState.EndTime,
            TotalTrackingTime = taskState.EndTime.HasValue
                ? (long)(taskState.EndTime.Value - (taskState.StartTime ?? DateTime.UtcNow)).TotalMilliseconds
                : (long)(DateTime.UtcNow - (taskState.StartTime ?? DateTime.UtcNow)).TotalMilliseconds,
            NodeId = taskState.NodeId,
            Progress = taskState.Progress
        };
    }

    /// <summary>
    /// 应用搜索过滤器
    /// </summary>
    private IEnumerable<TaskExecutionState> ApplySearchFilters(IEnumerable<TaskExecutionState> tasks, TaskSearchCriteria criteria)
    {
        var filteredTasks = tasks;

        if (!string.IsNullOrEmpty(criteria.TaskId))
        {
            filteredTasks = filteredTasks.Where(t => t.TaskId.Contains(criteria.TaskId, StringComparison.OrdinalIgnoreCase));
        }

        if (!string.IsNullOrEmpty(criteria.WorkflowId))
        {
            filteredTasks = filteredTasks.Where(t => t.WorkflowId.Contains(criteria.WorkflowId, StringComparison.OrdinalIgnoreCase));
        }

        if (!string.IsNullOrEmpty(criteria.NodeId))
        {
            filteredTasks = filteredTasks.Where(t => t.NodeId.Equals(criteria.NodeId, StringComparison.OrdinalIgnoreCase));
        }

        if (criteria.Status.HasValue)
        {
            filteredTasks = filteredTasks.Where(t => t.Status == criteria.Status.Value);
        }

        if (criteria.StartTimeFrom.HasValue)
        {
            filteredTasks = filteredTasks.Where(t => t.StartTime >= criteria.StartTimeFrom.Value);
        }

        if (criteria.StartTimeTo.HasValue)
        {
            filteredTasks = filteredTasks.Where(t => t.StartTime <= criteria.StartTimeTo.Value);
        }

        return filteredTasks;
    }

    /// <summary>
    /// 应用排序
    /// </summary>
    private IEnumerable<TaskExecutionState> ApplySorting(IEnumerable<TaskExecutionState> tasks, TaskSearchCriteria criteria)
    {
        return criteria.SortBy?.ToLower() switch
        {
            "starttime" => criteria.SortDescending
                ? tasks.OrderByDescending(t => t.StartTime)
                : tasks.OrderBy(t => t.StartTime),
            "status" => criteria.SortDescending
                ? tasks.OrderByDescending(t => t.Status)
                : tasks.OrderBy(t => t.Status),
            "nodeid" => criteria.SortDescending
                ? tasks.OrderByDescending(t => t.NodeId)
                : tasks.OrderBy(t => t.NodeId),
            _ => tasks.OrderByDescending(t => t.StartTime) // 默认按开始时间降序
        };
    }

    /// <summary>
    /// 计算搜索聚合信息
    /// </summary>
    private TaskSearchAggregations CalculateSearchAggregations(IEnumerable<TaskExecutionState> tasks)
    {
        var taskList = tasks.ToList();

        return new TaskSearchAggregations
        {
            StatusCounts = taskList.GroupBy(t => t.Status)
                .ToDictionary(g => g.Key, g => g.Count()),
            NodeCounts = taskList.GroupBy(t => t.NodeId)
                .ToDictionary(g => g.Key, g => g.Count()),
            WorkflowCounts = taskList.GroupBy(t => t.WorkflowId)
                .ToDictionary(g => g.Key, g => g.Count())
        };
    }

    #endregion

    #region 实时监控

    /// <inheritdoc />
    public Task<TaskExecutionStatistics> GetExecutionStatisticsAsync(
        TimeSpan? timeRange = null,
        CancellationToken cancellationToken = default)
    {
        var cutoffTime = timeRange.HasValue ? DateTime.UtcNow - timeRange.Value : DateTime.MinValue;
        var relevantTasks = _taskStates.Values.Where(t => t.StartTime >= cutoffTime).ToList();

        var statistics = new TaskExecutionStatistics
        {
            TotalTasks = relevantTasks.Count,
            RunningTasks = relevantTasks.Count(t => t.Status == TaskExecutionStatus.Running),
            CompletedTasks = relevantTasks.Count(t => t.Status == TaskExecutionStatus.Completed),
            FailedTasks = relevantTasks.Count(t => t.Status == TaskExecutionStatus.Failed),
            PendingTasks = relevantTasks.Count(t => t.Status == TaskExecutionStatus.Pending),
            TimeRangeMs = (long)(timeRange?.TotalMilliseconds ?? 0),
            StatusDistribution = relevantTasks.GroupBy(t => t.Status)
                .ToDictionary(g => g.Key, g => g.Count()),
            NodeStatistics = _nodeStatistics.ToDictionary(kvp => kvp.Key, kvp => kvp.Value)
        };

        return Task.FromResult(statistics);
    }

    /// <inheritdoc />
    public Task<NodeTaskStatistics> GetNodeStatisticsAsync(
        string nodeId,
        TimeSpan? timeRange = null,
        CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrEmpty(nodeId);

        if (_nodeStatistics.TryGetValue(nodeId, out var statistics))
        {
            return Task.FromResult(statistics);
        }

        // 如果没有统计数据，创建一个空的
        var emptyStats = new NodeTaskStatistics
        {
            NodeId = nodeId,
            TotalTasks = 0,
            RunningTasks = 0,
            CompletedTasks = 0,
            FailedTasks = 0
        };

        return Task.FromResult(emptyStats);
    }

    /// <inheritdoc />
    public Task<RealTimeMonitoringData> GetRealTimeDataAsync(CancellationToken cancellationToken = default)
    {
        var monitoringData = new RealTimeMonitoringData
        {
            Timestamp = DateTime.UtcNow,
            ActiveTasks = _taskStates.Count(kvp => kvp.Value.Status == TaskExecutionStatus.Running),
            TotalTrackedTasks = _taskStates.Count,
            SystemLoad = new SystemLoadIndicators
            {
                CpuUsagePercent = 0, // 需要实际实现
                MemoryUsagePercent = 0, // 需要实际实现
                ActiveConnections = _taskStates.Count
            }
        };

        return Task.FromResult(monitoringData);
    }

    #endregion

    #region 任务生命周期管理

    /// <inheritdoc />
    public async Task<TaskOperationResult> PauseTaskAsync(
        string taskId,
        string reason = "",
        CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrEmpty(taskId);

        try
        {
            if (!_taskStates.TryGetValue(taskId, out var taskState))
            {
                return new TaskOperationResult
                {
                    IsSuccess = false,
                    TaskId = taskId,
                    ErrorMessage = "Task not found"
                };
            }

            if (taskState.Status != TaskExecutionStatus.Running)
            {
                return new TaskOperationResult
                {
                    IsSuccess = false,
                    TaskId = taskId,
                    ErrorMessage = $"Task is not running (current status: {taskState.Status})"
                };
            }

            // 更新状态为暂停
            await UpdateTaskStatusAsync(taskId, TaskExecutionStatus.Paused, cancellationToken: cancellationToken);

            _logger.LogInformation("Paused task {TaskId}. Reason: {Reason}", taskId, reason);

            return new TaskOperationResult
            {
                IsSuccess = true,
                TaskId = taskId,
                OperationType = "Pause",
                ExecutedAt = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to pause task {TaskId}", taskId);
            return new TaskOperationResult
            {
                IsSuccess = false,
                TaskId = taskId,
                ErrorMessage = ex.Message
            };
        }
    }

    /// <inheritdoc />
    public async Task<TaskOperationResult> ResumeTaskAsync(
        string taskId,
        CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrEmpty(taskId);

        try
        {
            if (!_taskStates.TryGetValue(taskId, out var taskState))
            {
                return new TaskOperationResult
                {
                    IsSuccess = false,
                    TaskId = taskId,
                    ErrorMessage = "Task not found"
                };
            }

            if (taskState.Status != TaskExecutionStatus.Paused)
            {
                return new TaskOperationResult
                {
                    IsSuccess = false,
                    TaskId = taskId,
                    ErrorMessage = $"Task is not paused (current status: {taskState.Status})"
                };
            }

            // 更新状态为运行中
            await UpdateTaskStatusAsync(taskId, TaskExecutionStatus.Running, cancellationToken: cancellationToken);

            _logger.LogInformation("Resumed task {TaskId}", taskId);

            return new TaskOperationResult
            {
                IsSuccess = true,
                TaskId = taskId,
                OperationType = "Resume",
                ExecutedAt = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to resume task {TaskId}", taskId);
            return new TaskOperationResult
            {
                IsSuccess = false,
                TaskId = taskId,
                ErrorMessage = ex.Message
            };
        }
    }

    /// <inheritdoc />
    public async Task<TaskOperationResult> CancelTaskAsync(
        string taskId,
        string reason = "",
        CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrEmpty(taskId);

        try
        {
            if (!_taskStates.TryGetValue(taskId, out var taskState))
            {
                return new TaskOperationResult
                {
                    IsSuccess = false,
                    TaskId = taskId,
                    ErrorMessage = "Task not found"
                };
            }

            if (taskState.Status == TaskExecutionStatus.Completed ||
                taskState.Status == TaskExecutionStatus.Cancelled ||
                taskState.Status == TaskExecutionStatus.Failed)
            {
                return new TaskOperationResult
                {
                    IsSuccess = false,
                    TaskId = taskId,
                    ErrorMessage = $"Task cannot be cancelled (current status: {taskState.Status})"
                };
            }

            // 更新状态为已取消
            await UpdateTaskStatusAsync(taskId, TaskExecutionStatus.Cancelled, cancellationToken: cancellationToken);

            _logger.LogInformation("Cancelled task {TaskId}. Reason: {Reason}", taskId, reason);

            return new TaskOperationResult
            {
                IsSuccess = true,
                TaskId = taskId,
                OperationType = "Cancel",
                ExecutedAt = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to cancel task {TaskId}", taskId);
            return new TaskOperationResult
            {
                IsSuccess = false,
                TaskId = taskId,
                ErrorMessage = ex.Message
            };
        }
    }

    /// <inheritdoc />
    public async Task<TaskOperationResult> RestartTaskAsync(
        string taskId,
        CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrEmpty(taskId);

        try
        {
            if (!_taskStates.TryGetValue(taskId, out var taskState))
            {
                return new TaskOperationResult
                {
                    IsSuccess = false,
                    TaskId = taskId,
                    ErrorMessage = "Task not found"
                };
            }

            // 重置任务状态
            taskState.Status = TaskExecutionStatus.Pending;
            taskState.StartTime = DateTime.UtcNow;
            taskState.Progress = new TaskProgress { PercentComplete = 0 };
            taskState.ErrorMessage = null;

            await UpdateTaskStatusAsync(taskId, TaskExecutionStatus.Pending, taskState.Progress, cancellationToken);

            _logger.LogInformation("Restarted task {TaskId}", taskId);

            return new TaskOperationResult
            {
                IsSuccess = true,
                TaskId = taskId,
                OperationType = "Restart",
                ExecutedAt = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to restart task {TaskId}", taskId);
            return new TaskOperationResult
            {
                IsSuccess = false,
                TaskId = taskId,
                ErrorMessage = ex.Message
            };
        }
    }

    #endregion

    #region 健康检查

    /// <inheritdoc />
    public Task<HealthCheckResult> CheckHealthAsync(CancellationToken cancellationToken = default)
    {
        var healthResult = new HealthCheckResult
        {
            CheckId = Guid.NewGuid().ToString(),
            CheckedAt = DateTime.UtcNow,
            IsHealthy = true,
            Status = HealthStatus.Healthy
        };

        try
        {
            // 检查基本功能
            var activeTasksCount = _taskStates.Count(kvp => kvp.Value.Status == TaskExecutionStatus.Running);
            var totalTasksCount = _taskStates.Count;

            healthResult.CheckResults["ActiveTasks"] = new HealthCheckItemResult
            {
                ItemName = "ActiveTasks",
                IsPassed = true,
                Status = HealthStatus.Healthy,
                Message = $"Active tasks: {activeTasksCount}, Total tracked: {totalTasksCount}"
            };

            // 检查内存使用
            var memoryUsage = GC.GetTotalMemory(false);
            healthResult.CheckResults["MemoryUsage"] = new HealthCheckItemResult
            {
                ItemName = "MemoryUsage",
                IsPassed = memoryUsage < 100 * 1024 * 1024, // 100MB threshold
                Status = memoryUsage < 100 * 1024 * 1024 ? HealthStatus.Healthy : HealthStatus.Warning,
                Message = $"Memory usage: {memoryUsage / 1024 / 1024} MB"
            };

            healthResult.OverallScore = healthResult.CheckResults.Values.All(r => r.IsPassed) ? 100 : 75;
        }
        catch (Exception ex)
        {
            healthResult.IsHealthy = false;
            healthResult.Status = HealthStatus.Unhealthy;
            healthResult.Errors.Add($"Health check failed: {ex.Message}");
        }

        return Task.FromResult(healthResult);
    }

    /// <inheritdoc />
    public Task<CleanupResult> CleanupExpiredDataAsync(
        TimeSpan retentionPeriod,
        CancellationToken cancellationToken = default)
    {
        var cleanupResult = new CleanupResult
        {
            CleanupId = Guid.NewGuid().ToString(),
            CleanedAt = DateTime.UtcNow,
            IsSuccess = true
        };

        try
        {
            var cutoffTime = DateTime.UtcNow - retentionPeriod;
            var expiredTasks = _taskStates.Where(kvp =>
                kvp.Value.EndTime.HasValue && kvp.Value.EndTime.Value < cutoffTime).ToList();

            foreach (var expiredTask in expiredTasks)
            {
                _taskStates.TryRemove(expiredTask.Key, out _);
                _trackingResults.TryRemove(expiredTask.Key, out _);
            }

            cleanupResult.CleanedItemsCount = expiredTasks.Count;
            cleanupResult.CleanupTimeMs = 0; // 简化实现

            _logger.LogInformation("Cleaned up {Count} expired tasks older than {RetentionPeriod}",
                expiredTasks.Count, retentionPeriod);
        }
        catch (Exception ex)
        {
            cleanupResult.IsSuccess = false;
            cleanupResult.ErrorMessage = ex.Message;
            _logger.LogError(ex, "Failed to cleanup expired data");
        }

        return Task.FromResult(cleanupResult);
    }

    #endregion

    #region IDisposable

    public void Dispose()
    {
        _cleanupTimer?.Dispose();
        _statisticsTimer?.Dispose();
        GC.SuppressFinalize(this);
    }

    #endregion
}
