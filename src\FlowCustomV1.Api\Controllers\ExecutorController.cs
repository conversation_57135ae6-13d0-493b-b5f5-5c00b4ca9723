using FlowCustomV1.Api.Models.Requests;
using FlowCustomV1.Api.Models.Responses;
using FlowCustomV1.Core.Interfaces.Executor;
using FlowCustomV1.Core.Models.Common;
using FlowCustomV1.Core.Models.Executor;
using FlowCustomV1.Core.Models.Workflow;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel.DataAnnotations;

namespace FlowCustomV1.Api.Controllers;

/// <summary>
/// 工作流执行器控制器
/// 提供工作流执行相关的REST API接口
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public class ExecutorController : ControllerBase
{
    private readonly IWorkflowExecutorService _executorService;
    private readonly ILogger<ExecutorController> _logger;

    public ExecutorController(
        IWorkflowExecutorService executorService,
        ILogger<ExecutorController> logger)
    {
        _executorService = executorService ?? throw new ArgumentNullException(nameof(executorService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 执行工作流
    /// </summary>
    /// <param name="request">执行请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>执行响应</returns>
    [HttpPost("execute")]
    [ProducesResponseType(typeof(ApiResponse<ExecutionResponse>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<ApiResponse<ExecutionResponse>>> ExecuteWorkflowAsync(
        [FromBody] ExecuteWorkflowRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Executing workflow {WorkflowId}", request.WorkflowId);

            var executionId = await _executorService.ExecuteWorkflowAsync(
                request.WorkflowId, 
                request.InputData, 
                cancellationToken);

            var response = new ExecutionResponse
            {
                ExecutionId = executionId,
                WorkflowId = request.WorkflowId,
                Status = "Started",
                StartedAt = DateTime.UtcNow
            };

            return Ok(ApiResponse<ExecutionResponse>.CreateSuccess(response, "Workflow execution started successfully"));
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid request for workflow execution");
            return BadRequest(ApiResponse.Error(ex.Message, "INVALID_REQUEST"));
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning(ex, "Cannot execute workflow {WorkflowId}", request.WorkflowId);
            return BadRequest(ApiResponse.Error(ex.Message, "EXECUTION_NOT_ALLOWED"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to execute workflow {WorkflowId}", request.WorkflowId);
            return StatusCode(500, ApiResponse.Error("Internal server error occurred", "INTERNAL_ERROR"));
        }
    }

    /// <summary>
    /// 获取执行结果
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>执行结果</returns>
    [HttpGet("executions/{executionId}/result")]
    [ProducesResponseType(typeof(ApiResponse<WorkflowExecutionResult>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<ApiResponse<WorkflowExecutionResult>>> GetExecutionResultAsync(
        [FromRoute] string executionId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting execution result for {ExecutionId}", executionId);

            var result = await _executorService.GetExecutionResultAsync(executionId, cancellationToken);
            if (result == null)
            {
                return NotFound(ApiResponse.Error($"Execution {executionId} not found", "EXECUTION_NOT_FOUND"));
            }

            return Ok(ApiResponse<WorkflowExecutionResult>.CreateSuccess(result, "Execution result retrieved successfully"));
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid execution ID: {ExecutionId}", executionId);
            return BadRequest(ApiResponse.Error(ex.Message, "INVALID_EXECUTION_ID"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get execution result for {ExecutionId}", executionId);
            return StatusCode(500, ApiResponse.Error("Internal server error occurred", "INTERNAL_ERROR"));
        }
    }

    /// <summary>
    /// 取消工作流执行
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>取消结果</returns>
    [HttpPost("executions/{executionId}/cancel")]
    [ProducesResponseType(typeof(ApiResponse<bool>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<ApiResponse<bool>>> CancelExecutionAsync(
        [FromRoute] string executionId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Cancelling execution {ExecutionId}", executionId);

            var success = await _executorService.CancelExecutionAsync(executionId, cancellationToken);
            if (!success)
            {
                return NotFound(ApiResponse.Error($"Execution {executionId} not found or cannot be cancelled", "EXECUTION_NOT_FOUND"));
            }

            return Ok(ApiResponse<bool>.CreateSuccess(true, "Execution cancelled successfully"));
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid execution ID: {ExecutionId}", executionId);
            return BadRequest(ApiResponse.Error(ex.Message, "INVALID_EXECUTION_ID"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to cancel execution {ExecutionId}", executionId);
            return StatusCode(500, ApiResponse.Error("Internal server error occurred", "INTERNAL_ERROR"));
        }
    }

    /// <summary>
    /// 暂停工作流执行
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>暂停结果</returns>
    [HttpPost("executions/{executionId}/pause")]
    [ProducesResponseType(typeof(ApiResponse<bool>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<ApiResponse<bool>>> PauseExecutionAsync(
        [FromRoute] string executionId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Pausing execution {ExecutionId}", executionId);

            var success = await _executorService.PauseExecutionAsync(executionId, cancellationToken);
            if (!success)
            {
                return NotFound(ApiResponse.Error($"Execution {executionId} not found or cannot be paused", "EXECUTION_NOT_FOUND"));
            }

            return Ok(ApiResponse<bool>.CreateSuccess(true, "Execution paused successfully"));
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid execution ID: {ExecutionId}", executionId);
            return BadRequest(ApiResponse.Error(ex.Message, "INVALID_EXECUTION_ID"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to pause execution {ExecutionId}", executionId);
            return StatusCode(500, ApiResponse.Error("Internal server error occurred", "INTERNAL_ERROR"));
        }
    }

    /// <summary>
    /// 恢复工作流执行
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>恢复结果</returns>
    [HttpPost("executions/{executionId}/resume")]
    [ProducesResponseType(typeof(ApiResponse<bool>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<ApiResponse<bool>>> ResumeExecutionAsync(
        [FromRoute] string executionId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Resuming execution {ExecutionId}", executionId);

            var success = await _executorService.ResumeExecutionAsync(executionId, cancellationToken);
            if (!success)
            {
                return NotFound(ApiResponse.Error($"Execution {executionId} not found or cannot be resumed", "EXECUTION_NOT_FOUND"));
            }

            return Ok(ApiResponse<bool>.CreateSuccess(true, "Execution resumed successfully"));
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid execution ID: {ExecutionId}", executionId);
            return BadRequest(ApiResponse.Error(ex.Message, "INVALID_EXECUTION_ID"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to resume execution {ExecutionId}", executionId);
            return StatusCode(500, ApiResponse.Error("Internal server error occurred", "INTERNAL_ERROR"));
        }
    }

    /// <summary>
    /// 获取正在运行的执行列表
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>正在运行的执行列表</returns>
    [HttpGet("executions/running")]
    [ProducesResponseType(typeof(ApiResponse<IReadOnlyList<ExecutionInfo>>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<ApiResponse<IReadOnlyList<ExecutionInfo>>>> GetRunningExecutionsAsync(
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting running executions");

            var executions = await _executorService.GetRunningExecutionsAsync(cancellationToken);
            return Ok(ApiResponse<IReadOnlyList<ExecutionInfo>>.CreateSuccess(executions, "Running executions retrieved successfully"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get running executions");
            return StatusCode(500, ApiResponse.Error("Internal server error occurred", "INTERNAL_ERROR"));
        }
    }

    /// <summary>
    /// 获取工作流执行历史
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="pageIndex">页索引</param>
    /// <param name="pageSize">页大小</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>执行历史</returns>
    [HttpGet("workflows/{workflowId}/executions")]
    [ProducesResponseType(typeof(PagedApiResponse<ExecutionInfo>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<PagedApiResponse<ExecutionInfo>>> GetExecutionHistoryAsync(
        [FromRoute] string workflowId,
        [FromQuery] int pageIndex = 0,
        [FromQuery] int pageSize = 20,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting execution history for workflow {WorkflowId}", workflowId);

            // 验证分页参数
            if (pageIndex < 0) pageIndex = 0;
            if (pageSize <= 0 || pageSize > 100) pageSize = 20;

            var pagedResult = await _executorService.GetExecutionHistoryAsync(workflowId, pageIndex, pageSize, cancellationToken);
            
            var pagination = new PaginationInfo
            {
                CurrentPage = pageIndex + 1, // API使用1基索引
                PageSize = pageSize,
                TotalCount = pagedResult.TotalCount
            };

            return Ok(PagedApiResponse<ExecutionInfo>.CreateSuccess(pagedResult.Items, pagination, "Execution history retrieved successfully"));
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid workflow ID: {WorkflowId}", workflowId);
            return BadRequest(ApiResponse.Error(ex.Message, "INVALID_WORKFLOW_ID"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get execution history for workflow {WorkflowId}", workflowId);
            return StatusCode(500, ApiResponse.Error("Internal server error occurred", "INTERNAL_ERROR"));
        }
    }

    /// <summary>
    /// 获取执行统计信息
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>执行统计</returns>
    [HttpGet("statistics")]
    [ProducesResponseType(typeof(ApiResponse<ExecutionStatistics>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<ApiResponse<ExecutionStatistics>>> GetExecutionStatisticsAsync(
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting execution statistics");

            var statistics = await _executorService.GetExecutionStatisticsAsync(cancellationToken);
            return Ok(ApiResponse<ExecutionStatistics>.CreateSuccess(statistics, "Execution statistics retrieved successfully"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get execution statistics");
            return StatusCode(500, ApiResponse.Error("Internal server error occurred", "INTERNAL_ERROR"));
        }
    }

    /// <summary>
    /// 获取执行容量信息
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>执行容量</returns>
    [HttpGet("capacity")]
    [ProducesResponseType(typeof(ApiResponse<ExecutionCapacity>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<ApiResponse<ExecutionCapacity>>> GetExecutionCapacityAsync(
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting execution capacity");

            var capacity = await _executorService.GetExecutionCapacityAsync(cancellationToken);
            return Ok(ApiResponse<ExecutionCapacity>.CreateSuccess(capacity, "Execution capacity retrieved successfully"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get execution capacity");
            return StatusCode(500, ApiResponse.Error("Internal server error occurred", "INTERNAL_ERROR"));
        }
    }

    /// <summary>
    /// 获取服务状态
    /// </summary>
    /// <returns>服务状态</returns>
    [HttpGet("status")]
    [ProducesResponseType(typeof(ApiResponse<ExecutorServiceStatus>), StatusCodes.Status200OK)]
    public ActionResult<ApiResponse<ExecutorServiceStatus>> GetServiceStatus()
    {
        try
        {
            var status = _executorService.GetStatus();
            return Ok(ApiResponse<ExecutorServiceStatus>.CreateSuccess(status, "Service status retrieved successfully"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get service status");
            return StatusCode(500, ApiResponse.Error("Internal server error occurred", "INTERNAL_ERROR"));
        }
    }
}
