using FlowCustomV1.Core.Models.Workflow;

namespace FlowCustomV1.Core.Interfaces;

/// <summary>
/// 节点执行器接口
/// 定义工作流节点的执行、验证和生命周期管理功能
/// </summary>
public interface INodeExecutor
{
    /// <summary>
    /// 节点类型标识符
    /// </summary>
    string NodeType { get; }

    /// <summary>
    /// 节点显示名称
    /// </summary>
    string DisplayName { get; }

    /// <summary>
    /// 节点描述
    /// </summary>
    string Description { get; }

    /// <summary>
    /// 节点版本
    /// </summary>
    string Version { get; }

    /// <summary>
    /// 是否支持异步执行
    /// </summary>
    bool SupportsAsync { get; }

    /// <summary>
    /// 是否有状态节点
    /// </summary>
    bool IsStateful { get; }

    /// <summary>
    /// 初始化节点执行器
    /// </summary>
    /// <param name="configuration">节点配置</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>初始化任务</returns>
    Task InitializeAsync(
        NodeConfiguration configuration,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证节点配置
    /// </summary>
    /// <param name="configuration">节点配置</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    Task<NodeValidationResult> ValidateAsync(
        NodeConfiguration configuration,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 执行节点逻辑
    /// </summary>
    /// <param name="context">执行上下文</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>执行结果</returns>
    Task<NodeExecutionResult> ExecuteAsync(
        NodeExecutionContext context,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 清理节点资源
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>清理任务</returns>
    Task CleanupAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取节点定义
    /// </summary>
    /// <returns>节点定义</returns>
    NodeDefinition GetNodeDefinition();

    /// <summary>
    /// 获取节点的输入参数定义
    /// </summary>
    /// <returns>输入参数定义列表</returns>
    IEnumerable<NodeParameterDefinition> GetInputParameters();

    /// <summary>
    /// 获取节点的输出参数定义
    /// </summary>
    /// <returns>输出参数定义列表</returns>
    IEnumerable<NodeParameterDefinition> GetOutputParameters();

    /// <summary>
    /// 获取节点的配置参数定义
    /// </summary>
    /// <returns>配置参数定义列表</returns>
    IEnumerable<NodeParameterDefinition> GetConfigurationParameters();

    /// <summary>
    /// 检查节点是否可以处理指定的输入数据
    /// </summary>
    /// <param name="inputData">输入数据</param>
    /// <returns>是否可以处理</returns>
    bool CanHandle(Dictionary<string, object> inputData);

    /// <summary>
    /// 估算节点执行时间
    /// </summary>
    /// <param name="inputData">输入数据</param>
    /// <param name="configuration">节点配置</param>
    /// <returns>预估执行时间</returns>
    TimeSpan EstimateExecutionTime(
        Dictionary<string, object> inputData,
        NodeConfiguration configuration);

    /// <summary>
    /// 获取节点的资源需求
    /// </summary>
    /// <param name="inputData">输入数据</param>
    /// <param name="configuration">节点配置</param>
    /// <returns>资源需求</returns>
    NodeResourceRequirements GetResourceRequirements(
        Dictionary<string, object> inputData,
        NodeConfiguration configuration);

    /// <summary>
    /// 节点执行进度事件
    /// </summary>
    event EventHandler<NodeExecutionProgressEventArgs>? ExecutionProgress;

    /// <summary>
    /// 节点状态变更事件
    /// </summary>
    event EventHandler<NodeExecutionStatusChangedEventArgs>? StatusChanged;
}

/// <summary>
/// 节点执行进度事件参数
/// </summary>
public class NodeExecutionProgressEventArgs : EventArgs
{
    /// <summary>
    /// 节点ID
    /// </summary>
    public string NodeId { get; set; } = string.Empty;

    /// <summary>
    /// 执行ID
    /// </summary>
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 进度百分比 (0-100)
    /// </summary>
    public int ProgressPercentage { get; set; }

    /// <summary>
    /// 进度消息
    /// </summary>
    public string? Message { get; set; }

    /// <summary>
    /// 当前步骤
    /// </summary>
    public string? CurrentStep { get; set; }

    /// <summary>
    /// 总步骤数
    /// </summary>
    public int? TotalSteps { get; set; }

    /// <summary>
    /// 当前步骤索引
    /// </summary>
    public int? CurrentStepIndex { get; set; }

    /// <summary>
    /// 进度时间
    /// </summary>
    public DateTime ProgressAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 进度元数据
    /// </summary>
    public Dictionary<string, object> ProgressMetadata { get; set; } = new();
}

/// <summary>
/// 节点执行状态变更事件参数
/// </summary>
public class NodeExecutionStatusChangedEventArgs : EventArgs
{
    /// <summary>
    /// 节点ID
    /// </summary>
    public string NodeId { get; set; } = string.Empty;

    /// <summary>
    /// 执行ID
    /// </summary>
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 旧状态
    /// </summary>
    public NodeExecutionState OldState { get; set; }

    /// <summary>
    /// 新状态
    /// </summary>
    public NodeExecutionState NewState { get; set; }

    /// <summary>
    /// 状态变更时间
    /// </summary>
    public DateTime ChangedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 变更原因
    /// </summary>
    public string? Reason { get; set; }

    /// <summary>
    /// 状态元数据
    /// </summary>
    public Dictionary<string, object> StatusMetadata { get; set; } = new();
}
