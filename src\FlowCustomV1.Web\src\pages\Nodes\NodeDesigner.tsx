import React from 'react';
import { Row, Col, Statistic, Button, Space, Tag, Alert } from 'antd';
import { 
  DesktopOutlined, 
  PlayCircleOutlined, 
  TeamOutlined,
  SettingOutlined,
  MonitorOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';

const NodeDesigner: React.FC = () => {
  return (
    <div className="page-container">
      <div className="page-header">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="page-title">
              <DesktopOutlined className="mr-2" />
              Designer 设计器节点
            </h1>
            <p className="page-description">工作流设计和协作服务管理</p>
          </div>
          <Space>
            <Button icon={<SettingOutlined />}>
              节点配置
            </Button>
            <Button type="primary" icon={<ReloadOutlined />}>
              刷新状态
            </Button>
          </Space>
        </div>

        <Alert
          message="Designer 节点服务"
          description="负责工作流的可视化设计、模板管理、版本控制和多用户协作功能"
          type="info"
          showIcon
          className="mb-6"
        />
      </div>

      {/* 服务状态统计 */}
      <Row gutter={[16, 16]} className="mb-6">
        <Col xs={24} sm={12} lg={6}>
          <ProCard>
            <Statistic
              title="设计会话"
              value={3}
              prefix={<PlayCircleOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </ProCard>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <ProCard>
            <Statistic
              title="协作用户"
              value={8}
              prefix={<TeamOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </ProCard>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <ProCard>
            <Statistic
              title="模板数量"
              value={15}
              prefix={<DesktopOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </ProCard>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <ProCard>
            <Statistic
              title="版本管理"
              value={42}
              prefix={<MonitorOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </ProCard>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        {/* 服务功能 */}
        <Col xs={24} lg={12}>
          <ProCard title="核心功能">
            <div className="space-y-4">
              <div className="flex justify-between items-center p-3 border rounded">
                <div>
                  <div className="font-medium">工作流设计服务</div>
                  <div className="text-sm text-gray-500">可视化工作流设计和编辑</div>
                </div>
                <Tag color="green">运行中</Tag>
              </div>
              
              <div className="flex justify-between items-center p-3 border rounded">
                <div>
                  <div className="font-medium">模板管理服务</div>
                  <div className="text-sm text-gray-500">工作流模板创建和管理</div>
                </div>
                <Tag color="green">运行中</Tag>
              </div>
              
              <div className="flex justify-between items-center p-3 border rounded">
                <div>
                  <div className="font-medium">协作服务</div>
                  <div className="text-sm text-gray-500">多用户实时协作编辑</div>
                </div>
                <Tag color="green">运行中</Tag>
              </div>
              
              <div className="flex justify-between items-center p-3 border rounded">
                <div>
                  <div className="font-medium">版本控制服务</div>
                  <div className="text-sm text-gray-500">工作流版本管理和历史记录</div>
                </div>
                <Tag color="green">运行中</Tag>
              </div>
            </div>
          </ProCard>
        </Col>

        {/* 活动会话 */}
        <Col xs={24} lg={12}>
          <ProCard title="活动设计会话">
            <div className="space-y-3">
              <div className="border rounded p-3">
                <div className="flex justify-between items-start mb-2">
                  <div className="font-medium">数据处理流水线</div>
                  <Tag color="blue">设计中</Tag>
                </div>
                <div className="text-sm text-gray-500 mb-2">
                  参与者: 张三, 李四
                </div>
                <div className="text-xs text-gray-400">
                  开始时间: 2025-01-13 14:30
                </div>
              </div>
              
              <div className="border rounded p-3">
                <div className="flex justify-between items-start mb-2">
                  <div className="font-medium">API数据同步</div>
                  <Tag color="orange">协作中</Tag>
                </div>
                <div className="text-sm text-gray-500 mb-2">
                  参与者: 王五, 赵六, 孙七
                </div>
                <div className="text-xs text-gray-400">
                  开始时间: 2025-01-13 13:15
                </div>
              </div>
              
              <div className="border rounded p-3">
                <div className="flex justify-between items-start mb-2">
                  <div className="font-medium">文件批处理</div>
                  <Tag color="green">已保存</Tag>
                </div>
                <div className="text-sm text-gray-500 mb-2">
                  参与者: 周八
                </div>
                <div className="text-xs text-gray-400">
                  完成时间: 2025-01-13 12:45
                </div>
              </div>
            </div>
          </ProCard>
        </Col>
      </Row>

      {/* API 端点 */}
      <Row className="mt-6">
        <Col span={24}>
          <ProCard title="API 端点">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="border rounded p-3">
                <div className="font-medium text-blue-600">POST /api/designer/workflows</div>
                <div className="text-sm text-gray-500">创建新工作流</div>
              </div>
              
              <div className="border rounded p-3">
                <div className="font-medium text-green-600">GET /api/designer/workflows/&#123;id&#125;</div>
                <div className="text-sm text-gray-500">获取工作流详情</div>
              </div>
              
              <div className="border rounded p-3">
                <div className="font-medium text-orange-600">PUT /api/designer/workflows/&#123;id&#125;</div>
                <div className="text-sm text-gray-500">更新工作流</div>
              </div>
              
              <div className="border rounded p-3">
                <div className="font-medium text-purple-600">POST /api/designer/collaboration/sessions</div>
                <div className="text-sm text-gray-500">创建协作会话</div>
              </div>
              
              <div className="border rounded p-3">
                <div className="font-medium text-cyan-600">GET /api/designer/templates</div>
                <div className="text-sm text-gray-500">获取模板列表</div>
              </div>
              
              <div className="border rounded p-3">
                <div className="font-medium text-pink-600">POST /api/designer/templates</div>
                <div className="text-sm text-gray-500">创建新模板</div>
              </div>
            </div>
          </ProCard>
        </Col>
      </Row>
    </div>
  );
};

export default NodeDesigner;
