using System.ComponentModel.DataAnnotations;

namespace FlowCustomV1.Core.Models.Scheduling;

/// <summary>
/// 任务执行统计信息
/// </summary>
public class TaskExecutionStatistics
{
    /// <summary>
    /// 统计开始时间
    /// </summary>
    public DateTime StartTime { get; set; }

    /// <summary>
    /// 统计结束时间
    /// </summary>
    public DateTime EndTime { get; set; }

    /// <summary>
    /// 统计时间范围（毫秒）
    /// </summary>
    public long TimeRangeMs { get; set; }

    /// <summary>
    /// 计算的时间范围（毫秒）
    /// </summary>
    public long CalculatedTimeRangeMs => (long)(EndTime - StartTime).TotalMilliseconds;

    /// <summary>
    /// 总任务数
    /// </summary>
    public int TotalTasks { get; set; }

    /// <summary>
    /// 正在运行的任务数
    /// </summary>
    public int RunningTasks { get; set; }

    /// <summary>
    /// 已完成的任务数
    /// </summary>
    public int CompletedTasks { get; set; }

    /// <summary>
    /// 失败的任务数
    /// </summary>
    public int FailedTasks { get; set; }

    /// <summary>
    /// 已取消的任务数
    /// </summary>
    public int CancelledTasks { get; set; }

    /// <summary>
    /// 等待中的任务数
    /// </summary>
    public int PendingTasks { get; set; }

    /// <summary>
    /// 队列中的任务数
    /// </summary>
    public int QueuedTasks { get; set; }

    /// <summary>
    /// 成功率（0-1）
    /// </summary>
    public double SuccessRate => TotalTasks > 0 ? (double)CompletedTasks / TotalTasks : 0;

    /// <summary>
    /// 失败率（0-1）
    /// </summary>
    public double FailureRate => TotalTasks > 0 ? (double)FailedTasks / TotalTasks : 0;

    /// <summary>
    /// 平均执行时间（毫秒）
    /// </summary>
    public double AverageExecutionTimeMs { get; set; }

    /// <summary>
    /// 最大执行时间（毫秒）
    /// </summary>
    public long MaxExecutionTimeMs { get; set; }

    /// <summary>
    /// 最小执行时间（毫秒）
    /// </summary>
    public long MinExecutionTimeMs { get; set; }

    /// <summary>
    /// 吞吐量（任务/秒）
    /// </summary>
    public double ThroughputPerSecond => TimeRangeMs > 0 ? CompletedTasks / (TimeRangeMs / 1000.0) : 0;

    /// <summary>
    /// 按节点统计
    /// </summary>
    public Dictionary<string, NodeTaskStatistics> NodeStatistics { get; set; } = new();

    /// <summary>
    /// 按状态分布
    /// </summary>
    public Dictionary<TaskExecutionStatus, int> StatusDistribution { get; set; } = new();

    /// <summary>
    /// 按任务类型统计
    /// </summary>
    public Dictionary<string, TaskTypeStatistics> TaskTypeStatistics { get; set; } = new();

    /// <summary>
    /// 按优先级统计
    /// </summary>
    public Dictionary<int, PriorityStatistics> PriorityStatistics { get; set; } = new();

    /// <summary>
    /// 时间序列数据
    /// </summary>
    public List<TimeSeriesDataPoint> TimeSeriesData { get; set; } = new();
}

/// <summary>
/// 节点任务统计信息
/// </summary>
public class NodeTaskStatistics
{
    /// <summary>
    /// 节点ID
    /// </summary>
    public string NodeId { get; set; } = string.Empty;

    /// <summary>
    /// 节点名称
    /// </summary>
    public string NodeName { get; set; } = string.Empty;

    /// <summary>
    /// 总任务数
    /// </summary>
    public int TotalTasks { get; set; }

    /// <summary>
    /// 正在运行的任务数
    /// </summary>
    public int RunningTasks { get; set; }

    /// <summary>
    /// 已完成的任务数
    /// </summary>
    public int CompletedTasks { get; set; }

    /// <summary>
    /// 失败的任务数
    /// </summary>
    public int FailedTasks { get; set; }

    /// <summary>
    /// 已取消的任务数
    /// </summary>
    public int CancelledTasks { get; set; }

    /// <summary>
    /// 等待中的任务数
    /// </summary>
    public int PendingTasks { get; set; }

    /// <summary>
    /// 队列中的任务数
    /// </summary>
    public int QueuedTasks { get; set; }

    /// <summary>
    /// 成功率（0-1）
    /// </summary>
    public double SuccessRate { get; set; }

    /// <summary>
    /// 平均执行时间（毫秒）
    /// </summary>
    public double AverageExecutionTimeMs { get; set; }

    /// <summary>
    /// 最大执行时间（毫秒）
    /// </summary>
    public long MaxExecutionTimeMs { get; set; }

    /// <summary>
    /// 最小执行时间（毫秒）
    /// </summary>
    public long MinExecutionTimeMs { get; set; }

    /// <summary>
    /// 吞吐量（任务/秒）
    /// </summary>
    public double ThroughputPerSecond { get; set; }

    /// <summary>
    /// 统计开始时间
    /// </summary>
    public DateTime StatisticsStartTime { get; set; }

    /// <summary>
    /// 统计结束时间
    /// </summary>
    public DateTime StatisticsEndTime { get; set; }

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 最后更新时间（LastUpdated的别名，保持向后兼容）
    /// </summary>
    public DateTime LastUpdateTime
    {
        get => LastUpdated;
        set => LastUpdated = value;
    }
}

/// <summary>
/// 任务类型统计信息
/// </summary>
public class TaskTypeStatistics
{
    /// <summary>
    /// 任务类型
    /// </summary>
    public string TaskType { get; set; } = string.Empty;

    /// <summary>
    /// 任务数量
    /// </summary>
    public int TaskCount { get; set; }

    /// <summary>
    /// 成功数量
    /// </summary>
    public int SuccessCount { get; set; }

    /// <summary>
    /// 失败数量
    /// </summary>
    public int FailureCount { get; set; }

    /// <summary>
    /// 成功率（0-1）
    /// </summary>
    public double SuccessRate => TaskCount > 0 ? (double)SuccessCount / TaskCount : 0;

    /// <summary>
    /// 平均执行时间（毫秒）
    /// </summary>
    public double AverageExecutionTimeMs { get; set; }

    /// <summary>
    /// 最大执行时间（毫秒）
    /// </summary>
    public long MaxExecutionTimeMs { get; set; }

    /// <summary>
    /// 最小执行时间（毫秒）
    /// </summary>
    public long MinExecutionTimeMs { get; set; }
}

/// <summary>
/// 优先级统计信息
/// </summary>
public class PriorityStatistics
{
    /// <summary>
    /// 优先级
    /// </summary>
    public int Priority { get; set; }

    /// <summary>
    /// 任务数量
    /// </summary>
    public int TaskCount { get; set; }

    /// <summary>
    /// 平均等待时间（毫秒）
    /// </summary>
    public double AverageWaitTimeMs { get; set; }

    /// <summary>
    /// 平均执行时间（毫秒）
    /// </summary>
    public double AverageExecutionTimeMs { get; set; }

    /// <summary>
    /// 成功率（0-1）
    /// </summary>
    public double SuccessRate { get; set; }
}

/// <summary>
/// 时间序列数据点
/// </summary>
public class TimeSeriesDataPoint
{
    /// <summary>
    /// 时间戳
    /// </summary>
    public DateTime Timestamp { get; set; }

    /// <summary>
    /// 数据值
    /// </summary>
    public Dictionary<string, double> Values { get; set; } = new();
}

/// <summary>
/// 实时监控数据
/// </summary>
public class RealTimeMonitoringData
{
    /// <summary>
    /// 数据时间戳
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 总活跃任务数
    /// </summary>
    public int TotalActiveTasks { get; set; }

    /// <summary>
    /// 活跃任务数（TotalActiveTasks的别名，保持向后兼容）
    /// </summary>
    public int ActiveTasks
    {
        get => TotalActiveTasks;
        set => TotalActiveTasks = value;
    }

    /// <summary>
    /// 总跟踪任务数（包括已完成的任务）
    /// </summary>
    public int TotalTrackedTasks { get; set; }

    /// <summary>
    /// 正在运行的任务数
    /// </summary>
    public int RunningTasks { get; set; }

    /// <summary>
    /// 等待中的任务数
    /// </summary>
    public int PendingTasks { get; set; }

    /// <summary>
    /// 队列中的任务数
    /// </summary>
    public int QueuedTasks { get; set; }

    /// <summary>
    /// 正在初始化的任务数
    /// </summary>
    public int InitializingTasks { get; set; }

    /// <summary>
    /// 已暂停的任务数
    /// </summary>
    public int PausedTasks { get; set; }

    /// <summary>
    /// 正在取消的任务数
    /// </summary>
    public int CancellingTasks { get; set; }

    /// <summary>
    /// 正在重试的任务数
    /// </summary>
    public int RetryingTasks { get; set; }

    /// <summary>
    /// 按节点分组的任务数量
    /// </summary>
    public Dictionary<string, int> NodeTaskCounts { get; set; } = new();

    /// <summary>
    /// 按任务类型分组的数量
    /// </summary>
    public Dictionary<string, int> TaskTypeCounts { get; set; } = new();

    /// <summary>
    /// 按优先级分布
    /// </summary>
    public Dictionary<int, int> PriorityDistribution { get; set; } = new();

    /// <summary>
    /// 最近完成的任务数（5分钟内）
    /// </summary>
    public int RecentCompletedCount { get; set; }

    /// <summary>
    /// 最近失败的任务数（5分钟内）
    /// </summary>
    public int RecentFailedCount { get; set; }

    /// <summary>
    /// 吞吐量（每分钟完成的任务数）
    /// </summary>
    public int ThroughputPerMinute { get; set; }

    /// <summary>
    /// 平均响应时间（毫秒）
    /// </summary>
    public double AverageResponseTimeMs { get; set; }

    /// <summary>
    /// 系统负载指标
    /// </summary>
    public SystemLoadIndicators SystemLoad { get; set; } = new();

    /// <summary>
    /// 告警信息
    /// </summary>
    public List<AlertInfo> ActiveAlerts { get; set; } = new();
}

/// <summary>
/// 系统负载指标
/// </summary>
public class SystemLoadIndicators
{
    /// <summary>
    /// CPU使用率（0-100）
    /// </summary>
    [Range(0, 100)]
    public double CpuUsagePercent { get; set; }

    /// <summary>
    /// 内存使用率（0-100）
    /// </summary>
    [Range(0, 100)]
    public double MemoryUsagePercent { get; set; }

    /// <summary>
    /// 磁盘使用率（0-100）
    /// </summary>
    [Range(0, 100)]
    public double DiskUsagePercent { get; set; }

    /// <summary>
    /// 网络使用率（0-100）
    /// </summary>
    [Range(0, 100)]
    public double NetworkUsagePercent { get; set; }

    /// <summary>
    /// 活跃连接数
    /// </summary>
    public int ActiveConnections { get; set; }

    /// <summary>
    /// 队列深度
    /// </summary>
    public int QueueDepth { get; set; }

    /// <summary>
    /// 错误率（0-100）
    /// </summary>
    [Range(0, 100)]
    public double ErrorRatePercent { get; set; }
}

/// <summary>
/// 告警信息
/// </summary>
public class AlertInfo
{
    /// <summary>
    /// 告警ID
    /// </summary>
    public string AlertId { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 告警类型
    /// </summary>
    public AlertType Type { get; set; }

    /// <summary>
    /// 告警级别
    /// </summary>
    public AlertSeverity Severity { get; set; }

    /// <summary>
    /// 告警标题
    /// </summary>
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// 告警描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 告警时间
    /// </summary>
    public DateTime AlertTime { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 相关任务ID
    /// </summary>
    public string? RelatedTaskId { get; set; }

    /// <summary>
    /// 相关节点ID
    /// </summary>
    public string? RelatedNodeId { get; set; }

    /// <summary>
    /// 告警数据
    /// </summary>
    public Dictionary<string, object> AlertData { get; set; } = new();

    /// <summary>
    /// 是否已确认
    /// </summary>
    public bool IsAcknowledged { get; set; }

    /// <summary>
    /// 确认时间
    /// </summary>
    public DateTime? AcknowledgedAt { get; set; }

    /// <summary>
    /// 确认人
    /// </summary>
    public string? AcknowledgedBy { get; set; }
}

/// <summary>
/// 告警类型枚举
/// </summary>
public enum AlertType
{
    /// <summary>
    /// 任务执行超时
    /// </summary>
    TaskTimeout,

    /// <summary>
    /// 任务执行失败
    /// </summary>
    TaskFailure,

    /// <summary>
    /// 高失败率
    /// </summary>
    HighFailureRate,

    /// <summary>
    /// 系统资源不足
    /// </summary>
    ResourceExhaustion,

    /// <summary>
    /// 节点不可用
    /// </summary>
    NodeUnavailable,

    /// <summary>
    /// 队列积压
    /// </summary>
    QueueBacklog,

    /// <summary>
    /// 性能下降
    /// </summary>
    PerformanceDegradation,

    /// <summary>
    /// 系统错误
    /// </summary>
    SystemError
}

/// <summary>
/// 告警严重程度枚举
/// </summary>
public enum AlertSeverity
{
    /// <summary>
    /// 信息
    /// </summary>
    Info,

    /// <summary>
    /// 警告
    /// </summary>
    Warning,

    /// <summary>
    /// 错误
    /// </summary>
    Error,

    /// <summary>
    /// 严重
    /// </summary>
    Critical
}
