/* 全局样式 */
* {
  box-sizing: border-box;
}

html, body, #root {
  height: 100%;
  margin: 0;
  padding: 0;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif, 'Apple Color Emoji',
    'Segoe UI Emoji', 'Segoe UI Symbol';
  font-size: 14px;
  line-height: 1.5715;
  color: rgba(0, 0, 0, 0.85);
  background-color: #f0f2f5;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Pro Layout 样式调整 */
.ant-pro-layout {
  height: 100vh;
}

/* ProLayout Content 已移除，直接由ProLayout控制内容渲染 */

/* 页面容器样式 - 现在直接在ProLayout内渲染 */
.page-container {
  display: flex;
  flex-direction: column;
  height: 100%; /* 占满ProLayout内容区域 */
  background: #fff;
  border-radius: 8px;
  padding: 8px;
  margin-bottom: 4px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
}

/* 页面标题样式 */
.page-header {
  margin-bottom: var(--layout-page-header-margin); /* 使用CSS变量而不是硬编码 */
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.page-title {
  font-size: 20px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
  margin: 0;
  display: flex;
  align-items: center;
}

.page-description {
  color: rgba(0, 0, 0, 0.45);
  margin-top: 8px;
  margin-bottom: 0;
  font-size: 14px;
  line-height: 1.5;
}

/* 页面内容区域 */
.page-content {
  flex: 1;
  min-height: 0; /* 允许内容区域收缩 */
}

/* 工具栏样式 */
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 状态标签样式 */
.status-tag {
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ant-pro-layout-content {
    padding: 16px;
  }
  
  .page-container {
    padding: 16px;
  }
  
  .toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }
  
  .toolbar-left,
  .toolbar-right {
    justify-content: center;
  }
}

/* 工作流表格容器样式 */
.workflow-table-container {
  display: flex;
  flex-direction: column;
  min-height: 0; /* 允许flex子元素收缩 */
}

/* 工作流设计器样式 */
.workflow-designer-container {
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.workflow-designer-container .ant-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.workflow-designer-container .ant-card-body {
  height: 100%;
  overflow: hidden;
  flex: 1;
  display: flex;
  flex-direction: column;
}



/* ========== 清理完成：所有调试样式已移除 ========== */

/* 工作流设计器测试页面样式 */
.workflow-designer-test-container {
  height: 100%;
  overflow: hidden;
}

.workflow-designer-test-container .component-library {
  height: 100%;
  overflow-y: auto;
}

.workflow-designer-test-container .node-group {
  margin-bottom: 16px;
}

.workflow-designer-test-container .group-header {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background: #f5f5f5;
  border-radius: 6px;
  cursor: pointer;
  margin-bottom: 8px;
  font-size: 13px;
  font-weight: 500;
  color: #333;
  transition: background-color 0.2s ease;
}

.workflow-designer-test-container .group-header:hover {
  background: #e6f7ff;
}

.workflow-designer-test-container .workflow-node-item {
  display: flex;
  align-items: center;
  padding: 12px;
  margin: 4px 0;
  border-radius: 8px;
  cursor: grab;
  transition: all 0.2s ease;
  min-height: 60px;
  user-select: none;
}

.workflow-designer-test-container .workflow-node-item:active {
  cursor: grabbing;
}

.workflow-designer-test-container .workflow-node-item:hover {
  transform: translateY(-2px);
}

/* 导航菜单样式 */
.workflow-navigation .ant-tabs-card > .ant-tabs-nav .ant-tabs-tab {
  border-radius: 6px 6px 0 0;
  border: 1px solid #d9d9d9;
  background: #fafafa;
  margin-right: 2px;
}

.workflow-navigation .ant-tabs-card > .ant-tabs-nav .ant-tabs-tab-active {
  background: #fff;
  border-bottom-color: #fff;
}

.workflow-navigation .ant-tabs-card > .ant-tabs-nav {
  margin: 0;
}

.workflow-navigation .ant-tabs-card .ant-tabs-content {
  height: auto;
}

.workflow-navigation .ant-tabs-card > .ant-tabs-nav::before {
  border-bottom: 1px solid #d9d9d9;
}

/* 工具栏样式 */
.workflow-toolbar {
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

.workflow-toolbar .ant-btn {
  border-radius: 4px;
}

.workflow-toolbar .ant-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* ReactFlow 设计器样式 */
.workflow-designer-reactflow-container {
  height: 100%;
  overflow: hidden;
}

.workflow-designer-reactflow-container .react-flow__node {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.workflow-designer-reactflow-container .react-flow__edge {
  stroke: #1890ff;
  stroke-width: 2;
}

.workflow-designer-reactflow-container .react-flow__edge.animated {
  stroke-dasharray: 5;
  animation: dashdraw 0.5s linear infinite;
}

.workflow-designer-reactflow-container .react-flow__edge.selected {
  stroke: #ff4d4f;
}

@keyframes dashdraw {
  to {
    stroke-dashoffset: -10;
  }
}

/* ReactFlow 控制按钮样式 */
.workflow-designer-reactflow-container .react-flow__controls {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.workflow-designer-reactflow-container .react-flow__controls-button {
  border: none;
  background: white;
  border-bottom: 1px solid #d9d9d9;
}

.workflow-designer-reactflow-container .react-flow__controls-button:hover {
  background: #f5f5f5;
}

/* ReactFlow 小地图样式 */
.workflow-designer-reactflow-container .react-flow__minimap {
  background: #fafafa;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
}
