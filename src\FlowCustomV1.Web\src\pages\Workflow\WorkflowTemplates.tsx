import React, { useEffect, useState } from 'react';
import { Card, Row, Col, Button, Tag, Space, Input, Select, message, Modal } from 'antd';
import { 
  PlusOutlined, 
  SearchOutlined, 
  EyeOutlined, 
  CopyOutlined,
  StarOutlined,
  StarFilled,

  FilterOutlined
} from '@ant-design/icons';

import { useNavigate } from 'react-router-dom';
import { templateApi } from '@/services/workflow';
import type { WorkflowDefinition } from '@/types/api';

const { Search } = Input;
const { Option } = Select;

// 模拟模板数据
const mockTemplates: WorkflowDefinition[] = [
  {
    workflowId: 'template-1',
    name: '数据处理流水线',
    description: '用于处理CSV数据文件的标准流水线，包含数据清洗、转换和输出',
    version: '1.0.0',
    isActive: true,
    createdAt: '2024-01-15T10:00:00Z',
    lastModifiedAt: '2024-01-15T10:00:00Z',
    nodes: [],
    connections: [],
    tags: ['数据处理', 'CSV', '流水线'],
    publishStatus: 'Published',
  },
  {
    workflowId: 'template-2',
    name: 'API数据同步',
    description: '定期从外部API获取数据并同步到本地数据库',
    version: '2.1.0',
    isActive: true,
    createdAt: '2024-01-10T14:30:00Z',
    lastModifiedAt: '2024-01-20T09:15:00Z',
    nodes: [],
    connections: [],
    tags: ['API', '数据同步', '定时任务'],
    publishStatus: 'Published',
  },
  {
    workflowId: 'template-3',
    name: '文件批处理',
    description: '批量处理文件夹中的文件，支持多种文件格式转换',
    version: '1.5.0',
    isActive: true,
    createdAt: '2024-01-05T16:45:00Z',
    lastModifiedAt: '2024-01-18T11:20:00Z',
    nodes: [],
    connections: [],
    tags: ['文件处理', '批处理', '格式转换'],
    publishStatus: 'Published',
  },
  {
    workflowId: 'template-4',
    name: '邮件通知系统',
    description: '基于条件触发的智能邮件通知系统',
    version: '1.2.0',
    isActive: true,
    createdAt: '2024-01-12T08:00:00Z',
    lastModifiedAt: '2024-01-22T15:30:00Z',
    nodes: [],
    connections: [],
    tags: ['邮件', '通知', '条件触发'],
    publishStatus: 'Published',
  },
  {
    workflowId: 'template-5',
    name: '数据库备份',
    description: '自动化数据库备份和恢复流程',
    version: '3.0.0',
    isActive: true,
    createdAt: '2024-01-08T12:15:00Z',
    lastModifiedAt: '2024-01-25T10:45:00Z',
    nodes: [],
    connections: [],
    tags: ['数据库', '备份', '自动化'],
    publishStatus: 'Published',
  },
  {
    workflowId: 'template-6',
    name: '日志分析',
    description: '实时日志分析和异常检测工作流',
    version: '1.8.0',
    isActive: true,
    createdAt: '2024-01-03T09:30:00Z',
    lastModifiedAt: '2024-01-28T14:00:00Z',
    nodes: [],
    connections: [],
    tags: ['日志', '分析', '异常检测'],
    publishStatus: 'Published',
  },
];

const WorkflowTemplates: React.FC = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [templates, setTemplates] = useState<WorkflowDefinition[]>([]);
  const [filteredTemplates, setFilteredTemplates] = useState<WorkflowDefinition[]>([]);
  const [searchText, setSearchText] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [favorites, setFavorites] = useState<string[]>([]);

  // 加载模板列表
  const loadTemplates = async () => {
    try {
      setLoading(true);
      // 暂时使用模拟数据
      setTemplates(mockTemplates);
      setFilteredTemplates(mockTemplates);
    } catch (error) {
      console.error('加载模板列表失败:', error);
      message.error('加载模板列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadTemplates();
  }, []);

  // 搜索和筛选
  useEffect(() => {
    let filtered = templates;

    // 文本搜索
    if (searchText) {
      filtered = filtered.filter(template =>
        template.name.toLowerCase().includes(searchText.toLowerCase()) ||
        template.description?.toLowerCase().includes(searchText.toLowerCase()) ||
        template.tags?.some(tag => tag.toLowerCase().includes(searchText.toLowerCase()))
      );
    }

    // 分类筛选
    if (selectedCategory) {
      filtered = filtered.filter(template =>
        template.tags?.includes(selectedCategory)
      );
    }

    setFilteredTemplates(filtered);
  }, [templates, searchText, selectedCategory]);

  // 获取所有标签
  const getAllTags = () => {
    const tags = new Set<string>();
    templates.forEach(template => {
      template.tags?.forEach(tag => tags.add(tag));
    });
    return Array.from(tags);
  };

  // 切换收藏
  const toggleFavorite = (templateId: string) => {
    setFavorites(prev => 
      prev.includes(templateId) 
        ? prev.filter(id => id !== templateId)
        : [...prev, templateId]
    );
  };

  // 使用模板创建工作流
  const handleUseTemplate = (template: WorkflowDefinition) => {
    Modal.confirm({
      title: '使用模板',
      content: `确定要使用模板"${template.name}"创建新的工作流吗？`,
      onOk: async () => {
        try {
          const newWorkflow = await templateApi.createFromTemplate(
            template.workflowId, 
            `${template.name} - 副本`
          );
          message.success('工作流创建成功');
          navigate(`/workflow/designer/${newWorkflow.workflowId}`);
        } catch (error) {
          message.error('创建工作流失败');
        }
      },
    });
  };

  // 预览模板
  const handlePreview = (template: WorkflowDefinition) => {
    navigate(`/workflow/designer/${template.workflowId}?mode=view`);
  };

  return (
    <div className="page-container">
      <div className="page-header">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="page-title">工作流模板库</h1>
            <p className="page-description">选择合适的模板快速创建工作流</p>
          </div>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => navigate('/workflow/designer')}
          >
            创建空白工作流
          </Button>
        </div>
      </div>

      {/* 搜索和筛选 */}
        <div className="flex justify-between items-center mb-6">
          <Space size="large">
            <Search
              placeholder="搜索模板名称、描述或标签"
              allowClear
              style={{ width: 400 }}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              prefix={<SearchOutlined />}
            />
            <Select
              placeholder="选择分类"
              allowClear
              style={{ width: 200 }}
              value={selectedCategory}
              onChange={setSelectedCategory}
              suffixIcon={<FilterOutlined />}
            >
              {getAllTags().map(tag => (
                <Option key={tag} value={tag}>{tag}</Option>
              ))}
            </Select>
          </Space>
          <div className="text-gray-500">
            共找到 {filteredTemplates.length} 个模板
          </div>
        </div>

      {/* 模板网格 */}
      <Row gutter={[16, 16]}>
        {filteredTemplates.map(template => (
          <Col xs={24} sm={12} lg={8} xl={6} key={template.workflowId}>
            <Card
              hoverable
              className="h-full"
              cover={
                <div className="h-32 bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
                  <div className="text-4xl text-blue-400">📋</div>
                </div>
              }
              actions={[
                <Button 
                  key="preview"
                  type="text" 
                  icon={<EyeOutlined />}
                  onClick={() => handlePreview(template)}
                >
                  预览
                </Button>,
                <Button 
                  key="use"
                  type="text" 
                  icon={<CopyOutlined />}
                  onClick={() => handleUseTemplate(template)}
                >
                  使用
                </Button>,
                <Button 
                  key="favorite"
                  type="text" 
                  icon={favorites.includes(template.workflowId) ? <StarFilled /> : <StarOutlined />}
                  onClick={() => toggleFavorite(template.workflowId)}
                  className={favorites.includes(template.workflowId) ? 'text-yellow-500' : ''}
                >
                  收藏
                </Button>,
              ]}
            >
              <Card.Meta
                title={
                  <div className="flex justify-between items-start">
                    <span className="truncate">{template.name}</span>
                    <Tag color="blue" className="ml-2">{template.version}</Tag>
                  </div>
                }
                description={
                  <div>
                    <p className="text-gray-600 mb-3 line-clamp-2">
                      {template.description}
                    </p>
                    <div className="flex flex-wrap gap-1 mb-2">
                      {template.tags?.slice(0, 3).map(tag => (
                        <Tag key={tag}>{tag}</Tag>
                      ))}
                      {template.tags && template.tags.length > 3 && (
                        <Tag>+{template.tags.length - 3}</Tag>
                      )}
                    </div>
                    <div className="text-xs text-gray-400">
                      更新于 {new Date(template.lastModifiedAt).toLocaleDateString()}
                    </div>
                  </div>
                }
              />
            </Card>
          </Col>
        ))}
      </Row>

      {/* 空状态 */}
      {filteredTemplates.length === 0 && !loading && (
        <div className="text-center py-20">
          <div className="text-6xl text-gray-300 mb-4">📋</div>
          <h3 className="text-xl font-semibold text-gray-600 mb-2">
            没有找到匹配的模板
          </h3>
          <p className="text-gray-500 mb-4">
            尝试调整搜索条件或创建新的工作流
          </p>
          <Space>
            <Button onClick={() => { setSearchText(''); setSelectedCategory(''); }}>
              清除筛选
            </Button>
            <Button 
              type="primary" 
              onClick={() => navigate('/workflow/designer')}
            >
              创建新工作流
            </Button>
          </Space>
        </div>
      )}

      {/* 加载状态 */}
      {loading && (
        <div className="text-center py-20">
          <div className="text-lg">加载模板中...</div>
        </div>
      )}
    </div>
  );
};

export default WorkflowTemplates;
