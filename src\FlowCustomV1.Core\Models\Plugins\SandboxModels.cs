using FlowCustomV1.Core.Models.Workflow;

namespace FlowCustomV1.Core.Models.Plugins;

/// <summary>
/// 沙箱配置
/// </summary>
public class SandboxConfiguration
{
    /// <summary>
    /// 是否启用沙箱
    /// </summary>
    public bool EnableSandbox { get; set; } = true;

    /// <summary>
    /// 资源限制
    /// </summary>
    public ResourceLimits ResourceLimits { get; set; } = new();

    /// <summary>
    /// 权限策略
    /// </summary>
    public PermissionPolicy PermissionPolicy { get; set; } = new();

    /// <summary>
    /// 执行超时时间（毫秒）
    /// </summary>
    public int ExecutionTimeoutMs { get; set; } = 30000;

    /// <summary>
    /// 是否允许网络访问
    /// </summary>
    public bool AllowNetworkAccess { get; set; } = false;

    /// <summary>
    /// 是否允许文件系统访问
    /// </summary>
    public bool AllowFileSystemAccess { get; set; } = false;

    /// <summary>
    /// 允许访问的程序集
    /// </summary>
    public List<string> AllowedAssemblies { get; set; } = new();

    /// <summary>
    /// 禁止访问的程序集
    /// </summary>
    public List<string> ForbiddenAssemblies { get; set; } = new();

    /// <summary>
    /// 允许访问的命名空间
    /// </summary>
    public List<string> AllowedNamespaces { get; set; } = new();

    /// <summary>
    /// 禁止访问的命名空间
    /// </summary>
    public List<string> ForbiddenNamespaces { get; set; } = new();

    /// <summary>
    /// 自定义安全策略
    /// </summary>
    public Dictionary<string, object> CustomSecurityPolicies { get; set; } = new();
}

/// <summary>
/// 资源限制
/// </summary>
public class ResourceLimits
{
    /// <summary>
    /// 最大内存使用量（MB）
    /// </summary>
    public int MaxMemoryMB { get; set; } = 256;

    /// <summary>
    /// 最大CPU使用率（百分比）
    /// </summary>
    public int MaxCpuPercent { get; set; } = 50;

    /// <summary>
    /// 最大执行时间（毫秒）
    /// </summary>
    public int MaxExecutionTimeMs { get; set; } = 30000;

    /// <summary>
    /// 最大线程数
    /// </summary>
    public int MaxThreads { get; set; } = 10;

    /// <summary>
    /// 最大文件句柄数
    /// </summary>
    public int MaxFileHandles { get; set; } = 100;

    /// <summary>
    /// 最大网络连接数
    /// </summary>
    public int MaxNetworkConnections { get; set; } = 10;

    /// <summary>
    /// 最大磁盘使用量（MB）
    /// </summary>
    public int MaxDiskUsageMB { get; set; } = 100;
}

/// <summary>
/// 权限策略
/// </summary>
public class PermissionPolicy
{
    /// <summary>
    /// 默认权限模式
    /// </summary>
    public PermissionMode DefaultMode { get; set; } = PermissionMode.Deny;

    /// <summary>
    /// 允许的权限列表
    /// </summary>
    public List<string> AllowedPermissions { get; set; } = new();

    /// <summary>
    /// 拒绝的权限列表
    /// </summary>
    public List<string> DeniedPermissions { get; set; } = new();

    /// <summary>
    /// 权限规则
    /// </summary>
    public List<PermissionRule> Rules { get; set; } = new();

    /// <summary>
    /// 是否启用权限审计
    /// </summary>
    public bool EnableAudit { get; set; } = true;
}

/// <summary>
/// 权限模式
/// </summary>
public enum PermissionMode
{
    /// <summary>
    /// 默认允许
    /// </summary>
    Allow,

    /// <summary>
    /// 默认拒绝
    /// </summary>
    Deny
}

/// <summary>
/// 权限规则
/// </summary>
public class PermissionRule
{
    /// <summary>
    /// 规则名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 权限名称
    /// </summary>
    public string Permission { get; set; } = string.Empty;

    /// <summary>
    /// 规则动作
    /// </summary>
    public PermissionAction Action { get; set; }

    /// <summary>
    /// 条件表达式
    /// </summary>
    public string? Condition { get; set; }

    /// <summary>
    /// 优先级
    /// </summary>
    public int Priority { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; } = true;
}

/// <summary>
/// 权限动作
/// </summary>
public enum PermissionAction
{
    /// <summary>
    /// 允许
    /// </summary>
    Allow,

    /// <summary>
    /// 拒绝
    /// </summary>
    Deny,

    /// <summary>
    /// 审计
    /// </summary>
    Audit
}

/// <summary>
/// 沙箱执行结果
/// </summary>
public class SandboxExecutionResult
{
    /// <summary>
    /// 是否执行成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 执行结果
    /// </summary>
    public NodeExecutionResult? ExecutionResult { get; set; }

    /// <summary>
    /// 资源使用情况
    /// </summary>
    public ResourceUsage ResourceUsage { get; set; } = new();

    /// <summary>
    /// 权限违规记录
    /// </summary>
    public List<PermissionViolation> PermissionViolations { get; set; } = new();

    /// <summary>
    /// 执行时间（毫秒）
    /// </summary>
    public long ExecutionTimeMs { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 异常信息
    /// </summary>
    public Exception? Exception { get; set; }

    /// <summary>
    /// 是否超时
    /// </summary>
    public bool IsTimeout { get; set; }

    /// <summary>
    /// 是否被终止
    /// </summary>
    public bool IsTerminated { get; set; }

    /// <summary>
    /// 终止原因
    /// </summary>
    public string? TerminationReason { get; set; }
}

/// <summary>
/// 资源使用情况
/// </summary>
public class ResourceUsage
{
    /// <summary>
    /// 内存使用量（MB）
    /// </summary>
    public double MemoryUsageMB { get; set; }

    /// <summary>
    /// CPU使用率（百分比）
    /// </summary>
    public double CpuUsagePercent { get; set; }

    /// <summary>
    /// 执行时间（毫秒）
    /// </summary>
    public long ExecutionTimeMs { get; set; }

    /// <summary>
    /// 线程数
    /// </summary>
    public int ThreadCount { get; set; }

    /// <summary>
    /// 文件句柄数
    /// </summary>
    public int FileHandleCount { get; set; }

    /// <summary>
    /// 网络连接数
    /// </summary>
    public int NetworkConnectionCount { get; set; }

    /// <summary>
    /// 磁盘使用量（MB）
    /// </summary>
    public double DiskUsageMB { get; set; }

    /// <summary>
    /// 峰值内存使用量（MB）
    /// </summary>
    public double PeakMemoryUsageMB { get; set; }

    /// <summary>
    /// 峰值CPU使用率（百分比）
    /// </summary>
    public double PeakCpuUsagePercent { get; set; }
}

/// <summary>
/// 权限违规记录
/// </summary>
public class PermissionViolation
{
    /// <summary>
    /// 违规时间
    /// </summary>
    public DateTime OccurredAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 权限名称
    /// </summary>
    public string Permission { get; set; } = string.Empty;

    /// <summary>
    /// 违规描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 违规严重程度
    /// </summary>
    public ViolationSeverity Severity { get; set; }

    /// <summary>
    /// 违规动作
    /// </summary>
    public string Action { get; set; } = string.Empty;

    /// <summary>
    /// 相关资源
    /// </summary>
    public string? Resource { get; set; }

    /// <summary>
    /// 堆栈跟踪
    /// </summary>
    public string? StackTrace { get; set; }
}

/// <summary>
/// 违规严重程度
/// </summary>
public enum ViolationSeverity
{
    /// <summary>
    /// 低
    /// </summary>
    Low,

    /// <summary>
    /// 中
    /// </summary>
    Medium,

    /// <summary>
    /// 高
    /// </summary>
    High,

    /// <summary>
    /// 严重
    /// </summary>
    Critical
}

/// <summary>
/// 隔离执行环境
/// </summary>
public class IsolatedExecutionEnvironment
{
    /// <summary>
    /// 环境ID
    /// </summary>
    public string Id { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 环境名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 插件信息
    /// </summary>
    public PluginInfo PluginInfo { get; set; } = default!;

    /// <summary>
    /// 沙箱配置
    /// </summary>
    public SandboxConfiguration Configuration { get; set; } = new();

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 最后使用时间
    /// </summary>
    public DateTime LastUsedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 是否活跃
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// 环境状态
    /// </summary>
    public EnvironmentStatus Status { get; set; } = EnvironmentStatus.Created;

    /// <summary>
    /// 资源使用统计
    /// </summary>
    public ResourceUsage ResourceUsage { get; set; } = new();
}

/// <summary>
/// 环境状态
/// </summary>
public enum EnvironmentStatus
{
    /// <summary>
    /// 已创建
    /// </summary>
    Created,

    /// <summary>
    /// 正在初始化
    /// </summary>
    Initializing,

    /// <summary>
    /// 就绪
    /// </summary>
    Ready,

    /// <summary>
    /// 运行中
    /// </summary>
    Running,

    /// <summary>
    /// 暂停
    /// </summary>
    Paused,

    /// <summary>
    /// 正在销毁
    /// </summary>
    Destroying,

    /// <summary>
    /// 已销毁
    /// </summary>
    Destroyed,

    /// <summary>
    /// 错误
    /// </summary>
    Error
}

/// <summary>
/// 沙箱统计信息
/// </summary>
public class SandboxStatistics
{
    /// <summary>
    /// 总执行次数
    /// </summary>
    public long TotalExecutions { get; set; }

    /// <summary>
    /// 成功执行次数
    /// </summary>
    public long SuccessfulExecutions { get; set; }

    /// <summary>
    /// 失败执行次数
    /// </summary>
    public long FailedExecutions { get; set; }

    /// <summary>
    /// 超时执行次数
    /// </summary>
    public long TimeoutExecutions { get; set; }

    /// <summary>
    /// 权限违规次数
    /// </summary>
    public long PermissionViolations { get; set; }

    /// <summary>
    /// 资源超限次数
    /// </summary>
    public long ResourceLimitExceeded { get; set; }

    /// <summary>
    /// 平均执行时间（毫秒）
    /// </summary>
    public double AverageExecutionTimeMs { get; set; }

    /// <summary>
    /// 平均内存使用量（MB）
    /// </summary>
    public double AverageMemoryUsageMB { get; set; }

    /// <summary>
    /// 平均CPU使用率（百分比）
    /// </summary>
    public double AverageCpuUsagePercent { get; set; }

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 资源超限事件参数
/// </summary>
public class ResourceLimitExceededEventArgs : EventArgs
{
    /// <summary>
    /// 节点类型
    /// </summary>
    public string NodeType { get; set; } = string.Empty;

    /// <summary>
    /// 超限资源类型
    /// </summary>
    public string ResourceType { get; set; } = string.Empty;

    /// <summary>
    /// 当前使用量
    /// </summary>
    public double CurrentUsage { get; set; }

    /// <summary>
    /// 限制值
    /// </summary>
    public double Limit { get; set; }

    /// <summary>
    /// 超限时间
    /// </summary>
    public DateTime OccurredAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 处理动作
    /// </summary>
    public string Action { get; set; } = string.Empty;
}

/// <summary>
/// 权限违规事件参数
/// </summary>
public class PermissionViolationEventArgs : EventArgs
{
    /// <summary>
    /// 节点类型
    /// </summary>
    public string NodeType { get; set; } = string.Empty;

    /// <summary>
    /// 权限违规记录
    /// </summary>
    public PermissionViolation Violation { get; set; } = default!;

    /// <summary>
    /// 处理动作
    /// </summary>
    public string Action { get; set; } = string.Empty;
}

/// <summary>
/// 执行超时事件参数
/// </summary>
public class ExecutionTimeoutEventArgs : EventArgs
{
    /// <summary>
    /// 节点类型
    /// </summary>
    public string NodeType { get; set; } = string.Empty;

    /// <summary>
    /// 执行时间（毫秒）
    /// </summary>
    public long ExecutionTimeMs { get; set; }

    /// <summary>
    /// 超时限制（毫秒）
    /// </summary>
    public long TimeoutLimitMs { get; set; }

    /// <summary>
    /// 超时时间
    /// </summary>
    public DateTime OccurredAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 处理动作
    /// </summary>
    public string Action { get; set; } = string.Empty;
}
