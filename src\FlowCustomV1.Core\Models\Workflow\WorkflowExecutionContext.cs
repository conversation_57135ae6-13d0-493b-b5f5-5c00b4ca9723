using System.Collections.Concurrent;
using System.Text.Json.Serialization;
using FlowCustomV1.Core.Models.Executor;

namespace FlowCustomV1.Core.Models.Workflow;

/// <summary>
/// 工作流执行上下文
/// 包含工作流执行过程中的所有状态信息
/// </summary>
public class WorkflowExecutionContext
{
    /// <summary>
    /// 执行ID
    /// </summary>
    [JsonPropertyName("executionId")]
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 工作流ID
    /// </summary>
    [JsonPropertyName("workflowId")]
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 工作流定义
    /// </summary>
    [JsonPropertyName("definition")]
    public WorkflowDefinition Definition { get; set; } = new();

    /// <summary>
    /// 执行状态
    /// </summary>
    [JsonPropertyName("state")]
    public WorkflowExecutionState State { get; set; } = WorkflowExecutionState.NotStarted;

    /// <summary>
    /// 开始时间
    /// </summary>
    [JsonPropertyName("startedAt")]
    public DateTime StartedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 完成时间
    /// </summary>
    [JsonPropertyName("completedAt")]
    public DateTime? CompletedAt { get; set; }

    /// <summary>
    /// 执行时长
    /// </summary>
    [JsonIgnore]
    public TimeSpan? Duration => CompletedAt?.Subtract(StartedAt);

    /// <summary>
    /// 输入数据
    /// </summary>
    [JsonPropertyName("inputData")]
    public ConcurrentDictionary<string, object> InputData { get; set; } = new();

    /// <summary>
    /// 输出数据
    /// </summary>
    [JsonPropertyName("outputData")]
    public ConcurrentDictionary<string, object> OutputData { get; set; } = new();

    /// <summary>
    /// 工作流数据（节点间共享的数据）
    /// </summary>
    [JsonPropertyName("workflowData")]
    public ConcurrentDictionary<string, object> WorkflowData { get; set; } = new();

    /// <summary>
    /// 节点状态映射
    /// </summary>
    [JsonPropertyName("nodeStates")]
    public ConcurrentDictionary<string, NodeExecutionState> NodeStates { get; set; } = new();

    /// <summary>
    /// 节点执行结果映射
    /// </summary>
    [JsonPropertyName("nodeResults")]
    public ConcurrentDictionary<string, NodeExecutionResult> NodeResults { get; set; } = new();

    /// <summary>
    /// 当前执行的节点ID
    /// </summary>
    [JsonPropertyName("currentNodeId")]
    public string? CurrentNodeId { get; set; }

    /// <summary>
    /// 已完成的节点数
    /// </summary>
    [JsonPropertyName("completedNodes")]
    public int CompletedNodes { get; set; }

    /// <summary>
    /// 总节点数
    /// </summary>
    [JsonPropertyName("totalNodes")]
    public int TotalNodes => Definition.Nodes?.Count ?? 0;

    /// <summary>
    /// 执行进度（0-100）
    /// </summary>
    [JsonIgnore]
    public double Progress => TotalNodes > 0 ? (double)CompletedNodes / TotalNodes * 100 : 0;

    /// <summary>
    /// 错误信息
    /// </summary>
    [JsonPropertyName("errorMessage")]
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 异常信息
    /// </summary>
    [JsonIgnore]
    public Exception? Exception { get; set; }

    /// <summary>
    /// 执行统计信息
    /// </summary>
    [JsonPropertyName("stats")]
    public WorkflowExecutionStats Stats { get; set; } = new();

    /// <summary>
    /// 执行元数据
    /// </summary>
    [JsonPropertyName("metadata")]
    public Dictionary<string, object> Metadata { get; set; } = new();

    /// <summary>
    /// 执行器节点ID
    /// </summary>
    [JsonPropertyName("executorNodeId")]
    public string ExecutorNodeId { get; set; } = Environment.MachineName;

    /// <summary>
    /// 执行优先级
    /// </summary>
    [JsonPropertyName("priority")]
    public ExecutionPriority Priority { get; set; } = ExecutionPriority.Normal;

    /// <summary>
    /// 执行标签
    /// </summary>
    [JsonPropertyName("tags")]
    public List<string> Tags { get; set; } = new();

    /// <summary>
    /// 是否可以取消
    /// </summary>
    [JsonIgnore]
    public bool CanCancel => State == WorkflowExecutionState.Running || State == WorkflowExecutionState.Paused;

    /// <summary>
    /// 是否可以暂停
    /// </summary>
    [JsonIgnore]
    public bool CanPause => State == WorkflowExecutionState.Running;

    /// <summary>
    /// 是否可以恢复
    /// </summary>
    [JsonIgnore]
    public bool CanResume => State == WorkflowExecutionState.Paused;

    /// <summary>
    /// 是否已完成
    /// </summary>
    [JsonIgnore]
    public bool IsCompleted => State == WorkflowExecutionState.Completed || 
                               State == WorkflowExecutionState.Failed || 
                               State == WorkflowExecutionState.Cancelled;

    /// <summary>
    /// 是否成功
    /// </summary>
    [JsonIgnore]
    public bool IsSuccess => State == WorkflowExecutionState.Completed;

    /// <summary>
    /// 获取节点状态
    /// </summary>
    /// <param name="nodeId">节点ID</param>
    /// <returns>节点状态</returns>
    public NodeExecutionState GetNodeState(string nodeId)
    {
        return NodeStates.GetValueOrDefault(nodeId, NodeExecutionState.NotStarted);
    }

    /// <summary>
    /// 设置节点状态
    /// </summary>
    /// <param name="nodeId">节点ID</param>
    /// <param name="state">节点状态</param>
    public void SetNodeState(string nodeId, NodeExecutionState state)
    {
        NodeStates[nodeId] = state;
        
        // 更新统计信息
        if (state == NodeExecutionState.Completed)
        {
            CompletedNodes = NodeStates.Values.Count(s => s == NodeExecutionState.Completed);
        }
    }

    /// <summary>
    /// 获取节点执行结果
    /// </summary>
    /// <param name="nodeId">节点ID</param>
    /// <returns>节点执行结果</returns>
    public NodeExecutionResult? GetNodeResult(string nodeId)
    {
        return NodeResults.GetValueOrDefault(nodeId);
    }

    /// <summary>
    /// 设置节点执行结果
    /// </summary>
    /// <param name="nodeId">节点ID</param>
    /// <param name="result">节点执行结果</param>
    public void SetNodeResult(string nodeId, NodeExecutionResult result)
    {
        NodeResults[nodeId] = result;
    }

    /// <summary>
    /// 获取工作流数据
    /// </summary>
    /// <param name="key">数据键</param>
    /// <returns>数据值</returns>
    public object? GetWorkflowData(string key)
    {
        return WorkflowData.GetValueOrDefault(key);
    }

    /// <summary>
    /// 设置工作流数据
    /// </summary>
    /// <param name="key">数据键</param>
    /// <param name="value">数据值</param>
    public void SetWorkflowData(string key, object value)
    {
        WorkflowData[key] = value;
    }

    /// <summary>
    /// 获取强类型工作流数据
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    /// <param name="key">数据键</param>
    /// <returns>强类型数据值</returns>
    public T? GetWorkflowData<T>(string key)
    {
        var value = GetWorkflowData(key);
        if (value is T typedValue)
        {
            return typedValue;
        }
        
        if (value != null)
        {
            try
            {
                return (T)Convert.ChangeType(value, typeof(T));
            }
            catch
            {
                // 转换失败，返回默认值
            }
        }
        
        return default(T);
    }

    /// <summary>
    /// 创建执行结果
    /// </summary>
    /// <returns>工作流执行结果</returns>
    public WorkflowExecutionResult CreateResult()
    {
        return new WorkflowExecutionResult
        {
            ExecutionId = ExecutionId,
            WorkflowId = WorkflowId,
            State = State,
            IsSuccess = IsSuccess,
            StartedAt = StartedAt,
            CompletedAt = CompletedAt,
            OutputData = new Dictionary<string, object>(OutputData),
            NodeResults = NodeResults.Values.ToList(),
            ErrorMessage = ErrorMessage,
            Stats = Stats,
            Metadata = new Dictionary<string, object>(Metadata)
        };
    }

    /// <summary>
    /// 克隆执行上下文
    /// </summary>
    /// <returns>克隆的执行上下文</returns>
    public WorkflowExecutionContext Clone()
    {
        return new WorkflowExecutionContext
        {
            ExecutionId = ExecutionId,
            WorkflowId = WorkflowId,
            Definition = Definition, // 工作流定义通常是不可变的，可以共享引用
            State = State,
            StartedAt = StartedAt,
            CompletedAt = CompletedAt,
            InputData = new ConcurrentDictionary<string, object>(InputData),
            OutputData = new ConcurrentDictionary<string, object>(OutputData),
            WorkflowData = new ConcurrentDictionary<string, object>(WorkflowData),
            NodeStates = new ConcurrentDictionary<string, NodeExecutionState>(NodeStates),
            NodeResults = new ConcurrentDictionary<string, NodeExecutionResult>(NodeResults),
            CurrentNodeId = CurrentNodeId,
            CompletedNodes = CompletedNodes,
            ErrorMessage = ErrorMessage,
            Stats = Stats, // 统计信息可以共享引用
            Metadata = new Dictionary<string, object>(Metadata),
            ExecutorNodeId = ExecutorNodeId,
            Priority = Priority,
            Tags = new List<string>(Tags)
        };
    }
}
