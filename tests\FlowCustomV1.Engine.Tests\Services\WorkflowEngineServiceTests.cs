using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using FlowCustomV1.Engine.Services;
using FlowCustomV1.Engine.Schedulers;
using FlowCustomV1.Engine.StateTracking;
using FlowCustomV1.Engine.ErrorHandling;

namespace FlowCustomV1.Engine.Tests.Services;

/// <summary>
/// WorkflowEngineService 测试类
/// </summary>
public class WorkflowEngineServiceTests : TestBase
{
    [Fact]
    public void WorkflowEngineService_ShouldBeCreatedSuccessfully()
    {
        // Arrange
        var logger = ServiceProvider.GetRequiredService<ILogger<WorkflowEngineService>>();
        var scheduler = ServiceProvider.GetRequiredService<INodeScheduler>();
        var stateTracker = ServiceProvider.GetRequiredService<IExecutionStateTracker>();
        var errorHandler = ServiceProvider.GetRequiredService<IErrorHandler>();
        
        // Act
        var engine = new WorkflowEngineService(ServiceProvider, logger, scheduler, stateTracker, errorHandler);

        // Assert
        engine.Should().NotBeNull();
    }

    [Fact]
    public async Task StartAsync_ShouldStartEngine()
    {
        // Arrange
        var logger = ServiceProvider.GetRequiredService<ILogger<WorkflowEngineService>>();
        var scheduler = ServiceProvider.GetRequiredService<INodeScheduler>();
        var stateTracker = ServiceProvider.GetRequiredService<IExecutionStateTracker>();
        var errorHandler = ServiceProvider.GetRequiredService<IErrorHandler>();
        var engine = new WorkflowEngineService(ServiceProvider, logger, scheduler, stateTracker, errorHandler);

        // Act
        await engine.StartAsync();

        // Assert
        // 如果没有抛出异常，说明启动成功
        true.Should().BeTrue(); // 占位断言
    }
}