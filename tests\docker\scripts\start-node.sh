#!/bin/bash

# FlowCustomV1 测试节点启动脚本
# 根据节点角色配置不同的启动参数

set -e

echo "=========================================="
echo "FlowCustomV1 Test Node Startup"
echo "=========================================="
echo "Node Role: ${NODE_ROLE:-Unknown}"
echo "Node ID: ${NODE_ID:-Unknown}"
echo "Region: ${REGION:-Unknown}"
echo "DataCenter: ${DATACENTER:-Unknown}"
echo "NATS Configuration: Set via environment variables"
echo "=========================================="

# 等待依赖服务启动
echo "Waiting for dependencies..."

# 等待NATS集群
# 等待NATS服务器（使用固定的服务名）
echo "Waiting for NATS server: nats:4222"
while ! nc -z nats 4222; do
    echo "  NATS server nats:4222 is not ready, waiting..."
    sleep 2
done
echo "  ✓ NATS server nats:4222 is ready"

# 等待MySQL数据库
if [ ! -z "$MYSQL_CONNECTION" ]; then
    mysql_host=$(echo $MYSQL_CONNECTION | grep -o 'Server=[^;]*' | cut -d'=' -f2)
    mysql_port=3306
    
    echo "Waiting for MySQL server: $mysql_host:$mysql_port"
    while ! nc -z $mysql_host $mysql_port; do
        echo "  MySQL server is not ready, waiting..."
        sleep 2
    done
    echo "  ✓ MySQL server is ready"
fi

# 根据节点角色设置特定配置
case "$NODE_ROLE" in
    "Master")
        echo "Configuring Master node..."
        export ENABLE_TASK_DISTRIBUTION=true
        export ENABLE_CLUSTER_MANAGEMENT=true
        export ENABLE_LOAD_BALANCING=true
        export MAX_CONCURRENT_DISTRIBUTIONS=10
        ;;
    "Worker")
        echo "Configuring Worker node..."
        export ENABLE_TASK_EXECUTION=true
        export MAX_CONCURRENT_TASKS=${WORKER_MAX_TASKS:-10}
        export WORKER_CPU_CORES=${WORKER_CPU_CORES:-4}
        export WORKER_MEMORY_MB=${WORKER_MEMORY_MB:-8192}
        ;;
    "Designer")
        echo "Configuring Designer node..."
        export ENABLE_WORKFLOW_DESIGN=true
        export ENABLE_COLLABORATION=true
        export MAX_CONCURRENT_DESIGNS=5
        ;;
    "Validator")
        echo "Configuring Validator node..."
        export ENABLE_WORKFLOW_VALIDATION=true
        export ENABLE_DISTRIBUTED_VALIDATION=true
        export MAX_CONCURRENT_VALIDATIONS=8
        ;;
    *)
        echo "Unknown node role: $NODE_ROLE"
        exit 1
        ;;
esac

# 设置日志配置
export LOGGING__LOGLEVEL__DEFAULT=Information
export LOGGING__LOGLEVEL__FLOWCUSTOMV1=Debug
export LOGGING__CONSOLE__INCLUDESCOPES=true

# 设置节点发现配置
export NODEDISCOVERY__NODEID=$NODE_ID
export NODEDISCOVERY__NODENAME="${NODE_ROLE}Node-${NODE_ID}"
export NODEDISCOVERY__NODEROLE=$NODE_ROLE
export NODEDISCOVERY__REGION=$REGION
export NODEDISCOVERY__DATACENTER=$DATACENTER
export NODEDISCOVERY__HEARTBEATINTERVALMS=10000
export NODEDISCOVERY__NODEREGISTRATIONTIMEOUTMS=30000

# NATS配置通过环境变量已设置，无需额外处理
# export NATS__SERVERS 已通过Docker环境变量设置
# export NATS__CONNECTIONNAME 已通过Docker环境变量设置

# 设置任务分发配置（仅Master节点）
if [ "$NODE_ROLE" = "Master" ]; then
    export TASKDISTRIBUTION__MAXCANDIDATENODES=20
    export TASKDISTRIBUTION__MAXCONCURRENTDISTRIBUTIONS=10
    export TASKDISTRIBUTION__MINBALANCESCORE=0.7
    export TASKDISTRIBUTION__AUTOREBALANCINGENABLED=true
fi

# 设置任务跟踪配置
export TASKTRACKING__ENABLEDETAILEDLOGGING=true
export TASKTRACKING__ENABLEPERFORMANCEMONITORING=true
export TASKTRACKING__MAXRECENTCOMPLETEDTASKS=1000

# 创建启动延迟（避免所有节点同时启动）
node_number=$(echo $NODE_ID | grep -o '[0-9]*$')
startup_delay=$((node_number * 5))
echo "Startup delay: ${startup_delay}s"
sleep $startup_delay

# 显示最终配置
echo "=========================================="
echo "Final Configuration:"
echo "ASPNETCORE_ENVIRONMENT: $ASPNETCORE_ENVIRONMENT"
echo "ASPNETCORE_URLS: $ASPNETCORE_URLS"
echo "NODE_ROLE: $NODE_ROLE"
echo "NODE_ID: $NODE_ID"
echo "REGION: $REGION"
echo "NATS Configuration: Environment variables set"
echo "=========================================="

# 启动应用
echo "Starting FlowCustomV1 ${NODE_ROLE} Node..."
exec dotnet FlowCustomV1.Api.dll
