#!/usr/bin/env python3
"""
工作流测试数据生成脚本
清空数据库并生成50个测试工作流，覆盖所有16种节点类型
"""

import json
import requests
import time
import random
from typing import List, Dict, Any

# API基础URL
API_BASE_URL = "http://localhost:5000/api"

# 16种节点类型定义
NODE_TYPES = [
    {"type": "start", "name": "开始", "category": "基础控制"},
    {"type": "end", "name": "结束", "category": "基础控制"},
    {"type": "task", "name": "任务", "category": "基础控制"},
    {"type": "decision", "name": "决策", "category": "基础控制"},
    {"type": "parallel", "name": "并行", "category": "基础控制"},
    {"type": "merge", "name": "合并", "category": "基础控制"},
    {"type": "loop", "name": "循环", "category": "基础控制"},
    {"type": "delay", "name": "延时", "category": "基础控制"},
    {"type": "http", "name": "HTTP请求", "category": "检查器"},
    {"type": "database", "name": "数据库", "category": "检查器"},
    {"type": "email", "name": "邮件", "category": "检查器"},
    {"type": "file", "name": "文件", "category": "检查器"},
    {"type": "script", "name": "脚本", "category": "检查器"},
    {"type": "webhook", "name": "Webhook", "category": "检查器"},
    {"type": "timer", "name": "定时器", "category": "检查器"},
    {"type": "condition", "name": "条件", "category": "检查器"}
]

def clear_database():
    """清空数据库中的测试数据"""
    print("🧹 清空数据库测试数据...")

    try:
        # 删除所有工作流定义
        response = requests.get(f"{API_BASE_URL}/workflows")
        if response.status_code == 200:
            workflows = response.json()
            # 处理不同的响应格式
            if isinstance(workflows, dict) and 'data' in workflows:
                workflows = workflows['data']
            elif not isinstance(workflows, list):
                workflows = []

            for workflow in workflows:
                workflow_id = workflow.get('workflowId') or workflow.get('id')
                workflow_name = workflow.get('name', 'Unknown')
                if workflow_id:
                    delete_response = requests.delete(f"{API_BASE_URL}/workflows/{workflow_id}")
                    if delete_response.status_code in [200, 204]:
                        print(f"✅ 删除工作流: {workflow_name}")
                    else:
                        print(f"⚠️ 删除工作流失败: {workflow_name}")

        print("✅ 数据库清理完成")
        return True
    except Exception as e:
        print(f"❌ 清理数据库失败: {e}")
        return False

def generate_node_id():
    """生成节点ID"""
    return f"node_{random.randint(1000, 9999)}"

def create_basic_workflow(name: str, description: str, node_types: List[str]) -> Dict[str, Any]:
    """创建基础工作流结构"""
    nodes = []
    edges = []
    
    # 添加开始节点
    start_node = {
        "id": "start_node",
        "type": "start",
        "position": {"x": 100, "y": 100},
        "data": {
            "label": "开始",
            "nodeType": "start",
            "config": {}
        }
    }
    nodes.append(start_node)
    
    # 添加指定类型的节点
    prev_node_id = "start_node"
    x_pos = 300
    
    for i, node_type in enumerate(node_types):
        node_id = f"{node_type}_{i}"
        node_info = next((n for n in NODE_TYPES if n["type"] == node_type), None)
        
        if node_info:
            node = {
                "id": node_id,
                "type": node_type,
                "position": {"x": x_pos, "y": 100 + (i * 150)},
                "data": {
                    "label": node_info["name"],
                    "nodeType": node_type,
                    "config": generate_node_config(node_type)
                }
            }
            nodes.append(node)
            
            # 添加连接
            edge = {
                "id": f"edge_{prev_node_id}_{node_id}",
                "source": prev_node_id,
                "target": node_id,
                "type": "smoothstep"
            }
            edges.append(edge)
            
            prev_node_id = node_id
            if i % 3 == 0:  # 每3个节点换行
                x_pos += 200
    
    # 添加结束节点
    end_node = {
        "id": "end_node",
        "type": "end",
        "position": {"x": x_pos + 200, "y": 100},
        "data": {
            "label": "结束",
            "nodeType": "end",
            "config": {}
        }
    }
    nodes.append(end_node)
    
    # 连接到结束节点
    edge = {
        "id": f"edge_{prev_node_id}_end_node",
        "source": prev_node_id,
        "target": "end_node",
        "type": "smoothstep"
    }
    edges.append(edge)
    
    return {
        "workflowId": "",  # 让API自动生成
        "name": name,
        "description": description,
        "version": "1.0.0",
        "author": "测试脚本",
        "isActive": True,
        "publishStatus": "Draft",
        "nodes": nodes,
        "connections": edges,
        "inputParameters": [],
        "outputParameters": [],
        "configuration": {},
        "tags": ["测试", "自动生成"],
        "metadata": {
            "createdBy": "测试脚本",
            "category": "测试工作流",
            "nodeCount": len(nodes),
            "edgeCount": len(edges)
        }
    }

def generate_node_config(node_type: str) -> Dict[str, Any]:
    """为不同节点类型生成配置"""
    configs = {
        "task": {
            "taskName": f"执行任务_{random.randint(1, 100)}",
            "timeout": random.randint(30, 300),
            "retryCount": random.randint(1, 3)
        },
        "decision": {
            "condition": f"data.value > {random.randint(1, 100)}",
            "trueLabel": "是",
            "falseLabel": "否"
        },
        "delay": {
            "duration": random.randint(1, 60),
            "unit": random.choice(["seconds", "minutes"])
        },
        "http": {
            "url": f"https://api.example.com/endpoint_{random.randint(1, 10)}",
            "method": random.choice(["GET", "POST", "PUT"]),
            "timeout": random.randint(5, 30)
        },
        "database": {
            "query": f"SELECT * FROM table_{random.randint(1, 5)} WHERE id = ?",
            "parameters": ["${input.id}"]
        },
        "email": {
            "to": "<EMAIL>",
            "subject": f"工作流通知_{random.randint(1, 100)}",
            "template": "default"
        },
        "script": {
            "language": random.choice(["javascript", "python", "csharp"]),
            "code": f"// 脚本代码_{random.randint(1, 100)}\nreturn input.value * 2;"
        },
        "webhook": {
            "url": f"https://webhook.example.com/endpoint_{random.randint(1, 10)}",
            "method": "POST",
            "headers": {"Content-Type": "application/json"}
        },
        "timer": {
            "schedule": f"0 {random.randint(0, 23)} * * *",
            "timezone": "Asia/Shanghai"
        },
        "condition": {
            "expression": f"input.status == '{random.choice(['active', 'pending', 'completed'])}'"
        }
    }
    
    return configs.get(node_type, {})

def create_test_workflows() -> List[Dict[str, Any]]:
    """创建50个测试工作流"""
    workflows = []
    
    # 1. 单节点类型测试工作流 (16个)
    for node_type_info in NODE_TYPES:
        if node_type_info["type"] not in ["start", "end"]:  # 跳过开始和结束节点
            workflow = create_basic_workflow(
                f"单节点测试-{node_type_info['name']}",
                f"测试{node_type_info['name']}节点的基本功能",
                [node_type_info["type"]]
            )
            workflows.append(workflow)
    
    # 2. 组合节点测试工作流 (20个)
    combinations = [
        (["task", "decision", "task"], "任务决策流程"),
        (["http", "database", "email"], "数据处理通知流程"),
        (["timer", "condition", "webhook"], "定时条件触发流程"),
        (["parallel", "task", "task", "merge"], "并行任务处理流程"),
        (["loop", "script", "condition"], "循环脚本处理流程"),
        (["delay", "http", "decision", "email"], "延时HTTP决策通知流程"),
        (["file", "script", "database"], "文件处理存储流程"),
        (["webhook", "condition", "parallel", "task", "task"], "Webhook并行处理流程"),
        (["timer", "http", "decision", "database", "email"], "定时数据检查流程"),
        (["task", "parallel", "http", "database", "merge", "email"], "复杂数据处理流程"),
        (["condition", "loop", "script", "http"], "条件循环HTTP流程"),
        (["delay", "file", "script", "webhook"], "延时文件处理流程"),
        (["parallel", "http", "http", "merge", "decision"], "并行HTTP决策流程"),
        (["timer", "database", "condition", "email", "webhook"], "定时数据监控流程"),
        (["task", "delay", "http", "script", "database"], "任务延时处理流程"),
        (["loop", "condition", "parallel", "task", "task"], "循环并行任务流程"),
        (["webhook", "script", "decision", "email", "database"], "Webhook脚本决策流程"),
        (["file", "condition", "http", "delay", "email"], "文件条件处理流程"),
        (["timer", "parallel", "database", "database", "merge"], "定时并行数据流程"),
        (["task", "script", "condition", "loop", "webhook"], "任务脚本循环流程")
    ]
    
    for i, (node_types, desc) in enumerate(combinations):
        workflow = create_basic_workflow(
            f"组合测试-{desc}",
            f"测试{desc}的组合功能",
            node_types
        )
        workflows.append(workflow)
    
    # 3. 复杂流程测试工作流 (14个)
    complex_flows = [
        (["timer", "http", "decision", "parallel", "database", "email", "merge", "webhook"], "完整业务流程1"),
        (["webhook", "condition", "loop", "task", "script", "http", "decision", "email"], "完整业务流程2"),
        (["file", "script", "parallel", "database", "http", "merge", "condition", "webhook"], "完整业务流程3"),
        (["delay", "timer", "http", "decision", "task", "database", "email", "script"], "完整业务流程4"),
        (["parallel", "http", "database", "script", "merge", "condition", "loop", "webhook"], "完整业务流程5"),
        (["timer", "condition", "parallel", "task", "file", "script", "merge", "email"], "完整业务流程6"),
        (["webhook", "delay", "http", "decision", "database", "script", "condition", "email"], "完整业务流程7"),
        (["loop", "task", "parallel", "http", "database", "merge", "decision", "webhook"], "完整业务流程8"),
        (["file", "condition", "delay", "script", "http", "database", "email", "timer"], "完整业务流程9"),
        (["parallel", "webhook", "task", "script", "merge", "condition", "database", "email"], "完整业务流程10"),
        (["timer", "http", "loop", "decision", "task", "database", "script", "webhook"], "完整业务流程11"),
        (["condition", "parallel", "file", "http", "database", "merge", "email", "delay"], "完整业务流程12"),
        (["webhook", "script", "decision", "loop", "task", "http", "database", "email"], "完整业务流程13"),
        (["delay", "condition", "parallel", "timer", "database", "script", "merge", "webhook"], "完整业务流程14")
    ]
    
    for i, (node_types, desc) in enumerate(complex_flows):
        workflow = create_basic_workflow(
            f"复杂流程-{desc}",
            f"测试{desc}的复杂业务逻辑",
            node_types
        )
        workflows.append(workflow)
    
    return workflows

def upload_workflows(workflows: List[Dict[str, Any]]):
    """上传工作流到API"""
    print(f"📤 开始上传 {len(workflows)} 个测试工作流...")
    
    success_count = 0
    for i, workflow in enumerate(workflows, 1):
        try:
            response = requests.post(
                f"{API_BASE_URL}/workflows",
                json=workflow,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code in [200, 201]:
                print(f"✅ [{i:2d}/50] 上传成功: {workflow['name']}")
                success_count += 1
            else:
                print(f"❌ [{i:2d}/50] 上传失败: {workflow['name']} - {response.status_code}")
                print(f"    错误信息: {response.text}")
        
        except Exception as e:
            print(f"❌ [{i:2d}/50] 上传异常: {workflow['name']} - {e}")
        
        # 避免请求过快
        time.sleep(0.1)
    
    print(f"\n📊 上传完成: {success_count}/50 个工作流上传成功")
    return success_count

def main():
    """主函数"""
    print("🚀 开始生成工作流测试数据...")
    print("=" * 50)
    
    # 1. 清空数据库
    if not clear_database():
        print("❌ 数据库清理失败，退出")
        return
    
    print()
    
    # 2. 生成测试工作流
    print("🏗️ 生成测试工作流...")
    workflows = create_test_workflows()
    print(f"✅ 生成了 {len(workflows)} 个测试工作流")
    
    print()
    
    # 3. 上传工作流
    success_count = upload_workflows(workflows)
    
    print()
    print("=" * 50)
    if success_count == 50:
        print("🎉 所有测试工作流生成完成！")
        print("📋 工作流覆盖情况:")
        print("   - 单节点测试: 14个工作流")
        print("   - 组合节点测试: 20个工作流") 
        print("   - 复杂流程测试: 14个工作流")
        print("   - 总计: 50个工作流，覆盖所有16种节点类型")
    else:
        print(f"⚠️ 部分工作流生成失败: {success_count}/50")
    
    print("\n🌐 可以访问 http://localhost:3000/workflow/list 查看生成的工作流")

if __name__ == "__main__":
    main()
