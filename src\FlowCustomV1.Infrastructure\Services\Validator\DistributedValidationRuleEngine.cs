using System.Collections.Concurrent;
using FlowCustomV1.Core.Interfaces.Validator;
using FlowCustomV1.Core.Interfaces;
using FlowCustomV1.Core.Models.Workflow;
using Microsoft.Extensions.Logging;
using ValidatorValidationContext = FlowCustomV1.Core.Interfaces.Validator.ValidationContext;
using CoreValidationContext = FlowCustomV1.Core.Interfaces.ValidationContext;
using CoreValidationResult = FlowCustomV1.Core.Models.Workflow.ValidationResult;

namespace FlowCustomV1.Infrastructure.Services.Validator;

/// <summary>
/// 分布式验证规则引擎实现
/// 支持跨节点的验证规则管理和执行
/// </summary>
public class DistributedValidationRuleEngine : IDistributedValidationRuleEngine
{
    private readonly ILogger<DistributedValidationRuleEngine> _logger;
    private readonly ConcurrentDictionary<string, IDistributedValidationRule> _rules;
    private readonly ConcurrentDictionary<string, RuleExecutionStatistics> _statistics;
    private readonly object _lockObject = new();

    /// <summary>
    /// 构造函数
    /// </summary>
    public DistributedValidationRuleEngine(ILogger<DistributedValidationRuleEngine> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _rules = new ConcurrentDictionary<string, IDistributedValidationRule>();
        _statistics = new ConcurrentDictionary<string, RuleExecutionStatistics>();
    }

    /// <inheritdoc />
    public async Task<CoreValidationResult> ExecuteRulesAsync(ValidatorValidationContext context, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(context);

        var result = new CoreValidationResult { IsValid = true };
        var applicableRules = GetApplicableRules(context);

        _logger.LogDebug("Executing {Count} validation rules for {ValidationType}", 
            applicableRules.Count, context.ValidationType);

        if (context.Options.EnableParallelExecution && applicableRules.Count > 1)
        {
            // 并行执行规则
            await ExecuteRulesInParallelAsync(applicableRules, context, result, cancellationToken);
        }
        else
        {
            // 串行执行规则
            await ExecuteRulesSequentiallyAsync(applicableRules, context, result, cancellationToken);
        }

        _logger.LogDebug("Validation rules execution completed: {IsValid}, Errors: {ErrorCount}", 
            result.IsValid, result.Errors.Count);

        return result;
    }

    /// <inheritdoc />
    public async Task<bool> RegisterRuleAsync(IDistributedValidationRule rule, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(rule);

        try
        {
            _rules[rule.RuleId] = rule;
            
            // 初始化统计信息
            _statistics[rule.RuleId] = new RuleExecutionStatistics
            {
                RuleId = rule.RuleId
            };

            _logger.LogInformation("Registered validation rule: {RuleId} ({Category})", 
                rule.RuleId, rule.Category);

            await Task.CompletedTask;
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error registering validation rule {RuleId}", rule.RuleId);
            return false;
        }
    }

    /// <inheritdoc />
    public async Task<bool> RemoveRuleAsync(string ruleId, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(ruleId);

        try
        {
            var removed = _rules.TryRemove(ruleId, out _);
            if (removed)
            {
                _statistics.TryRemove(ruleId, out _);
                _logger.LogInformation("Removed validation rule: {RuleId}", ruleId);
            }

            await Task.CompletedTask;
            return removed;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing validation rule {RuleId}", ruleId);
            return false;
        }
    }

    /// <inheritdoc />
    public async Task<IReadOnlyList<IDistributedValidationRule>> GetAllRulesAsync(CancellationToken cancellationToken = default)
    {
        await Task.CompletedTask;
        return _rules.Values.ToList();
    }

    /// <inheritdoc />
    public async Task<IReadOnlyList<IDistributedValidationRule>> GetRulesByCategoryAsync(ValidationRuleCategory category, CancellationToken cancellationToken = default)
    {
        await Task.CompletedTask;
        return _rules.Values.Where(r => r.Category == category).ToList();
    }

    /// <inheritdoc />
    public async Task SyncRulesToNodesAsync(CancellationToken cancellationToken = default)
    {
        // TODO: 实现规则同步到其他节点的逻辑
        _logger.LogDebug("Syncing rules to other nodes (not implemented yet)");
        await Task.CompletedTask;
    }

    /// <inheritdoc />
    public async Task SyncRulesFromNodesAsync(CancellationToken cancellationToken = default)
    {
        // TODO: 实现从其他节点同步规则的逻辑
        _logger.LogDebug("Syncing rules from other nodes (not implemented yet)");
        await Task.CompletedTask;
    }

    /// <inheritdoc />
    public async Task<bool> EnableRuleAsync(string ruleId, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(ruleId);

        if (_rules.TryGetValue(ruleId, out var rule))
        {
            rule.IsEnabled = true;
            _logger.LogInformation("Enabled validation rule: {RuleId}", ruleId);
            await Task.CompletedTask;
            return true;
        }

        return false;
    }

    /// <inheritdoc />
    public async Task<bool> DisableRuleAsync(string ruleId, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(ruleId);

        if (_rules.TryGetValue(ruleId, out var rule))
        {
            rule.IsEnabled = false;
            _logger.LogInformation("Disabled validation rule: {RuleId}", ruleId);
            await Task.CompletedTask;
            return true;
        }

        return false;
    }

    /// <inheritdoc />
    public async Task<RuleExecutionStatistics> GetRuleStatisticsAsync(string ruleId, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(ruleId);

        await Task.CompletedTask;
        return _statistics.TryGetValue(ruleId, out var stats) 
            ? stats 
            : new RuleExecutionStatistics { RuleId = ruleId };
    }

    /// <summary>
    /// 获取适用于当前验证上下文的规则
    /// </summary>
    /// <param name="context">验证上下文</param>
    /// <returns>适用的规则列表</returns>
    private List<IDistributedValidationRule> GetApplicableRules(ValidatorValidationContext context)
    {
        var applicableRules = new List<IDistributedValidationRule>();

        foreach (var rule in _rules.Values)
        {
            if (!rule.IsEnabled)
                continue;

            // 检查规则类别是否被跳过
            if (context.Options.SkippedCategories.Contains(rule.Category))
                continue;

            // 检查节点类型支持
            if (context.NodeDefinition != null && 
                rule.SupportedNodeTypes.Any() && 
                !rule.SupportedNodeTypes.Contains(context.NodeDefinition.NodeType))
                continue;

            applicableRules.Add(rule);
        }

        // 按优先级排序
        return applicableRules.OrderByDescending(r => r.Priority).ToList();
    }

    /// <summary>
    /// 并行执行验证规则
    /// </summary>
    private async Task ExecuteRulesInParallelAsync(
        List<IDistributedValidationRule> rules,
        ValidatorValidationContext context,
        CoreValidationResult result,
        CancellationToken cancellationToken)
    {
        var parallelOptions = new ParallelOptions
        {
            MaxDegreeOfParallelism = Math.Min(context.Options.MaxParallelism, rules.Count),
            CancellationToken = cancellationToken
        };

        var tasks = rules
            .Where(r => r.SupportsParallelExecution)
            .Select(rule => ExecuteSingleRuleAsync(rule, context, cancellationToken))
            .ToList();

        // 串行执行不支持并行的规则
        var sequentialRules = rules.Where(r => !r.SupportsParallelExecution).ToList();
        if (sequentialRules.Any())
        {
            tasks.Add(Task.Run(async () =>
            {
                var sequentialResult = new CoreValidationResult { IsValid = true };
                await ExecuteRulesSequentiallyAsync(sequentialRules, context, sequentialResult, cancellationToken);
                return sequentialResult;
            }));
        }

        var results = await Task.WhenAll(tasks);

        // 合并结果
        foreach (var ruleResult in results)
        {
            if (!ruleResult.IsValid)
            {
                result.IsValid = false;
                result.Errors.AddRange(ruleResult.Errors);
            }
        }
    }

    /// <summary>
    /// 串行执行验证规则
    /// </summary>
    private async Task ExecuteRulesSequentiallyAsync(
        List<IDistributedValidationRule> rules,
        ValidatorValidationContext context,
        CoreValidationResult result,
        CancellationToken cancellationToken)
    {
        foreach (var rule in rules)
        {
            var ruleResult = await ExecuteSingleRuleAsync(rule, context, cancellationToken);
            if (!ruleResult.IsValid)
            {
                result.IsValid = false;
                result.Errors.AddRange(ruleResult.Errors);
            }
        }
    }

    /// <summary>
    /// 执行单个验证规则
    /// </summary>
    private async Task<CoreValidationResult> ExecuteSingleRuleAsync(
        IDistributedValidationRule rule,
        ValidatorValidationContext context,
        CancellationToken cancellationToken)
    {
        var startTime = DateTime.UtcNow;
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        try
        {
            _logger.LogDebug("Executing validation rule: {RuleId}", rule.RuleId);

            // 创建适配的验证上下文
            var adaptedContext = new CoreValidationContext
            {
                Target = context.WorkflowDefinition ?? (object?)context.NodeDefinition ?? new object(),
                ValidationType = context.ValidationType.ToString(),
                Data = new Dictionary<string, object>(context.Parameters)
            };

            // 添加元数据
            foreach (var kvp in context.Metadata)
            {
                adaptedContext.Data[kvp.Key] = kvp.Value;
            }

            var result = await rule.ValidateAsync(adaptedContext, cancellationToken);

            stopwatch.Stop();
            UpdateRuleStatistics(rule.RuleId, true, stopwatch.ElapsedMilliseconds);

            return result;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            UpdateRuleStatistics(rule.RuleId, false, stopwatch.ElapsedMilliseconds);

            _logger.LogError(ex, "Error executing validation rule {RuleId}", rule.RuleId);

            return new CoreValidationResult
            {
                IsValid = false,
                Errors = new List<string> { $"Rule '{rule.RuleId}' execution failed: {ex.Message}" },
                ValidationErrors = new List<ValidationError>
                {
                    new ValidationError
                    {
                        Code = "RULE_EXECUTION_ERROR",
                        Message = $"Rule '{rule.RuleId}' execution failed: {ex.Message}",
                        Field = rule.RuleId
                    }
                }
            };
        }
    }

    /// <summary>
    /// 更新规则执行统计信息
    /// </summary>
    private void UpdateRuleStatistics(string ruleId, bool success, long durationMs)
    {
        if (_statistics.TryGetValue(ruleId, out var stats))
        {
            lock (_lockObject)
            {
                stats.TotalExecutions++;
                stats.LastExecutionTime = DateTime.UtcNow;
                stats.LastExecutionResult = success;

                if (success)
                {
                    stats.SuccessfulExecutions++;
                }
                else
                {
                    stats.FailedExecutions++;
                }

                // 更新执行时间统计
                if (stats.MinExecutionTimeMs == 0 || durationMs < stats.MinExecutionTimeMs)
                {
                    stats.MinExecutionTimeMs = durationMs;
                }

                if (durationMs > stats.MaxExecutionTimeMs)
                {
                    stats.MaxExecutionTimeMs = durationMs;
                }

                var totalTime = stats.AverageExecutionTimeMs * (stats.TotalExecutions - 1) + durationMs;
                stats.AverageExecutionTimeMs = totalTime / stats.TotalExecutions;
            }
        }
    }

    /// <inheritdoc />
    public async Task AddRuleAsync(ValidationRule rule, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(rule);

        // 创建分布式验证规则适配器
        var distributedRule = new ValidationRuleAdapter(rule);

        var success = await RegisterRuleAsync(distributedRule, cancellationToken);
        if (!success)
        {
            throw new InvalidOperationException($"Failed to add validation rule: {rule.RuleId}");
        }

        _logger.LogInformation("Added validation rule: {RuleId}", rule.RuleId);
    }

    /// <inheritdoc />
    public async Task<bool> UpdateRuleAsync(string ruleId, ValidationRule rule, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(ruleId);
        ArgumentNullException.ThrowIfNull(rule);

        if (!_rules.ContainsKey(ruleId))
        {
            return false;
        }

        // 移除旧规则
        await RemoveRuleAsync(ruleId, cancellationToken);

        // 添加新规则
        rule.RuleId = ruleId; // 确保ID一致
        await AddRuleAsync(rule, cancellationToken);

        _logger.LogInformation("Updated validation rule: {RuleId}", ruleId);
        return true;
    }

    /// <inheritdoc />
    public async Task<bool> SetRuleEnabledAsync(string ruleId, bool enabled, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(ruleId);

        if (_rules.TryGetValue(ruleId, out var rule))
        {
            rule.IsEnabled = enabled;
            rule.UpdatedAt = DateTime.UtcNow;

            _logger.LogInformation("Set rule {RuleId} enabled state to: {Enabled}", ruleId, enabled);
            return true;
        }

        await Task.CompletedTask;
        return false;
    }
}

/// <summary>
/// 验证规则适配器，将ValidationRule适配为IDistributedValidationRule
/// </summary>
internal class ValidationRuleAdapter : IDistributedValidationRule
{
    private readonly ValidationRule _rule;

    public ValidationRuleAdapter(ValidationRule rule)
    {
        _rule = rule ?? throw new ArgumentNullException(nameof(rule));
    }

    public string RuleId => _rule.RuleId;
    public string RuleName => _rule.Name;
    public string Description => _rule.Description;
    public string Version => _rule.Version;
    public ValidationRuleCategory Category => MapRuleType(_rule.RuleType);
    public int Priority => _rule.Priority;
    public bool IsEnabled { get; set; } = true;
    public DateTime CreatedAt => _rule.CreatedAt;
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    public string CreatedBy => _rule.CreatedBy;
    public Dictionary<string, object> Configuration { get; set; } = new();
    public HashSet<string> SupportedNodeTypes => _rule.SupportedNodeTypes;
    public bool SupportsParallelExecution => true;
    public int EstimatedExecutionTimeMs => 100;

    public async Task<CoreValidationResult> ValidateAsync(CoreValidationContext context, CancellationToken cancellationToken = default)
    {
        // 简单的验证实现
        await Task.CompletedTask;

        return new CoreValidationResult
        {
            IsValid = true,
            ValidationErrors = new List<ValidationError>(),
            ValidationWarnings = new List<ValidationWarning>()
        };
    }

    public IDistributedValidationRule Clone()
    {
        return new ValidationRuleAdapter(_rule);
    }

    public string Serialize()
    {
        return System.Text.Json.JsonSerializer.Serialize(_rule);
    }

    public void Deserialize(string data)
    {
        // 反序列化逻辑
        var rule = System.Text.Json.JsonSerializer.Deserialize<ValidationRule>(data);
        if (rule != null)
        {
            // 更新内部规则属性
        }
    }

    private static ValidationRuleCategory MapRuleType(ValidationRuleType ruleType)
    {
        return ruleType switch
        {
            ValidationRuleType.Structural => ValidationRuleCategory.Structure,
            ValidationRuleType.Syntax => ValidationRuleCategory.Syntax,
            ValidationRuleType.Semantic => ValidationRuleCategory.Semantic,
            ValidationRuleType.Performance => ValidationRuleCategory.Performance,
            ValidationRuleType.Security => ValidationRuleCategory.Security,
            ValidationRuleType.Business => ValidationRuleCategory.Business,
            ValidationRuleType.Custom => ValidationRuleCategory.Custom,
            _ => ValidationRuleCategory.Custom
        };
    }
}
