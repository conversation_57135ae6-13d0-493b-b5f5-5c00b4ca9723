# FlowCustomV1 多环境配置使用指南

## 📋 概述

FlowCustomV1 v0.0.1.8版本实现了完整的多环境配置系统，支持开发、测试、生产三种环境的独立配置管理。

## 🎯 支持的环境

| 环境 | 配置文件 | 用途 | 特点 |
|------|---------|------|------|
| **Development** | `appsettings.Development.json` | 本地开发 | 详细日志、本地服务、开发优化 |
| **Testing** | `appsettings.Testing.json` | Docker集群测试 | 端口隔离、集群配置、测试优化 |
| **Production** | `appsettings.Production.json` | 生产部署 | 最小日志、生产端口、性能优化 |

## 🚀 启动方式

### 命令行参数方式

```bash
# 开发环境 (默认)
dotnet run --project src/FlowCustomV1.Api -- --environment Development
dotnet run --project src/FlowCustomV1.Api -- --env Development
dotnet run --project src/FlowCustomV1.Api -- -e Development

# 测试环境
dotnet run --project src/FlowCustomV1.Api -- --environment Testing
dotnet run --project src/FlowCustomV1.Api -- --env Testing
dotnet run --project src/FlowCustomV1.Api -- -e Testing

# 生产环境
dotnet run --project src/FlowCustomV1.Api -- --environment Production
dotnet run --project src/FlowCustomV1.Api -- --env Production
dotnet run --project src/FlowCustomV1.Api -- -e Production
```

### 环境变量方式

```bash
# Windows
set ASPNETCORE_ENVIRONMENT=Development
dotnet run --project src/FlowCustomV1.Api

# Linux/macOS
export ASPNETCORE_ENVIRONMENT=Development
dotnet run --project src/FlowCustomV1.Api
```

## 📁 配置文件结构

### 开发环境配置 (`appsettings.Development.json`)

```json
{
  "System": {
    "Network": {
      "HttpPort": 5000,
      "HttpsPort": 5001,
      "BindAddress": "127.0.0.1"
    },
    "Node": {
      "NodeId": "flowcustom-dev-api-001",
      "Roles": ["Api", "Worker", "Designer"]
    }
  },
  "Nats": {
    "Servers": ["nats://localhost:4222"]
  },
  "Database": {
    "ConnectionString": "Server=localhost;Port=3306;Database=flowcustom_dev;..."
  }
}
```

### 测试环境配置 (`appsettings.Testing.json`)

```json
{
  "System": {
    "Network": {
      "HttpPort": 25000,
      "HttpsPort": 25001,
      "BindAddress": "0.0.0.0"
    },
    "Node": {
      "NodeId": "flowcustom-test-api-001",
      "Roles": ["Api", "Worker"]
    }
  },
  "Nats": {
    "Servers": [
      "nats://nats-server-1:24222",
      "nats://nats-server-2:24223", 
      "nats://nats-server-3:24224"
    ]
  },
  "Database": {
    "ConnectionString": "Server=mysql;Port=23306;Database=flowcustom_test;..."
  }
}
```

### 生产环境配置 (`appsettings.Production.json`)

```json
{
  "System": {
    "Network": {
      "HttpPort": 80,
      "HttpsPort": 443,
      "BindAddress": "0.0.0.0"
    },
    "Node": {
      "NodeId": "flowcustom-prod-api-001",
      "Roles": ["Api"]
    }
  },
  "Nats": {
    "Servers": ["TO_BE_CONFIGURED_ON_DEPLOYMENT"]
  },
  "Database": {
    "ConnectionString": "TO_BE_CONFIGURED_ON_DEPLOYMENT"
  }
}
```

## 🐳 Docker集群测试环境

### 启动集群测试环境

```bash
# 进入集群测试目录
cd docker/cluster-test

# 启动完整集群 (8节点)
python start-cluster-test.py start

# 检查集群状态
python start-cluster-test.py status

# 停止集群
python start-cluster-test.py stop

# 清理集群资源
python start-cluster-test.py cleanup
```

### 集群节点配置

| 节点类型 | 容器名称 | 角色 | 端口 |
|---------|---------|------|------|
| API节点 | flowcustom-api-node | Api | 25000 |
| Worker节点 | flowcustom-worker-node | Worker | - |
| Designer节点 | flowcustom-designer-node | Designer | - |
| Validator节点 | flowcustom-validator-node | Validator | - |
| Executor节点 | flowcustom-executor-node | Executor | - |
| Monitor节点 | flowcustom-monitor-node | Monitor | - |
| Scheduler节点 | flowcustom-scheduler-node | Scheduler | - |
| 多角色节点 | flowcustom-multi-node | Api+Worker+Designer | 25001 |

### 基础设施服务

| 服务 | 容器名称 | 端口 | 用途 |
|------|---------|------|------|
| NATS Server 1 | nats-test-server-1 | 24222, 28222 | 消息队列集群 |
| NATS Server 2 | nats-test-server-2 | 24223, 28223 | 消息队列集群 |
| NATS Server 3 | nats-test-server-3 | 24224, 28224 | 消息队列集群 |
| MySQL | mysql-test | 23306 | 数据库 |

## 🧪 配置验证

### 自动化测试

```bash
# 测试开发环境配置
python scripts/test-environment-config.py --environment Development

# 测试测试环境配置  
python scripts/test-environment-config.py --environment Testing

# 测试生产环境配置
python scripts/test-environment-config.py --environment Production
```

### 测试项目

- ✅ **配置文件检查** - 验证所有配置文件存在
- ✅ **项目编译测试** - 确保项目正常编译
- ✅ **配置内容验证** - 检查NATS、数据库、节点、网络配置
- ✅ **环境特性验证** - 验证各环境特定配置

## 🔧 配置自定义

### 添加新环境

1. 创建新的配置文件 `appsettings.{Environment}.json`
2. 在 `Program.cs` 的 `ConfigureEnvironmentSettings` 方法中添加环境映射
3. 更新测试脚本支持新环境

### 修改现有配置

1. 直接编辑对应环境的配置文件
2. 运行配置验证测试确保修改正确
3. 重启应用程序使配置生效

## 📊 端口规划

| 环境 | HTTP | HTTPS | NATS | MySQL | WebSocket | Management |
|------|------|-------|------|-------|-----------|------------|
| **Development** | 5000 | 5001 | 4222 | 3306 | 8080 | 8081 |
| **Testing** | 25000 | 25001 | 24222-24224 | 23306 | 28080 | 28081 |
| **Production** | 80 | 443 | 待配置 | 待配置 | 待配置 | 待配置 |

## 🎯 最佳实践

1. **开发环境** - 用于日常开发，配置本地服务，启用详细日志
2. **测试环境** - 用于集成测试，模拟生产环境，使用Docker集群
3. **生产环境** - 用于生产部署，最小日志，性能优化配置

## 🔍 故障排除

### 常见问题

1. **配置文件不存在** - 检查配置文件路径和名称
2. **端口冲突** - 检查端口是否被占用，测试环境使用+20000端口
3. **NATS连接失败** - 检查NATS服务器是否启动，配置是否正确
4. **数据库连接失败** - 检查数据库服务器状态和连接字符串

### 调试方法

1. 使用配置验证脚本检查配置
2. 查看应用程序启动日志
3. 检查Docker容器状态
4. 验证网络连接和端口访问

## 📝 更新日志

- **v0.0.1.8** (2025-09-07) - 初始实现多环境配置系统
  - 支持Development/Testing/Production三种环境
  - 实现Docker集群测试环境
  - 添加自动化配置验证
