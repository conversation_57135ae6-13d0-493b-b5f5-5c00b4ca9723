using System.Text.Json.Serialization;
using FlowCustomV1.Core.Interfaces.Messaging;
using FlowCustomV1.Core.Models.Designer;
using FlowCustomV1.Core.Models.Workflow;

namespace FlowCustomV1.Core.Models.Messages;

/// <summary>
/// 工作流设计消息基类
/// </summary>
public abstract class WorkflowDesignMessage : IMessage
{
    /// <inheritdoc />
    [JsonPropertyName("messageId")]
    public string MessageId { get; set; } = Guid.NewGuid().ToString();

    /// <inheritdoc />
    [JsonPropertyName("messageType")]
    public abstract string MessageType { get; }

    /// <inheritdoc />
    [JsonPropertyName("senderId")]
    public string SenderId { get; set; } = string.Empty;

    /// <inheritdoc />
    [JsonPropertyName("targetId")]
    public string? TargetId { get; set; }

    /// <inheritdoc />
    [JsonPropertyName("createdAt")]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <inheritdoc />
    [JsonPropertyName("expiresAt")]
    public DateTime? ExpiresAt { get; set; }

    /// <inheritdoc />
    [JsonPropertyName("priority")]
    public MessagePriority Priority { get; set; } = MessagePriority.Normal;

    /// <inheritdoc />
    [JsonPropertyName("payload")]
    public object? Payload { get; set; }

    /// <inheritdoc />
    [JsonPropertyName("metadata")]
    public IReadOnlyDictionary<string, object> Metadata { get; set; } = new Dictionary<string, object>();
}

/// <summary>
/// 工作流CRUD操作消息
/// </summary>
public class WorkflowCrudMessage : WorkflowDesignMessage
{
    /// <inheritdoc />
    public override string MessageType => "workflow.crud";

    /// <summary>
    /// CRUD操作类型
    /// </summary>
    [JsonPropertyName("operation")]
    public CrudOperation Operation { get; set; }

    /// <summary>
    /// 工作流ID
    /// </summary>
    [JsonPropertyName("workflowId")]
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 工作流定义（创建和更新时使用）
    /// </summary>
    [JsonPropertyName("workflowDefinition")]
    public WorkflowDefinition? WorkflowDefinition { get; set; }

    /// <summary>
    /// 查询条件（查询时使用）
    /// </summary>
    [JsonPropertyName("query")]
    public WorkflowQuery? Query { get; set; }

    /// <summary>
    /// 操作者ID
    /// </summary>
    [JsonPropertyName("operatorId")]
    public string OperatorId { get; set; } = string.Empty;

    /// <summary>
    /// 请求ID（用于响应匹配）
    /// </summary>
    [JsonPropertyName("requestId")]
    public string RequestId { get; set; } = Guid.NewGuid().ToString();
}

/// <summary>
/// 工作流CRUD响应消息
/// </summary>
public class WorkflowCrudResponseMessage : WorkflowDesignMessage
{
    /// <inheritdoc />
    public override string MessageType => "workflow.crud.response";

    /// <summary>
    /// 请求ID（用于匹配请求）
    /// </summary>
    [JsonPropertyName("requestId")]
    public string RequestId { get; set; } = string.Empty;

    /// <summary>
    /// 操作是否成功
    /// </summary>
    [JsonPropertyName("success")]
    public bool Success { get; set; }

    /// <summary>
    /// 错误消息（失败时）
    /// </summary>
    [JsonPropertyName("errorMessage")]
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 工作流定义（查询成功时）
    /// </summary>
    [JsonPropertyName("workflow")]
    public WorkflowDefinition? Workflow { get; set; }

    /// <summary>
    /// 工作流列表（列表查询成功时）
    /// </summary>
    [JsonPropertyName("workflows")]
    public List<WorkflowDefinition>? Workflows { get; set; }

    /// <summary>
    /// 总数（分页查询时）
    /// </summary>
    [JsonPropertyName("totalCount")]
    public int TotalCount { get; set; }
}



/// <summary>
/// 模板操作消息
/// </summary>
public class TemplateOperationMessage : WorkflowDesignMessage
{
    /// <inheritdoc />
    public override string MessageType => "template.operation";

    /// <summary>
    /// 操作类型
    /// </summary>
    [JsonPropertyName("operation")]
    public CrudOperation Operation { get; set; }

    /// <summary>
    /// 模板ID
    /// </summary>
    [JsonPropertyName("templateId")]
    public string TemplateId { get; set; } = string.Empty;

    /// <summary>
    /// 模板定义（创建和更新时）
    /// </summary>
    [JsonPropertyName("template")]
    public WorkflowTemplate? Template { get; set; }

    /// <summary>
    /// 操作者ID
    /// </summary>
    [JsonPropertyName("operatorId")]
    public string OperatorId { get; set; } = string.Empty;

    /// <summary>
    /// 请求ID
    /// </summary>
    [JsonPropertyName("requestId")]
    public string RequestId { get; set; } = Guid.NewGuid().ToString();
}

/// <summary>
/// 模板操作响应消息
/// </summary>
public class TemplateOperationResponseMessage : WorkflowDesignMessage
{
    /// <inheritdoc />
    public override string MessageType => "template.operation.response";

    /// <summary>
    /// 请求ID
    /// </summary>
    [JsonPropertyName("requestId")]
    public string RequestId { get; set; } = string.Empty;

    /// <summary>
    /// 操作是否成功
    /// </summary>
    [JsonPropertyName("success")]
    public bool Success { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    [JsonPropertyName("errorMessage")]
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 模板信息
    /// </summary>
    [JsonPropertyName("template")]
    public WorkflowTemplate? Template { get; set; }

    /// <summary>
    /// 模板列表
    /// </summary>
    [JsonPropertyName("templates")]
    public List<WorkflowTemplate>? Templates { get; set; }
}

/// <summary>
/// 版本控制消息
/// </summary>
public class VersionControlMessage : WorkflowDesignMessage
{
    /// <inheritdoc />
    public override string MessageType => "version.control";

    /// <summary>
    /// 操作类型
    /// </summary>
    [JsonPropertyName("operation")]
    public VersionOperation Operation { get; set; }

    /// <summary>
    /// 工作流ID
    /// </summary>
    [JsonPropertyName("workflowId")]
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 版本ID
    /// </summary>
    [JsonPropertyName("versionId")]
    public string? VersionId { get; set; }

    /// <summary>
    /// 版本信息（创建时）
    /// </summary>
    [JsonPropertyName("versionInfo")]
    public WorkflowVersionInfo? VersionInfo { get; set; }

    /// <summary>
    /// 操作者ID
    /// </summary>
    [JsonPropertyName("operatorId")]
    public string OperatorId { get; set; } = string.Empty;

    /// <summary>
    /// 请求ID
    /// </summary>
    [JsonPropertyName("requestId")]
    public string RequestId { get; set; } = Guid.NewGuid().ToString();
}

/// <summary>
/// 版本控制响应消息
/// </summary>
public class VersionControlResponseMessage : WorkflowDesignMessage
{
    /// <inheritdoc />
    public override string MessageType => "version.control.response";

    /// <summary>
    /// 请求ID
    /// </summary>
    [JsonPropertyName("requestId")]
    public string RequestId { get; set; } = string.Empty;

    /// <summary>
    /// 操作是否成功
    /// </summary>
    [JsonPropertyName("success")]
    public bool Success { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    [JsonPropertyName("errorMessage")]
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 版本信息
    /// </summary>
    [JsonPropertyName("version")]
    public WorkflowVersion? Version { get; set; }

    /// <summary>
    /// 版本列表
    /// </summary>
    [JsonPropertyName("versions")]
    public List<WorkflowVersion>? Versions { get; set; }

    /// <summary>
    /// 创建的版本ID
    /// </summary>
    [JsonPropertyName("createdVersionId")]
    public string? CreatedVersionId { get; set; }
}

/// <summary>
/// CRUD操作枚举
/// </summary>
public enum CrudOperation
{
    /// <summary>
    /// 创建
    /// </summary>
    Create,

    /// <summary>
    /// 读取
    /// </summary>
    Read,

    /// <summary>
    /// 更新
    /// </summary>
    Update,

    /// <summary>
    /// 删除
    /// </summary>
    Delete,

    /// <summary>
    /// 列表查询
    /// </summary>
    List
}

/// <summary>
/// 版本操作枚举
/// </summary>
public enum VersionOperation
{
    /// <summary>
    /// 创建版本
    /// </summary>
    Create,

    /// <summary>
    /// 获取版本历史
    /// </summary>
    GetHistory,

    /// <summary>
    /// 获取指定版本
    /// </summary>
    GetVersion,

    /// <summary>
    /// 激活版本
    /// </summary>
    Activate,

    /// <summary>
    /// 发布版本
    /// </summary>
    Release
}
