using System.Text.Json.Serialization;

namespace FlowCustomV1.Core.Models.Cluster;

/// <summary>
/// 集群拓扑信息
/// 描述集群中节点的分布和连接关系
/// </summary>
public class ClusterTopology
{
    /// <summary>
    /// 集群名称
    /// </summary>
    [JsonPropertyName("clusterName")]
    public string ClusterName { get; set; } = string.Empty;

    /// <summary>
    /// 集群中的所有节点
    /// </summary>
    [JsonPropertyName("nodes")]
    public List<NodeInfo> Nodes { get; set; } = new();

    /// <summary>
    /// 节点间的连接关系
    /// </summary>
    [JsonPropertyName("nodeConnections")]
    public List<NodeConnection> NodeConnections { get; set; } = new();

    /// <summary>
    /// 拓扑版本号（用于检测变更）
    /// </summary>
    [JsonPropertyName("topologyVersion")]
    public string TopologyVersion { get; set; } = string.Empty;

    /// <summary>
    /// 最后更新时间
    /// </summary>
    [JsonPropertyName("lastUpdatedAt")]
    public DateTime LastUpdatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 拓扑元数据
    /// </summary>
    [JsonPropertyName("metadata")]
    public Dictionary<string, object> Metadata { get; set; } = new();

    /// <summary>
    /// 获取在线节点数量
    /// </summary>
    [JsonIgnore]
    public int OnlineNodeCount => Nodes.Count(n => n.Status == NodeStatus.Healthy);

    /// <summary>
    /// 获取离线节点数量
    /// </summary>
    [JsonIgnore]
    public int OfflineNodeCount => Nodes.Count(n => n.Status != NodeStatus.Healthy);

    /// <summary>
    /// 获取活跃连接数量
    /// </summary>
    [JsonIgnore]
    public int ActiveConnectionCount => NodeConnections.Count(c => c.IsActive);

    /// <summary>
    /// 按角色分组的节点统计
    /// </summary>
    [JsonPropertyName("nodesByRole")]
    public Dictionary<string, int> NodesByRole
    {
        get
        {
            return Nodes.GroupBy(n => n.Mode.ToString())
                       .ToDictionary(g => g.Key, g => g.Count());
        }
    }

    /// <summary>
    /// 按状态分组的节点统计
    /// </summary>
    [JsonPropertyName("nodesByStatus")]
    public Dictionary<string, int> NodesByStatus
    {
        get
        {
            return Nodes.GroupBy(n => n.Status.ToString())
                       .ToDictionary(g => g.Key, g => g.Count());
        }
    }
}

/// <summary>
/// 节点连接信息
/// 描述两个节点之间的连接关系
/// </summary>
public class NodeConnection
{
    /// <summary>
    /// 源节点ID
    /// </summary>
    [JsonPropertyName("sourceNodeId")]
    public string SourceNodeId { get; set; } = string.Empty;

    /// <summary>
    /// 目标节点ID
    /// </summary>
    [JsonPropertyName("targetNodeId")]
    public string TargetNodeId { get; set; } = string.Empty;

    /// <summary>
    /// 连接类型
    /// </summary>
    [JsonPropertyName("connectionType")]
    public string ConnectionType { get; set; } = string.Empty;

    /// <summary>
    /// 连接是否活跃
    /// </summary>
    [JsonPropertyName("isActive")]
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// 连接质量评分（0-100）
    /// </summary>
    [JsonPropertyName("qualityScore")]
    public double QualityScore { get; set; } = 100.0;

    /// <summary>
    /// 网络延迟（毫秒）
    /// </summary>
    [JsonPropertyName("latencyMs")]
    public double LatencyMs { get; set; } = 0;

    /// <summary>
    /// 带宽（Mbps）
    /// </summary>
    [JsonPropertyName("bandwidthMbps")]
    public double BandwidthMbps { get; set; } = 0;

    /// <summary>
    /// 最后检测时间
    /// </summary>
    [JsonPropertyName("lastSeen")]
    public DateTime LastSeen { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 连接建立时间
    /// </summary>
    [JsonPropertyName("establishedAt")]
    public DateTime EstablishedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 连接元数据
    /// </summary>
    [JsonPropertyName("metadata")]
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// 集群概览信息
/// 提供集群的高级摘要信息
/// </summary>
public class ClusterOverview
{
    /// <summary>
    /// 集群名称
    /// </summary>
    [JsonPropertyName("clusterName")]
    public string ClusterName { get; set; } = string.Empty;

    /// <summary>
    /// 总节点数
    /// </summary>
    [JsonPropertyName("totalNodes")]
    public int TotalNodes { get; set; } = 0;

    /// <summary>
    /// 在线节点数
    /// </summary>
    [JsonPropertyName("onlineNodes")]
    public int OnlineNodes { get; set; } = 0;

    /// <summary>
    /// 离线节点数
    /// </summary>
    [JsonPropertyName("offlineNodes")]
    public int OfflineNodes { get; set; } = 0;

    /// <summary>
    /// 集群健康评分
    /// </summary>
    [JsonPropertyName("clusterHealthScore")]
    public double ClusterHealthScore { get; set; } = 0;

    /// <summary>
    /// 当前节点ID
    /// </summary>
    [JsonPropertyName("currentNodeId")]
    public string CurrentNodeId { get; set; } = string.Empty;

    /// <summary>
    /// 最后更新时间
    /// </summary>
    [JsonPropertyName("lastUpdatedAt")]
    public DateTime LastUpdatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 按模式分组的节点统计
    /// </summary>
    [JsonPropertyName("nodesByMode")]
    public Dictionary<NodeMode, int> NodesByMode { get; set; } = new();

    /// <summary>
    /// 按状态分组的节点统计
    /// </summary>
    [JsonPropertyName("nodesByStatus")]
    public Dictionary<NodeStatus, int> NodesByStatus { get; set; } = new();

    /// <summary>
    /// 平均负载评分
    /// </summary>
    [JsonPropertyName("averageLoadScore")]
    public double AverageLoadScore { get; set; } = 0;

    /// <summary>
    /// 总CPU核心数
    /// </summary>
    [JsonPropertyName("totalCpuCores")]
    public int TotalCpuCores { get; set; } = 0;

    /// <summary>
    /// 总内存（MB）
    /// </summary>
    [JsonPropertyName("totalMemoryMB")]
    public long TotalMemoryMB { get; set; } = 0;

    /// <summary>
    /// 已使用CPU核心数
    /// </summary>
    [JsonPropertyName("usedCpuCores")]
    public int UsedCpuCores { get; set; } = 0;

    /// <summary>
    /// 已使用内存（MB）
    /// </summary>
    [JsonPropertyName("usedMemoryMB")]
    public long UsedMemoryMB { get; set; } = 0;

    /// <summary>
    /// 活跃任务数
    /// </summary>
    [JsonPropertyName("activeTasks")]
    public int ActiveTasks { get; set; } = 0;

    /// <summary>
    /// 队列任务数
    /// </summary>
    [JsonPropertyName("queuedTasks")]
    public int QueuedTasks { get; set; } = 0;

    /// <summary>
    /// CPU使用率百分比
    /// </summary>
    [JsonIgnore]
    public double CpuUsagePercentage => TotalCpuCores > 0 ? (double)UsedCpuCores / TotalCpuCores * 100 : 0;

    /// <summary>
    /// 内存使用率百分比
    /// </summary>
    [JsonIgnore]
    public double MemoryUsagePercentage => TotalMemoryMB > 0 ? (double)UsedMemoryMB / TotalMemoryMB * 100 : 0;

    /// <summary>
    /// 集群是否健康
    /// </summary>
    [JsonIgnore]
    public bool IsHealthy => ClusterHealthScore >= 70 && OfflineNodes == 0;
}

/// <summary>
/// 集群健康状态
/// </summary>
public class ClusterHealthStatus
{
    /// <summary>
    /// 集群是否健康
    /// </summary>
    [JsonPropertyName("isHealthy")]
    public bool IsHealthy { get; set; } = true;

    /// <summary>
    /// 健康评分（0-100）
    /// </summary>
    [JsonPropertyName("healthScore")]
    public double HealthScore { get; set; } = 100;

    /// <summary>
    /// 总节点数
    /// </summary>
    [JsonPropertyName("totalNodes")]
    public int TotalNodes { get; set; } = 0;

    /// <summary>
    /// 健康节点数
    /// </summary>
    [JsonPropertyName("healthyNodes")]
    public int HealthyNodes { get; set; } = 0;

    /// <summary>
    /// 不健康节点数
    /// </summary>
    [JsonPropertyName("unhealthyNodes")]
    public int UnhealthyNodes { get; set; } = 0;

    /// <summary>
    /// 检查时间
    /// </summary>
    [JsonPropertyName("checkTime")]
    public DateTime CheckTime { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 健康问题列表
    /// </summary>
    [JsonPropertyName("issues")]
    public List<string> Issues { get; set; } = new();

    /// <summary>
    /// 健康状态等级
    /// </summary>
    [JsonIgnore]
    public HealthLevel HealthLevel
    {
        get
        {
            if (HealthScore >= 80) return HealthLevel.Excellent;
            if (HealthScore >= 60) return HealthLevel.Good;
            if (HealthScore >= 40) return HealthLevel.Warning;
            return HealthLevel.Critical;
        }
    }
}

/// <summary>
/// 健康等级
/// </summary>
public enum HealthLevel
{
    /// <summary>
    /// 优秀
    /// </summary>
    Excellent = 1,

    /// <summary>
    /// 良好
    /// </summary>
    Good = 2,

    /// <summary>
    /// 警告
    /// </summary>
    Warning = 3,

    /// <summary>
    /// 严重
    /// </summary>
    Critical = 4
}
