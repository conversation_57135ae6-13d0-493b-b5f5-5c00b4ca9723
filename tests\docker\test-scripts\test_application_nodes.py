#!/usr/bin/env python3
"""
FlowCustomV1 v0.0.1.7 应用节点测试
测试FlowCustomV1 API服务和应用层功能
"""

import asyncio
import json
import time
import os
from datetime import datetime
from typing import Dict, Any
import aiohttp

class ApplicationNodeTester:
    def __init__(self):
        self.api_base_url = os.getenv("API_BASE_URL", "http://flowcustom-api:5000")
        self.nats_url = os.getenv("NATS_SERVERS", "nats://nats:4222")
        
        self.test_results = {
            "test_suite": "Application Node Tests",
            "start_time": datetime.utcnow().isoformat(),
            "tests": [],
            "summary": {
                "total": 0,
                "passed": 0,
                "failed": 0,
                "skipped": 0,
                "errors": []
            }
        }

    def log_test_result(self, test_name: str, status: str, details: Dict[str, Any] = None, error: str = None):
        """记录测试结果"""
        test_result = {
            "name": test_name,
            "status": status,
            "timestamp": datetime.utcnow().isoformat(),
            "details": details or {},
            "error": error
        }
        
        self.test_results["tests"].append(test_result)
        self.test_results["summary"]["total"] += 1
        
        if status == "passed":
            self.test_results["summary"]["passed"] += 1
            print(f"✅ {test_name}")
        elif status == "failed":
            self.test_results["summary"]["failed"] += 1
            self.test_results["summary"]["errors"].append(f"{test_name}: {error}")
            print(f"❌ {test_name}: {error}")
        elif status == "skipped":
            self.test_results["summary"]["skipped"] += 1
            print(f"⏭️ {test_name}: 跳过")

    async def test_api_health_check(self):
        """测试API健康检查"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.api_base_url}/health", timeout=10) as response:
                    if response.status == 200:
                        health_data = await response.json()
                        self.log_test_result("API健康检查", "passed", {
                            "status": health_data.get("status", "unknown"),
                            "timestamp": health_data.get("timestamp", "unknown"),
                            "version": health_data.get("version", "unknown")
                        })
                    else:
                        self.log_test_result("API健康检查", "failed", 
                                           error=f"HTTP状态码: {response.status}")
        except Exception as e:
            self.log_test_result("API健康检查", "failed", error=str(e))

    async def test_api_swagger_docs(self):
        """测试API文档"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.api_base_url}/swagger/index.html", timeout=10) as response:
                    if response.status == 200:
                        self.log_test_result("API文档访问", "passed", {
                            "swagger_available": True,
                            "content_type": response.headers.get("content-type", "unknown")
                        })
                    else:
                        self.log_test_result("API文档访问", "failed", 
                                           error=f"HTTP状态码: {response.status}")
        except Exception as e:
            self.log_test_result("API文档访问", "failed", error=str(e))

    async def test_cluster_nodes_api(self):
        """测试集群节点API"""
        try:
            async with aiohttp.ClientSession() as session:
                # 测试获取集群节点列表
                async with session.get(f"{self.api_base_url}/api/cluster/nodes", timeout=10) as response:
                    if response.status == 200:
                        nodes_data = await response.json()
                        self.log_test_result("集群节点列表API", "passed", {
                            "nodes_count": len(nodes_data) if isinstance(nodes_data, list) else 0,
                            "response_type": type(nodes_data).__name__
                        })
                    else:
                        self.log_test_result("集群节点列表API", "failed", 
                                           error=f"HTTP状态码: {response.status}")
        except Exception as e:
            self.log_test_result("集群节点列表API", "failed", error=str(e))

    async def test_workflow_definitions_api(self):
        """测试工作流定义API"""
        try:
            async with aiohttp.ClientSession() as session:
                # 测试获取工作流定义列表
                async with session.get(f"{self.api_base_url}/api/workflows", timeout=10) as response:
                    if response.status == 200:
                        workflows_data = await response.json()
                        self.log_test_result("工作流定义列表API", "passed", {
                            "workflows_count": len(workflows_data) if isinstance(workflows_data, list) else 0,
                            "response_type": type(workflows_data).__name__
                        })
                    else:
                        self.log_test_result("工作流定义列表API", "failed", 
                                           error=f"HTTP状态码: {response.status}")
        except Exception as e:
            self.log_test_result("工作流定义列表API", "failed", error=str(e))

    async def test_workflow_creation(self):
        """测试工作流创建"""
        try:
            test_workflow = {
                "workflowId": "test-app-workflow",
                "name": "应用测试工作流",
                "description": "用于测试应用节点功能的工作流",
                "definition": {
                    "nodes": [
                        {
                            "nodeId": "start",
                            "nodeType": "Start",
                            "name": "开始节点",
                            "position": {"x": 100, "y": 100}
                        },
                        {
                            "nodeId": "task1",
                            "nodeType": "Task",
                            "name": "测试任务",
                            "position": {"x": 300, "y": 100},
                            "configuration": {
                                "taskType": "simple_task",
                                "timeout": 30
                            }
                        },
                        {
                            "nodeId": "end",
                            "nodeType": "End",
                            "name": "结束节点",
                            "position": {"x": 500, "y": 100}
                        }
                    ],
                    "connections": [
                        {"sourceNodeId": "start", "targetNodeId": "task1"},
                        {"sourceNodeId": "task1", "targetNodeId": "end"}
                    ]
                }
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.api_base_url}/api/workflows",
                    json=test_workflow,
                    timeout=30
                ) as response:
                    if response.status in [200, 201]:
                        workflow_data = await response.json()
                        self.log_test_result("工作流创建API", "passed", {
                            "created_workflow_id": workflow_data.get("workflowId", "unknown"),
                            "status": workflow_data.get("status", "unknown")
                        })
                    else:
                        response_text = await response.text()
                        self.log_test_result("工作流创建API", "failed", 
                                           error=f"HTTP状态码: {response.status}, 响应: {response_text}")
        except Exception as e:
            self.log_test_result("工作流创建API", "failed", error=str(e))

    async def test_task_scheduling_api(self):
        """测试任务调度API"""
        try:
            test_task = {
                "taskId": "test-app-task",
                "taskType": "simple_task",
                "inputData": {
                    "message": "Hello from application test",
                    "timestamp": datetime.utcnow().isoformat()
                },
                "priority": 1,
                "timeout": 30
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.api_base_url}/api/tasks/schedule",
                    json=test_task,
                    timeout=30
                ) as response:
                    if response.status in [200, 201, 202]:
                        task_data = await response.json()
                        self.log_test_result("任务调度API", "passed", {
                            "scheduled_task_id": task_data.get("taskId", "unknown"),
                            "status": task_data.get("status", "unknown")
                        })
                    else:
                        response_text = await response.text()
                        self.log_test_result("任务调度API", "failed", 
                                           error=f"HTTP状态码: {response.status}, 响应: {response_text}")
        except Exception as e:
            self.log_test_result("任务调度API", "failed", error=str(e))

    async def test_node_registration(self):
        """测试节点注册功能"""
        try:
            test_node = {
                "nodeId": "test-worker-node",
                "nodeName": "Test Worker Node",
                "nodeRole": "Worker",
                "region": "Test",
                "dataCenter": "TestDC",
                "endpoint": "http://test-worker:5000",
                "capabilities": {
                    "task_execution": True,
                    "data_processing": True
                },
                "resources": {
                    "cpu_cores": 2,
                    "memory_mb": 4096,
                    "max_concurrent_tasks": 10
                }
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.api_base_url}/api/cluster/nodes/register",
                    json=test_node,
                    timeout=30
                ) as response:
                    if response.status in [200, 201]:
                        node_data = await response.json()
                        self.log_test_result("节点注册API", "passed", {
                            "registered_node_id": node_data.get("nodeId", "unknown"),
                            "status": node_data.get("status", "unknown")
                        })
                    else:
                        response_text = await response.text()
                        self.log_test_result("节点注册API", "failed", 
                                           error=f"HTTP状态码: {response.status}, 响应: {response_text}")
        except Exception as e:
            self.log_test_result("节点注册API", "failed", error=str(e))

    async def test_api_error_handling(self):
        """测试API错误处理"""
        try:
            async with aiohttp.ClientSession() as session:
                # 测试不存在的端点
                async with session.get(f"{self.api_base_url}/api/nonexistent", timeout=10) as response:
                    if response.status == 404:
                        self.log_test_result("API错误处理", "passed", {
                            "error_handling": "正确返回404",
                            "status_code": response.status
                        })
                    else:
                        self.log_test_result("API错误处理", "failed", 
                                           error=f"期望404，实际: {response.status}")
        except Exception as e:
            self.log_test_result("API错误处理", "failed", error=str(e))

    async def test_api_performance(self):
        """测试API性能"""
        try:
            async with aiohttp.ClientSession() as session:
                # 测试健康检查端点的响应时间
                start_time = time.time()
                async with session.get(f"{self.api_base_url}/health", timeout=10) as response:
                    response_time = time.time() - start_time
                    
                    if response.status == 200 and response_time < 1.0:  # 1秒内响应
                        self.log_test_result("API性能测试", "passed", {
                            "response_time_seconds": round(response_time, 3),
                            "performance_acceptable": response_time < 1.0
                        })
                    else:
                        self.log_test_result("API性能测试", "failed", 
                                           error=f"响应时间过长: {response_time:.3f}秒")
        except Exception as e:
            self.log_test_result("API性能测试", "failed", error=str(e))

    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始FlowCustomV1应用节点测试")
        print("=" * 50)
        
        # 定义测试列表
        tests = [
            ("API健康检查", self.test_api_health_check),
            ("API文档访问", self.test_api_swagger_docs),
            ("集群节点API", self.test_cluster_nodes_api),
            ("工作流定义API", self.test_workflow_definitions_api),
            ("工作流创建", self.test_workflow_creation),
            ("任务调度API", self.test_task_scheduling_api),
            ("节点注册", self.test_node_registration),
            ("API错误处理", self.test_api_error_handling),
            ("API性能测试", self.test_api_performance)
        ]
        
        # 运行所有测试
        for test_name, test_func in tests:
            try:
                await test_func()
            except Exception as e:
                self.log_test_result(test_name, "failed", error=f"测试执行异常: {str(e)}")
            
            # 测试间隔
            await asyncio.sleep(0.5)
        
        # 完成测试
        self.test_results["end_time"] = datetime.utcnow().isoformat()
        self.test_results["total_duration"] = sum(
            test.get("duration", 0) for test in self.test_results["tests"]
        )
        
        # 输出摘要
        summary = self.test_results["summary"]
        print("\n" + "=" * 50)
        print(f"📊 测试完成: {summary['passed']}/{summary['total']} 通过")
        print(f"⏭️ 跳过: {summary['skipped']}")
        
        if summary["failed"] > 0:
            print("❌ 失败的测试:")
            for error in summary["errors"]:
                print(f"  - {error}")
        
        # 保存结果
        os.makedirs("/app/test-results", exist_ok=True)
        with open("/app/test-results/application_nodes_test.json", 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, indent=2, ensure_ascii=False)
        
        print(f"\n📁 测试结果已保存到: /app/test-results/application_nodes_test.json")
        
        return summary["failed"] == 0

async def main():
    tester = ApplicationNodeTester()
    success = await tester.run_all_tests()
    
    if success:
        print("\n🎉 所有应用节点测试通过！")
    else:
        print("\n⚠️ 部分应用节点测试失败，请检查应用配置。")
    
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
