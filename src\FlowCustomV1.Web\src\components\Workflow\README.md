# 工作流节点组件

基于 `@antv/x6-react-shape` 的自定义工作流节点组件，提供美观的视觉效果和良好的用户体验。

## 组件概述

### BaseNode - 基础矩形节点
用于任务、HTTP请求、数据处理等功能节点。

**特点：**
- 矩形设计，左侧彩色条标识
- 支持图标和文本描述
- 悬停和点击交互效果
- 状态指示器

### CircleNode - 圆形节点
用于开始和结束节点。

**特点：**
- 圆形设计，简洁明了
- 图标和文字居中显示
- 不同颜色区分开始/结束

### DiamondNode - 菱形节点
用于决策和条件判断节点。

**特点：**
- 菱形设计，突出决策特性
- 45度旋转的视觉效果
- 内容反向旋转保持可读性

## 节点类型和样式

| 类型 | 颜色 | 图标 | 用途 |
|------|------|------|------|
| start | 绿色 | PlayCircleOutlined | 流程开始 |
| end | 红色 | StopOutlined | 流程结束 |
| task | 蓝色 | SettingOutlined | 通用任务 |
| timer | 橙色 | ClockCircleOutlined | 定时触发 |
| http | 紫色 | ApiOutlined | HTTP请求 |
| database | 青色 | DatabaseOutlined | 数据处理 |
| decision | 橙红色 | BranchesOutlined | 条件判断 |
| trigger | 绿黄色 | ThunderboltOutlined | 事件触发 |
| email | 粉色 | MailOutlined | 邮件发送 |
| message | 紫色 | MessageOutlined | 消息发送 |

## 使用方法

### 1. 注册节点
```typescript
import { register } from '@antv/x6-react-shape';
import { BaseNode, CircleNode, DiamondNode } from '@/components/Workflow/WorkflowNodes';

// 注册基础任务节点
register({
  shape: 'task-node',
  width: 140,
  height: 40,
  component: BaseNode,
  ports: { /* 端口配置 */ },
});

// 注册开始节点
register({
  shape: 'start-node',
  width: 60,
  height: 60,
  component: CircleNode,
  ports: { /* 端口配置 */ },
});

// 注册决策节点
register({
  shape: 'decision-node',
  width: 80,
  height: 80,
  component: DiamondNode,
  ports: { /* 端口配置 */ },
});
```

### 2. 创建节点
```typescript
const nodeData = {
  label: '数据处理',
  type: 'database',
  description: '处理用户数据',
  status: 'success'
};

const node = graph.addNode({
  shape: 'task-node',
  x: 100,
  y: 100,
  data: nodeData,
});
```

### 3. 节点数据结构
```typescript
interface NodeData {
  label: string;           // 节点标题
  type: string;           // 节点类型（决定样式）
  description?: string;   // 节点描述
  status?: 'success' | 'error' | 'warning' | 'processing' | 'default';
}
```

## 交互功能

### 悬停效果
- 节点放大 (scale: 1.02-1.05)
- 阴影加深
- 平滑过渡动画

### 点击事件
- 控制台输出节点信息
- 可扩展自定义处理逻辑

### 选中状态
- 蓝色边框高亮
- 通过CSS类 `.selected` 控制

## 样式定制

### 全局样式
在 `global.css` 中定义：
```css
.workflow-node {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto;
  user-select: none;
}

.workflow-node:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}
```

### 节点样式配置
在 `WorkflowNodes.tsx` 中的 `nodeStyles` 对象中配置：
```typescript
const nodeStyles = {
  task: {
    backgroundColor: '#f0f9ff',
    borderColor: '#1890ff',
    color: '#1890ff',
    icon: SettingOutlined,
  },
  // ... 其他类型
};
```

## 扩展指南

### 添加新节点类型
1. 在 `nodeStyles` 中添加样式配置
2. 在 `getNodeDescription` 中添加描述
3. 在 `createNode` 函数中添加类型映射
4. 更新类型定义和文档

### 自定义节点组件
继承 `BaseNodeProps` 接口，实现自定义渲染逻辑：
```typescript
export const CustomNode: React.FC<BaseNodeProps> = ({ node, data }) => {
  // 自定义渲染逻辑
  return <div>...</div>;
};
```

## 最佳实践

1. **保持一致性** - 使用统一的颜色和图标规范
2. **响应式设计** - 确保节点在不同尺寸下正常显示
3. **性能优化** - 避免在渲染函数中进行复杂计算
4. **可访问性** - 提供适当的键盘导航和屏幕阅读器支持
5. **测试覆盖** - 为节点组件编写单元测试

## 依赖项

- `@antv/x6` - 图形编辑器核心
- `@antv/x6-react-shape` - React节点支持
- `@ant-design/icons` - 图标库
- `antd` - UI组件库
