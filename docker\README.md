# FlowCustomV1 Docker环境配置

## 📋 环境结构总览

```
docker/
├── development/          # 开发环境 (默认端口)
│   ├── docker-compose.yml
│   ├── start-dev-env.py
│   ├── Dockerfile.*
│   ├── nats/            # NATS配置文件
│   │   └── nats-1.conf
│   └── mysql/           # MySQL配置文件
│       └── init/
│           └── 01-init-database.sql
├── testing/             # 测试环境 (端口+20000)
│   ├── docker-compose.yml
│   ├── start-test-cluster.py
│   ├── Dockerfile.*
│   ├── nats/            # NATS集群配置文件
│   │   ├── nats-1.conf
│   │   ├── nats-2.conf
│   │   └── nats-3.conf
│   └── mysql/           # MySQL配置文件
│       └── init/
│           └── 01-init-database.sql
└── production/          # 生产环境 (端口+10000)
    ├── docker-compose.yml
    ├── start-prod-cluster.py
    ├── Dockerfile.*
    ├── nats/            # NATS集群配置文件
    │   ├── nats-1.conf
    │   ├── nats-2.conf
    │   └── nats-3.conf
    └── mysql/           # MySQL配置文件
        └── init/
            └── 01-init-database.sql
```

## 🎯 端口规划标准

| 环境 | 端口偏移 | NATS | MySQL | API | 监控 |
|------|---------|------|-------|-----|------|
| **开发环境** | +0 | 4222 | 3306 | 5000 | 8222 |
| **生产环境** | +10000 | 14222-14224 | 13306 | 15000 | 18222-18224 |
| **测试环境** | +20000 | 24222-24224 | 23306 | 25000-25001 | 28222-28224 |

## 🚀 快速启动

### 开发环境 (单节点)
```bash
# 启动开发环境
python docker/development/start-dev-env.py start

# 访问服务
# API: http://localhost:5000
# NATS: nats://localhost:4222
# MySQL: localhost:3306
```

### 测试环境 (8节点集群)
```bash
# 启动测试集群
python docker/testing/start-test-cluster.py start

# 访问服务
# API节点: http://localhost:25000
# 多角色节点: http://localhost:25001
# NATS集群: nats://localhost:24222,localhost:24223,localhost:24224
# MySQL: localhost:23306
```

### 生产环境 (7节点集群)
```bash
# 启动生产集群 (需要先配置证书和密码)
python docker/production/start-prod-cluster.py start

# 访问服务
# API节点: http://localhost:15000
# NATS集群: nats://localhost:14222,localhost:14223,localhost:14224
# MySQL: localhost:13306
```

## 🏗️ 架构组件

### 开发环境组件
- **NATS**: 1个单节点 (nats:2.11.8-alpine)
- **MySQL**: 1个数据库实例
- **API**: 1个API节点 (多角色: Api+Worker+Designer)

### 测试环境组件
- **NATS**: 3节点集群 (nats:2.11.8-alpine)
- **MySQL**: 1个数据库实例
- **应用节点**: 8个节点
  - 1个API节点 (单角色)
  - 6个专用角色节点 (Worker, Designer, Validator, Executor, Monitor, Scheduler)
  - 1个多角色节点 (Api+Worker+Designer)

### 生产环境组件
- **NATS**: 3节点集群 (nats:2.11.8-alpine)
- **MySQL**: 1个数据库实例
- **应用节点**: 7个节点
  - 1个API节点 (单角色)
  - 6个专用角色节点 (Worker, Designer, Validator, Executor, Monitor, Scheduler)

## 🔧 配置特点

### 开发环境特点
- **简化配置**: 最小化配置，便于开发调试
- **详细日志**: Debug级别日志，完整错误信息
- **无安全限制**: 无TLS，简单认证
- **快速启动**: 单节点，启动速度快
- **资源占用小**: 适合本地开发机器

### 测试环境特点
- **集群模拟**: 完整的3节点NATS集群
- **端口隔离**: +20000端口偏移，避免冲突
- **集成测试**: 支持完整的集成测试
- **故障模拟**: 支持故障转移测试
- **性能测试**: 支持负载和性能测试

### 生产环境特点
- **高可用**: 3节点NATS集群，故障转移
- **安全配置**: TLS加密，强认证
- **性能优化**: 生产级别的资源配置
- **监控完整**: 完整的监控和日志
- **非root用户**: 安全的容器运行环境

## 📊 资源配置

### NATS JetStream配置

| 环境 | 内存存储 | 文件存储 | 最大连接 | 最大订阅 |
|------|---------|----------|----------|----------|
| **开发** | 128MB | 512MB | 100 | 1,000 |
| **测试** | 256MB | 1GB | 1,000 | 10,000 |
| **生产** | 2GB | 100GB | 10,000 | 100,000 |

### MySQL配置

| 环境 | 数据库名 | 用户 | 密码策略 |
|------|---------|------|----------|
| **开发** | flowcustom_dev | flowcustom | 简单密码 |
| **测试** | flowcustom_test | flowcustom | 测试密码 |
| **生产** | flowcustom_prod | flowcustom | 待部署配置 |

## 🔐 安全配置

### 开发环境
- ❌ 无TLS加密
- ❌ 简单密码认证
- ✅ 详细日志记录

### 测试环境
- ❌ 无TLS加密 (测试环境)
- ✅ 基本密码认证
- ✅ 调试日志记录

### 生产环境
- ✅ **必需TLS加密**
- ✅ 强密码认证
- ✅ 账户隔离
- ✅ 最小日志记录
- ✅ 非root用户运行
- ✅ 证书验证

## 🛠️ 管理命令

### 通用命令
```bash
# 启动环境
python docker/{environment}/start-{env}-{cluster}.py start

# 检查状态
python docker/{environment}/start-{env}-{cluster}.py status

# 停止环境
python docker/{environment}/start-{env}-{cluster}.py stop

# 清理环境
python docker/{environment}/start-{env}-{cluster}.py cleanup
```

### Docker Compose命令
```bash
# 启动特定服务
docker-compose -f docker/{environment}/docker-compose.yml up -d {service}

# 查看日志
docker-compose -f docker/{environment}/docker-compose.yml logs {service}

# 停止所有服务
docker-compose -f docker/{environment}/docker-compose.yml down
```

## 📝 版本信息

- **Docker配置版本**: v0.0.1.8
- **NATS版本**: 2.11.8-alpine
- **MySQL版本**: 8.0
- **.NET版本**: 9.0
- **最后更新**: 2025-09-07

## 🎯 使用建议

1. **开发阶段**: 使用开发环境进行日常开发和调试
2. **集成测试**: 使用测试环境进行完整的集成测试
3. **性能测试**: 使用测试环境进行负载和性能测试
4. **生产部署**: 使用生产环境配置进行实际部署

## 🔍 故障排除

### 端口冲突
- 检查端口是否被占用: `netstat -an | findstr {port}`
- 停止冲突的服务或容器

### 容器启动失败
- 查看容器日志: `docker logs {container_name}`
- 检查配置文件是否正确
- 确认依赖服务已启动

### 网络连接问题
- 检查Docker网络: `docker network ls`
- 验证服务间连通性
- 检查防火墙设置
