namespace FlowCustomV1.Core.Models.Plugins;

/// <summary>
/// 插件统计信息
/// </summary>
public class PluginStatistics
{
    /// <summary>
    /// 总插件数量
    /// </summary>
    public int TotalPlugins { get; set; }

    /// <summary>
    /// 已加载插件数量
    /// </summary>
    public int LoadedPlugins { get; set; }

    /// <summary>
    /// 内置插件数量
    /// </summary>
    public int BuiltinPlugins { get; set; }

    /// <summary>
    /// JSON配置插件数量
    /// </summary>
    public int JsonConfigPlugins { get; set; }

    /// <summary>
    /// DLL预编译插件数量
    /// </summary>
    public int DllPrecompiledPlugins { get; set; }

    /// <summary>
    /// 插件总使用次数
    /// </summary>
    public long TotalUsageCount { get; set; }

    /// <summary>
    /// 平均加载时间（毫秒）
    /// </summary>
    public double AverageLoadTimeMs { get; set; }

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 按类型分组的统计
    /// </summary>
    public Dictionary<PluginType, PluginTypeStatistics> ByType { get; set; } = new();

    /// <summary>
    /// 最常用的插件
    /// </summary>
    public List<PluginUsageInfo> MostUsedPlugins { get; set; } = new();

    /// <summary>
    /// 最近加载的插件
    /// </summary>
    public List<PluginInfo> RecentlyLoadedPlugins { get; set; } = new();
}

/// <summary>
/// 插件类型统计信息
/// </summary>
public class PluginTypeStatistics
{
    /// <summary>
    /// 插件类型
    /// </summary>
    public PluginType PluginType { get; set; }

    /// <summary>
    /// 插件数量
    /// </summary>
    public int Count { get; set; }

    /// <summary>
    /// 已加载数量
    /// </summary>
    public int LoadedCount { get; set; }

    /// <summary>
    /// 总使用次数
    /// </summary>
    public long TotalUsageCount { get; set; }

    /// <summary>
    /// 平均加载时间（毫秒）
    /// </summary>
    public double AverageLoadTimeMs { get; set; }

    /// <summary>
    /// 成功率
    /// </summary>
    public double SuccessRate { get; set; }
}

/// <summary>
/// 插件使用信息
/// </summary>
public class PluginUsageInfo
{
    /// <summary>
    /// 节点类型
    /// </summary>
    public string NodeType { get; set; } = string.Empty;

    /// <summary>
    /// 插件名称
    /// </summary>
    public string PluginName { get; set; } = string.Empty;

    /// <summary>
    /// 使用次数
    /// </summary>
    public long UsageCount { get; set; }

    /// <summary>
    /// 最后使用时间
    /// </summary>
    public DateTime LastUsedAt { get; set; }

    /// <summary>
    /// 平均执行时间（毫秒）
    /// </summary>
    public double AverageExecutionTimeMs { get; set; }

    /// <summary>
    /// 成功率
    /// </summary>
    public double SuccessRate { get; set; }
}

/// <summary>
/// 编译统计信息
/// </summary>
public class CompilationStatistics
{
    /// <summary>
    /// 总编译次数
    /// </summary>
    public long TotalCompilations { get; set; }

    /// <summary>
    /// 成功编译次数
    /// </summary>
    public long SuccessfulCompilations { get; set; }

    /// <summary>
    /// 失败编译次数
    /// </summary>
    public long FailedCompilations { get; set; }

    /// <summary>
    /// 缓存命中次数
    /// </summary>
    public long CacheHits { get; set; }

    /// <summary>
    /// 缓存未命中次数
    /// </summary>
    public long CacheMisses { get; set; }

    /// <summary>
    /// 平均编译时间（毫秒）
    /// </summary>
    public double AverageCompilationTimeMs { get; set; }

    /// <summary>
    /// 最快编译时间（毫秒）
    /// </summary>
    public long FastestCompilationTimeMs { get; set; }

    /// <summary>
    /// 最慢编译时间（毫秒）
    /// </summary>
    public long SlowestCompilationTimeMs { get; set; }

    /// <summary>
    /// 编译成功率
    /// </summary>
    public double SuccessRate { get; set; }

    /// <summary>
    /// 缓存命中率
    /// </summary>
    public double CacheHitRate { get; set; }

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 按节点类型分组的编译统计
    /// </summary>
    public Dictionary<string, NodeTypeCompilationStatistics> ByNodeType { get; set; } = new();
}

/// <summary>
/// 节点类型编译统计信息
/// </summary>
public class NodeTypeCompilationStatistics
{
    /// <summary>
    /// 节点类型
    /// </summary>
    public string NodeType { get; set; } = string.Empty;

    /// <summary>
    /// 编译次数
    /// </summary>
    public long CompilationCount { get; set; }

    /// <summary>
    /// 成功次数
    /// </summary>
    public long SuccessCount { get; set; }

    /// <summary>
    /// 失败次数
    /// </summary>
    public long FailureCount { get; set; }

    /// <summary>
    /// 平均编译时间（毫秒）
    /// </summary>
    public double AverageCompilationTimeMs { get; set; }

    /// <summary>
    /// 最后编译时间
    /// </summary>
    public DateTime LastCompiledAt { get; set; }

    /// <summary>
    /// 成功率
    /// </summary>
    public double SuccessRate { get; set; }
}
