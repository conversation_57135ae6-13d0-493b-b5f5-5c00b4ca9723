# FlowCustomV1 历史版本功能全面测试脚本
# 版本: v0.0.1.8
# 测试范围: v0.0.0.10 - v0.0.1.7 所有历史功能

param(
    [switch]$SkipInfrastructure,
    [switch]$Verbose,
    [int]$TimeoutSeconds = 600
)

# 测试结果记录
$TestResults = @()
$StartTime = Get-Date

# 日志函数
function Write-TestLog {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $color = switch ($Level) {
        "ERROR" { "Red" }
        "WARN" { "Yellow" }
        "SUCCESS" { "Green" }
        "SECTION" { "Cyan" }
        default { "White" }
    }
    Write-Host "[$timestamp] [$Level] $Message" -ForegroundColor $color
}

function Add-TestResult {
    param(
        [string]$TestName,
        [bool]$Passed,
        [string]$Details = "",
        [string]$Version = ""
    )
    
    $script:TestResults += [PSCustomObject]@{
        TestName = $TestName
        Version = $Version
        Passed = $Passed
        Details = $Details
        Timestamp = Get-Date
    }
    
    $status = if ($Passed) { "PASS" } else { "FAIL" }
    $versionInfo = if ($Version) { " [$Version]" } else { "" }
    Write-TestLog "$status - $TestName$versionInfo $(if($Details) { "($Details)" })" $(if($Passed) { "SUCCESS" } else { "ERROR" })
}

function Test-ApiEndpoint {
    param([string]$Url, [string]$Method = "GET", [string]$Body = $null, [int]$TimeoutSec = 10)
    
    try {
        $params = @{
            Uri = $Url
            Method = $Method
            TimeoutSec = $TimeoutSec
            UseBasicParsing = $true
        }
        
        if ($Body) {
            $params.Body = $Body
            $params.ContentType = "application/json"
        }
        
        $response = Invoke-WebRequest @params
        return @{
            Success = $response.StatusCode -eq 200
            StatusCode = $response.StatusCode
            Content = $response.Content
        }
    }
    catch {
        return @{
            Success = $false
            StatusCode = 0
            Content = $_.Exception.Message
        }
    }
}

function Test-ContainerLogs {
    param([string]$ContainerName, [string]$Pattern)
    
    try {
        $logs = docker logs $ContainerName 2>&1 | Select-String $Pattern
        return $logs.Count -gt 0
    }
    catch {
        return $false
    }
}

Write-TestLog "Starting FlowCustomV1 Historical Features Comprehensive Test" "SECTION"
Write-TestLog "Test Range: v0.0.0.10 - v0.0.1.7" "INFO"

try {
    # 设置工作目录
    $testDir = Split-Path -Parent $PSScriptRoot
    Set-Location $testDir

    # 确保Docker环境运行
    if (-not $SkipInfrastructure) {
        Write-TestLog "Verifying Docker environment..." "INFO"
        $containers = docker ps --format "{{.Names}}" | Where-Object { $_ -match "flowcustom-test" }
        if ($containers.Count -lt 4) {
            Write-TestLog "Docker environment not ready. Starting containers..." "WARN"
            docker-compose -f docker-compose.simple.yml up -d
            Start-Sleep -Seconds 60
        }
    }

    # ==================== v0.0.0.10 - RESTful API 基础功能测试 ====================
    Write-TestLog "Testing v0.0.0.10 - RESTful API Basic Functions" "SECTION"
    
    # 测试基础API端点
    Write-TestLog "Testing basic API endpoints..." "INFO"
    
    # 测试Swagger文档
    $swaggerTest = Test-ApiEndpoint "http://localhost:5001/swagger/index.html"
    Add-TestResult "Swagger Documentation" $swaggerTest.Success $swaggerTest.Content "v0.0.0.10"
    
    # 测试工作流API - 获取所有工作流
    $workflowsTest = Test-ApiEndpoint "http://localhost:5001/api/workflows"
    Add-TestResult "GET /api/workflows" $workflowsTest.Success $workflowsTest.StatusCode "v0.0.0.10"
    
    # 测试工作流创建
    $createWorkflowBody = @{
        name = "Test Workflow"
        description = "Test workflow for historical testing"
        definition = @{
            nodes = @()
            connections = @()
        }
    } | ConvertTo-Json -Depth 3
    
    $createTest = Test-ApiEndpoint "http://localhost:5001/api/workflows" "POST" $createWorkflowBody
    Add-TestResult "POST /api/workflows" $createTest.Success $createTest.StatusCode "v0.0.0.10"
    
    # 如果创建成功，测试其他操作
    if ($createTest.Success) {
        try {
            $workflowResponse = $createTest.Content | ConvertFrom-Json
            $workflowId = $workflowResponse.id
            
            # 测试获取单个工作流
            $getWorkflowTest = Test-ApiEndpoint "http://localhost:5001/api/workflows/$workflowId"
            Add-TestResult "GET /api/workflows/{id}" $getWorkflowTest.Success $getWorkflowTest.StatusCode "v0.0.0.10"
            
            # 测试工作流验证
            $validateTest = Test-ApiEndpoint "http://localhost:5001/api/workflows/validate" "POST" $createWorkflowBody
            Add-TestResult "POST /api/workflows/validate" $validateTest.Success $validateTest.StatusCode "v0.0.0.10"
            
            # 测试工作流执行
            $executeTest = Test-ApiEndpoint "http://localhost:5001/api/executions/start/$workflowId" "POST" "{}"
            Add-TestResult "POST /api/executions/start/{id}" $executeTest.Success $executeTest.StatusCode "v0.0.0.10"
        }
        catch {
            Write-TestLog "Error parsing workflow creation response: $($_.Exception.Message)" "WARN"
        }
    }

    # ==================== v0.0.1.1 - NATS消息路由功能测试 ====================
    Write-TestLog "Testing v0.0.1.1 - NATS Message Routing Functions" "SECTION"
    
    # 测试NATS连接
    $natsHealthy = Test-ApiEndpoint "http://localhost:8222/healthz"
    Add-TestResult "NATS Server Health" $natsHealthy.Success $natsHealthy.Content "v0.0.1.1"
    
    # 测试NATS连接信息
    $natsConnections = Test-ApiEndpoint "http://localhost:8222/connz"
    Add-TestResult "NATS Connections" $natsConnections.Success "Connection count check" "v0.0.1.1"
    
    # 检查应用日志中的NATS消息
    $masterNatsLogs = Test-ContainerLogs "flowcustom-test-master" "NATS.*connect"
    Add-TestResult "Master Node NATS Connection" $masterNatsLogs "NATS connection logs found" "v0.0.1.1"
    
    $workerNatsLogs = Test-ContainerLogs "flowcustom-test-worker" "NATS.*connect"
    Add-TestResult "Worker Node NATS Connection" $workerNatsLogs "NATS connection logs found" "v0.0.1.1"
    
    # 测试消息发布功能
    $masterMessageLogs = Test-ContainerLogs "flowcustom-test-master" "Message.*publish"
    Add-TestResult "Message Publishing" $masterMessageLogs "Message publishing logs found" "v0.0.1.1"

    # ==================== v0.0.1.3 - 节点服务发现功能测试 ====================
    Write-TestLog "Testing v0.0.1.3 - Node Discovery Functions" "SECTION"
    
    # 测试集群API
    $clusterTest = Test-ApiEndpoint "http://localhost:5001/api/cluster/status"
    Add-TestResult "Cluster Status API" $clusterTest.Success $clusterTest.StatusCode "v0.0.1.3"
    
    # 测试节点列表API
    $nodesTest = Test-ApiEndpoint "http://localhost:5001/api/nodes"
    Add-TestResult "Nodes List API" $nodesTest.Success $nodesTest.StatusCode "v0.0.1.3"
    
    # 检查节点注册日志
    $masterRegisterLogs = Test-ContainerLogs "flowcustom-test-master" "node.*register"
    Add-TestResult "Master Node Registration" $masterRegisterLogs "Node registration logs found" "v0.0.1.3"
    
    $workerRegisterLogs = Test-ContainerLogs "flowcustom-test-worker" "node.*register"
    Add-TestResult "Worker Node Registration" $workerRegisterLogs "Node registration logs found" "v0.0.1.3"
    
    # 检查心跳机制
    $masterHeartbeatLogs = Test-ContainerLogs "flowcustom-test-master" "heartbeat"
    Add-TestResult "Master Node Heartbeat" $masterHeartbeatLogs "Heartbeat logs found" "v0.0.1.3"
    
    $workerHeartbeatLogs = Test-ContainerLogs "flowcustom-test-worker" "heartbeat"
    Add-TestResult "Worker Node Heartbeat" $workerHeartbeatLogs "Heartbeat logs found" "v0.0.1.3"

    # ==================== v0.0.1.4 - Designer节点服务功能测试 ====================
    Write-TestLog "Testing v0.0.1.4 - Designer Node Functions" "SECTION"
    
    # 测试工作流设计器API
    $designerTest = Test-ApiEndpoint "http://localhost:5001/api/designer/workflows"
    Add-TestResult "Designer Workflows API" $designerTest.Success $designerTest.StatusCode "v0.0.1.4"
    
    # 测试模板管理API
    $templatesTest = Test-ApiEndpoint "http://localhost:5001/api/designer/templates"
    Add-TestResult "Template Management API" $templatesTest.Success $templatesTest.StatusCode "v0.0.1.4"
    
    # 测试协作API
    $collaborationTest = Test-ApiEndpoint "http://localhost:5001/api/collaboration/sessions"
    Add-TestResult "Collaboration API" $collaborationTest.Success $collaborationTest.StatusCode "v0.0.1.4"

    # ==================== v0.0.1.5 - Validator节点服务功能测试 ====================
    Write-TestLog "Testing v0.0.1.5 - Validator Node Functions" "SECTION"
    
    # 测试验证器API
    $validatorTest = Test-ApiEndpoint "http://localhost:5001/api/validator/workflows/validate" "POST" '{"workflowId":"test"}'
    Add-TestResult "Validator API" $validatorTest.Success $validatorTest.StatusCode "v0.0.1.5"
    
    # 测试验证缓存API
    $cacheTest = Test-ApiEndpoint "http://localhost:5001/api/validator/cache/stats"
    Add-TestResult "Validation Cache API" $cacheTest.Success $cacheTest.StatusCode "v0.0.1.5"
    
    # 测试验证规则API
    $rulesTest = Test-ApiEndpoint "http://localhost:5001/api/validator/rules"
    Add-TestResult "Validation Rules API" $rulesTest.Success $rulesTest.StatusCode "v0.0.1.5"

    # ==================== v0.0.1.6 - Executor节点服务功能测试 ====================
    Write-TestLog "Testing v0.0.1.6 - Executor Node Functions" "SECTION"
    
    # 测试执行器容量API
    $capacityTest = Test-ApiEndpoint "http://localhost:5001/api/executor/capacity"
    Add-TestResult "Executor Capacity API" $capacityTest.Success $capacityTest.StatusCode "v0.0.1.6"
    
    # 测试执行状态API
    $executionStatusTest = Test-ApiEndpoint "http://localhost:5001/api/executor/executions"
    Add-TestResult "Execution Status API" $executionStatusTest.Success $executionStatusTest.StatusCode "v0.0.1.6"

    # ==================== v0.0.1.7 - 分布式任务调度系统测试 ====================
    Write-TestLog "Testing v0.0.1.7 - Distributed Task Scheduling System" "SECTION"
    
    # 测试任务分发API
    $taskDistributionTest = Test-ApiEndpoint "http://localhost:5001/api/tasks/distribute" "POST" '{"taskType":"test"}'
    Add-TestResult "Task Distribution API" $taskDistributionTest.Success $taskDistributionTest.StatusCode "v0.0.1.7"
    
    # 测试负载均衡API
    $loadBalancingTest = Test-ApiEndpoint "http://localhost:5001/api/tasks/balance"
    Add-TestResult "Load Balancing API" $loadBalancingTest.Success $loadBalancingTest.StatusCode "v0.0.1.7"
    
    # 测试任务跟踪API
    $taskTrackingTest = Test-ApiEndpoint "http://localhost:5001/api/tasks/tracking"
    Add-TestResult "Task Tracking API" $taskTrackingTest.Success $taskTrackingTest.StatusCode "v0.0.1.7"
    
    # 检查任务分发日志
    $taskDistributionLogs = Test-ContainerLogs "flowcustom-test-master" "task.*distribut"
    Add-TestResult "Task Distribution Logs" $taskDistributionLogs "Task distribution logs found" "v0.0.1.7"

    # ==================== 系统集成测试 ====================
    Write-TestLog "Testing System Integration" "SECTION"
    
    # 测试完整的工作流执行流程
    if ($createTest.Success) {
        try {
            $workflowResponse = $createTest.Content | ConvertFrom-Json
            $workflowId = $workflowResponse.id
            
            # 启动工作流执行
            $executeTest = Test-ApiEndpoint "http://localhost:5001/api/executions/start/$workflowId" "POST" "{}"
            if ($executeTest.Success) {
                $executionResponse = $executeTest.Content | ConvertFrom-Json
                $executionId = $executionResponse.executionId
                
                # 等待执行完成
                Start-Sleep -Seconds 5
                
                # 检查执行结果
                $resultTest = Test-ApiEndpoint "http://localhost:5001/api/executions/$executionId"
                Add-TestResult "End-to-End Workflow Execution" $resultTest.Success $resultTest.StatusCode "Integration"
            }
        }
        catch {
            Add-TestResult "End-to-End Workflow Execution" $false $_.Exception.Message "Integration"
        }
    }

}
catch {
    Write-TestLog "Critical error during test execution: $($_.Exception.Message)" "ERROR"
}
finally {
    # 生成测试报告
    $EndTime = Get-Date
    $TotalDuration = ($EndTime - $StartTime).TotalSeconds
    
    Write-TestLog "Historical features testing completed, generating report..." "INFO"
    
    # 统计结果
    $TotalTests = $TestResults.Count
    $PassedTests = ($TestResults | Where-Object { $_.Passed }).Count
    $FailedTests = $TotalTests - $PassedTests
    $SuccessRate = if ($TotalTests -gt 0) { [math]::Round(($PassedTests / $TotalTests) * 100, 2) } else { 0 }
    
    # 按版本统计
    $VersionStats = $TestResults | Group-Object Version | ForEach-Object {
        $versionPassed = ($_.Group | Where-Object { $_.Passed }).Count
        $versionTotal = $_.Group.Count
        $versionRate = if ($versionTotal -gt 0) { [math]::Round(($versionPassed / $versionTotal) * 100, 2) } else { 0 }
        
        [PSCustomObject]@{
            Version = $_.Name
            Total = $versionTotal
            Passed = $versionPassed
            Failed = $versionTotal - $versionPassed
            SuccessRate = $versionRate
        }
    }
    
    # 输出测试报告
    Write-Host "`n" + "="*80 -ForegroundColor Cyan
    Write-Host "FlowCustomV1 Historical Features Test Report" -ForegroundColor Cyan
    Write-Host "="*80 -ForegroundColor Cyan
    Write-Host "Test Duration: $([math]::Round($TotalDuration, 2)) seconds" -ForegroundColor White
    Write-Host "Total Tests: $TotalTests" -ForegroundColor White
    Write-Host "Passed Tests: $PassedTests" -ForegroundColor Green
    Write-Host "Failed Tests: $FailedTests" -ForegroundColor Red
    Write-Host "Overall Success Rate: $SuccessRate%" -ForegroundColor $(if($SuccessRate -ge 70) { "Green" } else { "Red" })
    Write-Host ""
    
    # 版本统计
    Write-Host "Results by Version:" -ForegroundColor Yellow
    Write-Host "-"*80 -ForegroundColor Yellow
    foreach ($stat in $VersionStats | Sort-Object Version) {
        $color = if ($stat.SuccessRate -ge 70) { "Green" } else { "Red" }
        Write-Host "$($stat.Version): $($stat.Passed)/$($stat.Total) ($($stat.SuccessRate)%)" -ForegroundColor $color
    }
    Write-Host ""
    
    # 详细结果
    Write-Host "Detailed Test Results:" -ForegroundColor Yellow
    Write-Host "-"*80 -ForegroundColor Yellow
    
    foreach ($result in $TestResults | Sort-Object Version, TestName) {
        $status = if ($result.Passed) { "PASS" } else { "FAIL" }
        $versionInfo = if ($result.Version) { "[$($result.Version)]" } else { "[System]" }
        Write-Host "$status $versionInfo $($result.TestName)" -ForegroundColor $(if($result.Passed) { "Green" } else { "Red" })
        if ($result.Details -and -not $result.Passed) {
            Write-Host "    Details: $($result.Details)" -ForegroundColor Gray
        }
    }
    
    Write-Host "`n" + "="*80 -ForegroundColor Cyan
    
    # 保存测试报告
    $reportPath = "historical-test-report-$(Get-Date -Format 'yyyyMMdd-HHmmss').json"
    $reportData = @{
        TestSummary = @{
            TotalTests = $TotalTests
            PassedTests = $PassedTests
            FailedTests = $FailedTests
            SuccessRate = $SuccessRate
            Duration = $TotalDuration
        }
        VersionStats = $VersionStats
        DetailedResults = $TestResults
    }
    $reportData | ConvertTo-Json -Depth 4 | Out-File $reportPath
    Write-TestLog "Historical test report saved to: $reportPath" "INFO"
    
    # 返回退出码
    if ($SuccessRate -ge 70) {
        Write-TestLog "Historical features testing completed with acceptable success rate!" "SUCCESS"
        exit 0
    } else {
        Write-TestLog "Historical features testing completed with low success rate" "ERROR"
        exit 1
    }
}
