using System.ComponentModel.DataAnnotations;

namespace FlowCustomV1.Core.Models.Workflow;

/// <summary>
/// 节点资源限制
/// </summary>
public class NodeResourceLimits
{
    /// <summary>
    /// 最大内存使用量（MB）
    /// </summary>
    [Range(0, int.MaxValue)]
    public int MaxMemoryMb { get; set; } = 0; // 0表示无限制

    /// <summary>
    /// 最大CPU使用率（百分比）
    /// </summary>
    [Range(0, 100)]
    public int MaxCpuPercent { get; set; } = 0; // 0表示无限制

    /// <summary>
    /// 最大磁盘使用量（MB）
    /// </summary>
    [Range(0, long.MaxValue)]
    public long MaxDiskMb { get; set; } = 0; // 0表示无限制

    /// <summary>
    /// 最大网络带宽（Mbps）
    /// </summary>
    [Range(0, int.MaxValue)]
    public int MaxNetworkMbps { get; set; } = 0; // 0表示无限制

    /// <summary>
    /// 最大执行时间（秒）
    /// </summary>
    [Range(0, int.MaxValue)]
    public int MaxExecutionSeconds { get; set; } = 0; // 0表示无限制

    /// <summary>
    /// 验证资源限制的有效性
    /// </summary>
    /// <returns>是否有效</returns>
    public bool IsValid()
    {
        return MaxMemoryMb >= 0 &&
               MaxCpuPercent >= 0 && MaxCpuPercent <= 100 &&
               MaxDiskMb >= 0 &&
               MaxNetworkMbps >= 0 &&
               MaxExecutionSeconds >= 0;
    }

    /// <summary>
    /// 合并另一个资源限制
    /// </summary>
    /// <param name="other">要合并的资源限制</param>
    public void Merge(NodeResourceLimits other)
    {
        if (other == null) return;

        if (other.MaxMemoryMb > 0) MaxMemoryMb = other.MaxMemoryMb;
        if (other.MaxCpuPercent > 0) MaxCpuPercent = other.MaxCpuPercent;
        if (other.MaxDiskMb > 0) MaxDiskMb = other.MaxDiskMb;
        if (other.MaxNetworkMbps > 0) MaxNetworkMbps = other.MaxNetworkMbps;
        if (other.MaxExecutionSeconds > 0) MaxExecutionSeconds = other.MaxExecutionSeconds;
    }

    /// <summary>
    /// 创建资源限制的深拷贝
    /// </summary>
    /// <returns>资源限制的深拷贝</returns>
    public NodeResourceLimits Clone()
    {
        return new NodeResourceLimits
        {
            MaxMemoryMb = MaxMemoryMb,
            MaxCpuPercent = MaxCpuPercent,
            MaxDiskMb = MaxDiskMb,
            MaxNetworkMbps = MaxNetworkMbps,
            MaxExecutionSeconds = MaxExecutionSeconds
        };
    }
}

/// <summary>
/// 节点安全设置
/// </summary>
public class NodeSecuritySettings
{
    /// <summary>
    /// 是否启用沙箱模式
    /// </summary>
    public bool EnableSandbox { get; set; } = false;

    /// <summary>
    /// 允许的网络访问列表
    /// </summary>
    public List<string> AllowedNetworkAccess { get; set; } = new();

    /// <summary>
    /// 禁止的网络访问列表
    /// </summary>
    public List<string> DeniedNetworkAccess { get; set; } = new();

    /// <summary>
    /// 允许的文件系统访问路径
    /// </summary>
    public List<string> AllowedFileSystemPaths { get; set; } = new();

    /// <summary>
    /// 禁止的文件系统访问路径
    /// </summary>
    public List<string> DeniedFileSystemPaths { get; set; } = new();

    /// <summary>
    /// 是否允许执行外部程序
    /// </summary>
    public bool AllowExternalExecution { get; set; } = false;

    /// <summary>
    /// 安全标签
    /// </summary>
    public HashSet<string> SecurityTags { get; set; } = new();

    /// <summary>
    /// 合并另一个安全设置
    /// </summary>
    /// <param name="other">要合并的安全设置</param>
    public void Merge(NodeSecuritySettings other)
    {
        if (other == null) return;

        EnableSandbox = other.EnableSandbox || EnableSandbox;
        AllowExternalExecution = other.AllowExternalExecution || AllowExternalExecution;

        AllowedNetworkAccess.AddRange(other.AllowedNetworkAccess);
        DeniedNetworkAccess.AddRange(other.DeniedNetworkAccess);
        AllowedFileSystemPaths.AddRange(other.AllowedFileSystemPaths);
        DeniedFileSystemPaths.AddRange(other.DeniedFileSystemPaths);

        foreach (var tag in other.SecurityTags)
        {
            SecurityTags.Add(tag);
        }
    }

    /// <summary>
    /// 创建安全设置的深拷贝
    /// </summary>
    /// <returns>安全设置的深拷贝</returns>
    public NodeSecuritySettings Clone()
    {
        return new NodeSecuritySettings
        {
            EnableSandbox = EnableSandbox,
            AllowedNetworkAccess = new List<string>(AllowedNetworkAccess),
            DeniedNetworkAccess = new List<string>(DeniedNetworkAccess),
            AllowedFileSystemPaths = new List<string>(AllowedFileSystemPaths),
            DeniedFileSystemPaths = new List<string>(DeniedFileSystemPaths),
            AllowExternalExecution = AllowExternalExecution,
            SecurityTags = new HashSet<string>(SecurityTags)
        };
    }
}

/// <summary>
/// 节点缓存设置
/// </summary>
public class NodeCacheSettings
{
    /// <summary>
    /// 是否启用缓存
    /// </summary>
    public bool EnableCache { get; set; } = false;

    /// <summary>
    /// 缓存键模板
    /// </summary>
    public string CacheKeyTemplate { get; set; } = string.Empty;

    /// <summary>
    /// 缓存过期时间（分钟）
    /// </summary>
    [Range(0, int.MaxValue)]
    public int CacheExpirationMinutes { get; set; } = 60;

    /// <summary>
    /// 缓存策略
    /// </summary>
    public string CacheStrategy { get; set; } = "LRU";

    /// <summary>
    /// 最大缓存大小（MB）
    /// </summary>
    [Range(0, int.MaxValue)]
    public int MaxCacheSizeMb { get; set; } = 100;

    /// <summary>
    /// 缓存标签
    /// </summary>
    public HashSet<string> CacheTags { get; set; } = new();

    /// <summary>
    /// 合并另一个缓存设置
    /// </summary>
    /// <param name="other">要合并的缓存设置</param>
    public void Merge(NodeCacheSettings other)
    {
        if (other == null) return;

        EnableCache = other.EnableCache || EnableCache;
        if (!string.IsNullOrWhiteSpace(other.CacheKeyTemplate))
            CacheKeyTemplate = other.CacheKeyTemplate;
        if (other.CacheExpirationMinutes > 0)
            CacheExpirationMinutes = other.CacheExpirationMinutes;
        if (!string.IsNullOrWhiteSpace(other.CacheStrategy))
            CacheStrategy = other.CacheStrategy;
        if (other.MaxCacheSizeMb > 0)
            MaxCacheSizeMb = other.MaxCacheSizeMb;

        foreach (var tag in other.CacheTags)
        {
            CacheTags.Add(tag);
        }
    }

    /// <summary>
    /// 创建缓存设置的深拷贝
    /// </summary>
    /// <returns>缓存设置的深拷贝</returns>
    public NodeCacheSettings Clone()
    {
        return new NodeCacheSettings
        {
            EnableCache = EnableCache,
            CacheKeyTemplate = CacheKeyTemplate,
            CacheExpirationMinutes = CacheExpirationMinutes,
            CacheStrategy = CacheStrategy,
            MaxCacheSizeMb = MaxCacheSizeMb,
            CacheTags = new HashSet<string>(CacheTags)
        };
    }
}

/// <summary>
/// 节点监控设置
/// </summary>
public class NodeMonitoringSettings
{
    /// <summary>
    /// 是否启用详细监控
    /// </summary>
    public bool EnableDetailedMonitoring { get; set; } = false;

    /// <summary>
    /// 监控指标收集间隔（秒）
    /// </summary>
    [Range(1, int.MaxValue)]
    public int MetricsCollectionIntervalSeconds { get; set; } = 60;

    /// <summary>
    /// 是否记录性能指标
    /// </summary>
    public bool RecordPerformanceMetrics { get; set; } = true;

    /// <summary>
    /// 是否记录资源使用情况
    /// </summary>
    public bool RecordResourceUsage { get; set; } = true;

    /// <summary>
    /// 自定义监控标签
    /// </summary>
    public Dictionary<string, string> CustomTags { get; set; } = new();

    /// <summary>
    /// 告警阈值配置
    /// </summary>
    public Dictionary<string, double> AlertThresholds { get; set; } = new();

    /// <summary>
    /// 合并另一个监控设置
    /// </summary>
    /// <param name="other">要合并的监控设置</param>
    public void Merge(NodeMonitoringSettings other)
    {
        if (other == null) return;

        EnableDetailedMonitoring = other.EnableDetailedMonitoring || EnableDetailedMonitoring;
        RecordPerformanceMetrics = other.RecordPerformanceMetrics || RecordPerformanceMetrics;
        RecordResourceUsage = other.RecordResourceUsage || RecordResourceUsage;

        if (other.MetricsCollectionIntervalSeconds > 0)
            MetricsCollectionIntervalSeconds = other.MetricsCollectionIntervalSeconds;

        foreach (var kvp in other.CustomTags)
        {
            CustomTags[kvp.Key] = kvp.Value;
        }

        foreach (var kvp in other.AlertThresholds)
        {
            AlertThresholds[kvp.Key] = kvp.Value;
        }
    }

    /// <summary>
    /// 创建监控设置的深拷贝
    /// </summary>
    /// <returns>监控设置的深拷贝</returns>
    public NodeMonitoringSettings Clone()
    {
        return new NodeMonitoringSettings
        {
            EnableDetailedMonitoring = EnableDetailedMonitoring,
            MetricsCollectionIntervalSeconds = MetricsCollectionIntervalSeconds,
            RecordPerformanceMetrics = RecordPerformanceMetrics,
            RecordResourceUsage = RecordResourceUsage,
            CustomTags = new Dictionary<string, string>(CustomTags),
            AlertThresholds = new Dictionary<string, double>(AlertThresholds)
        };
    }
}
