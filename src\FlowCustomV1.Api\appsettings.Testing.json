{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning", "FlowCustomV1": "Information", "System": "Information"}}, "AllowedHosts": "*", "System": {"Network": {"HttpPort": 25000, "HttpsPort": 25001, "WebSocketPort": 28080, "ManagementPort": 28081, "BindAddress": "0.0.0.0", "AllowedHosts": ["*"], "CorsAllowedOrigins": ["*"]}, "Performance": {"MaxConcurrentWorkflows": 100, "MaxConcurrentNodes": 50, "WorkerThreadPoolSize": 8, "IoThreadPoolSize": 4, "MessageQueueSize": 10000, "BatchSize": 100, "MemoryThresholdMB": 2048, "CpuThresholdPercent": 80}, "Timeouts": {"HttpRequestTimeoutSeconds": 30, "DatabaseTimeoutSeconds": 30, "WorkflowExecutionTimeoutMinutes": 60, "NodeExecutionTimeoutSeconds": 300, "MessageProcessingTimeoutSeconds": 10, "HealthCheckTimeoutSeconds": 5, "ShutdownTimeoutSeconds": 30}, "Node": {"NodeId": "flowcustom-test-api-001", "NodeName": "FlowCustom Testing API Node", "IpAddress": "0.0.0.0", "Roles": ["Api", "Worker"], "Labels": {"environment": "testing", "version": "v0.0.1.8", "cluster": "docker"}, "MaxMemoryMB": 1024, "MaxCpuPercent": 80, "HeartbeatIntervalSeconds": 30, "EnableAutoDiscovery": true}, "Monitoring": {"Enabled": true, "MetricsCollectionIntervalSeconds": 60, "HealthCheckIntervalSeconds": 30, "LogLevel": "Information", "EnablePerformanceCounters": true, "EnableDistributedTracing": false, "TracingSampleRate": 0.1, "MetricsRetentionDays": 30}}, "MessagingTopics": {"RootPrefix": "flowcustom-test", "Cluster": {"Root": "cluster", "NodeHeartbeat": "nodes.{0}", "ServiceDiscovery": "discovery", "Config": "config", "NodeRegister": "register", "NodeUnregister": "unregister"}, "Workflows": {"Root": "workflows", "Events": "{0}.events", "State": "{0}.state", "Execution": "{0}.execution.{1}"}, "Nodes": {"Root": "nodes", "Tasks": "{0}.tasks", "Health": "{0}.health", "Status": "{0}.status"}, "Tasks": {"Root": "tasks", "HighPriority": "high", "NormalPriority": "normal", "LowPriority": "low"}, "Roles": {"Designer": "designer", "Validator": "validator", "Executor": "executor"}, "Ui": {"Root": "ui", "Updates": "updates", "Notifications": "notifications", "Events": "events"}, "Monitoring": {"Root": "monitoring", "Metrics": "metrics", "Health": "health", "Alerts": "alerts", "Logs": "logs"}}, "Database": {"Provider": "MySQL", "ConnectionString": "Server=mysql;Port=23306;Database=flowcustom_test;Uid=flowcustom;Pwd=FlowCustom@2025;"}, "Nats": {"Servers": ["nats://nats-server-1:24222", "nats://nats-server-2:24223", "nats://nats-server-3:24224"], "ConnectionName": "FlowCustomV1-Test-Api", "Username": "<PERSON><PERSON><PERSON>", "Password": "flowcustom_password", "ConnectionTimeoutSeconds": 30, "ReconnectIntervalSeconds": 5, "MaxReconnectAttempts": 10, "EnableAutoReconnect": true, "PingIntervalSeconds": 120, "MaxPingsOutstanding": 2, "EnableVerboseLogging": false, "JetStream": {"Enabled": true, "Domain": "flowcustom-test-cluster", "ApiPrefix": "$JS.API", "DefaultStream": {"Name": "FLOWCUSTOM_TEST", "Subjects": ["flowcustom-test.>"], "Storage": "file", "MaxMessages": 1000000, "MaxBytes": **********, "MaxAgeSeconds": 86400, "Replicas": 3}}, "ConnectionPool": {"Enabled": true, "MinConnections": 1, "MaxConnections": 10, "IdleTimeoutSeconds": 300, "AcquireTimeoutSeconds": 10}, "Serialization": {"Type": "json", "EnableCompression": false, "CompressionType": "gzip", "Json": {"UseCamelCase": true, "IgnoreNullValues": true, "WriteIndented": false, "DateTimeFormat": "yyyy-MM-ddTHH:mm:ss.fffZ"}}}, "NodeDiscovery": {"ClusterName": "FlowCustomV1-Test", "HeartbeatIntervalSeconds": 30, "NodeTimeoutSeconds": 120, "NodeCleanupIntervalSeconds": 60, "DiscoveryTimeoutSeconds": 5, "EnableAutoRegistration": true, "EnableHeartbeat": true, "EnableNodeCleanup": true, "MaxRetryAttempts": 3, "RetryIntervalSeconds": 5, "NodeRole": "Api", "NodeTags": ["api", "worker", "testing", "docker"], "CapabilityTags": ["workflow-execution", "api-gateway", "http-api"], "EnableVerboseLogging": false}}