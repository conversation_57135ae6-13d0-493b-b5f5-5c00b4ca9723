using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using FlowCustomV1.Core.Interfaces;
using FlowCustomV1.Core.Interfaces.Repositories;
using FlowCustomV1.Engine;
using FlowCustomV1.Infrastructure.Services;
using FlowCustomV1.Infrastructure;

namespace FlowCustomV1.Integration.Tests;

/// <summary>
/// 集成测试基类
/// </summary>
public abstract class TestBase : IDisposable
{
    protected IServiceProvider ServiceProvider { get; private set; }
    protected IConfiguration Configuration { get; private set; }
    protected IWorkflowEngine WorkflowEngine { get; private set; }
    protected IWorkflowRepository WorkflowRepository { get; private set; }
    protected IExecutionRepository ExecutionRepository { get; private set; }
    protected IDatabaseInitializationService DatabaseInitializationService { get; private set; }

    protected TestBase()
    {
        // 构建配置
        Configuration = new ConfigurationBuilder()
            .AddJsonFile("appsettings.test.json", optional: false)
            .Build();

        // 构建服务容器
        var services = new ServiceCollection();
        ConfigureServices(services);
        ServiceProvider = services.BuildServiceProvider();

        // 获取核心服务
        WorkflowEngine = ServiceProvider.GetRequiredService<IWorkflowEngine>();
        WorkflowRepository = ServiceProvider.GetRequiredService<IWorkflowRepository>();
        ExecutionRepository = ServiceProvider.GetRequiredService<IExecutionRepository>();
        DatabaseInitializationService = ServiceProvider.GetRequiredService<IDatabaseInitializationService>();

        // 初始化数据库
        InitializeDatabaseAsync().GetAwaiter().GetResult();

        // 启动工作流引擎
        StartWorkflowEngineAsync().GetAwaiter().GetResult();
    }

    /// <summary>
    /// 配置服务
    /// </summary>
    /// <param name="services">服务集合</param>
    protected virtual void ConfigureServices(IServiceCollection services)
    {
        // 添加日志记录
        services.AddLogging(builder =>
        {
            builder.AddConfiguration(Configuration.GetSection("Logging"));
            builder.AddConsole();
            builder.AddDebug();
        });

        // 使用内存数据库进行测试
        var databaseName = $"TestDatabase_{Guid.NewGuid():N}";
        services.AddWorkflowEngineInMemory();

        // 手动添加Infrastructure服务（因为Engine层不再直接依赖）
        services.AddInMemoryDatabase(databaseName);
    }

    /// <summary>
    /// 初始化数据库
    /// </summary>
    /// <returns>任务</returns>
    private async Task InitializeDatabaseAsync()
    {
        var success = await DatabaseInitializationService.InitializeDatabaseAsync();
        if (!success)
        {
            throw new InvalidOperationException("Failed to initialize test database");
        }
    }

    /// <summary>
    /// 启动工作流引擎
    /// </summary>
    /// <returns>任务</returns>
    private async Task StartWorkflowEngineAsync()
    {
        await WorkflowEngine.StartAsync();
    }

    /// <summary>
    /// 清理测试数据
    /// </summary>
    /// <returns>任务</returns>
    protected virtual async Task CleanupAsync()
    {
        try
        {
            // 清理测试数据库文件
            var connectionString = Configuration.GetConnectionString("DefaultConnection") ?? "Data Source=test_workflow.db";
            var dbFile = ExtractDbFileFromConnectionString(connectionString);
            
            if (!string.IsNullOrEmpty(dbFile) && File.Exists(dbFile))
            {
                File.Delete(dbFile);
            }

            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            // 忽略清理错误
            Console.WriteLine($"Cleanup error: {ex.Message}");
        }
    }

    /// <summary>
    /// 从连接字符串中提取数据库文件路径
    /// </summary>
    /// <param name="connectionString">连接字符串</param>
    /// <returns>文件路径</returns>
    private static string? ExtractDbFileFromConnectionString(string connectionString)
    {
        var parts = connectionString.Split(';');
        foreach (var part in parts)
        {
            var keyValue = part.Split('=');
            if (keyValue.Length == 2 && 
                keyValue[0].Trim().Equals("Data Source", StringComparison.OrdinalIgnoreCase))
            {
                return keyValue[1].Trim();
            }
        }
        return null;
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public virtual void Dispose()
    {
        try
        {
            // 停止工作流引擎
            WorkflowEngine?.StopAsync().GetAwaiter().GetResult();

            CleanupAsync().GetAwaiter().GetResult();
            if (ServiceProvider is IDisposable disposable)
            {
                disposable.Dispose();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Dispose error: {ex.Message}");
        }
    }
}
