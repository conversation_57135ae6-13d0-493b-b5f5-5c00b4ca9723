# FlowCustomV1 代码规范和最佳实践

## 📋 文档信息

| 文档信息 | 详细内容 |
|---------|---------|
| **项目名称** | FlowCustomV1 工作流自动化系统 |
| **文档类型** | 代码规范和最佳实践 |
| **文档版本** | v1.0 |
| **创建日期** | 2025-08-18 |
| **适用范围** | 所有开发人员 |
| **强制执行** | 是 |

---

## 🎯 规范目标

### 核心目标
1. **代码一致性** - 统一的代码风格和结构
2. **可读性** - 代码易于理解和维护
3. **可维护性** - 降低维护成本和复杂度
4. **质量保证** - 减少缺陷和提升代码质量

### 执行原则
- **强制执行** - 所有代码必须遵循规范
- **工具辅助** - 使用工具自动检查和格式化
- **持续改进** - 根据实践经验不断优化规范
- **团队共识** - 团队成员共同遵守和维护

---

## 🏗️ 架构规范

### 清洁架构原则
```
┌─────────────────────────────────────┐
│           Presentation Layer        │  ← Controllers, Views
├─────────────────────────────────────┤
│           Application Layer         │  ← Use Cases, Services
├─────────────────────────────────────┤
│             Domain Layer            │  ← Entities, Value Objects
├─────────────────────────────────────┤
│          Infrastructure Layer       │  ← Data Access, External Services
└─────────────────────────────────────┘
```

### 依赖规则
1. **内层不依赖外层** - Domain层不能依赖Application层
2. **接口在内层定义** - 仓储接口在Domain层定义
3. **实现在外层** - 仓储实现在Infrastructure层
4. **依赖注入** - 通过DI容器管理依赖关系

### 项目结构规范
```
FlowCustom.Domain/
├── Entities/           # 实体类
├── ValueObjects/       # 值对象
├── Interfaces/         # 领域接口
├── Services/          # 领域服务
└── Events/            # 领域事件

FlowCustom.Application/
├── UseCases/          # 用例
├── Services/          # 应用服务
├── DTOs/              # 数据传输对象
├── Interfaces/        # 应用接口
└── Validators/        # 验证器

FlowCustom.Infrastructure/
├── Data/              # 数据访问
├── Repositories/      # 仓储实现
├── Services/          # 基础设施服务
└── Configuration/     # 配置

FlowCustom.Presentation/
├── Controllers/       # API控制器
├── Models/           # 视图模型
├── Filters/          # 过滤器
└── Middleware/       # 中间件
```

---

## 📝 C# 编码规范

### 命名规范

#### 类和接口命名
```csharp
// ✅ 正确 - PascalCase
public class WorkflowDefinition { }
public interface IWorkflowRepository { }

// ❌ 错误
public class workflowDefinition { }
public interface workflowRepository { }
```

#### 方法和属性命名
```csharp
// ✅ 正确 - PascalCase
public string WorkflowName { get; set; }
public async Task<WorkflowInstance> ExecuteWorkflowAsync(Guid workflowId)

// ❌ 错误
public string workflowName { get; set; }
public async Task<WorkflowInstance> executeWorkflow(Guid workflowId)
```

#### 字段和变量命名
```csharp
// ✅ 正确 - camelCase
private readonly IWorkflowRepository _workflowRepository;
private string workflowName;
public const int MaxRetryCount = 3;

// ❌ 错误
private readonly IWorkflowRepository WorkflowRepository;
private string WorkflowName;
public const int maxRetryCount = 3;
```

### 代码格式规范

#### 缩进和空格
```csharp
// ✅ 正确 - 4个空格缩进
public class WorkflowService
{
    private readonly IWorkflowRepository _repository;
    
    public WorkflowService(IWorkflowRepository repository)
    {
        _repository = repository ?? throw new ArgumentNullException(nameof(repository));
    }
}
```

#### 大括号规范
```csharp
// ✅ 正确 - 大括号独占一行
if (condition)
{
    DoSomething();
}
else
{
    DoSomethingElse();
}

// ❌ 错误
if (condition) {
    DoSomething();
} else {
    DoSomethingElse();
}
```

### 注释规范

#### XML文档注释
```csharp
/// <summary>
/// 执行指定的工作流实例
/// </summary>
/// <param name="workflowId">工作流定义ID</param>
/// <param name="input">输入参数</param>
/// <param name="cancellationToken">取消令牌</param>
/// <returns>工作流执行结果</returns>
/// <exception cref="WorkflowNotFoundException">当工作流不存在时抛出</exception>
public async Task<WorkflowExecutionResult> ExecuteWorkflowAsync(
    Guid workflowId,
    Dictionary<string, object> input,
    CancellationToken cancellationToken = default)
{
    // 实现代码
}
```

#### 代码注释
```csharp
// ✅ 正确 - 解释为什么，不是做什么
// 使用重试机制处理临时网络故障
for (int i = 0; i < MaxRetryCount; i++)
{
    try
    {
        return await CallExternalService();
    }
    catch (HttpRequestException) when (i < MaxRetryCount - 1)
    {
        await Task.Delay(TimeSpan.FromSeconds(Math.Pow(2, i)));
    }
}

// ❌ 错误 - 重复代码逻辑
// 循环3次
for (int i = 0; i < 3; i++)
{
    // 调用外部服务
    return await CallExternalService();
}
```

---

## 🔧 最佳实践

### 异步编程
```csharp
// ✅ 正确 - 使用ConfigureAwait(false)
public async Task<WorkflowInstance> GetWorkflowAsync(Guid id)
{
    var workflow = await _repository.GetByIdAsync(id).ConfigureAwait(false);
    return workflow;
}

// ✅ 正确 - 异步方法命名以Async结尾
public async Task<bool> ValidateWorkflowAsync(WorkflowDefinition workflow)

// ❌ 错误 - 同步调用异步方法
public WorkflowInstance GetWorkflow(Guid id)
{
    return _repository.GetByIdAsync(id).Result; // 可能导致死锁
}
```

### 异常处理
```csharp
// ✅ 正确 - 具体的异常类型
public class WorkflowNotFoundException : Exception
{
    public WorkflowNotFoundException(Guid workflowId) 
        : base($"Workflow with ID {workflowId} was not found.")
    {
        WorkflowId = workflowId;
    }
    
    public Guid WorkflowId { get; }
}

// ✅ 正确 - 适当的异常处理
try
{
    var result = await ExecuteWorkflowAsync(workflowId);
    return result;
}
catch (WorkflowNotFoundException ex)
{
    _logger.LogWarning("Workflow {WorkflowId} not found", ex.WorkflowId);
    throw;
}
catch (Exception ex)
{
    _logger.LogError(ex, "Unexpected error executing workflow {WorkflowId}", workflowId);
    throw new WorkflowExecutionException("Failed to execute workflow", ex);
}
```

### 依赖注入
```csharp
// ✅ 正确 - 构造函数注入
public class WorkflowService
{
    private readonly IWorkflowRepository _repository;
    private readonly ILogger<WorkflowService> _logger;
    
    public WorkflowService(
        IWorkflowRepository repository,
        ILogger<WorkflowService> logger)
    {
        _repository = repository ?? throw new ArgumentNullException(nameof(repository));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }
}

// ✅ 正确 - 服务注册
services.AddScoped<IWorkflowService, WorkflowService>();
services.AddScoped<IWorkflowRepository, WorkflowRepository>();
```

### 日志记录
```csharp
// ✅ 正确 - 结构化日志
_logger.LogInformation("Starting workflow execution for {WorkflowId} with {InputCount} parameters", 
    workflowId, input.Count);

_logger.LogError(ex, "Failed to execute workflow {WorkflowId} at node {NodeId}", 
    workflowId, currentNodeId);

// ❌ 错误 - 字符串拼接
_logger.LogInformation("Starting workflow execution for " + workflowId);
```

---

## 🧪 测试规范

### 单元测试命名
```csharp
// ✅ 正确 - 方法名_场景_期望结果
[Test]
public async Task ExecuteWorkflowAsync_WithValidWorkflow_ReturnsSuccessResult()

[Test]
public async Task ExecuteWorkflowAsync_WithInvalidWorkflow_ThrowsValidationException()

[Test]
public async Task ExecuteWorkflowAsync_WhenRepositoryThrows_PropagatesException()
```

### 测试结构 (AAA模式)
```csharp
[Test]
public async Task ExecuteWorkflowAsync_WithValidWorkflow_ReturnsSuccessResult()
{
    // Arrange
    var workflowId = Guid.NewGuid();
    var workflow = new WorkflowDefinition { Id = workflowId, Name = "Test Workflow" };
    var input = new Dictionary<string, object> { { "key", "value" } };
    
    _mockRepository.Setup(r => r.GetByIdAsync(workflowId))
        .ReturnsAsync(workflow);
    
    // Act
    var result = await _workflowService.ExecuteWorkflowAsync(workflowId, input);
    
    // Assert
    Assert.That(result.IsSuccess, Is.True);
    Assert.That(result.WorkflowId, Is.EqualTo(workflowId));
    _mockRepository.Verify(r => r.GetByIdAsync(workflowId), Times.Once);
}
```

---

## 📊 代码质量指标

### 复杂度控制
- **圈复杂度** ≤ 10
- **方法长度** ≤ 50行
- **类长度** ≤ 500行
- **参数个数** ≤ 5个

### 测试覆盖率
- **单元测试覆盖率** ≥ 80%
- **集成测试覆盖率** ≥ 60%
- **关键路径覆盖率** = 100%

### 代码重复
- **重复代码率** ≤ 5%
- **重复方法** = 0
- **魔法数字** = 0

---

## 🛠️ 工具配置

### EditorConfig (.editorconfig)
```ini
root = true

[*]
charset = utf-8
end_of_line = crlf
insert_final_newline = true
trim_trailing_whitespace = true

[*.cs]
indent_style = space
indent_size = 4
```

### StyleCop 规则
```xml
<PropertyGroup>
  <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
  <CodeAnalysisRuleSet>stylecop.ruleset</CodeAnalysisRuleSet>
</PropertyGroup>
```

### SonarQube 质量门禁
- **可靠性评级** ≥ A
- **安全评级** ≥ A
- **可维护性评级** ≥ A
- **覆盖率** ≥ 80%
- **重复率** ≤ 3%

---

## 🔍 代码审查清单

### 功能性检查
- [ ] 代码实现符合需求
- [ ] 边界条件处理正确
- [ ] 异常处理完善
- [ ] 性能考虑充分

### 代码质量检查
- [ ] 命名规范符合标准
- [ ] 代码结构清晰
- [ ] 注释充分准确
- [ ] 无重复代码

### 安全性检查
- [ ] 输入验证充分
- [ ] 敏感信息保护
- [ ] 权限检查正确
- [ ] SQL注入防护

### 测试检查
- [ ] 单元测试充分
- [ ] 测试用例覆盖全面
- [ ] 测试数据合理
- [ ] 测试可维护

---

## 📋 违规处理

### 违规等级
1. **严重违规** - 影响系统安全或稳定性
2. **一般违规** - 影响代码质量或可维护性
3. **轻微违规** - 格式或命名不规范

### 处理流程
1. **自动检查** - CI/CD流水线自动检查
2. **代码审查** - 人工审查发现问题
3. **修复要求** - 要求开发者修复
4. **重新审查** - 修复后重新审查

---

**规范版本**: v1.0
**最后更新**: 2025-08-18
**下次评估**: v0.1.0完成后
**维护责任**: 技术负责人
