import React, { useState } from 'react';
import { Drawer, Button, Space, Select, message, Divider, Typography, InputNumber } from 'antd';
import { SettingOutlined, ReloadOutlined, BugOutlined } from '@ant-design/icons';

const { Option } = Select;
const { Text, Title } = Typography;

interface LayoutConfigPanelProps {
  visible: boolean;
  onClose: () => void;
}

/**
 * 纯CSS版本的布局配置面板
 *
 * 通过修改CSS变量实现实时布局调整
 * 无需JavaScript配置文件
 */
const LayoutConfigPanel: React.FC<LayoutConfigPanelProps> = ({ visible, onClose }) => {
  const [currentPreset, setCurrentPreset] = useState('standard');

  // 获取根元素
  const getRootElement = () => document.documentElement;

  // 设置CSS变量
  const setCSSVariable = (name: string, value: string) => {
    getRootElement().style.setProperty(name, value);
  };

  // 获取CSS变量
  const getCSSVariable = (name: string) => {
    return getComputedStyle(getRootElement()).getPropertyValue(name).trim();
  };

  // 应用预设配置
  const applyPreset = (presetName: string) => {
    // 移除所有预设类
    document.body.classList.remove('layout-preset-compact', 'layout-preset-standard', 'layout-preset-spacious');

    // 添加新的预设类
    document.body.classList.add(`layout-preset-${presetName}`);

    setCurrentPreset(presetName);
    message.success(`已应用${presetName}布局预设`);
  };

  // 重置为标准配置
  const resetConfig = () => {
    applyPreset('standard');
  };

  // 调试模式已移除
  const toggleDebug = () => {
    message.info('调试模式已移除，如需调试请使用浏览器开发者工具');
  };

  // 快速调整函数
  const quickAdjust = {
    tableHeight: (size: 'small' | 'medium' | 'large') => {
      document.body.classList.remove('table-height-small', 'table-height-medium', 'table-height-large');
      document.body.classList.add(`table-height-${size}`);
      message.success(`表格高度已调整为${size}`);
    },
    spacing: (size: 'tight' | 'normal' | 'loose') => {
      document.body.classList.remove('spacing-tight', 'spacing-normal', 'spacing-loose');
      document.body.classList.add(`spacing-${size}`);
      message.success(`间距已调整为${size}`);
    },
    toolbarMargin: (size: 'small' | 'medium' | 'large') => {
      document.body.classList.remove('toolbar-margin-small', 'toolbar-margin-medium', 'toolbar-margin-large');
      document.body.classList.add(`toolbar-margin-${size}`);
      message.success(`工具栏边距已调整为${size}`);
    }
  };

  // 自定义CSS变量调整
  const adjustVariable = (variable: string, value: number, unit: string = 'px') => {
    setCSSVariable(variable, `${value}${unit}`);
    message.success(`${variable} 已调整为 ${value}${unit}`);
  };

  return (
    <Drawer
      title={
        <Space>
          <SettingOutlined />
          <span>布局配置 - 纯CSS版本</span>
        </Space>
      }
      placement="right"
      width={400}
      open={visible}
      onClose={onClose}
      extra={
        <Space>
          <Button icon={<BugOutlined />} onClick={toggleDebug}>
            调试
          </Button>
          <Button icon={<ReloadOutlined />} onClick={resetConfig}>
            重置
          </Button>
        </Space>
      }
    >
      <div style={{ padding: '16px 0' }}>
        {/* 预设配置 */}
        <Title level={5}>🎨 快速预设</Title>
        <Space.Compact style={{ width: '100%', marginBottom: '16px' }}>
          <Button
            type={currentPreset === 'compact' ? 'primary' : 'default'}
            onClick={() => applyPreset('compact')}
            style={{ flex: 1 }}
          >
            紧凑
          </Button>
          <Button
            type={currentPreset === 'standard' ? 'primary' : 'default'}
            onClick={() => applyPreset('standard')}
            style={{ flex: 1 }}
          >
            标准
          </Button>
          <Button
            type={currentPreset === 'spacious' ? 'primary' : 'default'}
            onClick={() => applyPreset('spacious')}
            style={{ flex: 1 }}
          >
            宽松
          </Button>
        </Space.Compact>

        <Divider />

        {/* 快速调整 */}
        <Title level={5}>⚡ 快速调整</Title>

        <div style={{ marginBottom: '16px' }}>
          <Text strong>表格高度：</Text>
          <br />
          <Space.Compact style={{ width: '100%', marginTop: '8px' }}>
            <Button onClick={() => quickAdjust.tableHeight('small')} style={{ flex: 1 }}>
              小
            </Button>
            <Button onClick={() => quickAdjust.tableHeight('medium')} style={{ flex: 1 }}>
              中
            </Button>
            <Button onClick={() => quickAdjust.tableHeight('large')} style={{ flex: 1 }}>
              大
            </Button>
          </Space.Compact>
        </div>

        <div style={{ marginBottom: '16px' }}>
          <Text strong>间距大小：</Text>
          <br />
          <Space.Compact style={{ width: '100%', marginTop: '8px' }}>
            <Button onClick={() => quickAdjust.spacing('tight')} style={{ flex: 1 }}>
              紧凑
            </Button>
            <Button onClick={() => quickAdjust.spacing('normal')} style={{ flex: 1 }}>
              正常
            </Button>
            <Button onClick={() => quickAdjust.spacing('loose')} style={{ flex: 1 }}>
              宽松
            </Button>
          </Space.Compact>
        </div>

        <div style={{ marginBottom: '16px' }}>
          <Text strong>工具栏边距：</Text>
          <br />
          <Space.Compact style={{ width: '100%', marginTop: '8px' }}>
            <Button onClick={() => quickAdjust.toolbarMargin('small')} style={{ flex: 1 }}>
              小
            </Button>
            <Button onClick={() => quickAdjust.toolbarMargin('medium')} style={{ flex: 1 }}>
              中
            </Button>
            <Button onClick={() => quickAdjust.toolbarMargin('large')} style={{ flex: 1 }}>
              大
            </Button>
          </Space.Compact>
        </div>

        <Divider />

        {/* 精确调整 */}
        <Title level={5}>🔧 精确调整</Title>

        <div style={{ marginBottom: '16px' }}>
          <Text strong>头部高度：</Text>
          <InputNumber
            min={48}
            max={100}
            defaultValue={64}
            addonAfter="px"
            style={{ width: '100%', marginTop: '8px' }}
            onChange={(value) => adjustVariable('--layout-header-height', value || 64)}
          />
        </div>

        <div style={{ marginBottom: '16px' }}>
          <Text strong>内容边距：</Text>
          <InputNumber
            min={0}
            max={32}
            defaultValue={8}
            addonAfter="px"
            style={{ width: '100%', marginTop: '8px' }}
            onChange={(value) => adjustVariable('--layout-content-padding', value || 8)}
          />
        </div>

        <div style={{ marginBottom: '16px' }}>
          <Text strong>表格偏移：</Text>
          <InputNumber
            min={200}
            max={500}
            defaultValue={320}
            addonAfter="px"
            style={{ width: '100%', marginTop: '8px' }}
            onChange={(value) => adjustVariable('--layout-table-offset', value || 320)}
          />
        </div>

        <Divider />

        {/* 使用说明 */}
        <div style={{
          padding: '12px',
          background: '#f0f9ff',
          borderRadius: '6px',
          fontSize: '12px',
          color: '#0369a1',
          border: '1px solid #bae6fd'
        }}>
          <Text strong style={{ color: '#0369a1' }}>💡 纯CSS配置说明：</Text>
          <br />• 预设配置：通过CSS类快速切换布局风格
          <br />• 快速调整：常用参数的一键调整
          <br />• 精确调整：实时修改CSS变量
          <br />• 调试模式：显示容器边框便于调试
          <br />• 配置文件：<code>src/styles/layout-config.css</code>
        </div>
      </div>
    </Drawer>
  );
};

export default LayoutConfigPanel;
