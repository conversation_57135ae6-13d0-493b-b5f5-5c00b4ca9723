# FlowCustomV1 独立开发流程控制规范

## 📋 文档信息

| 项目信息 | 详细内容 |
|---------|---------|
| **项目名称** | FlowCustomV1 工作流自动化系统 |
| **起始版本** | v0.0.0.1 (从零开始) |
| **文档类型** | 独立开发流程控制规范 |
| **创建日期** | 2025-08-17 |
| **适用范围** | 独立开发者 + Augment AI助手 |
| **开发模式** | 敏捷迭代 + AI辅助开发 |

---

## 🎯 第一章：独立开发流程设计

### 1.1 核心设计原则

#### 1.1.1 AI辅助开发原则
- **禁止临时性代码**：严禁为了通过编译而创建临时函数和数据类型
- **架构优先**：每个功能都必须先设计架构，再实现代码
- **渐进式构建**：从最小可用版本开始，逐步添加功能
- **代码质量控制**：宁可编译失败，也不允许低质量代码
- **文档驱动开发**：先写设计文档，再写代码实现

#### 1.1.2 独立开发约束
- **单人决策**：所有技术决策由开发者本人做出
- **AI助手角色**：Augment仅作为编码助手，不做架构决策
- **版本控制严格**：每个提交都必须是完整功能，不允许半成品
- **质量门禁**：每个版本都必须通过完整的质量检查
- **可回滚性**：任何时候都能回滚到上一个稳定版本

### 1.2 敏捷迭代模型

#### 1.2.1 微迭代周期 (3-5天)
采用**超短迭代周期**，确保每个迭代都有可交付成果：

```
┌─────────────────────────────────────────────────────────────┐
│                    需求分析层 (1小时)                        │
│              明确目标 → 设计架构 → 定义接口                  │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    设计验证层 (2小时)                        │
│            架构审查 → 接口设计 → 数据模型 → 依赖分析          │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    AI辅助开发层 (主要时间)                   │
│        严格按设计编码 → 禁止临时代码 → 完整实现              │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    质量验证层 (1小时)                        │
│      功能测试 → 代码审查 → 文档更新 → 版本提交              │
└─────────────────────────────────────────────────────────────┘
```

#### 1.2.2 单日工作流程
**上午 (4小时)：设计与规划**
- 30分钟：回顾昨日成果和今日目标
- 90分钟：详细设计当日要实现的功能
- 60分钟：定义接口、数据结构和依赖关系
- 30分钟：与Augment确认开发计划

**下午 (4小时)：AI辅助开发**
- 180分钟：严格按设计实现代码
- 30分钟：代码自审和质量检查
- 30分钟：测试验证和问题修复

**晚上 (1小时)：总结与规划**
- 30分钟：更新文档和提交代码
- 30分钟：规划明日工作和风险识别

### 1.3 AI代码质量控制机制

#### 1.3.1 三级质量门禁 (独立开发版)
```
设计完成 → [门禁1] → AI编码 → [门禁2] → 功能验证 → [门禁3] → 版本提交
```

**门禁1：设计完整性检查**
- [ ] 功能需求明确定义
- [ ] 接口设计完整无歧义
- [ ] 数据模型设计合理
- [ ] 依赖关系清晰
- [ ] 无临时性设计决策

**门禁2：代码质量检查**
- [ ] 严格按设计实现，无偏差
- [ ] 无临时性函数和数据类型
- [ ] 无为了编译通过的妥协代码
- [ ] 代码结构清晰，命名规范
- [ ] 错误处理完整

**门禁3：功能完整性验证**
- [ ] 功能完全按设计实现
- [ ] 基本测试用例通过
- [ ] 文档更新完成
- [ ] 版本号正确递增
- [ ] 可独立运行或集成

#### 1.3.2 AI编码约束规则

**严禁行为清单**：
❌ 创建临时性的占位符函数
❌ 定义未经设计的数据类型
❌ 为了编译通过而添加空实现
❌ 使用硬编码值替代配置
❌ 跳过错误处理逻辑
❌ 创建未在设计中定义的依赖关系

**必须行为清单**：
✅ 严格按照接口设计实现
✅ 完整实现所有设计的功能点
✅ 添加适当的错误处理和日志
✅ 使用设计中定义的数据类型
✅ 遵循既定的命名和编码规范
✅ 编译失败时停止并报告问题

---

## 🔄 第二章：独立开发版本控制策略

### 2.1 简化Git工作流

#### 2.1.1 单人分支策略
```
main (主分支，始终可运行)
├── v0.0.0.1 (第一个版本标签)
├── v0.0.0.2 (第二个版本标签)
├── v0.0.0.x (持续迭代版本)
└── dev/[功能名] (临时开发分支，完成后立即合并删除)
```

**分支使用原则**：
- **main分支**：始终保持可编译、可运行状态
- **开发分支**：每个功能创建临时分支，完成后立即合并
- **无长期分支**：避免长期维护多个分支的复杂性
- **标签管理**：每个稳定版本都打标签，便于回滚

#### 2.1.2 版本号规则 (从v0.0.0.1开始)
```
v0.0.0.1 - 项目初始化，基础架构搭建
v0.0.0.2 - 第一个核心模块实现
v0.0.0.3 - 第二个核心模块实现
...
v0.0.1.0 - 第一个里程碑版本 (10个基础功能完成)
v0.0.2.0 - 第二个里程碑版本
...
v0.1.0.0 - 第一个可用版本 (MVP)
v1.0.0.0 - 第一个正式版本
```

**版本递增规则**：
- **第4位数字**：每日迭代版本 (0.0.0.1 → 0.0.0.2)
- **第3位数字**：功能模块完成 (0.0.0.x → 0.0.1.0)
- **第2位数字**：重要里程碑 (0.0.x.0 → 0.1.0.0)
- **第1位数字**：正式发布版本 (0.x.0.0 → 1.0.0.0)

### 2.2 简化提交规范

#### 2.2.1 提交消息格式 (独立开发版)
```
v0.0.0.x: [模块] 功能描述

详细说明：
- 具体实现内容
- 重要技术决策
- 已知问题或限制

质量检查：
✅ 编译通过
✅ 基本功能测试通过
✅ 无临时性代码
✅ 文档已更新
```

**提交类型简化**:
- `v0.0.0.x: [Core]` - 核心架构和基础设施
- `v0.0.0.x: [Engine]` - 工作流引擎相关
- `v0.0.0.x: [Plugin]` - 插件系统相关
- `v0.0.0.x: [API]` - API接口相关
- `v0.0.0.x: [UI]` - 用户界面相关
- `v0.0.0.x: [Fix]` - 问题修复
- `v0.0.0.x: [Doc]` - 文档更新

**示例提交消息**:
```
v0.0.0.5: [Core] 实现基础项目结构和依赖注入

详细说明：
- 创建清洁架构的项目结构
- 配置依赖注入容器
- 实现基础接口定义
- 添加日志和配置管理

质量检查：
✅ 编译通过
✅ 项目结构符合设计
✅ 无临时性代码
✅ README文档已更新
```

### 2.3 版本发布流程

#### 2.3.1 语义化版本控制
采用 `MAJOR.MINOR.PATCH-LABEL` 格式：
- **MAJOR**: 不兼容的API变更
- **MINOR**: 向后兼容的功能新增
- **PATCH**: 向后兼容的缺陷修复
- **LABEL**: 预发布标识 (alpha, beta, rc)

**版本示例**:
- `v0.9.7`: 当前开发版本
- `v0.9.8-alpha.1`: 第一个Alpha版本
- `v0.9.8-beta.2`: 第二个Beta版本
- `v0.9.8-rc.1`: 第一个候选版本
- `v0.9.8`: 正式发布版本

#### 2.3.2 发布检查清单
**预发布检查**:
- [ ] 所有功能开发完成
- [ ] 代码审查100%通过
- [ ] 测试覆盖率达标
- [ ] 性能测试通过
- [ ] 安全扫描通过
- [ ] 文档更新完成
- [ ] 变更日志准备完成

**发布执行**:
- [ ] 创建release分支
- [ ] 执行自动化测试套件
- [ ] 构建发布包
- [ ] 部署到测试环境验证
- [ ] 创建Git标签
- [ ] 合并到master分支
- [ ] 部署到生产环境

**发布后验证**:
- [ ] 生产环境功能验证
- [ ] 性能指标监控
- [ ] 错误日志检查
- [ ] 用户反馈收集

---

## 📊 第三章：代码质量标准

### 3.1 代码审查规范

#### 3.1.1 审查流程
```
开发者提交PR → 自动化检查 → 同行审查 → 架构师审查 → 合并到目标分支
```

**自动化检查项**:
- 代码编译通过
- 单元测试通过
- 代码覆盖率检查
- 静态代码分析
- 安全漏洞扫描
- 代码格式检查

**人工审查重点**:
- 架构一致性
- 代码可读性
- 性能影响
- 安全考虑
- 错误处理
- 文档完整性

#### 3.1.2 审查标准
**必须修复 (Must Fix)**:
- 功能缺陷
- 安全漏洞
- 性能问题
- 架构违反

**建议修复 (Should Fix)**:
- 代码重复
- 命名不规范
- 注释不足
- 测试不充分

**可选修复 (Could Fix)**:
- 代码风格
- 性能优化
- 重构建议

### 3.2 测试策略

#### 3.2.1 测试金字塔
```
        E2E Tests (5%)
       ┌─────────────┐
      │  集成测试 (15%) │
     └─────────────────┘
    ┌───────────────────────┐
   │    单元测试 (80%)      │
  └─────────────────────────┘
```

**单元测试要求**:
- 覆盖率 ≥ 80%
- 关键路径覆盖率 ≥ 95%
- 测试用例命名规范
- 测试数据独立性

**集成测试要求**:
- API接口测试
- 数据库集成测试
- 外部服务集成测试
- 消息传递测试

**端到端测试要求**:
- 关键业务流程测试
- 用户场景测试
- 性能基准测试
- 兼容性测试

### 3.3 静态代码分析

#### 3.3.1 分析工具配置
**SonarQube规则集**:
- 代码异味检测
- 安全漏洞扫描
- 代码重复度分析
- 复杂度分析
- 可维护性评估

**质量门禁标准**:
- 新增代码覆盖率 ≥ 80%
- 重复代码率 ≤ 3%
- 维护性评级 ≥ A
- 可靠性评级 ≥ A
- 安全性评级 ≥ A

---

## 🎯 第四章：项目管理规则

### 4.1 任务管理规范

#### 4.1.1 任务分类体系
**Epic (史诗)**:
- 大型功能模块
- 跨多个迭代
- 业务价值明确
- 可独立交付

**Story (用户故事)**:
- 单个功能点
- 1-2个迭代内完成
- 有明确验收标准
- 用户价值导向

**Task (任务)**:
- 技术实现细节
- 1-3天内完成
- 可独立执行
- 有明确产出

**Bug (缺陷)**:
- 功能异常
- 优先级分级
- 根因分析
- 回归验证

#### 4.1.2 优先级定义
**P0 - 紧急**: 系统崩溃、数据丢失、安全漏洞
**P1 - 高**: 核心功能异常、性能严重下降
**P2 - 中**: 一般功能异常、用户体验问题
**P3 - 低**: 优化建议、非关键功能

### 4.2 进度跟踪机制

#### 4.2.1 日常跟踪
**每日站会 (Daily Standup)**:
- 昨日完成工作
- 今日计划工作
- 遇到的阻碍
- 需要的帮助

**周度回顾 (Weekly Review)**:
- 迭代进度检查
- 质量指标回顾
- 风险状态更新
- 下周计划调整

#### 4.2.2 里程碑管理
**迭代里程碑**:
- 功能开发完成
- 测试验证通过
- 文档更新完成
- 部署准备就绪

**版本里程碑**:
- Alpha版本发布
- Beta版本发布
- RC版本发布
- 正式版本发布

### 4.3 风险控制策略

#### 4.3.1 风险识别矩阵
```
影响程度 \ 发生概率    低      中      高
高                  中风险   高风险   极高风险
中                  低风险   中风险   高风险
低                  极低风险  低风险   中风险
```

**技术风险**:
- 架构设计风险
- 技术选型风险
- 性能风险
- 安全风险

**项目风险**:
- 进度延期风险
- 资源不足风险
- 需求变更风险
- 质量风险

#### 4.3.2 应对策略
**预防措施**:
- 技术预研和验证
- 原型开发和测试
- 风险评估和规划
- 备选方案准备

**应急措施**:
- 快速响应机制
- 问题升级流程
- 资源调配方案
- 沟通协调机制

---

## 🤖 第三章：Augment AI工作指导规则

### 3.1 AI助手核心约束

#### 3.1.1 绝对禁止行为 (红线规则)
**🚫 编译导向的临时代码**:
- 禁止创建空的占位符方法
- 禁止返回null或默认值来通过编译
- 禁止创建未经设计的临时数据类型
- 禁止使用throw new NotImplementedException()

**🚫 架构破坏行为**:
- 禁止绕过既定的架构层次
- 禁止创建循环依赖
- 禁止直接访问不应该访问的层
- 禁止修改核心接口定义

**🚫 质量妥协行为**:
- 禁止为了快速完成而跳过错误处理
- 禁止使用硬编码替代配置
- 禁止忽略异常和错误情况
- 禁止创建无意义的变量名

#### 3.1.2 必须执行行为 (绿灯规则)
**✅ 设计优先原则**:
- 必须先理解完整的功能需求
- 必须先设计接口和数据结构
- 必须确认设计符合整体架构
- 必须获得开发者确认后再编码

**✅ 完整实现原则**:
- 必须完整实现设计的所有功能点
- 必须添加适当的错误处理
- 必须添加必要的日志记录
- 必须编写基本的测试用例

**✅ 质量保证原则**:
- 必须使用有意义的命名
- 必须添加必要的代码注释
- 必须遵循既定的编码规范
- 必须确保代码可读性和可维护性

### 3.2 AI工作流程规范

#### 3.2.1 标准工作流程
**第一步：需求理解确认**
1. 仔细阅读功能需求描述
2. 识别涉及的模块和组件
3. 确认技术实现方案
4. 向开发者确认理解是否正确

**第二步：设计方案制定**
1. 设计接口定义 (输入/输出/异常)
2. 设计数据结构和模型
3. 设计依赖关系和调用流程
4. 制定测试验证方案

**第三步：实现方案确认**
1. 向开发者展示设计方案
2. 确认方案符合整体架构
3. 确认没有临时性设计决策
4. 获得明确的实施许可

**第四步：严格按设计编码**
1. 严格按照确认的设计实现
2. 不允许任何偏离设计的修改
3. 遇到问题立即停止并报告
4. 完成后进行自我质量检查

#### 3.2.2 异常处理流程
**遇到编译错误时**:
1. 🛑 立即停止编码
2. 📋 分析错误的根本原因
3. 💭 思考正确的解决方案
4. 🗣️ 向开发者报告问题和建议方案
5. ⏳ 等待开发者决策后再继续

**遇到设计冲突时**:
1. 🛑 立即停止当前工作
2. 📝 详细描述冲突的具体情况
3. 💡 提供可能的解决方案选项
4. 🤝 与开发者讨论最佳解决方案
5. ✅ 确认解决方案后再继续

### 3.3 代码质量标准

#### 3.3.1 编码规范 (强制执行)
**命名规范**:
- 类名: PascalCase (如: WorkflowEngine)
- 接口名: IPascalCase (如: IWorkflowEngine)
- 方法名: PascalCase (如: ExecuteAsync)
- 变量名: camelCase (如: nodeExecutor)
- 常量名: PascalCase (如: MaxRetryCount)

**代码结构约束**:
- 每个类单一职责，职责明确
- 方法长度不超过30行 (独立开发更严格)
- 类长度不超过200行 (独立开发更严格)
- 嵌套层级不超过3层

**必须添加的注释**:
- 所有公共接口必须有XML文档注释
- 复杂算法必须有实现说明
- 重要业务逻辑必须有注释
- 所有TODO必须有明确的完成计划

#### 3.3.2 质量检查清单
**每次提交前必须检查**:
- [ ] 代码编译通过，无警告
- [ ] 所有方法都有明确的返回值处理
- [ ] 所有异常情况都有适当处理
- [ ] 没有硬编码的魔法数字或字符串
- [ ] 没有注释掉的代码
- [ ] 没有调试用的Console.WriteLine
- [ ] 变量名和方法名都有明确含义
- [ ] 没有重复的代码逻辑

**架构一致性检查**:
- [ ] 遵循依赖注入原则
- [ ] 遵循单一职责原则
- [ ] 遵循开闭原则
- [ ] 没有违反层次架构的调用
- [ ] 接口定义清晰，职责明确

### 5.3 文档维护规范

#### 5.3.1 文档类型
**技术文档**:
- API文档 (自动生成)
- 架构设计文档
- 数据库设计文档
- 部署运维文档

**用户文档**:
- 用户使用手册
- 功能说明文档
- 常见问题解答
- 最佳实践指南

#### 5.3.2 更新要求
**同步更新**:
- 代码变更必须同步更新文档
- API变更必须更新接口文档
- 配置变更必须更新配置说明
- 部署变更必须更新部署文档

**版本管理**:
- 文档要有版本号
- 重要变更要有变更日志
- 废弃功能要有迁移指南
- 新功能要有使用示例

### 5.4 问题处理规范

#### 5.4.1 问题分类
**功能问题**:
- 需求理解偏差
- 功能实现错误
- 业务逻辑缺陷
- 用户体验问题

**技术问题**:
- 架构设计问题
- 性能瓶颈问题
- 安全漏洞问题
- 兼容性问题

#### 5.4.2 处理流程
**问题识别**:
1. 收集问题信息
2. 重现问题场景
3. 分析根本原因
4. 评估影响范围

**解决方案**:
1. 制定修复方案
2. 评估方案风险
3. 实施解决方案
4. 验证修复效果

**预防措施**:
1. 总结经验教训
2. 完善测试用例
3. 更新开发规范
4. 分享知识经验

---

## 📋 第四章：项目启动实施指导

### 4.1 项目初始化计划

#### 4.1.1 第一周：基础设施搭建
**Day 1: 项目结构创建**
- 创建Git仓库
- 设计项目目录结构
- 创建基础的解决方案文件
- 配置.gitignore和README

**Day 2-3: 核心架构搭建**
- 实现清洁架构的项目结构
- 配置依赖注入容器
- 实现基础接口定义
- 添加日志和配置管理

**Day 4-5: 开发环境配置**
- 配置开发工具和插件
- 设置代码格式化规则
- 配置静态代码分析
- 创建开发文档模板

**Day 6-7: 第一个功能模块**
- 选择最简单的核心功能
- 完整实现第一个模块
- 验证架构设计的可行性
- 发布v0.0.0.1版本

#### 4.1.2 成功指标 (独立开发版)
**质量指标**:
- 每个版本都能成功编译运行
- 无临时性代码和占位符
- 代码结构清晰，职责明确
- 文档与代码保持同步

**进度指标**:
- 每3-5天发布一个新版本
- 每个版本都有明确的功能增量
- 版本号严格按规则递增
- 每个版本都有完整的提交说明

**可维护性指标**:
- 任何时候都能回滚到上一版本
- 新功能不破坏现有功能
- 代码复杂度保持在可控范围
- 技术债务及时清理

### 4.2 开发工具配置

#### 4.2.1 必需工具清单
**开发环境**:
- Visual Studio 2022 Community (免费版)
- .NET 8 SDK
- Git for Windows
- GitHub Desktop (可选，简化Git操作)

**代码质量工具**:
- EditorConfig (代码格式统一)
- StyleCop.Analyzers (C#代码规范)
- Microsoft.CodeAnalysis.NetAnalyzers (静态分析)
- SonarLint (实时代码质量检查)

**项目管理工具**:
- GitHub Issues (任务管理)
- GitHub Projects (看板管理)
- Markdown编辑器 (文档编写)
- 简单的时间跟踪工具

#### 4.2.2 配置文件模板
**EditorConfig (.editorconfig)**:
```ini
root = true

[*]
charset = utf-8
end_of_line = crlf
insert_final_newline = true
indent_style = space
indent_size = 4

[*.{cs,csx}]
indent_size = 4
```

**Directory.Build.props**:
```xml
<Project>
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <WarningsAsErrors />
    <WarningsNotAsErrors>CS1591</WarningsNotAsErrors>
  </PropertyGroup>
</Project>
```

---

## 📊 附录：检查清单和模板

### A.1 每日开发检查清单

**开始开发前 (5分钟)**:
- [ ] 明确今日要实现的功能
- [ ] 确认功能的接口设计
- [ ] 确认数据结构设计
- [ ] 确认与现有代码的集成方式

**AI编码过程中**:
- [ ] 严格按照设计实现，无偏差
- [ ] 遇到问题立即停止并报告
- [ ] 不创建任何临时性代码
- [ ] 不为了编译通过而妥协质量

**完成开发后 (10分钟)**:
- [ ] 代码编译通过，无警告
- [ ] 基本功能测试通过
- [ ] 代码符合命名规范
- [ ] 添加了必要的注释
- [ ] 更新了相关文档

### A.2 版本发布检查清单

**发布前检查**:
- [ ] 功能完全按设计实现
- [ ] 无临时性代码和占位符
- [ ] 代码质量符合标准
- [ ] 版本号正确递增
- [ ] 提交消息格式正确

**发布后验证**:
- [ ] 项目能够成功编译
- [ ] 基本功能能够正常运行
- [ ] 文档与代码保持同步
- [ ] Git标签创建正确

### A.3 Augment工作模板

**功能开发请求模板**:
```
功能名称：[具体功能名称]
功能描述：[详细的功能描述]
接口设计：[输入/输出/异常定义]
数据结构：[涉及的数据模型]
依赖关系：[与其他模块的关系]
测试要求：[基本测试场景]

请严格按照以上设计实现，不允许任何偏离。
如遇到问题，立即停止并报告。
```

**问题报告模板**:
```
问题类型：[编译错误/设计冲突/实现困难]
问题描述：[具体问题说明]
当前状态：[已停止工作，等待指导]
建议方案：[可能的解决方案]
需要决策：[需要开发者决定的事项]
```

---

## 🚨 .NET 9.0升级后编译问题分析和解决方案

### 问题根本原因分析

**重要发现**：编译问题不是由.NET 9.0升级直接导致的，而是在后续开发过程中引入的**代码不一致性问题**。

#### 1. 问题时间线
- **v0.0.1.4**: .NET 9.0升级完成，编译成功 ✅
- **v0.0.1.5**: Validator节点服务实现，引入大量新模型和接口
- **当前**: 出现54个编译错误 ❌

#### 2. 核心问题类型

**A. 属性名称不一致**
```csharp
// TaskExecutionState类定义中有：
public string ExecutorNodeId { get; set; } = string.Empty;

// 但代码中大量使用不存在的属性：
taskState.NodeId  // ❌ 错误：属性不存在
```

**B. 模型属性缺失**
```csharp
// TaskOperationResult缺少ExecutedAt属性
result.ExecutedAt = DateTime.UtcNow;  // ❌ 错误

// TaskProgress缺少PercentComplete属性
progress.PercentComplete = 50.0;  // ❌ 错误

// RealTimeMonitoringData缺少ActiveTasks属性
data.ActiveTasks = count;  // ❌ 错误
```

**C. 接口方法签名不匹配**
```csharp
// 方法调用缺少必需参数
HandleTerminalStatusAsync(taskId, status);  // ❌ 缺少cancellationToken参数

// 类型转换错误
ResourceUsage resourceUsage = finalResult.ResourceUsage;  // ❌ 类型不匹配
```

**D. 配置属性缺失**
```csharp
// TaskTrackingConfiguration缺少DataRetentionDays属性
var days = _config.DataRetentionDays;  // ❌ 属性不存在
```

### 全面编译策略

#### 1. 强制全项目编译规则

**🔴 新规则：后续编译必须全部编译，不允许只编译某个模块**

**原因**：
- 单模块编译会隐藏跨模块的依赖问题
- 接口变更影响多个项目，只有全编译才能发现
- 类型不匹配问题只有在完整编译时才会暴露

**实施方法**：
```powershell
# 强制使用完整解决方案编译
dotnet build FlowCustomV1.sln --verbosity normal

# 禁止使用单项目编译（除非明确指定用于调试）
# dotnet build src/FlowCustomV1.Core/FlowCustomV1.Core.csproj  # ❌ 禁止
```

#### 2. 编译前检查清单

**A. 模型一致性检查**
- [ ] 所有引用的属性在模型类中都存在
- [ ] 属性名称在整个项目中保持一致
- [ ] 新增属性已在所有使用位置更新

**B. 接口一致性检查**
- [ ] 接口定义与实现完全匹配
- [ ] 方法签名参数完整
- [ ] 返回类型正确

**C. 配置完整性检查**
- [ ] 配置类包含所有被引用的属性
- [ ] 配置默认值合理
- [ ] 配置验证逻辑完整

#### 3. 问题修复优先级

**P0 - 立即修复（编译阻塞）**
1. 属性名称不一致（如NodeId vs ExecutorNodeId）
2. 缺失的必需属性
3. 接口方法签名不匹配
4. 类型转换错误

**P1 - 高优先级（功能影响）**
1. 配置属性缺失
2. 日志扩展方法缺失
3. 异步方法警告

**P2 - 中优先级（代码质量）**
1. 未使用的字段警告
2. 代码格式问题

### 预防措施

#### 1. 开发流程改进

**A. 模型变更流程**
```
1. 修改模型定义
2. 全项目搜索所有使用位置
3. 批量更新所有引用
4. 全解决方案编译验证
5. 运行相关测试
```

**B. 接口变更流程**
```
1. 修改接口定义
2. 更新所有实现类
3. 更新所有调用位置
4. 全解决方案编译验证
5. 运行集成测试
```

#### 2. 代码审查要点

**A. 新增模型审查**
- 属性命名是否与现有代码一致
- 是否有重复或冲突的属性名
- 是否需要更新相关的使用代码

**B. 接口变更审查**
- 是否影响现有实现
- 参数变更是否向后兼容
- 是否需要更新调用代码

#### 3. 自动化检查

**A. 编译脚本增强**
```powershell
# 增强的编译脚本
function Build-FullSolution {
    Write-Host "=== 全解决方案编译检查 ===" -ForegroundColor Yellow

    # 清理所有输出
    dotnet clean FlowCustomV1.sln

    # 恢复包
    dotnet restore FlowCustomV1.sln

    # 全解决方案编译
    dotnet build FlowCustomV1.sln --verbosity normal --no-restore

    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ 编译失败，必须修复所有错误后才能继续" -ForegroundColor Red
        exit 1
    }

    Write-Host "✅ 全解决方案编译成功" -ForegroundColor Green
}
```

**B. 属性一致性检查脚本**
```powershell
# 检查属性名称一致性
function Check-PropertyConsistency {
    $inconsistencies = @()

    # 检查常见的不一致模式
    $patterns = @(
        @{ Pattern = "\.NodeId"; Expected = "\.ExecutorNodeId" },
        @{ Pattern = "\.ExecutedAt"; Expected = "\.OperationTime" }
    )

    foreach ($pattern in $patterns) {
        $files = Get-ChildItem -Path "src" -Recurse -Filter "*.cs"
        foreach ($file in $files) {
            $content = Get-Content $file.FullName -Raw
            if ($content -match $pattern.Pattern) {
                $inconsistencies += "$($file.FullName): 使用了 $($pattern.Pattern)，应该使用 $($pattern.Expected)"
            }
        }
    }

    if ($inconsistencies.Count -gt 0) {
        Write-Host "❌ 发现属性不一致问题：" -ForegroundColor Red
        $inconsistencies | ForEach-Object { Write-Host "  $_" -ForegroundColor Yellow }
        return $false
    }

    return $true
}
```

### 经验教训

#### 1. 架构层面
- **接口稳定性优先**：Core层接口一旦定义，变更需要极其谨慎
- **模型一致性**：属性命名必须在整个项目中保持一致
- **依赖方向清晰**：Infrastructure层不能定义Core层应该有的模型

#### 2. 开发流程
- **全编译验证**：任何代码变更都必须通过全解决方案编译
- **批量更新**：模型变更时必须批量更新所有使用位置
- **测试驱动**：重要变更必须有对应的测试验证

#### 3. 质量控制
- **代码审查严格**：跨层变更必须经过严格审查
- **自动化检查**：建立自动化的一致性检查机制
- **文档同步**：模型变更必须同步更新文档

### .NET 9.0升级编译问题修复实战经验 (2025-09-06)

#### 问题根本原因
升级到.NET 9.0后出现54个编译错误，**根本原因不是.NET版本升级本身**，而是在后续开发过程中引入的**代码不一致性问题**：

1. **属性命名不一致**：同一概念在不同地方使用了不同的属性名
2. **模型演进不同步**：新增功能时没有同步更新所有相关模型
3. **接口实现不完整**：接口定义了方法但实现类没有提供

#### 修复策略和最佳实践

**A. 兼容性优先策略**
```csharp
// ✅ 正确做法：添加别名属性保持兼容性
public string ExecutorNodeId { get; set; } = string.Empty;

public string NodeId
{
    get => ExecutorNodeId;
    set => ExecutorNodeId = value;
}

// ❌ 错误做法：直接重命名属性（会破坏现有代码）
// public string NodeId { get; set; } = string.Empty; // 重命名ExecutorNodeId
```

**B. 类型转换处理**
```csharp
// ✅ 正确做法：创建转换方法处理类型不匹配
private FlowCustomV1.Core.Models.Executor.ResourceUsage? ConvertResourceUsage(
    FlowCustomV1.Core.Interfaces.Scheduling.ResourceUsage? schedulingResourceUsage)
{
    if (schedulingResourceUsage == null) return null;

    return new FlowCustomV1.Core.Models.Executor.ResourceUsage
    {
        CpuCores = schedulingResourceUsage.PeakCpuUsage / 100.0,
        MemoryMB = schedulingResourceUsage.PeakMemoryUsageMB,
        // ... 其他属性转换
    };
}
```

**C. 可空类型安全处理**
```csharp
// ✅ 正确做法：安全处理可空类型
StartTime = taskState.StartTime ?? DateTime.UtcNow,
EndTime = taskState.EndTime,
TotalTrackingTime = taskState.EndTime.HasValue
    ? (long)(taskState.EndTime.Value - (taskState.StartTime ?? DateTime.UtcNow)).TotalMilliseconds
    : (long)(DateTime.UtcNow - (taskState.StartTime ?? DateTime.UtcNow)).TotalMilliseconds,
```

#### 编译错误修复检查清单

**1. 属性不一致检查**
- [ ] 搜索所有使用旧属性名的位置
- [ ] 添加别名属性而不是重命名
- [ ] 确保getter/setter正确映射

**2. 接口实现检查**
- [ ] 检查所有接口是否完整实现
- [ ] 确认方法签名完全匹配（包括泛型约束）
- [ ] 验证返回类型和参数类型

**3. 类型转换检查**
- [ ] 识别类型不匹配的位置
- [ ] 创建专门的转换方法
- [ ] 处理null值和边界情况

**4. 依赖引用检查**
- [ ] 确认所有必需的using指令
- [ ] 检查NuGet包版本兼容性
- [ ] 验证项目引用关系

#### 预防措施

**A. 开发阶段预防**
```powershell
# 每次提交前运行全解决方案编译
dotnet build FlowCustomV1.sln --verbosity normal

# 不允许单项目编译通过就认为没问题
# dotnet build src/FlowCustomV1.Core/FlowCustomV1.Core.csproj  # ❌ 禁止
```

**B. 代码审查要点**
- 新增属性时检查是否与现有属性冲突
- 接口变更时确认所有实现类都已更新
- 模型变更时搜索所有使用位置

**C. 自动化检查脚本**
```powershell
# 属性一致性检查脚本示例
function Check-PropertyConsistency {
    $patterns = @(
        @{ Pattern = "\.NodeId\b"; Expected = "\.ExecutorNodeId" },
        @{ Pattern = "\.ExecutedAt\b"; Expected = "\.OperationTime" }
    )

    foreach ($pattern in $patterns) {
        $files = Get-ChildItem -Path "src" -Recurse -Filter "*.cs"
        foreach ($file in $files) {
            $content = Get-Content $file.FullName -Raw
            if ($content -match $pattern.Pattern) {
                Write-Warning "发现不一致：$($file.FullName)"
            }
        }
    }
}
```

#### 成功指标
- ✅ 从54个编译错误减少到0个
- ✅ 保持了向后兼容性
- ✅ 没有破坏现有功能
- ✅ 代码质量得到提升

这次修复证明了**系统性分析和兼容性优先策略**的重要性，为后续类似问题提供了宝贵经验。

---

## 📝 结语

本规范文档专门为独立开发者使用Augment AI助手开发FlowCustomV1项目而设计，重点解决AI助手容易产生临时性代码导致项目失控的问题。

**核心价值**:
1. **防止项目失控**: 通过严格的质量门禁防止临时性代码积累
2. **保证代码质量**: 设计优先的开发模式确保架构一致性
3. **提高开发效率**: 清晰的流程和规范减少返工和重构
4. **确保项目成功**: 渐进式开发模式降低项目风险

**关键成功因素**:
1. **严格执行红线规则**: 绝不允许为了编译通过而妥协质量
2. **设计优先原则**: 先设计后编码，确保架构一致性
3. **版本控制严格**: 每个版本都是完整可用的功能增量
4. **持续质量监控**: 及时发现和解决质量问题

**实施建议**:
1. 从v0.0.0.1开始，严格按照版本规则递增
2. 每次开发前都要明确设计，获得确认后再编码
3. 遇到任何问题都要停止并寻求指导，不要妥协
4. 定期回顾和优化开发流程

**预期效果**:
通过严格执行本规范，FlowCustomV1项目将能够：
- 避免因临时性代码导致的项目失败
- 保持清晰的架构和高质量的代码
- 实现可持续的迭代开发
- 最终交付一个成功的工作流自动化系统

**重要提醒**:
本规范的核心是"质量优于速度"，宁可开发慢一些，也要确保每一行代码都是高质量的。这是独立开发项目成功的关键。
