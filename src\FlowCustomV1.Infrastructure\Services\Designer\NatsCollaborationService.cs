using FlowCustomV1.Core.Interfaces.Messaging;
using FlowCustomV1.Core.Interfaces.Services;
using FlowCustomV1.Core.Models.Designer;
using FlowCustomV1.Core.Models.Messages;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Text.Json;

namespace FlowCustomV1.Infrastructure.Services.Designer;

/// <summary>
/// 基于NATS的实时协作服务
/// 使用NATS集群实现分布式实时协作
/// </summary>
public class NatsCollaborationService : ICollaborationService, IDisposable
{
    private readonly INatsService _natsService;
    private readonly ILogger<NatsCollaborationService> _logger;
    
    // 本地协作状态缓存
    private readonly ConcurrentDictionary<string, CollaborationSession> _localSessions = new();
    private readonly ConcurrentDictionary<string, List<CollaborationHistoryEntry>> _sessionHistory = new();
    private readonly ConcurrentDictionary<string, List<DesignConflict>> _sessionConflicts = new();
    private readonly ConcurrentDictionary<string, CollaborationStatistics> _sessionStatistics = new();
    
    // NATS订阅管理
    private readonly ConcurrentDictionary<string, string> _subscriptions = new();
    private readonly Timer _heartbeatTimer;
    
    private bool _disposed = false;
    private readonly string _nodeId;

    /// <summary>
    /// 构造函数
    /// </summary>
    public NatsCollaborationService(
        INatsService natsService,
        ILogger<NatsCollaborationService> logger)
    {
        _natsService = natsService ?? throw new ArgumentNullException(nameof(natsService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _nodeId = Environment.MachineName + "-" + Guid.NewGuid().ToString("N")[..8];

        // 启动心跳定时器（每30秒发送一次心跳）
        _heartbeatTimer = new Timer(SendHeartbeat, null, TimeSpan.FromSeconds(30), TimeSpan.FromSeconds(30));
        
        // 初始化NATS订阅
        _ = Task.Run(InitializeNatsSubscriptionsAsync);
    }

    #region 协作会话管理

    /// <inheritdoc />
    public async Task<string> CreateCollaborationSessionAsync(string workflowId, CollaborationSessionInfo sessionInfo, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(workflowId);
        ArgumentNullException.ThrowIfNull(sessionInfo);

        try
        {
            _logger.LogInformation("Creating collaboration session for workflow {WorkflowId}", workflowId);

            var sessionId = Guid.NewGuid().ToString("N");
            var session = new CollaborationSession
            {
                SessionId = sessionId,
                WorkflowId = workflowId,
                Name = sessionInfo.Name,
                Description = sessionInfo.Description,
                CreatedBy = sessionInfo.CreatedBy,
                CreatedAt = DateTime.UtcNow,
                Status = SessionStatus.Active,
                MaxCollaborators = sessionInfo.MaxCollaborators,
                Settings = sessionInfo.Settings,
                LastActivityAt = DateTime.UtcNow
            };

            // 本地存储
            _localSessions.TryAdd(sessionId, session);
            _sessionHistory.TryAdd(sessionId, new List<CollaborationHistoryEntry>());
            _sessionConflicts.TryAdd(sessionId, new List<DesignConflict>());
            _sessionStatistics.TryAdd(sessionId, new CollaborationStatistics { SessionId = sessionId });

            // 通过NATS广播会话创建
            var message = new CollaborationSessionMessage
            {
                SenderId = _nodeId,
                Action = SessionAction.Created,
                Session = session,
                Timestamp = DateTime.UtcNow
            };

            await _natsService.PublishAsync($"flowcustom.collaboration.sessions.{sessionId}.lifecycle", message, cancellationToken);

            // 订阅会话相关消息
            await SubscribeToSessionAsync(sessionId);

            _logger.LogInformation("Collaboration session {SessionId} created successfully", sessionId);
            return sessionId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create collaboration session for workflow {WorkflowId}", workflowId);
            return string.Empty;
        }
    }

    /// <inheritdoc />
    public async Task<bool> JoinSessionAsync(string sessionId, CollaboratorInfo collaborator, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(sessionId);
        ArgumentNullException.ThrowIfNull(collaborator);

        try
        {
            _logger.LogInformation("Collaborator {CollaboratorId} joining session {SessionId}", collaborator.CollaboratorId, sessionId);

            // 检查本地会话
            if (!_localSessions.TryGetValue(sessionId, out var session))
            {
                // 尝试从NATS获取会话信息
                session = await RequestSessionInfoAsync(sessionId, cancellationToken);
                if (session == null)
                {
                    _logger.LogWarning("Session {SessionId} not found", sessionId);
                    return false;
                }
                _localSessions.TryAdd(sessionId, session);
            }

            if (session.Status != SessionStatus.Active)
            {
                _logger.LogWarning("Session {SessionId} is not active", sessionId);
                return false;
            }

            if (session.Collaborators.Count >= session.MaxCollaborators)
            {
                _logger.LogWarning("Session {SessionId} has reached maximum collaborators limit", sessionId);
                return false;
            }

            // 更新协作者信息
            var existingCollaborator = session.Collaborators.FirstOrDefault(c => c.CollaboratorId == collaborator.CollaboratorId);
            if (existingCollaborator != null)
            {
                existingCollaborator.LastActiveAt = DateTime.UtcNow;
                existingCollaborator.Status = CollaboratorStatus.Online;
            }
            else
            {
                collaborator.JoinedAt = DateTime.UtcNow;
                collaborator.LastActiveAt = DateTime.UtcNow;
                collaborator.Status = CollaboratorStatus.Online;
                session.Collaborators.Add(collaborator);
            }

            session.LastActivityAt = DateTime.UtcNow;

            // 通过NATS广播加入事件
            var message = new CollaborationMessage
            {
                SenderId = _nodeId,
                SessionId = sessionId,
                Action = CollaborationAction.Join,
                Collaborator = collaborator,
                Timestamp = DateTime.UtcNow
            };

            await _natsService.PublishAsync($"flowcustom.collaboration.sessions.{sessionId}.events", message, cancellationToken);

            // 确保订阅了会话消息
            await SubscribeToSessionAsync(sessionId);

            // 触发本地事件
            OnCollaboratorJoined(new CollaboratorJoinedEventArgs
            {
                SessionId = sessionId,
                Collaborator = collaborator,
                JoinedAt = DateTime.UtcNow,
                TotalCollaborators = session.Collaborators.Count
            });

            _logger.LogInformation("Collaborator {CollaboratorId} joined session {SessionId} successfully", collaborator.CollaboratorId, sessionId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to join session {SessionId}", sessionId);
            return false;
        }
    }

    /// <inheritdoc />
    public async Task<bool> LeaveSessionAsync(string sessionId, string collaboratorId, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(sessionId);
        ArgumentException.ThrowIfNullOrWhiteSpace(collaboratorId);

        try
        {
            _logger.LogInformation("Collaborator {CollaboratorId} leaving session {SessionId}", collaboratorId, sessionId);

            if (!_localSessions.TryGetValue(sessionId, out var session))
            {
                _logger.LogWarning("Session {SessionId} not found locally", sessionId);
                return false;
            }

            var collaborator = session.Collaborators.FirstOrDefault(c => c.CollaboratorId == collaboratorId);
            if (collaborator == null)
            {
                _logger.LogWarning("Collaborator {CollaboratorId} not found in session {SessionId}", collaboratorId, sessionId);
                return false;
            }

            // 移除协作者
            session.Collaborators.Remove(collaborator);
            session.LastActivityAt = DateTime.UtcNow;

            // 通过NATS广播离开事件
            var message = new CollaborationMessage
            {
                SenderId = _nodeId,
                SessionId = sessionId,
                Action = CollaborationAction.Leave,
                Collaborator = collaborator,
                Timestamp = DateTime.UtcNow
            };

            await _natsService.PublishAsync($"flowcustom.collaboration.sessions.{sessionId}.events", message, cancellationToken);

            // 触发本地事件
            OnCollaboratorLeft(new CollaboratorLeftEventArgs
            {
                SessionId = sessionId,
                CollaboratorId = collaboratorId,
                CollaboratorName = collaborator.Name,
                LeftAt = DateTime.UtcNow,
                RemainingCollaborators = session.Collaborators.Count
            });

            _logger.LogInformation("Collaborator {CollaboratorId} left session {SessionId} successfully", collaboratorId, sessionId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to leave session {SessionId}", sessionId);
            return false;
        }
    }

    /// <inheritdoc />
    public async Task<CollaborationSession?> GetSessionAsync(string sessionId, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(sessionId);

        try
        {
            // 先检查本地缓存
            if (_localSessions.TryGetValue(sessionId, out var session))
            {
                return CloneSession(session);
            }

            // 从NATS请求会话信息
            session = await RequestSessionInfoAsync(sessionId, cancellationToken);
            if (session != null)
            {
                _localSessions.TryAdd(sessionId, session);
                return CloneSession(session);
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get session {SessionId}", sessionId);
            return null;
        }
    }

    /// <inheritdoc />
    public Task<IReadOnlyList<CollaborationSession>> GetActiveSessionsAsync(string workflowId, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(workflowId);

        try
        {
            // 从本地缓存获取
            var localSessions = _localSessions.Values
                .Where(s => s.WorkflowId == workflowId && s.Status == SessionStatus.Active)
                .Select(CloneSession)
                .ToList();

            // TODO: 可以通过NATS查询其他节点的会话信息
            // 这里简化实现，只返回本地会话

            return Task.FromResult<IReadOnlyList<CollaborationSession>>(localSessions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get active sessions for workflow {WorkflowId}", workflowId);
            return Task.FromResult<IReadOnlyList<CollaborationSession>>(new List<CollaborationSession>());
        }
    }

    /// <inheritdoc />
    public async Task<bool> EndSessionAsync(string sessionId, string reason = "", CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(sessionId);

        try
        {
            _logger.LogInformation("Ending session {SessionId} with reason: {Reason}", sessionId, reason);

            if (!_localSessions.TryGetValue(sessionId, out var session))
            {
                return false;
            }

            var previousStatus = session.Status;
            session.Status = SessionStatus.Ended;
            session.EndedAt = DateTime.UtcNow;

            // 通过NATS广播会话结束
            var message = new CollaborationSessionMessage
            {
                SenderId = _nodeId,
                Action = SessionAction.Ended,
                Session = session,
                Reason = reason,
                Timestamp = DateTime.UtcNow
            };

            await _natsService.PublishAsync($"flowcustom.collaboration.sessions.{sessionId}.lifecycle", message, cancellationToken);

            // 取消订阅
            await UnsubscribeFromSessionAsync(sessionId);

            // 触发本地事件
            OnSessionStatusChanged(new SessionStatusChangedEventArgs
            {
                SessionId = sessionId,
                PreviousStatus = previousStatus,
                NewStatus = SessionStatus.Ended,
                ChangedAt = DateTime.UtcNow,
                Reason = reason,
                TriggeredBy = _nodeId
            });

            _logger.LogInformation("Session {SessionId} ended successfully", sessionId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to end session {SessionId}", sessionId);
            return false;
        }
    }

    #endregion

    #region 实时协作

    /// <inheritdoc />
    public async Task BroadcastOperationAsync(string sessionId, DesignOperation operation, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(sessionId);
        ArgumentNullException.ThrowIfNull(operation);

        try
        {
            _logger.LogDebug("Broadcasting operation {OperationType} in session {SessionId}", operation.Type, sessionId);

            // 检测冲突
            var conflictResult = await DetectConflictAsync(sessionId, operation, cancellationToken);
            if (conflictResult.HasConflict)
            {
                _logger.LogWarning("Conflict detected for operation {OperationId}", operation.OperationId);

                // 广播冲突检测结果
                var conflictMessage = new CollaborationConflictMessage
                {
                    SenderId = _nodeId,
                    SessionId = sessionId,
                    ConflictResult = conflictResult,
                    Timestamp = DateTime.UtcNow
                };

                await _natsService.PublishAsync($"flowcustom.collaboration.sessions.{sessionId}.conflicts", conflictMessage, cancellationToken);
                return;
            }

            // 更新本地会话状态
            if (_localSessions.TryGetValue(sessionId, out var session))
            {
                session.LastActivityAt = DateTime.UtcNow;
            }

            // 通过NATS广播设计操作
            var message = new CollaborationOperationMessage
            {
                SenderId = _nodeId,
                SessionId = sessionId,
                Operation = operation,
                Timestamp = DateTime.UtcNow
            };

            await _natsService.PublishAsync($"flowcustom.collaboration.sessions.{sessionId}.operations", message, cancellationToken);

            // 记录操作历史
            await LogActivityAsync(sessionId, new CollaborationActivity
            {
                CollaboratorId = operation.CollaboratorId,
                CollaboratorName = session?.Collaborators.FirstOrDefault(c => c.CollaboratorId == operation.CollaboratorId)?.Name ?? "Unknown",
                ActivityType = CollaborationActivityType.DesignOperation,
                Description = $"Performed {operation.Type} operation on {operation.TargetType}",
                Operation = operation
            }, cancellationToken);

            _logger.LogDebug("Operation {OperationId} broadcasted successfully", operation.OperationId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to broadcast operation in session {SessionId}", sessionId);
            throw;
        }
    }

    /// <inheritdoc />
    public async Task<bool> UpdateCollaboratorStatusAsync(string sessionId, string collaboratorId, CollaboratorStatus status, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(sessionId);
        ArgumentException.ThrowIfNullOrWhiteSpace(collaboratorId);

        try
        {
            if (!_localSessions.TryGetValue(sessionId, out var session))
            {
                return false;
            }

            var collaborator = session.Collaborators.FirstOrDefault(c => c.CollaboratorId == collaboratorId);
            if (collaborator == null)
            {
                return false;
            }

            var previousStatus = collaborator.Status;
            collaborator.Status = status;
            collaborator.LastActiveAt = DateTime.UtcNow;
            session.LastActivityAt = DateTime.UtcNow;

            // 通过NATS广播状态更新
            var message = new CollaborationStatusMessage
            {
                SenderId = _nodeId,
                SessionId = sessionId,
                CollaboratorId = collaboratorId,
                Status = status,
                PreviousStatus = previousStatus,
                Timestamp = DateTime.UtcNow
            };

            await _natsService.PublishAsync($"flowcustom.collaboration.sessions.{sessionId}.status", message, cancellationToken);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update collaborator status in session {SessionId}", sessionId);
            return false;
        }
    }

    /// <inheritdoc />
    public async Task<bool> UpdateCursorPositionAsync(string sessionId, string collaboratorId, CursorPosition position, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(sessionId);
        ArgumentException.ThrowIfNullOrWhiteSpace(collaboratorId);
        ArgumentNullException.ThrowIfNull(position);

        try
        {
            if (!_localSessions.TryGetValue(sessionId, out var session))
            {
                return false;
            }

            var collaborator = session.Collaborators.FirstOrDefault(c => c.CollaboratorId == collaboratorId);
            if (collaborator == null)
            {
                return false;
            }

            collaborator.CursorPosition = position;
            collaborator.LastActiveAt = DateTime.UtcNow;
            session.LastActivityAt = DateTime.UtcNow;

            // 通过NATS广播光标位置更新（使用高频主题）
            var message = new CollaborationCursorMessage
            {
                SenderId = _nodeId,
                SessionId = sessionId,
                CollaboratorId = collaboratorId,
                Position = position,
                Timestamp = DateTime.UtcNow
            };

            await _natsService.PublishAsync($"flowcustom.collaboration.sessions.{sessionId}.cursors", message, cancellationToken);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update cursor position in session {SessionId}", sessionId);
            return false;
        }
    }

    /// <inheritdoc />
    public async Task<bool> UpdateSelectionAsync(string sessionId, string collaboratorId, string selection, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(sessionId);
        ArgumentException.ThrowIfNullOrWhiteSpace(collaboratorId);

        try
        {
            if (!_localSessions.TryGetValue(sessionId, out var session))
            {
                return false;
            }

            var collaborator = session.Collaborators.FirstOrDefault(c => c.CollaboratorId == collaboratorId);
            if (collaborator == null)
            {
                return false;
            }

            var previousSelection = collaborator.CurrentSelection;
            collaborator.CurrentSelection = selection;
            collaborator.LastActiveAt = DateTime.UtcNow;
            session.LastActivityAt = DateTime.UtcNow;

            // 通过NATS广播选择更新
            var message = new CollaborationSelectionMessage
            {
                SenderId = _nodeId,
                SessionId = sessionId,
                CollaboratorId = collaboratorId,
                Selection = selection,
                PreviousSelection = previousSelection,
                Timestamp = DateTime.UtcNow
            };

            await _natsService.PublishAsync($"flowcustom.collaboration.sessions.{sessionId}.selections", message, cancellationToken);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update selection in session {SessionId}", sessionId);
            return false;
        }
    }

    #endregion

    #region 冲突检测和解决

    /// <inheritdoc />
    public Task<ConflictDetectionResult> DetectConflictAsync(string sessionId, DesignOperation operation, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(sessionId);
        ArgumentNullException.ThrowIfNull(operation);

        try
        {
            if (!_localSessions.TryGetValue(sessionId, out var session))
            {
                return Task.FromResult(new ConflictDetectionResult { HasConflict = false });
            }

            if (!session.Settings.EnableConflictDetection)
            {
                return Task.FromResult(new ConflictDetectionResult { HasConflict = false });
            }

            var result = new ConflictDetectionResult
            {
                HasConflict = false,
                DetectedAt = DateTime.UtcNow
            };

            // 检查并发编辑冲突
            var conflictingCollaborators = session.Collaborators
                .Where(c => c.CollaboratorId != operation.CollaboratorId &&
                           c.CurrentSelection == operation.TargetId &&
                           DateTime.UtcNow - c.LastActiveAt < TimeSpan.FromSeconds(30))
                .ToList();

            if (conflictingCollaborators.Any())
            {
                result.HasConflict = true;
                result.ConflictType = ConflictType.ConcurrentEdit;
                result.ConflictId = Guid.NewGuid().ToString("N");
                result.Description = $"Multiple collaborators are editing the same object: {operation.TargetId}";
                result.InvolvedCollaborators = conflictingCollaborators.Select(c => c.CollaboratorId).ToList();
                result.ConflictingOperations = new List<DesignOperation> { operation };

                // 创建冲突记录
                var conflict = new DesignConflict
                {
                    ConflictId = result.ConflictId,
                    SessionId = sessionId,
                    ConflictType = result.ConflictType,
                    Status = ConflictStatus.Pending,
                    Description = result.Description,
                    InvolvedCollaborators = conflictingCollaborators,
                    ConflictingOperations = result.ConflictingOperations,
                    CreatedAt = DateTime.UtcNow
                };

                if (_sessionConflicts.TryGetValue(sessionId, out var conflicts))
                {
                    conflicts.Add(conflict);
                }

                // 更新统计信息
                if (_sessionStatistics.TryGetValue(sessionId, out var stats))
                {
                    stats.TotalConflicts++;
                }

                // 生成解决建议
                result.SuggestedResolutions = GenerateConflictResolutionSuggestions(conflict);
            }

            return Task.FromResult(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to detect conflict in session {SessionId}", sessionId);
            return Task.FromResult(new ConflictDetectionResult { HasConflict = false });
        }
    }

    /// <inheritdoc />
    public async Task<bool> ResolveConflictAsync(string sessionId, string conflictId, ConflictResolution resolution, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(sessionId);
        ArgumentException.ThrowIfNullOrWhiteSpace(conflictId);
        ArgumentNullException.ThrowIfNull(resolution);

        try
        {
            _logger.LogInformation("Resolving conflict {ConflictId} in session {SessionId}", conflictId, sessionId);

            if (!_sessionConflicts.TryGetValue(sessionId, out var conflicts))
            {
                return false;
            }

            var conflict = conflicts.FirstOrDefault(c => c.ConflictId == conflictId);
            if (conflict == null)
            {
                return false;
            }

            if (conflict.Status != ConflictStatus.Pending)
            {
                return false;
            }

            // 应用解决方案
            conflict.Status = ConflictStatus.Resolved;
            conflict.ResolvedAt = DateTime.UtcNow;
            conflict.Resolution = resolution;

            // 通过NATS广播冲突解决
            var message = new CollaborationConflictMessage
            {
                SenderId = _nodeId,
                SessionId = sessionId,
                ConflictResult = new ConflictDetectionResult
                {
                    ConflictId = conflictId,
                    HasConflict = false, // 已解决
                    ConflictType = conflict.ConflictType,
                    Description = $"Conflict resolved using {resolution.Strategy} strategy"
                },
                Timestamp = DateTime.UtcNow
            };

            await _natsService.PublishAsync($"flowcustom.collaboration.sessions.{sessionId}.conflicts", message, cancellationToken);

            // 更新统计信息
            if (_sessionStatistics.TryGetValue(sessionId, out var stats))
            {
                stats.ResolvedConflicts++;
            }

            // 触发本地事件
            OnConflictResolved(new ConflictResolvedEventArgs
            {
                SessionId = sessionId,
                ConflictId = conflictId,
                Resolution = resolution,
                ResolvedBy = resolution.ResolvedBy,
                ResolvedAt = DateTime.UtcNow,
                ResolutionTimeMs = (long)(DateTime.UtcNow - conflict.CreatedAt).TotalMilliseconds
            });

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to resolve conflict {ConflictId}", conflictId);
            return false;
        }
    }

    /// <inheritdoc />
    public Task<IReadOnlyList<DesignConflict>> GetPendingConflictsAsync(string sessionId, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(sessionId);

        try
        {
            if (_sessionConflicts.TryGetValue(sessionId, out var conflicts))
            {
                var result = conflicts
                    .Where(c => c.Status == ConflictStatus.Pending)
                    .OrderByDescending(c => c.CreatedAt)
                    .ToList();
                return Task.FromResult<IReadOnlyList<DesignConflict>>(result);
            }

            return Task.FromResult<IReadOnlyList<DesignConflict>>(new List<DesignConflict>());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get pending conflicts for session {SessionId}", sessionId);
            return Task.FromResult<IReadOnlyList<DesignConflict>>(new List<DesignConflict>());
        }
    }

    /// <inheritdoc />
    public async Task<OperationApplicationResult> ApplyOperationsAsync(string sessionId, IReadOnlyList<DesignOperation> operations, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(sessionId);
        ArgumentNullException.ThrowIfNull(operations);

        var result = new OperationApplicationResult
        {
            AppliedAt = DateTime.UtcNow
        };

        try
        {
            foreach (var operation in operations)
            {
                try
                {
                    // 检测冲突
                    var conflictResult = await DetectConflictAsync(sessionId, operation, cancellationToken);
                    if (conflictResult.HasConflict)
                    {
                        result.SkippedCount++;
                        result.Warnings.Add($"Operation {operation.OperationId} skipped due to conflict");
                        continue;
                    }

                    // 广播操作
                    await BroadcastOperationAsync(sessionId, operation, cancellationToken);
                    result.AppliedCount++;
                }
                catch (Exception ex)
                {
                    result.FailedCount++;
                    result.Errors.Add($"Failed to apply operation {operation.OperationId}: {ex.Message}");
                }
            }

            result.Success = result.FailedCount == 0;
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to apply operations in session {SessionId}", sessionId);
            result.Success = false;
            result.Errors.Add($"Operation application failed: {ex.Message}");
            return result;
        }
    }

    #endregion

    #region 协作历史和审计

    /// <inheritdoc />
    public Task<IReadOnlyList<CollaborationHistoryEntry>> GetCollaborationHistoryAsync(string sessionId, CollaborationHistoryQuery? query = null, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(sessionId);

        try
        {
            if (!_sessionHistory.TryGetValue(sessionId, out var history))
            {
                return Task.FromResult<IReadOnlyList<CollaborationHistoryEntry>>(new List<CollaborationHistoryEntry>());
            }

            var filteredHistory = history.AsEnumerable();

            // 应用查询过滤
            if (query != null)
            {
                if (!string.IsNullOrWhiteSpace(query.CollaboratorId))
                {
                    filteredHistory = filteredHistory.Where(h => h.CollaboratorId == query.CollaboratorId);
                }

                if (query.ActivityTypes?.Count > 0)
                {
                    filteredHistory = filteredHistory.Where(h => query.ActivityTypes.Contains(h.ActivityType));
                }

                if (query.StartTime.HasValue)
                {
                    filteredHistory = filteredHistory.Where(h => h.Timestamp >= query.StartTime.Value);
                }

                if (query.EndTime.HasValue)
                {
                    filteredHistory = filteredHistory.Where(h => h.Timestamp <= query.EndTime.Value);
                }

                // 排序和分页
                filteredHistory = query.SortDirection == SortDirection.Ascending
                    ? filteredHistory.OrderBy(h => h.Timestamp)
                    : filteredHistory.OrderByDescending(h => h.Timestamp);

                filteredHistory = filteredHistory.Skip((query.PageNumber - 1) * query.PageSize).Take(query.PageSize);
            }

            return Task.FromResult<IReadOnlyList<CollaborationHistoryEntry>>(filteredHistory.ToList());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get collaboration history for session {SessionId}", sessionId);
            return Task.FromResult<IReadOnlyList<CollaborationHistoryEntry>>(new List<CollaborationHistoryEntry>());
        }
    }

    /// <inheritdoc />
    public Task<bool> LogActivityAsync(string sessionId, CollaborationActivity activity, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(sessionId);
        ArgumentNullException.ThrowIfNull(activity);

        try
        {
            if (!_localSessions.TryGetValue(sessionId, out var session))
            {
                return Task.FromResult(false);
            }

            if (!session.Settings.RecordHistory)
            {
                return Task.FromResult(true);
            }

            var historyEntry = new CollaborationHistoryEntry
            {
                EntryId = activity.ActivityId,
                SessionId = sessionId,
                CollaboratorId = activity.CollaboratorId,
                CollaboratorName = activity.CollaboratorName,
                ActivityType = activity.ActivityType,
                Description = activity.Description,
                Operation = activity.Operation,
                Timestamp = activity.Timestamp,
                Data = activity.Data,
                IpAddress = activity.ClientInfo?.IpAddress ?? "",
                UserAgent = activity.ClientInfo?.UserAgent ?? ""
            };

            if (_sessionHistory.TryGetValue(sessionId, out var history))
            {
                history.Add(historyEntry);

                // 清理过期历史记录
                var cutoffDate = DateTime.UtcNow.AddDays(-session.Settings.HistoryRetentionDays);
                var expiredEntries = history.Where(h => h.Timestamp < cutoffDate).ToList();
                foreach (var expired in expiredEntries)
                {
                    history.Remove(expired);
                }
            }

            return Task.FromResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to log activity in session {SessionId}", sessionId);
            return Task.FromResult(false);
        }
    }

    /// <inheritdoc />
    public Task<CollaborationStatistics> GetStatisticsAsync(string sessionId, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(sessionId);

        try
        {
            if (_sessionStatistics.TryGetValue(sessionId, out var stats))
            {
                // 更新实时统计
                if (_localSessions.TryGetValue(sessionId, out var session))
                {
                    stats.ActiveCollaborators = session.Collaborators.Count(c => c.Status == CollaboratorStatus.Online);
                    stats.SessionDurationMinutes = (DateTime.UtcNow - session.CreatedAt).TotalMinutes;

                    // 计算协作效率评分
                    if (stats.TotalOperations > 0)
                    {
                        var conflictRate = (double)stats.TotalConflicts / stats.TotalOperations;
                        var resolutionRate = stats.TotalConflicts > 0 ? (double)stats.ResolvedConflicts / stats.TotalConflicts : 1.0;
                        stats.CollaborationEfficiencyScore = Math.Max(0, Math.Min(100, (1 - conflictRate) * resolutionRate * 100));
                    }
                }

                stats.GeneratedAt = DateTime.UtcNow;
                return Task.FromResult(stats);
            }

            return Task.FromResult(new CollaborationStatistics { SessionId = sessionId });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get statistics for session {SessionId}", sessionId);
            return Task.FromResult(new CollaborationStatistics { SessionId = sessionId });
        }
    }

    #endregion

    #region 权限管理

    /// <inheritdoc />
    public Task<bool> CheckPermissionAsync(string sessionId, string collaboratorId, string permission, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(sessionId);
        ArgumentException.ThrowIfNullOrWhiteSpace(collaboratorId);
        ArgumentException.ThrowIfNullOrWhiteSpace(permission);

        try
        {
            if (!_localSessions.TryGetValue(sessionId, out var session))
            {
                return Task.FromResult(false);
            }

            var collaborator = session.Collaborators.FirstOrDefault(c => c.CollaboratorId == collaboratorId);
            if (collaborator == null)
            {
                return Task.FromResult(false);
            }

            // 检查显式权限
            if (collaborator.Permissions.Contains(permission))
            {
                return Task.FromResult(true);
            }

            // 检查角色权限
            var result = collaborator.Role switch
            {
                CollaboratorRole.Owner => true,
                CollaboratorRole.Admin => permission != "delete_session",
                CollaboratorRole.Editor => permission is "edit" or "comment" or "view",
                CollaboratorRole.Viewer => permission == "view",
                _ => false
            };
            return Task.FromResult(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to check permission in session {SessionId}", sessionId);
            return Task.FromResult(false);
        }
    }

    /// <inheritdoc />
    public async Task<bool> UpdatePermissionsAsync(string sessionId, string collaboratorId, HashSet<string> permissions, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(sessionId);
        ArgumentException.ThrowIfNullOrWhiteSpace(collaboratorId);
        ArgumentNullException.ThrowIfNull(permissions);

        try
        {
            if (!_localSessions.TryGetValue(sessionId, out var session))
            {
                return false;
            }

            var collaborator = session.Collaborators.FirstOrDefault(c => c.CollaboratorId == collaboratorId);
            if (collaborator == null)
            {
                return false;
            }

            var previousPermissions = new HashSet<string>(collaborator.Permissions);
            collaborator.Permissions = permissions;

            // 通过NATS广播权限变更
            var message = new CollaborationMessage
            {
                SenderId = _nodeId,
                SessionId = sessionId,
                Action = CollaborationAction.PermissionChange,
                Collaborator = collaborator,
                Timestamp = DateTime.UtcNow
            };

            await _natsService.PublishAsync($"flowcustom.collaboration.sessions.{sessionId}.events", message, cancellationToken);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update permissions in session {SessionId}", sessionId);
            return false;
        }
    }

    /// <inheritdoc />
    public async Task<bool> UpdateRoleAsync(string sessionId, string collaboratorId, CollaboratorRole role, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(sessionId);
        ArgumentException.ThrowIfNullOrWhiteSpace(collaboratorId);

        try
        {
            if (!_localSessions.TryGetValue(sessionId, out var session))
            {
                return false;
            }

            var collaborator = session.Collaborators.FirstOrDefault(c => c.CollaboratorId == collaboratorId);
            if (collaborator == null)
            {
                return false;
            }

            var previousRole = collaborator.Role;
            collaborator.Role = role;

            // 通过NATS广播角色变更
            var message = new CollaborationMessage
            {
                SenderId = _nodeId,
                SessionId = sessionId,
                Action = CollaborationAction.PermissionChange,
                Collaborator = collaborator,
                Timestamp = DateTime.UtcNow
            };

            await _natsService.PublishAsync($"flowcustom.collaboration.sessions.{sessionId}.events", message, cancellationToken);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update role in session {SessionId}", sessionId);
            return false;
        }
    }

    #endregion

    #region 事件

    /// <inheritdoc />
    public event EventHandler<CollaboratorJoinedEventArgs>? CollaboratorJoined;

    /// <inheritdoc />
    public event EventHandler<CollaboratorLeftEventArgs>? CollaboratorLeft;

    /// <inheritdoc />
    public event EventHandler<DesignOperationEventArgs>? DesignOperationReceived;

    /// <inheritdoc />
    public event EventHandler<ConflictDetectedEventArgs>? ConflictDetected;

    /// <inheritdoc />
    public event EventHandler<ConflictResolvedEventArgs>? ConflictResolved;

    /// <inheritdoc />
    public event EventHandler<SessionStatusChangedEventArgs>? SessionStatusChanged;

    /// <summary>
    /// 触发协作者加入事件
    /// </summary>
    protected virtual void OnCollaboratorJoined(CollaboratorJoinedEventArgs e)
    {
        CollaboratorJoined?.Invoke(this, e);
    }

    /// <summary>
    /// 触发协作者离开事件
    /// </summary>
    protected virtual void OnCollaboratorLeft(CollaboratorLeftEventArgs e)
    {
        CollaboratorLeft?.Invoke(this, e);
    }

    /// <summary>
    /// 触发设计操作事件
    /// </summary>
    protected virtual void OnDesignOperationReceived(DesignOperationEventArgs e)
    {
        DesignOperationReceived?.Invoke(this, e);
    }

    /// <summary>
    /// 触发冲突检测事件
    /// </summary>
    protected virtual void OnConflictDetected(ConflictDetectedEventArgs e)
    {
        ConflictDetected?.Invoke(this, e);
    }

    /// <summary>
    /// 触发冲突解决事件
    /// </summary>
    protected virtual void OnConflictResolved(ConflictResolvedEventArgs e)
    {
        ConflictResolved?.Invoke(this, e);
    }

    /// <summary>
    /// 触发会话状态变更事件
    /// </summary>
    protected virtual void OnSessionStatusChanged(SessionStatusChangedEventArgs e)
    {
        SessionStatusChanged?.Invoke(this, e);
    }

    #endregion

    #region NATS订阅管理

    /// <summary>
    /// 初始化NATS订阅
    /// </summary>
    private async Task InitializeNatsSubscriptionsAsync()
    {
        try
        {
            _logger.LogInformation("Initializing NATS subscriptions for collaboration service");

            // 确保NATS连接
            if (!_natsService.IsConnected)
            {
                await _natsService.ConnectAsync();
            }

            // 订阅全局协作消息
            await _natsService.SubscribeAsync<CollaborationSessionMessage>(
                "flowcustom.collaboration.sessions.*.lifecycle",
                OnSessionLifecycleMessageReceived);

            await _natsService.SubscribeAsync<CollaborationMessage>(
                "flowcustom.collaboration.sessions.*.events",
                OnCollaborationEventMessageReceived);

            await _natsService.SubscribeAsync<CollaborationHeartbeatMessage>(
                "flowcustom.collaboration.heartbeat",
                OnHeartbeatMessageReceived);

            _logger.LogInformation("NATS subscriptions initialized successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize NATS subscriptions");
        }
    }

    /// <summary>
    /// 订阅会话相关消息
    /// </summary>
    private async Task SubscribeToSessionAsync(string sessionId)
    {
        try
        {
            if (_subscriptions.ContainsKey(sessionId))
            {
                return; // 已经订阅
            }

            // 订阅会话操作消息
            await _natsService.SubscribeAsync<CollaborationOperationMessage>(
                $"flowcustom.collaboration.sessions.{sessionId}.operations",
                OnOperationMessageReceived);

            // 订阅会话状态消息
            await _natsService.SubscribeAsync<CollaborationStatusMessage>(
                $"flowcustom.collaboration.sessions.{sessionId}.status",
                OnStatusMessageReceived);

            // 订阅光标位置消息
            await _natsService.SubscribeAsync<CollaborationCursorMessage>(
                $"flowcustom.collaboration.sessions.{sessionId}.cursors",
                OnCursorMessageReceived);

            // 订阅选择消息
            await _natsService.SubscribeAsync<CollaborationSelectionMessage>(
                $"flowcustom.collaboration.sessions.{sessionId}.selections",
                OnSelectionMessageReceived);

            // 订阅冲突消息
            await _natsService.SubscribeAsync<CollaborationConflictMessage>(
                $"flowcustom.collaboration.sessions.{sessionId}.conflicts",
                OnConflictMessageReceived);

            // 订阅聊天消息
            await _natsService.SubscribeAsync<CollaborationChatMessage>(
                $"flowcustom.collaboration.sessions.{sessionId}.chat",
                OnChatMessageReceived);

            _subscriptions.TryAdd(sessionId, sessionId);
            _logger.LogDebug("Subscribed to session {SessionId} messages", sessionId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to subscribe to session {SessionId}", sessionId);
        }
    }

    /// <summary>
    /// 取消订阅会话消息
    /// </summary>
    private Task UnsubscribeFromSessionAsync(string sessionId)
    {
        try
        {
            if (!_subscriptions.ContainsKey(sessionId))
            {
                return Task.CompletedTask; // 未订阅
            }

            // TODO: 实现NATS取消订阅逻辑
            // 当前NATS服务接口可能需要扩展以支持取消订阅

            _subscriptions.TryRemove(sessionId, out _);
            _logger.LogDebug("Unsubscribed from session {SessionId} messages", sessionId);
            return Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to unsubscribe from session {SessionId}", sessionId);
            return Task.CompletedTask;
        }
    }

    #endregion

    #region 消息处理方法

    /// <summary>
    /// 处理会话生命周期消息
    /// </summary>
    private async Task OnSessionLifecycleMessageReceived(CollaborationSessionMessage message)
    {
        try
        {
            if (message.SenderId == _nodeId)
            {
                return; // 忽略自己发送的消息
            }

            _logger.LogDebug("Received session lifecycle message: {Action} for session {SessionId}",
                message.Action, message.SessionId);

            switch (message.Action)
            {
                case SessionAction.Created:
                    if (message.Session != null && !_localSessions.ContainsKey(message.SessionId))
                    {
                        _localSessions.TryAdd(message.SessionId, message.Session);
                        _sessionHistory.TryAdd(message.SessionId, new List<CollaborationHistoryEntry>());
                        _sessionConflicts.TryAdd(message.SessionId, new List<DesignConflict>());
                        _sessionStatistics.TryAdd(message.SessionId, new CollaborationStatistics { SessionId = message.SessionId });
                    }
                    break;

                case SessionAction.Ended:
                    if (_localSessions.TryGetValue(message.SessionId, out var session))
                    {
                        session.Status = SessionStatus.Ended;
                        session.EndedAt = DateTime.UtcNow;
                        await UnsubscribeFromSessionAsync(message.SessionId);
                    }
                    break;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to handle session lifecycle message");
        }
    }

    /// <summary>
    /// 处理协作事件消息
    /// </summary>
    private Task OnCollaborationEventMessageReceived(CollaborationMessage message)
    {
        try
        {
            if (message.SenderId == _nodeId)
            {
                return Task.CompletedTask; // 忽略自己发送的消息
            }

            _logger.LogDebug("Received collaboration event: {Action} in session {SessionId}",
                message.Action, message.SessionId);

            if (!_localSessions.TryGetValue(message.SessionId, out var session))
            {
                return Task.CompletedTask; // 会话不存在
            }

            switch (message.Action)
            {
                case CollaborationAction.Join:
                    if (message.Collaborator != null)
                    {
                        var existingCollaborator = session.Collaborators.FirstOrDefault(c => c.CollaboratorId == message.Collaborator.CollaboratorId);
                        if (existingCollaborator != null)
                        {
                            existingCollaborator.LastActiveAt = DateTime.UtcNow;
                            existingCollaborator.Status = CollaboratorStatus.Online;
                        }
                        else
                        {
                            session.Collaborators.Add(message.Collaborator);
                        }

                        OnCollaboratorJoined(new CollaboratorJoinedEventArgs
                        {
                            SessionId = message.SessionId,
                            Collaborator = message.Collaborator,
                            JoinedAt = message.Timestamp,
                            TotalCollaborators = session.Collaborators.Count
                        });
                    }
                    break;

                case CollaborationAction.Leave:
                    if (message.Collaborator != null)
                    {
                        var collaborator = session.Collaborators.FirstOrDefault(c => c.CollaboratorId == message.Collaborator.CollaboratorId);
                        if (collaborator != null)
                        {
                            session.Collaborators.Remove(collaborator);

                            OnCollaboratorLeft(new CollaboratorLeftEventArgs
                            {
                                SessionId = message.SessionId,
                                CollaboratorId = message.Collaborator.CollaboratorId,
                                CollaboratorName = message.Collaborator.Name,
                                LeftAt = message.Timestamp,
                                RemainingCollaborators = session.Collaborators.Count
                            });
                        }
                    }
                    break;
            }

            session.LastActivityAt = message.Timestamp;
            return Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to handle collaboration event message");
            return Task.CompletedTask;
        }
    }

    /// <summary>
    /// 处理操作消息
    /// </summary>
    private Task OnOperationMessageReceived(CollaborationOperationMessage message)
    {
        try
        {
            if (message.SenderId == _nodeId)
            {
                return Task.CompletedTask; // 忽略自己发送的消息
            }

            _logger.LogDebug("Received operation message: {OperationType} in session {SessionId}",
                message.Operation.Type, message.SessionId);

            if (_localSessions.TryGetValue(message.SessionId, out var session))
            {
                session.LastActivityAt = message.Timestamp;

                // 更新统计信息
                if (_sessionStatistics.TryGetValue(message.SessionId, out var stats))
                {
                    stats.TotalOperations++;
                }

                // 触发设计操作事件
                OnDesignOperationReceived(new DesignOperationEventArgs
                {
                    SessionId = message.SessionId,
                    Operation = message.Operation,
                    SourceCollaborator = session.Collaborators.FirstOrDefault(c => c.CollaboratorId == message.Operation.CollaboratorId),
                    ReceivedAt = DateTime.UtcNow,
                    ShouldBroadcast = false // 已经通过NATS广播了
                });
            }
            return Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to handle operation message");
            return Task.CompletedTask;
        }
    }

    /// <summary>
    /// 处理状态消息
    /// </summary>
    private Task OnStatusMessageReceived(CollaborationStatusMessage message)
    {
        try
        {
            if (message.SenderId == _nodeId)
            {
                return Task.CompletedTask;
            }

            if (_localSessions.TryGetValue(message.SessionId, out var session))
            {
                var collaborator = session.Collaborators.FirstOrDefault(c => c.CollaboratorId == message.CollaboratorId);
                if (collaborator != null)
                {
                    collaborator.Status = message.Status;
                    collaborator.LastActiveAt = message.Timestamp;
                    session.LastActivityAt = message.Timestamp;
                }
            }
            return Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to handle status message");
            return Task.CompletedTask;
        }
    }

    /// <summary>
    /// 处理光标消息
    /// </summary>
    private Task OnCursorMessageReceived(CollaborationCursorMessage message)
    {
        try
        {
            if (message.SenderId == _nodeId)
            {
                return Task.CompletedTask;
            }

            if (_localSessions.TryGetValue(message.SessionId, out var session))
            {
                var collaborator = session.Collaborators.FirstOrDefault(c => c.CollaboratorId == message.CollaboratorId);
                if (collaborator != null)
                {
                    collaborator.CursorPosition = message.Position;
                    collaborator.LastActiveAt = message.Timestamp;
                    session.LastActivityAt = message.Timestamp;
                }
            }
            return Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to handle cursor message");
            return Task.CompletedTask;
        }
    }

    /// <summary>
    /// 处理选择消息
    /// </summary>
    private Task OnSelectionMessageReceived(CollaborationSelectionMessage message)
    {
        try
        {
            if (message.SenderId == _nodeId)
            {
                return Task.CompletedTask;
            }

            if (_localSessions.TryGetValue(message.SessionId, out var session))
            {
                var collaborator = session.Collaborators.FirstOrDefault(c => c.CollaboratorId == message.CollaboratorId);
                if (collaborator != null)
                {
                    collaborator.CurrentSelection = message.Selection;
                    collaborator.LastActiveAt = message.Timestamp;
                    session.LastActivityAt = message.Timestamp;
                }
            }
            return Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to handle selection message");
            return Task.CompletedTask;
        }
    }

    /// <summary>
    /// 处理冲突消息
    /// </summary>
    private Task OnConflictMessageReceived(CollaborationConflictMessage message)
    {
        try
        {
            if (message.SenderId == _nodeId)
            {
                return Task.CompletedTask;
            }

            _logger.LogDebug("Received conflict message for session {SessionId}", message.SessionId);

            if (message.ConflictResult.HasConflict)
            {
                // 触发冲突检测事件
                OnConflictDetected(new ConflictDetectedEventArgs
                {
                    ConflictId = message.ConflictResult.ConflictId,
                    WorkflowId = _localSessions.TryGetValue(message.SessionId, out var session) ? session.WorkflowId : "",
                    ConflictType = message.ConflictResult.ConflictType,
                    DetectedAt = message.Timestamp,
                    Description = message.ConflictResult.Description
                });
            }
            return Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to handle conflict message");
            return Task.CompletedTask;
        }
    }

    /// <summary>
    /// 处理聊天消息
    /// </summary>
    private Task OnChatMessageReceived(CollaborationChatMessage message)
    {
        try
        {
            if (message.SenderId == _nodeId)
            {
                return Task.CompletedTask;
            }

            _logger.LogDebug("Received chat message in session {SessionId}", message.SessionId);

            // 这里可以触发聊天消息事件，如果需要的话
            // 目前只是记录日志
            return Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to handle chat message");
            return Task.CompletedTask;
        }
    }

    /// <summary>
    /// 处理心跳消息
    /// </summary>
    private Task OnHeartbeatMessageReceived(CollaborationHeartbeatMessage message)
    {
        try
        {
            if (message.SenderId == _nodeId)
            {
                return Task.CompletedTask;
            }

            _logger.LogDebug("Received heartbeat from node {NodeId}", message.NodeId);
            // 这里可以记录其他节点的状态信息
            return Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to handle heartbeat message");
            return Task.CompletedTask;
        }
    }

    #endregion

    #region 辅助方法

    /// <summary>
    /// 请求会话信息
    /// </summary>
    private Task<CollaborationSession?> RequestSessionInfoAsync(string sessionId, CancellationToken cancellationToken = default)
    {
        try
        {
            // TODO: 实现通过NATS请求会话信息的逻辑
            // 这里可以发送请求消息到其他节点，询问会话信息
            // 目前简化实现，返回null
            return Task.FromResult<CollaborationSession?>(null);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to request session info for {SessionId}", sessionId);
            return Task.FromResult<CollaborationSession?>(null);
        }
    }

    /// <summary>
    /// 克隆会话对象
    /// </summary>
    private static CollaborationSession CloneSession(CollaborationSession session)
    {
        return JsonSerializer.Deserialize<CollaborationSession>(
            JsonSerializer.Serialize(session)) ?? new CollaborationSession();
    }

    /// <summary>
    /// 生成冲突解决建议
    /// </summary>
    private static List<ConflictResolutionSuggestion> GenerateConflictResolutionSuggestions(DesignConflict conflict)
    {
        var suggestions = new List<ConflictResolutionSuggestion>();

        switch (conflict.ConflictType)
        {
            case ConflictType.ConcurrentEdit:
                suggestions.Add(new ConflictResolutionSuggestion
                {
                    Strategy = ConflictResolutionStrategy.AcceptCurrent,
                    Description = "Keep the current version and discard conflicting changes",
                    Priority = 1,
                    IsAutomatic = false,
                    ExpectedOutcome = "Current version is preserved"
                });

                suggestions.Add(new ConflictResolutionSuggestion
                {
                    Strategy = ConflictResolutionStrategy.AcceptIncoming,
                    Description = "Accept the incoming changes and overwrite current version",
                    Priority = 2,
                    IsAutomatic = false,
                    ExpectedOutcome = "Incoming changes are applied"
                });

                suggestions.Add(new ConflictResolutionSuggestion
                {
                    Strategy = ConflictResolutionStrategy.ManualMerge,
                    Description = "Manually merge the conflicting changes",
                    Priority = 3,
                    IsAutomatic = false,
                    ExpectedOutcome = "Best of both versions combined"
                });
                break;

            case ConflictType.DeleteWhileEditing:
                suggestions.Add(new ConflictResolutionSuggestion
                {
                    Strategy = ConflictResolutionStrategy.AcceptCurrent,
                    Description = "Cancel the delete operation and keep editing",
                    Priority = 1,
                    IsAutomatic = false,
                    ExpectedOutcome = "Object remains and can be edited"
                });
                break;

            default:
                suggestions.Add(new ConflictResolutionSuggestion
                {
                    Strategy = ConflictResolutionStrategy.ManualMerge,
                    Description = "Manually resolve the conflict",
                    Priority = 1,
                    IsAutomatic = false,
                    ExpectedOutcome = "Conflict resolved based on user decision"
                });
                break;
        }

        return suggestions;
    }

    /// <summary>
    /// 发送心跳
    /// </summary>
    private async void SendHeartbeat(object? state)
    {
        try
        {
            var heartbeat = new CollaborationHeartbeatMessage
            {
                SenderId = _nodeId,
                NodeId = _nodeId,
                NodeStatus = "healthy",
                ActiveSessionCount = _localSessions.Count(s => s.Value.Status == SessionStatus.Active),
                TotalCollaborators = _localSessions.Values.Sum(s => s.Collaborators.Count),
                Timestamp = DateTime.UtcNow
            };

            await _natsService.PublishAsync("flowcustom.collaboration.heartbeat", heartbeat);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send heartbeat");
        }
    }

    #endregion

    #region IDisposable实现

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed && disposing)
        {
            try
            {
                // 停止心跳定时器
                _heartbeatTimer?.Dispose();

                // 结束所有活跃会话
                var activeSessions = _localSessions.Values.Where(s => s.Status == SessionStatus.Active).ToList();
                foreach (var session in activeSessions)
                {
                    try
                    {
                        _ = EndSessionAsync(session.SessionId, "Service shutting down");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Failed to end session {SessionId} during disposal", session.SessionId);
                    }
                }

                // 清理存储
                _localSessions.Clear();
                _sessionHistory.Clear();
                _sessionConflicts.Clear();
                _sessionStatistics.Clear();
                _subscriptions.Clear();

                _logger.LogInformation("NatsCollaborationService disposed successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while disposing NatsCollaborationService");
            }
            finally
            {
                _disposed = true;
            }
        }
    }

    /// <summary>
    /// 析构函数
    /// </summary>
    ~NatsCollaborationService()
    {
        Dispose(false);
    }

    #endregion
}
