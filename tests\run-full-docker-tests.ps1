# FlowCustomV1 v0.0.1.7 完整Docker环境测试脚本
# 执行所有功能的全面测试

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("all", "infrastructure", "services", "workflows", "performance", "monitoring")]
    [string]$TestSuite = "all",
    
    [Parameter(Mandatory=$false)]
    [switch]$WithMonitoring,
    
    [Parameter(Mandatory=$false)]
    [switch]$WithLogging,
    
    [Parameter(Mandatory=$false)]
    [switch]$WithCache,
    
    [Parameter(Mandatory=$false)]
    [switch]$CleanStart,
    
    [Parameter(Mandatory=$false)]
    [int]$TimeoutMinutes = 60,
    
    [Parameter(Mandatory=$false)]
    [switch]$GenerateReport
)

# 脚本配置
$ErrorActionPreference = "Stop"
$TestStartTime = Get-Date
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$DockerDir = Join-Path $ScriptDir "docker"
$TestResultsDir = Join-Path $ScriptDir "test-results"
$LogsDir = Join-Path $TestResultsDir "logs"

# 创建必要的目录
New-Item -ItemType Directory -Force -Path $TestResultsDir | Out-Null
New-Item -ItemType Directory -Force -Path $LogsDir | Out-Null

# 日志函数
function Write-TestLog {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    Write-Host $logMessage
    $logMessage | Out-File -FilePath (Join-Path $LogsDir "full-test.log") -Append
}

function Write-TestSuccess {
    param([string]$Message)
    Write-TestLog $Message "SUCCESS"
}

function Write-TestError {
    param([string]$Message)
    Write-TestLog $Message "ERROR"
}

# 清理函数
function Stop-TestEnvironment {
    Write-TestLog "停止测试环境..."
    
    try {
        Push-Location $DockerDir
        
        # 停止所有服务
        docker-compose -f docker-compose.full-test.yml --profile test --profile monitoring --profile logging --profile cache down -v
        
        # 清理未使用的资源
        docker system prune -f
        
        Write-TestSuccess "测试环境已停止"
    }
    catch {
        Write-TestError "停止测试环境失败: $($_.Exception.Message)"
    }
    finally {
        Pop-Location
    }
}

# 启动测试环境
function Start-TestEnvironment {
    Write-TestLog "启动测试环境..."
    
    try {
        Push-Location $DockerDir
        
        # 构建profiles参数
        $profiles = @("test")
        if ($WithMonitoring) { $profiles += "monitoring" }
        if ($WithLogging) { $profiles += "logging" }
        if ($WithCache) { $profiles += "cache" }
        
        $profileArgs = $profiles | ForEach-Object { "--profile", $_ }
        
        # 启动基础设施
        Write-TestLog "启动基础设施服务..."
        docker-compose -f docker-compose.full-test.yml up -d nats-1 nats-2 nats-3 nats-surveyor mysql-full-test
        
        # 等待基础设施就绪
        Write-TestLog "等待基础设施就绪..."
        Start-Sleep -Seconds 30
        
        # 启动应用节点
        Write-TestLog "启动应用节点..."
        docker-compose -f docker-compose.full-test.yml up -d `
            master-node-beijing master-node-shanghai `
            worker-node-beijing-1 worker-node-beijing-2 worker-node-shanghai-1 `
            designer-node-beijing designer-node-shanghai `
            validator-node-beijing validator-node-shanghai `
            executor-node-beijing executor-node-shanghai
        
        # 等待应用节点就绪
        Write-TestLog "等待应用节点就绪..."
        Start-Sleep -Seconds 60
        
        # 启动可选服务
        if ($WithMonitoring -or $WithLogging -or $WithCache) {
            Write-TestLog "启动可选服务..."
            docker-compose -f docker-compose.full-test.yml @profileArgs up -d
            Start-Sleep -Seconds 30
        }
        
        Write-TestSuccess "测试环境启动完成"
    }
    catch {
        Write-TestError "启动测试环境失败: $($_.Exception.Message)"
        throw
    }
    finally {
        Pop-Location
    }
}

# 验证环境健康状态
function Test-EnvironmentHealth {
    Write-TestLog "验证环境健康状态..."
    
    $healthChecks = @(
        @{ Name = "NATS-1"; Url = "http://localhost:28222/healthz" },
        @{ Name = "NATS-2"; Url = "http://localhost:28223/healthz" },
        @{ Name = "NATS-3"; Url = "http://localhost:28224/healthz" },
        @{ Name = "Master-Beijing"; Url = "http://localhost:25001/health" },
        @{ Name = "Master-Shanghai"; Url = "http://localhost:25002/health" },
        @{ Name = "Worker-Beijing-1"; Url = "http://localhost:25011/health" },
        @{ Name = "Worker-Beijing-2"; Url = "http://localhost:25012/health" },
        @{ Name = "Worker-Shanghai-1"; Url = "http://localhost:25013/health" },
        @{ Name = "Designer-Beijing"; Url = "http://localhost:25021/health" },
        @{ Name = "Designer-Shanghai"; Url = "http://localhost:25022/health" },
        @{ Name = "Validator-Beijing"; Url = "http://localhost:25031/health" },
        @{ Name = "Validator-Shanghai"; Url = "http://localhost:25032/health" },
        @{ Name = "Executor-Beijing"; Url = "http://localhost:25041/health" },
        @{ Name = "Executor-Shanghai"; Url = "http://localhost:25042/health" }
    )
    
    $failedChecks = @()
    
    foreach ($check in $healthChecks) {
        try {
            $response = Invoke-WebRequest -Uri $check.Url -TimeoutSec 10 -UseBasicParsing
            if ($response.StatusCode -eq 200) {
                Write-TestLog "✅ $($check.Name) 健康检查通过"
            } else {
                Write-TestError "❌ $($check.Name) 健康检查失败: HTTP $($response.StatusCode)"
                $failedChecks += $check.Name
            }
        }
        catch {
            Write-TestError "❌ $($check.Name) 健康检查失败: $($_.Exception.Message)"
            $failedChecks += $check.Name
        }
    }
    
    if ($failedChecks.Count -gt 0) {
        throw "健康检查失败的服务: $($failedChecks -join ', ')"
    }
    
    Write-TestSuccess "所有服务健康检查通过"
}

# 执行测试套件
function Invoke-TestSuite {
    Write-TestLog "执行测试套件: $TestSuite"
    
    try {
        Push-Location $DockerDir
        
        # 启动测试协调器
        Write-TestLog "启动测试协调器..."
        $env:TEST_SUITE = $TestSuite
        $env:TEST_TIMEOUT_MINUTES = $TimeoutMinutes
        
        docker-compose -f docker-compose.full-test.yml --profile test up --abort-on-container-exit full-test-coordinator
        
        Write-TestSuccess "测试套件执行完成"
    }
    catch {
        Write-TestError "测试套件执行失败: $($_.Exception.Message)"
        throw
    }
    finally {
        Pop-Location
    }
}

# 收集测试结果
function Get-TestResults {
    Write-TestLog "收集测试结果..."
    
    try {
        # 从测试协调器容器复制结果
        $containerName = "flowcustom-full-test-coordinator"
        
        if (docker ps -a --format "{{.Names}}" | Select-String -Pattern $containerName) {
            docker cp "${containerName}:/app/test-results/." $TestResultsDir
            docker cp "${containerName}:/app/reports/." (Join-Path $TestResultsDir "reports")
            
            Write-TestSuccess "测试结果已收集到: $TestResultsDir"
        } else {
            Write-TestError "测试协调器容器未找到"
        }
    }
    catch {
        Write-TestError "收集测试结果失败: $($_.Exception.Message)"
    }
}

# 生成测试报告
function New-TestReport {
    if (-not $GenerateReport) {
        return
    }
    
    Write-TestLog "生成测试报告..."
    
    try {
        $reportData = @{
            TestSuite = $TestSuite
            StartTime = $TestStartTime
            EndTime = Get-Date
            Duration = (Get-Date) - $TestStartTime
            Environment = @{
                WithMonitoring = $WithMonitoring
                WithLogging = $WithLogging
                WithCache = $WithCache
                TimeoutMinutes = $TimeoutMinutes
            }
            Results = @{}
        }
        
        # 读取测试结果文件
        $resultFiles = Get-ChildItem -Path $TestResultsDir -Filter "*.json" -Recurse
        foreach ($file in $resultFiles) {
            try {
                $content = Get-Content $file.FullName | ConvertFrom-Json
                $reportData.Results[$file.BaseName] = $content
            }
            catch {
                Write-TestError "读取结果文件失败: $($file.FullName)"
            }
        }
        
        # 保存报告
        $reportPath = Join-Path $TestResultsDir "full-test-summary.json"
        $reportData | ConvertTo-Json -Depth 10 | Out-File $reportPath
        
        Write-TestSuccess "测试报告已生成: $reportPath"
    }
    catch {
        Write-TestError "生成测试报告失败: $($_.Exception.Message)"
    }
}

# 主函数
function Main {
    Write-TestLog "🚀 开始FlowCustomV1 v0.0.1.7完整Docker环境测试"
    Write-TestLog "测试套件: $TestSuite"
    Write-TestLog "超时时间: $TimeoutMinutes 分钟"
    Write-TestLog "监控服务: $WithMonitoring"
    Write-TestLog "日志服务: $WithLogging"
    Write-TestLog "缓存服务: $WithCache"
    
    try {
        # 清理环境（如果需要）
        if ($CleanStart) {
            Stop-TestEnvironment
        }
        
        # 启动测试环境
        Start-TestEnvironment
        
        # 验证环境健康状态
        Test-EnvironmentHealth
        
        # 执行测试套件
        Invoke-TestSuite
        
        # 收集测试结果
        Get-TestResults
        
        # 生成测试报告
        New-TestReport
        
        Write-TestSuccess "✅ FlowCustomV1 v0.0.1.7完整Docker环境测试完成！"
        Write-TestLog "测试结果目录: $TestResultsDir"
        
    }
    catch {
        Write-TestError "❌ 测试执行失败: $($_.Exception.Message)"
        exit 1
    }
    finally {
        # 清理环境
        if (-not $env:KEEP_ENVIRONMENT) {
            Stop-TestEnvironment
        }
    }
}

# 执行主函数
Main
