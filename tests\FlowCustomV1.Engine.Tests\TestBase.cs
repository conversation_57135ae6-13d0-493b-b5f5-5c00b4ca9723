using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using FlowCustomV1.Core.Models.Workflow;
using FlowCustomV1.Engine;
using FlowCustomV1.Engine.Context;
using System.Collections.Concurrent;

// 注意：Engine层测试不应该直接依赖EntityFrameworkCore
// 如果需要数据库功能，应该通过接口和模拟对象进行测试

namespace FlowCustomV1.Engine.Tests;

/// <summary>
/// 测试基类
/// 提供测试所需的通用设置和辅助方法
/// </summary>
public abstract class TestBase : IDisposable
{
    protected IServiceProvider ServiceProvider { get; private set; }
    protected IServiceCollection Services { get; private set; } = new ServiceCollection();
    protected ILogger Logger { get; private set; }

    protected TestBase(IServiceProvider? serviceProvider = null)
    {
        if (serviceProvider == null)
        {
            Services = new ServiceCollection();
            SetupServices();
            ServiceProvider = Services.BuildServiceProvider();
        }
        else
        {
            ServiceProvider = serviceProvider;
        }

        Logger = ServiceProvider.GetRequiredService<ILogger<TestBase>>();
    }

    /// <summary>
    /// 设置测试服务
    /// </summary>
    protected virtual void SetupServices()
    {
        // 添加日志服务
        Services.AddLogging(builder =>
        {
            builder.AddConsole();
            builder.SetMinimumLevel(LogLevel.Debug);
        });

        // 添加工作流引擎服务
        Services.AddWorkflowEngine(options =>
        {
            options.EnableDetailedLogging = true;
            options.SchedulerOptions.MaxConcurrentExecutions = 5;
            options.SchedulerOptions.QueueCapacity = 100;
            options.SchedulerOptions.EnableDetailedLogging = true;
        });
        
        // 注意：Engine层测试不应该直接依赖Infrastructure层
        // 使用模拟对象替代真实的数据库和仓储实现
        // 这样保持了架构层次的清晰性

        // TODO: 使用Mock对象替代真实实现
        // Services.AddScoped<IExecutionRepository, Mock<IExecutionRepository>>();
        // Services.AddScoped<IWorkflowRepository, Mock<IWorkflowRepository>>();
    }



    /// <summary>
    /// 创建测试用的工作流定义
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="name">工作流名称</param>
    /// <returns>工作流定义</returns>
    protected WorkflowDefinition CreateTestWorkflowDefinition(string workflowId = "test-workflow", string name = "Test Workflow")
    {
        return new WorkflowDefinition
        {
            WorkflowId = workflowId,
            Name = name,
            Version = "1.0.0",
            Description = "Test workflow for unit testing",
            Nodes = new List<WorkflowNode>
            {
                new WorkflowNode
                {
                    NodeId = "start-node",
                    NodeType = "Start",
                    Name = "Start Node"
                },
                new WorkflowNode
                {
                    NodeId = "task-node-1",
                    NodeType = "Task",
                    Name = "Task Node 1",
                    Configuration = new NodeConfiguration
                    {
                        Parameters = new Dictionary<string, object>
                        {
                            ["TaskType"] = "TestTask"
                        }
                    }
                },
                new WorkflowNode
                {
                    NodeId = "end-node",
                    NodeType = "End",
                    Name = "End Node"
                }
            },
            Connections = new List<WorkflowConnection>
            {
                new WorkflowConnection
                {
                    SourceNodeId = "start-node",
                    TargetNodeId = "task-node-1"
                },
                new WorkflowConnection
                {
                    SourceNodeId = "task-node-1",
                    TargetNodeId = "end-node"
                }
            },
            InputParameters = new List<WorkflowParameterDefinition>(),
            OutputParameters = new List<WorkflowParameterDefinition>(),
            Configuration = new WorkflowConfiguration()
        };
    }


    /// <summary>
    /// 创建测试用的系统上下文
    /// </summary>
    /// <returns>系统上下文</returns>
    protected SystemExecutionContext CreateTestSystemContext()
    {
        return new SystemExecutionContext
        {
            SystemId = Guid.NewGuid().ToString(),
            ServiceProvider = ServiceProvider,
            SystemStartTime = DateTime.UtcNow,
            GlobalConfiguration = new Dictionary<string, object>
            {
                ["TestMode"] = true,
                ["MaxExecutionTime"] = TimeSpan.FromMinutes(5)
            }
        };
    }

    /// <summary>
    /// 创建测试用的工作流执行上下文
    /// </summary>
    /// <param name="workflowDefinition">工作流定义</param>
    /// <param name="inputData">输入数据</param>
    /// <returns>工作流执行上下文</returns>
    protected EngineWorkflowContext CreateTestWorkflowContext(
        WorkflowDefinition workflowDefinition,
        Dictionary<string, object>? inputData = null)
    {
        var context = new EngineWorkflowContext
        {
            ExecutionId = Guid.NewGuid().ToString(),
            WorkflowId = workflowDefinition.WorkflowId,
            Definition = workflowDefinition.Clone(),
            SystemContext = CreateTestSystemContext(),
            InputData = new ConcurrentDictionary<string, object>(inputData ?? new Dictionary<string, object>()),
            StartedAt = DateTime.UtcNow,
            WorkflowData = new ConcurrentDictionary<string, object>(new Dictionary<string, object>()),
            NodeResults = new ConcurrentDictionary<string, NodeExecutionResult>(new Dictionary<string, NodeExecutionResult>()),
            NodeStates = new ConcurrentDictionary<string, NodeExecutionState>(new Dictionary<string, NodeExecutionState>()),
            Stats = new WorkflowExecutionStats(),
            Metadata = new Dictionary<string, object>()
        };

        // 初始化工作流数据
        foreach (var kvp in context.InputData)
        {
            context.WorkflowData[kvp.Key] = kvp.Value;
        }

        // 初始化节点状态
        foreach (var node in workflowDefinition.Nodes)
        {
            context.NodeStates[node.NodeId] = NodeExecutionState.NotStarted;
        }

        return context;
    }


    /// <summary>
    /// 等待条件满足
    /// </summary>
    /// <param name="condition">条件函数</param>
    /// <param name="timeout">超时时间</param>
    /// <param name="checkInterval">检查间隔</param>
    /// <returns>是否满足条件</returns>
    protected async Task<bool> WaitForConditionAsync(
        Func<bool> condition,
        TimeSpan timeout,
        TimeSpan? checkInterval = null)
    {
        checkInterval ??= TimeSpan.FromMilliseconds(100);
        var startTime = DateTime.UtcNow;

        while (DateTime.UtcNow - startTime < timeout)
        {
            if (condition())
                return true;

            await Task.Delay(checkInterval.Value);
        }

        return false;
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    /// <summary>
    /// 创建测试用的节点定义
    /// </summary>
    /// <param name="nodeType">节点类型</param>
    /// <param name="displayName">显示名称</param>
    /// <returns>节点定义</returns>
    protected NodeDefinition CreateTestNodeDefinition(string nodeType = "Task", string displayName = "Test Node")
    {
        return new NodeDefinition
        {
            NodeType = nodeType,
            DisplayName = displayName,
            Description = "Test node definition",
            InputParameters = new List<NodeParameterDefinition>(),
            OutputParameters = new List<NodeParameterDefinition>(),
            ConfigurationParameters = new List<NodeParameterDefinition>()
        };
    }

    /// <summary>
    /// 创建测试用的节点定义
    /// </summary>
    /// <param name="nodeId">节点ID</param>
    /// <param name="nodeType">节点类型</param>
    /// <returns>工作流节点</returns>
    protected WorkflowNode CreateTestWorkflowNode(string nodeId = "test-node", string nodeType = "Task")
    {
        return new WorkflowNode
        {
            NodeId = nodeId,
            NodeType = nodeType,
            Name = "Test Node",
            Configuration = new NodeConfiguration()
        };
    }

    /// <summary>
    /// 创建测试用的连接
    /// </summary>
    /// <param name="sourceNodeId">源节点ID</param>
    /// <param name="targetNodeId">目标节点ID</param>
    /// <returns>工作流连接</returns>
    protected WorkflowConnection CreateTestConnection(string sourceNodeId = "source-node", string targetNodeId = "target-node")
    {
        return new WorkflowConnection
        {
            SourceNodeId = sourceNodeId,
            TargetNodeId = targetNodeId
        };
    }

    /// <summary>
    /// 创建测试用的节点资源配置
    /// </summary>
    /// <returns>节点资源配置</returns>
    protected NodeResourceRequirements CreateTestResourceRequirements()
    {
        return new NodeResourceRequirements
        {
            RequiredCpuCores = 1.0,
            RequiredMemoryMb = 100,
            RequiredDiskMb = 50,
            RequiredTags = new HashSet<string> { "test" }
        };
    }

    public void Dispose()
    {
        if (ServiceProvider is IDisposable disposable)
        {
            disposable.Dispose();
        }
    }
}
