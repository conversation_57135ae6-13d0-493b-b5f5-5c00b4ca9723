using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using FlowCustomV1.Infrastructure.Configuration;
using FlowCustomV1.Infrastructure.Data;
using FlowCustomV1.Infrastructure.Health;
using System.Diagnostics;

namespace FlowCustomV1.Infrastructure.Services;

/// <summary>
/// 数据库初始化服务实现
/// 提供数据库自动创建、迁移和健康检查功能
/// </summary>
public class DatabaseInitializationService : IDatabaseInitializationService
{
    private readonly WorkflowDbContext _dbContext;
    private readonly DatabaseOptions _options;
    private readonly ILogger<DatabaseInitializationService> _logger;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="dbContext">数据库上下文</param>
    /// <param name="options">数据库配置选项</param>
    /// <param name="logger">日志记录器</param>
    public DatabaseInitializationService(
        WorkflowDbContext dbContext,
        IOptions<DatabaseOptions> options,
        ILogger<DatabaseInitializationService> logger)
    {
        _dbContext = dbContext;
        _options = options.Value;
        _logger = logger;
    }

    /// <summary>
    /// 初始化数据库
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>初始化是否成功</returns>
    public async Task<bool> InitializeDatabaseAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("开始初始化数据库...");

            // 1. 检查数据库连接
            var canConnect = await CheckConnectionAsync(cancellationToken);
            if (!canConnect)
            {
                _logger.LogError("无法连接到数据库");
                return false;
            }

            // 2. 确保数据库存在
            var databaseCreated = await EnsureDatabaseCreatedAsync(cancellationToken);
            if (!databaseCreated)
            {
                _logger.LogError("无法创建数据库");
                return false;
            }

            // 3. 应用所有待处理的迁移
            if (_options.AutoMigrate)
            {
                var migrated = await MigrateDatabaseAsync(cancellationToken);
                if (!migrated)
                {
                    _logger.LogError("数据库迁移失败");
                    return false;
                }
            }

            // 4. 验证表结构完整性
            var schemaValid = await ValidateSchemaAsync(cancellationToken);
            if (!schemaValid)
            {
                _logger.LogError("数据库表结构验证失败");
                return false;
            }

            // 5. 创建必要的索引和约束
            await EnsureIndexesAndConstraintsAsync(cancellationToken);

            _logger.LogInformation("数据库初始化完成");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "数据库初始化过程中发生错误");
            
            // 尝试错误恢复
            if (_options.AutoMigrate)
            {
                return await HandleInitializationErrorAsync(ex, cancellationToken);
            }
            
            return false;
        }
    }

    /// <summary>
    /// 检查数据库连接
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否可以连接</returns>
    private async Task<bool> CheckConnectionAsync(CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("检查数据库连接...");
            return await _dbContext.Database.CanConnectAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "数据库连接检查失败");
            return false;
        }
    }

    /// <summary>
    /// 确保数据库存在
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作是否成功</returns>
    public async Task<bool> EnsureDatabaseCreatedAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("确保数据库存在...");
            var created = await _dbContext.Database.EnsureCreatedAsync(cancellationToken);
            
            if (created)
            {
                _logger.LogInformation("数据库已创建");
            }
            else
            {
                _logger.LogDebug("数据库已存在");
            }
            
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建数据库失败");
            return false;
        }
    }

    /// <summary>
    /// 迁移数据库到最新版本
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>迁移是否成功</returns>
    public async Task<bool> MigrateDatabaseAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("开始数据库迁移...");

            // 获取待处理的迁移
            var pendingMigrations = await GetPendingMigrationsAsync(cancellationToken);
            var pendingList = pendingMigrations.ToList();

            if (!pendingList.Any())
            {
                _logger.LogDebug("没有待处理的迁移");
                return true;
            }

            _logger.LogInformation("发现 {Count} 个待处理的迁移: {Migrations}", 
                pendingList.Count, string.Join(", ", pendingList));

            // 备份数据库（如果配置了备份）
            if (_options.Migration.CreateBackup)
            {
                await BackupDatabaseAsync(cancellationToken: cancellationToken);
            }

            // 应用迁移
            using var timeoutCts = new CancellationTokenSource(_options.Migration.Timeout);
            using var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(
                cancellationToken, timeoutCts.Token);

            await _dbContext.Database.MigrateAsync(combinedCts.Token);

            _logger.LogInformation("数据库迁移完成");
            return true;
        }
        catch (OperationCanceledException) when (cancellationToken.IsCancellationRequested)
        {
            _logger.LogWarning("数据库迁移被取消");
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "数据库迁移失败");
            return false;
        }
    }

    /// <summary>
    /// 验证数据库表结构
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证是否通过</returns>
    public async Task<bool> ValidateSchemaAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("验证数据库表结构...");

            // 检查必要的表是否存在
            var requiredTables = new[] { "WorkflowDefinitions", "WorkflowInstances", "NodeExecutions" };
            
            foreach (var tableName in requiredTables)
            {
                var tableExists = await CheckTableExistsAsync(tableName, cancellationToken);
                if (!tableExists)
                {
                    _logger.LogError("必需的表 {TableName} 不存在", tableName);
                    return false;
                }
            }

            _logger.LogDebug("数据库表结构验证通过");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证数据库表结构时发生错误");
            return false;
        }
    }

    /// <summary>
    /// 检查表是否存在
    /// </summary>
    /// <param name="tableName">表名</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>表是否存在</returns>
    private async Task<bool> CheckTableExistsAsync(string tableName, CancellationToken cancellationToken)
    {
        try
        {
            // 对于内存数据库，直接检查DbSet是否可用
            if (_options.Provider.ToLower() == "inmemory")
            {
                // 内存数据库中，如果能创建DbContext，表就存在
                return tableName switch
                {
                    "WorkflowDefinitions" => _dbContext.WorkflowDefinitions != null,
                    "WorkflowInstances" => _dbContext.WorkflowInstances != null,
                    "NodeExecutions" => _dbContext.NodeExecutions != null,
                    _ => false
                };
            }

            // 对于关系型数据库，使用SQL查询
            var sql = _options.Provider.ToLower() switch
            {
                "mysql" => $"SELECT COUNT(*) as Value FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = '{tableName}'",
                "sqlite" or _ => $"SELECT COUNT(*) as Value FROM sqlite_master WHERE type='table' AND name='{tableName}'"
            };

            var count = await _dbContext.Database.SqlQueryRaw<int>(sql).FirstOrDefaultAsync(cancellationToken);
            return count > 0;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "检查表 {TableName} 是否存在时发生错误", tableName);
            return false;
        }
    }

    /// <summary>
    /// 确保索引和约束存在
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    private Task EnsureIndexesAndConstraintsAsync(CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("确保索引和约束存在...");

            // 这里可以添加额外的索引创建逻辑
            // 目前索引已在 OnModelCreating 中定义

            return Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "创建索引和约束时发生错误");
            return Task.CompletedTask;
        }
    }

    /// <summary>
    /// 处理初始化错误
    /// </summary>
    /// <param name="exception">异常</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否恢复成功</returns>
    private async Task<bool> HandleInitializationErrorAsync(Exception exception, CancellationToken cancellationToken)
    {
        _logger.LogWarning("尝试从初始化错误中恢复...");

        try
        {
            // 尝试修复数据库
            var repaired = await RepairDatabaseAsync(cancellationToken);
            if (repaired)
            {
                _logger.LogInformation("数据库修复成功");
                return true;
            }

            // 如果修复失败，尝试重置数据库
            if (_options.Migration.CreateBackup)
            {
                var reset = await ResetDatabaseAsync(cancellationToken);
                if (reset)
                {
                    _logger.LogInformation("数据库重置成功");
                    return true;
                }
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "错误恢复过程中发生异常");
            return false;
        }
    }

    /// <summary>
    /// 检查数据库健康状态
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>健康状态</returns>
    public async Task<DatabaseHealthStatus> CheckDatabaseHealthAsync(CancellationToken cancellationToken = default)
    {
        var status = new DatabaseHealthStatus();
        var stopwatch = Stopwatch.StartNew();

        try
        {
            // 检查连接
            status.CanConnect = await CheckConnectionAsync(cancellationToken);
            if (!status.CanConnect)
            {
                status.AddIssue("无法连接到数据库");
                return status;
            }

            // 检查数据库是否存在
            try
            {
                status.DatabaseExists = await _dbContext.Database.CanConnectAsync(cancellationToken);
            }
            catch
            {
                status.DatabaseExists = false;
                status.AddIssue("数据库不存在");
            }

            // 检查表是否存在
            if (status.DatabaseExists)
            {
                var requiredTables = new[] { "WorkflowDefinitions", "WorkflowInstances", "NodeExecutions" };
                var existingTables = 0;

                foreach (var tableName in requiredTables)
                {
                    if (await CheckTableExistsAsync(tableName, cancellationToken))
                    {
                        existingTables++;
                    }
                    else
                    {
                        status.AddIssue($"表 {tableName} 不存在");
                    }
                }

                status.TablesExist = existingTables == requiredTables.Length;
            }

            // 检查待处理的迁移
            if (status.DatabaseExists)
            {
                var pendingMigrations = await GetPendingMigrationsAsync(cancellationToken);
                status.PendingMigrationsCount = pendingMigrations.Count();

                if (status.PendingMigrationsCount > 0)
                {
                    status.AddRecommendation($"有 {status.PendingMigrationsCount} 个待处理的迁移");
                }
            }

            // 验证表结构
            status.SchemaIsValid = await ValidateSchemaAsync(cancellationToken);

            // 设置整体健康状态
            status.IsHealthy = status.CanConnect && status.DatabaseExists &&
                              status.TablesExist && status.SchemaIsValid &&
                              status.PendingMigrationsCount == 0;

            // 添加详细信息
            status.AddDetail("Provider", _options.Provider);
            status.AddDetail("AutoInitialize", _options.AutoInitialize);
            status.AddDetail("AutoMigrate", _options.AutoMigrate);
        }
        catch (Exception ex)
        {
            status.SetError("健康检查过程中发生错误", ex);
        }
        finally
        {
            stopwatch.Stop();
            status.ResponseTimeMs = stopwatch.Elapsed.TotalMilliseconds;
        }

        return status;
    }

    /// <summary>
    /// 获取待处理的迁移列表
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>待处理的迁移ID列表</returns>
    public async Task<IEnumerable<string>> GetPendingMigrationsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            return await _dbContext.Database.GetPendingMigrationsAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "获取待处理迁移列表时发生错误");
            return Enumerable.Empty<string>();
        }
    }

    /// <summary>
    /// 获取已应用的迁移列表
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>已应用的迁移ID列表</returns>
    public async Task<IEnumerable<string>> GetAppliedMigrationsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            return await _dbContext.Database.GetAppliedMigrationsAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "获取已应用迁移列表时发生错误");
            return Enumerable.Empty<string>();
        }
    }

    /// <summary>
    /// 备份数据库
    /// </summary>
    /// <param name="backupPath">备份文件路径</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>备份是否成功</returns>
    public Task<bool> BackupDatabaseAsync(string? backupPath = null, CancellationToken cancellationToken = default)
    {
        try
        {
            if (_options.Provider.ToLower() != "sqlite")
            {
                _logger.LogWarning("当前仅支持SQLite数据库备份");
                return Task.FromResult(false);
            }

            // 生成备份文件路径
            if (string.IsNullOrEmpty(backupPath))
            {
                var backupDir = _options.Migration.BackupDirectory;
                Directory.CreateDirectory(backupDir);

                var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                backupPath = Path.Combine(backupDir, $"workflow_backup_{timestamp}.db");
            }

            // 获取源数据库文件路径
            var connectionString = _options.ConnectionString;
            var sourceFile = ExtractFilePathFromConnectionString(connectionString);

            if (string.IsNullOrEmpty(sourceFile) || !File.Exists(sourceFile))
            {
                _logger.LogError("无法找到源数据库文件: {SourceFile}", sourceFile);
                return Task.FromResult(false);
            }

            // 复制文件
            File.Copy(sourceFile, backupPath, true);

            _logger.LogInformation("数据库备份完成: {BackupPath}", backupPath);
            return Task.FromResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "备份数据库时发生错误");
            return Task.FromResult(false);
        }
    }

    /// <summary>
    /// 从连接字符串中提取文件路径
    /// </summary>
    /// <param name="connectionString">连接字符串</param>
    /// <returns>文件路径</returns>
    private static string? ExtractFilePathFromConnectionString(string connectionString)
    {
        // 简单的SQLite连接字符串解析
        var parts = connectionString.Split(';');
        foreach (var part in parts)
        {
            var keyValue = part.Split('=');
            if (keyValue.Length == 2 &&
                keyValue[0].Trim().Equals("Data Source", StringComparison.OrdinalIgnoreCase))
            {
                return keyValue[1].Trim();
            }
        }
        return null;
    }

    /// <summary>
    /// 修复数据库
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>修复是否成功</returns>
    public async Task<bool> RepairDatabaseAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("开始修复数据库...");

            // 1. 尝试重新连接
            var canConnect = await CheckConnectionAsync(cancellationToken);
            if (!canConnect)
            {
                _logger.LogError("无法连接到数据库，修复失败");
                return false;
            }

            // 2. 确保数据库存在
            await EnsureDatabaseCreatedAsync(cancellationToken);

            // 3. 尝试应用迁移
            var migrated = await MigrateDatabaseAsync(cancellationToken);
            if (!migrated)
            {
                _logger.LogWarning("迁移失败，尝试重置数据库");
                return await ResetDatabaseAsync(cancellationToken);
            }

            // 4. 验证修复结果
            var schemaValid = await ValidateSchemaAsync(cancellationToken);
            if (!schemaValid)
            {
                _logger.LogWarning("表结构验证失败，尝试重置数据库");
                return await ResetDatabaseAsync(cancellationToken);
            }

            _logger.LogInformation("数据库修复完成");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "修复数据库时发生错误");
            return false;
        }
    }

    /// <summary>
    /// 重置数据库
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>重置是否成功</returns>
    public async Task<bool> ResetDatabaseAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogWarning("开始重置数据库...");

            // 备份现有数据库
            if (_options.Migration.CreateBackup)
            {
                await BackupDatabaseAsync(cancellationToken: cancellationToken);
            }

            // 删除数据库
            var deleted = await _dbContext.Database.EnsureDeletedAsync(cancellationToken);
            if (deleted)
            {
                _logger.LogInformation("已删除现有数据库");
            }

            // 重新创建数据库
            var created = await _dbContext.Database.EnsureCreatedAsync(cancellationToken);
            if (created)
            {
                _logger.LogInformation("已重新创建数据库");
            }

            // 应用迁移
            await _dbContext.Database.MigrateAsync(cancellationToken);

            // 验证重置结果
            var schemaValid = await ValidateSchemaAsync(cancellationToken);
            if (!schemaValid)
            {
                _logger.LogError("数据库重置后表结构验证失败");
                return false;
            }

            _logger.LogInformation("数据库重置完成");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "重置数据库时发生错误");
            return false;
        }
    }
}
