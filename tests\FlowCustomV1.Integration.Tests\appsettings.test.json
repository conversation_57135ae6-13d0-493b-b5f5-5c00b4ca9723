{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "FlowCustomV1": "Debug"}}, "Nats": {"Servers": ["nats://localhost:24222"], "ConnectionName": "FlowCustomV1-Integration-Test", "Username": "<PERSON><PERSON><PERSON>", "Password": "flowcustom_password", "ConnectionTimeoutSeconds": 30, "ReconnectIntervalSeconds": 2, "MaxReconnectAttempts": 10, "EnableAutoReconnect": true, "PingIntervalSeconds": 30, "MaxPingsOutstanding": 3, "EnableVerboseLogging": false, "JetStream": {"Enabled": true, "Domain": "flowcustom-test", "ApiPrefix": "$JS.API", "DefaultStream": {"Name": "FLOWCUSTOM_INTEGRATION_TEST", "Subjects": ["flowcustom-test.>"], "Storage": "memory", "MaxMessages": 10000, "MaxBytes": 10485760, "MaxAgeSeconds": 3600, "Replicas": 1}}}, "Database": {"ConnectionString": "Server=mysql;Port=23306;Database=flowcustom_test;Uid=flowcustom;Pwd=TestPassword123!;", "Provider": "MySQL", "AutoInitialize": true, "AutoMigrate": true, "BackupBeforeMigration": false, "FallbackToInMemory": false, "HealthCheckInterval": "00:01:00", "Migration": {"Timeout": "00:02:00", "RetryCount": 3, "CreateBackup": false, "BackupDirectory": "test_backups", "ValidateAfterMigration": true}, "ConnectionPool": {"MaxPoolSize": 20, "MinPoolSize": 2, "ConnectionTimeout": 30, "CommandTimeout": 60, "ConnectionLifetime": 300}}}