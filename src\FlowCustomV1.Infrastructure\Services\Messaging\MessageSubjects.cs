using FlowCustomV1.Core.Interfaces.Messaging;

namespace FlowCustomV1.Infrastructure.Services.Messaging;

/// <summary>
/// NATS消息主题定义
/// 基于架构设计文档中的主题规范，使用配置化的主题服务
/// 这是Infrastructure层的具体实现，依赖于IMessageTopicService
/// </summary>
public class MessageSubjects
{
    private readonly IMessageTopicService _topicService;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="topicService">消息主题服务</param>
    public MessageSubjects(IMessageTopicService topicService)
    {
        _topicService = topicService ?? throw new ArgumentNullException(nameof(topicService));
    }

    #region 集群管理主题

    /// <summary>
    /// 集群管理根主题
    /// </summary>
    public string CLUSTER_ROOT => _topicService.GetClusterRootTopic();

    /// <summary>
    /// 获取节点心跳主题
    /// </summary>
    /// <param name="nodeId">节点ID</param>
    /// <returns>节点心跳主题</returns>
    public string GetNodeHeartbeatTopic(string nodeId) => _topicService.GetNodeHeartbeatTopic(nodeId);

    /// <summary>
    /// 服务发现主题
    /// </summary>
    public string SERVICE_DISCOVERY => _topicService.GetServiceDiscoveryTopic();

    /// <summary>
    /// 集群配置更新主题
    /// </summary>
    public string CLUSTER_CONFIG => _topicService.GetClusterConfigTopic();

    /// <summary>
    /// 节点注册主题
    /// </summary>
    public string NODE_REGISTER => _topicService.GetNodeRegisterTopic();

    /// <summary>
    /// 节点下线主题
    /// </summary>
    public string NODE_UNREGISTER => _topicService.GetNodeUnregisterTopic();

    #endregion

    #region 工作流核心功能主题

    /// <summary>
    /// 工作流根主题
    /// </summary>
    public string WORKFLOW_ROOT => _topicService.GetWorkflowRootTopic();

    /// <summary>
    /// 获取工作流事件主题
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <returns>工作流事件主题</returns>
    public string GetWorkflowEventsTopic(string workflowId) => _topicService.GetWorkflowEventsTopic(workflowId);

    /// <summary>
    /// 获取工作流状态主题
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <returns>工作流状态主题</returns>
    public string GetWorkflowStateTopic(string workflowId) => _topicService.GetWorkflowStateTopic(workflowId);

    /// <summary>
    /// 获取工作流执行主题
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="executionId">执行ID</param>
    /// <returns>工作流执行主题</returns>
    public string GetWorkflowExecutionTopic(string workflowId, string executionId) => _topicService.GetWorkflowExecutionTopic(workflowId, executionId);

    #endregion

    #region 节点任务分发主题

    /// <summary>
    /// 节点根主题
    /// </summary>
    public string NODES_ROOT => _topicService.GetNodesRootTopic();

    /// <summary>
    /// 获取节点任务主题
    /// </summary>
    /// <param name="nodeId">节点ID</param>
    /// <returns>节点任务主题</returns>
    public string GetNodeTasksTopic(string nodeId) => _topicService.GetNodeTasksTopic(nodeId);

    /// <summary>
    /// 获取节点健康检查主题
    /// </summary>
    /// <param name="nodeId">节点ID</param>
    /// <returns>节点健康检查主题</returns>
    public string GetNodeHealthTopic(string nodeId) => _topicService.GetNodeHealthTopic(nodeId);

    /// <summary>
    /// 获取节点状态主题
    /// </summary>
    /// <param name="nodeId">节点ID</param>
    /// <returns>节点状态主题</returns>
    public string GetNodeStatusTopic(string nodeId) => _topicService.GetNodeStatusTopic(nodeId);

    #endregion

    #region 任务调度主题

    /// <summary>
    /// 任务调度根主题
    /// </summary>
    public string TASKS_ROOT => _topicService.GetTasksRootTopic();

    /// <summary>
    /// 高优先级任务队列主题
    /// </summary>
    public string TASKS_HIGH_PRIORITY => _topicService.GetHighPriorityTasksTopic();

    /// <summary>
    /// 普通优先级任务队列主题
    /// </summary>
    public string TASKS_NORMAL_PRIORITY => _topicService.GetNormalPriorityTasksTopic();

    /// <summary>
    /// 低优先级任务队列主题
    /// </summary>
    public string TASKS_LOW_PRIORITY => _topicService.GetLowPriorityTasksTopic();

    #endregion

    #region 角色专业化主题

    /// <summary>
    /// Designer节点专用主题
    /// </summary>
    public string DESIGNER_ROOT => _topicService.GetDesignerRootTopic();

    /// <summary>
    /// Validator节点专用主题
    /// </summary>
    public string VALIDATOR_ROOT => _topicService.GetValidatorRootTopic();

    /// <summary>
    /// Executor节点专用主题
    /// </summary>
    public string EXECUTOR_ROOT => _topicService.GetExecutorRootTopic();

    #endregion

    #region UI通信主题

    /// <summary>
    /// UI根主题
    /// </summary>
    public string UI_ROOT => _topicService.GetUiRootTopic();

    /// <summary>
    /// UI实时更新主题
    /// </summary>
    public string UI_UPDATES => _topicService.GetUiUpdatesTopic();

    /// <summary>
    /// 用户通知主题
    /// </summary>
    public string UI_NOTIFICATIONS => _topicService.GetUiNotificationsTopic();

    /// <summary>
    /// UI系统事件主题
    /// </summary>
    public string UI_EVENTS => _topicService.GetUiEventsTopic();

    #endregion

    #region 监控主题

    /// <summary>
    /// 监控根主题
    /// </summary>
    public string MONITORING_ROOT => _topicService.GetMonitoringRootTopic();

    /// <summary>
    /// 性能指标主题
    /// </summary>
    public string MONITORING_METRICS => _topicService.GetMonitoringMetricsTopic();

    /// <summary>
    /// 健康检查主题
    /// </summary>
    public string MONITORING_HEALTH => _topicService.GetMonitoringHealthTopic();

    /// <summary>
    /// 告警主题
    /// </summary>
    public string MONITORING_ALERTS => _topicService.GetMonitoringAlertsTopic();

    /// <summary>
    /// 日志主题
    /// </summary>
    public string MONITORING_LOGS => _topicService.GetMonitoringLogsTopic();

    #endregion
}
