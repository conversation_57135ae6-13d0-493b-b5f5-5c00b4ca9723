<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="9.0.8" />
    <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="9.0.8" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="9.0.8" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="9.0.8" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.8">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Pomelo.EntityFrameworkCore.MySql" Version="9.0.0" />
    <PackageReference Include="NATS.Net" Version="2.6.8" />
    <PackageReference Include="System.Linq.Async" Version="6.0.1" />
    <!-- 插件系统完整功能支持 -->
    <PackageReference Include="DotNetCore.Natasha.CSharp.Compiler" Version="*******" />
    <PackageReference Include="McMaster.NETCore.Plugins" Version="1.4.0" />
  </ItemGroup>

  <ItemGroup>
    <!-- Infrastructure层引用Engine层，Engine层引用Core层 -->
    <!-- 这样形成正确的依赖链：Infrastructure → Engine → Core -->
    <ProjectReference Include="..\FlowCustomV1.Engine\FlowCustomV1.Engine.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Migrations\" />
  </ItemGroup>

</Project>