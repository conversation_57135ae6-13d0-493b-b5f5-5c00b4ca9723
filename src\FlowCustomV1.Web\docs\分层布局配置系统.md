# 分层布局配置系统

## 🎯 系统概述

FlowCustomV1采用**分层配置架构**，将布局参数分为两个层次：
1. **全局基础配置** - 所有页面共享的基础框架参数
2. **页面特定配置** - 每个页面根据内容特点的个性化参数

## 🏗️ 架构设计

### 层次1：全局基础配置 (`layout-config.css`)

```css
:root {
  /* 🟢 绿色框架 - Page Container */
  --layout-content-padding: 8px;
  --layout-container-padding: 8px;
  
  /* 🟡 黄色框架 - Page Header */
  --layout-page-header-margin: 12px;
  
  /* 🔵 蓝色框架 - Page Content */
  --layout-element-spacing: 4px;
  
  /* 🟠 橙色框架 - 可配置组件容器 */
  --layout-toolbar-height: 60px;
  --layout-toolbar-margin: 16px;
  --layout-stats-container-height: 120px;
  --layout-stats-container-margin: 16px;
  --layout-stats-container-padding: 16px;
  
  /* 🟣 紫色框架 - Table Container */
  --layout-table-container-padding: 0px;
  --layout-table-pagination-height: 60px;
}
```

### 层次2：页面特定配置 (`PAGE_LAYOUT_CONFIG`)

```tsx
// ========== 页面布局配置 ==========
const PAGE_LAYOUT_CONFIG = {
  // 🎯 页面类型标识
  pageType: 'complex-with-stats', // 或 'simple-list'
  
  // 🟠 统计卡片容器配置
  hasStatsContainer: true,
  statsContainerHeight: 'var(--layout-stats-container-height)',
  statsContainerMargin: 'var(--layout-stats-container-margin)',
  statsContainerPadding: 'var(--layout-stats-container-padding)',
  statsCardCount: 4,
  
  // 🟠 工具栏配置
  hasToolbar: true,
  toolbarHeight: 'var(--layout-toolbar-height)',
  toolbarMargin: 'var(--layout-toolbar-margin)',
  toolbarComplexity: 'high', // 'low' | 'medium' | 'high'
  
  // 🟣 表格配置
  tableScrollY: 'calc(100vh - 520px)',
  tablePaginationHeight: 'var(--layout-table-pagination-height)',
  tableHasSelection: true,
  
  // 📐 页面特定间距调整
  customSpacing: {
    extraOffset: 70,
    compactMode: false
  }
};
```

## 📋 页面类型分类

### 类型A：简单列表页面 (`simple-list`)
**特点**：页面头部 + 工具栏 + 表格
**配置示例**：
```tsx
const PAGE_LAYOUT_CONFIG = {
  pageType: 'simple-list',
  hasStatsContainer: false,
  hasToolbar: true,
  toolbarComplexity: 'high',
  tableScrollY: 'calc(100vh - 355px)',
  tableHasSelection: true,
  customSpacing: { extraOffset: 0, compactMode: false }
};
```
**适用页面**：工作流列表

### 类型B：复杂统计页面 (`complex-with-stats`)
**特点**：页面头部 + 统计卡片容器 + 工具栏 + 表格
**配置示例**：
```tsx
const PAGE_LAYOUT_CONFIG = {
  pageType: 'complex-with-stats',
  hasStatsContainer: true,
  statsCardCount: 4,
  hasToolbar: true,
  toolbarComplexity: 'medium',
  tableScrollY: 'calc(100vh - 520px)',
  tableHasSelection: false,
  customSpacing: { extraOffset: 50, compactMode: false }
};
```
**适用页面**：执行监控、执行历史、集群节点

## 🔧 配置参数详解

### 🎯 页面类型参数
- `pageType`: 页面类型标识，用于快速识别页面布局模式
- `customSpacing.extraOffset`: 额外偏移量，用于微调表格高度
- `customSpacing.compactMode`: 紧凑模式，减少间距

### 🟠 统计卡片容器参数
- `hasStatsContainer`: 是否显示统计卡片容器
- `statsContainerHeight`: 容器高度（引用全局变量或自定义）
- `statsContainerMargin`: 容器下边距
- `statsContainerPadding`: 容器内边距
- `statsCardCount`: 统计卡片数量

### 🟠 工具栏参数
- `hasToolbar`: 是否显示工具栏
- `toolbarHeight`: 工具栏高度
- `toolbarMargin`: 工具栏下边距
- `toolbarComplexity`: 工具栏复杂度
  - `low`: 简单工具栏（1-2个控件）
  - `medium`: 中等工具栏（3-5个控件）
  - `high`: 复杂工具栏（6+个控件）

### 🟣 表格参数
- `tableScrollY`: 表格垂直滚动高度
- `tablePaginationHeight`: 分页栏高度
- `tableHasSelection`: 是否支持行选择

## 🎨 使用方式

### 1. 引用全局变量
```tsx
statsContainerHeight: 'var(--layout-stats-container-height)'
```

### 2. 自定义数值
```tsx
tableScrollY: 'calc(100vh - 520px)'
```

### 3. 条件渲染
```tsx
{PAGE_LAYOUT_CONFIG.hasStatsContainer && (
  <div className="stats-cards-container">
    {/* 统计卡片内容 */}
  </div>
)}
```

## 🔄 配置继承和覆盖

### 继承规则
1. 页面配置优先级 > 全局配置
2. 自定义数值 > CSS变量引用
3. 页面特定参数 > 通用参数

### 覆盖示例
```tsx
// 全局配置
--layout-toolbar-height: 60px;

// 页面配置（覆盖全局）
toolbarHeight: '80px', // 这个页面需要更高的工具栏
```

## 📐 高度计算公式

### 简单页面
```
表格高度 = 100vh - (页面头部 + 工具栏 + 分页栏 + 边距)
         = 100vh - 355px
```

### 复杂页面
```
表格高度 = 100vh - (页面头部 + 统计容器 + 工具栏 + 分页栏 + 边距)
         = 100vh - 520px
```

## 🎯 配置优势

### 1. **分层清晰**
- 全局配置管理通用参数
- 页面配置处理特殊需求

### 2. **灵活可控**
- 可以引用全局变量保持一致性
- 可以自定义数值满足特殊需求

### 3. **易于维护**
- 配置集中在文件开头
- 参数含义清晰明确

### 4. **扩展性强**
- 新增页面类型容易
- 新增配置参数简单

这个分层配置系统既保持了全局一致性，又提供了页面级别的灵活性，是理想的布局管理方案。
