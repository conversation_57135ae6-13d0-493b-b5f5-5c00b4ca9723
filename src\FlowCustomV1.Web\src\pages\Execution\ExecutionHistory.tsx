import React, { useEffect, useState } from 'react';
import { Tag, Button, Space, Input, Select, DatePicker, Statistic, Row, Col } from 'antd';
import {
  EyeOutlined,
  ReloadOutlined,
  SearchOutlined,
  FilterOutlined,
  DownloadOutlined,
  RetweetOutlined,
  HistoryOutlined
} from '@ant-design/icons';
import { ProTable, ProColumns, ProCard } from '@ant-design/pro-components';
import { executionApi } from '@/services/execution';
import type { WorkflowExecution } from '@/types/api';
import { ExecutionStatus } from '@/types/api';
import PageLayout from '@/components/Layout/PageLayout';

// ========== 页面布局配置 ==========
const PAGE_LAYOUT_CONFIG = {
  // 🎯 页面类型标识
  pageType: 'complex-with-stats',

  // 🟠 统计卡片容器配置
  hasStatsContainer: true,
  statsContainerHeight: 'var(--layout-stats-container-height)',
  statsContainerMargin: 'var(--layout-stats-container-margin)',
  statsContainerPadding: 'var(--layout-stats-container-padding)',
  statsCardCount: 4, // 4个统计卡片

  // 🟠 工具栏配置
  hasToolbar: true,
  toolbarHeight: 'var(--layout-toolbar-height)',
  toolbarMargin: 'var(--layout-toolbar-margin)',
  toolbarComplexity: 'medium', // 中等复杂度工具栏

  // 🟣 表格配置
  tableScrollY: 'calc(100vh - 520px)', // 复杂页面，偏移量较大
  tablePaginationHeight: 'var(--layout-table-pagination-height)',
  tableHasSelection: false, // 不支持行选择

  // 📐 页面特定间距调整
  customSpacing: {
    extraOffset: 50, // 历史页面需要额外空间
    compactMode: false
  }
};

const { Search } = Input;
const { Option } = Select;
const { RangePicker } = DatePicker;

const ExecutionHistory: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [executions, setExecutions] = useState<WorkflowExecution[]>([]);
  const [stats, setStats] = useState({
    total: 0,
    success: 0,
    failed: 0,
    averageDuration: 0,
  });

  // 加载历史数据
  const loadHistory = async () => {
    try {
      setLoading(true);
      const [executionData, statsData] = await Promise.all([
        executionApi.getAllExecutions({ pageIndex: 0, pageSize: 100 }),
        executionApi.getExecutionStats(),
      ]);

      // 只显示已完成的执行记录
      const completedExecutions = Array.isArray(executionData)
        ? executionData.filter(e => e.status !== ExecutionStatus.Running && e.status !== ExecutionStatus.Pending)
        : (executionData.data || []).filter((e: WorkflowExecution) => e.status !== ExecutionStatus.Running && e.status !== ExecutionStatus.Pending);

      setExecutions(completedExecutions);
      setStats({
        total: statsData.total,
        success: statsData.success,
        failed: statsData.failed,
        averageDuration: statsData.averageDuration || 0,
      });
    } catch (error) {
      console.error('加载执行历史失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadHistory();
  }, []);

  // 获取状态标签
  const getStatusTag = (status: ExecutionStatus) => {
    const statusMap: Record<ExecutionStatus, { color: string; text: string }> = {
      [ExecutionStatus.Completed]: { color: 'success', text: '已完成' },
      [ExecutionStatus.Failed]: { color: 'error', text: '失败' },
      [ExecutionStatus.Cancelled]: { color: 'warning', text: '已取消' },
      [ExecutionStatus.Pending]: { color: 'default', text: '等待中' },
      [ExecutionStatus.Running]: { color: 'processing', text: '运行中' },
      [ExecutionStatus.Paused]: { color: 'warning', text: '已暂停' },
    };
    const config = statusMap[status] || { color: 'default', text: status as string };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 重试执行
  const handleRetry = async (executionId: string) => {
    try {
      await executionApi.retryExecution(executionId);
      loadHistory();
    } catch (error) {
      console.error('重试执行失败:', error);
    }
  };

  // 导出执行日志
  const handleExportLogs = async (executionId: string) => {
    try {
      const logs = await executionApi.getExecutionLogs(executionId);
      const blob = new Blob([logs.join('\n')], { type: 'text/plain' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `execution-${executionId}-logs.txt`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('导出日志失败:', error);
    }
  };

  // 表格列定义
  const columns: ProColumns<WorkflowExecution>[] = [
    {
      title: '执行ID',
      dataIndex: 'executionId',
      key: 'executionId',
      width: 120,
      ellipsis: true,
      render: (_, record) => (
        <code className="text-xs bg-gray-100 px-1 rounded">
          {record.executionId.substring(0, 8)}...
        </code>
      ),
    },
    {
      title: '工作流名称',
      dataIndex: 'workflowName',
      key: 'workflowName',
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (_, record) => getStatusTag(record.status),
    },
    {
      title: '开始时间',
      dataIndex: 'startedAt',
      key: 'startedAt',
      width: 160,
      render: (_, record) => new Date(record.startedAt).toLocaleString(),
    },
    {
      title: '完成时间',
      dataIndex: 'completedAt',
      key: 'completedAt',
      width: 160,
      render: (_, record) => record.completedAt ? new Date(record.completedAt).toLocaleString() : '-',
    },
    {
      title: '持续时间',
      dataIndex: 'duration',
      key: 'duration',
      width: 100,
      render: (_, record) => {
        if (record.duration) {
          const seconds = Math.round(record.duration / 1000);
          if (seconds < 60) return `${seconds}s`;
          const minutes = Math.floor(seconds / 60);
          const remainingSeconds = seconds % 60;
          return `${minutes}m ${remainingSeconds}s`;
        }
        return '-';
      },
    },
    {
      title: '错误信息',
      dataIndex: 'errorMessage',
      key: 'errorMessage',
      width: 200,
      ellipsis: true,
      render: (error) => error ? (
        <span className="text-red-600 text-sm">{error}</span>
      ) : '-',
    },
    {
      title: '操作',
      key: 'action',
      width: 180,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Button 
            type="text" 
            size="small" 
            icon={<EyeOutlined />}
            onClick={() => window.open(`/execution/detail/${record.executionId}`, '_blank')}
          >
            详情
          </Button>
          <Button 
            type="text" 
            size="small" 
            icon={<DownloadOutlined />}
            onClick={() => handleExportLogs(record.executionId)}
          >
            日志
          </Button>
          {record.status === ExecutionStatus.Failed && (
            <Button 
              type="text" 
              size="small" 
              icon={<RetweetOutlined />}
              onClick={() => handleRetry(record.executionId)}
            >
              重试
            </Button>
          )}
        </Space>
      ),
    },
  ];

  return (
    <PageLayout
      title="执行历史"
      description="查看工作流的历史执行记录"
      icon={<HistoryOutlined />}
      actions={
        <Button
          type="primary"
          icon={<ReloadOutlined />}
          loading={loading}
          onClick={loadHistory}
        >
          刷新
        </Button>
      }
    >
      {/* 统计卡片容器 */}
      {PAGE_LAYOUT_CONFIG.hasStatsContainer && (
        <div
          className="stats-cards-container"
          style={{
            height: PAGE_LAYOUT_CONFIG.statsContainerHeight,
            marginBottom: PAGE_LAYOUT_CONFIG.statsContainerMargin,
            padding: PAGE_LAYOUT_CONFIG.statsContainerPadding,
            border: '2px solid #ff9800', // 橙色调试边框
            borderRadius: '6px',
            background: '#fff'
          }}
        >
        <Row gutter={[16, 16]} className="layout-card-grid">
          <Col xs={24} sm={6}>
            <ProCard className="layout-card-statistic">
              <Statistic
                title="总执行数"
                value={stats.total}
                valueStyle={{ color: '#1890ff' }}
              />
            </ProCard>
          </Col>
          <Col xs={24} sm={6}>
            <ProCard className="layout-card-statistic">
              <Statistic
                title="成功执行"
                value={stats.success}
                valueStyle={{ color: '#52c41a' }}
              />
            </ProCard>
          </Col>
          <Col xs={24} sm={6}>
            <ProCard className="layout-card-statistic">
              <Statistic
                title="失败执行"
                value={stats.failed}
                valueStyle={{ color: '#ff4d4f' }}
              />
            </ProCard>
          </Col>
          <Col xs={24} sm={6}>
            <ProCard className="layout-card-statistic">
              <Statistic
                title="平均耗时"
                value={stats.averageDuration ? Math.round(stats.averageDuration / 1000) : 0}
                suffix="秒"
                valueStyle={{ color: '#722ed1' }}
              />
            </ProCard>
          </Col>
        </Row>
        </div>
      )}

      {/* 筛选工具栏 */}
      <div className="toolbar">
        <div className="toolbar-left">
          <Space>
            <Search
              placeholder="搜索工作流名称或执行ID"
              allowClear
              style={{ width: 300 }}
              prefix={<SearchOutlined />}
            />
            <Select
              placeholder="状态筛选"
              allowClear
              style={{ width: 120 }}
              suffixIcon={<FilterOutlined />}
            >
              <Option value="Completed">已完成</Option>
              <Option value="Failed">失败</Option>
              <Option value="Cancelled">已取消</Option>
            </Select>
            <RangePicker placeholder={['开始时间', '结束时间']} />
          </Space>
        </div>
      </div>

      {/* 历史记录表格 */}
      <div className="workflow-table-container">
        <ProTable<WorkflowExecution>
          columns={columns}
          dataSource={executions}
          rowKey="executionId"
          loading={loading}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`,
          }}
          scroll={{
            x: 1200,
            y: PAGE_LAYOUT_CONFIG.tableScrollY
          }}
          search={false}
          toolBarRender={false}
          options={false}
          rowClassName={(record) => {
            if (record.status === ExecutionStatus.Failed) return 'bg-red-50';
            if (record.status === ExecutionStatus.Completed) return 'bg-green-50';
            return '';
          }}
        />
      </div>
    </PageLayout>
  );
};

export default ExecutionHistory;
