using FlowCustomV1.Core.Models.Cluster;
using FlowCustomV1.Core.Models.Messaging;
using ClusterNodeInfo = FlowCustomV1.Core.Models.Cluster.NodeInfo;

namespace FlowCustomV1.Core.Interfaces.Messaging;

/// <summary>
/// 基于角色的消息路由服务接口
/// 支持角色感知的智能消息路由和负载均衡
/// </summary>
public interface IRoleBasedMessageRouter
{
    #region 角色路由规则管理

    /// <summary>
    /// 注册基于角色的路由规则
    /// </summary>
    /// <param name="pattern">主题模式</param>
    /// <param name="targetRoles">目标角色列表</param>
    /// <param name="routingStrategy">路由策略</param>
    /// <param name="priority">路由优先级</param>
    void RegisterRoleRoute(string pattern, NodeRole[] targetRoles, RoutingStrategy routingStrategy = RoutingStrategy.RoundRobin, int priority = 0);

    /// <summary>
    /// 移除角色路由规则
    /// </summary>
    /// <param name="pattern">主题模式</param>
    void UnregisterRoleRoute(string pattern);

    /// <summary>
    /// 获取所有角色路由规则
    /// </summary>
    /// <returns>角色路由规则列表</returns>
    IReadOnlyList<RoleRouteRule> GetRoleRoutes();

    /// <summary>
    /// 启用/禁用角色路由规则
    /// </summary>
    /// <param name="pattern">主题模式</param>
    /// <param name="enabled">是否启用</param>
    void SetRoleRouteEnabled(string pattern, bool enabled);

    #endregion

    #region 角色感知消息路由

    /// <summary>
    /// 路由消息到最佳角色节点
    /// </summary>
    /// <param name="message">消息对象</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>路由任务</returns>
    Task RouteMessageAsync(IMessage message, CancellationToken cancellationToken = default);

    /// <summary>
    /// 路由消息到指定角色的节点
    /// </summary>
    /// <param name="message">消息对象</param>
    /// <param name="targetRole">目标角色</param>
    /// <param name="strategy">路由策略</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>路由任务</returns>
    Task RouteToRoleAsync(IMessage message, NodeRole targetRole, RoutingStrategy strategy = RoutingStrategy.RoundRobin, CancellationToken cancellationToken = default);

    /// <summary>
    /// 广播消息到指定角色的所有节点
    /// </summary>
    /// <param name="message">消息对象</param>
    /// <param name="targetRole">目标角色</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>广播任务</returns>
    Task BroadcastToRoleAsync(IMessage message, NodeRole targetRole, CancellationToken cancellationToken = default);

    /// <summary>
    /// 多播消息到多个角色
    /// </summary>
    /// <param name="message">消息对象</param>
    /// <param name="targetRoles">目标角色列表</param>
    /// <param name="strategy">路由策略</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>多播任务</returns>
    Task MulticastToRolesAsync(IMessage message, NodeRole[] targetRoles, RoutingStrategy strategy = RoutingStrategy.RoundRobin, CancellationToken cancellationToken = default);

    #endregion

    #region 角色节点管理

    /// <summary>
    /// 注册角色节点
    /// </summary>
    /// <param name="nodeInfo">节点信息</param>
    void RegisterRoleNode(ClusterNodeInfo nodeInfo);

    /// <summary>
    /// 注销角色节点
    /// </summary>
    /// <param name="nodeId">节点ID</param>
    void UnregisterRoleNode(string nodeId);

    /// <summary>
    /// 更新节点角色配置
    /// </summary>
    /// <param name="nodeId">节点ID</param>
    /// <param name="roleConfiguration">角色配置</param>
    void UpdateNodeRoles(string nodeId, NodeRoleConfiguration roleConfiguration);

    /// <summary>
    /// 获取指定角色的可用节点
    /// </summary>
    /// <param name="role">目标角色</param>
    /// <returns>可用节点列表</returns>
    IReadOnlyList<ClusterNodeInfo> GetAvailableRoleNodes(NodeRole role);

    /// <summary>
    /// 获取所有角色节点
    /// </summary>
    /// <returns>角色节点字典</returns>
    IReadOnlyDictionary<string, ClusterNodeInfo> GetAllRoleNodes();

    #endregion

    #region 路由统计和监控

    /// <summary>
    /// 获取角色路由统计信息
    /// </summary>
    /// <param name="role">目标角色</param>
    /// <returns>路由统计</returns>
    RoleRoutingStatistics GetRoleRoutingStatistics(NodeRole role);

    /// <summary>
    /// 获取所有角色的路由统计
    /// </summary>
    /// <returns>路由统计字典</returns>
    IReadOnlyDictionary<NodeRole, RoleRoutingStatistics> GetAllRoleRoutingStatistics();

    /// <summary>
    /// 重置路由统计信息
    /// </summary>
    /// <param name="role">目标角色（可选，为空则重置所有）</param>
    void ResetRoutingStatistics(NodeRole? role = null);

    #endregion

    #region 事件

    /// <summary>
    /// 角色节点状态变更事件
    /// </summary>
    event EventHandler<RoleNodeStatusChangedEventArgs>? RoleNodeStatusChanged;

    /// <summary>
    /// 角色路由失败事件
    /// </summary>
    event EventHandler<RoleRoutingFailedEventArgs>? RoleRoutingFailed;

    /// <summary>
    /// 角色负载变更事件
    /// </summary>
    event EventHandler<RoleLoadChangedEventArgs>? RoleLoadChanged;

    #endregion
}

/// <summary>
/// 角色路由规则
/// </summary>
public class RoleRouteRule
{
    /// <summary>
    /// 主题模式
    /// </summary>
    public string Pattern { get; set; } = string.Empty;

    /// <summary>
    /// 目标角色列表
    /// </summary>
    public NodeRole[] TargetRoles { get; set; } = Array.Empty<NodeRole>();

    /// <summary>
    /// 路由策略
    /// </summary>
    public RoutingStrategy Strategy { get; set; } = RoutingStrategy.RoundRobin;

    /// <summary>
    /// 路由优先级（数值越大优先级越高）
    /// </summary>
    public int Priority { get; set; } = 0;

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 规则描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 规则元数据
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// 角色路由统计信息
/// </summary>
public class RoleRoutingStatistics
{
    /// <summary>
    /// 角色
    /// </summary>
    public NodeRole Role { get; set; }

    /// <summary>
    /// 总路由次数
    /// </summary>
    public long TotalRoutes { get; set; }

    /// <summary>
    /// 成功路由次数
    /// </summary>
    public long SuccessfulRoutes { get; set; }

    /// <summary>
    /// 失败路由次数
    /// </summary>
    public long FailedRoutes { get; set; }

    /// <summary>
    /// 平均路由时间（毫秒）
    /// </summary>
    public double AverageRoutingTimeMs { get; set; }

    /// <summary>
    /// 最后路由时间
    /// </summary>
    public DateTime? LastRoutingTime { get; set; }

    /// <summary>
    /// 活跃节点数量
    /// </summary>
    public int ActiveNodeCount { get; set; }

    /// <summary>
    /// 总负载
    /// </summary>
    public double TotalLoad { get; set; }

    /// <summary>
    /// 平均负载
    /// </summary>
    public double AverageLoad { get; set; }
}

/// <summary>
/// 角色节点状态变更事件参数
/// </summary>
public class RoleNodeStatusChangedEventArgs : EventArgs
{
    /// <summary>
    /// 节点ID
    /// </summary>
    public string NodeId { get; set; } = string.Empty;

    /// <summary>
    /// 节点角色
    /// </summary>
    public NodeRole Roles { get; set; }

    /// <summary>
    /// 旧状态
    /// </summary>
    public NodeStatus OldStatus { get; set; }

    /// <summary>
    /// 新状态
    /// </summary>
    public NodeStatus NewStatus { get; set; }

    /// <summary>
    /// 变更时间
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 角色路由失败事件参数
/// </summary>
public class RoleRoutingFailedEventArgs : EventArgs
{
    /// <summary>
    /// 消息ID
    /// </summary>
    public string MessageId { get; set; } = string.Empty;

    /// <summary>
    /// 目标角色
    /// </summary>
    public NodeRole TargetRole { get; set; }

    /// <summary>
    /// 失败原因
    /// </summary>
    public string Reason { get; set; } = string.Empty;

    /// <summary>
    /// 失败时间
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 异常信息
    /// </summary>
    public Exception? Exception { get; set; }
}

/// <summary>
/// 角色负载变更事件参数
/// </summary>
public class RoleLoadChangedEventArgs : EventArgs
{
    /// <summary>
    /// 角色
    /// </summary>
    public NodeRole Role { get; set; }

    /// <summary>
    /// 旧负载
    /// </summary>
    public double OldLoad { get; set; }

    /// <summary>
    /// 新负载
    /// </summary>
    public double NewLoad { get; set; }

    /// <summary>
    /// 变更时间
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}
