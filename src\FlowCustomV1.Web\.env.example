# FlowCustomV1 Web Frontend 环境变量配置示例
# 复制此文件为 .env.local 并根据实际情况修改配置

# 应用基础配置
VITE_APP_TITLE=FlowCustomV1 工作流自动化系统
VITE_APP_VERSION=v0.0.1.11
VITE_APP_DESCRIPTION=FlowCustomV1 分布式工作流自动化管理平台

# API 配置
VITE_API_BASE_URL=http://localhost:5000/api
VITE_API_TIMEOUT=30000

# 开发配置
VITE_DEBUG=false
VITE_MOCK_API=false

# 功能开关
VITE_ENABLE_DEVTOOLS=true
VITE_ENABLE_ANALYTICS=false
VITE_ENABLE_ERROR_REPORTING=false

# 主题配置
VITE_DEFAULT_THEME=light
VITE_DEFAULT_LANGUAGE=zh-CN

# 外部服务配置
VITE_GITHUB_URL=https://github.com/your-repo/FlowCustomV1
VITE_DOCS_URL=https://docs.flowcustom.com

# 构建配置
VITE_BUILD_ANALYZE=false
VITE_BUILD_SOURCEMAP=false
