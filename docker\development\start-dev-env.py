#!/usr/bin/env python3
"""
FlowCustomV1 开发环境启动脚本
启动单节点NATS和MySQL用于本地开发
"""

import subprocess
import sys
import time
from pathlib import Path

def run_command(command, cwd=None, timeout=60):
    """执行命令并返回结果"""
    try:
        result = subprocess.run(
            command, 
            cwd=cwd, 
            capture_output=True, 
            text=True, 
            shell=True,
            encoding='utf-8',
            errors='ignore',
            timeout=timeout
        )
        return result.returncode == 0, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return False, "", f"命令超时 ({timeout}秒)"
    except Exception as e:
        return False, "", str(e)

def start_dev_environment():
    """启动开发环境"""
    print("🚀 启动FlowCustomV1开发环境...")
    
    script_dir = Path(__file__).parent
    
    # 启动服务
    success, stdout, stderr = run_command(
        "docker-compose up -d",
        cwd=script_dir,
        timeout=120
    )
    
    if not success:
        print(f"❌ 开发环境启动失败:")
        print(f"stderr: {stderr}")
        return False
    
    print("✅ 开发环境启动成功")
    
    # 等待服务启动
    print("⏳ 等待服务初始化...")
    time.sleep(10)
    
    return True

def check_services():
    """检查服务状态"""
    print("\n🔍 检查服务状态...")
    
    script_dir = Path(__file__).parent
    
    # 检查容器状态
    success, stdout, stderr = run_command(
        "docker-compose ps",
        cwd=script_dir
    )
    
    if success:
        print("📊 服务状态:")
        print(stdout)
    
    # 检查NATS
    success, stdout, stderr = run_command(
        "docker exec nats-dev-server nats server info",
        timeout=10
    )
    
    if success:
        print("✅ NATS服务器运行正常")
    else:
        print("⚠️ NATS服务器状态检查失败")
    
    # 检查MySQL
    success, stdout, stderr = run_command(
        "docker exec mysql-dev mysqladmin ping -h localhost",
        timeout=10
    )
    
    if success:
        print("✅ MySQL数据库运行正常")
    else:
        print("⚠️ MySQL数据库状态检查失败")

def stop_dev_environment():
    """停止开发环境"""
    print("\n🛑 停止开发环境...")
    
    script_dir = Path(__file__).parent
    
    success, stdout, stderr = run_command(
        "docker-compose down",
        cwd=script_dir,
        timeout=30
    )
    
    if success:
        print("✅ 开发环境已停止")
    else:
        print(f"⚠️ 停止可能不完整: {stderr}")

def cleanup_dev_environment():
    """清理开发环境"""
    print("\n🧹 清理开发环境...")
    
    script_dir = Path(__file__).parent
    
    success, stdout, stderr = run_command(
        "docker-compose down -v",
        cwd=script_dir,
        timeout=30
    )
    
    if success:
        print("✅ 开发环境清理完成")
    else:
        print(f"⚠️ 清理可能不完整: {stderr}")

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python start-dev-env.py [start|stop|status|cleanup]")
        sys.exit(1)
    
    command = sys.argv[1].lower()
    
    if command == "start":
        if start_dev_environment():
            check_services()
            print("\n🎉 开发环境启动完成!")
            print("\n🌐 服务访问信息:")
            print("NATS服务器:    nats://localhost:4222")
            print("NATS监控:      http://localhost:8222")
            print("MySQL数据库:   localhost:3306")
            print("\n💡 现在可以启动API服务:")
            print("dotnet run --project src/FlowCustomV1.Api -- --environment Development")
        else:
            sys.exit(1)
    
    elif command == "stop":
        stop_dev_environment()
    
    elif command == "status":
        check_services()
    
    elif command == "cleanup":
        cleanup_dev_environment()
    
    else:
        print("❌ 未知命令，支持的命令: start, stop, status, cleanup")
        sys.exit(1)

if __name__ == "__main__":
    main()
