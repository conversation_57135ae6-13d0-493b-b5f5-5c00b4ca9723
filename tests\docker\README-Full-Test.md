# FlowCustomV1 v0.0.1.7 完整Docker环境测试指南

## 📋 概述

本测试环境为FlowCustomV1 v0.0.1.7版本提供完整的Docker化测试方案，包含所有已实现功能的全面测试。

## 🏗️ 测试环境架构

### 基础设施层
- **NATS集群**: 3节点高可用集群 (nats-1, nats-2, nats-3)
- **MySQL数据库**: 持久化存储
- **NATS Surveyor**: Prometheus指标监控

### 应用层节点
- **Master节点**: 2个 (北京、上海)
- **Worker节点**: 3个 (北京2个、上海1个)
- **Designer节点**: 2个 (北京、上海)
- **Validator节点**: 2个 (北京、上海)
- **Executor节点**: 2个 (北京、上海)

### 可选服务
- **Prometheus**: 指标收集
- **Grafana**: 监控可视化
- **Redis**: 缓存服务
- **Elasticsearch + Kibana**: 日志聚合

## 🚀 快速开始

### 1. 基础测试（推荐）

```powershell
# 运行完整测试套件
.\run-full-docker-tests.ps1 -TestSuite all

# 运行特定测试套件
.\run-full-docker-tests.ps1 -TestSuite infrastructure
.\run-full-docker-tests.ps1 -TestSuite services
.\run-full-docker-tests.ps1 -TestSuite workflows
.\run-full-docker-tests.ps1 -TestSuite performance
```

### 2. 带监控的测试

```powershell
# 启用监控服务
.\run-full-docker-tests.ps1 -TestSuite all -WithMonitoring

# 启用所有可选服务
.\run-full-docker-tests.ps1 -TestSuite all -WithMonitoring -WithLogging -WithCache
```

### 3. 清理重启测试

```powershell
# 清理环境后重新测试
.\run-full-docker-tests.ps1 -TestSuite all -CleanStart
```

## 📊 测试套件说明

### infrastructure
- NATS集群连接测试
- MySQL数据库测试
- 基础设施健康检查

### services
- 节点发现和注册测试
- 专业化节点服务测试
- 跨节点通信测试

### workflows
- 工作流端到端测试
- 简单和复杂工作流测试
- 并发工作流测试

### performance
- 性能基准测试
- 压力测试
- 负载均衡测试

### monitoring
- 监控指标收集测试
- 告警功能测试

## 🔍 手动测试

### 1. 启动测试环境

```bash
cd tests/docker
docker-compose -f docker-compose.full-test.yml up -d
```

### 2. 验证服务状态

```bash
# 检查所有服务状态
docker-compose -f docker-compose.full-test.yml ps

# 检查服务健康状态
curl http://localhost:25001/health  # Master北京
curl http://localhost:25002/health  # Master上海
curl http://localhost:25011/health  # Worker北京1
# ... 其他节点
```

### 3. 访问监控界面

- **NATS监控**: http://localhost:28222/varz (nats-1)
- **NATS Surveyor**: http://localhost:27777/metrics
- **Prometheus**: http://localhost:29090 (需启用monitoring profile)
- **Grafana**: http://localhost:23000 (需启用monitoring profile)

### 4. 运行单独测试

```bash
# 进入测试协调器容器
docker exec -it flowcustom-full-test-coordinator bash

# 运行特定测试
python3 /app/test-scripts/test_nats_cluster.py
python3 /app/test-scripts/test_specialized_nodes.py
```

## 📁 端口映射

### 基础设施
- **NATS-1**: 24222 (client), 28222 (monitoring), 26222 (cluster)
- **NATS-2**: 24223 (client), 28223 (monitoring), 26223 (cluster)
- **NATS-3**: 24224 (client), 28224 (monitoring), 26224 (cluster)
- **NATS Surveyor**: 27777
- **MySQL**: 23306

### 应用节点
- **Master北京**: 25001
- **Master上海**: 25002
- **Worker北京1**: 25011
- **Worker北京2**: 25012
- **Worker上海1**: 25013
- **Designer北京**: 25021
- **Designer上海**: 25022
- **Validator北京**: 25031
- **Validator上海**: 25032
- **Executor北京**: 25041
- **Executor上海**: 25042

### 可选服务
- **Prometheus**: 29090
- **Grafana**: 23000
- **Redis**: 26379
- **Elasticsearch**: 29200
- **Kibana**: 25601

## 📋 测试结果

测试结果将保存在 `tests/test-results/` 目录下：

- **JSON结果**: 各个测试的详细结果
- **HTML报告**: 可视化测试报告
- **日志文件**: 详细的执行日志

## 🔧 故障排除

### 常见问题

1. **服务启动失败**
   ```bash
   # 检查日志
   docker-compose -f docker-compose.full-test.yml logs [service-name]
   
   # 重启特定服务
   docker-compose -f docker-compose.full-test.yml restart [service-name]
   ```

2. **端口冲突**
   - 检查端口是否被占用
   - 修改docker-compose.full-test.yml中的端口映射

3. **内存不足**
   - 确保Docker有足够内存分配（推荐8GB+）
   - 减少并发节点数量

4. **网络问题**
   ```bash
   # 检查网络
   docker network ls
   docker network inspect flowcustom-full-test
   ```

### 清理环境

```bash
# 停止所有服务
docker-compose -f docker-compose.full-test.yml down -v

# 清理所有资源
docker system prune -a -f
```

## 📈 性能调优

### 资源要求
- **最小配置**: 4GB RAM, 2 CPU cores
- **推荐配置**: 8GB RAM, 4 CPU cores
- **最佳配置**: 16GB RAM, 8 CPU cores

### 优化建议
1. 增加Docker内存限制
2. 使用SSD存储
3. 调整并发测试数量
4. 优化网络配置

## 🤝 贡献指南

1. 添加新测试脚本到 `test-scripts/` 目录
2. 更新测试协调器配置
3. 添加相应的文档说明
4. 确保测试的幂等性和可重复性

## 📞 支持

如有问题或建议：
1. 查看日志文件
2. 检查服务健康状态
3. 提交Issue到项目仓库

---

**FlowCustomV1 v0.0.1.7 - 完整Docker环境测试，验证分布式工作流系统的所有功能！**
