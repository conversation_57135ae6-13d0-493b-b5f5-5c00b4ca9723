using FlowCustomV1.Core.Interfaces;
using FlowCustomV1.Core.Interfaces.Plugins;
using FlowCustomV1.Core.Models.Plugins;
using McMaster.NETCore.Plugins;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Diagnostics;
using System.Reflection;

namespace FlowCustomV1.Infrastructure.Services.Plugins;

/// <summary>
/// McMaster插件加载器
/// 支持DLL预编译插件的热加载和管理
/// </summary>
public class McMasterPluginLoader : IPluginLoader
{
    private readonly ILogger<McMasterPluginLoader> _logger;
    private readonly ConcurrentDictionary<string, PluginLoader> _pluginLoaders;
    private readonly ConcurrentDictionary<string, INodeExecutor> _loadedPlugins;
    private readonly ConcurrentDictionary<string, PluginInfo> _pluginInfos;
    private bool _isInitialized;

    public McMasterPluginLoader(ILogger<McMasterPluginLoader> logger)
    {
        _logger = logger;
        _pluginLoaders = new ConcurrentDictionary<string, PluginLoader>();
        _loadedPlugins = new ConcurrentDictionary<string, INodeExecutor>();
        _pluginInfos = new ConcurrentDictionary<string, PluginInfo>();
    }

    /// <summary>
    /// 插件类型
    /// </summary>
    public PluginType PluginType => PluginType.DllPrecompiled;

    /// <summary>
    /// 插件加载事件
    /// </summary>
    public event EventHandler<PluginLoadedEventArgs>? PluginLoaded;

    /// <summary>
    /// 插件卸载事件
    /// </summary>
    public event EventHandler<PluginUnloadedEventArgs>? PluginUnloaded;

    /// <summary>
    /// 插件错误事件
    /// </summary>
    public event EventHandler<PluginErrorEventArgs>? PluginError;

    /// <summary>
    /// 初始化插件加载器
    /// </summary>
    public async Task InitializeAsync(CancellationToken cancellationToken = default)
    {
        if (_isInitialized)
            return;

        try
        {
            _logger.LogInformation("正在初始化McMaster插件加载器...");

            await Task.Run(() =>
            {
                // 初始化插件加载环境
                _logger.LogInformation("McMaster插件加载器初始化完成");
            }, cancellationToken);

            _isInitialized = true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "初始化McMaster插件加载器失败");
            throw;
        }
    }

    /// <summary>
    /// 加载插件
    /// </summary>
    public async Task<PluginLoadResult> LoadPluginAsync(string pluginPath, CancellationToken cancellationToken = default)
    {
        if (!_isInitialized)
            await InitializeAsync(cancellationToken);

        var stopwatch = Stopwatch.StartNew();
        var result = new PluginLoadResult
        {
            StartedAt = DateTime.UtcNow
        };

        try
        {
            _logger.LogDebug("开始加载DLL插件: {PluginPath}", pluginPath);

            if (!File.Exists(pluginPath))
            {
                result.IsSuccess = false;
                result.ErrorMessage = $"插件文件不存在: {pluginPath}";
                return result;
            }

            // 获取插件元数据
            var metadata = await GetPluginMetadataAsync(pluginPath, cancellationToken);
            if (metadata == null)
            {
                result.IsSuccess = false;
                result.ErrorMessage = "无法获取插件元数据";
                return result;
            }

            var nodeType = metadata.NodeType;
            if (string.IsNullOrEmpty(nodeType))
            {
                result.IsSuccess = false;
                result.ErrorMessage = "插件元数据中缺少节点类型信息";
                return result;
            }

            // 检查是否已加载
            if (_loadedPlugins.ContainsKey(nodeType))
            {
                result.IsSuccess = false;
                result.ErrorMessage = $"节点类型 {nodeType} 的插件已经加载";
                return result;
            }

            // 创建插件加载器
            var pluginLoader = PluginLoader.CreateFromAssemblyFile(
                pluginPath,
                config =>
                {
                    config.PreferSharedTypes = true;
                    config.IsUnloadable = true;
                    config.LoadInMemory = true;
                });

            // 加载程序集
            var assembly = pluginLoader.LoadDefaultAssembly();
            
            // 查找实现了INodeExecutor接口的类型
            var executorType = assembly.GetTypes()
                .FirstOrDefault(t => typeof(INodeExecutor).IsAssignableFrom(t) && !t.IsInterface && !t.IsAbstract);

            if (executorType == null)
            {
                result.IsSuccess = false;
                result.ErrorMessage = "插件中未找到INodeExecutor的实现";
                pluginLoader.Dispose();
                return result;
            }

            // 创建执行器实例
            var executor = Activator.CreateInstance(executorType) as INodeExecutor;
            if (executor == null)
            {
                result.IsSuccess = false;
                result.ErrorMessage = "无法创建插件执行器实例";
                pluginLoader.Dispose();
                return result;
            }

            // 缓存插件信息
            _pluginLoaders.TryAdd(nodeType, pluginLoader);
            _loadedPlugins.TryAdd(nodeType, executor);

            var pluginInfo = new PluginInfo
            {
                NodeType = nodeType,
                Name = metadata.Name,
                DisplayName = metadata.DisplayName,
                Description = metadata.Description,
                Version = metadata.Version,
                Author = metadata.Author,
                PluginType = PluginType.DllPrecompiled,
                PluginPath = pluginPath,
                IsLoaded = true,
                LoadedAt = DateTime.UtcNow,
                Metadata = metadata
            };

            _pluginInfos.TryAdd(nodeType, pluginInfo);

            result.IsSuccess = true;
            result.NodeType = nodeType;
            result.Executor = executor;
            result.PluginInfo = pluginInfo;

            _logger.LogInformation("DLL插件加载成功: {NodeType} from {PluginPath}, 耗时: {ElapsedMs}ms",
                nodeType, pluginPath, stopwatch.ElapsedMilliseconds);

            // 触发插件加载事件
            PluginLoaded?.Invoke(this, new PluginLoadedEventArgs
            {
                PluginInfo = pluginInfo,
                LoadTimeMs = stopwatch.ElapsedMilliseconds
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载DLL插件时发生异常: {PluginPath}", pluginPath);

            result.IsSuccess = false;
            result.ErrorMessage = ex.Message;
            result.Exception = ex;

            // 触发插件错误事件
            PluginError?.Invoke(this, new PluginErrorEventArgs
            {
                NodeType = result.NodeType,
                ErrorMessage = ex.Message,
                Exception = ex,
                ErrorType = PluginErrorType.LoadError,
                Severity = PluginErrorSeverity.Error
            });
        }
        finally
        {
            stopwatch.Stop();
            result.LoadTimeMs = stopwatch.ElapsedMilliseconds;
            result.CompletedAt = DateTime.UtcNow;
        }

        return result;
    }

    /// <summary>
    /// 卸载插件
    /// </summary>
    public async Task<PluginUnloadResult> UnloadPluginAsync(string nodeType, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var result = new PluginUnloadResult
        {
            NodeType = nodeType,
            StartedAt = DateTime.UtcNow
        };

        try
        {
            _logger.LogDebug("开始卸载DLL插件: {NodeType}", nodeType);

            if (!_loadedPlugins.ContainsKey(nodeType))
            {
                result.IsSuccess = false;
                result.ErrorMessage = $"节点类型 {nodeType} 的插件未加载";
                return result;
            }

            // 移除插件执行器
            _loadedPlugins.TryRemove(nodeType, out _);

            // 移除插件信息
            _pluginInfos.TryRemove(nodeType, out var pluginInfo);

            // 卸载插件加载器
            if (_pluginLoaders.TryRemove(nodeType, out var pluginLoader))
            {
                await Task.Run(() =>
                {
                    pluginLoader.Dispose();
                    result.CleanedResources.Add("PluginLoader");
                }, cancellationToken);
            }

            result.IsSuccess = true;

            _logger.LogInformation("DLL插件卸载成功: {NodeType}, 耗时: {ElapsedMs}ms",
                nodeType, stopwatch.ElapsedMilliseconds);

            // 触发插件卸载事件
            PluginUnloaded?.Invoke(this, new PluginUnloadedEventArgs
            {
                NodeType = nodeType,
                PluginInfo = pluginInfo,
                UnloadTimeMs = stopwatch.ElapsedMilliseconds,
                Reason = "Manual unload"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "卸载DLL插件时发生异常: {NodeType}", nodeType);

            result.IsSuccess = false;
            result.ErrorMessage = ex.Message;
            result.Exception = ex;

            // 触发插件错误事件
            PluginError?.Invoke(this, new PluginErrorEventArgs
            {
                NodeType = nodeType,
                ErrorMessage = ex.Message,
                Exception = ex,
                ErrorType = PluginErrorType.LoadError,
                Severity = PluginErrorSeverity.Error
            });
        }
        finally
        {
            stopwatch.Stop();
            result.UnloadTimeMs = stopwatch.ElapsedMilliseconds;
            result.CompletedAt = DateTime.UtcNow;
        }

        return result;
    }

    /// <summary>
    /// 重新加载插件
    /// </summary>
    public async Task<PluginLoadResult> ReloadPluginAsync(string nodeType, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("开始重新加载DLL插件: {NodeType}", nodeType);

            // 获取原插件路径
            if (!_pluginInfos.TryGetValue(nodeType, out var pluginInfo))
            {
                return new PluginLoadResult
                {
                    IsSuccess = false,
                    NodeType = nodeType,
                    ErrorMessage = $"未找到节点类型 {nodeType} 的插件信息",
                    StartedAt = DateTime.UtcNow,
                    CompletedAt = DateTime.UtcNow
                };
            }

            var pluginPath = pluginInfo.PluginPath;

            // 先卸载
            var unloadResult = await UnloadPluginAsync(nodeType, cancellationToken);
            if (!unloadResult.IsSuccess)
            {
                return new PluginLoadResult
                {
                    IsSuccess = false,
                    NodeType = nodeType,
                    ErrorMessage = $"卸载插件失败: {unloadResult.ErrorMessage}",
                    StartedAt = DateTime.UtcNow,
                    CompletedAt = DateTime.UtcNow
                };
            }

            // 等待一段时间确保资源释放
            await Task.Delay(100, cancellationToken);

            // 重新加载
            return await LoadPluginAsync(pluginPath, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "重新加载DLL插件时发生异常: {NodeType}", nodeType);

            return new PluginLoadResult
            {
                IsSuccess = false,
                NodeType = nodeType,
                ErrorMessage = ex.Message,
                Exception = ex,
                StartedAt = DateTime.UtcNow,
                CompletedAt = DateTime.UtcNow
            };
        }
    }

    /// <summary>
    /// 获取已加载的插件
    /// </summary>
    public INodeExecutor? GetLoadedPlugin(string nodeType)
    {
        _loadedPlugins.TryGetValue(nodeType, out var executor);
        return executor;
    }

    /// <summary>
    /// 获取所有已加载的插件
    /// </summary>
    public IReadOnlyDictionary<string, INodeExecutor> GetAllLoadedPlugins()
    {
        return _loadedPlugins.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
    }

    /// <summary>
    /// 检查插件是否已加载
    /// </summary>
    public bool IsPluginLoaded(string nodeType)
    {
        return _loadedPlugins.ContainsKey(nodeType);
    }

    /// <summary>
    /// 发现可用插件
    /// </summary>
    public async Task<IReadOnlyList<PluginInfo>> DiscoverPluginsAsync(string searchPath, CancellationToken cancellationToken = default)
    {
        var plugins = new List<PluginInfo>();

        try
        {
            _logger.LogDebug("开始发现插件: {SearchPath}", searchPath);

            if (!Directory.Exists(searchPath))
            {
                _logger.LogWarning("插件搜索路径不存在: {SearchPath}", searchPath);
                return plugins;
            }

            var dllFiles = Directory.GetFiles(searchPath, "*.dll", SearchOption.AllDirectories);

            var tasks = dllFiles.Select(async dllFile =>
            {
                try
                {
                    var metadata = await GetPluginMetadataAsync(dllFile, cancellationToken);
                    if (metadata != null)
                    {
                        var pluginInfo = new PluginInfo
                        {
                            NodeType = metadata.NodeType,
                            Name = metadata.Name,
                            DisplayName = metadata.DisplayName,
                            Description = metadata.Description,
                            Version = metadata.Version,
                            Author = metadata.Author,
                            PluginType = PluginType.DllPrecompiled,
                            PluginPath = dllFile,
                            IsLoaded = _loadedPlugins.ContainsKey(metadata.NodeType),
                            Metadata = metadata
                        };

                        return pluginInfo;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "检查插件文件时发生异常: {DllFile}", dllFile);
                }

                return null;
            });

            var results = await Task.WhenAll(tasks);
            plugins.AddRange(results.Where(p => p != null)!);

            _logger.LogInformation("发现 {Count} 个DLL插件", plugins.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发现插件时发生异常: {SearchPath}", searchPath);
        }

        return plugins;
    }

    /// <summary>
    /// 验证插件
    /// </summary>
    public async Task<PluginValidationResult> ValidatePluginAsync(string pluginPath, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var result = new PluginValidationResult
        {
            PluginPath = pluginPath,
            StartedAt = DateTime.UtcNow
        };

        try
        {
            _logger.LogDebug("开始验证DLL插件: {PluginPath}", pluginPath);

            // 检查文件是否存在
            if (!File.Exists(pluginPath))
            {
                result.Errors.Add(new ValidationError
                {
                    Code = "FILE_NOT_FOUND",
                    Message = $"插件文件不存在: {pluginPath}",
                    Type = ValidationErrorType.FileNotFound,
                    Severity = ValidationSeverity.Critical
                });
                result.IsValid = false;
                return result;
            }

            result.CheckedItems.Add("文件存在性检查");

            // 检查文件是否为有效的.NET程序集
            try
            {
                using var pluginLoader = PluginLoader.CreateFromAssemblyFile(
                    pluginPath,
                    config =>
                    {
                        config.PreferSharedTypes = true;
                        config.IsUnloadable = true;
                        config.LoadInMemory = true;
                    });

                var assembly = pluginLoader.LoadDefaultAssembly();
                result.CheckedItems.Add("程序集加载检查");

                // 检查是否包含INodeExecutor实现
                var executorTypes = assembly.GetTypes()
                    .Where(t => typeof(INodeExecutor).IsAssignableFrom(t) && !t.IsInterface && !t.IsAbstract)
                    .ToList();

                if (!executorTypes.Any())
                {
                    result.Errors.Add(new ValidationError
                    {
                        Code = "NO_EXECUTOR_IMPLEMENTATION",
                        Message = "插件中未找到INodeExecutor的实现",
                        Type = ValidationErrorType.InvalidFormat,
                        Severity = ValidationSeverity.Critical
                    });
                    result.IsValid = false;
                }
                else if (executorTypes.Count > 1)
                {
                    result.Warnings.Add(new ValidationWarning
                    {
                        Code = "MULTIPLE_EXECUTOR_IMPLEMENTATIONS",
                        Message = $"插件中找到多个INodeExecutor实现: {string.Join(", ", executorTypes.Select(t => t.Name))}",
                        Type = ValidationWarningType.CompatibilityIssue
                    });
                }

                result.CheckedItems.Add("INodeExecutor实现检查");

                pluginLoader.Dispose();
            }
            catch (Exception ex)
            {
                result.Errors.Add(new ValidationError
                {
                    Code = "ASSEMBLY_LOAD_FAILED",
                    Message = $"加载程序集失败: {ex.Message}",
                    Type = ValidationErrorType.InvalidFormat,
                    Severity = ValidationSeverity.Critical
                });
                result.IsValid = false;
            }

            // 如果没有错误，则验证通过
            if (!result.Errors.Any())
            {
                result.IsValid = true;
            }

            _logger.LogDebug("DLL插件验证完成: {PluginPath}, 结果: {IsValid}, 耗时: {ElapsedMs}ms",
                pluginPath, result.IsValid, stopwatch.ElapsedMilliseconds);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证DLL插件时发生异常: {PluginPath}", pluginPath);

            result.IsValid = false;
            result.Errors.Add(new ValidationError
            {
                Code = "VALIDATION_EXCEPTION",
                Message = $"验证过程中发生异常: {ex.Message}",
                Type = ValidationErrorType.InvalidFormat,
                Severity = ValidationSeverity.Critical
            });
        }
        finally
        {
            stopwatch.Stop();
            result.ValidationTimeMs = stopwatch.ElapsedMilliseconds;
            result.CompletedAt = DateTime.UtcNow;
        }

        return result;
    }

    /// <summary>
    /// 获取插件元数据
    /// </summary>
    public async Task<PluginMetadata?> GetPluginMetadataAsync(string pluginPath, CancellationToken cancellationToken = default)
    {
        try
        {
            return await Task.Run(() =>
            {
                try
                {
                    using var pluginLoader = PluginLoader.CreateFromAssemblyFile(
                        pluginPath,
                        config =>
                        {
                            config.PreferSharedTypes = true;
                            config.IsUnloadable = true;
                            config.LoadInMemory = true;
                        });

                    var assembly = pluginLoader.LoadDefaultAssembly();

                    // 尝试从程序集属性中获取元数据
                    var assemblyName = assembly.GetName();
                    var fileInfo = new FileInfo(pluginPath);

                    // 查找INodeExecutor实现以获取节点类型
                    var executorType = assembly.GetTypes()
                        .FirstOrDefault(t => typeof(INodeExecutor).IsAssignableFrom(t) && !t.IsInterface && !t.IsAbstract);

                    var nodeType = executorType?.Name.Replace("Executor", "").Replace("NodeExecutor", "") ?? "Unknown";

                    var metadata = new PluginMetadata
                    {
                        NodeType = nodeType,
                        Name = assemblyName.Name ?? Path.GetFileNameWithoutExtension(pluginPath),
                        DisplayName = nodeType,
                        Description = $"DLL插件: {nodeType}",
                        Version = assemblyName.Version?.ToString() ?? "1.0.0",
                        Author = "Unknown",
                        CreatedAt = fileInfo.CreationTime,
                        ModifiedAt = fileInfo.LastWriteTime,
                        FileSize = fileInfo.Length,
                        FileHash = ComputeFileHash(pluginPath)
                    };

                    pluginLoader.Dispose();
                    return metadata;
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "获取插件元数据失败: {PluginPath}", pluginPath);
                    return null;
                }
            }, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取插件元数据时发生异常: {PluginPath}", pluginPath);
            return null;
        }
    }

    #region 私有方法

    /// <summary>
    /// 计算文件哈希值
    /// </summary>
    private string ComputeFileHash(string filePath)
    {
        try
        {
            using var stream = File.OpenRead(filePath);
            using var sha256 = System.Security.Cryptography.SHA256.Create();
            var hashBytes = sha256.ComputeHash(stream);
            return Convert.ToHexString(hashBytes);
        }
        catch
        {
            return string.Empty;
        }
    }

    #endregion
}
