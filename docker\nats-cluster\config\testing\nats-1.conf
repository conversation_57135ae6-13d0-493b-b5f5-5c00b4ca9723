# NATS Server Configuration - Testing Environment
# Node 1 of 3 in FlowCustomV1 Testing Cluster

# Server Identity
server_name: "flowcustom-test-nats-1"

# Network Configuration - Testing ports (+20000)
port: 4222
http_port: 8222

# Cluster Configuration
cluster {
    name: "flowcustom-test-cluster"
    port: 6222
    
    # Routes to other cluster members
    routes: [
        "nats://nats-test-server-2:6222"
        "nats://nats-test-server-3:6222"
    ]
    
    # Cluster authentication (testing environment)
    authorization {
        user: "cluster_user"
        password: "test_cluster_password"
        timeout: 2
    }
}

# JetStream Configuration - Testing optimized
jetstream {
    enabled: true
    store_dir: "/data/jetstream"
    
    # Testing environment limits
    max_memory_store: 256MB
    max_file_store: 1GB
    
    # Domain for testing
    domain: "flowcustom-test"
}

# Client Authentication - Testing environment
authorization {
    users: [
        {
            user: "flowcustom"
            password: "flowcustom_password"
            permissions: {
                publish: {
                    allow: ["flowcustom-test.>", "_INBOX.>"]
                }
                subscribe: {
                    allow: ["flowcustom-test.>", "_INBOX.>", "$JS.>"]
                }
            }
        },
        {
            user: "test_admin"
            password: "test_admin_password"
            permissions: {
                publish: {
                    allow: [">"]
                }
                subscribe: {
                    allow: [">"]
                }
            }
        }
    ]
}

# Logging Configuration - Testing environment
log_file: "/var/log/nats/nats-server.log"
logtime: true
debug: true
trace: false

# Monitoring Configuration
monitor_port: 8222
server_tags: [
    "environment:testing",
    "cluster:flowcustom-test",
    "node:1",
    "version:v0.0.1.8"
]

# Performance Settings - Testing optimized
max_connections: 1000
max_subscriptions: 10000
max_payload: 1MB
max_pending: 64MB

# Write deadline for slow consumers
write_deadline: "10s"

# Ping settings
ping_interval: "2m"
ping_max: 2

# TLS Configuration (disabled for testing)
# tls {
#     cert_file: "/etc/nats/certs/server.crt"
#     key_file: "/etc/nats/certs/server.key"
#     ca_file: "/etc/nats/certs/ca.crt"
#     verify: true
# }
