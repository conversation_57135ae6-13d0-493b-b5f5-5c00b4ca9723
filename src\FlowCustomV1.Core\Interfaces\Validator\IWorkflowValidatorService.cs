using FlowCustomV1.Core.Models.Workflow;

namespace FlowCustomV1.Core.Interfaces.Validator;

/// <summary>
/// 工作流验证器服务接口
/// 提供分布式工作流验证功能，支持验证规则引擎和结果缓存
/// </summary>
public interface IWorkflowValidatorService
{
    /// <summary>
    /// 验证工作流定义
    /// </summary>
    /// <param name="workflowDefinition">工作流定义</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    Task<WorkflowValidationResult> ValidateWorkflowAsync(WorkflowDefinition workflowDefinition, CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证节点配置
    /// </summary>
    /// <param name="nodeDefinition">节点定义</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    Task<NodeValidationResult> ValidateNodeAsync(NodeDefinition nodeDefinition, CancellationToken cancellationToken = default);

    /// <summary>
    /// 批量验证工作流
    /// </summary>
    /// <param name="workflowDefinitions">工作流定义列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>批量验证结果</returns>
    Task<BatchValidationResult> ValidateBatchAsync(IEnumerable<WorkflowDefinition> workflowDefinitions, CancellationToken cancellationToken = default);

    /// <summary>
    /// 检查循环依赖
    /// </summary>
    /// <param name="workflowDefinition">工作流定义</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>循环依赖检查结果</returns>
    Task<CyclicDependencyResult> CheckCyclicDependencyAsync(WorkflowDefinition workflowDefinition, CancellationToken cancellationToken = default);

    /// <summary>
    /// 性能分析
    /// </summary>
    /// <param name="workflowDefinition">工作流定义</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>性能分析结果</returns>
    Task<PerformanceAnalysisResult> AnalyzePerformanceAsync(WorkflowDefinition workflowDefinition, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取缓存的验证结果
    /// </summary>
    /// <param name="workflowHash">工作流哈希值</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>缓存的验证结果</returns>
    Task<WorkflowValidationResult?> GetCachedValidationAsync(string workflowHash, CancellationToken cancellationToken = default);

    /// <summary>
    /// 缓存验证结果
    /// </summary>
    /// <param name="workflowHash">工作流哈希值</param>
    /// <param name="validationResult">验证结果</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>缓存任务</returns>
    Task CacheValidationResultAsync(string workflowHash, WorkflowValidationResult validationResult, CancellationToken cancellationToken = default);

    /// <summary>
    /// 注册自定义验证规则
    /// </summary>
    /// <param name="ruleName">规则名称</param>
    /// <param name="rule">验证规则</param>
    /// <returns>注册结果</returns>
    Task<bool> RegisterValidationRuleAsync(string ruleName, IValidationRule rule);

    /// <summary>
    /// 移除验证规则
    /// </summary>
    /// <param name="ruleName">规则名称</param>
    /// <returns>移除结果</returns>
    Task<bool> RemoveValidationRuleAsync(string ruleName);

    /// <summary>
    /// 获取所有注册的验证规则
    /// </summary>
    /// <returns>验证规则字典</returns>
    Task<IReadOnlyDictionary<string, IValidationRule>> GetValidationRulesAsync();

    /// <summary>
    /// 启动验证服务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>启动任务</returns>
    Task StartAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 停止验证服务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>停止任务</returns>
    Task StopAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取服务状态
    /// </summary>
    /// <returns>服务状态</returns>
    ValidatorServiceStatus GetStatus();

    /// <summary>
    /// 获取验证统计信息
    /// </summary>
    /// <returns>验证统计</returns>
    ValidationStatistics GetStatistics();
}

/// <summary>
/// 循环依赖检查结果
/// </summary>
public class CyclicDependencyResult
{
    /// <summary>
    /// 是否存在循环依赖
    /// </summary>
    public bool HasCyclicDependency { get; set; }

    /// <summary>
    /// 循环路径列表
    /// </summary>
    public List<List<string>> CyclicPaths { get; set; } = new();

    /// <summary>
    /// 检查详情
    /// </summary>
    public string Details { get; set; } = string.Empty;

    /// <summary>
    /// 检查时间
    /// </summary>
    public DateTime CheckedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 性能分析结果
/// </summary>
public class PerformanceAnalysisResult
{
    /// <summary>
    /// 预估执行时间（毫秒）
    /// </summary>
    public long EstimatedExecutionTimeMs { get; set; }

    /// <summary>
    /// 预估内存使用（MB）
    /// </summary>
    public int EstimatedMemoryUsageMb { get; set; }

    /// <summary>
    /// 复杂度评分（1-10）
    /// </summary>
    public int ComplexityScore { get; set; }

    /// <summary>
    /// 性能瓶颈节点
    /// </summary>
    public List<string> BottleneckNodes { get; set; } = new();

    /// <summary>
    /// 优化建议
    /// </summary>
    public List<string> OptimizationSuggestions { get; set; } = new();

    /// <summary>
    /// 分析时间
    /// </summary>
    public DateTime AnalyzedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 批量验证结果
/// </summary>
public class BatchValidationResult
{
    /// <summary>
    /// 总验证数量
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// 成功验证数量
    /// </summary>
    public int SuccessCount { get; set; }

    /// <summary>
    /// 失败验证数量
    /// </summary>
    public int FailureCount { get; set; }

    /// <summary>
    /// 个别验证结果
    /// </summary>
    public Dictionary<string, WorkflowValidationResult> Results { get; set; } = new();

    /// <summary>
    /// 批量验证摘要
    /// </summary>
    public string Summary { get; set; } = string.Empty;

    /// <summary>
    /// 验证时间
    /// </summary>
    public DateTime ValidatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 总耗时（毫秒）
    /// </summary>
    public long TotalDurationMs { get; set; }
}

/// <summary>
/// 验证器服务状态
/// </summary>
public enum ValidatorServiceStatus
{
    /// <summary>
    /// 未启动
    /// </summary>
    NotStarted,

    /// <summary>
    /// 启动中
    /// </summary>
    Starting,

    /// <summary>
    /// 运行中
    /// </summary>
    Running,

    /// <summary>
    /// 停止中
    /// </summary>
    Stopping,

    /// <summary>
    /// 已停止
    /// </summary>
    Stopped,

    /// <summary>
    /// 错误状态
    /// </summary>
    Error
}

/// <summary>
/// 验证统计信息
/// </summary>
public class ValidationStatistics
{
    /// <summary>
    /// 总验证次数
    /// </summary>
    public long TotalValidations { get; set; }

    /// <summary>
    /// 成功验证次数
    /// </summary>
    public long SuccessfulValidations { get; set; }

    /// <summary>
    /// 失败验证次数
    /// </summary>
    public long FailedValidations { get; set; }

    /// <summary>
    /// 缓存命中次数
    /// </summary>
    public long CacheHits { get; set; }

    /// <summary>
    /// 缓存未命中次数
    /// </summary>
    public long CacheMisses { get; set; }

    /// <summary>
    /// 平均验证时间（毫秒）
    /// </summary>
    public double AverageValidationTimeMs { get; set; }

    /// <summary>
    /// 最后验证时间
    /// </summary>
    public DateTime? LastValidationTime { get; set; }

    /// <summary>
    /// 服务启动时间
    /// </summary>
    public DateTime ServiceStartTime { get; set; } = DateTime.UtcNow;
}
