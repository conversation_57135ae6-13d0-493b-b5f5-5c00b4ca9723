import React, { useState } from 'react';
import { useLocation, useNavigate, Link } from 'react-router-dom';
import { ProLayout, MenuDataItem } from '@ant-design/pro-components';
import { Avatar, Dropdown, Space, Badge, Button } from 'antd';
import {
  UserOutlined,
  LogoutOutlined,
  SettingOutlined,
  BellOutlined,
  QuestionCircleOutlined,
  GithubOutlined,
} from '@ant-design/icons';
import { menuItems } from '@/constants/menu';

interface BasicLayoutProps {
  children: React.ReactNode;
}

const BasicLayout: React.FC<BasicLayoutProps> = ({ children }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const [collapsed, setCollapsed] = useState(false);

  // 转换菜单数据格式
  const convertMenuItems = (items: any[]): MenuDataItem[] => {
    return items.map(item => ({
      key: item.key,
      name: item.label,
      icon: item.icon ? React.createElement(item.icon) : undefined,
      path: item.path,
      children: item.children ? convertMenuItems(item.children) : undefined,
      disabled: item.disabled,
    }));
  };

  // 用户下拉菜单
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人中心',
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '个人设置',
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
    },
  ];

  const handleUserMenuClick = ({ key }: { key: string }) => {
    switch (key) {
      case 'profile':
        navigate('/profile');
        break;
      case 'settings':
        navigate('/settings');
        break;
      case 'logout':
        // 处理退出登录逻辑
        console.log('退出登录');
        break;
    }
  };

  return (
    <ProLayout
        title="FlowCustomV1"
        logo="/logo.svg"
        layout="mix"
        contentWidth="Fluid"
        splitMenus={false}
        navTheme="light"

        fixedHeader
        fixSiderbar

        // 直接控制内容渲染，移除ProLayout Content层级
        contentStyle={{
          margin: 0,
          padding: '8px',
          background: '#f0f2f5',
          height: 'calc(100vh - 64px)',
          overflow: 'auto'
        }}
        collapsed={collapsed}
        onCollapse={setCollapsed}
        location={{
          pathname: location.pathname,
        }}
        route={{
          path: '/',
          routes: convertMenuItems(menuItems),
        }}
        menuDataRender={() => convertMenuItems(menuItems)}
        menuItemRender={(item, dom) => (
          <Link to={item.path || '/'} style={{ textDecoration: 'none' }}>
            {dom}
          </Link>
        )}
        breadcrumbRender={(routers = []) => [
          {
            path: '/',
            breadcrumbName: '首页',
          },
          ...routers,
        ]}
        headerTitleRender={(logo, title) => (
          <div className="flex items-center">
            {logo}
            <span className="ml-2 text-gray-800 font-semibold text-lg">{title}</span>
            <Badge
              count="v0.0.1.13-AntVX6"
              style={{
                backgroundColor: '#52c41a',
                marginLeft: 8,
                fontSize: '10px',
                height: '18px',
                lineHeight: '18px',
                borderRadius: '9px',
                padding: '0 6px',
              }}
            />
          </div>
        )}
        headerContentRender={() => (
          <div className="flex items-center space-x-4">
            <Button
              type="text"
              icon={<QuestionCircleOutlined />}
              className="text-gray-600 hover:bg-gray-100"
              onClick={() => window.open('https://github.com/your-repo/FlowCustomV1', '_blank')}
            >
              帮助文档
            </Button>
            <Button
              type="text"
              icon={<GithubOutlined />}
              className="text-gray-600 hover:bg-gray-100"
              onClick={() => window.open('https://github.com/your-repo/FlowCustomV1', '_blank')}
            >
              GitHub
            </Button>
          </div>
        )}
        actionsRender={() => [
          <Badge key="notification" count={5} size="small">
            <Button
              type="text"
              icon={<BellOutlined />}
              className="text-gray-600 hover:bg-gray-100"
            />
          </Badge>,
          <Dropdown
            key="user"
            menu={{
              items: userMenuItems,
              onClick: handleUserMenuClick,
            }}
            placement="bottomRight"
          >
            <Space className="cursor-pointer text-gray-600 hover:bg-gray-100 px-2 py-1 rounded">
              <Avatar size="small" icon={<UserOutlined />} />
              <span>管理员</span>
            </Space>
          </Dropdown>,
        ]}
        footerRender={false}
        menuFooterRender={() => (
          <div className="text-center text-gray-500 py-3 px-2 border-t">
            <div className="text-xs font-medium">FlowCustomV1</div>
            <div className="text-xs">©2025 版权所有</div>
          </div>
        )}
      >
        {children}
      </ProLayout>
  );
};

export default BasicLayout;
