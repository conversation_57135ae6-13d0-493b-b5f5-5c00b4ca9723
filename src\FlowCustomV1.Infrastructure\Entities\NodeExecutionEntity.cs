using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using FlowCustomV1.Core.Models.Workflow;

namespace FlowCustomV1.Infrastructure.Entities;

/// <summary>
/// 节点执行实体
/// 对应数据库中的NodeExecutions表
/// </summary>
[Table("NodeExecutions")]
public class NodeExecutionEntity
{
    /// <summary>
    /// 主键ID
    /// </summary>
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public long Id { get; set; }

    /// <summary>
    /// 执行唯一标识符
    /// </summary>
    [Required]
    [MaxLength(50)]
    [Column("ExecutionId")]
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 实例唯一标识符
    /// </summary>
    [Column(TypeName = "varchar(255)")]
    public string InstanceId { get; set; } = string.Empty;

    /// <summary>
    /// 节点标识符
    /// </summary>
    [Column(TypeName = "varchar(255)")]
    public string NodeId { get; set; } = string.Empty;

    /// <summary>
    /// 节点类型
    /// </summary>
    [Required]
    [MaxLength(50)]
    [Column("NodeType")]
    public string NodeType { get; set; } = string.Empty;

    /// <summary>
    /// 执行状态
    /// </summary>
    [Required]
    [Column("State")]
    public NodeExecutionState State { get; set; } = NodeExecutionState.NotStarted;

    /// <summary>
    /// 是否成功
    /// </summary>
    [Column("IsSuccess")]
    public bool IsSuccess { get; set; } = false;

    /// <summary>
    /// 开始时间
    /// </summary>
    [Required]
    [Column("StartedAt")]
    public DateTime StartedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 完成时间
    /// </summary>
    [Column("CompletedAt")]
    public DateTime? CompletedAt { get; set; }

    /// <summary>
    /// 输入数据（JSON格式）
    /// </summary>
    [Column("InputData", TypeName = "TEXT")]
    public string? InputData { get; set; }

    /// <summary>
    /// 输出数据（JSON格式）
    /// </summary>
    [Column("OutputData", TypeName = "TEXT")]
    public string? OutputData { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    [Column("ErrorMessage", TypeName = "TEXT")]
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 重试次数
    /// </summary>
    [Column("RetryCount")]
    public int RetryCount { get; set; } = 0;

    /// <summary>
    /// 执行日志（JSON格式）
    /// </summary>
    [Column("ExecutionLogs", TypeName = "TEXT")]
    public string? ExecutionLogs { get; set; }

    /// <summary>
    /// 性能指标（JSON格式）
    /// </summary>
    [Column("PerformanceMetrics", TypeName = "TEXT")]
    public string? PerformanceMetrics { get; set; }

    /// <summary>
    /// 元数据（JSON格式）
    /// </summary>
    [Column("Metadata", TypeName = "TEXT")]
    public string? Metadata { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    [Required]
    [Column("CreatedAt")]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 最后更新时间
    /// </summary>
    [Required]
    [Column("LastUpdatedAt")]
    public DateTime LastUpdatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 关联的工作流实例
    /// </summary>
    [ForeignKey(nameof(InstanceId))]
    public virtual WorkflowInstanceEntity? WorkflowInstance { get; set; }

    /// <summary>
    /// 版本戳
    /// </summary>
    [Timestamp]
    public byte[]? RowVersion { get; set; }

    /// <summary>
    /// 计算执行时长
    /// </summary>
    public TimeSpan Duration => CompletedAt?.Subtract(StartedAt) ?? TimeSpan.Zero;
}
