import React, { useState } from 'react';
import { Card, Button, Space, Tabs } from 'antd';
import {
  ClockCircleOutlined,
  SettingOutlined,
  ApiOutlined,
  DatabaseOutlined,
  NotificationOutlined,
  MinusOutlined,
  PlusOutlined,
  ApartmentOutlined,
  SaveOutlined,
  PlayCircleOutlined,
  StopOutlined,
  FileOutlined,
  EditOutlined,
  EyeOutlined,
  BugOutlined,
  HistoryOutlined,
  ShareAltOutlined,
} from '@ant-design/icons';
import PageLayout from '@/components/Layout/PageLayout';

// 导航菜单配置
const navigationTabs = [
  { key: 'design', label: '设计', icon: <EditOutlined /> },
  { key: 'preview', label: '预览', icon: <EyeOutlined /> },
  { key: 'debug', label: '调试', icon: <BugOutlined /> },
  { key: 'history', label: '历史', icon: <HistoryOutlined /> },
  { key: 'share', label: '分享', icon: <ShareAltOutlined /> }
];

// 节点数据配置
const nodeGroups = [
  {
    id: 'basic',
    title: '基础控制',
    collapsed: false,
    nodes: [
      {
        id: 'start',
        title: '开始',
        subtitle: '流程开始节点',
        icon: PlayCircleOutlined,
        color: '#52c41a',
        bgColor: '#f6ffed',
        borderColor: '#b7eb8f'
      },
      {
        id: 'end',
        title: '结束',
        subtitle: '流程结束节点',
        icon: StopOutlined,
        color: '#ff4d4f',
        bgColor: '#fff2f0',
        borderColor: '#ffadd2'
      },
      {
        id: 'task',
        title: '任务',
        subtitle: '通用任务节点',
        icon: SettingOutlined,
        color: '#1890ff',
        bgColor: '#e6f7ff',
        borderColor: '#91d5ff'
      }
    ]
  },
  {
    id: 'trigger',
    title: '触发器',
    collapsed: false,
    nodes: [
      {
        id: 'timer-trigger',
        title: '定时触发',
        subtitle: '定时触发',
        icon: ClockCircleOutlined,
        color: '#fa8c16',
        bgColor: '#fff7e6',
        borderColor: '#ffd591'
      },
      {
        id: 'event-trigger',
        title: '事件触发',
        subtitle: '事件触发',
        icon: SettingOutlined,
        color: '#1890ff',
        bgColor: '#e6f7ff',
        borderColor: '#91d5ff'
      },
      {
        id: 'webhook-trigger',
        title: 'Webhook',
        subtitle: 'Webhook触发',
        icon: SettingOutlined,
        color: '#1890ff',
        bgColor: '#e6f7ff',
        borderColor: '#91d5ff'
      }
    ]
  },
  {
    id: 'action',
    title: '动作节点',
    collapsed: false,
    nodes: [
      {
        id: 'http-request',
        title: 'HTTP请求',
        subtitle: 'HTTP请求',
        icon: ApiOutlined,
        color: '#722ed1',
        bgColor: '#f9f0ff',
        borderColor: '#d3adf7'
      },
      {
        id: 'data-processor',
        title: '数据处理',
        subtitle: '数据处理',
        icon: DatabaseOutlined,
        color: '#13c2c2',
        bgColor: '#e6fffb',
        borderColor: '#87e8de'
      },
      {
        id: 'notification',
        title: '通知发送',
        subtitle: '通知发送',
        icon: NotificationOutlined,
        color: '#1890ff',
        bgColor: '#e6f7ff',
        borderColor: '#91d5ff'
      }
    ]
  }
];

const WorkflowDesignerTest: React.FC = () => {
  const [groupStates, setGroupStates] = useState<Record<string, boolean>>(
    nodeGroups.reduce((acc, group) => ({ ...acc, [group.id]: !group.collapsed }), {})
  );
  const [activeTab, setActiveTab] = useState('design');

  const toggleGroup = (groupId: string) => {
    setGroupStates(prev => ({
      ...prev,
      [groupId]: !prev[groupId]
    }));
  };

  const handleNodeDragStart = (e: React.DragEvent, nodeId: string) => {
    e.dataTransfer.setData('text/plain', nodeId);
    console.log('开始拖拽节点:', nodeId);
  };

  const renderDesignContent = () => (
    <div className="flex gap-4 h-full">
      {/* 左侧组件库 */}
      <Card
        title="组件库"
        className="w-64 flex-shrink-0"
        styles={{ body: { padding: '12px', height: 'calc(100% - 57px)', overflow: 'auto' } }}
      >
        <div className="component-library">
          {nodeGroups.map(group => (
            <div key={group.id} className="node-group" style={{ marginBottom: '16px' }}>
              {/* 分组标题 */}
              <div 
                className="group-header"
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  padding: '8px 12px',
                  background: '#f5f5f5',
                  borderRadius: '6px',
                  cursor: 'pointer',
                  marginBottom: '8px',
                  fontSize: '13px',
                  fontWeight: 500,
                  color: '#333'
                }}
                onClick={() => toggleGroup(group.id)}
              >
                {groupStates[group.id] ? <MinusOutlined /> : <PlusOutlined />}
                <span style={{ marginLeft: '8px' }}>{group.title}</span>
              </div>

              {/* 节点列表 */}
              {groupStates[group.id] && (
                <div className="node-list">
                  {group.nodes.map(node => {
                    const IconComponent = node.icon;
                    return (
                      <div
                        key={node.id}
                        className="workflow-node-item"
                        draggable
                        onDragStart={(e) => handleNodeDragStart(e, node.id)}
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          padding: '12px',
                          margin: '4px 0',
                          background: node.bgColor,
                          border: `2px solid ${node.borderColor}`,
                          borderRadius: '8px',
                          cursor: 'grab',
                          transition: 'all 0.2s ease',
                          minHeight: '60px'
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.transform = 'translateY(-2px)';
                          e.currentTarget.style.boxShadow = `0 4px 12px ${node.color}40`;
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.transform = 'translateY(0)';
                          e.currentTarget.style.boxShadow = 'none';
                        }}
                      >
                        {/* 节点图标 */}
                        <div 
                          style={{
                            width: '32px',
                            height: '32px',
                            borderRadius: '6px',
                            background: node.color,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            marginRight: '12px',
                            flexShrink: 0
                          }}
                        >
                          <IconComponent style={{ color: 'white', fontSize: '16px' }} />
                        </div>

                        {/* 节点文本 */}
                        <div style={{ flex: 1, minWidth: 0 }}>
                          <div 
                            style={{
                              fontSize: '14px',
                              fontWeight: 500,
                              color: '#333',
                              lineHeight: '20px',
                              marginBottom: '2px'
                            }}
                          >
                            {node.title}
                          </div>
                          <div 
                            style={{
                              fontSize: '12px',
                              color: '#666',
                              lineHeight: '16px'
                            }}
                          >
                            {node.subtitle}
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          ))}
        </div>
      </Card>

      {/* 中间画布区域 */}
      <Card
        className="flex-1"
        styles={{ body: { padding: '20px', height: 'calc(100% - 57px)' } }}
      >
        <div 
          style={{
            width: '100%',
            height: '100%',
            border: '2px dashed #d9d9d9',
            borderRadius: '8px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            background: '#fafafa',
            color: '#999',
            fontSize: '16px'
          }}
          onDragOver={(e) => e.preventDefault()}
          onDrop={(e) => {
            e.preventDefault();
            const nodeId = e.dataTransfer.getData('text/plain');
            console.log('放置节点:', nodeId);
          }}
        >
          拖拽节点到此处开始设计工作流
        </div>
      </Card>

      {/* 右侧属性面板 */}
      <div className="w-64 flex-shrink-0">
        <Card title="属性面板" styles={{ body: { padding: '16px' } }}>
          <div style={{ color: '#666', fontSize: '14px' }}>
            选择节点查看属性
          </div>
        </Card>
      </div>
    </div>
  );

  const renderOtherContent = (icon: React.ReactNode, title: string, description: string) => (
    <div style={{ 
      display: 'flex', 
      alignItems: 'center', 
      justifyContent: 'center', 
      height: '100%',
      flexDirection: 'column',
      gap: '16px'
    }}>
      {icon}
      <div style={{ fontSize: '18px', color: '#333' }}>{title}</div>
      <div style={{ fontSize: '14px', color: '#666' }}>{description}</div>
    </div>
  );

  return (
    <PageLayout
      title="工作流设计器测试"
      description="测试********-临时版的流程设计页面样式"
      icon={<ApartmentOutlined />}
      actions={
        <Space>
          <Button icon={<SaveOutlined />} type="primary">保存</Button>
          <Button icon={<PlayCircleOutlined />}>执行</Button>
        </Space>
      }
    >
      {/* 🟡 导航菜单区域 */}
      <div 
        className="workflow-navigation"
        style={{
          marginBottom: '16px',
          background: '#fff',
          borderRadius: '6px',
          border: '1px solid #d9d9d9',
          overflow: 'hidden'
        }}
      >
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          type="card"
          size="small"
          style={{ margin: 0 }}
          items={navigationTabs.map(tab => ({
            key: tab.key,
            label: (
              <span style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
                {tab.icon}
                {tab.label}
              </span>
            ),
          }))}
        />
      </div>

      {/* 🟠 工具栏区域 */}
      {activeTab === 'design' && (
        <div 
          className="workflow-toolbar"
          style={{
            marginBottom: '16px',
            background: '#fff',
            borderRadius: '6px',
            border: '1px solid #d9d9d9',
            padding: '12px 16px'
          }}
        >
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Space>
              <Button size="small" icon={<SaveOutlined />}>保存</Button>
              <Button size="small" icon={<FileOutlined />}>另存为</Button>
              <Button size="small" icon={<HistoryOutlined />}>撤销</Button>
              <Button size="small" icon={<HistoryOutlined />}>重做</Button>
            </Space>
            <Space>
              <Button size="small" type="primary" icon={<PlayCircleOutlined />}>运行</Button>
              <Button size="small" icon={<BugOutlined />}>调试</Button>
              <Button size="small" icon={<SettingOutlined />}>设置</Button>
            </Space>
          </div>
        </div>
      )}

      {/* 🔵 主要内容区域 */}
      <div 
        className="workflow-designer-test-container"
        style={{
          height: activeTab === 'design' ? 'calc(100vh - 340px)' : 'calc(100vh - 280px)',
          border: '2px solid #2196f3',
          borderRadius: '6px',
          padding: 'var(--layout-element-spacing)',
          background: '#fff',
          overflow: 'hidden'
        }}
      >
        {/* 根据activeTab渲染不同内容 */}
        {activeTab === 'design' && renderDesignContent()}
        {activeTab === 'preview' && renderOtherContent(
          <EyeOutlined style={{ fontSize: '48px', color: '#1890ff' }} />,
          '工作流预览',
          '在这里可以预览工作流的执行流程'
        )}
        {activeTab === 'debug' && renderOtherContent(
          <BugOutlined style={{ fontSize: '48px', color: '#fa8c16' }} />,
          '调试模式',
          '在这里可以调试工作流的执行过程'
        )}
        {activeTab === 'history' && renderOtherContent(
          <HistoryOutlined style={{ fontSize: '48px', color: '#52c41a' }} />,
          '历史记录',
          '查看工作流的修改历史和版本记录'
        )}
        {activeTab === 'share' && renderOtherContent(
          <ShareAltOutlined style={{ fontSize: '48px', color: '#722ed1' }} />,
          '分享工作流',
          '生成分享链接或导出工作流配置'
        )}
      </div>
    </PageLayout>
  );
};

export default WorkflowDesignerTest;
