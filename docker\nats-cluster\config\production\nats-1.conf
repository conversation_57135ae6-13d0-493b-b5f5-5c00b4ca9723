# NATS Server Configuration - Production Environment
# Node 1 of 3 in FlowCustomV1 Production Cluster

# Server Identity
server_name: "flowcustom-prod-nats-1"

# Network Configuration - Production ports
port: 4222
http_port: 8222

# Cluster Configuration
cluster {
    name: "flowcustom-prod-cluster"
    port: 6222
    
    # Routes to other cluster members (TO_BE_CONFIGURED_ON_DEPLOYMENT)
    routes: [
        "nats://nats-prod-server-2:6222"
        "nats://nats-prod-server-3:6222"
    ]
    
    # Cluster authentication (production environment - SECURE)
    authorization {
        user: "TO_BE_CONFIGURED_ON_DEPLOYMENT"
        password: "TO_BE_CONFIGURED_ON_DEPLOYMENT"
        timeout: 5
    }
}

# JetStream Configuration - Production optimized
jetstream {
    enabled: true
    store_dir: "/data/jetstream"
    
    # Production environment limits
    max_memory_store: 2GB
    max_file_store: 100GB
    
    # Domain for production
    domain: "flowcustom-prod"
    
    # Production durability settings
    sync_interval: "2s"
    sync_always: true
}

# Client Authentication - Production environment (SECURE)
authorization {
    users: [
        {
            user: "TO_BE_CONFIGURED_ON_DEPLOYMENT"
            password: "TO_BE_CONFIGURED_ON_DEPLOYMENT"
            permissions: {
                publish: {
                    allow: ["flowcustom-prod.>", "_INBOX.>"]
                }
                subscribe: {
                    allow: ["flowcustom-prod.>", "_INBOX.>", "$JS.>"]
                }
            }
        },
        {
            user: "TO_BE_CONFIGURED_ON_DEPLOYMENT"
            password: "TO_BE_CONFIGURED_ON_DEPLOYMENT"
            permissions: {
                publish: {
                    allow: ["flowcustom-prod.monitoring.>", "flowcustom-prod.admin.>"]
                }
                subscribe: {
                    allow: ["flowcustom-prod.>", "$JS.>"]
                }
            }
        }
    ]
}

# Logging Configuration - Production environment (minimal)
log_file: "/var/log/nats/nats-server.log"
logtime: true
debug: false
trace: false
log_size_limit: 100MB
max_traced_msg_len: 0

# Monitoring Configuration
monitor_port: 8222
server_tags: [
    "environment:production",
    "cluster:flowcustom-prod",
    "node:1",
    "version:v0.0.1.8",
    "datacenter:TO_BE_CONFIGURED"
]

# Performance Settings - Production optimized
max_connections: 10000
max_subscriptions: 100000
max_payload: 8MB
max_pending: 256MB

# Write deadline for slow consumers
write_deadline: "30s"

# Ping settings
ping_interval: "5m"
ping_max: 3

# TLS Configuration (REQUIRED for production)
tls {
    cert_file: "/etc/nats/certs/server.crt"
    key_file: "/etc/nats/certs/server.key"
    ca_file: "/etc/nats/certs/ca.crt"
    verify: true
    timeout: 5
    
    # Cipher suites for security
    cipher_suites: [
        "TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384",
        "TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384",
        "TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305",
        "TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305"
    ]
    
    # Minimum TLS version
    min_version: "1.2"
}

# System Account (for monitoring and management)
system_account: "SYS"

# Accounts Configuration (production multi-tenancy)
accounts: {
    SYS: {
        users: [
            {
                user: "TO_BE_CONFIGURED_ON_DEPLOYMENT"
                password: "TO_BE_CONFIGURED_ON_DEPLOYMENT"
            }
        ]
    }
    FLOWCUSTOM: {
        users: [
            {
                user: "TO_BE_CONFIGURED_ON_DEPLOYMENT"
                password: "TO_BE_CONFIGURED_ON_DEPLOYMENT"
            }
        ]
        jetstream: enabled
    }
}
