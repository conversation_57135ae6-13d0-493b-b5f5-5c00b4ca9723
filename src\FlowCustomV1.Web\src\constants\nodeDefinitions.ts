import type { NodeCategory, NodeDefinition } from '@/types/workflow';

// 基础控制节点定义
const basicControlNodes: NodeDefinition[] = [
  {
    type: 'Start',
    name: '开始节点',
    displayName: '开始',
    description: '工作流开始节点',
    icon: '▶️',
    color: '#52c41a',
    category: 'basic',
    version: '1.0.0',
    author: 'FlowCustomV1',
    tags: ['builtin', 'control', 'start'],
    inputs: [],
    outputs: [
      { id: 'output', name: '输出', type: 'control', description: '开始节点输出' }
    ],
    properties: [
      { id: 'name', name: '节点名称', type: 'string', defaultValue: '开始', required: true },
      { id: 'description', name: '描述', type: 'textarea', defaultValue: '' }
    ]
  },
  {
    type: 'End',
    name: '结束节点',
    displayName: '结束',
    description: '工作流结束节点',
    icon: '⏹️',
    color: '#ff4d4f',
    category: 'basic',
    version: '1.0.0',
    author: 'FlowCustomV1',
    tags: ['builtin', 'control', 'end'],
    inputs: [
      { id: 'input', name: '输入', type: 'control', description: '结束节点输入' }
    ],
    outputs: [],
    properties: [
      { id: 'name', name: '节点名称', type: 'string', defaultValue: '结束', required: true },
      { id: 'description', name: '描述', type: 'textarea', defaultValue: '' }
    ]
  },
  {
    type: 'Task',
    name: '任务节点',
    displayName: '任务',
    description: '通用任务执行节点',
    icon: '⚙️',
    color: '#1890ff',
    category: 'basic',
    version: '1.0.0',
    author: 'FlowCustomV1',
    tags: ['builtin', 'control', 'task'],
    inputs: [
      { id: 'input', name: '输入', type: 'control', description: '任务节点输入' }
    ],
    outputs: [
      { id: 'output', name: '输出', type: 'control', description: '任务节点输出' }
    ],
    properties: [
      { id: 'name', name: '节点名称', type: 'string', defaultValue: '任务', required: true },
      { id: 'description', name: '描述', type: 'textarea', defaultValue: '' },
      { id: 'taskName', name: '任务名称', type: 'string', defaultValue: 'default_task', required: true },
      { id: 'delaySeconds', name: '延迟秒数', type: 'number', defaultValue: 0 }
    ]
  }
];

// 触发器节点定义
const triggerNodes: NodeDefinition[] = [
  {
    type: 'TimerTrigger',
    name: '定时触发器',
    displayName: '定时触发',
    description: '按指定时间间隔触发工作流',
    icon: '⏰',
    color: '#fa8c16',
    category: 'trigger',
    version: '1.0.0',
    author: 'FlowCustomV1',
    tags: ['builtin', 'trigger', 'timer'],
    inputs: [],
    outputs: [
      { id: 'output', name: '输出', type: 'control', description: '触发器输出' }
    ],
    properties: [
      { id: 'name', name: '节点名称', type: 'string', defaultValue: '定时触发', required: true },
      { id: 'description', name: '描述', type: 'textarea', defaultValue: '' },
      { id: 'intervalSeconds', name: '间隔秒数', type: 'number', defaultValue: 60, required: true },
      { id: 'maxExecutions', name: '最大执行次数', type: 'number', defaultValue: -1 }
    ]
  },
  {
    type: 'EventTrigger',
    name: '事件触发器',
    displayName: '事件触发',
    description: '监听特定事件触发工作流',
    icon: '📡',
    color: '#fa8c16',
    category: 'trigger',
    version: '1.0.0',
    author: 'FlowCustomV1',
    tags: ['builtin', 'trigger', 'event'],
    inputs: [],
    outputs: [
      { id: 'output', name: '输出', type: 'control', description: '触发器输出' }
    ],
    properties: [
      { id: 'name', name: '节点名称', type: 'string', defaultValue: '事件触发', required: true },
      { id: 'description', name: '描述', type: 'textarea', defaultValue: '' },
      { id: 'eventType', name: '事件类型', type: 'string', defaultValue: 'custom-event', required: true },
      { id: 'eventSource', name: '事件源', type: 'string', defaultValue: 'system' }
    ]
  },
  {
    type: 'WebhookTrigger',
    name: 'Webhook触发器',
    displayName: 'Webhook触发',
    description: '接收HTTP Webhook请求触发工作流',
    icon: '🌐',
    color: '#fa8c16',
    category: 'trigger',
    version: '1.0.0',
    author: 'FlowCustomV1',
    tags: ['builtin', 'trigger', 'webhook', 'http'],
    inputs: [],
    outputs: [
      { id: 'output', name: '输出', type: 'control', description: '触发器输出' }
    ],
    properties: [
      { id: 'name', name: '节点名称', type: 'string', defaultValue: 'Webhook触发', required: true },
      { id: 'description', name: '描述', type: 'textarea', defaultValue: '' },
      { id: 'webhookUrl', name: 'Webhook URL', type: 'string', defaultValue: '/webhook/trigger', required: true },
      { id: 'method', name: 'HTTP方法', type: 'select', defaultValue: 'POST', 
        options: [
          { label: 'GET', value: 'GET' },
          { label: 'POST', value: 'POST' },
          { label: 'PUT', value: 'PUT' },
          { label: 'DELETE', value: 'DELETE' }
        ]
      }
    ]
  }
];

// 动作节点定义
const actionNodes: NodeDefinition[] = [
  {
    type: 'HttpRequest',
    name: 'HTTP请求',
    displayName: 'HTTP请求',
    description: '发送HTTP请求到指定URL',
    icon: '🌍',
    color: '#722ed1',
    category: 'action',
    version: '1.0.0',
    author: 'FlowCustomV1',
    tags: ['builtin', 'action', 'http', 'api'],
    inputs: [
      { id: 'input', name: '输入', type: 'control', description: 'HTTP请求输入' }
    ],
    outputs: [
      { id: 'output', name: '输出', type: 'control', description: 'HTTP请求输出' }
    ],
    properties: [
      { id: 'name', name: '节点名称', type: 'string', defaultValue: 'HTTP请求', required: true },
      { id: 'description', name: '描述', type: 'textarea', defaultValue: '' },
      { id: 'url', name: 'URL', type: 'string', defaultValue: 'https://httpbin.org/get', required: true },
      { id: 'method', name: 'HTTP方法', type: 'select', defaultValue: 'GET',
        options: [
          { label: 'GET', value: 'GET' },
          { label: 'POST', value: 'POST' },
          { label: 'PUT', value: 'PUT' },
          { label: 'DELETE', value: 'DELETE' },
          { label: 'PATCH', value: 'PATCH' }
        ]
      },
      { id: 'headers', name: '请求头', type: 'json', defaultValue: {} },
      { id: 'body', name: '请求体', type: 'textarea', defaultValue: '' },
      { id: 'timeout', name: '超时时间(秒)', type: 'number', defaultValue: 30 }
    ]
  },
  {
    type: 'DataProcessor',
    name: '数据处理器',
    displayName: '数据处理',
    description: '处理和转换数据',
    icon: '🔄',
    color: '#722ed1',
    category: 'action',
    version: '1.0.0',
    author: 'FlowCustomV1',
    tags: ['builtin', 'action', 'data', 'processing'],
    inputs: [
      { id: 'input', name: '输入', type: 'control', description: '数据处理输入' }
    ],
    outputs: [
      { id: 'output', name: '输出', type: 'control', description: '数据处理输出' }
    ],
    properties: [
      { id: 'name', name: '节点名称', type: 'string', defaultValue: '数据处理', required: true },
      { id: 'description', name: '描述', type: 'textarea', defaultValue: '' },
      { id: 'processingType', name: '处理类型', type: 'select', defaultValue: 'transform',
        options: [
          { label: '数据转换', value: 'transform' },
          { label: '数据验证', value: 'validate' },
          { label: '数据计算', value: 'calculate' },
          { label: '数据过滤', value: 'filter' }
        ]
      },
      { id: 'processingScript', name: '处理脚本', type: 'textarea', defaultValue: '' }
    ]
  },
  {
    type: 'NotificationSender',
    name: '通知发送器',
    displayName: '发送通知',
    description: '发送各种类型的通知',
    icon: '📢',
    color: '#722ed1',
    category: 'action',
    version: '1.0.0',
    author: 'FlowCustomV1',
    tags: ['builtin', 'action', 'notification', 'alert'],
    inputs: [
      { id: 'input', name: '输入', type: 'control', description: '通知发送输入' }
    ],
    outputs: [
      { id: 'output', name: '输出', type: 'control', description: '通知发送输出' }
    ],
    properties: [
      { id: 'name', name: '节点名称', type: 'string', defaultValue: '发送通知', required: true },
      { id: 'description', name: '描述', type: 'textarea', defaultValue: '' },
      { id: 'notificationType', name: '通知类型', type: 'select', defaultValue: 'email',
        options: [
          { label: '邮件', value: 'email' },
          { label: '短信', value: 'sms' },
          { label: '微信', value: 'wechat' },
          { label: '钉钉', value: 'dingtalk' }
        ]
      },
      { id: 'recipient', name: '接收者', type: 'string', defaultValue: '', required: true },
      { id: 'subject', name: '主题', type: 'string', defaultValue: '' },
      { id: 'message', name: '消息内容', type: 'textarea', defaultValue: '', required: true }
    ]
  }
];

// 控制流节点定义
const controlFlowNodes: NodeDefinition[] = [
  {
    type: 'IfCondition',
    name: '条件分支',
    displayName: '条件判断',
    description: '根据条件执行不同的分支逻辑',
    icon: '🔀',
    color: '#13c2c2',
    category: 'control',
    version: '1.0.0',
    author: 'FlowCustomV1',
    tags: ['builtin', 'control', 'condition', 'branch'],
    inputs: [
      { id: 'input', name: '输入', type: 'control', description: '条件判断输入' }
    ],
    outputs: [
      { id: 'true', name: '真', type: 'control', description: '条件为真时的输出' },
      { id: 'false', name: '假', type: 'control', description: '条件为假时的输出' }
    ],
    properties: [
      { id: 'name', name: '节点名称', type: 'string', defaultValue: '条件判断', required: true },
      { id: 'description', name: '描述', type: 'textarea', defaultValue: '' },
      { id: 'condition', name: '条件表达式', type: 'string', defaultValue: 'true', required: true },
      { id: 'leftValue', name: '左值', type: 'string', defaultValue: '' },
      { id: 'operator', name: '操作符', type: 'select', defaultValue: '==',
        options: [
          { label: '等于 (==)', value: '==' },
          { label: '不等于 (!=)', value: '!=' },
          { label: '大于 (>)', value: '>' },
          { label: '小于 (<)', value: '<' },
          { label: '大于等于 (>=)', value: '>=' },
          { label: '小于等于 (<=)', value: '<=' }
        ]
      },
      { id: 'rightValue', name: '右值', type: 'string', defaultValue: '' }
    ]
  },
  {
    type: 'ForLoop',
    name: '循环控制',
    displayName: '循环',
    description: '循环执行指定次数或条件',
    icon: '🔄',
    color: '#13c2c2',
    category: 'control',
    version: '1.0.0',
    author: 'FlowCustomV1',
    tags: ['builtin', 'control', 'loop', 'iteration'],
    inputs: [
      { id: 'input', name: '输入', type: 'control', description: '循环控制输入' }
    ],
    outputs: [
      { id: 'loop', name: '循环体', type: 'control', description: '循环体输出' },
      { id: 'output', name: '输出', type: 'control', description: '循环完成输出' }
    ],
    properties: [
      { id: 'name', name: '节点名称', type: 'string', defaultValue: '循环', required: true },
      { id: 'description', name: '描述', type: 'textarea', defaultValue: '' },
      { id: 'loopType', name: '循环类型', type: 'select', defaultValue: 'count',
        options: [
          { label: '计数循环', value: 'count' },
          { label: '条件循环', value: 'condition' },
          { label: '数组循环', value: 'array' }
        ]
      },
      { id: 'maxIterations', name: '最大迭代次数', type: 'number', defaultValue: 10 },
      { id: 'loopCondition', name: '循环条件', type: 'string', defaultValue: '' }
    ]
  },
  {
    type: 'ParallelExecution',
    name: '并行执行',
    displayName: '并行',
    description: '并行执行多个分支',
    icon: '⚡',
    color: '#13c2c2',
    category: 'control',
    version: '1.0.0',
    author: 'FlowCustomV1',
    tags: ['builtin', 'control', 'parallel', 'concurrent'],
    inputs: [
      { id: 'input', name: '输入', type: 'control', description: '并行执行输入' }
    ],
    outputs: [
      { id: 'branch1', name: '分支1', type: 'control', description: '并行分支1' },
      { id: 'branch2', name: '分支2', type: 'control', description: '并行分支2' },
      { id: 'branch3', name: '分支3', type: 'control', description: '并行分支3' },
      { id: 'output', name: '输出', type: 'control', description: '并行完成输出' }
    ],
    properties: [
      { id: 'name', name: '节点名称', type: 'string', defaultValue: '并行执行', required: true },
      { id: 'description', name: '描述', type: 'textarea', defaultValue: '' },
      { id: 'waitForAll', name: '等待所有分支', type: 'boolean', defaultValue: true },
      { id: 'maxConcurrency', name: '最大并发数', type: 'number', defaultValue: 3 },
      { id: 'timeoutSeconds', name: '超时时间(秒)', type: 'number', defaultValue: 300 }
    ]
  }
];

// 数据转换节点定义
const dataTransformNodes: NodeDefinition[] = [
  {
    type: 'DataMapper',
    name: '数据映射器',
    displayName: '数据映射',
    description: '将输入数据映射到指定的输出格式',
    icon: '🗂️',
    color: '#eb2f96',
    category: 'transform',
    version: '1.0.0',
    author: 'FlowCustomV1',
    tags: ['builtin', 'transform', 'mapping', 'data'],
    inputs: [
      { id: 'input', name: '输入', type: 'control', description: '数据映射输入' }
    ],
    outputs: [
      { id: 'output', name: '输出', type: 'control', description: '数据映射输出' }
    ],
    properties: [
      { id: 'name', name: '节点名称', type: 'string', defaultValue: '数据映射', required: true },
      { id: 'description', name: '描述', type: 'textarea', defaultValue: '' },
      { id: 'mappingRules', name: '映射规则', type: 'json', defaultValue: {} },
      { id: 'outputFormat', name: '输出格式', type: 'select', defaultValue: 'json',
        options: [
          { label: 'JSON', value: 'json' },
          { label: 'XML', value: 'xml' },
          { label: 'CSV', value: 'csv' },
          { label: '自定义', value: 'custom' }
        ]
      }
    ]
  },
  {
    type: 'DataFilter',
    name: '数据过滤器',
    displayName: '数据过滤',
    description: '根据条件过滤数据',
    icon: '🔍',
    color: '#eb2f96',
    category: 'transform',
    version: '1.0.0',
    author: 'FlowCustomV1',
    tags: ['builtin', 'transform', 'filter', 'data'],
    inputs: [
      { id: 'input', name: '输入', type: 'control', description: '数据过滤输入' }
    ],
    outputs: [
      { id: 'output', name: '输出', type: 'control', description: '数据过滤输出' }
    ],
    properties: [
      { id: 'name', name: '节点名称', type: 'string', defaultValue: '数据过滤', required: true },
      { id: 'description', name: '描述', type: 'textarea', defaultValue: '' },
      { id: 'filterCondition', name: '过滤条件', type: 'string', defaultValue: '', required: true },
      { id: 'filterType', name: '过滤类型', type: 'select', defaultValue: 'include',
        options: [
          { label: '包含', value: 'include' },
          { label: '排除', value: 'exclude' },
          { label: '转换', value: 'transform' }
        ]
      }
    ]
  }
];

// 外部服务节点定义
const externalServiceNodes: NodeDefinition[] = [
  {
    type: 'MySqlDatabase',
    name: 'MySQL数据库',
    displayName: 'MySQL',
    description: '连接MySQL数据库执行查询和操作',
    icon: '🗄️',
    color: '#f5222d',
    category: 'external',
    version: '1.0.0',
    author: 'FlowCustomV1',
    tags: ['builtin', 'database', 'mysql', 'external'],
    inputs: [
      { id: 'input', name: '输入', type: 'control', description: 'MySQL数据库输入' }
    ],
    outputs: [
      { id: 'output', name: '输出', type: 'control', description: 'MySQL数据库输出' }
    ],
    properties: [
      { id: 'name', name: '节点名称', type: 'string', defaultValue: 'MySQL数据库', required: true },
      { id: 'description', name: '描述', type: 'textarea', defaultValue: '' },
      { id: 'connectionString', name: '连接字符串', type: 'string',
        defaultValue: 'Server=localhost;Database=test;Uid=root;Pwd=password;', required: true },
      { id: 'operation', name: '操作类型', type: 'select', defaultValue: 'SELECT',
        options: [
          { label: '查询 (SELECT)', value: 'SELECT' },
          { label: '插入 (INSERT)', value: 'INSERT' },
          { label: '更新 (UPDATE)', value: 'UPDATE' },
          { label: '删除 (DELETE)', value: 'DELETE' }
        ]
      },
      { id: 'sqlQuery', name: 'SQL查询', type: 'textarea', defaultValue: 'SELECT 1', required: true }
    ]
  },
  {
    type: 'NatsMessage',
    name: 'NATS消息队列',
    displayName: 'NATS消息',
    description: '发送和接收NATS消息队列消息',
    icon: '📨',
    color: '#f5222d',
    category: 'external',
    version: '1.0.0',
    author: 'FlowCustomV1',
    tags: ['builtin', 'messaging', 'nats', 'external'],
    inputs: [
      { id: 'input', name: '输入', type: 'control', description: 'NATS消息输入' }
    ],
    outputs: [
      { id: 'output', name: '输出', type: 'control', description: 'NATS消息输出' }
    ],
    properties: [
      { id: 'name', name: '节点名称', type: 'string', defaultValue: 'NATS消息', required: true },
      { id: 'description', name: '描述', type: 'textarea', defaultValue: '' },
      { id: 'operation', name: '操作类型', type: 'select', defaultValue: 'publish',
        options: [
          { label: '发布消息', value: 'publish' },
          { label: '订阅消息', value: 'subscribe' },
          { label: '请求响应', value: 'request' }
        ]
      },
      { id: 'subject', name: '主题', type: 'string', defaultValue: 'test.subject', required: true },
      { id: 'message', name: '消息内容', type: 'textarea', defaultValue: 'Hello NATS' }
    ]
  },
  {
    type: 'RestApiCall',
    name: 'REST API调用',
    displayName: 'REST API',
    description: '调用REST API接口',
    icon: '🌐',
    color: '#f5222d',
    category: 'external',
    version: '1.0.0',
    author: 'FlowCustomV1',
    tags: ['builtin', 'api', 'rest', 'external'],
    inputs: [
      { id: 'input', name: '输入', type: 'control', description: 'REST API输入' }
    ],
    outputs: [
      { id: 'output', name: '输出', type: 'control', description: 'REST API输出' }
    ],
    properties: [
      { id: 'name', name: '节点名称', type: 'string', defaultValue: 'REST API', required: true },
      { id: 'description', name: '描述', type: 'textarea', defaultValue: '' },
      { id: 'baseUrl', name: '基础URL', type: 'string', defaultValue: 'https://api.example.com', required: true },
      { id: 'endpoint', name: '端点', type: 'string', defaultValue: '/users', required: true },
      { id: 'method', name: 'HTTP方法', type: 'select', defaultValue: 'GET',
        options: [
          { label: 'GET', value: 'GET' },
          { label: 'POST', value: 'POST' },
          { label: 'PUT', value: 'PUT' },
          { label: 'DELETE', value: 'DELETE' },
          { label: 'PATCH', value: 'PATCH' }
        ]
      },
      { id: 'authentication', name: '认证方式', type: 'select', defaultValue: 'none',
        options: [
          { label: '无认证', value: 'none' },
          { label: 'Bearer Token', value: 'bearer' },
          { label: 'Basic Auth', value: 'basic' },
          { label: 'API Key', value: 'apikey' }
        ]
      }
    ]
  }
];

// 节点分类定义
export const nodeCategories: NodeCategory[] = [
  {
    id: 'basic',
    name: '基础控制',
    description: '基础的工作流控制节点',
    icon: '⚙️',
    color: '#1890ff',
    nodes: basicControlNodes
  },
  {
    id: 'trigger',
    name: '触发器',
    description: '工作流触发器节点',
    icon: '⚡',
    color: '#fa8c16',
    nodes: triggerNodes
  },
  {
    id: 'action',
    name: '动作',
    description: '执行具体动作的节点',
    icon: '🎯',
    color: '#722ed1',
    nodes: actionNodes
  },
  {
    id: 'control',
    name: '控制流',
    description: '工作流控制流节点',
    icon: '🔀',
    color: '#13c2c2',
    nodes: controlFlowNodes
  },
  {
    id: 'transform',
    name: '数据转换',
    description: '数据转换和处理节点',
    icon: '🔄',
    color: '#eb2f96',
    nodes: dataTransformNodes
  },
  {
    id: 'external',
    name: '外部服务',
    description: '外部服务集成节点',
    icon: '🔌',
    color: '#f5222d',
    nodes: externalServiceNodes
  }
];

// 获取所有节点定义
export const getAllNodeDefinitions = (): NodeDefinition[] => {
  return nodeCategories.flatMap(category => category.nodes);
};

// 根据类型获取节点定义
export const getNodeDefinition = (type: string): NodeDefinition | undefined => {
  return getAllNodeDefinitions().find(node => node.type === type);
};
