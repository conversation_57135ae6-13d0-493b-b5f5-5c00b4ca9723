# 系统性重命名执行计划

## 🎯 目标
解决当前131个编译错误，通过系统性重命名文件和类来统一命名规范。

## 📋 重命名映射表

### 文件重命名 (需要重新创建文件)
```
Cluster模型文件:
UnifiedClusterModels.cs → ClusterModels.cs
UnifiedHealthStatus.cs → HealthStatus.cs  
UnifiedNetworkInfo.cs → NetworkInfo.cs
UnifiedNodeCapabilities.cs → NodeCapabilities.cs
UnifiedNodeInfo.cs → NodeInfo.cs
UnifiedNodeLoad.cs → NodeLoad.cs
UnifiedNodeMode.cs → NodeMode.cs
UnifiedNodeStatus.cs → NodeStatus.cs
UnifiedTimestamps.cs → Timestamps.cs

Messages文件:
UnifiedClusterMessage.cs → ClusterMessage.cs
UnifiedNodeHeartbeatMessage.cs → NodeHeartbeatMessage.cs

Services文件:
UnifiedClusterService.cs → ClusterService.cs
IUnifiedClusterService.cs → IClusterService.cs
```

### 类名重命名 (在文件内容中)
```
UnifiedDiscoveryQuery → NodeDiscoveryQuery
UnifiedExecutionRequirements → NodeExecutionRequirements  
UnifiedClusterStats → ClusterStats
UnifiedClusterHealthCheckResult → ClusterHealthCheckResult
UnifiedClusterMessage → ClusterMessage
UnifiedNodeHeartbeatMessage → NodeHeartbeatMessage
UnifiedClusterService → ClusterService
IUnifiedClusterService → IClusterService
```

## 🚀 执行策略

### Step 1: 重新创建核心模型文件
1. 基于现有内容创建新文件名的文件
2. 同时更新文件内的类名
3. 删除旧文件

### Step 2: 更新所有引用
1. 更新using语句
2. 更新类型引用
3. 更新属性名匹配

### Step 3: 验证编译
1. 每完成一组文件就编译验证
2. 逐步解决编译错误

## ⚠️ 注意事项
- 保持属性名一致性 (NodeName vs DisplayName)
- 确保所有引用都更新
- 文件重命名需要删除旧文件，创建新文件

## 📊 当前状态 (2025-08-18)

### 🎯 已完成的工作
1. **核心类重命名**:
   - ✅ `NodeInfo`, `NodeStatus`, `NodeMode` 类定义完成
   - ✅ `ClusterModels.cs` 创建完成，包含所有重命名的类
   - ✅ `NetworkInfo.cs` 创建完成

2. **部分引用更新**:
   - ✅ 修复了服务接口中的类名引用
   - ✅ 修复了部分服务实现中的引用
   - ✅ 修复了关键的属性名问题 (`DisplayName` → `NodeName`)

### 🚨 当前挑战 (123个编译错误)
主要错误类型：
1. **属性不匹配** (60%): 新类定义中缺少旧代码使用的属性
2. **类名引用未更新** (30%): 还有很多地方使用旧类名
3. **结构不匹配** (10%): 类结构发生了变化

### 🎯 建议的解决方案

#### 选项A: 渐进式重构 ⭐ 推荐
1. **保持向后兼容**: 在新类中添加缺失的属性
2. **逐步迁移**: 一个文件一个文件地更新引用
3. **分阶段验证**: 每完成一部分就编译验证

#### 选项B: 快速完成重构
1. **批量替换**: 使用脚本批量替换所有引用
2. **一次性修复**: 同时解决所有属性不匹配问题
3. **风险较高**: 可能引入新的问题

### 📋 下一步行动计划

#### 立即行动 (解决最关键的问题)
1. **补充缺失属性**: 在新类中添加旧代码需要的属性
2. **修复核心引用**: 更新最关键的类名引用
3. **验证编译**: 确保错误数量持续减少

#### 中期目标
1. **完成所有文件重命名**: 删除所有 `Unified*` 文件
2. **统一属性命名**: 确保所有属性名一致
3. **更新文档**: 同步更新相关文档

## 💡 经验教训
1. **重构规模控制**: 大规模重构需要更细致的规划
2. **向后兼容重要**: 保持API稳定性很关键
3. **分步验证必要**: 每一步都要验证编译状态
