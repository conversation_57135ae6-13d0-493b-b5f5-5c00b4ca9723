#!/usr/bin/env python3
"""
FlowCustomV1 集成测试运行器

用于运行FlowCustomV1项目的集成测试，支持不同的测试环境和配置选项
"""

import argparse
import subprocess
import sys
import time
import json
import requests
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Optional, Tuple

class Colors:
    """控制台颜色常量"""
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    RED = '\033[91m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    RESET = '\033[0m'

class TestRunner:
    """集成测试运行器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.test_project = self.project_root / "tests" / "FlowCustomV1.Integration.Tests"
        self.results_dir = self.project_root / "TestResults"
        
    def log(self, message: str, level: str = "INFO") -> None:
        """输出日志信息"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        color_map = {
            "SUCCESS": Colors.GREEN + "✅",
            "WARNING": Colors.YELLOW + "⚠️",
            "ERROR": Colors.RED + "❌",
            "INFO": Colors.CYAN + "ℹ️"
        }
        
        color = color_map.get(level, Colors.WHITE + "•")
        print(f"[{timestamp}] {color} {message}{Colors.RESET}")
    
    def run_command(self, command: str, cwd: Optional[Path] = None, timeout: int = 300) -> Tuple[bool, str, str]:
        """执行命令并返回结果"""
        try:
            result = subprocess.run(
                command,
                shell=True,
                cwd=cwd or self.project_root,
                capture_output=True,
                text=True,
                timeout=timeout
            )
            return result.returncode == 0, result.stdout, result.stderr
        except subprocess.TimeoutExpired:
            return False, "", f"命令执行超时 ({timeout}秒)"
        except Exception as e:
            return False, "", str(e)
    
    def check_infrastructure(self) -> bool:
        """检查基础设施状态"""
        self.log("检查基础设施状态...", "INFO")
        
        # 检查Docker容器状态
        required_containers = [
            "mysql-test",
            "nats-test-server-1", 
            "nats-test-server-2",
            "nats-test-server-3"
        ]
        
        for container in required_containers:
            success, stdout, stderr = self.run_command(f'docker ps --filter "name={container}" --format "{{{{.Status}}}}"')
            if not success or "Up" not in stdout:
                self.log(f"容器 {container} 未运行", "ERROR")
                return False
            self.log(f"容器 {container} 运行正常", "SUCCESS")
        
        # 检查MySQL连接
        success, stdout, stderr = self.run_command(
            'docker exec mysql-test mysql -u flowcustom -pTestPassword123! -e "SELECT 1" 2>/dev/null'
        )
        if success:
            self.log("MySQL连接测试通过", "SUCCESS")
        else:
            self.log("MySQL连接测试失败", "ERROR")
            return False
        
        # 检查NATS集群状态
        try:
            response = requests.get("http://localhost:28222/healthz", timeout=5)
            if response.status_code == 200:
                self.log("NATS集群健康检查通过", "SUCCESS")
            else:
                self.log(f"NATS集群健康检查失败: HTTP {response.status_code}", "WARNING")
        except Exception as e:
            self.log(f"NATS集群健康检查异常: {str(e)}", "WARNING")
        
        return True
    
    def get_test_filter(self, category: str) -> str:
        """构建测试过滤器"""
        filter_map = {
            "WorkflowManagement": "FullyQualifiedName~WorkflowManagementTests",
            "ClusterManagement": "FullyQualifiedName~ClusterManagementTests", 
            "NodeServices": "FullyQualifiedName~SpecializedNodeServicesTests",
            "Collaboration": "FullyQualifiedName~CollaborationFeaturesTests",
            "All": ""
        }
        return filter_map.get(category, "")
    
    def run_tests(self, environment: str, test_category: str, parallel: bool, verbose: bool) -> bool:
        """运行集成测试"""
        self.log("开始运行集成测试", "INFO")
        self.log(f"环境: {environment}", "INFO")
        self.log(f"测试类别: {test_category}", "INFO")
        
        # 构建测试命令
        command_parts = ["dotnet", "test", str(self.test_project)]
        
        # 添加过滤器
        test_filter = self.get_test_filter(test_category)
        if test_filter:
            command_parts.extend(["--filter", f'"{test_filter}"'])
        
        # 添加日志选项
        if verbose:
            command_parts.extend(["--logger", "console;verbosity=detailed"])
        else:
            command_parts.extend(["--logger", "console;verbosity=normal"])
        
        # 添加并行选项
        if parallel:
            command_parts.append("--parallel")
        
        # 添加结果输出
        self.results_dir.mkdir(exist_ok=True)
        command_parts.extend([
            "--logger", "trx",
            "--results-directory", str(self.results_dir)
        ])
        
        command = " ".join(command_parts)
        self.log(f"执行命令: {command}", "INFO")
        
        # 设置环境变量
        import os
        os.environ["ASPNETCORE_ENVIRONMENT"] = environment
        os.environ["DOTNET_ENVIRONMENT"] = environment
        
        # 执行测试
        start_time = time.time()
        success, stdout, stderr = self.run_command(command, timeout=600)  # 10分钟超时
        end_time = time.time()
        duration = end_time - start_time
        
        if success:
            self.log(f"集成测试执行成功 (耗时: {duration:.2f}秒)", "SUCCESS")
            return True
        else:
            self.log(f"集成测试执行失败", "ERROR")
            if stderr:
                self.log(f"错误信息: {stderr}", "ERROR")
            return False
    
    def generate_report(self) -> None:
        """生成测试报告"""
        self.log("生成测试报告...", "INFO")
        
        if self.results_dir.exists():
            trx_files = list(self.results_dir.glob("*.trx"))
            if trx_files:
                latest_trx = max(trx_files, key=lambda f: f.stat().st_mtime)
                self.log(f"最新测试结果文件: {latest_trx}", "INFO")
                
                # 这里可以添加更多报告生成逻辑
                # 例如解析TRX文件、生成HTML报告等
    
    def show_help(self) -> None:
        """显示帮助信息"""
        help_text = f"""
{Colors.WHITE}FlowCustomV1 集成测试运行器{Colors.RESET}

{Colors.CYAN}用法:{Colors.RESET}
    python run_integration_tests.py [选项]

{Colors.CYAN}选项:{Colors.RESET}
    -e, --environment <env>     测试环境 (Development|Testing|Production)
    -c, --category <cat>        测试类别 (All|WorkflowManagement|ClusterManagement|NodeServices|Collaboration)
    -p, --parallel             并行运行测试
    -v, --verbose               显示详细输出
    -s, --skip-infrastructure   跳过基础设施检查
    -h, --help                  显示此帮助信息

{Colors.CYAN}示例:{Colors.RESET}
    python run_integration_tests.py -e Testing -c All -v
    python run_integration_tests.py -e Development -c WorkflowManagement
    python run_integration_tests.py -s -p

{Colors.CYAN}测试类别说明:{Colors.RESET}
    WorkflowManagement    - 工作流管理功能测试 (FR-WM)
    ClusterManagement     - 分布式集群管理测试 (FR-CM)
    NodeServices          - 专业化节点服务测试 (FR-NS)
    Collaboration         - 协作功能测试 (FR-CF)
    All                   - 运行所有测试类别
        """
        print(help_text)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="FlowCustomV1 集成测试运行器", add_help=False)
    parser.add_argument("-e", "--environment", 
                       choices=["Development", "Testing", "Production"],
                       default="Testing",
                       help="测试环境")
    parser.add_argument("-c", "--category",
                       choices=["All", "WorkflowManagement", "ClusterManagement", "NodeServices", "Collaboration"],
                       default="All",
                       help="测试类别")
    parser.add_argument("-p", "--parallel", action="store_true", help="并行运行测试")
    parser.add_argument("-v", "--verbose", action="store_true", help="显示详细输出")
    parser.add_argument("-s", "--skip-infrastructure", action="store_true", help="跳过基础设施检查")
    parser.add_argument("-h", "--help", action="store_true", help="显示帮助信息")
    
    args = parser.parse_args()
    
    runner = TestRunner()
    
    if args.help:
        runner.show_help()
        return 0
    
    runner.log("=== FlowCustomV1 集成测试运行器 ===", "INFO")
    runner.log(f"开始时间: {datetime.now()}", "INFO")
    
    try:
        # 检查基础设施
        if not args.skip_infrastructure:
            if not runner.check_infrastructure():
                runner.log("基础设施检查失败，请确保测试环境正常运行", "ERROR")
                return 1
        else:
            runner.log("跳过基础设施检查", "WARNING")
        
        # 运行测试
        test_success = runner.run_tests(
            args.environment, 
            args.category, 
            args.parallel, 
            args.verbose
        )
        
        # 生成报告
        runner.generate_report()
        
        # 输出结果
        if test_success:
            runner.log("=== 集成测试完成 - 成功 ===", "SUCCESS")
            return 0
        else:
            runner.log("=== 集成测试完成 - 失败 ===", "ERROR")
            return 1
            
    except KeyboardInterrupt:
        runner.log("测试被用户中断", "WARNING")
        return 1
    except Exception as e:
        runner.log(f"执行过程中发生异常: {str(e)}", "ERROR")
        return 1
    finally:
        runner.log(f"结束时间: {datetime.now()}", "INFO")

if __name__ == "__main__":
    sys.exit(main())
