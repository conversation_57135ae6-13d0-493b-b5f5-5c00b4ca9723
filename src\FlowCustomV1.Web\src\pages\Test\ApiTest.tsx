import React, { useState } from 'react';
import { <PERSON><PERSON>, Card, Typography, Space, message } from 'antd';
import { workflowApi } from '@/services/workflow';

const { Title, Paragraph } = Typography;

const ApiTest: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);

  const testGetWorkflows = async () => {
    try {
      setLoading(true);
      console.log('开始测试 API...');
      
      const response = await workflowApi.getWorkflows();
      console.log('API 响应:', response);
      
      setResult(response);
      message.success('API 测试成功');
    } catch (error) {
      console.error('API 测试失败:', error);
      setResult({ error: (error as Error).message, details: error });
      message.error('API 测试失败');
    } finally {
      setLoading(false);
    }
  };

  const testDirectFetch = async () => {
    try {
      setLoading(true);
      console.log('开始测试直接 fetch...');
      
      const response = await fetch('/api/workflows');
      const data = await response.json();
      
      console.log('直接 fetch 响应:', data);
      setResult({ direct: data, status: response.status });
      message.success('直接 fetch 测试成功');
    } catch (error) {
      console.error('直接 fetch 测试失败:', error);
      setResult({ error: (error as Error).message });
      message.error('直接 fetch 测试失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-6">
      <Card>
        <Title level={2}>API 连接测试</Title>
        
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <div>
            <Paragraph>
              测试前端与后端 API 的连接状态
            </Paragraph>
            
            <Space>
              <Button 
                type="primary" 
                loading={loading}
                onClick={testGetWorkflows}
              >
                测试工作流 API
              </Button>
              
              <Button 
                loading={loading}
                onClick={testDirectFetch}
              >
                测试直接 Fetch
              </Button>
            </Space>
          </div>
          
          {result && (
            <Card title="测试结果" size="small">
              <pre style={{ 
                background: '#f5f5f5', 
                padding: '12px', 
                borderRadius: '4px',
                overflow: 'auto',
                maxHeight: '400px'
              }}>
                {JSON.stringify(result, null, 2)}
              </pre>
            </Card>
          )}
        </Space>
      </Card>
    </div>
  );
};

export default ApiTest;
