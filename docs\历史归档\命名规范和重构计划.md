# FlowCustomV1 命名规范和重构计划

## 📋 问题分析

### 当前命名问题
1. **过度复杂的前缀**: `UnifiedNodeInfo` → `NodeInfo`
2. **不直观的属性名**: `DisplayName` → `NodeName`
3. **过度参考旧设计文档**: 导致使用了不合适的命名
4. **编译错误大量存在**: 144个编译错误主要由命名不匹配导致

### 根本原因
- 过度依赖设计文档中的旧系统命名
- 没有建立清晰的命名规范
- 类名和属性名过于复杂

## 🎯 新命名规范

### 类名规范
```csharp
// ❌ 旧命名 (过于复杂)
UnifiedNodeInfo
UnifiedNodeStatus
UnifiedNodeCapabilities
UnifiedClusterService

// ✅ 新命名 (简洁直观)
NodeInfo
NodeStatus
NodeCapabilities
ClusterService
```

### 属性名规范
```csharp
// ❌ 旧命名 (不直观)
DisplayName
NetworkInfo
HealthStatus
RegisteredAt
LastSeenAt

// ✅ 新命名 (直观易懂)
NodeName
Network
Health
CreatedAt
LastActiveAt
```

### 枚举值规范
```csharp
// ❌ 旧命名 (不存在的值)
UnifiedNodeStatus.Online

// ✅ 新命名 (实际存在的值)
NodeStatus.Healthy
```

## 🔄 重构计划

### Phase 1: 核心类重命名
1. **集群相关类**
   - `UnifiedNodeInfo` → `NodeInfo`
   - `UnifiedNodeStatus` → `NodeStatus`
   - `UnifiedNodeCapabilities` → `NodeCapabilities`
   - `UnifiedNodeLoad` → `NodeLoad`
   - `UnifiedHealthStatus` → `HealthStatus`
   - `UnifiedTimestamps` → `Timestamps`
   - `UnifiedNetworkInfo` → `NetworkInfo`
   - `UnifiedClusterService` → `ClusterService`

2. **消息相关类**
   - `UnifiedClusterMessage` → `ClusterMessage`
   - `UnifiedNodeHeartbeatMessage` → `NodeHeartbeatMessage`

### Phase 2: 属性名重命名
1. **NodeInfo类属性**
   - `DisplayName` → `NodeName`
   - `NetworkInfo` → `Network`
   - `HealthStatus` → `Health`

2. **Timestamps类属性**
   - `RegisteredAt` → `CreatedAt`
   - `LastSeenAt` → `LastActiveAt`

3. **枚举值修正**
   - `UnifiedNodeStatus.Online` → `NodeStatus.Healthy`

### Phase 3: 服务类重命名
1. **接口重命名**
   - `IUnifiedClusterService` → `IClusterService`

2. **实现类重命名**
   - `UnifiedClusterService` → `ClusterService`

## 🛠️ 重构执行步骤

### Step 1: 重命名文件和类
1. 重命名所有 `Unified*` 类文件
2. 更新类名定义
3. 更新命名空间引用

### Step 2: 更新属性名
1. 修改属性定义
2. 更新所有使用这些属性的代码
3. 修正枚举值引用

### Step 3: 更新服务注册
1. 修改依赖注入配置
2. 更新接口引用
3. 修正服务实现

### Step 4: 验证和测试
1. 确保编译通过
2. 验证功能完整性
3. 更新文档

## 📝 重构后的代码示例

### 重构前
```csharp
public class UnifiedNodeInfo
{
    public string DisplayName { get; set; }
    public UnifiedNetworkInfo NetworkInfo { get; set; }
    public UnifiedHealthStatus HealthStatus { get; set; }
    public UnifiedTimestamps Timestamps { get; set; }
}

public class UnifiedTimestamps
{
    public DateTime RegisteredAt { get; set; }
    public DateTime LastSeenAt { get; set; }
}
```

### 重构后
```csharp
public class NodeInfo
{
    public string NodeName { get; set; }
    public NetworkInfo Network { get; set; }
    public HealthStatus Health { get; set; }
    public Timestamps Timestamps { get; set; }
}

public class Timestamps
{
    public DateTime CreatedAt { get; set; }
    public DateTime LastActiveAt { get; set; }
}
```

## ⚠️ 重构注意事项

### 保持向后兼容
- 在重构过程中保持接口稳定
- 分阶段进行，避免大规模破坏性更改

### 文档同步更新
- 同步更新所有相关文档
- 更新API文档和示例代码

### 测试覆盖
- 确保重构后功能不受影响
- 添加必要的单元测试

## 🎯 预期效果

### 编译错误解决
- 解决当前144个编译错误
- 确保代码编译通过

### 代码可读性提升
- 类名更简洁直观
- 属性名更符合直觉
- 减少认知负担

### 维护性改善
- 降低新开发者的学习成本
- 提高代码的可维护性
- 符合.NET命名约定

---

**创建时间**: 2025-08-18
**状态**: 待执行
**优先级**: 高 (解决编译问题)
