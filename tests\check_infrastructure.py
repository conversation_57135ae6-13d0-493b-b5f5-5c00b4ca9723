#!/usr/bin/env python3
"""
简单的基础设施检查脚本
"""

import subprocess
import requests
import time

def run_command(command):
    """执行命令并返回结果"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=10)
        return result.returncode == 0, result.stdout.strip(), result.stderr.strip()
    except Exception as e:
        return False, "", str(e)

def check_docker_containers():
    """检查Docker容器状态"""
    print("🔍 检查Docker容器状态...")
    
    containers = ["mysql-test", "nats-test-server-1", "nats-test-server-2", "nats-test-server-3"]
    
    for container in containers:
        success, stdout, stderr = run_command(f'docker ps --filter "name={container}" --format "{{{{.Status}}}}"')
        if success and "Up" in stdout:
            print(f"✅ {container}: 运行中")
        else:
            print(f"❌ {container}: 未运行")
            return False
    
    return True

def check_mysql():
    """检查MySQL连接"""
    print("🔍 检查MySQL连接...")
    
    success, stdout, stderr = run_command(
        'docker exec mysql-test mysql -u root -pFlowCustomRoot@2025 -e "SELECT 1" 2>nul'
    )
    
    if success:
        print("✅ MySQL连接正常")
        return True
    else:
        print(f"❌ MySQL连接失败: {stderr}")
        return False

def check_nats():
    """检查NATS集群"""
    print("🔍 检查NATS集群...")
    
    try:
        response = requests.get("http://localhost:28222/healthz", timeout=5)
        if response.status_code == 200:
            print("✅ NATS集群健康检查通过")
            return True
        else:
            print(f"❌ NATS集群健康检查失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ NATS集群连接失败: {str(e)}")
        return False

def check_cpu_usage():
    """检查容器CPU使用情况"""
    print("🔍 检查容器CPU使用情况...")
    
    success, stdout, stderr = run_command('docker stats --no-stream --format "table {{.Name}}\\t{{.CPUPerc}}"')
    
    if success:
        print("📊 容器CPU使用情况:")
        lines = stdout.split('\n')
        for line in lines:
            if 'nats' in line.lower() or 'mysql' in line.lower():
                parts = line.split('\t')
                if len(parts) >= 2:
                    name = parts[0]
                    cpu = parts[1]
                    try:
                        cpu_value = float(cpu.replace('%', ''))
                        if cpu_value > 50:
                            print(f"⚠️  {name}: {cpu} (高CPU使用率)")
                        else:
                            print(f"✅ {name}: {cpu}")
                    except:
                        print(f"ℹ️  {name}: {cpu}")
        return True
    else:
        print(f"❌ 无法获取CPU使用情况: {stderr}")
        return False

def main():
    """主函数"""
    print("🚀 FlowCustomV1 基础设施检查")
    print("=" * 50)
    
    all_checks_passed = True
    
    # 检查Docker容器
    if not check_docker_containers():
        all_checks_passed = False
    
    print()
    
    # 检查MySQL
    if not check_mysql():
        all_checks_passed = False
    
    print()
    
    # 检查NATS
    if not check_nats():
        all_checks_passed = False
    
    print()
    
    # 检查CPU使用情况
    check_cpu_usage()
    
    print()
    print("=" * 50)
    
    if all_checks_passed:
        print("✅ 所有基础设施检查通过")
        return 0
    else:
        print("❌ 部分基础设施检查失败")
        return 1

if __name__ == "__main__":
    exit(main())
