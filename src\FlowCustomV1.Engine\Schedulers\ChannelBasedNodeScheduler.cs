using System.Collections.Concurrent;
using System.Threading.Channels;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using FlowCustomV1.Core.Interfaces;
using FlowCustomV1.Core.Models.Workflow;
using FlowCustomV1.Engine.Context;

namespace FlowCustomV1.Engine.Schedulers;

/// <summary>
/// 基于Channel的节点调度器实现
/// 使用Channel队列实现事件驱动的异步节点调度
/// </summary>
public class ChannelBasedNodeScheduler : INodeScheduler
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<ChannelBasedNodeScheduler> _logger;
    private readonly SchedulerOptions _options;
    
    private readonly Channel<NodeScheduleRequest> _scheduleChannel;
    private readonly ChannelWriter<NodeScheduleRequest> _scheduleWriter;
    private readonly ChannelReader<NodeScheduleRequest> _scheduleReader;
    
    private readonly SemaphoreSlim _executionSemaphore;
    private readonly ConcurrentDictionary<string, CancellationTokenSource> _activeExecutions;
    private readonly ConcurrentDictionary<string, NodeScheduleRequest> _queuedRequests;
    
    private CancellationTokenSource? _schedulerCancellationTokenSource;
    private Task? _schedulingTask;
    private Task? _delayedSchedulingTask;
    private Task? _statisticsUpdateTask;
    private Task? _timeoutCheckTask;
    
    private SchedulerState _state = SchedulerState.NotStarted;
    private readonly object _stateLock = new();
    
    /// <summary>
    /// 调度器唯一标识符
    /// </summary>
    public string SchedulerId { get; }
    
    /// <summary>
    /// 当前调度器状态
    /// </summary>
    public SchedulerState State
    {
        get
        {
            lock (_stateLock)
            {
                return _state;
            }
        }
        private set
        {
            SchedulerState oldState;
            lock (_stateLock)
            {
                oldState = _state;
                _state = value;
            }
            
            if (oldState != value)
            {
                _logger.LogInformation("Scheduler state changed: {OldState} → {NewState}", oldState, value);
                StateChanged?.Invoke(this, new SchedulerStateChangedEventArgs
                {
                    SchedulerId = SchedulerId,
                    OldState = oldState,
                    NewState = value,
                    ChangedAt = DateTime.UtcNow
                });
            }
        }
    }
    
    /// <summary>
    /// 调度器统计信息
    /// </summary>
    public SchedulerStatistics Statistics { get; } = new();
    
    /// <summary>
    /// 最大并发执行数
    /// </summary>
    public int MaxConcurrentExecutions
    {
        get => _options.MaxConcurrentExecutions;
        set
        {
            _options.MaxConcurrentExecutions = value;
            // 注意：这里不能直接修改信号量的计数，需要重新创建调度器
            _logger.LogWarning("MaxConcurrentExecutions changed to {Value}. Restart scheduler to take effect.", value);
        }
    }
    
    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="serviceProvider">服务提供者</param>
    /// <param name="logger">日志记录器</param>
    /// <param name="options">调度器选项</param>
    public ChannelBasedNodeScheduler(
        IServiceProvider serviceProvider,
        ILogger<ChannelBasedNodeScheduler> logger,
        SchedulerOptions? options = null)
    {
        _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _options = options ?? new SchedulerOptions();
        
        SchedulerId = _options.SchedulerId;
        
        // 创建Channel队列
        var channelOptions = new BoundedChannelOptions(_options.QueueCapacity)
        {
            FullMode = BoundedChannelFullMode.Wait,
            SingleReader = false,
            SingleWriter = false
        };
        
        _scheduleChannel = Channel.CreateBounded<NodeScheduleRequest>(channelOptions);
        _scheduleWriter = _scheduleChannel.Writer;
        _scheduleReader = _scheduleChannel.Reader;
        
        // 创建执行控制信号量
        _executionSemaphore = new SemaphoreSlim(_options.MaxConcurrentExecutions, _options.MaxConcurrentExecutions);
        
        // 创建并发字典
        _activeExecutions = new ConcurrentDictionary<string, CancellationTokenSource>();
        _queuedRequests = new ConcurrentDictionary<string, NodeScheduleRequest>();
        
        _logger.LogInformation("ChannelBasedNodeScheduler created with ID: {SchedulerId}, MaxConcurrent: {MaxConcurrent}, QueueCapacity: {QueueCapacity}",
            SchedulerId, _options.MaxConcurrentExecutions, _options.QueueCapacity);
    }

    /// <summary>
    /// 启动调度器
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>启动任务</returns>
    public async Task StartAsync(CancellationToken cancellationToken = default)
    {
        if (State != SchedulerState.NotStarted && State != SchedulerState.Stopped)
        {
            _logger.LogWarning("Scheduler is already started or starting. Current state: {State}", State);
            return;
        }

        State = SchedulerState.Starting;
        
        try
        {
            _schedulerCancellationTokenSource = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
            var schedulerToken = _schedulerCancellationTokenSource.Token;

            // 启动主调度任务
            _schedulingTask = SchedulingLoopAsync(schedulerToken);
            
            // 启动延迟调度任务
            _delayedSchedulingTask = DelayedSchedulingLoopAsync(schedulerToken);
            
            // 启动统计信息更新任务
            _statisticsUpdateTask = StatisticsUpdateLoopAsync(schedulerToken);
            
            // 启动超时检查任务（如果启用）
            if (_options.EnableExecutionTimeoutCheck)
            {
                _timeoutCheckTask = TimeoutCheckLoopAsync(schedulerToken);
            }

            Statistics.StartedAt = DateTime.UtcNow;
            State = SchedulerState.Running;

            _logger.LogInformation("Scheduler {SchedulerId} started successfully", SchedulerId);

            // 等待一个已完成的任务以消除async警告
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            State = SchedulerState.Error;
            _logger.LogError(ex, "Failed to start scheduler {SchedulerId}", SchedulerId);
            throw;
        }
    }

    /// <summary>
    /// 停止调度器
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>停止任务</returns>
    public async Task StopAsync(CancellationToken cancellationToken = default)
    {
        if (State == SchedulerState.NotStarted || State == SchedulerState.Stopped)
        {
            _logger.LogWarning("Scheduler is not running. Current state: {State}", State);
            return;
        }

        State = SchedulerState.Stopping;
        
        try
        {
            // 停止接受新的调度请求（检查是否已经关闭）
            if (!_scheduleWriter.TryComplete())
            {
                _logger.LogDebug("Schedule channel was already closed");
            }
            
            // 取消调度器任务
            _schedulerCancellationTokenSource?.Cancel();
            
            // 等待所有任务完成
            var tasks = new List<Task>();
            if (_schedulingTask != null) tasks.Add(_schedulingTask);
            if (_delayedSchedulingTask != null) tasks.Add(_delayedSchedulingTask);
            if (_statisticsUpdateTask != null) tasks.Add(_statisticsUpdateTask);
            if (_timeoutCheckTask != null) tasks.Add(_timeoutCheckTask);
            
            await Task.WhenAll(tasks).ConfigureAwait(false);
            
            // 取消所有活跃的执行
            foreach (var kvp in _activeExecutions)
            {
                kvp.Value.Cancel();
            }
            
            // 等待所有执行完成或超时
            var timeout = TimeSpan.FromSeconds(30);
            var waitStart = DateTime.UtcNow;
            while (_activeExecutions.Count > 0 && DateTime.UtcNow - waitStart < timeout)
            {
                await Task.Delay(100, cancellationToken).ConfigureAwait(false);
            }
            
            State = SchedulerState.Stopped;
            _logger.LogInformation("Scheduler {SchedulerId} stopped successfully", SchedulerId);
        }
        catch (Exception ex)
        {
            State = SchedulerState.Error;
            _logger.LogError(ex, "Failed to stop scheduler {SchedulerId}", SchedulerId);
            throw;
        }
    }

    /// <summary>
    /// 暂停调度器
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>暂停任务</returns>
    public async Task PauseAsync(CancellationToken cancellationToken = default)
    {
        if (State != SchedulerState.Running)
        {
            _logger.LogWarning("Scheduler is not running. Current state: {State}", State);
            return;
        }

        State = SchedulerState.Pausing;
        
        // 暂停逻辑：停止处理新请求，但不取消正在执行的任务
        // 这里可以添加更复杂的暂停逻辑
        
        State = SchedulerState.Paused;
        _logger.LogInformation("Scheduler {SchedulerId} paused", SchedulerId);
        
        await Task.CompletedTask;
    }

    /// <summary>
    /// 恢复调度器
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>恢复任务</returns>
    public async Task ResumeAsync(CancellationToken cancellationToken = default)
    {
        if (State != SchedulerState.Paused)
        {
            _logger.LogWarning("Scheduler is not paused. Current state: {State}", State);
            return;
        }

        State = SchedulerState.Running;
        _logger.LogInformation("Scheduler {SchedulerId} resumed", SchedulerId);
        
        await Task.CompletedTask;
    }

    /// <summary>
    /// 调度节点执行
    /// </summary>
    /// <param name="request">调度请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>调度任务</returns>
    public async Task ScheduleNodeAsync(NodeScheduleRequest request, CancellationToken cancellationToken = default)
    {
        if (request == null)
            throw new ArgumentNullException(nameof(request));

        if (State != SchedulerState.Running)
        {
            throw new InvalidOperationException($"Scheduler is not running. Current state: {State}");
        }

        try
        {
            // 添加到队列跟踪
            _queuedRequests.TryAdd(request.RequestId, request);
            
            // 写入调度队列
            await _scheduleWriter.WriteAsync(request, cancellationToken).ConfigureAwait(false);
            
            Statistics.TotalRequests++;
            Statistics.QueuedRequests = _queuedRequests.Count;
            
            if (_options.EnableDetailedLogging)
            {
                _logger.LogDebug("Node scheduled: {NodeId} (Request: {RequestId})", 
                    request.NodeContext.NodeId, request.RequestId);
            }
        }
        catch (Exception ex)
        {
            _queuedRequests.TryRemove(request.RequestId, out _);
            _logger.LogError(ex, "Failed to schedule node {NodeId}", request.NodeContext.NodeId);
            throw;
        }
    }

    /// <summary>
    /// 批量调度节点执行
    /// </summary>
    /// <param name="requests">调度请求列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>调度任务</returns>
    public async Task ScheduleNodesAsync(IEnumerable<NodeScheduleRequest> requests, CancellationToken cancellationToken = default)
    {
        if (requests == null)
            throw new ArgumentNullException(nameof(requests));

        var requestList = requests.ToList();
        if (requestList.Count == 0)
            return;

        var tasks = requestList.Select(request => ScheduleNodeAsync(request, cancellationToken));
        await Task.WhenAll(tasks).ConfigureAwait(false);

        _logger.LogInformation("Batch scheduled {Count} nodes", requestList.Count);
    }

    /// <summary>
    /// 取消指定节点的执行
    /// </summary>
    /// <param name="nodeId">节点ID</param>
    /// <param name="executionId">执行ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>取消任务</returns>
    public async Task CancelNodeExecutionAsync(string nodeId, string executionId, CancellationToken cancellationToken = default)
    {
        var executionKey = $"{executionId}_{nodeId}";

        if (_activeExecutions.TryRemove(executionKey, out var cancellationTokenSource))
        {
            cancellationTokenSource.Cancel();
            _logger.LogInformation("Cancelled execution for node {NodeId} in workflow {ExecutionId}", nodeId, executionId);
        }
        else
        {
            _logger.LogWarning("No active execution found for node {NodeId} in workflow {ExecutionId}", nodeId, executionId);
        }

        await Task.CompletedTask;
    }

    /// <summary>
    /// 取消指定工作流的所有节点执行
    /// </summary>
    /// <param name="executionId">工作流执行ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>取消任务</returns>
    public async Task CancelWorkflowExecutionAsync(string executionId, CancellationToken cancellationToken = default)
    {
        var cancelledCount = 0;
        var keysToRemove = _activeExecutions.Keys.Where(key => key.StartsWith($"{executionId}_")).ToList();

        foreach (var key in keysToRemove)
        {
            if (_activeExecutions.TryRemove(key, out var cancellationTokenSource))
            {
                cancellationTokenSource.Cancel();
                cancelledCount++;
            }
        }

        _logger.LogInformation("Cancelled {Count} active executions for workflow {ExecutionId}", cancelledCount, executionId);
        await Task.CompletedTask;
    }

    /// <summary>
    /// 获取当前队列中的请求数量
    /// </summary>
    /// <returns>队列中的请求数量</returns>
    public int GetQueuedRequestCount()
    {
        return _queuedRequests.Count;
    }

    /// <summary>
    /// 获取当前正在执行的节点数量
    /// </summary>
    /// <returns>正在执行的节点数量</returns>
    public int GetActiveExecutionCount()
    {
        return _activeExecutions.Count;
    }

    /// <summary>
    /// 获取指定工作流的执行状态
    /// </summary>
    /// <param name="executionId">工作流执行ID</param>
    /// <returns>执行状态信息</returns>
    public async Task<Dictionary<string, object>> GetWorkflowExecutionStatusAsync(string executionId)
    {
        var status = new Dictionary<string, object>
        {
            ["ExecutionId"] = executionId,
            ["Timestamp"] = DateTime.UtcNow,
            ["SchedulerId"] = SchedulerId
        };

        // 统计该工作流的活跃执行
        var activeNodes = _activeExecutions.Keys
            .Where(key => key.StartsWith($"{executionId}_"))
            .Select(key => key.Substring($"{executionId}_".Length))
            .ToList();

        status["ActiveNodes"] = activeNodes;
        status["ActiveNodeCount"] = activeNodes.Count;

        // 统计该工作流的队列中请求
        var queuedNodes = _queuedRequests.Values
            .Where(req => req.NodeContext.ExecutionId == executionId)
            .Select(req => req.NodeContext.NodeId)
            .ToList();

        status["QueuedNodes"] = queuedNodes;
        status["QueuedNodeCount"] = queuedNodes.Count;

        return await Task.FromResult(status);
    }

    /// <summary>
    /// 清空调度队列
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>清空任务</returns>
    public async Task ClearQueueAsync(CancellationToken cancellationToken = default)
    {
        var clearedCount = _queuedRequests.Count;
        _queuedRequests.Clear();

        // 创建新的Channel来替换当前的（检查是否已经关闭）
        if (!_scheduleWriter.TryComplete())
        {
            _logger.LogDebug("Schedule channel was already closed during clear operation");
        }

        _logger.LogInformation("Cleared {Count} requests from scheduler queue", clearedCount);
        await Task.CompletedTask;
    }

    /// <summary>
    /// 重置调度器统计信息
    /// </summary>
    public void ResetStatistics()
    {
        Statistics.TotalRequests = 0;
        Statistics.SuccessfulExecutions = 0;
        Statistics.FailedExecutions = 0;
        Statistics.AverageExecutionTimeMs = 0;
        Statistics.MaxExecutionTimeMs = 0;
        Statistics.MinExecutionTimeMs = 0;
        Statistics.StartedAt = DateTime.UtcNow;

        _logger.LogInformation("Scheduler statistics reset");
    }

    /// <summary>
    /// 主调度循环
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>调度任务</returns>
    private async Task SchedulingLoopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Scheduling loop started");

        try
        {
            await foreach (var request in _scheduleReader.ReadAllAsync(cancellationToken))
            {
                if (cancellationToken.IsCancellationRequested)
                    break;

                if (State == SchedulerState.Paused)
                {
                    // 暂停时等待一段时间再检查
                    await Task.Delay(1000, cancellationToken);
                    continue;
                }

                try
                {
                    // 检查是否有延迟
                    if (request.Delay > TimeSpan.Zero)
                    {
                        // 延迟调度，稍后处理
                        _ = Task.Run(async () =>
                        {
                            await Task.Delay(request.Delay, cancellationToken);
                            await ProcessScheduleRequestAsync(request, cancellationToken);
                        }, cancellationToken);
                    }
                    else
                    {
                        // 立即处理
                        await ProcessScheduleRequestAsync(request, cancellationToken);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing schedule request {RequestId}", request.RequestId);
                    _queuedRequests.TryRemove(request.RequestId, out _);
                }
            }
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("Scheduling loop cancelled");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Scheduling loop error");
        }

        _logger.LogInformation("Scheduling loop ended");
    }

    /// <summary>
    /// 处理调度请求
    /// </summary>
    /// <param name="request">调度请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理任务</returns>
    private async Task ProcessScheduleRequestAsync(NodeScheduleRequest request, CancellationToken cancellationToken)
    {
        // 等待执行槽位
        await _executionSemaphore.WaitAsync(cancellationToken);

        try
        {
            // 从队列中移除
            _queuedRequests.TryRemove(request.RequestId, out _);

            // 创建执行取消令牌
            var executionCts = CancellationTokenSource.CreateLinkedTokenSource(
                cancellationToken,
                request.NodeContext.CancellationToken);

            var executionKey = $"{request.NodeContext.ExecutionId}_{request.NodeContext.NodeId}";
            _activeExecutions.TryAdd(executionKey, executionCts);

            // 触发调度事件
            NodeScheduled?.Invoke(this, new NodeScheduledEventArgs
            {
                Request = request,
                ScheduledAt = DateTime.UtcNow,
                SchedulerId = SchedulerId
            });

            // 异步执行节点
            _ = Task.Run(async () =>
            {
                try
                {
                    await ExecuteNodeAsync(request, executionCts.Token);
                }
                finally
                {
                    _activeExecutions.TryRemove(executionKey, out _);
                    _executionSemaphore.Release();
                }
            }, cancellationToken);
        }
        catch (Exception ex)
        {
            _executionSemaphore.Release();
            _logger.LogError(ex, "Error starting node execution for {NodeId}", request.NodeContext.NodeId);
            throw;
        }
    }

    /// <summary>
    /// 执行节点
    /// </summary>
    /// <param name="request">调度请求</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>执行任务</returns>
    private async Task ExecuteNodeAsync(NodeScheduleRequest request, CancellationToken cancellationToken)
    {
        var context = request.NodeContext;
        var startTime = DateTime.UtcNow;
        NodeExecutionResult? result = null;

        try
        {
            context.UpdateState(NodeExecutionState.Running, "Node execution started");
            context.AddLog($"Starting execution of node {context.NodeId} (Type: {context.NodeType})");

            // 在 scope 中获取和执行节点执行器
            using var scope = _serviceProvider.CreateScope();
            var nodeExecutor = GetNodeExecutorFromScope(scope.ServiceProvider, context.NodeType);
            if (nodeExecutor == null)
            {
                throw new InvalidOperationException($"No executor found for node type: {context.NodeType}");
            }

            // 执行节点
            result = await nodeExecutor.ExecuteAsync(context, cancellationToken);

            // 更新状态
            context.UpdateState(result.State, "Node execution completed");
            context.AddLog($"Node execution completed with state: {result.State}");

            // 记录性能指标
            var executionTime = DateTime.UtcNow - startTime;
            context.RecordMetric("ExecutionTimeMs", executionTime.TotalMilliseconds);

            // 更新统计信息
            if (result.IsSuccess)
            {
                Statistics.SuccessfulExecutions++;
            }
            else
            {
                Statistics.FailedExecutions++;
            }

            UpdateExecutionTimeStatistics(executionTime.TotalMilliseconds);

            // 触发完成事件
            NodeCompleted?.Invoke(this, new NodeCompletedEventArgs
            {
                NodeContext = context,
                Result = result,
                CompletedAt = DateTime.UtcNow,
                ExecutionDuration = executionTime
            });
        }
        catch (Exception ex)
        {
            var executionTime = DateTime.UtcNow - startTime;

            // 创建错误结果
            result = new NodeExecutionResult
            {
                NodeId = context.NodeId,
                ExecutionId = context.ExecutionId,
                State = NodeExecutionState.Failed,
                IsSuccess = false,
                StartedAt = startTime,
                CompletedAt = DateTime.UtcNow,
                Exception = ex,
                ErrorMessage = ex.Message
            };

            context.UpdateState(NodeExecutionState.Failed, $"Node execution failed: {ex.Message}");
            context.AddLog($"Node execution failed: {ex.Message}", "ERROR");
            context.LastError = ex;

            Statistics.FailedExecutions++;
            UpdateExecutionTimeStatistics(executionTime.TotalMilliseconds);

            // 触发错误事件
            NodeExecutionError?.Invoke(this, new NodeExecutionErrorEventArgs
            {
                NodeContext = context,
                Error = ex,
                ErrorAt = DateTime.UtcNow,
                CanRetry = ShouldRetryExecution(context, ex),
                RetryCount = context.RetryCount
            });

            _logger.LogError(ex, "Node execution failed for {NodeId}", context.NodeId);
        }
        finally
        {
            // 设置节点执行结果到工作流上下文
            if (result != null && context.WorkflowExecutionContext is EngineWorkflowContext workflowContext)
            {
                workflowContext.NodeResults[context.NodeId] = result;
            }
        }
    }

    /// <summary>
    /// 从指定的服务提供者获取节点执行器
    /// </summary>
    /// <param name="serviceProvider">服务提供者</param>
    /// <param name="nodeType">节点类型</param>
    /// <returns>节点执行器</returns>
    private INodeExecutor? GetNodeExecutorFromScope(IServiceProvider serviceProvider, string nodeType)
    {
        try
        {
            var executors = serviceProvider.GetServices<INodeExecutor>();
            var executorList = executors.ToList();

            _logger.LogDebug("Looking for executor for node type '{NodeType}'. Available executors: {ExecutorCount}",
                nodeType, executorList.Count);

            foreach (var executor in executorList)
            {
                _logger.LogDebug("Available executor: {ExecutorType} with NodeType '{ExecutorNodeType}'",
                    executor.GetType().Name, executor.NodeType);
            }

            var matchedExecutor = executorList.FirstOrDefault(e => e.NodeType.Equals(nodeType, StringComparison.OrdinalIgnoreCase));

            if (matchedExecutor != null)
            {
                _logger.LogDebug("Found executor for node type '{NodeType}': {ExecutorType}",
                    nodeType, matchedExecutor.GetType().Name);
                return matchedExecutor;
            }

            _logger.LogWarning("No executor found for node type: {NodeType}", nodeType);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get node executor for type {NodeType}", nodeType);
            return null;
        }
    }

    /// <summary>
    /// 获取节点执行器（已弃用，使用 GetNodeExecutorFromScope 替代）
    /// </summary>
    /// <param name="nodeType">节点类型</param>
    /// <returns>节点执行器</returns>
    [Obsolete("Use GetNodeExecutorFromScope with a scoped service provider instead")]
    private INodeExecutor? GetNodeExecutor(string nodeType)
    {
        try
        {
            // 创建一个 scope 来解析 scoped 服务
            using var scope = _serviceProvider.CreateScope();
            return GetNodeExecutorFromScope(scope.ServiceProvider, nodeType);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get node executor for type {NodeType}", nodeType);
            return null;
        }
    }

    /// <summary>
    /// 获取节点执行器的旧版本实现（保留用于兼容性）
    /// </summary>
    /// <param name="nodeType">节点类型</param>
    /// <returns>节点执行器</returns>
    private INodeExecutor? GetNodeExecutorLegacy(string nodeType)
    {
        try
        {
            var matchedExecutor = GetNodeExecutorFromScope(_serviceProvider, nodeType);

            if (matchedExecutor != null)
            {
                _logger.LogDebug("Found matching executor: {ExecutorType} for node type '{NodeType}'",
                    matchedExecutor.GetType().Name, nodeType);
            }
            else
            {
                _logger.LogWarning("No matching executor found for node type '{NodeType}'", nodeType);
            }

            return matchedExecutor;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get node executor for type {NodeType}", nodeType);
            return null;
        }
    }

    /// <summary>
    /// 判断是否应该重试执行
    /// </summary>
    /// <param name="context">节点执行上下文</param>
    /// <param name="exception">异常信息</param>
    /// <returns>是否应该重试</returns>
    private bool ShouldRetryExecution(NodeExecutionContext context, Exception exception)
    {
        // 获取工作流执行上下文
        var workflowContext = context.WorkflowExecutionContext as EngineWorkflowContext;
        var retryStrategy = workflowContext?.Definition.Nodes
            .FirstOrDefault(n => n.NodeId == context.NodeId)?.RetryStrategy;

        if (retryStrategy == null || retryStrategy.StrategyType == RetryStrategyType.None)
            return false;

        // NodeExecutionContext没有RetryCount属性，使用默认值0
        var retryCount = 0; // 可以从Metadata中获取或使用其他方式跟踪
        if (retryCount >= retryStrategy.MaxRetryCount)
            return false;

        return retryStrategy.ShouldRetry(exception.GetType().Name);
    }

    /// <summary>
    /// 更新执行时间统计
    /// </summary>
    /// <param name="executionTimeMs">执行时间（毫秒）</param>
    private void UpdateExecutionTimeStatistics(double executionTimeMs)
    {
        if (Statistics.MaxExecutionTimeMs < executionTimeMs)
            Statistics.MaxExecutionTimeMs = executionTimeMs;

        if (Statistics.MinExecutionTimeMs == 0 || Statistics.MinExecutionTimeMs > executionTimeMs)
            Statistics.MinExecutionTimeMs = executionTimeMs;

        // 简单的移动平均
        var totalExecutions = Statistics.SuccessfulExecutions + Statistics.FailedExecutions;
        if (totalExecutions > 0)
        {
            Statistics.AverageExecutionTimeMs =
                (Statistics.AverageExecutionTimeMs * (totalExecutions - 1) + executionTimeMs) / totalExecutions;
        }
    }

    /// <summary>
    /// 延迟调度循环
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>延迟调度任务</returns>
    private async Task DelayedSchedulingLoopAsync(CancellationToken cancellationToken)
    {
        _logger.LogDebug("Delayed scheduling loop started");

        try
        {
            while (!cancellationToken.IsCancellationRequested)
            {
                await Task.Delay(_options.DelayCheckIntervalMs, cancellationToken);

                // 这里可以添加延迟调度的逻辑
                // 当前实现中延迟调度在主循环中处理
            }
        }
        catch (OperationCanceledException)
        {
            _logger.LogDebug("Delayed scheduling loop cancelled");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Delayed scheduling loop error");
        }

        _logger.LogDebug("Delayed scheduling loop ended");
    }

    /// <summary>
    /// 统计信息更新循环
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>统计更新任务</returns>
    private async Task StatisticsUpdateLoopAsync(CancellationToken cancellationToken)
    {
        _logger.LogDebug("Statistics update loop started");

        try
        {
            while (!cancellationToken.IsCancellationRequested)
            {
                await Task.Delay(_options.StatisticsUpdateIntervalMs, cancellationToken);

                // 更新实时统计信息
                Statistics.QueuedRequests = _queuedRequests.Count;
                Statistics.ActiveExecutions = _activeExecutions.Count;

                if (_options.EnableDetailedLogging)
                {
                    _logger.LogDebug("Statistics: Queued={Queued}, Active={Active}, Success={Success}, Failed={Failed}",
                        Statistics.QueuedRequests, Statistics.ActiveExecutions,
                        Statistics.SuccessfulExecutions, Statistics.FailedExecutions);
                }
            }
        }
        catch (OperationCanceledException)
        {
            _logger.LogDebug("Statistics update loop cancelled");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Statistics update loop error");
        }

        _logger.LogDebug("Statistics update loop ended");
    }

    /// <summary>
    /// 超时检查循环
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>超时检查任务</returns>
    private async Task TimeoutCheckLoopAsync(CancellationToken cancellationToken)
    {
        _logger.LogDebug("Timeout check loop started");

        try
        {
            while (!cancellationToken.IsCancellationRequested)
            {
                await Task.Delay(_options.TimeoutCheckIntervalMs, cancellationToken);

                var timeout = TimeSpan.FromMinutes(_options.NodeExecutionTimeoutMinutes);
                var now = DateTime.UtcNow;
                var timeoutKeys = new List<string>();

                // 检查超时的执行
                foreach (var kvp in _activeExecutions)
                {
                    // 这里需要跟踪执行开始时间，简化实现暂时跳过
                    // 在实际实现中应该维护执行开始时间的映射
                }

                // 取消超时的执行
                foreach (var key in timeoutKeys)
                {
                    if (_activeExecutions.TryRemove(key, out var cts))
                    {
                        cts.Cancel();
                        _logger.LogWarning("Cancelled execution due to timeout: {ExecutionKey}", key);
                    }
                }
            }
        }
        catch (OperationCanceledException)
        {
            _logger.LogDebug("Timeout check loop cancelled");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Timeout check loop error");
        }

        _logger.LogDebug("Timeout check loop ended");
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        try
        {
            if (State == SchedulerState.Running)
            {
                StopAsync().GetAwaiter().GetResult();
            }

            _schedulerCancellationTokenSource?.Dispose();
            _executionSemaphore?.Dispose();

            foreach (var cts in _activeExecutions.Values)
            {
                cts?.Dispose();
            }

            _activeExecutions.Clear();
            _queuedRequests.Clear();

            _logger.LogInformation("Scheduler {SchedulerId} disposed", SchedulerId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error disposing scheduler {SchedulerId}", SchedulerId);
        }
    }

    /// <summary>
    /// 节点调度事件
    /// </summary>
    public event EventHandler<NodeScheduledEventArgs>? NodeScheduled;

    /// <summary>
    /// 节点完成事件
    /// </summary>
    public event EventHandler<NodeCompletedEventArgs>? NodeCompleted;

    /// <summary>
    /// 节点执行错误事件
    /// </summary>
    public event EventHandler<NodeExecutionErrorEventArgs>? NodeExecutionError;

    /// <summary>
    /// 调度器状态变更事件
    /// </summary>
    public event EventHandler<SchedulerStateChangedEventArgs>? StateChanged;
}
