using FlowCustomV1.Core.Interfaces.Executor;
using FlowCustomV1.Core.Interfaces.Messaging;
using FlowCustomV1.Core.Models.Executor;
using FlowCustomV1.Core.Models.Messages;
using FlowCustomV1.Core.Models.Workflow;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;

namespace FlowCustomV1.Infrastructure.Services.Executor;

/// <summary>
/// 执行状态同步服务实现
/// 负责在分布式环境中同步工作流执行状态
/// </summary>
public class ExecutionStateSyncService : IExecutionStateSyncService
{
    private readonly INatsService _natsService;
    private readonly IMessageTopicService _topicService;
    private readonly ILogger<ExecutionStateSyncService> _logger;

    // 状态缓存
    private readonly ConcurrentDictionary<string, ExecutionStateInfo> _stateCache = new();
    private readonly ConcurrentDictionary<string, ExecutionContextSnapshot> _contextCache = new();
    
    // 订阅管理
    private readonly ConcurrentDictionary<string, List<Action<ExecutionStateChangedEvent>>> _stateSubscriptions = new();
    
    // 统计信息
    private long _totalSyncs = 0;
    private long _successfulSyncs = 0;
    private long _failedSyncs = 0;
    private readonly ConcurrentDictionary<StateSyncType, SyncTypeStatistics> _syncTypeStats = new();

    // 服务状态
    private bool _isStarted = false;
    private readonly object _startStopLock = new();

    public ExecutionStateSyncService(
        INatsService natsService,
        IMessageTopicService topicService,
        ILogger<ExecutionStateSyncService> logger)
    {
        _natsService = natsService ?? throw new ArgumentNullException(nameof(natsService));
        _topicService = topicService ?? throw new ArgumentNullException(nameof(topicService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        // 初始化统计信息
        foreach (var syncType in Enum.GetValues<StateSyncType>())
        {
            _syncTypeStats[syncType] = new SyncTypeStatistics { SyncType = syncType };
        }
    }

    /// <inheritdoc />
    public async Task SyncExecutionStateAsync(string executionId, WorkflowExecutionState state, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrEmpty(executionId);

        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        
        try
        {
            _logger.LogDebug("Syncing execution state for {ExecutionId}: {State}", executionId, state);

            Interlocked.Increment(ref _totalSyncs);
            UpdateSyncTypeStats(StateSyncType.ExecutionState, true, 0);

            // 更新本地缓存
            var stateInfo = _stateCache.GetOrAdd(executionId, _ => new ExecutionStateInfo { ExecutionId = executionId });
            stateInfo.State = state;
            stateInfo.LastUpdated = DateTime.UtcNow;

            // 创建状态更新消息
            var statusMessage = new ExecutionStatusUpdateMessage
            {
                ExecutionId = executionId,
                State = state,
                UpdatedAt = DateTime.UtcNow,
                ExecutorNodeId = Environment.MachineName
            };

            // 广播状态更新
            var statusTopic = _topicService.GetExecutionStatusTopic();
            await _natsService.PublishAsync(statusTopic, statusMessage, cancellationToken);

            Interlocked.Increment(ref _successfulSyncs);
            UpdateSyncTypeStats(StateSyncType.ExecutionState, true, stopwatch.ElapsedMilliseconds);

            // 触发状态同步事件
            OnStateSynced(new StateSyncEventArgs
            {
                ExecutionId = executionId,
                SyncType = StateSyncType.ExecutionState,
                Duration = stopwatch.Elapsed
            });

            _logger.LogDebug("Execution state synced successfully for {ExecutionId} in {Duration}ms", 
                executionId, stopwatch.ElapsedMilliseconds);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to sync execution state for {ExecutionId}", executionId);
            
            Interlocked.Increment(ref _failedSyncs);
            UpdateSyncTypeStats(StateSyncType.ExecutionState, false, stopwatch.ElapsedMilliseconds);

            // 触发同步失败事件
            OnStateSyncFailed(new StateSyncFailedEventArgs
            {
                ExecutionId = executionId,
                SyncType = StateSyncType.ExecutionState,
                Duration = stopwatch.Elapsed,
                ErrorMessage = ex.Message,
                Exception = ex
            });

            throw;
        }
    }

    /// <inheritdoc />
    public async Task SyncNodeStateAsync(string executionId, string nodeId, NodeExecutionState state, NodeExecutionResult? result = null, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrEmpty(executionId);
        ArgumentException.ThrowIfNullOrEmpty(nodeId);

        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        try
        {
            _logger.LogDebug("Syncing node state for {ExecutionId}/{NodeId}: {State}", executionId, nodeId, state);

            Interlocked.Increment(ref _totalSyncs);

            // 更新本地缓存
            var stateInfo = _stateCache.GetOrAdd(executionId, _ => new ExecutionStateInfo { ExecutionId = executionId });
            stateInfo.NodeStates[nodeId] = state;
            stateInfo.LastUpdated = DateTime.UtcNow;

            if (state == NodeExecutionState.Completed)
            {
                stateInfo.CompletedNodes++;
            }

            // 创建节点状态消息
            var nodeStateMessage = new NodeStateUpdateMessage
            {
                ExecutionId = executionId,
                NodeId = nodeId,
                State = state,
                Result = result,
                UpdatedAt = DateTime.UtcNow,
                ExecutorNodeId = Environment.MachineName
            };

            // 广播节点状态更新
            var nodeStateTopic = _topicService.GetNodeStateTopic();
            await _natsService.PublishAsync(nodeStateTopic, nodeStateMessage, cancellationToken);

            Interlocked.Increment(ref _successfulSyncs);
            UpdateSyncTypeStats(StateSyncType.NodeState, true, stopwatch.ElapsedMilliseconds);

            // 触发状态同步事件
            OnStateSynced(new StateSyncEventArgs
            {
                ExecutionId = executionId,
                NodeId = nodeId,
                SyncType = StateSyncType.NodeState,
                Duration = stopwatch.Elapsed
            });

            _logger.LogDebug("Node state synced successfully for {ExecutionId}/{NodeId} in {Duration}ms", 
                executionId, nodeId, stopwatch.ElapsedMilliseconds);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to sync node state for {ExecutionId}/{NodeId}", executionId, nodeId);
            
            Interlocked.Increment(ref _failedSyncs);
            UpdateSyncTypeStats(StateSyncType.NodeState, false, stopwatch.ElapsedMilliseconds);

            // 触发同步失败事件
            OnStateSyncFailed(new StateSyncFailedEventArgs
            {
                ExecutionId = executionId,
                NodeId = nodeId,
                SyncType = StateSyncType.NodeState,
                Duration = stopwatch.Elapsed,
                ErrorMessage = ex.Message,
                Exception = ex
            });

            throw;
        }
    }

    /// <inheritdoc />
    public async Task SyncExecutionResultAsync(WorkflowExecutionResult executionResult, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(executionResult);

        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        try
        {
            _logger.LogDebug("Syncing execution result for {ExecutionId}", executionResult.ExecutionId);

            Interlocked.Increment(ref _totalSyncs);

            // 创建执行结果消息
            var resultMessage = new ExecutionResultMessage
            {
                ExecutionId = executionResult.ExecutionId,
                WorkflowId = executionResult.WorkflowId,
                Result = executionResult,
                ExecutorNodeId = Environment.MachineName,
                CompletedAt = DateTime.UtcNow
            };

            // 广播执行结果
            var resultTopic = _topicService.GetExecutionResultTopic();
            await _natsService.PublishAsync(resultTopic, resultMessage, cancellationToken);

            Interlocked.Increment(ref _successfulSyncs);
            UpdateSyncTypeStats(StateSyncType.ExecutionResult, true, stopwatch.ElapsedMilliseconds);

            // 触发状态同步事件
            OnStateSynced(new StateSyncEventArgs
            {
                ExecutionId = executionResult.ExecutionId,
                SyncType = StateSyncType.ExecutionResult,
                Duration = stopwatch.Elapsed
            });

            _logger.LogDebug("Execution result synced successfully for {ExecutionId} in {Duration}ms", 
                executionResult.ExecutionId, stopwatch.ElapsedMilliseconds);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to sync execution result for {ExecutionId}", executionResult.ExecutionId);
            
            Interlocked.Increment(ref _failedSyncs);
            UpdateSyncTypeStats(StateSyncType.ExecutionResult, false, stopwatch.ElapsedMilliseconds);

            // 触发同步失败事件
            OnStateSyncFailed(new StateSyncFailedEventArgs
            {
                ExecutionId = executionResult.ExecutionId,
                SyncType = StateSyncType.ExecutionResult,
                Duration = stopwatch.Elapsed,
                ErrorMessage = ex.Message,
                Exception = ex
            });

            throw;
        }
    }

    /// <inheritdoc />
    public async Task<ExecutionStateInfo?> GetExecutionStateAsync(string executionId, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrEmpty(executionId);

        try
        {
            // 首先检查本地缓存
            if (_stateCache.TryGetValue(executionId, out var cachedState))
            {
                return cachedState;
            }

            // 如果本地没有，通过NATS查询
            var queryMessage = new ExecutionStateQueryMessage
            {
                ExecutionId = executionId,
                RequesterId = Environment.MachineName
            };

            var queryTopic = _topicService.GetExecutionStateQueryTopic();
            var response = await _natsService.RequestAsync<ExecutionStateQueryMessage, ExecutionStateResponseMessage>(
                queryTopic, queryMessage, TimeSpan.FromSeconds(10), cancellationToken);

            if (response?.StateInfo != null)
            {
                // 缓存查询结果
                _stateCache.TryAdd(executionId, response.StateInfo);
                return response.StateInfo;
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get execution state for {ExecutionId}", executionId);
            throw;
        }
    }

    /// <inheritdoc />
    public async Task SubscribeExecutionStateAsync(string executionId, Action<ExecutionStateChangedEvent> onStateChanged, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrEmpty(executionId);
        ArgumentNullException.ThrowIfNull(onStateChanged);

        try
        {
            _logger.LogDebug("Subscribing to execution state changes for {ExecutionId}", executionId);

            // 添加到订阅列表
            var subscriptions = _stateSubscriptions.GetOrAdd(executionId, _ => new List<Action<ExecutionStateChangedEvent>>());
            lock (subscriptions)
            {
                subscriptions.Add(onStateChanged);
            }

            _logger.LogDebug("Subscribed to execution state changes for {ExecutionId}", executionId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to subscribe to execution state for {ExecutionId}", executionId);
            throw;
        }
    }

    /// <inheritdoc />
    public async Task UnsubscribeExecutionStateAsync(string executionId, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrEmpty(executionId);

        try
        {
            _logger.LogDebug("Unsubscribing from execution state changes for {ExecutionId}", executionId);

            // 移除订阅
            _stateSubscriptions.TryRemove(executionId, out _);

            _logger.LogDebug("Unsubscribed from execution state changes for {ExecutionId}", executionId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to unsubscribe from execution state for {ExecutionId}", executionId);
            throw;
        }
    }

    /// <inheritdoc />
    public async Task BroadcastExecutionEventAsync(ExecutionEvent executionEvent, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(executionEvent);

        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        try
        {
            _logger.LogDebug("Broadcasting execution event {EventType} for {ExecutionId}",
                executionEvent.EventType, executionEvent.ExecutionId);

            Interlocked.Increment(ref _totalSyncs);

            // 广播执行事件
            var eventTopic = _topicService.GetExecutionEventTopic();
            await _natsService.PublishAsync(eventTopic, executionEvent, cancellationToken);

            Interlocked.Increment(ref _successfulSyncs);
            UpdateSyncTypeStats(StateSyncType.EventBroadcast, true, stopwatch.ElapsedMilliseconds);

            // 触发状态同步事件
            OnStateSynced(new StateSyncEventArgs
            {
                ExecutionId = executionEvent.ExecutionId,
                SyncType = StateSyncType.EventBroadcast,
                Duration = stopwatch.Elapsed
            });

            _logger.LogDebug("Execution event broadcasted successfully for {ExecutionId} in {Duration}ms",
                executionEvent.ExecutionId, stopwatch.ElapsedMilliseconds);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to broadcast execution event for {ExecutionId}", executionEvent.ExecutionId);

            Interlocked.Increment(ref _failedSyncs);
            UpdateSyncTypeStats(StateSyncType.EventBroadcast, false, stopwatch.ElapsedMilliseconds);

            // 触发同步失败事件
            OnStateSyncFailed(new StateSyncFailedEventArgs
            {
                ExecutionId = executionEvent.ExecutionId,
                SyncType = StateSyncType.EventBroadcast,
                Duration = stopwatch.Elapsed,
                ErrorMessage = ex.Message,
                Exception = ex
            });

            throw;
        }
    }

    /// <inheritdoc />
    public async Task SyncExecutionContextAsync(string executionId, ExecutionContextSnapshot context, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrEmpty(executionId);
        ArgumentNullException.ThrowIfNull(context);

        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        try
        {
            _logger.LogDebug("Syncing execution context for {ExecutionId}", executionId);

            Interlocked.Increment(ref _totalSyncs);

            // 更新本地缓存
            _contextCache.AddOrUpdate(executionId, context, (_, _) => context);

            // 创建上下文同步消息
            var contextMessage = new ExecutionContextSyncMessage
            {
                ExecutionId = executionId,
                Context = context,
                SyncedAt = DateTime.UtcNow,
                ExecutorNodeId = Environment.MachineName
            };

            // 广播上下文同步
            var contextTopic = _topicService.GetExecutionContextTopic();
            await _natsService.PublishAsync(contextTopic, contextMessage, cancellationToken);

            Interlocked.Increment(ref _successfulSyncs);
            UpdateSyncTypeStats(StateSyncType.ExecutionContext, true, stopwatch.ElapsedMilliseconds);

            // 触发状态同步事件
            OnStateSynced(new StateSyncEventArgs
            {
                ExecutionId = executionId,
                SyncType = StateSyncType.ExecutionContext,
                Duration = stopwatch.Elapsed
            });

            _logger.LogDebug("Execution context synced successfully for {ExecutionId} in {Duration}ms",
                executionId, stopwatch.ElapsedMilliseconds);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to sync execution context for {ExecutionId}", executionId);

            Interlocked.Increment(ref _failedSyncs);
            UpdateSyncTypeStats(StateSyncType.ExecutionContext, false, stopwatch.ElapsedMilliseconds);

            // 触发同步失败事件
            OnStateSyncFailed(new StateSyncFailedEventArgs
            {
                ExecutionId = executionId,
                SyncType = StateSyncType.ExecutionContext,
                Duration = stopwatch.Elapsed,
                ErrorMessage = ex.Message,
                Exception = ex
            });

            throw;
        }
    }

    /// <inheritdoc />
    public async Task<ExecutionContextSnapshot?> GetExecutionContextAsync(string executionId, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrEmpty(executionId);

        try
        {
            // 首先检查本地缓存
            if (_contextCache.TryGetValue(executionId, out var cachedContext))
            {
                return cachedContext;
            }

            // 如果本地没有，通过NATS查询
            var queryMessage = new ExecutionContextQueryMessage
            {
                ExecutionId = executionId,
                RequesterId = Environment.MachineName
            };

            var queryTopic = _topicService.GetExecutionContextQueryTopic();
            var response = await _natsService.RequestAsync<ExecutionContextQueryMessage, ExecutionContextResponseMessage>(
                queryTopic, queryMessage, TimeSpan.FromSeconds(10), cancellationToken);

            if (response?.Context != null)
            {
                // 缓存查询结果
                _contextCache.TryAdd(executionId, response.Context);
                return response.Context;
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get execution context for {ExecutionId}", executionId);
            throw;
        }
    }

    /// <inheritdoc />
    public async Task<int> CleanupExpiredStatesAsync(TimeSpan expirationTime, CancellationToken cancellationToken = default)
    {
        try
        {
            var expiredCount = 0;
            var cutoffTime = DateTime.UtcNow - expirationTime;
            var expiredExecutions = new List<string>();

            // 清理状态缓存
            foreach (var kvp in _stateCache)
            {
                if (kvp.Value.LastUpdated < cutoffTime)
                {
                    expiredExecutions.Add(kvp.Key);
                }
            }

            foreach (var executionId in expiredExecutions)
            {
                if (_stateCache.TryRemove(executionId, out _))
                {
                    expiredCount++;
                }

                // 同时清理上下文缓存
                _contextCache.TryRemove(executionId, out _);

                // 清理订阅
                _stateSubscriptions.TryRemove(executionId, out _);
            }

            if (expiredCount > 0)
            {
                _logger.LogInformation("Cleaned up {ExpiredCount} expired execution states", expiredCount);
            }

            return expiredCount;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to cleanup expired states");
            return 0;
        }
    }

    /// <inheritdoc />
    public StateSyncStatistics GetSyncStatistics()
    {
        try
        {
            var now = DateTime.UtcNow;
            var startTime = now.AddHours(-24); // 最近24小时的统计

            return new StateSyncStatistics
            {
                StartTime = startTime,
                EndTime = now,
                TotalSyncs = _totalSyncs,
                SuccessfulSyncs = _successfulSyncs,
                FailedSyncs = _failedSyncs,
                AverageSyncTimeMs = _syncTypeStats.Values.Where(s => s.Count > 0).Any() ?
                    _syncTypeStats.Values.Where(s => s.Count > 0).Average(s => s.AverageTimeMs) : 0,
                MaxSyncTimeMs = _syncTypeStats.Values.Where(s => s.Count > 0).Any() ?
                    (long)_syncTypeStats.Values.Where(s => s.Count > 0).Max(s => s.AverageTimeMs) : 0,
                MinSyncTimeMs = _syncTypeStats.Values.Where(s => s.Count > 0).Any() ?
                    (long)_syncTypeStats.Values.Where(s => s.Count > 0).Min(s => s.AverageTimeMs) : 0,
                SyncTypeStats = new Dictionary<StateSyncType, SyncTypeStatistics>(_syncTypeStats)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get sync statistics");
            throw;
        }
    }

    /// <inheritdoc />
    public async Task StartAsync(CancellationToken cancellationToken = default)
    {
        lock (_startStopLock)
        {
            if (_isStarted)
            {
                return;
            }
            _isStarted = true;
        }

        try
        {
            _logger.LogInformation("Starting Execution State Sync Service");

            // 初始化NATS订阅
            await InitializeNatsSubscriptionsAsync();

            _logger.LogInformation("Execution State Sync Service started successfully");
        }
        catch (Exception ex)
        {
            lock (_startStopLock)
            {
                _isStarted = false;
            }
            _logger.LogError(ex, "Failed to start Execution State Sync Service");
            throw;
        }
    }

    /// <inheritdoc />
    public async Task StopAsync(CancellationToken cancellationToken = default)
    {
        lock (_startStopLock)
        {
            if (!_isStarted)
            {
                return;
            }
            _isStarted = false;
        }

        try
        {
            _logger.LogInformation("Stopping Execution State Sync Service");

            // 清理缓存
            _stateCache.Clear();
            _contextCache.Clear();
            _stateSubscriptions.Clear();

            _logger.LogInformation("Execution State Sync Service stopped successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to stop Execution State Sync Service");
            throw;
        }
    }

    /// <inheritdoc />
    public event EventHandler<StateSyncEventArgs>? StateSynced;

    /// <inheritdoc />
    public event EventHandler<StateSyncFailedEventArgs>? StateSyncFailed;

    /// <summary>
    /// 触发状态同步事件
    /// </summary>
    protected virtual void OnStateSynced(StateSyncEventArgs e)
    {
        StateSynced?.Invoke(this, e);
    }

    /// <summary>
    /// 触发状态同步失败事件
    /// </summary>
    protected virtual void OnStateSyncFailed(StateSyncFailedEventArgs e)
    {
        StateSyncFailed?.Invoke(this, e);
    }

    #region 私有方法

    /// <summary>
    /// 初始化NATS订阅
    /// </summary>
    private async Task InitializeNatsSubscriptionsAsync()
    {
        try
        {
            // 订阅状态查询请求
            var stateQueryTopic = _topicService.GetExecutionStateQueryTopic();
            await _natsService.SubscribeAsync<ExecutionStateQueryMessage>(
                stateQueryTopic, OnExecutionStateQueryReceived);

            // 订阅上下文查询请求
            var contextQueryTopic = _topicService.GetExecutionContextQueryTopic();
            await _natsService.SubscribeAsync<ExecutionContextQueryMessage>(
                contextQueryTopic, OnExecutionContextQueryReceived);

            // 订阅状态更新通知
            var statusUpdateTopic = _topicService.GetExecutionStatusTopic();
            await _natsService.SubscribeAsync<ExecutionStatusUpdateMessage>(
                statusUpdateTopic, OnExecutionStatusUpdateReceived);

            _logger.LogInformation("NATS subscriptions initialized successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize NATS subscriptions");
            throw;
        }
    }

    /// <summary>
    /// 处理执行状态查询请求
    /// </summary>
    private async Task OnExecutionStateQueryReceived(ExecutionStateQueryMessage message)
    {
        try
        {
            _logger.LogDebug("Received execution state query for {ExecutionId}", message.ExecutionId);

            ExecutionStateInfo? stateInfo = null;
            if (_stateCache.TryGetValue(message.ExecutionId, out var cachedState))
            {
                stateInfo = cachedState;
            }

            var response = new ExecutionStateResponseMessage
            {
                ExecutionId = message.ExecutionId,
                StateInfo = stateInfo,
                ResponseTime = DateTime.UtcNow,
                ResponderNodeId = Environment.MachineName
            };

            var responseTopic = _topicService.GetExecutionStateResponseTopic();
            await _natsService.PublishAsync(responseTopic, response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to handle execution state query for {ExecutionId}", message.ExecutionId);
        }
    }

    /// <summary>
    /// 处理执行上下文查询请求
    /// </summary>
    private async Task OnExecutionContextQueryReceived(ExecutionContextQueryMessage message)
    {
        try
        {
            _logger.LogDebug("Received execution context query for {ExecutionId}", message.ExecutionId);

            ExecutionContextSnapshot? context = null;
            if (_contextCache.TryGetValue(message.ExecutionId, out var cachedContext))
            {
                context = cachedContext;
            }

            var response = new ExecutionContextResponseMessage
            {
                ExecutionId = message.ExecutionId,
                Context = context,
                ResponseTime = DateTime.UtcNow,
                ResponderNodeId = Environment.MachineName
            };

            var responseTopic = _topicService.GetExecutionContextResponseTopic();
            await _natsService.PublishAsync(responseTopic, response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to handle execution context query for {ExecutionId}", message.ExecutionId);
        }
    }

    /// <summary>
    /// 处理执行状态更新通知
    /// </summary>
    private async Task OnExecutionStatusUpdateReceived(ExecutionStatusUpdateMessage message)
    {
        try
        {
            _logger.LogDebug("Received execution status update for {ExecutionId}: {State}",
                message.ExecutionId, message.State);

            // 更新本地缓存
            var stateInfo = _stateCache.GetOrAdd(message.ExecutionId, _ => new ExecutionStateInfo
            {
                ExecutionId = message.ExecutionId
            });

            stateInfo.State = message.State;
            stateInfo.WorkflowId = message.WorkflowId;
            stateInfo.ExecutorNodeId = message.ExecutorNodeId;
            stateInfo.CurrentNodeId = message.CurrentNodeId;
            stateInfo.CompletedNodes = message.CompletedNodes;
            stateInfo.TotalNodes = message.TotalNodes;
            stateInfo.ErrorMessage = message.ErrorMessage;
            stateInfo.LastUpdated = message.UpdatedAt;

            // 通知订阅者
            if (_stateSubscriptions.TryGetValue(message.ExecutionId, out var subscriptions))
            {
                var stateChangedEvent = new ExecutionStateChangedEvent
                {
                    ExecutionId = message.ExecutionId,
                    WorkflowId = message.WorkflowId,
                    NewState = message.State,
                    Timestamp = message.UpdatedAt
                };

                lock (subscriptions)
                {
                    foreach (var callback in subscriptions)
                    {
                        try
                        {
                            callback(stateChangedEvent);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "Error in state change callback for {ExecutionId}", message.ExecutionId);
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to handle execution status update for {ExecutionId}", message.ExecutionId);
        }
    }

    /// <summary>
    /// 更新同步类型统计
    /// </summary>
    private void UpdateSyncTypeStats(StateSyncType syncType, bool success, long durationMs)
    {
        if (_syncTypeStats.TryGetValue(syncType, out var stats))
        {
            stats.Count++;
            if (success)
            {
                stats.SuccessCount++;
            }
            else
            {
                stats.FailureCount++;
            }

            // 更新平均时间（简单移动平均）
            stats.AverageTimeMs = (stats.AverageTimeMs * (stats.Count - 1) + durationMs) / stats.Count;
        }
    }

    #endregion
}
