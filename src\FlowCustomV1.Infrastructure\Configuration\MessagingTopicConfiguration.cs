using System.ComponentModel.DataAnnotations;

namespace FlowCustomV1.Infrastructure.Configuration;

/// <summary>
/// 消息主题配置
/// 定义NATS消息系统的主题结构和命名规范
/// </summary>
public class MessagingTopicConfiguration
{
    /// <summary>
    /// 配置节名称
    /// </summary>
    public const string SectionName = "MessagingTopics";

    /// <summary>
    /// 根主题前缀
    /// </summary>
    [Required]
    public string RootPrefix { get; set; } = "flowcustom";

    /// <summary>
    /// 集群管理主题配置
    /// </summary>
    public ClusterTopicConfiguration Cluster { get; set; } = new();

    /// <summary>
    /// 工作流主题配置
    /// </summary>
    public WorkflowTopicConfiguration Workflows { get; set; } = new();

    /// <summary>
    /// 节点主题配置
    /// </summary>
    public NodeTopicConfiguration Nodes { get; set; } = new();

    /// <summary>
    /// 任务调度主题配置
    /// </summary>
    public TaskTopicConfiguration Tasks { get; set; } = new();

    /// <summary>
    /// 角色专业化主题配置
    /// </summary>
    public RoleTopicConfiguration Roles { get; set; } = new();

    /// <summary>
    /// UI通信主题配置
    /// </summary>
    public UiTopicConfiguration Ui { get; set; } = new();

    /// <summary>
    /// 监控主题配置
    /// </summary>
    public MonitoringTopicConfiguration Monitoring { get; set; } = new();
}

/// <summary>
/// 集群管理主题配置
/// </summary>
public class ClusterTopicConfiguration
{
    /// <summary>
    /// 集群根主题后缀
    /// </summary>
    public string Root { get; set; } = "cluster";

    /// <summary>
    /// 节点心跳主题模板
    /// </summary>
    public string NodeHeartbeat { get; set; } = "nodes.{0}";

    /// <summary>
    /// 服务发现主题
    /// </summary>
    public string ServiceDiscovery { get; set; } = "discovery";

    /// <summary>
    /// 集群配置更新主题
    /// </summary>
    public string Config { get; set; } = "config";

    /// <summary>
    /// 节点注册主题
    /// </summary>
    public string NodeRegister { get; set; } = "register";

    /// <summary>
    /// 节点下线主题
    /// </summary>
    public string NodeUnregister { get; set; } = "unregister";
}

/// <summary>
/// 工作流主题配置
/// </summary>
public class WorkflowTopicConfiguration
{
    /// <summary>
    /// 工作流根主题后缀
    /// </summary>
    public string Root { get; set; } = "workflows";

    /// <summary>
    /// 工作流事件主题模板
    /// </summary>
    public string Events { get; set; } = "{0}.events";

    /// <summary>
    /// 工作流状态主题模板
    /// </summary>
    public string State { get; set; } = "{0}.state";

    /// <summary>
    /// 工作流执行主题模板
    /// </summary>
    public string Execution { get; set; } = "{0}.execution.{1}";
}

/// <summary>
/// 节点主题配置
/// </summary>
public class NodeTopicConfiguration
{
    /// <summary>
    /// 节点根主题后缀
    /// </summary>
    public string Root { get; set; } = "nodes";

    /// <summary>
    /// 节点任务主题模板
    /// </summary>
    public string Tasks { get; set; } = "{0}.tasks";

    /// <summary>
    /// 节点健康检查主题模板
    /// </summary>
    public string Health { get; set; } = "{0}.health";

    /// <summary>
    /// 节点状态主题模板
    /// </summary>
    public string Status { get; set; } = "{0}.status";
}

/// <summary>
/// 任务调度主题配置
/// </summary>
public class TaskTopicConfiguration
{
    /// <summary>
    /// 任务调度根主题后缀
    /// </summary>
    public string Root { get; set; } = "tasks";

    /// <summary>
    /// 高优先级任务队列
    /// </summary>
    public string HighPriority { get; set; } = "high";

    /// <summary>
    /// 普通优先级任务队列
    /// </summary>
    public string NormalPriority { get; set; } = "normal";

    /// <summary>
    /// 低优先级任务队列
    /// </summary>
    public string LowPriority { get; set; } = "low";
}

/// <summary>
/// 角色专业化主题配置
/// </summary>
public class RoleTopicConfiguration
{
    /// <summary>
    /// Designer节点专用主题后缀
    /// </summary>
    public string Designer { get; set; } = "designer";

    /// <summary>
    /// Validator节点专用主题后缀
    /// </summary>
    public string Validator { get; set; } = "validator";

    /// <summary>
    /// Executor节点专用主题后缀
    /// </summary>
    public string Executor { get; set; } = "executor";
}

/// <summary>
/// UI通信主题配置
/// </summary>
public class UiTopicConfiguration
{
    /// <summary>
    /// UI根主题后缀
    /// </summary>
    public string Root { get; set; } = "ui";

    /// <summary>
    /// 实时更新主题
    /// </summary>
    public string Updates { get; set; } = "updates";

    /// <summary>
    /// 用户通知主题
    /// </summary>
    public string Notifications { get; set; } = "notifications";

    /// <summary>
    /// 系统事件主题
    /// </summary>
    public string Events { get; set; } = "events";
}

/// <summary>
/// 监控主题配置
/// </summary>
public class MonitoringTopicConfiguration
{
    /// <summary>
    /// 监控根主题后缀
    /// </summary>
    public string Root { get; set; } = "monitoring";

    /// <summary>
    /// 性能指标主题
    /// </summary>
    public string Metrics { get; set; } = "metrics";

    /// <summary>
    /// 健康检查主题
    /// </summary>
    public string Health { get; set; } = "health";

    /// <summary>
    /// 告警主题
    /// </summary>
    public string Alerts { get; set; } = "alerts";

    /// <summary>
    /// 日志主题
    /// </summary>
    public string Logs { get; set; } = "logs";
}
