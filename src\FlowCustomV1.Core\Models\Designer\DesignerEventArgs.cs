using System.Text.Json.Serialization;
using FlowCustomV1.Core.Models.Workflow;

namespace FlowCustomV1.Core.Models.Designer;

/// <summary>
/// 工作流创建事件参数
/// </summary>
public class WorkflowCreatedEventArgs : EventArgs
{
    /// <summary>
    /// 工作流定义
    /// </summary>
    [JsonPropertyName("workflow")]
    public WorkflowDefinition Workflow { get; set; } = new();

    /// <summary>
    /// 创建者ID
    /// </summary>
    [JsonPropertyName("createdBy")]
    public string CreatedBy { get; set; } = string.Empty;

    /// <summary>
    /// 创建时间
    /// </summary>
    [JsonPropertyName("createdAt")]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 使用的模板ID（如果有）
    /// </summary>
    [JsonPropertyName("templateId")]
    public string? TemplateId { get; set; }
}

/// <summary>
/// 工作流更新事件参数
/// </summary>
public class WorkflowUpdatedEventArgs : EventArgs
{
    /// <summary>
    /// 工作流ID
    /// </summary>
    [JsonPropertyName("workflowId")]
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 更新后的工作流定义
    /// </summary>
    [JsonPropertyName("workflow")]
    public WorkflowDefinition Workflow { get; set; } = new();

    /// <summary>
    /// 更新前的工作流定义
    /// </summary>
    [JsonPropertyName("previousWorkflow")]
    public WorkflowDefinition? PreviousWorkflow { get; set; }

    /// <summary>
    /// 更新者ID
    /// </summary>
    [JsonPropertyName("updatedBy")]
    public string UpdatedBy { get; set; } = string.Empty;

    /// <summary>
    /// 更新时间
    /// </summary>
    [JsonPropertyName("updatedAt")]
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 变更摘要
    /// </summary>
    [JsonPropertyName("changes")]
    public List<VersionChange> Changes { get; set; } = new();
}

/// <summary>
/// 工作流删除事件参数
/// </summary>
public class WorkflowDeletedEventArgs : EventArgs
{
    /// <summary>
    /// 工作流ID
    /// </summary>
    [JsonPropertyName("workflowId")]
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 删除前的工作流定义
    /// </summary>
    [JsonPropertyName("deletedWorkflow")]
    public WorkflowDefinition DeletedWorkflow { get; set; } = new();

    /// <summary>
    /// 删除者ID
    /// </summary>
    [JsonPropertyName("deletedBy")]
    public string DeletedBy { get; set; } = string.Empty;

    /// <summary>
    /// 删除时间
    /// </summary>
    [JsonPropertyName("deletedAt")]
    public DateTime DeletedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 删除原因
    /// </summary>
    [JsonPropertyName("reason")]
    public string Reason { get; set; } = string.Empty;
}

/// <summary>
/// 设计协作事件参数
/// </summary>
public class DesignCollaborationEventArgs : EventArgs
{
    /// <summary>
    /// 工作流ID
    /// </summary>
    [JsonPropertyName("workflowId")]
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 协作动作
    /// </summary>
    [JsonPropertyName("action")]
    public CollaborationAction Action { get; set; }

    /// <summary>
    /// 协作者信息
    /// </summary>
    [JsonPropertyName("collaborator")]
    public CollaboratorInfo? Collaborator { get; set; }

    /// <summary>
    /// 设计操作（当动作为设计变更时）
    /// </summary>
    [JsonPropertyName("operation")]
    public DesignOperation? Operation { get; set; }

    /// <summary>
    /// 事件时间
    /// </summary>
    [JsonPropertyName("timestamp")]
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 活跃协作者列表
    /// </summary>
    [JsonPropertyName("activeCollaborators")]
    public List<CollaboratorInfo> ActiveCollaborators { get; set; } = new();
}

/// <summary>
/// 模板变更事件参数
/// </summary>
public class TemplateChangedEventArgs : EventArgs
{
    /// <summary>
    /// 模板ID
    /// </summary>
    [JsonPropertyName("templateId")]
    public string TemplateId { get; set; } = string.Empty;

    /// <summary>
    /// 变更类型
    /// </summary>
    [JsonPropertyName("changeType")]
    public TemplateChangeType ChangeType { get; set; }

    /// <summary>
    /// 模板信息
    /// </summary>
    [JsonPropertyName("template")]
    public WorkflowTemplate Template { get; set; } = new();

    /// <summary>
    /// 变更前的模板信息（更新和删除时）
    /// </summary>
    [JsonPropertyName("previousTemplate")]
    public WorkflowTemplate? PreviousTemplate { get; set; }

    /// <summary>
    /// 变更者ID
    /// </summary>
    [JsonPropertyName("changedBy")]
    public string ChangedBy { get; set; } = string.Empty;

    /// <summary>
    /// 变更时间
    /// </summary>
    [JsonPropertyName("changedAt")]
    public DateTime ChangedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 模板变更类型枚举
/// </summary>
public enum TemplateChangeType
{
    /// <summary>
    /// 创建
    /// </summary>
    Created,

    /// <summary>
    /// 更新
    /// </summary>
    Updated,

    /// <summary>
    /// 删除
    /// </summary>
    Deleted,

    /// <summary>
    /// 发布
    /// </summary>
    Published,

    /// <summary>
    /// 取消发布
    /// </summary>
    Unpublished
}

/// <summary>
/// 版本创建事件参数
/// </summary>
public class VersionCreatedEventArgs : EventArgs
{
    /// <summary>
    /// 工作流ID
    /// </summary>
    [JsonPropertyName("workflowId")]
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 版本信息
    /// </summary>
    [JsonPropertyName("version")]
    public WorkflowVersion Version { get; set; } = new();

    /// <summary>
    /// 创建者ID
    /// </summary>
    [JsonPropertyName("createdBy")]
    public string CreatedBy { get; set; } = string.Empty;

    /// <summary>
    /// 创建时间
    /// </summary>
    [JsonPropertyName("createdAt")]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 是否为自动创建
    /// </summary>
    [JsonPropertyName("isAutoCreated")]
    public bool IsAutoCreated { get; set; } = false;
}

/// <summary>
/// 冲突检测事件参数
/// </summary>
public class ConflictDetectedEventArgs : EventArgs
{
    /// <summary>
    /// 冲突ID
    /// </summary>
    [JsonPropertyName("conflictId")]
    public string ConflictId { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 工作流ID
    /// </summary>
    [JsonPropertyName("workflowId")]
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 冲突类型
    /// </summary>
    [JsonPropertyName("conflictType")]
    public ConflictType ConflictType { get; set; }

    /// <summary>
    /// 冲突的协作者列表
    /// </summary>
    [JsonPropertyName("conflictingCollaborators")]
    public List<CollaboratorInfo> ConflictingCollaborators { get; set; } = new();

    /// <summary>
    /// 冲突的操作列表
    /// </summary>
    [JsonPropertyName("conflictingOperations")]
    public List<DesignOperation> ConflictingOperations { get; set; } = new();

    /// <summary>
    /// 检测时间
    /// </summary>
    [JsonPropertyName("detectedAt")]
    public DateTime DetectedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 冲突描述
    /// </summary>
    [JsonPropertyName("description")]
    public string Description { get; set; } = string.Empty;
}

/// <summary>
/// 冲突类型枚举
/// </summary>
public enum ConflictType
{
    /// <summary>
    /// 并发编辑同一对象
    /// </summary>
    ConcurrentEdit,

    /// <summary>
    /// 删除正在编辑的对象
    /// </summary>
    DeleteWhileEditing,

    /// <summary>
    /// 移动正在编辑的对象
    /// </summary>
    MoveWhileEditing,

    /// <summary>
    /// 版本冲突
    /// </summary>
    VersionConflict,

    /// <summary>
    /// 权限冲突
    /// </summary>
    PermissionConflict
}
