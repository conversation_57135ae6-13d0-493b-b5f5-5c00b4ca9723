using FlowCustomV1.Core.Models.Plugins;

namespace FlowCustomV1.Core.Interfaces.Plugins;

/// <summary>
/// 插件加载器接口
/// 支持不同类型插件的加载和管理
/// </summary>
public interface IPluginLoader
{
    /// <summary>
    /// 插件类型
    /// </summary>
    PluginType PluginType { get; }

    /// <summary>
    /// 初始化插件加载器
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>初始化任务</returns>
    Task InitializeAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 加载插件
    /// </summary>
    /// <param name="pluginPath">插件路径</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>加载结果</returns>
    Task<PluginLoadResult> LoadPluginAsync(string pluginPath, CancellationToken cancellationToken = default);

    /// <summary>
    /// 卸载插件
    /// </summary>
    /// <param name="nodeType">节点类型</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>卸载结果</returns>
    Task<PluginUnloadResult> UnloadPluginAsync(string nodeType, CancellationToken cancellationToken = default);

    /// <summary>
    /// 重新加载插件
    /// </summary>
    /// <param name="nodeType">节点类型</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>重新加载结果</returns>
    Task<PluginLoadResult> ReloadPluginAsync(string nodeType, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取已加载的插件
    /// </summary>
    /// <param name="nodeType">节点类型</param>
    /// <returns>插件执行器</returns>
    INodeExecutor? GetLoadedPlugin(string nodeType);

    /// <summary>
    /// 获取所有已加载的插件
    /// </summary>
    /// <returns>插件执行器字典</returns>
    IReadOnlyDictionary<string, INodeExecutor> GetAllLoadedPlugins();

    /// <summary>
    /// 检查插件是否已加载
    /// </summary>
    /// <param name="nodeType">节点类型</param>
    /// <returns>是否已加载</returns>
    bool IsPluginLoaded(string nodeType);

    /// <summary>
    /// 发现可用插件
    /// </summary>
    /// <param name="searchPath">搜索路径</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>可用插件列表</returns>
    Task<IReadOnlyList<PluginInfo>> DiscoverPluginsAsync(string searchPath, CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证插件
    /// </summary>
    /// <param name="pluginPath">插件路径</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    Task<PluginValidationResult> ValidatePluginAsync(string pluginPath, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取插件元数据
    /// </summary>
    /// <param name="pluginPath">插件路径</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>插件元数据</returns>
    Task<PluginMetadata?> GetPluginMetadataAsync(string pluginPath, CancellationToken cancellationToken = default);

    /// <summary>
    /// 插件加载事件
    /// </summary>
    event EventHandler<PluginLoadedEventArgs> PluginLoaded;

    /// <summary>
    /// 插件卸载事件
    /// </summary>
    event EventHandler<PluginUnloadedEventArgs> PluginUnloaded;

    /// <summary>
    /// 插件错误事件
    /// </summary>
    event EventHandler<PluginErrorEventArgs> PluginError;
}
