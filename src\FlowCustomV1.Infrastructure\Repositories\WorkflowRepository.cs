using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using FlowCustomV1.Core.Interfaces;
using FlowCustomV1.Core.Models.Workflow;
using FlowCustomV1.Infrastructure.Data;
using FlowCustomV1.Infrastructure.Mappers;

namespace FlowCustomV1.Infrastructure.Repositories;

/// <summary>
/// 工作流仓储实现
/// 提供工作流定义的持久化和查询功能
/// </summary>
public class WorkflowRepository : IWorkflowRepository
{
    private readonly WorkflowDbContext _dbContext;
    private readonly ILogger<WorkflowRepository> _logger;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="dbContext">数据库上下文</param>
    /// <param name="logger">日志记录器</param>
    public WorkflowRepository(WorkflowDbContext dbContext, ILogger<WorkflowRepository> logger)
    {
        _dbContext = dbContext;
        _logger = logger;
    }

    /// <summary>
    /// 保存工作流定义
    /// </summary>
    /// <param name="workflowDefinition">工作流定义</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>保存结果</returns>
    public async Task<bool> SaveWorkflowDefinitionAsync(WorkflowDefinition workflowDefinition, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("保存工作流定义: {WorkflowId}", workflowDefinition.WorkflowId);

            // 检查是否已存在
            var existingEntity = await _dbContext.WorkflowDefinitions
                .FirstOrDefaultAsync(w => w.WorkflowId == workflowDefinition.WorkflowId, cancellationToken);

            if (existingEntity != null)
            {
                // 更新现有记录
                var updatedEntity = workflowDefinition.ToEntity();
                updatedEntity.Id = existingEntity.Id;
                updatedEntity.CreatedAt = existingEntity.CreatedAt; // 保持原创建时间
                updatedEntity.LastModifiedAt = DateTime.UtcNow;

                _dbContext.Entry(existingEntity).CurrentValues.SetValues(updatedEntity);
            }
            else
            {
                // 创建新记录
                var entity = workflowDefinition.ToEntity();
                await _dbContext.WorkflowDefinitions.AddAsync(entity, cancellationToken);
            }

            var result = await _dbContext.SaveChangesAsync(cancellationToken);
            
            _logger.LogInformation("工作流定义保存成功: {WorkflowId}", workflowDefinition.WorkflowId);
            return result > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存工作流定义失败: {WorkflowId}", workflowDefinition.WorkflowId);
            return false;
        }
    }

    /// <summary>
    /// 根据ID获取工作流定义
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>工作流定义</returns>
    public async Task<WorkflowDefinition?> GetWorkflowDefinitionAsync(string workflowId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("获取工作流定义: {WorkflowId}", workflowId);

            var entity = await _dbContext.WorkflowDefinitions
                .FirstOrDefaultAsync(w => w.WorkflowId == workflowId && w.IsActive, cancellationToken);

            if (entity == null)
            {
                _logger.LogWarning("工作流定义不存在: {WorkflowId}", workflowId);
                return null;
            }

            return entity.ToDomain();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取工作流定义失败: {WorkflowId}", workflowId);
            return null;
        }
    }

    /// <summary>
    /// 根据ID获取工作流定义（别名方法）
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>工作流定义</returns>
    public async Task<WorkflowDefinition?> GetByIdAsync(string workflowId, CancellationToken cancellationToken = default)
    {
        return await GetWorkflowDefinitionAsync(workflowId, cancellationToken);
    }

    /// <summary>
    /// 根据名称和版本获取工作流定义
    /// </summary>
    /// <param name="name">工作流名称</param>
    /// <param name="version">工作流版本</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>工作流定义</returns>
    public async Task<WorkflowDefinition?> GetWorkflowDefinitionAsync(string name, string version, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("获取工作流定义: {Name} v{Version}", name, version);

            var entity = await _dbContext.WorkflowDefinitions
                .FirstOrDefaultAsync(w => w.Name == name && w.Version == version && w.IsActive, cancellationToken);

            return entity?.ToDomain();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取工作流定义失败: {Name} v{Version}", name, version);
            return null;
        }
    }

    /// <summary>
    /// 获取所有工作流定义
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>工作流定义列表</returns>
    public async Task<IEnumerable<WorkflowDefinition>> GetAllWorkflowDefinitionsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("获取所有工作流定义");

            var entities = await _dbContext.WorkflowDefinitions
                .Where(w => w.IsActive)
                .OrderBy(w => w.Name)
                .ThenByDescending(w => w.CreatedAt)
                .ToListAsync(cancellationToken);

            return entities.Select(e => e.ToDomain());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取所有工作流定义失败");
            return Enumerable.Empty<WorkflowDefinition>();
        }
    }

    /// <summary>
    /// 根据标签查询工作流定义
    /// </summary>
    /// <param name="tags">标签列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>工作流定义列表</returns>
    public async Task<IEnumerable<WorkflowDefinition>> GetWorkflowDefinitionsByTagsAsync(IEnumerable<string> tags, CancellationToken cancellationToken = default)
    {
        try
        {
            var tagList = tags.ToList();
            _logger.LogDebug("根据标签查询工作流定义: {Tags}", string.Join(", ", tagList));

            var entities = await _dbContext.WorkflowDefinitions
                .Where(w => w.IsActive && w.Tags != null)
                .ToListAsync(cancellationToken);

            // 在内存中过滤标签（因为JSON查询在不同数据库中语法不同）
            var filteredEntities = entities.Where(e =>
            {
                if (string.IsNullOrEmpty(e.Tags)) return false;
                
                try
                {
                    var entityTags = System.Text.Json.JsonSerializer.Deserialize<HashSet<string>>(e.Tags);
                    return entityTags != null && tagList.Any(tag => entityTags.Contains(tag));
                }
                catch
                {
                    return false;
                }
            });

            return filteredEntities.Select(e => e.ToDomain());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据标签查询工作流定义失败");
            return Enumerable.Empty<WorkflowDefinition>();
        }
    }

    /// <summary>
    /// 根据作者查询工作流定义
    /// </summary>
    /// <param name="author">作者</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>工作流定义列表</returns>
    public async Task<IEnumerable<WorkflowDefinition>> GetWorkflowDefinitionsByAuthorAsync(string author, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("根据作者查询工作流定义: {Author}", author);

            var entities = await _dbContext.WorkflowDefinitions
                .Where(w => w.IsActive && w.Author == author)
                .OrderByDescending(w => w.CreatedAt)
                .ToListAsync(cancellationToken);

            return entities.Select(e => e.ToDomain());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据作者查询工作流定义失败: {Author}", author);
            return Enumerable.Empty<WorkflowDefinition>();
        }
    }

    /// <summary>
    /// 删除工作流定义
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>删除结果</returns>
    public async Task<bool> DeleteWorkflowDefinitionAsync(string workflowId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("删除工作流定义: {WorkflowId}", workflowId);

            var entity = await _dbContext.WorkflowDefinitions
                .FirstOrDefaultAsync(w => w.WorkflowId == workflowId, cancellationToken);

            if (entity == null)
            {
                _logger.LogWarning("要删除的工作流定义不存在: {WorkflowId}", workflowId);
                return false;
            }

            // 软删除：设置为非活跃状态
            entity.IsActive = false;
            entity.LastModifiedAt = DateTime.UtcNow;

            var result = await _dbContext.SaveChangesAsync(cancellationToken);
            
            _logger.LogInformation("工作流定义删除成功: {WorkflowId}", workflowId);
            return result > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除工作流定义失败: {WorkflowId}", workflowId);
            return false;
        }
    }

    /// <summary>
    /// 检查工作流定义是否存在
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否存在</returns>
    public async Task<bool> ExistsAsync(string workflowId, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _dbContext.WorkflowDefinitions
                .AnyAsync(w => w.WorkflowId == workflowId && w.IsActive, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查工作流定义是否存在失败: {WorkflowId}", workflowId);
            return false;
        }
    }

    /// <summary>
    /// 获取工作流定义的版本历史
    /// </summary>
    /// <param name="name">工作流名称</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>版本历史列表</returns>
    public async Task<IEnumerable<WorkflowDefinition>> GetWorkflowVersionHistoryAsync(string name, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("获取工作流版本历史: {Name}", name);

            var entities = await _dbContext.WorkflowDefinitions
                .Where(w => w.Name == name && w.IsActive)
                .OrderByDescending(w => w.CreatedAt)
                .ToListAsync(cancellationToken);

            return entities.Select(e => e.ToDomain());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取工作流版本历史失败: {Name}", name);
            return Enumerable.Empty<WorkflowDefinition>();
        }
    }

    /// <summary>
    /// 搜索工作流定义
    /// </summary>
    /// <param name="searchCriteria">搜索条件</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>搜索结果</returns>
    public async Task<IEnumerable<WorkflowDefinition>> SearchWorkflowDefinitionsAsync(WorkflowSearchCriteria searchCriteria, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("搜索工作流定义: {Keyword}", searchCriteria.Keyword);

            var query = _dbContext.WorkflowDefinitions.Where(w => w.IsActive);

            // 关键词搜索
            if (!string.IsNullOrEmpty(searchCriteria.Keyword))
            {
                var keyword = searchCriteria.Keyword.ToLower();
                query = query.Where(w =>
                    w.Name.ToLower().Contains(keyword) ||
                    (w.Description != null && w.Description.ToLower().Contains(keyword)));
            }

            // 作者过滤
            if (!string.IsNullOrEmpty(searchCriteria.Author))
            {
                query = query.Where(w => w.Author == searchCriteria.Author);
            }

            // 时间范围过滤
            if (searchCriteria.CreatedAfter.HasValue)
            {
                query = query.Where(w => w.CreatedAt >= searchCriteria.CreatedAfter.Value);
            }

            if (searchCriteria.CreatedBefore.HasValue)
            {
                query = query.Where(w => w.CreatedAt <= searchCriteria.CreatedBefore.Value);
            }

            // 排序
            query = searchCriteria.SortBy.ToLower() switch
            {
                "name" => searchCriteria.SortDescending ?
                    query.OrderByDescending(w => w.Name) :
                    query.OrderBy(w => w.Name),
                "author" => searchCriteria.SortDescending ?
                    query.OrderByDescending(w => w.Author) :
                    query.OrderBy(w => w.Author),
                "lastmodifiedat" => searchCriteria.SortDescending ?
                    query.OrderByDescending(w => w.LastModifiedAt) :
                    query.OrderBy(w => w.LastModifiedAt),
                _ => searchCriteria.SortDescending ?
                    query.OrderByDescending(w => w.CreatedAt) :
                    query.OrderBy(w => w.CreatedAt)
            };

            // 分页
            var skip = (searchCriteria.PageNumber - 1) * searchCriteria.PageSize;
            query = query.Skip(skip).Take(searchCriteria.PageSize);

            var entities = await query.ToListAsync(cancellationToken);

            // 标签过滤（在内存中进行）
            var results = entities.Select(e => e.ToDomain()).ToList();

            if (searchCriteria.Tags.Any())
            {
                results = results.Where(w =>
                    searchCriteria.Tags.Any(tag => w.Tags.Contains(tag))
                ).ToList();
            }

            return results;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "搜索工作流定义失败");
            return Enumerable.Empty<WorkflowDefinition>();
        }
    }

    /// <summary>
    /// 获取工作流定义统计信息
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>统计信息</returns>
    public async Task<WorkflowRepositoryStatistics> GetStatisticsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("获取工作流定义统计信息");

            var totalWorkflows = await _dbContext.WorkflowDefinitions
                .CountAsync(w => w.IsActive, cancellationToken);

            var activeWorkflows = totalWorkflows; // 当前所有活跃的都算作活跃

            var totalAuthors = await _dbContext.WorkflowDefinitions
                .Where(w => w.IsActive && w.Author != null)
                .Select(w => w.Author)
                .Distinct()
                .CountAsync(cancellationToken);

            var lastCreated = await _dbContext.WorkflowDefinitions
                .Where(w => w.IsActive)
                .MaxAsync(w => (DateTime?)w.CreatedAt, cancellationToken);

            var lastModified = await _dbContext.WorkflowDefinitions
                .Where(w => w.IsActive)
                .MaxAsync(w => (DateTime?)w.LastModifiedAt, cancellationToken);

            // 计算存储大小（近似值）
            var definitionJsonSizes = await _dbContext.WorkflowDefinitions
                .Where(w => w.IsActive)
                .Select(w => w.DefinitionJson.Length)
                .ToListAsync(cancellationToken);

            var storageSizeBytes = definitionJsonSizes.Sum(size => (long)size);

            return new WorkflowRepositoryStatistics
            {
                TotalWorkflows = totalWorkflows,
                ActiveWorkflows = activeWorkflows,
                TotalAuthors = totalAuthors,
                TotalTags = 0, // 需要复杂查询，暂时设为0
                LastCreatedAt = lastCreated,
                LastModifiedAt = lastModified,
                StorageSizeBytes = storageSizeBytes
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取工作流定义统计信息失败");
            return new WorkflowRepositoryStatistics();
        }
    }

    /// <summary>
    /// 分页获取工作流定义
    /// </summary>
    /// <param name="pageIndex">页索引</param>
    /// <param name="pageSize">页大小</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>分页结果</returns>
    public async Task<IReadOnlyList<WorkflowDefinition>> GetWorkflowDefinitionsAsync(int pageIndex, int pageSize, CancellationToken cancellationToken = default)
    {
        try
        {
            var entities = await _dbContext.WorkflowDefinitions
                .OrderByDescending(w => w.CreatedAt)
                .Skip(pageIndex * pageSize)
                .Take(pageSize)
                .ToListAsync(cancellationToken);

            return entities.Select(WorkflowMapper.ToModel).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "分页获取工作流定义失败，页索引: {PageIndex}, 页大小: {PageSize}", pageIndex, pageSize);
            return new List<WorkflowDefinition>();
        }
    }

    /// <summary>
    /// 获取工作流总数
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>工作流总数</returns>
    public async Task<long> GetWorkflowCountAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            return await _dbContext.WorkflowDefinitions.CountAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取工作流总数失败");
            return 0;
        }
    }

    /// <summary>
    /// 搜索工作流定义（支持分页）
    /// </summary>
    /// <param name="searchTerm">搜索词</param>
    /// <param name="pageIndex">页索引</param>
    /// <param name="pageSize">页大小</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>搜索结果</returns>
    public async Task<IReadOnlyList<WorkflowDefinition>> SearchWorkflowDefinitionsAsync(string searchTerm, int pageIndex, int pageSize, CancellationToken cancellationToken = default)
    {
        try
        {
            var query = _dbContext.WorkflowDefinitions.AsQueryable();

            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                query = query.Where(w =>
                    w.Name.Contains(searchTerm) ||
                    w.Description.Contains(searchTerm) ||
                    w.Author.Contains(searchTerm));
            }

            var entities = await query
                .OrderByDescending(w => w.CreatedAt)
                .Skip(pageIndex * pageSize)
                .Take(pageSize)
                .ToListAsync(cancellationToken);

            return entities.Select(WorkflowMapper.ToModel).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "搜索工作流定义失败，搜索词: {SearchTerm}", searchTerm);
            return new List<WorkflowDefinition>();
        }
    }

    /// <summary>
    /// 根据搜索条件获取工作流总数
    /// </summary>
    /// <param name="searchTerm">搜索词</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>匹配的工作流总数</returns>
    public async Task<long> GetWorkflowCountBySearchAsync(string searchTerm, CancellationToken cancellationToken = default)
    {
        try
        {
            var query = _dbContext.WorkflowDefinitions.AsQueryable();

            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                query = query.Where(w =>
                    w.Name.Contains(searchTerm) ||
                    w.Description.Contains(searchTerm) ||
                    w.Author.Contains(searchTerm));
            }

            return await query.CountAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据搜索条件获取工作流总数失败，搜索词: {SearchTerm}", searchTerm);
            return 0;
        }
    }

    /// <summary>
    /// 获取工作流版本列表
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>版本列表</returns>
    public async Task<IReadOnlyList<string>> GetWorkflowVersionsAsync(string workflowId, CancellationToken cancellationToken = default)
    {
        try
        {
            var versions = await _dbContext.WorkflowDefinitions
                .Where(w => w.WorkflowId == workflowId)
                .Select(w => w.Version)
                .Distinct()
                .OrderByDescending(v => v)
                .ToListAsync(cancellationToken);

            return versions;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取工作流版本列表失败，工作流ID: {WorkflowId}", workflowId);
            return new List<string>();
        }
    }

    /// <summary>
    /// 根据版本获取工作流定义
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="version">版本号</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>工作流定义</returns>
    public async Task<WorkflowDefinition?> GetWorkflowDefinitionByVersionAsync(string workflowId, string version, CancellationToken cancellationToken = default)
    {
        try
        {
            var entity = await _dbContext.WorkflowDefinitions
                .FirstOrDefaultAsync(w => w.WorkflowId == workflowId && w.Version == version, cancellationToken);

            return entity != null ? WorkflowMapper.ToModel(entity) : null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据版本获取工作流定义失败，工作流ID: {WorkflowId}, 版本: {Version}", workflowId, version);
            return null;
        }
    }
}
