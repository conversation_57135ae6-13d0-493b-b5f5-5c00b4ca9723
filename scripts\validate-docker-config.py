#!/usr/bin/env python3
"""
FlowCustomV1 Docker配置验证脚本
验证所有环境的Docker配置完整性和正确性
"""

import os
import json
from pathlib import Path

def check_file_exists(file_path, description):
    """检查文件是否存在"""
    if file_path.exists():
        print(f"✅ {description}: 存在")
        return True
    else:
        print(f"❌ {description}: 不存在 - {file_path}")
        return False

def validate_environment_config(env_name, expected_ports):
    """验证单个环境配置"""
    print(f"\n🔍 验证 {env_name.upper()} 环境配置...")
    
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    env_dir = project_root / "docker" / env_name
    
    issues = []
    
    # 检查目录存在
    if not env_dir.exists():
        print(f"❌ 环境目录不存在: {env_dir}")
        return False
    
    # 检查docker-compose.yml
    compose_file = env_dir / "docker-compose.yml"
    if not check_file_exists(compose_file, "Docker Compose配置"):
        issues.append("缺少docker-compose.yml")
    else:
        # 验证端口配置
        try:
            with open(compose_file, 'r', encoding='utf-8') as f:
                compose_content = f.read()
                
            # 检查端口配置
            for service, port in expected_ports.items():
                if f'"{port}:' in compose_content:
                    print(f"✅ {service}端口配置正确: {port}")
                else:
                    print(f"⚠️ {service}端口配置可能不正确: 期望{port}")
                    issues.append(f"{service}端口配置")
                    
        except Exception as e:
            print(f"❌ 读取docker-compose.yml失败: {e}")
            issues.append("docker-compose.yml读取失败")
    
    # 检查启动脚本
    if env_name == "development":
        script_name = "start-dev-env.py"
    elif env_name == "testing":
        script_name = "start-test-cluster.py"
    elif env_name == "production":
        script_name = "start-prod-cluster.py"
    
    script_file = env_dir / script_name
    if not check_file_exists(script_file, f"启动脚本 ({script_name})"):
        issues.append(f"缺少{script_name}")
    
    # 检查Dockerfile文件
    required_dockerfiles = ["api", "worker", "designer", "validator", "executor", "monitor", "scheduler"]
    if env_name == "testing":
        required_dockerfiles.append("multi")
    
    for dockerfile_name in required_dockerfiles:
        dockerfile_path = env_dir / f"Dockerfile.{dockerfile_name}"
        if not check_file_exists(dockerfile_path, f"Dockerfile.{dockerfile_name}"):
            issues.append(f"缺少Dockerfile.{dockerfile_name}")
    
    # 总结
    if not issues:
        print(f"✅ {env_name.upper()} 环境配置验证通过")
        return True
    else:
        print(f"❌ {env_name.upper()} 环境配置存在问题:")
        for issue in issues:
            print(f"   - {issue}")
        return False

def validate_nats_config():
    """验证NATS配置"""
    print("\n🔍 验证NATS集群配置...")

    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    docker_dir = project_root / "docker"

    issues = []

    # 检查各环境的NATS配置
    environments = ["development", "testing", "production"]
    for env in environments:
        env_nats_dir = docker_dir / env / "nats"
        if not env_nats_dir.exists():
            print(f"❌ {env} 环境NATS配置目录不存在")
            issues.append(f"{env} 环境NATS配置目录")
            continue

        print(f"✅ {env} 环境NATS配置目录存在")

        # 检查配置文件
        if env == "development":
            # 开发环境只需要1个配置文件
            config_files = ["nats-1.conf"]
        else:
            # 测试和生产环境需要3个配置文件
            config_files = ["nats-1.conf", "nats-2.conf", "nats-3.conf"]

        for config_file in config_files:
            config_path = env_nats_dir / config_file
            if not check_file_exists(config_path, f"{env} NATS {config_file}"):
                issues.append(f"{env} NATS {config_file}")

    # 总结
    if not issues:
        print("✅ NATS集群配置验证通过")
        return True
    else:
        print("❌ NATS集群配置存在问题:")
        for issue in issues:
            print(f"   - {issue}")
        return False

def validate_port_conflicts():
    """验证端口冲突"""
    print("\n🔍 验证端口配置和冲突...")
    
    # 定义端口配置
    port_configs = {
        "development": {
            "NATS": 4222,
            "NATS监控": 8222,
            "MySQL": 3306,
            "API": 5000
        },
        "production": {
            "NATS": [14222, 14223, 14224],
            "NATS监控": [18222, 18223, 18224],
            "MySQL": 13306,
            "API": 15000
        },
        "testing": {
            "NATS": [24222, 24223, 24224],
            "NATS监控": [28222, 28223, 28224],
            "MySQL": 23306,
            "API": [25000, 25001]
        }
    }
    
    # 收集所有端口
    all_ports = set()
    conflicts = []
    
    for env_name, services in port_configs.items():
        print(f"\n📊 {env_name.upper()} 环境端口:")
        for service, ports in services.items():
            if isinstance(ports, list):
                for port in ports:
                    if port in all_ports:
                        conflicts.append(f"端口 {port} 在多个环境中使用")
                    all_ports.add(port)
                    print(f"   {service}: {port}")
            else:
                if ports in all_ports:
                    conflicts.append(f"端口 {ports} 在多个环境中使用")
                all_ports.add(ports)
                print(f"   {service}: {ports}")
    
    # 检查冲突
    if not conflicts:
        print("\n✅ 端口配置无冲突")
        return True
    else:
        print("\n❌ 发现端口冲突:")
        for conflict in conflicts:
            print(f"   - {conflict}")
        return False

def main():
    """主函数"""
    print("🚀 FlowCustomV1 Docker配置验证")
    print("=" * 50)
    
    # 验证各环境配置
    environments = {
        "development": {
            "NATS": 4222,
            "MySQL": 3306,
            "API": 5000
        },
        "production": {
            "NATS": 14222,
            "MySQL": 13306,
            "API": 15000
        },
        "testing": {
            "NATS": 24222,
            "MySQL": 23306,
            "API": 25000
        }
    }
    
    all_passed = True
    
    # 验证每个环境
    for env_name, expected_ports in environments.items():
        if not validate_environment_config(env_name, expected_ports):
            all_passed = False
    
    # 验证NATS配置
    if not validate_nats_config():
        all_passed = False
    
    # 验证端口冲突
    if not validate_port_conflicts():
        all_passed = False
    
    # 总结
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 所有Docker配置验证通过!")
        print("\n📋 环境总结:")
        print("✅ 开发环境: 单节点，默认端口")
        print("✅ 生产环境: 3节点集群，端口+10000")
        print("✅ 测试环境: 3节点集群，端口+20000")
        print("\n🚀 可以开始使用Docker环境!")
    else:
        print("❌ Docker配置验证失败，请修复上述问题")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
