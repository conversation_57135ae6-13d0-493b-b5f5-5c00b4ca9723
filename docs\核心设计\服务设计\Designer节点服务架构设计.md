# Designer节点服务架构设计文档

## 📋 文档信息

| 项目信息 | 详细内容 |
|---------|---------|
| **版本** | v0.0.1.4 |
| **创建日期** | 2025-01-05 |
| **文档类型** | Designer节点服务架构设计 |
| **适用范围** | FlowCustomV1分布式工作流系统 |

---

## 🎯 设计目标

### 核心目标
1. **专业化节点服务**: 创建专门负责工作流设计的独立节点服务
2. **分布式架构**: 基于NATS消息系统实现分布式工作流设计功能
3. **协作设计**: 支持多用户实时协作和冲突解决
4. **模板管理**: 提供工作流模板和版本控制功能

### 技术目标
- 基于现有的NodeDiscoveryService架构
- 集成NATS消息路由系统
- 实现工作流CRUD操作的消息化
- 支持独立部署和水平扩展

---

## 🏗️ 整体架构设计

### 架构层次图
```
┌─────────────────────────────────────────────────────────────┐
│                    Designer节点服务                          │
├─────────────────────────────────────────────────────────────┤
│  API层 (FlowCustomV1.Designer.Api)                         │
│  ├── DesignerController (HTTP API)                         │
│  ├── WebSocket Handler (实时协作)                           │
│  └── Health Check                                          │
├─────────────────────────────────────────────────────────────┤
│  应用层 (FlowCustomV1.Designer.Application)                │
│  ├── WorkflowDesignService                                 │
│  ├── TemplateManagementService                             │
│  ├── CollaborationService                                  │
│  └── VersionControlService                                 │
├─────────────────────────────────────────────────────────────┤
│  基础设施层 (FlowCustomV1.Designer.Infrastructure)         │
│  ├── NATS消息处理器                                         │
│  ├── 工作流存储适配器                                        │
│  ├── 模板存储适配器                                         │
│  └── 协作状态管理                                           │
├─────────────────────────────────────────────────────────────┤
│  核心层 (FlowCustomV1.Core - 共享)                         │
│  ├── IWorkflowDesignerService                              │
│  ├── 工作流模型                                             │
│  └── 消息模型                                               │
└─────────────────────────────────────────────────────────────┘
```

### 节点角色定义
```csharp
public enum NodeMode
{
    Standalone,
    Master,
    Worker,
    Designer,    // 新增：工作流设计节点
    Validator,   // 计划：工作流验证节点
    Executor     // 计划：工作流执行节点
}
```

---

## 🔧 核心组件设计

### 1. IWorkflowDesignerService 接口设计

```csharp
/// <summary>
/// 工作流设计服务接口
/// 提供分布式工作流设计功能
/// </summary>
public interface IWorkflowDesignerService
{
    #region 工作流设计
    
    /// <summary>
    /// 创建新工作流
    /// </summary>
    /// <param name="template">工作流模板</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>工作流定义</returns>
    Task<WorkflowDefinition> CreateWorkflowAsync(WorkflowTemplate template, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 更新工作流定义
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="workflow">工作流定义</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>更新结果</returns>
    Task<bool> UpdateWorkflowAsync(string workflowId, WorkflowDefinition workflow, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取工作流定义
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>工作流定义</returns>
    Task<WorkflowDefinition?> GetWorkflowAsync(string workflowId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 删除工作流
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>删除结果</returns>
    Task<bool> DeleteWorkflowAsync(string workflowId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取工作流列表
    /// </summary>
    /// <param name="query">查询条件</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>工作流列表</returns>
    Task<IReadOnlyList<WorkflowDefinition>> GetWorkflowsAsync(WorkflowQuery? query = null, CancellationToken cancellationToken = default);
    
    #endregion
    
    #region 模板管理
    
    /// <summary>
    /// 获取工作流模板列表
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>模板列表</returns>
    Task<IReadOnlyList<WorkflowTemplate>> GetTemplatesAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 创建工作流模板
    /// </summary>
    /// <param name="template">模板定义</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>创建结果</returns>
    Task<bool> CreateTemplateAsync(WorkflowTemplate template, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 更新工作流模板
    /// </summary>
    /// <param name="templateId">模板ID</param>
    /// <param name="template">模板定义</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>更新结果</returns>
    Task<bool> UpdateTemplateAsync(string templateId, WorkflowTemplate template, CancellationToken cancellationToken = default);
    
    #endregion
    
    #region 版本控制
    
    /// <summary>
    /// 获取工作流版本历史
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>版本历史</returns>
    Task<IReadOnlyList<WorkflowVersion>> GetVersionHistoryAsync(string workflowId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 创建工作流版本
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="versionInfo">版本信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>版本ID</returns>
    Task<string> CreateVersionAsync(string workflowId, WorkflowVersionInfo versionInfo, CancellationToken cancellationToken = default);
    
    #endregion
    
    #region 协作功能
    
    /// <summary>
    /// 广播设计变更
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="operation">设计操作</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>广播任务</returns>
    Task BroadcastDesignChangeAsync(string workflowId, DesignOperation operation, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取活跃协作者
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>协作者列表</returns>
    Task<IReadOnlyList<CollaboratorInfo>> GetActiveCollaboratorsAsync(string workflowId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 加入协作会话
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="collaborator">协作者信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>加入结果</returns>
    Task<bool> JoinCollaborationAsync(string workflowId, CollaboratorInfo collaborator, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 离开协作会话
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="collaboratorId">协作者ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>离开结果</returns>
    Task<bool> LeaveCollaborationAsync(string workflowId, string collaboratorId, CancellationToken cancellationToken = default);
    
    #endregion
    
    #region 事件
    
    /// <summary>
    /// 工作流创建事件
    /// </summary>
    event EventHandler<WorkflowCreatedEventArgs>? WorkflowCreated;
    
    /// <summary>
    /// 工作流更新事件
    /// </summary>
    event EventHandler<WorkflowUpdatedEventArgs>? WorkflowUpdated;
    
    /// <summary>
    /// 设计协作事件
    /// </summary>
    event EventHandler<DesignCollaborationEventArgs>? DesignCollaborationChanged;
    
    #endregion
}
```

### 2. 数据模型设计

```csharp
/// <summary>
/// 工作流模板
/// </summary>
public class WorkflowTemplate
{
    public string TemplateId { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public List<WorkflowNode> DefaultNodes { get; set; } = new();
    public List<WorkflowConnection> DefaultConnections { get; set; } = new();
    public Dictionary<string, object> DefaultParameters { get; set; } = new();
    public HashSet<string> Tags { get; set; } = new();
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public string CreatedBy { get; set; } = string.Empty;
}

/// <summary>
/// 工作流版本信息
/// </summary>
public class WorkflowVersion
{
    public string VersionId { get; set; } = string.Empty;
    public string WorkflowId { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public WorkflowDefinition Definition { get; set; } = new();
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public string CreatedBy { get; set; } = string.Empty;
    public bool IsActive { get; set; }
}

/// <summary>
/// 协作者信息
/// </summary>
public class CollaboratorInfo
{
    public string CollaboratorId { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public DateTime JoinedAt { get; set; } = DateTime.UtcNow;
    public DateTime LastActiveAt { get; set; } = DateTime.UtcNow;
    public CollaboratorRole Role { get; set; } = CollaboratorRole.Editor;
    public string CurrentSelection { get; set; } = string.Empty;
}

/// <summary>
/// 设计操作
/// </summary>
public class DesignOperation
{
    public string OperationId { get; set; } = Guid.NewGuid().ToString();
    public string WorkflowId { get; set; } = string.Empty;
    public string CollaboratorId { get; set; } = string.Empty;
    public DesignOperationType Type { get; set; }
    public Dictionary<string, object> Data { get; set; } = new();
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}
```

---

## 📨 NATS消息集成设计

### 消息主题规划
```
flowcustom.designer.workflows.create      - 创建工作流
flowcustom.designer.workflows.update      - 更新工作流
flowcustom.designer.workflows.delete      - 删除工作流
flowcustom.designer.workflows.get         - 获取工作流
flowcustom.designer.workflows.list        - 工作流列表

flowcustom.designer.templates.create      - 创建模板
flowcustom.designer.templates.update      - 更新模板
flowcustom.designer.templates.list        - 模板列表

flowcustom.designer.collaboration.join    - 加入协作
flowcustom.designer.collaboration.leave   - 离开协作
flowcustom.designer.collaboration.change  - 设计变更广播

flowcustom.designer.versions.create       - 创建版本
flowcustom.designer.versions.list         - 版本列表
```

### 消息模型设计
```csharp
/// <summary>
/// 工作流设计消息基类
/// </summary>
public abstract class WorkflowDesignMessage : IMessage
{
    public string MessageId { get; set; } = Guid.NewGuid().ToString();
    public abstract string MessageType { get; }
    public string SenderId { get; set; } = string.Empty;
    public string? TargetId { get; set; }
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime? ExpiresAt { get; set; }
    public MessagePriority Priority { get; set; } = MessagePriority.Normal;
    public object? Payload { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// 工作流CRUD操作消息
/// </summary>
public class WorkflowCrudMessage : WorkflowDesignMessage
{
    public override string MessageType => "workflow.crud";
    public CrudOperation Operation { get; set; }
    public string WorkflowId { get; set; } = string.Empty;
    public WorkflowDefinition? WorkflowDefinition { get; set; }
    public WorkflowQuery? Query { get; set; }
}

/// <summary>
/// 协作设计消息
/// </summary>
public class CollaborationMessage : WorkflowDesignMessage
{
    public override string MessageType => "design.collaboration";
    public string WorkflowId { get; set; } = string.Empty;
    public CollaboratorInfo? Collaborator { get; set; }
    public DesignOperation? Operation { get; set; }
    public CollaborationAction Action { get; set; }
}
```

---

## 🚀 部署架构设计

### Designer节点配置
```json
{
  "NodeConfig": {
    "NodeId": "designer-001",
    "NodeName": "Designer Node 001",
    "Mode": "Designer",
    "ClusterName": "flowcustom-cluster",
    "Capabilities": {
      "MaxConcurrentDesigns": 50,
      "SupportedFeatures": [
        "WorkflowDesign",
        "TemplateManagement", 
        "VersionControl",
        "Collaboration"
      ]
    }
  },
  "DesignerConfig": {
    "EnableCollaboration": true,
    "MaxCollaboratorsPerWorkflow": 10,
    "AutoSaveIntervalSeconds": 30,
    "VersionRetentionDays": 90,
    "EnableTemplateSharing": true
  },
  "NatsConfig": {
    "Servers": ["nats://nats-cluster:4222"],
    "ConnectionName": "FlowCustomV1-Designer"
  }
}
```

### 服务注册配置
```csharp
// Program.cs
services.AddSingleton<IWorkflowDesignerService, WorkflowDesignerService>();
services.AddSingleton<ITemplateManagementService, TemplateManagementService>();
services.AddSingleton<ICollaborationService, CollaborationService>();
services.AddSingleton<IVersionControlService, VersionControlService>();

// 注册为Designer节点
services.Configure<NodeDiscoveryConfiguration>(options =>
{
    options.NodeRole = "Designer";
    options.CapabilityTags = new List<string> 
    { 
        "workflow-design", 
        "template-management", 
        "collaboration" 
    };
});
```

这个架构设计为v0.0.1.4版本的Designer节点服务提供了完整的技术框架。接下来我将继续实现具体的服务组件。
