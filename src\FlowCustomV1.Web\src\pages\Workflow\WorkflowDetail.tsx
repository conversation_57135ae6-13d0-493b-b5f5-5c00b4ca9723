import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Card,
  Descriptions,
  Tag,
  Button,
  Space,
  Statistic,
  Row,
  Col,

  message,
  Popconfirm,
  Timeline,
  Alert
} from 'antd';
import {
  EditOutlined,
  DeleteOutlined,
  PlayCircleOutlined,
  CopyOutlined,
  ArrowLeftOutlined,
  HistoryOutlined,
  EyeOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { workflowApi, executionApi } from '@/services/workflow';
import type { WorkflowDefinition, WorkflowExecution } from '@/types/api';

const WorkflowDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [workflow, setWorkflow] = useState<WorkflowDefinition | null>(null);
  const [executions, setExecutions] = useState<WorkflowExecution[]>([]);
  const [stats, setStats] = useState({
    totalExecutions: 0,
    successfulExecutions: 0,
    failedExecutions: 0,
    averageExecutionTime: 0
  });

  // 加载工作流详情
  const loadWorkflowDetail = async () => {
    if (!id) return;

    try {
      setLoading(true);

      // 先只加载工作流数据，执行数据暂时跳过
      const workflowData = await workflowApi.getWorkflow(id);
      const executionData = { data: [] }; // 临时空数据

      if (workflowData) {
        setWorkflow(workflowData);
      }
      
      // 处理执行数据
      const executionList = Array.isArray(executionData) ? executionData : executionData.data || [];
      setExecutions(executionList.slice(0, 5)); // 只显示最近5条

      // 计算统计数据
      const total = executionList.length;
      const successful = executionList.filter((e: any) => e.status === 'Completed').length;
      const failed = executionList.filter((e: any) => e.status === 'Failed').length;
      const avgTime = executionList.length > 0
        ? executionList.reduce((sum: number, e: any) => sum + (e.duration || 0), 0) / executionList.length
        : 0;

      setStats({
        totalExecutions: total,
        successfulExecutions: successful,
        failedExecutions: failed,
        averageExecutionTime: Math.round(avgTime / 1000) // 转换为秒
      });
    } catch (error) {
      console.error('加载工作流详情失败:', error);
      message.error('加载工作流详情失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadWorkflowDetail();
  }, [id]);



  // 删除工作流
  const handleDelete = async () => {
    if (!workflow) return;
    
    try {
      await workflowApi.deleteWorkflow(workflow.workflowId);
      message.success('工作流删除成功');
      navigate('/workflow/list');
    } catch (error) {
      console.error('删除工作流失败:', error);
      message.error('删除工作流失败');
    }
  };

  // 复制工作流
  const handleCopy = async () => {
    if (!workflow) return;
    
    try {
      const copiedWorkflow = await workflowApi.copyWorkflow(workflow.workflowId, {
        name: `${workflow.name} - 副本`,
        description: `${workflow.description || ''} (复制)`
      });
      message.success('工作流复制成功');
      navigate(`/workflow/detail/${copiedWorkflow.workflowId}`);
    } catch (error) {
      console.error('复制工作流失败:', error);
      message.error('复制工作流失败');
    }
  };

  // 执行工作流
  const handleExecute = async () => {
    if (!workflow) return;
    
    try {
      await executionApi.startExecution(workflow.workflowId, {});
      message.success('工作流执行已启动');
      // 刷新执行历史
      loadWorkflowDetail();
    } catch (error) {
      console.error('执行工作流失败:', error);
      message.error('执行工作流失败');
    }
  };

  // 获取状态标签
  const getStatusTag = (status: string) => {
    const statusMap: Record<string, { color: string; text: string }> = {
      'Draft': { color: 'default', text: '草稿' },
      'Published': { color: 'success', text: '已发布' },
      'Archived': { color: 'warning', text: '已归档' },
    };
    const config = statusMap[status] || { color: 'default', text: '未知' };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="text-lg">加载工作流详情中...</div>
        </div>
      </div>
    );
  }

  if (!workflow) {
    return (
      <div>
        <Alert
          message="工作流不存在"
          description="请检查工作流ID是否正确，或者该工作流可能已被删除。"
          type="error"
          showIcon
          action={
            <Button size="small" onClick={() => navigate('/workflow/list')}>
              返回列表
            </Button>
          }
        />
      </div>
    );
  }

  return (
    <div>
      {/* 页面头部 */}
      <div className="mb-6">
        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center">
            <Button 
              icon={<ArrowLeftOutlined />} 
              onClick={() => navigate('/workflow/list')}
              className="mr-3"
            >
              返回列表
            </Button>
            <div>
              <h1 className="text-2xl font-semibold text-gray-900 mb-1">
                {workflow.name}
              </h1>
              <p className="text-gray-600 text-sm">工作流详细信息</p>
            </div>
          </div>
          <Space>
            <Button 
              icon={<CopyOutlined />}
              onClick={handleCopy}
            >
              复制
            </Button>
            <Button 
              icon={<EditOutlined />}
              onClick={() => navigate(`/workflow/edit/${workflow.workflowId}`)}
            >
              编辑
            </Button>
            <Button 
              icon={<SettingOutlined />}
              onClick={() => navigate(`/workflow/designer/${workflow.workflowId}`)}
            >
              设计器
            </Button>
            <Button 
              type="primary"
              icon={<PlayCircleOutlined />}
              onClick={handleExecute}
              disabled={workflow.publishStatus !== 'Published'}
            >
              执行
            </Button>
            <Popconfirm
              title="确定要删除这个工作流吗？"
              description="删除后将无法恢复，请谨慎操作。"
              onConfirm={handleDelete}
              okText="确定"
              cancelText="取消"
            >
              <Button 
                danger
                icon={<DeleteOutlined />}
              >
                删除
              </Button>
            </Popconfirm>
          </Space>
        </div>
      </div>

      {/* 统计卡片 */}
      <Row gutter={16} className="mb-6">
        <Col span={6}>
          <Card>
            <Statistic
              title="总执行次数"
              value={stats.totalExecutions}
              prefix={<HistoryOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="成功执行"
              value={stats.successfulExecutions}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="失败执行"
              value={stats.failedExecutions}
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="平均执行时间"
              value={stats.averageExecutionTime}
              suffix="秒"
            />
          </Card>
        </Col>
      </Row>

      {/* 基本信息 */}
      <Card title="基本信息" className="mb-4">
        <Descriptions column={3} bordered>
          <Descriptions.Item label="工作流名称">{workflow.name}</Descriptions.Item>
          <Descriptions.Item label="状态">{getStatusTag(workflow.publishStatus)}</Descriptions.Item>
          <Descriptions.Item label="版本">{workflow.version}</Descriptions.Item>
          <Descriptions.Item label="创建时间">
            {new Date(workflow.createdAt).toLocaleString()}
          </Descriptions.Item>
          <Descriptions.Item label="更新时间">
            {(workflow as any).updatedAt ? new Date((workflow as any).updatedAt).toLocaleString() : '未更新'}
          </Descriptions.Item>
          <Descriptions.Item label="创建者">{workflow.createdBy || '系统'}</Descriptions.Item>
          <Descriptions.Item label="描述" span={3}>
            {workflow.description || '暂无描述'}
          </Descriptions.Item>
        </Descriptions>
      </Card>

      <Row gutter={16}>
        {/* 工作流定义 */}
        <Col span={12}>
          <Card title="工作流定义" style={{ height: '400px' }}>
            <div style={{ height: '300px', overflowY: 'auto', margin: '0 0 16px 0' }} className="bg-gray-50 p-4 rounded">
              <pre className="text-sm whitespace-pre-wrap">
                {JSON.stringify((workflow as any).definition || workflow, null, 2)}
              </pre>
            </div>
          </Card>
        </Col>

        {/* 执行历史 */}
        <Col span={12}>
          <Card
            title="最近执行历史"
            style={{ height: '400px' }}
            extra={
              <Button
                size="small"
                icon={<EyeOutlined />}
                onClick={() => navigate('/execution/monitor')}
              >
                查看全部
              </Button>
            }
          >
            <div style={{ height: '320px', overflowY: 'auto' }}>
              {executions.length > 0 ? (
                <Timeline
                  items={executions.slice(0, 5).map(execution => ({
                    color: execution.status === 'Completed' ? 'green' :
                           execution.status === 'Failed' ? 'red' : 'blue',
                    children: (
                      <div>
                        <div className="font-medium">
                          {execution.status === 'Completed' ? '执行成功' :
                           execution.status === 'Failed' ? '执行失败' : '执行中'}
                        </div>
                        <div className="text-sm text-gray-500">
                          {new Date(execution.startedAt).toLocaleString()}
                        </div>
                        {execution.duration && (
                          <div className="text-sm text-gray-500">
                            耗时: {Math.round(execution.duration / 1000)}秒
                          </div>
                        )}
                      </div>
                    )
                  }))}
                />
              ) : (
                <div className="flex items-center justify-center h-full text-gray-500">
                  <div className="text-center">
                    <div className="text-lg mb-2">📋</div>
                    <div>暂无执行历史</div>
                  </div>
                </div>
              )}
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default WorkflowDetail;
