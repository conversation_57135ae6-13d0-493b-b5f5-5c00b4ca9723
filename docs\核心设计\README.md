# 核心设计文档

## 📋 目录概述

本目录包含FlowCustomV1系统的核心技术设计文档，按照设计类型进行分类组织。

---

## 🏗️ 架构设计

系统整体架构和分布式集群设计相关文档。

- 🏛️ [系统架构设计文档](架构设计/系统架构设计文档.md) - 整体系统架构设计
- 🌐 [分布式集群架构设计](架构设计/分布式集群架构设计.md) - 分布式集群架构详细设计
- 🔧 [架构兼容性设计文档](架构设计/架构兼容性设计文档.md) - 架构兼容性和演进策略
- 📖 [架构澄清说明](架构设计/架构澄清说明.md) - 架构设计澄清和说明

---

## 🎨 服务设计

各个服务模块的详细设计文档。

- 🎨 [Designer节点服务架构设计](服务设计/Designer节点服务架构设计.md) - 设计器节点详细设计
- 🔌 [API接口设计文档](服务设计/API接口设计文档.md) - RESTful API接口设计

---

## ⚙️ 配置管理

系统配置和参数管理相关设计文档。

- ⚙️ [参数配置体系设计文档](配置管理/参数配置体系设计文档.md) - 完整的配置管理体系
- 📚 [配置参数快速参考](配置管理/配置参数快速参考.md) - 配置参数速查手册

---

## 📊 文档统计

- **架构设计文档**: 4个
- **服务设计文档**: 2个  
- **配置管理文档**: 2个
- **总计**: 8个核心设计文档

---

## 🔗 相关文档

- [架构决策记录](../架构决策/ADR-索引.md) - 重要架构决策的记录和追溯
- [软件需求规格说明书](../需求管理/软件需求规格说明书.md) - 系统需求定义
- [测试策略文档](../测试管理/测试策略文档.md) - 测试策略和方法

---

**核心设计文档为FlowCustomV1系统的技术实现提供了完整的设计指导，确保系统架构的一致性和可维护性。**
