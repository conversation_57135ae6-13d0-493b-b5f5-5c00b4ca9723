services:
  mysql-prod:
    image: mysql:8.0
    container_name: mysql-prod
    hostname: mysql-prod
    restart: unless-stopped
    ports:
      - "13306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: TO_BE_CONFIGURED_ON_DEPLOYMENT
      MYSQL_DATABASE: flowcustom_prod
      MYSQL_USER: flowcustom
      MYSQL_PASSWORD: TO_BE_CONFIGURED_ON_DEPLOYMENT
      MYSQL_CHARACTER_SET_SERVER: utf8mb4
      MYSQL_COLLATION_SERVER: utf8mb4_unicode_ci
    command: [
      "mysqld",
      "--lower-case-table-names=1",
      "--character-set-server=utf8mb4",
      "--collation-server=utf8mb4_unicode_ci",
      "--default-authentication-plugin=mysql_native_password",
      "--ssl-ca=/etc/mysql/certs/ca.pem",
      "--ssl-cert=/etc/mysql/certs/server-cert.pem",
      "--ssl-key=/etc/mysql/certs/server-key.pem",
      "--require-secure-transport=ON"
    ]
    volumes:
      - mysql_prod_data:/var/lib/mysql
      - ./init:/docker-entrypoint-initdb.d
      - ./certs:/etc/mysql/certs:ro
    networks:
      - flowcustom-prod
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-pTO_BE_CONFIGURED_ON_DEPLOYMENT", "--ssl"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

volumes:
  mysql_prod_data:
    driver: local

networks:
  flowcustom-prod:
    driver: bridge
