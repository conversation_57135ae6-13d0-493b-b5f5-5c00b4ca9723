namespace FlowCustomV1.Infrastructure.Configuration;

/// <summary>
/// 数据库配置选项
/// </summary>
public class DatabaseOptions
{
    /// <summary>
    /// 配置节名称
    /// </summary>
    public const string SectionName = "Database";

    /// <summary>
    /// 数据库连接字符串
    /// </summary>
    public string ConnectionString { get; set; } = "Server=localhost;Port=3306;Database=flowcustom;Uid=root;Pwd=password;";

    /// <summary>
    /// 数据库提供程序 (MySQL)
    /// </summary>
    public string Provider { get; set; } = "MySQL";

    /// <summary>
    /// 是否自动初始化数据库
    /// </summary>
    public bool AutoInitialize { get; set; } = true;

    /// <summary>
    /// 是否自动迁移数据库
    /// </summary>
    public bool AutoMigrate { get; set; } = true;

    /// <summary>
    /// 迁移前是否备份数据库
    /// </summary>
    public bool BackupBeforeMigration { get; set; } = true;

    /// <summary>
    /// 数据库连接失败时是否降级到内存模式
    /// </summary>
    public bool FallbackToInMemory { get; set; } = true;

    /// <summary>
    /// 数据库健康检查间隔
    /// </summary>
    public TimeSpan HealthCheckInterval { get; set; } = TimeSpan.FromMinutes(5);

    /// <summary>
    /// 迁移配置选项
    /// </summary>
    public MigrationOptions Migration { get; set; } = new();

    /// <summary>
    /// 连接池配置选项
    /// </summary>
    public ConnectionPoolOptions ConnectionPool { get; set; } = new();
}

/// <summary>
/// 迁移配置选项
/// </summary>
public class MigrationOptions
{
    /// <summary>
    /// 迁移超时时间
    /// </summary>
    public TimeSpan Timeout { get; set; } = TimeSpan.FromMinutes(2);

    /// <summary>
    /// 重试次数
    /// </summary>
    public int RetryCount { get; set; } = 3;

    /// <summary>
    /// 是否创建备份
    /// </summary>
    public bool CreateBackup { get; set; } = true;

    /// <summary>
    /// 备份目录
    /// </summary>
    public string BackupDirectory { get; set; } = "backups";

    /// <summary>
    /// 是否验证迁移结果
    /// </summary>
    public bool ValidateAfterMigration { get; set; } = true;
}

/// <summary>
/// 连接池配置选项
/// </summary>
public class ConnectionPoolOptions
{
    /// <summary>
    /// 最大连接数
    /// </summary>
    public int MaxPoolSize { get; set; } = 100;

    /// <summary>
    /// 最小连接数
    /// </summary>
    public int MinPoolSize { get; set; } = 5;

    /// <summary>
    /// 连接超时时间（秒）
    /// </summary>
    public int ConnectionTimeout { get; set; } = 30;

    /// <summary>
    /// 命令超时时间（秒）
    /// </summary>
    public int CommandTimeout { get; set; } = 30;

    /// <summary>
    /// 连接生命周期（秒）
    /// </summary>
    public int ConnectionLifetime { get; set; } = 300;
}