namespace FlowCustomV1.Core.Models.Plugins;

/// <summary>
/// 插件加载事件参数
/// </summary>
public class PluginLoadedEventArgs : EventArgs
{
    /// <summary>
    /// 插件信息
    /// </summary>
    public PluginInfo PluginInfo { get; set; } = default!;

    /// <summary>
    /// 加载时间（毫秒）
    /// </summary>
    public long LoadTimeMs { get; set; }

    /// <summary>
    /// 加载时间戳
    /// </summary>
    public DateTime LoadedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 插件卸载事件参数
/// </summary>
public class PluginUnloadedEventArgs : EventArgs
{
    /// <summary>
    /// 节点类型
    /// </summary>
    public string NodeType { get; set; } = string.Empty;

    /// <summary>
    /// 插件信息
    /// </summary>
    public PluginInfo? PluginInfo { get; set; }

    /// <summary>
    /// 卸载时间（毫秒）
    /// </summary>
    public long UnloadTimeMs { get; set; }

    /// <summary>
    /// 卸载时间戳
    /// </summary>
    public DateTime UnloadedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 卸载原因
    /// </summary>
    public string Reason { get; set; } = string.Empty;
}

/// <summary>
/// 插件错误事件参数
/// </summary>
public class PluginErrorEventArgs : EventArgs
{
    /// <summary>
    /// 节点类型
    /// </summary>
    public string NodeType { get; set; } = string.Empty;

    /// <summary>
    /// 错误消息
    /// </summary>
    public string ErrorMessage { get; set; } = string.Empty;

    /// <summary>
    /// 异常信息
    /// </summary>
    public Exception? Exception { get; set; }

    /// <summary>
    /// 错误类型
    /// </summary>
    public PluginErrorType ErrorType { get; set; }

    /// <summary>
    /// 错误时间戳
    /// </summary>
    public DateTime OccurredAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 错误严重程度
    /// </summary>
    public PluginErrorSeverity Severity { get; set; }
}

/// <summary>
/// 编译完成事件参数
/// </summary>
public class CompilationCompletedEventArgs : EventArgs
{
    /// <summary>
    /// 编译结果
    /// </summary>
    public CompilationResult Result { get; set; } = default!;

    /// <summary>
    /// 编译时间戳
    /// </summary>
    public DateTime CompletedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 编译错误事件参数
/// </summary>
public class CompilationErrorEventArgs : EventArgs
{
    /// <summary>
    /// 节点类型
    /// </summary>
    public string NodeType { get; set; } = string.Empty;

    /// <summary>
    /// 编译错误列表
    /// </summary>
    public List<CompilationError> Errors { get; set; } = new();

    /// <summary>
    /// 源代码
    /// </summary>
    public string SourceCode { get; set; } = string.Empty;

    /// <summary>
    /// 错误时间戳
    /// </summary>
    public DateTime OccurredAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 插件错误类型
/// </summary>
public enum PluginErrorType
{
    /// <summary>
    /// 加载错误
    /// </summary>
    LoadError,

    /// <summary>
    /// 编译错误
    /// </summary>
    CompilationError,

    /// <summary>
    /// 执行错误
    /// </summary>
    ExecutionError,

    /// <summary>
    /// 配置错误
    /// </summary>
    ConfigurationError,

    /// <summary>
    /// 依赖错误
    /// </summary>
    DependencyError,

    /// <summary>
    /// 安全错误
    /// </summary>
    SecurityError,

    /// <summary>
    /// 资源错误
    /// </summary>
    ResourceError,

    /// <summary>
    /// 网络错误
    /// </summary>
    NetworkError,

    /// <summary>
    /// 未知错误
    /// </summary>
    Unknown
}

/// <summary>
/// 插件错误严重程度
/// </summary>
public enum PluginErrorSeverity
{
    /// <summary>
    /// 信息
    /// </summary>
    Info,

    /// <summary>
    /// 警告
    /// </summary>
    Warning,

    /// <summary>
    /// 错误
    /// </summary>
    Error,

    /// <summary>
    /// 严重错误
    /// </summary>
    Critical,

    /// <summary>
    /// 致命错误
    /// </summary>
    Fatal
}
