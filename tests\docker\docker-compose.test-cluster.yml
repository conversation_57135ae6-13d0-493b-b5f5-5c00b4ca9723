version: '3.8'

# FlowCustomV1 v0.0.1.7 真实多节点集群测试环境
# 基于Docker构建完整的分布式测试集群

services:
  # NATS集群 - 消息中间件
  nats-1:
    image: nats:2.11.8-alpine
    container_name: flowcustom-test-nats-1
    ports:
      - "14222:4222"
      - "18222:8222"
    volumes:
      - ./nats-config/nats-1.conf:/etc/nats/nats-server.conf
    command: ["-c", "/etc/nats/nats-server.conf"]
    networks:
      - flowcustom-test-network
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8222/healthz"]
      interval: 10s
      timeout: 5s
      retries: 3

  nats-2:
    image: nats:2.11.8-alpine
    container_name: flowcustom-test-nats-2
    ports:
      - "14223:4222"
      - "18223:8222"
    volumes:
      - ./nats-config/nats-2.conf:/etc/nats/nats-server.conf
    command: ["-c", "/etc/nats/nats-server.conf"]
    networks:
      - flowcustom-test-network
    depends_on:
      - nats-1
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8222/healthz"]
      interval: 10s
      timeout: 5s
      retries: 3

  nats-3:
    image: nats:2.11.8-alpine
    container_name: flowcustom-test-nats-3
    ports:
      - "14224:4222"
      - "18224:8222"
    volumes:
      - ./nats-config/nats-3.conf:/etc/nats/nats-server.conf
    command: ["-c", "/etc/nats/nats-server.conf"]
    networks:
      - flowcustom-test-network
    depends_on:
      - nats-1
      - nats-2
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8222/healthz"]
      interval: 10s
      timeout: 5s
      retries: 3

  # MySQL数据库 - 持久化存储
  mysql-test:
    image: mysql:8.0
    container_name: flowcustom-test-mysql
    environment:
      MYSQL_ROOT_PASSWORD: TestPassword123!
      MYSQL_DATABASE: flowcustom_test
      MYSQL_USER: flowcustom
      MYSQL_PASSWORD: TestPassword123!
    ports:
      - "13306:3306"
    volumes:
      - mysql-test-data:/var/lib/mysql
      - ./mysql-config/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - flowcustom-test-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-pTestPassword123!"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Master节点 - 任务调度和集群管理
  master-node-1:
    build:
      context: ../../
      dockerfile: tests/docker/Dockerfile.test-node
      args:
        NODE_ROLE: Master
        NODE_ID: master-1
        REGION: Beijing
    container_name: flowcustom-test-master-1
    environment:
      - NODE_ROLE=Master
      - NODE_ID=master-1
      - NODE_NAME=TestMaster1
      - REGION=Beijing
      - DATACENTER=Beijing-DC1
      - NATS_SERVERS=nats://nats-1:4222,nats://nats-2:4222,nats://nats-3:4222
      - MYSQL_CONNECTION=Server=mysql-test;Database=flowcustom_test;Uid=flowcustom;Pwd=TestPassword123!;
      - ASPNETCORE_ENVIRONMENT=Testing
      - ASPNETCORE_URLS=http://+:5000
    ports:
      - "15001:5000"
    depends_on:
      nats-1:
        condition: service_healthy
      nats-2:
        condition: service_healthy
      nats-3:
        condition: service_healthy
      mysql-test:
        condition: service_healthy
    networks:
      - flowcustom-test-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 15s
      timeout: 10s
      retries: 3

  master-node-2:
    build:
      context: ../../
      dockerfile: tests/docker/Dockerfile.test-node
      args:
        NODE_ROLE: Master
        NODE_ID: master-2
        REGION: Shanghai
    container_name: flowcustom-test-master-2
    environment:
      - NODE_ROLE=Master
      - NODE_ID=master-2
      - NODE_NAME=TestMaster2
      - REGION=Shanghai
      - DATACENTER=Shanghai-DC1
      - NATS_SERVERS=nats://nats-1:4222,nats://nats-2:4222,nats://nats-3:4222
      - MYSQL_CONNECTION=Server=mysql-test;Database=flowcustom_test;Uid=flowcustom;Pwd=TestPassword123!;
      - ASPNETCORE_ENVIRONMENT=Testing
      - ASPNETCORE_URLS=http://+:5000
    ports:
      - "15002:5000"
    depends_on:
      - master-node-1
    networks:
      - flowcustom-test-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 15s
      timeout: 10s
      retries: 3

  # Worker节点 - 任务执行
  worker-node-1:
    build:
      context: ../../
      dockerfile: tests/docker/Dockerfile.test-node
      args:
        NODE_ROLE: Worker
        NODE_ID: worker-1
        REGION: Beijing
    container_name: flowcustom-test-worker-1
    environment:
      - NODE_ROLE=Worker
      - NODE_ID=worker-1
      - NODE_NAME=TestWorker1
      - REGION=Beijing
      - DATACENTER=Beijing-DC1
      - NATS_SERVERS=nats://nats-1:4222,nats://nats-2:4222,nats://nats-3:4222
      - MYSQL_CONNECTION=Server=mysql-test;Database=flowcustom_test;Uid=flowcustom;Pwd=TestPassword123!;
      - ASPNETCORE_ENVIRONMENT=Testing
      - ASPNETCORE_URLS=http://+:5000
      - WORKER_MAX_TASKS=20
      - WORKER_CPU_CORES=4
      - WORKER_MEMORY_MB=8192
    ports:
      - "15011:5000"
    depends_on:
      - master-node-1
    networks:
      - flowcustom-test-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 15s
      timeout: 10s
      retries: 3

  worker-node-2:
    build:
      context: ../../
      dockerfile: tests/docker/Dockerfile.test-node
      args:
        NODE_ROLE: Worker
        NODE_ID: worker-2
        REGION: Beijing
    container_name: flowcustom-test-worker-2
    environment:
      - NODE_ROLE=Worker
      - NODE_ID=worker-2
      - NODE_NAME=TestWorker2
      - REGION=Beijing
      - DATACENTER=Beijing-DC1
      - NATS_SERVERS=nats://nats-1:4222,nats://nats-2:4222,nats://nats-3:4222
      - MYSQL_CONNECTION=Server=mysql-test;Database=flowcustom_test;Uid=flowcustom;Pwd=TestPassword123!;
      - ASPNETCORE_ENVIRONMENT=Testing
      - ASPNETCORE_URLS=http://+:5000
      - WORKER_MAX_TASKS=20
      - WORKER_CPU_CORES=8
      - WORKER_MEMORY_MB=16384
    ports:
      - "15012:5000"
    depends_on:
      - master-node-1
    networks:
      - flowcustom-test-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 15s
      timeout: 10s
      retries: 3

  worker-node-3:
    build:
      context: ../../
      dockerfile: tests/docker/Dockerfile.test-node
      args:
        NODE_ROLE: Worker
        NODE_ID: worker-3
        REGION: Shanghai
    container_name: flowcustom-test-worker-3
    environment:
      - NODE_ROLE=Worker
      - NODE_ID=worker-3
      - NODE_NAME=TestWorker3
      - REGION=Shanghai
      - DATACENTER=Shanghai-DC1
      - NATS_SERVERS=nats://nats-1:4222,nats://nats-2:4222,nats://nats-3:4222
      - MYSQL_CONNECTION=Server=mysql-test;Database=flowcustom_test;Uid=flowcustom;Pwd=TestPassword123!;
      - ASPNETCORE_ENVIRONMENT=Testing
      - ASPNETCORE_URLS=http://+:5000
      - WORKER_MAX_TASKS=15
      - WORKER_CPU_CORES=6
      - WORKER_MEMORY_MB=12288
    ports:
      - "15013:5000"
    depends_on:
      - master-node-1
    networks:
      - flowcustom-test-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 15s
      timeout: 10s
      retries: 3

  # Designer节点 - 工作流设计
  designer-node-1:
    build:
      context: ../../
      dockerfile: tests/docker/Dockerfile.test-node
      args:
        NODE_ROLE: Designer
        NODE_ID: designer-1
        REGION: Beijing
    container_name: flowcustom-test-designer-1
    environment:
      - NODE_ROLE=Designer
      - NODE_ID=designer-1
      - NODE_NAME=TestDesigner1
      - REGION=Beijing
      - DATACENTER=Beijing-DC1
      - NATS_SERVERS=nats://nats-1:4222,nats://nats-2:4222,nats://nats-3:4222
      - MYSQL_CONNECTION=Server=mysql-test;Database=flowcustom_test;Uid=flowcustom;Pwd=TestPassword123!;
      - ASPNETCORE_ENVIRONMENT=Testing
      - ASPNETCORE_URLS=http://+:5000
    ports:
      - "15021:5000"
    depends_on:
      - master-node-1
    networks:
      - flowcustom-test-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 15s
      timeout: 10s
      retries: 3

  designer-node-2:
    build:
      context: ../../
      dockerfile: tests/docker/Dockerfile.test-node
      args:
        NODE_ROLE: Designer
        NODE_ID: designer-2
        REGION: Shanghai
    container_name: flowcustom-test-designer-2
    environment:
      - NODE_ROLE=Designer
      - NODE_ID=designer-2
      - NODE_NAME=TestDesigner2
      - REGION=Shanghai
      - DATACENTER=Shanghai-DC1
      - NATS_SERVERS=nats://nats-1:4222,nats://nats-2:4222,nats://nats-3:4222
      - MYSQL_CONNECTION=Server=mysql-test;Database=flowcustom_test;Uid=flowcustom;Pwd=TestPassword123!;
      - ASPNETCORE_ENVIRONMENT=Testing
      - ASPNETCORE_URLS=http://+:5000
    ports:
      - "15022:5000"
    depends_on:
      - master-node-1
    networks:
      - flowcustom-test-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 15s
      timeout: 10s
      retries: 3

  # Validator节点 - 工作流验证
  validator-node-1:
    build:
      context: ../../
      dockerfile: tests/docker/Dockerfile.test-node
      args:
        NODE_ROLE: Validator
        NODE_ID: validator-1
        REGION: Beijing
    container_name: flowcustom-test-validator-1
    environment:
      - NODE_ROLE=Validator
      - NODE_ID=validator-1
      - NODE_NAME=TestValidator1
      - REGION=Beijing
      - DATACENTER=Beijing-DC1
      - NATS_SERVERS=nats://nats-1:4222,nats://nats-2:4222,nats://nats-3:4222
      - MYSQL_CONNECTION=Server=mysql-test;Database=flowcustom_test;Uid=flowcustom;Pwd=TestPassword123!;
      - ASPNETCORE_ENVIRONMENT=Testing
      - ASPNETCORE_URLS=http://+:5000
    ports:
      - "15031:5000"
    depends_on:
      - master-node-1
    networks:
      - flowcustom-test-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 15s
      timeout: 10s
      retries: 3

  validator-node-2:
    build:
      context: ../../
      dockerfile: tests/docker/Dockerfile.test-node
      args:
        NODE_ROLE: Validator
        NODE_ID: validator-2
        REGION: Shanghai
    container_name: flowcustom-test-validator-2
    environment:
      - NODE_ROLE=Validator
      - NODE_ID=validator-2
      - NODE_NAME=TestValidator2
      - REGION=Shanghai
      - DATACENTER=Shanghai-DC1
      - NATS_SERVERS=nats://nats-1:4222,nats://nats-2:4222,nats://nats-3:4222
      - MYSQL_CONNECTION=Server=mysql-test;Database=flowcustom_test;Uid=flowcustom;Pwd=TestPassword123!;
      - ASPNETCORE_ENVIRONMENT=Testing
      - ASPNETCORE_URLS=http://+:5000
    ports:
      - "15032:5000"
    depends_on:
      - master-node-1
    networks:
      - flowcustom-test-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 15s
      timeout: 10s
      retries: 3

  # 测试协调器 - 执行测试用例
  test-coordinator:
    build:
      context: ../../
      dockerfile: tests/docker/Dockerfile.test-coordinator
    container_name: flowcustom-test-coordinator
    environment:
      - TEST_ENVIRONMENT=Docker
      - CLUSTER_NODES=master-node-1:5000,master-node-2:5000,worker-node-1:5000,worker-node-2:5000,worker-node-3:5000,designer-node-1:5000,designer-node-2:5000,validator-node-1:5000,validator-node-2:5000
      - NATS_SERVERS=nats://nats-1:4222,nats://nats-2:4222,nats://nats-3:4222
      - MYSQL_CONNECTION=Server=mysql-test;Database=flowcustom_test;Uid=flowcustom;Pwd=TestPassword123!;
      - TEST_RESULTS_PATH=/app/test-results
    volumes:
      - ./test-results:/app/test-results
    depends_on:
      master-node-1:
        condition: service_healthy
      master-node-2:
        condition: service_healthy
      worker-node-1:
        condition: service_healthy
      worker-node-2:
        condition: service_healthy
      worker-node-3:
        condition: service_healthy
      designer-node-1:
        condition: service_healthy
      designer-node-2:
        condition: service_healthy
      validator-node-1:
        condition: service_healthy
      validator-node-2:
        condition: service_healthy
    networks:
      - flowcustom-test-network
    profiles:
      - test

  # 监控和可视化
  prometheus:
    image: prom/prometheus:latest
    container_name: flowcustom-test-prometheus
    ports:
      - "19090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
    networks:
      - flowcustom-test-network
    profiles:
      - monitoring

  grafana:
    image: grafana/grafana:latest
    container_name: flowcustom-test-grafana
    ports:
      - "13000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - grafana-test-data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - flowcustom-test-network
    profiles:
      - monitoring

networks:
  flowcustom-test-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  mysql-test-data:
    driver: local
  grafana-test-data:
    driver: local
