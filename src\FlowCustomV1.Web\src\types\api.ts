// API 响应类型定义

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  errorCode?: string;
  timestamp?: string;
}

export interface PagedResponse<T = any> {
  data: T[];
  totalCount: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export interface PaginationParams {
  pageIndex?: number;
  pageSize?: number;
  sortField?: string;
  sortOrder?: 'asc' | 'desc';
}

// 工作流相关类型 - 完全对齐后端数据结构
export interface WorkflowDefinition {
  workflowId: string;
  name: string;
  description?: string;
  version: string;
  author?: string;
  createdAt: string;
  lastModifiedAt: string;
  isActive: boolean;
  isPublished?: boolean;
  publishedAt?: string;
  publishedBy?: string;
  publishStatus: string; // 后端返回字符串，不是枚举
  createdBy?: string;
  lastModifiedBy?: string;
  nodes?: any[]; // 后端返回的节点数组
  connections?: any[]; // 后端返回的连接数组
  inputParameters?: any[];
  outputParameters?: any[];
  configuration?: any;
  tags?: string[];
  metadata?: any;
}

// 发布状态枚举
export enum PublishStatus {
  Draft = 'Draft',
  Published = 'Published',
  Deprecated = 'Deprecated',
  Archived = 'Archived'
}

// 发布请求接口
export interface PublishWorkflowRequest {
  workflowId: string;
  version?: string;
  releaseNotes?: string;
  publishToEnvironments?: string[];
}

// 发布响应接口
export interface PublishWorkflowResponse {
  success: boolean;
  publishedVersion: string;
  publishedAt: string;
  message?: string;
}

export interface WorkflowNode {
  nodeId: string;
  nodeType: string;
  name: string;
  description?: string;
  position: { x: number; y: number };
  configuration: Record<string, any>;
  inputPorts: NodePort[];
  outputPorts: NodePort[];
}

export interface NodePort {
  portId: string;
  name: string;
  dataType: string;
  isRequired: boolean;
}

export interface WorkflowConnection {
  connectionId: string;
  sourceNodeId: string;
  sourcePortId: string;
  targetNodeId: string;
  targetPortId: string;
}

export interface WorkflowVariable {
  name: string;
  dataType: string;
  defaultValue?: any;
  description?: string;
}

// 执行相关类型
export interface WorkflowExecution {
  executionId: string;
  workflowId: string;
  workflowName: string;
  status: ExecutionStatus;
  startedAt: string;
  completedAt?: string;
  duration?: number;
  inputData?: Record<string, any>;
  outputData?: Record<string, any>;
  errorMessage?: string;
  progress: number;
  currentNodeId?: string;
}

// 执行统计类型
export interface ExecutionStats {
  total: number;
  running?: number;
  success: number;
  failed: number;
  averageDuration?: number;
}

export enum ExecutionStatus {
  Pending = 'Pending',
  Running = 'Running',
  Completed = 'Completed',
  Failed = 'Failed',
  Cancelled = 'Cancelled',
  Paused = 'Paused'
}

// 集群相关类型
export interface NodeInfo {
  nodeId: string;
  nodeName: string;
  nodeType: NodeType;
  roles: NodeRole[];
  status: NodeStatus;
  address: string;
  port: number;
  version: string;
  startedAt: string;
  lastHeartbeat: string;
  loadInfo: NodeLoadInfo;
  capabilities: string[];
  tags: Record<string, string>;
}

export enum NodeType {
  Master = 'Master',
  Worker = 'Worker',
  Hybrid = 'Hybrid'
}

export enum NodeRole {
  Api = 'Api',
  Designer = 'Designer',
  Validator = 'Validator',
  Executor = 'Executor',
  Monitor = 'Monitor',
  Scheduler = 'Scheduler',
  Storage = 'Storage'
}

export enum NodeStatus {
  Online = 'Online',
  Offline = 'Offline',
  Busy = 'Busy',
  Maintenance = 'Maintenance'
}

export interface NodeLoadInfo {
  cpuUsage: number;
  memoryUsage: number;
  diskUsage: number;
  networkUsage: number;
  activeExecutions: number;
  maxExecutions: number;
}

export interface ClusterStats {
  clusterName: string;
  totalNodes: number;
  onlineNodes: number;
  totalExecutions: number;
  runningExecutions: number;
  completedExecutions: number;
  failedExecutions: number;
  averageExecutionTime: number;
  systemHealth: 'Healthy' | 'Warning' | 'Critical';
}

// 设计器相关类型
export interface DesignerSession {
  sessionId: string;
  workflowId: string;
  participants: Participant[];
  createdAt: string;
  lastActivity: string;
  isActive: boolean;
}

export interface Participant {
  userId: string;
  userName: string;
  role: 'Owner' | 'Editor' | 'Viewer';
  joinedAt: string;
  lastActivity: string;
  isOnline: boolean;
}

// 验证相关类型
export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
  validatedAt: string;
  validationDuration: number;
}

export interface ValidationError {
  errorCode: string;
  message: string;
  nodeId?: string;
  severity: 'Error' | 'Warning' | 'Info';
}

export interface ValidationWarning {
  warningCode: string;
  message: string;
  nodeId?: string;
  suggestion?: string;
}
