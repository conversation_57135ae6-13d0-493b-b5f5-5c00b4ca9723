using FlowCustomV1.Core.Models.Cluster;
using FlowCustomV1.Core.Interfaces.Scheduling;

namespace FlowCustomV1.Core.Models.Scheduling;

/// <summary>
/// 任务分发完成事件参数
/// </summary>
public class TaskDistributedEventArgs : EventArgs
{
    /// <summary>
    /// 分发的任务
    /// </summary>
    public DistributedTask Task { get; set; } = null!;

    /// <summary>
    /// 分发结果
    /// </summary>
    public TaskDistributionResult Result { get; set; } = null!;

    /// <summary>
    /// 分配的节点
    /// </summary>
    public NodeInfo? AssignedNode { get; set; }

    /// <summary>
    /// 分发策略
    /// </summary>
    public TaskDistributionStrategy Strategy { get; set; }

    /// <summary>
    /// 分发时间
    /// </summary>
    public DateTime DistributedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 候选节点数量
    /// </summary>
    public int CandidateNodeCount { get; set; }

    /// <summary>
    /// 分发耗时（毫秒）
    /// </summary>
    public long DistributionTimeMs { get; set; }

    /// <summary>
    /// 选择评分
    /// </summary>
    public double SelectionScore { get; set; }

    /// <summary>
    /// 是否为重新分发
    /// </summary>
    public bool IsRedistribution { get; set; }

    /// <summary>
    /// 附加信息
    /// </summary>
    public Dictionary<string, object> AdditionalInfo { get; set; } = new();
}

/// <summary>
/// 任务重新分发事件参数
/// </summary>
public class TaskRedistributedEventArgs : EventArgs
{
    /// <summary>
    /// 原始任务
    /// </summary>
    public DistributedTask OriginalTask { get; set; } = null!;

    /// <summary>
    /// 重新分发结果
    /// </summary>
    public TaskDistributionResult Result { get; set; } = null!;

    /// <summary>
    /// 原始节点ID
    /// </summary>
    public string? OriginalNodeId { get; set; }

    /// <summary>
    /// 新分配的节点
    /// </summary>
    public NodeInfo? NewAssignedNode { get; set; }

    /// <summary>
    /// 重新分发原因
    /// </summary>
    public string RedistributionReason { get; set; } = string.Empty;

    /// <summary>
    /// 重新分发时间
    /// </summary>
    public DateTime RedistributedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 重试次数
    /// </summary>
    public int RetryCount { get; set; }

    /// <summary>
    /// 排除的节点ID列表
    /// </summary>
    public List<string> ExcludedNodes { get; set; } = new();

    /// <summary>
    /// 重新分发耗时（毫秒）
    /// </summary>
    public long RedistributionTimeMs { get; set; }

    /// <summary>
    /// 是否为最后一次重试
    /// </summary>
    public bool IsLastRetry { get; set; }

    /// <summary>
    /// 附加信息
    /// </summary>
    public Dictionary<string, object> AdditionalInfo { get; set; } = new();
}

/// <summary>
/// 负载重新平衡事件参数
/// </summary>
public class LoadRebalancedEventArgs : EventArgs
{
    /// <summary>
    /// 重新平衡结果
    /// </summary>
    public LoadRebalancingResult Result { get; set; } = null!;

    /// <summary>
    /// 触发重新平衡的原因
    /// </summary>
    public string TriggerReason { get; set; } = string.Empty;

    /// <summary>
    /// 重新平衡类型
    /// </summary>
    public RebalancingType RebalancingType { get; set; }

    /// <summary>
    /// 重新平衡开始时间
    /// </summary>
    public DateTime StartTime { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 重新平衡结束时间
    /// </summary>
    public DateTime EndTime { get; set; }

    /// <summary>
    /// 总耗时（毫秒）
    /// </summary>
    public long TotalTimeMs => (long)(EndTime - StartTime).TotalMilliseconds;

    /// <summary>
    /// 涉及的节点数量
    /// </summary>
    public int InvolvedNodeCount { get; set; }

    /// <summary>
    /// 迁移的任务数量
    /// </summary>
    public int MigratedTaskCount { get; set; }

    /// <summary>
    /// 重新平衡前的负载均衡度
    /// </summary>
    public double BeforeBalanceScore { get; set; }

    /// <summary>
    /// 重新平衡后的负载均衡度
    /// </summary>
    public double AfterBalanceScore { get; set; }

    /// <summary>
    /// 改善程度（0-1）
    /// </summary>
    public double ImprovementScore { get; set; }

    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// 警告信息
    /// </summary>
    public List<string> Warnings { get; set; } = new();

    /// <summary>
    /// 附加信息
    /// </summary>
    public Dictionary<string, object> AdditionalInfo { get; set; } = new();
}

/// <summary>
/// 节点选择事件参数
/// </summary>
public class NodeSelectedEventArgs : EventArgs
{
    /// <summary>
    /// 任务要求
    /// </summary>
    public TaskRequirements TaskRequirements { get; set; } = null!;

    /// <summary>
    /// 选择的节点
    /// </summary>
    public NodeInfo? SelectedNode { get; set; }

    /// <summary>
    /// 候选节点列表
    /// </summary>
    public List<NodeInfo> CandidateNodes { get; set; } = new();

    /// <summary>
    /// 选择策略
    /// </summary>
    public NodeSelectionStrategy Strategy { get; set; }

    /// <summary>
    /// 选择时间
    /// </summary>
    public DateTime SelectionTime { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 选择耗时（毫秒）
    /// </summary>
    public long SelectionTimeMs { get; set; }

    /// <summary>
    /// 选择评分
    /// </summary>
    public double SelectionScore { get; set; }

    /// <summary>
    /// 选择原因
    /// </summary>
    public string SelectionReason { get; set; } = string.Empty;

    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 附加信息
    /// </summary>
    public Dictionary<string, object> AdditionalInfo { get; set; } = new();
}

/// <summary>
/// 负载状态变更事件参数
/// </summary>
public class LoadStatusChangedEventArgs : EventArgs
{
    /// <summary>
    /// 节点ID
    /// </summary>
    public string NodeId { get; set; } = string.Empty;

    /// <summary>
    /// 旧的负载状态
    /// </summary>
    public NodeLoadSummary? OldLoadStatus { get; set; }

    /// <summary>
    /// 新的负载状态
    /// </summary>
    public NodeLoadSummary NewLoadStatus { get; set; } = null!;

    /// <summary>
    /// 变更时间
    /// </summary>
    public DateTime ChangeTime { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 变更类型
    /// </summary>
    public LoadChangeType ChangeType { get; set; }

    /// <summary>
    /// 负载变化量
    /// </summary>
    public double LoadDelta { get; set; }

    /// <summary>
    /// 是否触发告警
    /// </summary>
    public bool TriggersAlert { get; set; }

    /// <summary>
    /// 告警级别
    /// </summary>
    public AlertLevel AlertLevel { get; set; } = AlertLevel.None;

    /// <summary>
    /// 变更原因
    /// </summary>
    public string ChangeReason { get; set; } = string.Empty;

    /// <summary>
    /// 附加信息
    /// </summary>
    public Dictionary<string, object> AdditionalInfo { get; set; } = new();
}

/// <summary>
/// 负载变更类型枚举
/// </summary>
public enum LoadChangeType
{
    /// <summary>
    /// 负载增加
    /// </summary>
    Increase,

    /// <summary>
    /// 负载减少
    /// </summary>
    Decrease,

    /// <summary>
    /// 负载稳定
    /// </summary>
    Stable,

    /// <summary>
    /// 负载异常
    /// </summary>
    Abnormal
}

/// <summary>
/// 告警级别枚举
/// </summary>
public enum AlertLevel
{
    /// <summary>
    /// 无告警
    /// </summary>
    None,

    /// <summary>
    /// 信息
    /// </summary>
    Info,

    /// <summary>
    /// 警告
    /// </summary>
    Warning,

    /// <summary>
    /// 错误
    /// </summary>
    Error,

    /// <summary>
    /// 严重
    /// </summary>
    Critical
}
