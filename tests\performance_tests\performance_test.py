#!/usr/bin/env python3
"""
FlowCustomV1 性能测试脚本
测试开发环境下的系统性能
"""

import requests
import time
import statistics
import json
import threading
import concurrent.futures
from datetime import datetime
import sys

# 测试配置
BASE_URL = "http://localhost:5000"
ENDPOINTS = {
    "cluster_nodes": "/api/cluster/nodes",
    "executor_capacity": "/api/executor/capacity",
    "swagger": "/swagger"
}

class PerformanceTest:
    def __init__(self):
        self.results = {}
        
    def test_single_request(self, endpoint_name, url, iterations=10):
        """测试单个端点的响应时间"""
        print(f"\n🔍 测试端点: {endpoint_name}")
        print(f"URL: {url}")
        print(f"迭代次数: {iterations}")
        
        times = []
        success_count = 0
        
        for i in range(iterations):
            try:
                start_time = time.time()
                response = requests.get(url, timeout=30)
                end_time = time.time()
                
                response_time = (end_time - start_time) * 1000  # 转换为毫秒
                times.append(response_time)
                
                if response.status_code == 200:
                    success_count += 1
                    print(f"  请求 {i+1}: {response_time:.2f}ms ✅")
                else:
                    print(f"  请求 {i+1}: {response_time:.2f}ms ❌ (状态码: {response.status_code})")
                    
            except Exception as e:
                print(f"  请求 {i+1}: 失败 ❌ ({str(e)})")
                
        if times:
            avg_time = statistics.mean(times)
            min_time = min(times)
            max_time = max(times)
            median_time = statistics.median(times)
            
            print(f"\n📊 {endpoint_name} 性能统计:")
            print(f"  成功率: {success_count}/{iterations} ({success_count/iterations*100:.1f}%)")
            print(f"  平均响应时间: {avg_time:.2f}ms")
            print(f"  最快响应时间: {min_time:.2f}ms")
            print(f"  最慢响应时间: {max_time:.2f}ms")
            print(f"  中位数响应时间: {median_time:.2f}ms")
            
            self.results[endpoint_name] = {
                "success_rate": success_count/iterations,
                "avg_time": avg_time,
                "min_time": min_time,
                "max_time": max_time,
                "median_time": median_time,
                "total_requests": iterations
            }
        else:
            print(f"❌ {endpoint_name} 所有请求都失败了")
            
    def test_concurrent_requests(self, endpoint_name, url, concurrent_users=5, requests_per_user=10):
        """测试并发请求性能"""
        print(f"\n🚀 并发测试: {endpoint_name}")
        print(f"并发用户数: {concurrent_users}")
        print(f"每用户请求数: {requests_per_user}")
        print(f"总请求数: {concurrent_users * requests_per_user}")
        
        def make_requests(user_id):
            user_times = []
            user_success = 0
            
            for i in range(requests_per_user):
                try:
                    start_time = time.time()
                    response = requests.get(url, timeout=30)
                    end_time = time.time()
                    
                    response_time = (end_time - start_time) * 1000
                    user_times.append(response_time)
                    
                    if response.status_code == 200:
                        user_success += 1
                        
                except Exception as e:
                    pass
                    
            return user_times, user_success
        
        start_time = time.time()
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=concurrent_users) as executor:
            futures = [executor.submit(make_requests, i) for i in range(concurrent_users)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        end_time = time.time()
        total_duration = end_time - start_time
        
        # 汇总结果
        all_times = []
        total_success = 0
        
        for times, success in results:
            all_times.extend(times)
            total_success += success
            
        total_requests = concurrent_users * requests_per_user
        
        if all_times:
            avg_time = statistics.mean(all_times)
            min_time = min(all_times)
            max_time = max(all_times)
            throughput = total_requests / total_duration
            
            print(f"\n📊 {endpoint_name} 并发性能统计:")
            print(f"  总耗时: {total_duration:.2f}秒")
            print(f"  成功率: {total_success}/{total_requests} ({total_success/total_requests*100:.1f}%)")
            print(f"  吞吐量: {throughput:.2f} 请求/秒")
            print(f"  平均响应时间: {avg_time:.2f}ms")
            print(f"  最快响应时间: {min_time:.2f}ms")
            print(f"  最慢响应时间: {max_time:.2f}ms")
            
            concurrent_key = f"{endpoint_name}_concurrent"
            self.results[concurrent_key] = {
                "concurrent_users": concurrent_users,
                "requests_per_user": requests_per_user,
                "total_duration": total_duration,
                "success_rate": total_success/total_requests,
                "throughput": throughput,
                "avg_time": avg_time,
                "min_time": min_time,
                "max_time": max_time
            }
        else:
            print(f"❌ {endpoint_name} 并发测试失败")
            
    def test_system_resources(self):
        """测试系统资源使用情况"""
        print(f"\n💻 系统资源测试")
        
        try:
            # 测试NATS状态
            nats_response = requests.get("http://localhost:8222/varz", timeout=10)
            if nats_response.status_code == 200:
                nats_data = nats_response.json()
                print(f"  NATS服务器状态: ✅")
                print(f"  NATS连接数: {nats_data.get('connections', 'N/A')}")
                print(f"  NATS内存使用: {nats_data.get('mem', 'N/A')} bytes")
                
            # 测试JetStream状态
            js_response = requests.get("http://localhost:8222/jsz", timeout=10)
            if js_response.status_code == 200:
                js_data = js_response.json()
                print(f"  JetStream状态: ✅")
                print(f"  JetStream内存使用: {js_data.get('memory', 'N/A')} bytes")
                print(f"  JetStream错误数: {js_data.get('api', {}).get('errors', 'N/A')}")
                
        except Exception as e:
            print(f"  系统资源检查失败: {str(e)}")
            
    def run_all_tests(self):
        """运行所有性能测试"""
        print("🎯 FlowCustomV1 开发环境性能测试")
        print("=" * 50)
        print(f"测试开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 系统资源测试
        self.test_system_resources()
        
        # 单请求性能测试
        for endpoint_name, path in ENDPOINTS.items():
            url = BASE_URL + path
            self.test_single_request(endpoint_name, url, iterations=10)
            
        # 并发性能测试（只测试主要端点）
        main_endpoints = ["cluster_nodes", "executor_capacity"]
        for endpoint_name in main_endpoints:
            if endpoint_name in ENDPOINTS:
                url = BASE_URL + ENDPOINTS[endpoint_name]
                self.test_concurrent_requests(endpoint_name, url, concurrent_users=5, requests_per_user=10)
                
        # 输出总结
        self.print_summary()
        
    def print_summary(self):
        """打印测试总结"""
        print("\n" + "=" * 50)
        print("📋 性能测试总结")
        print("=" * 50)
        
        for endpoint, data in self.results.items():
            if "concurrent" not in endpoint:
                print(f"\n🔸 {endpoint}:")
                print(f"  平均响应时间: {data['avg_time']:.2f}ms")
                print(f"  成功率: {data['success_rate']*100:.1f}%")
            else:
                print(f"\n🔸 {endpoint}:")
                print(f"  吞吐量: {data['throughput']:.2f} 请求/秒")
                print(f"  平均响应时间: {data['avg_time']:.2f}ms")
                print(f"  成功率: {data['success_rate']*100:.1f}%")
                
        print(f"\n测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    test = PerformanceTest()
    test.run_all_tests()
