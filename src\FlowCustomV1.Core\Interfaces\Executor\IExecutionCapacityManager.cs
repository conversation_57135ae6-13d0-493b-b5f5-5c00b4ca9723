using FlowCustomV1.Core.Models.Executor;

namespace FlowCustomV1.Core.Interfaces.Executor;

/// <summary>
/// 执行容量管理器接口
/// 负责管理节点的执行容量和资源分配
/// </summary>
public interface IExecutionCapacityManager
{
    /// <summary>
    /// 获取当前执行容量
    /// </summary>
    /// <returns>执行容量信息</returns>
    ExecutionCapacity GetCurrentCapacity();

    /// <summary>
    /// 检查是否可以接受新的执行任务
    /// </summary>
    /// <param name="estimatedResourceUsage">预估资源使用量</param>
    /// <returns>是否可以接受</returns>
    bool CanAcceptExecution(ResourceUsage estimatedResourceUsage);

    /// <summary>
    /// 预留执行资源
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="resourceUsage">资源使用量</param>
    /// <returns>预留结果</returns>
    bool ReserveResources(string executionId, ResourceUsage resourceUsage);

    /// <summary>
    /// 释放执行资源
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <returns>释放结果</returns>
    bool ReleaseResources(string executionId);

    /// <summary>
    /// 更新资源使用情况
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="actualUsage">实际使用量</param>
    void UpdateResourceUsage(string executionId, ResourceUsage actualUsage);

    /// <summary>
    /// 获取节点负载信息
    /// </summary>
    /// <returns>节点负载信息</returns>
    NodeLoadInfo GetNodeLoadInfo();

    /// <summary>
    /// 获取资源使用统计
    /// </summary>
    /// <returns>资源使用统计</returns>
    ResourceUsageStatistics GetResourceUsageStatistics();

    /// <summary>
    /// 设置容量限制
    /// </summary>
    /// <param name="limits">容量限制</param>
    void SetCapacityLimits(CapacityLimits limits);

    /// <summary>
    /// 获取容量限制
    /// </summary>
    /// <returns>容量限制</returns>
    CapacityLimits GetCapacityLimits();

    /// <summary>
    /// 清理过期的资源预留
    /// </summary>
    /// <param name="expirationTime">过期时间</param>
    /// <returns>清理的资源数量</returns>
    int CleanupExpiredReservations(TimeSpan expirationTime);
}

/// <summary>
/// 执行容量管理器事件参数基类
/// </summary>
public abstract class CapacityEventArgs : EventArgs
{
    /// <summary>
    /// 事件时间戳
    /// </summary>
    public DateTime Timestamp { get; } = DateTime.UtcNow;

    /// <summary>
    /// 节点ID
    /// </summary>
    public string NodeId { get; set; } = string.Empty;
}

/// <summary>
/// 容量不足事件参数
/// </summary>
public class CapacityExceededEventArgs : CapacityEventArgs
{
    /// <summary>
    /// 当前容量
    /// </summary>
    public ExecutionCapacity CurrentCapacity { get; set; } = new();

    /// <summary>
    /// 请求的资源使用量
    /// </summary>
    public ResourceUsage RequestedUsage { get; set; } = new();

    /// <summary>
    /// 拒绝原因
    /// </summary>
    public string Reason { get; set; } = string.Empty;
}

/// <summary>
/// 资源预留事件参数
/// </summary>
public class ResourceReservedEventArgs : CapacityEventArgs
{
    /// <summary>
    /// 执行ID
    /// </summary>
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 预留的资源使用量
    /// </summary>
    public ResourceUsage ReservedUsage { get; set; } = new();

    /// <summary>
    /// 预留后的可用容量
    /// </summary>
    public ExecutionCapacity AvailableCapacity { get; set; } = new();
}

/// <summary>
/// 资源释放事件参数
/// </summary>
public class ResourceReleasedEventArgs : CapacityEventArgs
{
    /// <summary>
    /// 执行ID
    /// </summary>
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 释放的资源使用量
    /// </summary>
    public ResourceUsage ReleasedUsage { get; set; } = new();

    /// <summary>
    /// 释放后的可用容量
    /// </summary>
    public ExecutionCapacity AvailableCapacity { get; set; } = new();
}
