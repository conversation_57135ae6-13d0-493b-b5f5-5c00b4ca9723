name: flowcustom-prod

services:
  # NATS集群 (3节点) - 生产环境端口 +10000
  nats-server-1:
    image: nats:2.11.8-alpine
    container_name: nats-prod-server-1
    ports:
      - "14222:4222"
      - "18222:8222"
    volumes:
      - ./nats/nats-1.conf:/nats-server.conf:ro
      - nats_data_1:/data
    command: ["-c", "/nats-server.conf"]
    networks:
      - flowcustom-prod

  nats-server-2:
    image: nats:2.11.8-alpine
    container_name: nats-prod-server-2
    ports:
      - "14223:4222"
      - "18223:8222"
    volumes:
      - ./nats/nats-2.conf:/nats-server.conf:ro
      - nats_data_2:/data
    command: ["-c", "/nats-server.conf"]
    networks:
      - flowcustom-prod
    depends_on:
      - nats-server-1

  nats-server-3:
    image: nats:2.11.8-alpine
    container_name: nats-prod-server-3
    ports:
      - "14224:4222"
      - "18224:8222"
    volumes:
      - ./nats/nats-3.conf:/nats-server.conf:ro
      - nats_data_3:/data
    command: ["-c", "/nats-server.conf"]
    networks:
      - flowcustom-prod
    depends_on:
      - nats-server-1

  # MySQL数据库 - 生产环境端口 +10000
  mysql:
    image: mysql:8.0
    container_name: mysql-prod
    environment:
      MYSQL_ROOT_PASSWORD: TO_BE_CONFIGURED_ON_DEPLOYMENT
      MYSQL_DATABASE: flowcustom_prod
      MYSQL_USER: flowcustom
      MYSQL_PASSWORD: TO_BE_CONFIGURED_ON_DEPLOYMENT
    ports:
      - "13306:3306"
    volumes:
      - mysql_prod_data:/var/lib/mysql
      - ./mysql/init:/docker-entrypoint-initdb.d
    networks:
      - flowcustom-prod

  # API节点 (单角色) - 生产环境端口 +10000
  api-node:
    build:
      context: ../../
      dockerfile: docker/production/Dockerfile.api
    container_name: flowcustom-prod-api-node
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:5000
    ports:
      - "15000:5000"
    networks:
      - flowcustom-prod
    depends_on:
      - nats-server-1
      - nats-server-2
      - nats-server-3
      - mysql

  # Worker节点 (单角色)
  worker-node:
    build:
      context: ../../
      dockerfile: docker/production/Dockerfile.worker
    container_name: flowcustom-prod-worker-node
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
    networks:
      - flowcustom-prod
    depends_on:
      - nats-server-1
      - nats-server-2
      - nats-server-3
      - mysql

  # Designer节点 (单角色)
  designer-node:
    build:
      context: ../../
      dockerfile: docker/production/Dockerfile.designer
    container_name: flowcustom-prod-designer-node
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
    networks:
      - flowcustom-prod
    depends_on:
      - nats-server-1
      - nats-server-2
      - nats-server-3
      - mysql

  # Validator节点 (单角色)
  validator-node:
    build:
      context: ../../
      dockerfile: docker/production/Dockerfile.validator
    container_name: flowcustom-prod-validator-node
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
    networks:
      - flowcustom-prod
    depends_on:
      - nats-server-1
      - nats-server-2
      - nats-server-3
      - mysql

  # Executor节点 (单角色)
  executor-node:
    build:
      context: ../../
      dockerfile: docker/production/Dockerfile.executor
    container_name: flowcustom-prod-executor-node
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
    networks:
      - flowcustom-prod
    depends_on:
      - nats-server-1
      - nats-server-2
      - nats-server-3
      - mysql

  # Monitor节点 (单角色)
  monitor-node:
    build:
      context: ../../
      dockerfile: docker/production/Dockerfile.monitor
    container_name: flowcustom-prod-monitor-node
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
    networks:
      - flowcustom-prod
    depends_on:
      - nats-server-1
      - nats-server-2
      - nats-server-3
      - mysql

  # Scheduler节点 (单角色)
  scheduler-node:
    build:
      context: ../../
      dockerfile: docker/production/Dockerfile.scheduler
    container_name: flowcustom-prod-scheduler-node
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
    networks:
      - flowcustom-prod
    depends_on:
      - nats-server-1
      - nats-server-2
      - nats-server-3
      - mysql

volumes:
  mysql_prod_data:
  nats_data_1:
  nats_data_2:
  nats_data_3:

networks:
  flowcustom-prod:
    driver: bridge
