import React from 'react';
import {
  PlayCircleOutlined,
  StopOutlined,
  SettingOutlined,
  ClockCircleOutlined,
  ApiOutlined,
  DatabaseOutlined,
  BranchesOutlined,
  ThunderboltOutlined,
  FileTextOutlined,
  MailOutlined,
  PhoneOutlined,
  MessageOutlined
} from '@ant-design/icons';

// 节点类型定义
export interface NodeData {
  label: string;
  type: string;
  description?: string;
  status?: 'success' | 'error' | 'warning' | 'processing' | 'default';
}

// 节点样式配置
const nodeStyles = {
  start: {
    backgroundColor: '#f6ffed',
    borderColor: '#52c41a',
    color: '#52c41a',
    icon: PlayCircleOutlined,
  },
  end: {
    backgroundColor: '#fff2f0',
    borderColor: '#ff4d4f',
    color: '#ff4d4f',
    icon: StopOutlined,
  },
  task: {
    backgroundColor: '#f0f9ff',
    borderColor: '#1890ff',
    color: '#1890ff',
    icon: SettingOutlined,
  },
  timer: {
    backgroundColor: '#fff7e6',
    borderColor: '#fa8c16',
    color: '#fa8c16',
    icon: ClockCircleOutlined,
  },
  http: {
    backgroundColor: '#f9f0ff',
    borderColor: '#722ed1',
    color: '#722ed1',
    icon: ApiOutlined,
  },
  database: {
    backgroundColor: '#e6f7ff',
    borderColor: '#13c2c2',
    color: '#13c2c2',
    icon: DatabaseOutlined,
  },
  decision: {
    backgroundColor: '#fff1f0',
    borderColor: '#fa541c',
    color: '#fa541c',
    icon: BranchesOutlined,
  },
  trigger: {
    backgroundColor: '#fcffe6',
    borderColor: '#a0d911',
    color: '#a0d911',
    icon: ThunderboltOutlined,
  },
  document: {
    backgroundColor: '#f6ffed',
    borderColor: '#52c41a',
    color: '#52c41a',
    icon: FileTextOutlined,
  },
  email: {
    backgroundColor: '#fff0f6',
    borderColor: '#eb2f96',
    color: '#eb2f96',
    icon: MailOutlined,
  },
  phone: {
    backgroundColor: '#f0f5ff',
    borderColor: '#2f54eb',
    color: '#2f54eb',
    icon: PhoneOutlined,
  },
  message: {
    backgroundColor: '#f9f0ff',
    borderColor: '#722ed1',
    color: '#722ed1',
    icon: MessageOutlined,
  },
};

// 基础节点组件
interface BaseNodeProps {
  node?: any;
  data?: NodeData;
}

export const BaseNode: React.FC<BaseNodeProps> = ({ node, data }) => {
  const nodeData = data || node?.getData();
  const { label, type = 'task', status } = nodeData || {};

  const style = nodeStyles[type as keyof typeof nodeStyles] || nodeStyles.task;
  const IconComponent = style.icon;

  const handleClick = () => {
    if (node) {
      console.log('节点被点击:', nodeData);
      // 这里可以添加节点点击的处理逻辑
    }
  };

  return (
    <div
      className="workflow-node"
      onClick={handleClick}
      style={{
        width: '100%',
        height: '100%',
        backgroundColor: '#fff',
        border: `1px solid ${style.borderColor}`,
        borderLeft: `4px solid ${style.borderColor}`,
        borderRadius: '4px',
        padding: '8px',
        display: 'flex',
        alignItems: 'center',
        gap: '8px',
        cursor: 'pointer',
        boxShadow: '0 2px 5px 1px rgba(0, 0, 0, 0.06)',
      }}
      onMouseEnter={(e) => {
        e.currentTarget.style.borderColor = '#1890ff';
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.borderColor = style.borderColor;
      }}
    >

      
      {/* 图标 */}
      <IconComponent
        style={{
          fontSize: '20px',
          color: style.color,
          flexShrink: 0,
          marginLeft: '6px',
        }}
      />
      
      {/* 文本内容 */}
      <div style={{ flex: 1, minWidth: 0 }}>
        <div
          style={{
            fontSize: '12px',
            fontWeight: 500,
            color: '#666',
            lineHeight: '14px',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
            width: '90px',
            marginLeft: '6px',
          }}
        >
          {label || '未命名节点'}
        </div>
      </div>
      
      {/* 状态指示器 */}
      {status && status !== 'default' && (
        <div
          style={{
            width: '8px',
            height: '8px',
            borderRadius: '50%',
            backgroundColor: 
              status === 'success' ? '#52c41a' :
              status === 'error' ? '#ff4d4f' :
              status === 'warning' ? '#fa8c16' :
              status === 'processing' ? '#1890ff' : '#d9d9d9',
            flexShrink: 0,
          }}
        />
      )}
    </div>
  );
};

// 圆形节点组件（用于开始和结束节点）
export const CircleNode: React.FC<BaseNodeProps> = ({ node, data }) => {
  const nodeData = data || node?.getData();
  const { label, type = 'start' } = nodeData || {};

  const style = nodeStyles[type as keyof typeof nodeStyles] || nodeStyles.start;
  const IconComponent = style.icon;

  const handleClick = () => {
    if (node) {
      console.log('圆形节点被点击:', nodeData);
    }
  };

  return (
    <div
      className="workflow-circle-node"
      onClick={handleClick}
      style={{
        width: '60px',
        height: '60px',
        backgroundColor: style.backgroundColor,
        border: `3px solid ${style.borderColor}`,
        borderRadius: '50%',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        cursor: 'pointer',
        transition: 'all 0.2s ease',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
      }}
      onMouseEnter={(e) => {
        e.currentTarget.style.transform = 'scale(1.05)';
        e.currentTarget.style.boxShadow = '0 4px 8px rgba(0,0,0,0.15)';
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.transform = 'scale(1)';
        e.currentTarget.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
      }}
    >
      <IconComponent
        style={{
          fontSize: '18px',
          color: style.color,
          marginBottom: '2px',
        }}
      />
      <div
        style={{
          fontSize: '10px',
          fontWeight: 500,
          color: style.color,
          textAlign: 'center',
          lineHeight: '12px',
        }}
      >
        {label || (type === 'start' ? '开始' : '结束')}
      </div>
    </div>
  );
};

// 菱形节点组件（用于决策节点）
export const DiamondNode: React.FC<BaseNodeProps> = ({ node, data }) => {
  const nodeData = data || node?.getData();
  const { label, type = 'decision' } = nodeData || {};

  const style = nodeStyles[type as keyof typeof nodeStyles] || nodeStyles.decision;
  const IconComponent = style.icon;

  const handleClick = () => {
    if (node) {
      console.log('菱形节点被点击:', nodeData);
    }
  };

  return (
    <div
      className="workflow-diamond-node"
      onClick={handleClick}
      style={{
        width: '80px',
        height: '80px',
        backgroundColor: style.backgroundColor,
        border: `2px solid ${style.borderColor}`,
        transform: 'rotate(45deg)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        cursor: 'pointer',
        transition: 'all 0.2s ease',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        position: 'relative',
      }}
      onMouseEnter={(e) => {
        e.currentTarget.style.transform = 'rotate(45deg) scale(1.05)';
        e.currentTarget.style.boxShadow = '0 4px 8px rgba(0,0,0,0.15)';
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.transform = 'rotate(45deg) scale(1)';
        e.currentTarget.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
      }}
    >
      <div
        style={{
          transform: 'rotate(-45deg)',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <IconComponent
          style={{
            fontSize: '16px',
            color: style.color,
            marginBottom: '2px',
          }}
        />
        <div
          style={{
            fontSize: '10px',
            fontWeight: 500,
            color: style.color,
            textAlign: 'center',
            lineHeight: '12px',
            whiteSpace: 'nowrap',
          }}
        >
          {label || '决策'}
        </div>
      </div>
    </div>
  );
};

export default BaseNode;
