#!/usr/bin/env python3
"""
FlowCustomV1 生产环境集群启动脚本
启动完整的生产环境集群 (端口+10000)
"""

import subprocess
import sys
import time
import requests
from pathlib import Path

def run_command(command, cwd=None, timeout=60):
    """执行命令并返回结果"""
    try:
        result = subprocess.run(
            command, 
            cwd=cwd, 
            capture_output=True, 
            text=True, 
            shell=True,
            encoding='utf-8',
            errors='ignore',
            timeout=timeout
        )
        return result.returncode == 0, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return False, "", f"命令超时 ({timeout}秒)"
    except Exception as e:
        return False, "", str(e)

def check_service_health(url, timeout=10):
    """检查服务健康状态"""
    try:
        response = requests.get(f"{url}/health", timeout=timeout)
        return response.status_code == 200
    except:
        return False

def start_production_cluster():
    """启动生产环境集群"""
    print("🚀 启动FlowCustomV1生产环境集群...")
    print("⚠️  注意: 生产环境端口 = 默认端口 + 10000")
    
    script_dir = Path(__file__).parent
    
    # 检查生产环境配置
    print("🔍 检查生产环境配置...")
    
    # 启动基础设施
    print("🔄 启动基础设施服务 (NATS集群 + MySQL)...")
    success, stdout, stderr = run_command(
        "docker-compose up -d nats-server-1 nats-server-2 nats-server-3 mysql",
        cwd=script_dir,
        timeout=120
    )
    
    if not success:
        print(f"❌ 基础设施启动失败:")
        print(f"stderr: {stderr}")
        return False
    
    print("✅ 基础设施服务启动成功")
    
    # 等待基础设施启动
    print("⏳ 等待基础设施初始化...")
    time.sleep(20)
    
    # 启动应用节点
    print("🔄 启动应用节点...")
    app_nodes = ["api-node", "worker-node", "designer-node", "validator-node", 
                 "executor-node", "monitor-node", "scheduler-node"]
    
    for node in app_nodes:
        print(f"🔄 启动 {node}...")
        success, stdout, stderr = run_command(
            f"docker-compose up -d {node}",
            cwd=script_dir,
            timeout=120
        )
        
        if not success:
            print(f"⚠️ {node} 启动失败: {stderr}")
        else:
            print(f"✅ {node} 启动成功")
        
        time.sleep(5)  # 错开启动时间
    
    return True

def check_cluster_status():
    """检查集群状态"""
    print("\n🔍 检查集群状态...")
    
    script_dir = Path(__file__).parent
    
    # 检查容器状态
    success, stdout, stderr = run_command(
        "docker-compose ps",
        cwd=script_dir
    )
    
    if success:
        print("📊 容器状态:")
        print(stdout)
    
    # 检查NATS集群
    print("🔍 检查NATS集群状态...")
    for i, port in enumerate([14222, 14223, 14224], 1):
        try:
            result = subprocess.run(
                ["docker", "exec", f"nats-prod-server-{i}", "nats", "server", "info"], 
                capture_output=True, text=True, timeout=10
            )
            if result.returncode == 0:
                print(f"✅ NATS服务器 {port} 运行正常")
            else:
                print(f"⚠️ NATS服务器 {port} 状态检查失败")
        except:
            print(f"⚠️ 无法检查NATS服务器 {port} 状态")
    
    # 检查MySQL
    try:
        result = subprocess.run(
            ["docker", "exec", "mysql-prod", "mysqladmin", "ping", "-h", "localhost"], 
            capture_output=True, text=True, timeout=10
        )
        if result.returncode == 0:
            print("✅ MySQL数据库运行正常")
        else:
            print("⚠️ MySQL数据库状态检查失败")
    except:
        print("⚠️ 无法检查MySQL数据库状态")
    
    # 检查API节点
    print("🔍 检查API节点健康状态...")
    if check_service_health("http://localhost:15000"):
        print("✅ API节点健康检查通过")
    else:
        print("⚠️ API节点健康检查失败")

def stop_production_cluster():
    """停止生产环境集群"""
    print("🛑 停止生产环境集群...")
    
    script_dir = Path(__file__).parent
    
    success, stdout, stderr = run_command(
        "docker-compose down",
        cwd=script_dir,
        timeout=60
    )
    
    if success:
        print("✅ 生产环境集群已停止")
    else:
        print(f"⚠️ 停止可能不完整: {stderr}")

def cleanup_production_cluster():
    """清理生产环境集群"""
    print("🧹 清理生产环境集群...")
    
    script_dir = Path(__file__).parent
    
    success, stdout, stderr = run_command(
        "docker-compose down -v",
        cwd=script_dir,
        timeout=60
    )
    
    if success:
        print("✅ 生产环境集群清理完成")
    else:
        print(f"⚠️ 清理可能不完整: {stderr}")

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python start-prod-cluster.py [start|stop|status|cleanup]")
        sys.exit(1)
    
    command = sys.argv[1].lower()
    
    if command == "start":
        if start_production_cluster():
            check_cluster_status()
            print("\n🎉 生产环境集群启动完成!")
            print("\n🌐 集群访问信息 (端口+10000):")
            print("API节点:      http://localhost:15000")
            print("NATS监控:     http://localhost:18222-18224")
            print("MySQL数据库:  localhost:13306")
            print("\n⚠️  生产环境注意事项:")
            print("1. 确保已配置TLS证书")
            print("2. 确保已设置安全密码")
            print("3. 确保防火墙配置正确")
        else:
            sys.exit(1)
    
    elif command == "stop":
        stop_production_cluster()
    
    elif command == "status":
        check_cluster_status()
    
    elif command == "cleanup":
        cleanup_production_cluster()
    
    else:
        print("❌ 未知命令，支持的命令: start, stop, status, cleanup")
        sys.exit(1)

if __name__ == "__main__":
    main()
