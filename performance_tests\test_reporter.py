#!/usr/bin/env python3
"""
FlowCustomV1 性能测试报告生成器
收集、分析和生成性能测试报告
"""

import json
import os
from datetime import datetime
from test_config import get_performance_grade, format_performance_result, OUTPUT_CONFIG

class PerformanceReporter:
    def __init__(self):
        self.results = {}
        self.test_start_time = None
        self.test_end_time = None
        
    def start_test_session(self, test_name):
        """开始测试会话"""
        self.test_start_time = datetime.now()
        self.results = {
            "test_name": test_name,
            "start_time": self.test_start_time.strftime(OUTPUT_CONFIG["datetime_format"]),
            "test_results": {},
            "summary": {},
            "recommendations": []
        }
        
    def end_test_session(self):
        """结束测试会话"""
        self.test_end_time = datetime.now()
        if self.test_start_time:
            duration = (self.test_end_time - self.test_start_time).total_seconds()
            self.results["end_time"] = self.test_end_time.strftime(OUTPUT_CONFIG["datetime_format"])
            self.results["total_duration"] = duration
            
    def add_test_result(self, test_category, test_name, result_data):
        """添加测试结果"""
        if test_category not in self.results["test_results"]:
            self.results["test_results"][test_category] = {}
            
        self.results["test_results"][test_category][test_name] = result_data
        
    def add_api_result(self, endpoint_name, avg_time, success_rate, throughput=None):
        """添加API测试结果"""
        grade = get_performance_grade(avg_time, "api", "response_time")
        
        result = {
            "avg_response_time": avg_time,
            "success_rate": success_rate,
            "grade": grade,
            "formatted_time": format_performance_result(avg_time, "ms"),
            "formatted_success_rate": f"{success_rate*100:.1f}%"
        }
        
        if throughput:
            throughput_grade = get_performance_grade(throughput, "api", "throughput")
            result["throughput"] = throughput
            result["throughput_grade"] = throughput_grade
            result["formatted_throughput"] = format_performance_result(throughput, " req/s")
            
        self.add_test_result("api_performance", endpoint_name, result)
        
    def add_infrastructure_result(self, component, test_name, avg_time=None, throughput=None, success_rate=1.0):
        """添加基础设施测试结果"""
        result = {
            "success_rate": success_rate,
            "formatted_success_rate": f"{success_rate*100:.1f}%"
        }
        
        if avg_time:
            grade = get_performance_grade(avg_time, component, "response_time")
            result["avg_response_time"] = avg_time
            result["grade"] = grade
            result["formatted_time"] = format_performance_result(avg_time, "ms")
            
        if throughput:
            throughput_grade = get_performance_grade(throughput, component, "throughput")
            result["throughput"] = throughput
            result["throughput_grade"] = throughput_grade
            unit = " queries/s" if component == "mysql" else " req/s"
            result["formatted_throughput"] = format_performance_result(throughput, unit)
            
        self.add_test_result(f"{component}_performance", test_name, result)
        
    def add_system_limit_result(self, max_stable_load, breaking_point):
        """添加系统极限测试结果"""
        result = {
            "max_stable_load": max_stable_load,
            "breaking_point": breaking_point,
            "stability_margin": breaking_point - max_stable_load if breaking_point > max_stable_load else 0
        }
        
        self.add_test_result("system_limits", "breaking_point_analysis", result)
        
    def generate_summary(self):
        """生成测试总结"""
        summary = {
            "total_tests": 0,
            "excellent_count": 0,
            "good_count": 0,
            "acceptable_count": 0,
            "poor_count": 0,
            "overall_grade": "unknown"
        }
        
        # 统计各类测试结果
        for category, tests in self.results["test_results"].items():
            for test_name, result in tests.items():
                summary["total_tests"] += 1
                
                # 获取主要评级
                grade_key = "unknown"
                if "grade" in result:
                    grade_name = result["grade"]["name"]
                    if grade_name == "优秀":
                        grade_key = "excellent"
                    elif grade_name == "良好":
                        grade_key = "good"
                    elif grade_name == "一般":
                        grade_key = "acceptable"
                    elif grade_name == "较差":
                        grade_key = "poor"
                        
                if grade_key in summary:
                    summary[f"{grade_key}_count"] += 1
                    
        # 计算总体评级
        if summary["total_tests"] > 0:
            excellent_ratio = summary["excellent_count"] / summary["total_tests"]
            poor_ratio = summary["poor_count"] / summary["total_tests"]
            
            if excellent_ratio >= 0.8:
                summary["overall_grade"] = "excellent"
            elif poor_ratio <= 0.1 and excellent_ratio >= 0.5:
                summary["overall_grade"] = "good"
            elif poor_ratio <= 0.3:
                summary["overall_grade"] = "acceptable"
            else:
                summary["overall_grade"] = "poor"
                
        self.results["summary"] = summary
        
    def generate_recommendations(self):
        """生成优化建议"""
        recommendations = []
        
        # 分析API性能
        if "api_performance" in self.results["test_results"]:
            for endpoint, result in self.results["test_results"]["api_performance"].items():
                if result.get("grade", {}).get("name") == "较差":
                    recommendations.append(f"🔧 {endpoint} 端点响应时间过长，建议优化查询逻辑或添加缓存")
                elif result.get("grade", {}).get("name") == "一般":
                    recommendations.append(f"⚠️ {endpoint} 端点性能接近边界，建议监控并考虑优化")
                    
        # 分析基础设施性能
        for component in ["nats", "mysql"]:
            component_key = f"{component}_performance"
            if component_key in self.results["test_results"]:
                poor_tests = []
                for test_name, result in self.results["test_results"][component_key].items():
                    if result.get("throughput_grade", {}).get("name") == "较差":
                        poor_tests.append(test_name)
                        
                if poor_tests:
                    component_name = "NATS" if component == "nats" else "MySQL"
                    recommendations.append(f"🔧 {component_name} 在高并发下性能不佳，建议检查配置和资源分配")
                    
        # 分析系统极限
        if "system_limits" in self.results["test_results"]:
            limits = self.results["test_results"]["system_limits"].get("breaking_point_analysis", {})
            max_stable = limits.get("max_stable_load", 0)
            if max_stable < 1000:
                recommendations.append("⚠️ 系统并发处理能力较低，建议进行性能优化或扩容")
            elif max_stable < 500:
                recommendations.append("🔴 系统并发处理能力严重不足，需要立即优化")
                
        # 通用建议
        if not recommendations:
            recommendations.append("✅ 系统性能表现良好，建议定期监控以确保持续稳定")
            
        self.results["recommendations"] = recommendations
        
    def print_console_report(self):
        """打印控制台报告"""
        print("\n" + "=" * 60)
        print(f"📋 {self.results.get('test_name', '性能测试')} 报告")
        print("=" * 60)
        print(f"测试时间: {self.results.get('start_time', 'N/A')} - {self.results.get('end_time', 'N/A')}")
        if "total_duration" in self.results:
            print(f"总耗时: {self.results['total_duration']:.1f}秒")
        print()
        
        # 打印测试结果
        for category, tests in self.results["test_results"].items():
            category_name = {
                "api_performance": "🔸 API性能测试",
                "nats_performance": "🔸 NATS性能测试", 
                "mysql_performance": "🔸 MySQL性能测试",
                "system_limits": "🔸 系统极限测试"
            }.get(category, f"🔸 {category}")
            
            print(category_name)
            
            for test_name, result in tests.items():
                grade_info = result.get("grade", {})
                grade_symbol = grade_info.get("symbol", "")
                
                if "avg_response_time" in result:
                    print(f"  {test_name}: {result['formatted_time']} {grade_symbol}")
                elif "throughput" in result:
                    throughput_grade = result.get("throughput_grade", {})
                    throughput_symbol = throughput_grade.get("symbol", "")
                    print(f"  {test_name}: {result['formatted_throughput']} {throughput_symbol}")
                elif category == "system_limits":
                    print(f"  最大稳定负载: {result.get('max_stable_load', 'N/A')}")
                    print(f"  系统崩溃点: {result.get('breaking_point', 'N/A')}")
                    
            print()
            
        # 打印总结
        if "summary" in self.results:
            summary = self.results["summary"]
            print("🎯 测试总结:")
            print(f"  总测试数: {summary['total_tests']}")
            print(f"  优秀: {summary['excellent_count']} | 良好: {summary['good_count']} | 一般: {summary['acceptable_count']} | 较差: {summary['poor_count']}")
            print(f"  总体评级: {summary['overall_grade']}")
            print()
            
        # 打印建议
        if "recommendations" in self.results:
            print("💡 优化建议:")
            for rec in self.results["recommendations"]:
                print(f"  {rec}")
            print()
            
    def save_json_report(self, filename=None):
        """保存JSON格式报告"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            test_name = self.results.get("test_name", "performance_test").replace(" ", "_")
            filename = f"performance_report_{test_name}_{timestamp}.json"
            
        report_dir = os.path.join(os.path.dirname(__file__), "reports")
        os.makedirs(report_dir, exist_ok=True)
        
        filepath = os.path.join(report_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)
            
        print(f"📄 详细报告已保存: {filepath}")
        
    def generate_full_report(self):
        """生成完整报告"""
        self.end_test_session()
        self.generate_summary()
        self.generate_recommendations()
        self.print_console_report()
        self.save_json_report()

# 全局报告器实例
_global_reporter = None

def get_reporter():
    """获取全局报告器实例"""
    global _global_reporter
    if _global_reporter is None:
        _global_reporter = PerformanceReporter()
    return _global_reporter

def start_test_report(test_name):
    """开始测试报告"""
    reporter = get_reporter()
    reporter.start_test_session(test_name)
    return reporter

def finish_test_report():
    """完成测试报告"""
    reporter = get_reporter()
    reporter.generate_full_report()
    return reporter
