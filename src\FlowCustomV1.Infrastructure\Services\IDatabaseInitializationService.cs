using FlowCustomV1.Infrastructure.Health;

namespace FlowCustomV1.Infrastructure.Services;

/// <summary>
/// 数据库初始化服务接口
/// 提供数据库自动创建、迁移和健康检查功能
/// </summary>
public interface IDatabaseInitializationService
{
    /// <summary>
    /// 初始化数据库
    /// 包括创建数据库、应用迁移、验证表结构等完整流程
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>初始化是否成功</returns>
    Task<bool> InitializeDatabaseAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证数据库表结构
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证是否通过</returns>
    Task<bool> ValidateSchemaAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 迁移数据库到最新版本
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>迁移是否成功</returns>
    Task<bool> MigrateDatabaseAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 检查数据库健康状态
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>健康状态</returns>
    Task<DatabaseHealthStatus> CheckDatabaseHealthAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 确保数据库存在
    /// 如果数据库不存在则自动创建
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作是否成功</returns>
    Task<bool> EnsureDatabaseCreatedAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取待处理的迁移列表
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>待处理的迁移ID列表</returns>
    Task<IEnumerable<string>> GetPendingMigrationsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取已应用的迁移列表
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>已应用的迁移ID列表</returns>
    Task<IEnumerable<string>> GetAppliedMigrationsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 备份数据库
    /// </summary>
    /// <param name="backupPath">备份文件路径</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>备份是否成功</returns>
    Task<bool> BackupDatabaseAsync(string? backupPath = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 修复数据库
    /// 尝试自动修复常见的数据库问题
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>修复是否成功</returns>
    Task<bool> RepairDatabaseAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 重置数据库
    /// 删除所有表并重新创建
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>重置是否成功</returns>
    Task<bool> ResetDatabaseAsync(CancellationToken cancellationToken = default);
}
