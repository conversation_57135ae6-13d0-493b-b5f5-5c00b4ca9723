using FlowCustomV1.Core.Models.Workflow;

namespace FlowCustomV1.Core.Interfaces;

/// <summary>
/// 工作流调度器接口
/// 负责工作流的编排和调度逻辑（纯业务逻辑，不涉及技术实现）
/// </summary>
public interface IWorkflowScheduler
{
    /// <summary>
    /// 调度工作流执行
    /// </summary>
    /// <param name="workflowDefinition">工作流定义</param>
    /// <param name="inputData">输入数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>执行结果</returns>
    Task<WorkflowExecutionResult> ScheduleWorkflowAsync(
        WorkflowDefinition workflowDefinition, 
        Dictionary<string, object>? inputData = null, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 取消工作流执行
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>取消是否成功</returns>
    Task<bool> CancelExecutionAsync(string executionId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 工作流执行开始事件
    /// </summary>
    event EventHandler<WorkflowExecutionStartedEventArgs>? ExecutionStarted;

    /// <summary>
    /// 工作流执行完成事件
    /// </summary>
    event EventHandler<WorkflowExecutionCompletedEventArgs>? ExecutionCompleted;

    /// <summary>
    /// 工作流执行失败事件
    /// </summary>
    event EventHandler<WorkflowExecutionErrorEventArgs>? ExecutionFailed;
}

/// <summary>
/// 工作流执行开始事件参数
/// </summary>
public class WorkflowExecutionStartedEventArgs : EventArgs
{
    /// <summary>
    /// 执行ID
    /// </summary>
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 工作流ID
    /// </summary>
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime StartedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 输入数据
    /// </summary>
    public Dictionary<string, object> InputData { get; set; } = new();
}
