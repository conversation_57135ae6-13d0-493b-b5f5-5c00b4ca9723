# v0.0.1.10 内置节点功能测试指南

## 📋 测试概览

**版本**: v0.0.1.10  
**状态**: 测试就绪  
**节点总数**: 16种内置节点类型  
**测试范围**: 功能测试、集成测试、性能测试  

## 🎯 节点分类和测试重点

### 1. 基础控制节点 (3种)

#### Start - 开始节点
- **功能**: 工作流启动入口
- **测试重点**: 
  - 输出数据完整性 (started、startTime、workflowId、executionId)
  - 工作流上下文初始化
- **预期输出**: `started: true`, `startTime: DateTime`, `workflowId: string`, `executionId: string`

#### End - 结束节点  
- **功能**: 工作流结束出口
- **测试重点**:
  - 执行时长计算 (duration)
  - 最终结果传递 (finalResult)
- **预期输出**: `completed: true`, `endTime: DateTime`, `duration: TimeSpan`

#### Task - 任务节点
- **功能**: 通用任务执行
- **测试重点**:
  - 可配置延迟执行
  - 任务名称和参数处理
- **预期输出**: `taskCompleted: true`, `taskName: string`, `executedAt: DateTime`

### 2. 触发器节点 (3种)

#### TimerTrigger - 定时触发器
- **功能**: 按时间间隔触发工作流
- **测试重点**:
  - 时间间隔配置 (intervalSeconds)
  - 最大执行次数控制 (maxExecutions)
  - 执行计数器 (currentExecution)
- **配置参数**: `intervalSeconds`, `maxExecutions`, `currentExecution`
- **预期输出**: `triggered: boolean`, `triggerTime: DateTime`, `nextExecution: number`

#### EventTrigger - 事件触发器
- **功能**: 监听特定事件触发工作流
- **测试重点**:
  - 事件类型识别 (eventType)
  - 事件源追踪 (eventSource)
  - 事件数据传递 (eventData)
- **配置参数**: `eventType`, `eventSource`, `eventData`
- **预期输出**: `triggered: boolean`, `eventType: string`, `eventId: string`

#### WebhookTrigger - Webhook触发器
- **功能**: 接收HTTP Webhook请求触发工作流
- **测试重点**:
  - Webhook URL配置
  - HTTP方法支持 (GET、POST等)
  - 请求头和载荷处理
- **配置参数**: `webhookUrl`, `method`, `headers`, `payload`
- **预期输出**: `triggered: boolean`, `requestId: string`, `triggerTime: DateTime`

### 3. 动作节点 (3种)

#### HttpRequest - HTTP请求节点
- **功能**: 发送HTTP请求到指定URL
- **测试重点**:
  - 多种HTTP方法支持 (GET、POST、PUT、DELETE)
  - 请求头和认证处理
  - 响应解析和错误处理
- **配置参数**: `url`, `method`, `headers`, `body`, `timeout`
- **预期输出**: `success: boolean`, `statusCode: number`, `responseBody: string`

#### DataProcessor - 数据处理器
- **功能**: 对输入数据进行处理和转换
- **测试重点**:
  - 多种处理操作 (passthrough、filter、transform、aggregate)
  - 数据过滤和转换规则
  - 聚合计算功能
- **配置参数**: `operation`, `data`, `rules`
- **预期输出**: `success: boolean`, `processedData: object`, `processedCount: number`

#### NotificationSender - 通知发送器
- **功能**: 发送各种类型的通知消息
- **测试重点**:
  - 多种通知类型 (email、sms、webhook、log)
  - 消息内容和接收者配置
  - 优先级和元数据处理
- **配置参数**: `type`, `message`, `recipient`, `priority`, `metadata`
- **预期输出**: `success: boolean`, `notificationId: string`, `sentAt: DateTime`

### 4. 控制流节点 (3种)

#### IfCondition - 条件分支
- **功能**: 根据条件执行不同的分支逻辑
- **测试重点**:
  - 多种比较操作符 (equals、greaterthan、contains等)
  - 条件表达式评估
  - 分支结果输出
- **配置参数**: `leftValue`, `rightValue`, `operator`
- **预期输出**: `conditionResult: boolean`, `branch: string`, `evaluatedAt: DateTime`

#### ForLoop - 循环执行
- **功能**: 按指定次数循环执行逻辑
- **测试重点**:
  - 循环范围控制 (startIndex、endIndex、step)
  - 最大迭代次数限制
  - 取消令牌支持
- **配置参数**: `startIndex`, `endIndex`, `step`, `maxIterations`
- **预期输出**: `success: boolean`, `iterations: array`, `totalIterations: number`

#### ParallelExecution - 并行执行
- **功能**: 并行执行多个任务
- **测试重点**:
  - 并发度控制 (maxConcurrency)
  - 任务超时处理
  - 结果聚合和错误处理
- **配置参数**: `tasks`, `maxConcurrency`, `timeoutMs`
- **预期输出**: `success: boolean`, `results: array`, `successCount: number`

### 5. 数据转换节点 (2种)

#### DataMapper - 数据映射器
- **功能**: 将输入数据映射到指定的输出格式
- **测试重点**:
  - 映射规则配置 (mappingRules)
  - 默认值处理
  - 严格模式验证
- **配置参数**: `mappingRules`, `sourceData`, `defaultValue`, `strictMode`
- **预期输出**: `success: boolean`, `mappedData: object`, `mappingErrors: array`

#### DataFilter - 数据过滤器
- **功能**: 根据指定条件过滤数据
- **测试重点**:
  - 多种过滤类型 (include、exclude、condition、notnull)
  - 过滤键和条件配置
  - 过滤结果统计
- **配置参数**: `filterType`, `filterKeys`, `filterConditions`, `sourceData`
- **预期输出**: `success: boolean`, `filteredData: object`, `filteredCount: number`

### 6. 外部服务集成节点 (3种)

#### MySqlDatabase - MySQL数据库
- **功能**: 连接MySQL数据库执行查询和操作
- **测试重点**:
  - 多种SQL操作 (SELECT、INSERT、UPDATE、DELETE)
  - 参数化查询支持
  - 连接超时和错误处理
- **配置参数**: `connectionString`, `queryType`, `sqlQuery`, `parameters`, `timeout`
- **预期输出**: `success: boolean`, `queryResult: array`, `affectedRows: number`

#### NatsMessage - NATS消息队列
- **功能**: 发送和接收NATS消息队列消息
- **测试重点**:
  - 多种NATS操作 (publish、subscribe、request)
  - 服务器集群配置
  - 队列组和超时处理
- **配置参数**: `operation`, `subject`, `message`, `servers`, `timeout`, `queueGroup`
- **预期输出**: `success: boolean`, `operationResult: object`, `executedAt: DateTime`

#### RestApiCall - REST API调用
- **功能**: 调用REST API接口，支持多种HTTP方法
- **测试重点**:
  - 全面的HTTP方法支持 (GET、POST、PUT、DELETE、PATCH、HEAD、OPTIONS)
  - 多种认证方式 (Bearer、Basic、API Key)
  - 响应解析和错误处理
- **配置参数**: `url`, `method`, `headers`, `body`, `contentType`, `authentication`
- **预期输出**: `success: boolean`, `statusCode: number`, `responseBody: string`, `duration: number`

## 🧪 测试建议

### 功能测试
1. **单节点测试**: 每种节点类型的基本功能验证
2. **参数测试**: 各种配置参数的边界值测试
3. **错误处理测试**: 异常情况和错误恢复测试

### 集成测试
1. **节点组合测试**: 多个节点组成的工作流测试
2. **数据流测试**: 节点间数据传递和转换测试
3. **外部服务测试**: 与MySQL、NATS、REST API的集成测试

### 性能测试
1. **单节点性能**: 各节点类型的执行性能测试
2. **并发性能**: 并行执行和高并发场景测试
3. **内存使用**: 动态编译和插件加载的内存占用测试

## ✅ 测试就绪确认

v0.0.1.10版本现已具备：
- ✅ 16种内置节点类型完整实现
- ✅ 覆盖所有核心工作流场景
- ✅ 完整的外部服务集成能力
- ✅ 丰富的配置选项和错误处理
- ✅ 可开展全面的功能、集成、性能测试

**建议测试顺序**: 基础控制节点 → 数据转换节点 → 动作节点 → 控制流节点 → 触发器节点 → 外部服务集成节点
