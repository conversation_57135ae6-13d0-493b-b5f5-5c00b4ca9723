namespace FlowCustomV1.Core.Constants;

/// <summary>
/// 系统常量定义
/// 包含系统级别的常量和配置值
/// </summary>
public static class SystemConstants
{
    /// <summary>
    /// 系统版本信息
    /// </summary>
    public static class Version
    {
        /// <summary>
        /// 当前系统版本
        /// </summary>
        public const string Current = "0.0.0.2";

        /// <summary>
        /// API版本
        /// </summary>
        public const string Api = "v1";

        /// <summary>
        /// 协议版本
        /// </summary>
        public const string Protocol = "1.0";

        /// <summary>
        /// 最小兼容版本
        /// </summary>
        public const string MinCompatible = "0.0.0.1";
    }

    /// <summary>
    /// 默认配置值
    /// </summary>
    public static class Defaults
    {
        /// <summary>
        /// 默认HTTP端口
        /// </summary>
        public const int HttpPort = 8080;

        /// <summary>
        /// 默认NATS端口
        /// </summary>
        public const int NatsPort = 4222;

        /// <summary>
        /// 默认管理端口
        /// </summary>
        public const int ManagementPort = 8081;

        /// <summary>
        /// 默认心跳间隔（秒）
        /// </summary>
        public const int HeartbeatIntervalSeconds = 30;

        /// <summary>
        /// 默认健康检查间隔（秒）
        /// </summary>
        public const int HealthCheckIntervalSeconds = 60;

        /// <summary>
        /// 默认节点超时时间（秒）
        /// </summary>
        public const int NodeTimeoutSeconds = 180;

        /// <summary>
        /// 默认最大并发执行数
        /// </summary>
        public const int MaxConcurrentExecutions = 10;

        /// <summary>
        /// 默认工作流超时时间（分钟）
        /// </summary>
        public const int WorkflowTimeoutMinutes = 60;

        /// <summary>
        /// 默认节点执行超时时间（分钟）
        /// </summary>
        public const int NodeExecutionTimeoutMinutes = 30;
    }

    /// <summary>
    /// 限制值
    /// </summary>
    public static class Limits
    {
        /// <summary>
        /// 最大节点名称长度
        /// </summary>
        public const int MaxNodeNameLength = 100;

        /// <summary>
        /// 最大工作流名称长度
        /// </summary>
        public const int MaxWorkflowNameLength = 200;

        /// <summary>
        /// 最大描述长度
        /// </summary>
        public const int MaxDescriptionLength = 1000;

        /// <summary>
        /// 最大元数据键长度
        /// </summary>
        public const int MaxMetadataKeyLength = 50;

        /// <summary>
        /// 最大元数据值长度
        /// </summary>
        public const int MaxMetadataValueLength = 500;

        /// <summary>
        /// 最大工作流节点数
        /// </summary>
        public const int MaxWorkflowNodes = 1000;

        /// <summary>
        /// 最大工作流连接数
        /// </summary>
        public const int MaxWorkflowConnections = 2000;

        /// <summary>
        /// 最大并发工作流执行数
        /// </summary>
        public const int MaxConcurrentWorkflows = 100;

        /// <summary>
        /// 最大历史记录保留天数
        /// </summary>
        public const int MaxHistoryRetentionDays = 90;
    }

    /// <summary>
    /// 系统标识符
    /// </summary>
    public static class Identifiers
    {
        /// <summary>
        /// 系统名称
        /// </summary>
        public const string SystemName = "FlowCustomV1";

        /// <summary>
        /// 默认集群名称
        /// </summary>
        public const string DefaultClusterName = "FlowCustomV1-Cluster";

        /// <summary>
        /// 系统用户ID
        /// </summary>
        public const string SystemUserId = "system";

        /// <summary>
        /// 匿名用户ID
        /// </summary>
        public const string AnonymousUserId = "anonymous";

        /// <summary>
        /// 默认租户ID
        /// </summary>
        public const string DefaultTenantId = "default";
    }

    /// <summary>
    /// 文件和路径
    /// </summary>
    public static class Paths
    {
        /// <summary>
        /// 配置文件名
        /// </summary>
        public const string ConfigFileName = "appsettings.json";

        /// <summary>
        /// 日志目录
        /// </summary>
        public const string LogDirectory = "logs";

        /// <summary>
        /// 数据目录
        /// </summary>
        public const string DataDirectory = "data";

        /// <summary>
        /// 插件目录
        /// </summary>
        public const string PluginsDirectory = "plugins";

        /// <summary>
        /// 临时目录
        /// </summary>
        public const string TempDirectory = "temp";

        /// <summary>
        /// 备份目录
        /// </summary>
        public const string BackupDirectory = "backup";
    }

    /// <summary>
    /// 消息和事件
    /// </summary>
    public static class Messages
    {
        /// <summary>
        /// 默认消息过期时间（分钟）
        /// </summary>
        public const int DefaultMessageTtlMinutes = 30;

        /// <summary>
        /// 最大消息大小（字节）
        /// </summary>
        public const int MaxMessageSizeBytes = 1024 * 1024; // 1MB

        /// <summary>
        /// 最大批处理消息数
        /// </summary>
        public const int MaxBatchMessageCount = 100;

        /// <summary>
        /// 消息重试次数
        /// </summary>
        public const int MessageRetryCount = 3;

        /// <summary>
        /// 消息重试间隔（秒）
        /// </summary>
        public const int MessageRetryIntervalSeconds = 5;
    }

    /// <summary>
    /// 性能和监控
    /// </summary>
    public static class Performance
    {
        /// <summary>
        /// 性能指标收集间隔（秒）
        /// </summary>
        public const int MetricsCollectionIntervalSeconds = 60;

        /// <summary>
        /// 性能数据保留小时数
        /// </summary>
        public const int MetricsRetentionHours = 24;

        /// <summary>
        /// 慢查询阈值（毫秒）
        /// </summary>
        public const int SlowQueryThresholdMs = 1000;

        /// <summary>
        /// 内存使用警告阈值（百分比）
        /// </summary>
        public const int MemoryWarningThreshold = 80;

        /// <summary>
        /// CPU使用警告阈值（百分比）
        /// </summary>
        public const int CpuWarningThreshold = 80;

        /// <summary>
        /// 磁盘使用警告阈值（百分比）
        /// </summary>
        public const int DiskWarningThreshold = 85;
    }

    /// <summary>
    /// 安全相关
    /// </summary>
    public static class Security
    {
        /// <summary>
        /// 默认令牌过期时间（小时）
        /// </summary>
        public const int DefaultTokenExpirationHours = 24;

        /// <summary>
        /// 最大登录尝试次数
        /// </summary>
        public const int MaxLoginAttempts = 5;

        /// <summary>
        /// 账户锁定时间（分钟）
        /// </summary>
        public const int AccountLockoutMinutes = 30;

        /// <summary>
        /// 密码最小长度
        /// </summary>
        public const int MinPasswordLength = 8;

        /// <summary>
        /// 会话超时时间（分钟）
        /// </summary>
        public const int SessionTimeoutMinutes = 120;
    }
}
