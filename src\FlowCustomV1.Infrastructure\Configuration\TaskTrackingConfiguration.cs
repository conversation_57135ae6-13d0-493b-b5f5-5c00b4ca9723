using System.ComponentModel.DataAnnotations;

namespace FlowCustomV1.Infrastructure.Configuration;

/// <summary>
/// 任务跟踪配置
/// </summary>
public class TaskTrackingConfiguration
{
    /// <summary>
    /// 配置节名称
    /// </summary>
    public const string SectionName = "TaskTracking";

    /// <summary>
    /// 清理间隔（分钟）
    /// </summary>
    [Range(1, 1440)]
    public int CleanupIntervalMinutes { get; set; } = 60;

    /// <summary>
    /// 统计更新间隔（秒）
    /// </summary>
    [Range(10, 3600)]
    public int StatisticsUpdateIntervalSeconds { get; set; } = 30;

    /// <summary>
    /// 最大最近完成任务数量
    /// </summary>
    [Range(100, 10000)]
    public int MaxRecentCompletedTasks { get; set; } = 1000;

    /// <summary>
    /// 自动清理完成任务
    /// </summary>
    public bool AutoCleanupOnCompletion { get; set; } = false;

    /// <summary>
    /// 任务状态保留天数
    /// </summary>
    [Range(1, 365)]
    public int TaskStateRetentionDays { get; set; } = 7;

    /// <summary>
    /// 数据保留天数（TaskStateRetentionDays的别名，保持向后兼容）
    /// </summary>
    [Range(1, 365)]
    public int DataRetentionDays
    {
        get => TaskStateRetentionDays;
        set => TaskStateRetentionDays = value;
    }

    /// <summary>
    /// 启用详细日志
    /// </summary>
    public bool EnableDetailedLogging { get; set; } = false;

    /// <summary>
    /// 启用性能监控
    /// </summary>
    public bool EnablePerformanceMonitoring { get; set; } = true;

    /// <summary>
    /// 最大并发跟踪任务数
    /// </summary>
    [Range(10, 10000)]
    public int MaxConcurrentTrackedTasks { get; set; } = 1000;

    /// <summary>
    /// 任务超时检查间隔（秒）
    /// </summary>
    [Range(10, 3600)]
    public int TimeoutCheckIntervalSeconds { get; set; } = 60;

    /// <summary>
    /// 启用任务超时检查
    /// </summary>
    public bool EnableTimeoutCheck { get; set; } = true;

    /// <summary>
    /// 默认任务超时时间（毫秒）
    /// </summary>
    [Range(1000, 3600000)]
    public long DefaultTaskTimeoutMs { get; set; } = 300000; // 5分钟

    /// <summary>
    /// 启用任务进度跟踪
    /// </summary>
    public bool EnableProgressTracking { get; set; } = true;

    /// <summary>
    /// 进度更新最小间隔（毫秒）
    /// </summary>
    [Range(100, 60000)]
    public int ProgressUpdateMinIntervalMs { get; set; } = 1000;

    /// <summary>
    /// 启用资源使用监控
    /// </summary>
    public bool EnableResourceMonitoring { get; set; } = true;

    /// <summary>
    /// 资源监控间隔（秒）
    /// </summary>
    [Range(5, 300)]
    public int ResourceMonitoringIntervalSeconds { get; set; } = 30;

    /// <summary>
    /// 启用事件发布
    /// </summary>
    public bool EnableEventPublishing { get; set; } = true;

    /// <summary>
    /// 事件发布主题前缀
    /// </summary>
    public string EventTopicPrefix { get; set; } = "task.tracking";

    /// <summary>
    /// 批量操作大小
    /// </summary>
    [Range(10, 1000)]
    public int BatchOperationSize { get; set; } = 100;

    /// <summary>
    /// 启用任务依赖跟踪
    /// </summary>
    public bool EnableDependencyTracking { get; set; } = true;

    /// <summary>
    /// 最大任务依赖深度
    /// </summary>
    [Range(1, 20)]
    public int MaxDependencyDepth { get; set; } = 10;

    /// <summary>
    /// 启用任务重试跟踪
    /// </summary>
    public bool EnableRetryTracking { get; set; } = true;

    /// <summary>
    /// 最大重试历史记录数
    /// </summary>
    [Range(1, 100)]
    public int MaxRetryHistoryCount { get; set; } = 10;

    /// <summary>
    /// 启用任务执行日志
    /// </summary>
    public bool EnableExecutionLogging { get; set; } = true;

    /// <summary>
    /// 最大执行日志条数
    /// </summary>
    [Range(10, 10000)]
    public int MaxExecutionLogCount { get; set; } = 1000;

    /// <summary>
    /// 日志级别过滤
    /// </summary>
    public List<string> LogLevelFilters { get; set; } = new() { "Information", "Warning", "Error", "Critical" };

    /// <summary>
    /// 启用任务状态历史
    /// </summary>
    public bool EnableStatusHistory { get; set; } = true;

    /// <summary>
    /// 最大状态历史记录数
    /// </summary>
    [Range(5, 1000)]
    public int MaxStatusHistoryCount { get; set; } = 50;

    /// <summary>
    /// 启用任务性能分析
    /// </summary>
    public bool EnablePerformanceAnalysis { get; set; } = true;

    /// <summary>
    /// 性能分析采样率（0-1）
    /// </summary>
    [Range(0.0, 1.0)]
    public double PerformanceAnalysisSamplingRate { get; set; } = 0.1;

    /// <summary>
    /// 启用任务告警
    /// </summary>
    public bool EnableTaskAlerting { get; set; } = true;

    /// <summary>
    /// 任务执行时间告警阈值（毫秒）
    /// </summary>
    [Range(1000, 3600000)]
    public long ExecutionTimeAlertThresholdMs { get; set; } = 600000; // 10分钟

    /// <summary>
    /// 任务失败率告警阈值（0-1）
    /// </summary>
    [Range(0.0, 1.0)]
    public double FailureRateAlertThreshold { get; set; } = 0.1; // 10%

    /// <summary>
    /// 告警检查间隔（秒）
    /// </summary>
    [Range(30, 3600)]
    public int AlertCheckIntervalSeconds { get; set; } = 300; // 5分钟

    /// <summary>
    /// 启用任务指标导出
    /// </summary>
    public bool EnableMetricsExport { get; set; } = true;

    /// <summary>
    /// 指标导出间隔（秒）
    /// </summary>
    [Range(10, 3600)]
    public int MetricsExportIntervalSeconds { get; set; } = 60;

    /// <summary>
    /// 指标保留天数
    /// </summary>
    [Range(1, 365)]
    public int MetricsRetentionDays { get; set; } = 30;

    /// <summary>
    /// 启用分布式跟踪
    /// </summary>
    public bool EnableDistributedTracing { get; set; } = true;

    /// <summary>
    /// 跟踪采样率（0-1）
    /// </summary>
    [Range(0.0, 1.0)]
    public double TracingSamplingRate { get; set; } = 0.1;

    /// <summary>
    /// 跟踪服务名称
    /// </summary>
    public string TracingServiceName { get; set; } = "FlowCustomV1.TaskTracking";

    /// <summary>
    /// 启用健康检查
    /// </summary>
    public bool EnableHealthCheck { get; set; } = true;

    /// <summary>
    /// 健康检查间隔（秒）
    /// </summary>
    [Range(10, 3600)]
    public int HealthCheckIntervalSeconds { get; set; } = 60;

    /// <summary>
    /// 健康检查超时时间（毫秒）
    /// </summary>
    [Range(1000, 30000)]
    public int HealthCheckTimeoutMs { get; set; } = 5000;

    /// <summary>
    /// 验证配置的有效性
    /// </summary>
    /// <returns>验证结果</returns>
    public ValidationResult Validate()
    {
        var errors = new List<string>();

        // 检查时间间隔的合理性
        if (StatisticsUpdateIntervalSeconds >= CleanupIntervalMinutes * 60)
        {
            errors.Add("统计更新间隔不能大于等于清理间隔");
        }

        if (ProgressUpdateMinIntervalMs >= DefaultTaskTimeoutMs)
        {
            errors.Add("进度更新间隔不能大于等于默认任务超时时间");
        }

        // 检查采样率
        if (PerformanceAnalysisSamplingRate < 0 || PerformanceAnalysisSamplingRate > 1)
        {
            errors.Add("性能分析采样率必须在0-1之间");
        }

        if (TracingSamplingRate < 0 || TracingSamplingRate > 1)
        {
            errors.Add("跟踪采样率必须在0-1之间");
        }

        // 检查阈值
        if (FailureRateAlertThreshold < 0 || FailureRateAlertThreshold > 1)
        {
            errors.Add("失败率告警阈值必须在0-1之间");
        }

        // 检查保留期
        if (TaskStateRetentionDays > MetricsRetentionDays)
        {
            errors.Add("任务状态保留天数不应大于指标保留天数");
        }

        return new ValidationResult
        {
            IsValid = errors.Count == 0,
            Errors = errors
        };
    }
}

/// <summary>
/// 任务跟踪配置验证结果
/// </summary>
public class TaskTrackingValidationResult
{
    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 错误列表
    /// </summary>
    public List<string> Errors { get; set; } = new();
}
