import React from 'react';
import { Card, Button, Space, Steps, Table, Tag, Collapse, Alert } from 'antd';
import { 
  BugOutlined, 
  PlayCircleOutlined, 
  PauseCircleOutlined,
  StepForwardOutlined,
  ReloadOutlined,
  EyeOutlined,

} from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';

const { Step } = Steps;
const { Panel: _Panel } = Collapse;

// 模拟调试数据
const mockDebugSession = {
  workflowId: 'workflow-001',
  workflowName: '数据处理流水线',
  executionId: 'exec-debug-001',
  status: 'Debugging',
  currentNodeId: 'node-002',
  breakpoints: ['node-002', 'node-004'],
  variables: [
    { name: 'inputData', type: 'Object', value: '{"records": 100, "format": "json"}' },
    { name: 'processedCount', type: 'Number', value: '45' },
    { name: 'errorCount', type: 'Number', value: '2' },
    { name: 'currentBatch', type: 'Array', value: '[{...}, {...}, {...}]' },
  ],
  executionTrace: [
    { nodeId: 'node-001', nodeName: '数据输入', status: 'Completed', duration: 120, timestamp: '15:30:01' },
    { nodeId: 'node-002', nodeName: '数据验证', status: 'Paused', duration: 0, timestamp: '15:30:02' },
    { nodeId: 'node-003', nodeName: '数据转换', status: 'Pending', duration: 0, timestamp: '-' },
    { nodeId: 'node-004', nodeName: '数据输出', status: 'Pending', duration: 0, timestamp: '-' },
  ],
  logs: [
    { level: 'INFO', timestamp: '15:30:01.123', message: '开始执行工作流: 数据处理流水线' },
    { level: 'INFO', timestamp: '15:30:01.456', message: '节点 node-001 开始执行' },
    { level: 'INFO', timestamp: '15:30:01.576', message: '成功读取输入数据，记录数: 100' },
    { level: 'INFO', timestamp: '15:30:02.001', message: '节点 node-002 开始执行' },
    { level: 'WARN', timestamp: '15:30:02.234', message: '发现 2 条无效记录' },
    { level: 'INFO', timestamp: '15:30:02.345', message: '在断点处暂停执行: node-002' },
  ],
};

const WorkflowDebugger: React.FC = () => {
  const [debugSession, _setDebugSession] = React.useState(mockDebugSession);
  const [selectedVariable, setSelectedVariable] = React.useState<any>(null);

  // 获取状态标签
  const getStatusTag = (status: string) => {
    const statusMap = {
      Completed: { color: 'success', text: '已完成' },
      Paused: { color: 'warning', text: '已暂停' },
      Pending: { color: 'default', text: '等待中' },
      Running: { color: 'processing', text: '运行中' },
      Failed: { color: 'error', text: '失败' },
    };
    const config = statusMap[status as keyof typeof statusMap] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };



  // 变量表格列定义
  const variableColumns = [
    {
      title: '变量名',
      dataIndex: 'name',
      key: 'name',
      render: (text: string) => <code className="bg-gray-100 px-1 rounded">{text}</code>,
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => <Tag color="blue">{type}</Tag>,
    },
    {
      title: '值',
      dataIndex: 'value',
      key: 'value',
      ellipsis: true,
      render: (value: string) => (
        <div className="max-w-xs truncate" title={value}>
          {value}
        </div>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: any) => (
        <Button 
          type="text" 
          size="small" 
          icon={<EyeOutlined />}
          onClick={() => setSelectedVariable(record)}
        >
          查看
        </Button>
      ),
    },
  ];

  // 执行跟踪表格列定义
  const traceColumns = [
    {
      title: '节点ID',
      dataIndex: 'nodeId',
      key: 'nodeId',
      render: (text: string) => <code className="bg-gray-100 px-1 rounded text-xs">{text}</code>,
    },
    {
      title: '节点名称',
      dataIndex: 'nodeName',
      key: 'nodeName',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => getStatusTag(status),
    },
    {
      title: '执行时间',
      dataIndex: 'duration',
      key: 'duration',
      render: (duration: number) => duration > 0 ? `${duration}ms` : '-',
    },
    {
      title: '时间戳',
      dataIndex: 'timestamp',
      key: 'timestamp',
    },
  ];

  return (
    <div className="page-container">
      <div className="page-header">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="page-title">
              <BugOutlined className="mr-2" />
              工作流调试器
            </h1>
            <p className="page-description">调试和测试工作流执行过程</p>
          </div>
          <Space>
            <Button icon={<ReloadOutlined />}>
              重置
            </Button>
            <Button icon={<StepForwardOutlined />}>
              单步执行
            </Button>
            <Button type="primary" icon={<PlayCircleOutlined />}>
              继续执行
            </Button>
          </Space>
        </div>

        <Alert
          message="调试会话活跃"
          description={`正在调试工作流: ${debugSession.workflowName} (${debugSession.executionId})`}
          type="info"
          showIcon
          className="mb-6"
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 执行流程 */}
        <div className="lg:col-span-2">
          <ProCard title="执行流程" className="mb-6">
            <Steps
              current={debugSession.executionTrace.findIndex(t => t.status === 'Paused')}
              status={debugSession.status === 'Debugging' ? 'process' : 'finish'}
              direction="vertical"
            >
              {debugSession.executionTrace.map((trace, _index) => (
                <Step
                  key={trace.nodeId}
                  title={trace.nodeName}
                  description={
                    <div>
                      <div className="flex items-center space-x-2 mb-1">
                        {getStatusTag(trace.status)}
                        {debugSession.breakpoints.includes(trace.nodeId) && (
                          <Tag color="red">断点</Tag>
                        )}
                      </div>
                      <div className="text-xs text-gray-500">
                        {trace.nodeId} | {trace.timestamp} | {trace.duration > 0 ? `${trace.duration}ms` : ''}
                      </div>
                    </div>
                  }
                  status={
                    trace.status === 'Completed' ? 'finish' :
                    trace.status === 'Paused' ? 'process' :
                    trace.status === 'Failed' ? 'error' : 'wait'
                  }
                />
              ))}
            </Steps>
          </ProCard>

          {/* 执行跟踪 */}
          <ProCard title="执行跟踪">
            <Table
              columns={traceColumns}
              dataSource={debugSession.executionTrace}
              rowKey="nodeId"
              pagination={false}
              size="small"
            />
          </ProCard>
        </div>

        {/* 调试信息 */}
        <div>
          <ProCard title="调试信息" className="mb-6">
            <div className="space-y-4">
              <div>
                <div className="text-sm text-gray-500 mb-1">当前节点</div>
                <code className="bg-blue-100 px-2 py-1 rounded text-sm">
                  {debugSession.currentNodeId}
                </code>
              </div>
              <div>
                <div className="text-sm text-gray-500 mb-1">执行状态</div>
                {getStatusTag(debugSession.status)}
              </div>
              <div>
                <div className="text-sm text-gray-500 mb-1">断点数量</div>
                <span className="font-semibold">{debugSession.breakpoints.length}</span>
              </div>
            </div>
          </ProCard>

          {/* 变量监视 */}
          <ProCard title="变量监视" className="mb-6">
            <Table
              columns={variableColumns}
              dataSource={debugSession.variables}
              rowKey="name"
              pagination={false}
              size="small"
            />
          </ProCard>

          {/* 调试控制 */}
          <ProCard title="调试控制">
            <div className="space-y-3">
              <Button block icon={<PlayCircleOutlined />}>
                继续执行
              </Button>
              <Button block icon={<PauseCircleOutlined />}>
                暂停执行
              </Button>
              <Button block icon={<StepForwardOutlined />}>
                单步执行
              </Button>
              <Button block icon={<ReloadOutlined />}>
                重新开始
              </Button>
            </div>
          </ProCard>
        </div>
      </div>

      {/* 执行日志 */}
      <div className="mt-6">
        <ProCard title="执行日志">
          <div className="bg-black text-green-400 p-4 rounded font-mono text-sm max-h-64 overflow-y-auto">
            {debugSession.logs.map((log, index) => (
              <div key={index} className="mb-1">
                <span className="text-gray-500">[{log.timestamp}]</span>
                <span className={`ml-2 ${
                  log.level === 'ERROR' ? 'text-red-400' :
                  log.level === 'WARN' ? 'text-yellow-400' :
                  'text-green-400'
                }`}>
                  {log.level}
                </span>
                <span className="ml-2">{log.message}</span>
              </div>
            ))}
          </div>
        </ProCard>
      </div>

      {/* 变量详情模态框 */}
      {selectedVariable && (
        <Card
          title={`变量详情: ${selectedVariable.name}`}
          className="fixed top-20 right-6 w-96 shadow-lg z-50"
          extra={
            <Button 
              type="text" 
              onClick={() => setSelectedVariable(null)}
            >
              ×
            </Button>
          }
        >
          <div className="space-y-3">
            <div>
              <div className="text-sm text-gray-500">类型</div>
              <Tag color="blue">{selectedVariable.type}</Tag>
            </div>
            <div>
              <div className="text-sm text-gray-500 mb-2">值</div>
              <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto max-h-40">
                {selectedVariable.value}
              </pre>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};

export default WorkflowDebugger;
