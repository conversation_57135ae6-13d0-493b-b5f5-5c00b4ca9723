import React, { useState, useCallback } from 'react';
import { Card, Button, Space, Tabs, Input, Badge, Form, message } from 'antd';
import {
  ClockCircleOutlined,
  SettingOutlined,
  ApiOutlined,
  DatabaseOutlined,
  NotificationOutlined,
  MinusOutlined,
  PlusOutlined,
  ApartmentOutlined,
  SaveOutlined,
  PlayCircleOutlined,
  StopOutlined,
  FileOutlined,
  EditOutlined,
  EyeOutlined,
  BugOutlined,
  HistoryOutlined,
  ShareAltOutlined,
  SearchOutlined,
  ThunderboltOutlined,
  BranchesOutlined,
  MailOutlined,
  MessageOutlined,
  FilterOutlined,
  DeleteOutlined,
  InfoCircleOutlined,
  UndoOutlined,
  RedoOutlined,
  CopyOutlined,
  ScissorOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
  CompressOutlined,
  MenuOutlined,
  EyeInvisibleOutlined,
} from '@ant-design/icons';
import ReactFlow, {
  Node,
  Edge,
  addEdge,
  Background,
  Controls,
  MiniMap,
  useNodesState,
  useEdgesState,
  Connection,
  ConnectionMode,
  ReactFlowProvider,
} from 'reactflow';
import 'reactflow/dist/style.css';
import PageLayout from '@/components/Layout/PageLayout';
import PropertyPanel from '@/components/WorkflowDesigner/PropertyPanel';
import type { WorkflowNode } from '@/types/workflow';

// 导航菜单配置
const navigationTabs = [
  { key: 'design', label: '设计', icon: <EditOutlined /> },
  { key: 'preview', label: '预览', icon: <EyeOutlined /> },
  { key: 'debug', label: '调试', icon: <BugOutlined /> },
  { key: 'history', label: '历史', icon: <HistoryOutlined /> },
  { key: 'share', label: '分享', icon: <ShareAltOutlined /> }
];

// 完整的16种节点类型配置 - 基于********-临时版
const nodeGroups = [
  {
    id: 'basic',
    title: '基础控制',
    collapsed: false,
    nodes: [
      {
        id: 'Start',
        title: '开始',
        subtitle: '流程开始节点',
        icon: PlayCircleOutlined,
        color: '#52c41a',
        bgColor: '#f6ffed',
        borderColor: '#52c41a',
        type: 'Start'
      },
      {
        id: 'End',
        title: '结束',
        subtitle: '流程结束节点',
        icon: StopOutlined,
        color: '#ff4d4f',
        bgColor: '#fff2f0',
        borderColor: '#ff4d4f',
        type: 'End'
      },
      {
        id: 'Task',
        title: '任务',
        subtitle: '通用任务节点',
        icon: SettingOutlined,
        color: '#1890ff',
        bgColor: '#e6f7ff',
        borderColor: '#1890ff',
        type: 'Task'
      }
    ]
  },
  {
    id: 'trigger',
    title: '触发器',
    collapsed: false,
    nodes: [
      {
        id: 'TimerTrigger',
        title: '定时触发',
        subtitle: '定时触发器',
        icon: ClockCircleOutlined,
        color: '#fa8c16',
        bgColor: '#fff7e6',
        borderColor: '#fa8c16',
        type: 'TimerTrigger'
      },
      {
        id: 'EventTrigger',
        title: '事件触发',
        subtitle: '事件触发器',
        icon: ThunderboltOutlined,
        color: '#52c41a',
        bgColor: '#f6ffed',
        borderColor: '#52c41a',
        type: 'EventTrigger'
      },
      {
        id: 'WebhookTrigger',
        title: 'Webhook触发',
        subtitle: 'Webhook触发器',
        icon: ApiOutlined,
        color: '#722ed1',
        bgColor: '#f9f0ff',
        borderColor: '#722ed1',
        type: 'WebhookTrigger'
      }
    ]
  },
  {
    id: 'action',
    title: '动作节点',
    collapsed: false,
    nodes: [
      {
        id: 'HttpRequest',
        title: 'HTTP请求',
        subtitle: 'HTTP请求处理',
        icon: ApiOutlined,
        color: '#722ed1',
        bgColor: '#f9f0ff',
        borderColor: '#722ed1',
        type: 'HttpRequest'
      },
      {
        id: 'DataProcessor',
        title: '数据处理',
        subtitle: '数据处理器',
        icon: DatabaseOutlined,
        color: '#13c2c2',
        bgColor: '#e6fffb',
        borderColor: '#13c2c2',
        type: 'DataProcessor'
      },
      {
        id: 'NotificationSender',
        title: '通知发送',
        subtitle: '通知发送器',
        icon: NotificationOutlined,
        color: '#eb2f96',
        bgColor: '#fff0f6',
        borderColor: '#eb2f96',
        type: 'NotificationSender'
      }
    ]
  },
  {
    id: 'control',
    title: '控制流',
    collapsed: false,
    nodes: [
      {
        id: 'IfCondition',
        title: '条件判断',
        subtitle: '条件分支',
        icon: BranchesOutlined,
        color: '#fa541c',
        bgColor: '#fff2e8',
        borderColor: '#fa541c',
        type: 'IfCondition'
      },
      {
        id: 'ForLoop',
        title: '循环执行',
        subtitle: '循环控制',
        icon: SettingOutlined,
        color: '#1890ff',
        bgColor: '#e6f7ff',
        borderColor: '#1890ff',
        type: 'ForLoop'
      },
      {
        id: 'ParallelExecution',
        title: '并行执行',
        subtitle: '并行处理',
        icon: ApartmentOutlined,
        color: '#722ed1',
        bgColor: '#f9f0ff',
        borderColor: '#722ed1',
        type: 'ParallelExecution'
      }
    ]
  },
  {
    id: 'data',
    title: '数据转换',
    collapsed: false,
    nodes: [
      {
        id: 'DataMapper',
        title: '数据映射',
        subtitle: '数据映射器',
        icon: FilterOutlined,
        color: '#13c2c2',
        bgColor: '#e6fffb',
        borderColor: '#13c2c2',
        type: 'DataMapper'
      },
      {
        id: 'DataFilter',
        title: '数据过滤',
        subtitle: '数据过滤器',
        icon: FilterOutlined,
        color: '#fa8c16',
        bgColor: '#fff7e6',
        borderColor: '#fa8c16',
        type: 'DataFilter'
      }
    ]
  },
  {
    id: 'external',
    title: '外部服务',
    collapsed: false,
    nodes: [
      {
        id: 'MySqlDatabase',
        title: 'MySQL数据库',
        subtitle: 'MySQL连接',
        icon: DatabaseOutlined,
        color: '#13c2c2',
        bgColor: '#e6fffb',
        borderColor: '#13c2c2',
        type: 'MySqlDatabase'
      },
      {
        id: 'NatsMessage',
        title: 'NATS消息',
        subtitle: 'NATS消息队列',
        icon: MessageOutlined,
        color: '#722ed1',
        bgColor: '#f9f0ff',
        borderColor: '#722ed1',
        type: 'NatsMessage'
      },
      {
        id: 'RestApiCall',
        title: 'REST API',
        subtitle: 'REST API调用',
        icon: ApiOutlined,
        color: '#1890ff',
        bgColor: '#e6f7ff',
        borderColor: '#1890ff',
        type: 'RestApiCall'
      }
    ]
  }
];

// 初始节点和边
const initialNodes: Node[] = [
  {
    id: '1',
    type: 'default',
    position: { x: 250, y: 100 },
    data: { label: '开始' },
    style: { background: '#f6ffed', border: '2px solid #52c41a', borderRadius: '8px' }
  },
  {
    id: '2',
    type: 'default',
    position: { x: 400, y: 100 },
    data: { label: '任务' },
    style: { background: '#e6f7ff', border: '2px solid #1890ff', borderRadius: '8px' }
  },
  {
    id: '3',
    type: 'default',
    position: { x: 550, y: 100 },
    data: { label: '结束' },
    style: { background: '#fff2f0', border: '2px solid #ff4d4f', borderRadius: '8px' }
  }
];

const initialEdges: Edge[] = [
  { id: 'e1-2', source: '1', target: '2', animated: true },
  { id: 'e2-3', source: '2', target: '3', animated: true }
];

const WorkflowDesignerReactFlow: React.FC = () => {
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);
  const [activeTab, setActiveTab] = useState('design');
  const [groupStates, setGroupStates] = useState<Record<string, boolean>>(
    nodeGroups.reduce((acc, group) => ({ ...acc, [group.id]: !group.collapsed }), {})
  );
  const [selectedNode, setSelectedNode] = useState<WorkflowNode | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [stencilVisible, setStencilVisible] = useState(true);
  const [minimapVisible, setMinimapVisible] = useState(true);
  const [form] = Form.useForm();

  const onConnect = useCallback(
    (params: Connection) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  );

  const toggleGroup = (groupId: string) => {
    setGroupStates(prev => ({
      ...prev,
      [groupId]: !prev[groupId]
    }));
  };

  // 节点选择处理
  const onNodeClick = useCallback((event: React.MouseEvent, node: Node) => {
    const workflowNode: WorkflowNode = {
      id: node.id,
      type: node.data.type || 'Task',
      position: node.position,
      data: {
        label: node.data.label || '',
        description: node.data.description || '',
        properties: node.data.properties || {},
        status: node.data.status || 'idle'
      }
    };
    setSelectedNode(workflowNode);
  }, []);

  // 节点更新处理
  const onNodeUpdate = useCallback((nodeId: string, updates: Partial<WorkflowNode['data']>) => {
    setNodes((nds) =>
      nds.map((node) =>
        node.id === nodeId
          ? { ...node, data: { ...node.data, ...updates } }
          : node
      )
    );

    // 更新选中节点
    if (selectedNode && selectedNode.id === nodeId) {
      setSelectedNode(prev => prev ? {
        ...prev,
        data: { ...prev.data, ...updates }
      } : null);
    }
  }, [selectedNode, setNodes]);

  // 节点删除处理
  const onNodeDelete = useCallback((nodeId: string) => {
    setNodes((nds) => nds.filter((node) => node.id !== nodeId));
    setEdges((eds) => eds.filter((edge) => edge.source !== nodeId && edge.target !== nodeId));
    if (selectedNode && selectedNode.id === nodeId) {
      setSelectedNode(null);
    }
    message.success('节点已删除');
  }, [selectedNode, setNodes, setEdges]);

  // 工具栏操作
  const handleSave = () => {
    message.success('工作流已保存');
  };

  const handleRun = () => {
    message.info('开始执行工作流');
  };

  const handleUndo = () => {
    message.info('撤销操作');
  };

  const handleRedo = () => {
    message.info('重做操作');
  };

  const handleCopy = () => {
    if (selectedNode) {
      message.success('节点已复制');
    } else {
      message.warning('请先选择要复制的节点');
    }
  };

  const handleCut = () => {
    if (selectedNode) {
      message.success('节点已剪切');
    } else {
      message.warning('请先选择要剪切的节点');
    }
  };

  const handleDelete = () => {
    if (selectedNode) {
      onNodeDelete(selectedNode.id);
    } else {
      message.warning('请先选择要删除的节点');
    }
  };

  const toggleStencil = () => {
    setStencilVisible(!stencilVisible);
  };

  const toggleMinimap = () => {
    setMinimapVisible(!minimapVisible);
  };

  const onDragStart = (event: React.DragEvent, nodeType: string, nodeData: any) => {
    event.dataTransfer.setData('application/reactflow', nodeType);
    event.dataTransfer.setData('application/nodedata', JSON.stringify(nodeData));
    event.dataTransfer.effectAllowed = 'move';
  };

  const onDrop = useCallback(
    (event: React.DragEvent) => {
      event.preventDefault();

      const reactFlowBounds = (event.target as Element).getBoundingClientRect();
      const type = event.dataTransfer.getData('application/reactflow');
      const nodeDataStr = event.dataTransfer.getData('application/nodedata');
      
      if (typeof type === 'undefined' || !type) {
        return;
      }

      const nodeData = JSON.parse(nodeDataStr);
      const position = {
        x: event.clientX - reactFlowBounds.left,
        y: event.clientY - reactFlowBounds.top,
      };

      const newNode: Node = {
        id: `${type}-${Date.now()}`,
        type: 'default',
        position,
        data: { label: nodeData.title },
        style: {
          background: nodeData.bgColor,
          border: `2px solid ${nodeData.borderColor}`,
          borderRadius: '8px',
          padding: '10px'
        }
      };

      setNodes((nds) => nds.concat(newNode));
    },
    [setNodes]
  );

  const onDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
  }, []);

  // 过滤节点
  const filteredNodeGroups = nodeGroups.map(group => ({
    ...group,
    nodes: group.nodes.filter(node =>
      searchTerm === '' ||
      node.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      node.subtitle.toLowerCase().includes(searchTerm.toLowerCase())
    )
  })).filter(group => group.nodes.length > 0);

  const renderNodeLibrary = () => (
    <Card
      title={
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <span>组件库</span>
          <Badge count={nodeGroups.reduce((total, group) => total + group.nodes.length, 0)}
                 style={{ backgroundColor: '#52c41a' }} />
        </div>
      }
      className="w-64 flex-shrink-0"
      styles={{ body: { padding: '12px', height: 'calc(100% - 57px)', overflow: 'auto' } }}
      extra={
        <Button
          type="text"
          icon={<SearchOutlined />}
          size="small"
          onClick={() => {
            const searchInput = document.getElementById('node-search') as HTMLInputElement;
            if (searchInput) searchInput.focus();
          }}
        />
      }
    >
      {/* 搜索框 */}
      <div style={{ marginBottom: '12px' }}>
        <Input
          id="node-search"
          placeholder="搜索节点..."
          prefix={<SearchOutlined />}
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          allowClear
          size="small"
        />
      </div>

      <div className="component-library">
        {filteredNodeGroups.map(group => (
          <div key={group.id} className="node-group" style={{ marginBottom: '16px' }}>
            {/* 分组标题 */}
            <div
              className="group-header"
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                padding: '8px 12px',
                background: '#f5f5f5',
                borderRadius: '6px',
                cursor: 'pointer',
                marginBottom: '8px',
                fontSize: '13px',
                fontWeight: 500,
                color: '#333'
              }}
              onClick={() => toggleGroup(group.id)}
            >
              <div style={{ display: 'flex', alignItems: 'center' }}>
                {groupStates[group.id] ? <MinusOutlined /> : <PlusOutlined />}
                <span style={{ marginLeft: '8px' }}>{group.title}</span>
              </div>
              <Badge count={group.nodes.length} size="small" style={{ backgroundColor: '#1890ff' }} />
            </div>

            {/* 节点列表 - 完全按照图片样式 */}
            {groupStates[group.id] && (
              <div className="node-list">
                {group.nodes.map(node => {
                  const IconComponent = node.icon;
                  return (
                    <div
                      key={node.id}
                      className="workflow-node-item"
                      draggable
                      onDragStart={(e) => onDragStart(e, node.id, node)}
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        padding: '16px',
                        margin: '8px 0',
                        background: node.bgColor,
                        border: `3px solid ${node.borderColor}`,
                        borderRadius: '12px',
                        cursor: 'grab',
                        transition: 'all 0.2s ease',
                        minHeight: '70px',
                        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.transform = 'translateY(-2px)';
                        e.currentTarget.style.boxShadow = `0 4px 16px ${node.color}40`;
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.transform = 'translateY(0)';
                        e.currentTarget.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)';
                      }}
                    >
                      {/* 左侧图标 - 完全按照图片样式 */}
                      <div
                        style={{
                          width: '40px',
                          height: '40px',
                          borderRadius: '8px',
                          background: node.color,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          marginRight: '16px',
                          flexShrink: 0,
                          boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                        }}
                      >
                        <IconComponent style={{ color: 'white', fontSize: '20px' }} />
                      </div>

                      {/* 右侧文本 - 主标题和副标题 */}
                      <div style={{ flex: 1, minWidth: 0 }}>
                        <div
                          style={{
                            fontSize: '16px',
                            fontWeight: 600,
                            color: '#333',
                            lineHeight: '22px',
                            marginBottom: '4px'
                          }}
                        >
                          {node.title}
                        </div>
                        <div
                          style={{
                            fontSize: '13px',
                            color: '#666',
                            lineHeight: '18px'
                          }}
                        >
                          {node.subtitle}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        ))}

        {/* 搜索无结果提示 */}
        {searchTerm && filteredNodeGroups.length === 0 && (
          <div style={{
            textAlign: 'center',
            color: '#999',
            padding: '20px',
            fontSize: '14px'
          }}>
            <SearchOutlined style={{ fontSize: '24px', marginBottom: '8px' }} />
            <div>未找到匹配的节点</div>
            <div style={{ fontSize: '12px', marginTop: '4px' }}>
              尝试使用其他关键词搜索
            </div>
          </div>
        )}
      </div>
    </Card>
  );

  const renderOtherContent = (icon: React.ReactNode, title: string, description: string) => (
    <div style={{ 
      display: 'flex', 
      alignItems: 'center', 
      justifyContent: 'center', 
      height: '100%',
      flexDirection: 'column',
      gap: '16px'
    }}>
      {icon}
      <div style={{ fontSize: '18px', color: '#333' }}>{title}</div>
      <div style={{ fontSize: '14px', color: '#666' }}>{description}</div>
    </div>
  );

  return (
    <ReactFlowProvider>
      <PageLayout
        title="可视化设计器 - ReactFlow (********-临时版)"
        description="基于ReactFlow的工作流可视化设计器，完整复刻********-临时版功能"
        icon={<ApartmentOutlined />}
        actions={
          <Space>
            <Button icon={<SaveOutlined />} type="primary" onClick={handleSave}>保存</Button>
            <Button icon={<PlayCircleOutlined />} onClick={handleRun}>执行</Button>
            <Button icon={<SettingOutlined />}>设置</Button>
          </Space>
        }
      >
      {/* 🟡 导航菜单区域 */}
      <div 
        className="workflow-navigation"
        style={{
          marginBottom: '16px',
          background: '#fff',
          borderRadius: '6px',
          border: '1px solid #d9d9d9',
          overflow: 'hidden'
        }}
      >
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          type="card"
          size="small"
          style={{ margin: 0 }}
          items={navigationTabs.map(tab => ({
            key: tab.key,
            label: (
              <span style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
                {tab.icon}
                {tab.label}
              </span>
            ),
          }))}
        />
      </div>

      {/* 🟠 工具栏区域 - 完整的********-临时版工具栏 */}
      {activeTab === 'design' && (
        <div
          className="workflow-toolbar"
          style={{
            marginBottom: '16px',
            background: '#fff',
            borderRadius: '6px',
            border: '2px solid #fa8c16', // 橙色调试边框
            padding: '12px 16px'
          }}
        >
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            {/* 左侧操作按钮 */}
            <Space>
              <Button size="small" icon={<SaveOutlined />} onClick={handleSave}>保存</Button>
              <Button size="small" icon={<FileOutlined />}>另存为</Button>
              <Button size="small" icon={<UndoOutlined />} onClick={handleUndo}>撤销</Button>
              <Button size="small" icon={<RedoOutlined />} onClick={handleRedo}>重做</Button>
              <Button size="small" icon={<CopyOutlined />} onClick={handleCopy}>复制</Button>
              <Button size="small" icon={<ScissorOutlined />} onClick={handleCut}>剪切</Button>
              <Button size="small" icon={<DeleteOutlined />} onClick={handleDelete} danger>删除</Button>
            </Space>

            {/* 中间状态信息 */}
            <Space>
              <Badge count={nodes.length} style={{ backgroundColor: '#52c41a' }}>
                <span style={{ marginRight: '8px' }}>节点</span>
              </Badge>
              <Badge count={edges.length} style={{ backgroundColor: '#1890ff' }}>
                <span style={{ marginRight: '8px' }}>连接</span>
              </Badge>
              {selectedNode && (
                <Badge status="processing" text={`已选中: ${selectedNode.data.label}`} />
              )}
            </Space>

            {/* 右侧操作按钮 */}
            <Space>
              <Button size="small" icon={<ZoomOutOutlined />}>缩小</Button>
              <Button size="small" icon={<ZoomInOutlined />}>放大</Button>
              <Button size="small" icon={<CompressOutlined />}>适应</Button>
              <Button
                size="small"
                icon={<MenuOutlined />}
                onClick={toggleStencil}
                type={stencilVisible ? "default" : "primary"}
                title={stencilVisible ? "隐藏组件库" : "显示组件库"}
              />
              <Button
                size="small"
                icon={minimapVisible ? <EyeOutlined /> : <EyeInvisibleOutlined />}
                onClick={toggleMinimap}
                type={minimapVisible ? "default" : "primary"}
                title={minimapVisible ? "隐藏小地图" : "显示小地图"}
              />
              <Button size="small" type="primary" icon={<PlayCircleOutlined />} onClick={handleRun}>运行</Button>
              <Button size="small" icon={<BugOutlined />}>调试</Button>
              <Button size="small" icon={<SettingOutlined />}>设置</Button>
            </Space>
          </div>
        </div>
      )}

      {/* 🔵 主要内容区域 */}
      <div 
        className="workflow-designer-reactflow-container"
        style={{
          height: activeTab === 'design' ? 'calc(100vh - 340px)' : 'calc(100vh - 280px)',
          border: '2px solid #2196f3',
          borderRadius: '6px',
          padding: 'var(--layout-element-spacing)',
          background: '#fff',
          overflow: 'hidden'
        }}
      >
        {/* 根据activeTab渲染不同内容 */}
        {activeTab === 'design' && (
          <div className="flex gap-4 h-full">
            {/* 左侧组件库 - 完全按照********-临时版样式 */}
            {stencilVisible && renderNodeLibrary()}

            {/* 中间ReactFlow画布区域 */}
            <div className="flex-1" style={{ height: '100%' }}>
              <Card
                title={
                  <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <span>工作流画布</span>
                    <Space>
                      <Badge count={nodes.length} style={{ backgroundColor: '#52c41a' }}>
                        <span style={{ fontSize: '12px' }}>节点</span>
                      </Badge>
                      <Badge count={edges.length} style={{ backgroundColor: '#1890ff' }}>
                        <span style={{ fontSize: '12px' }}>连接</span>
                      </Badge>
                    </Space>
                  </div>
                }
                styles={{ body: { padding: 0, height: 'calc(100% - 57px)' } }}
                className="h-full"
              >
                <ReactFlow
                  nodes={nodes}
                  edges={edges}
                  onNodesChange={onNodesChange}
                  onEdgesChange={onEdgesChange}
                  onConnect={onConnect}
                  onDrop={onDrop}
                  onDragOver={onDragOver}
                  onNodeClick={onNodeClick}
                  connectionMode={ConnectionMode.Loose}
                  fitView
                  style={{
                    background: '#f5f5f5',
                    borderRadius: '0 0 6px 6px'
                  }}
                  defaultViewport={{ x: 0, y: 0, zoom: 1 }}
                  minZoom={0.1}
                  maxZoom={2}
                  snapToGrid={true}
                  snapGrid={[15, 15]}
                >
                  <Background
                    gap={15}
                    size={1}
                    color="#ccc"
                  />
                  <Controls
                    position="bottom-left"
                    showZoom={true}
                    showFitView={true}
                    showInteractive={true}
                  />
                  {minimapVisible && (
                    <MiniMap
                      position="bottom-right"
                      nodeColor="#1890ff"
                      nodeStrokeWidth={2}
                      pannable={true}
                      zoomable={true}
                      style={{
                        background: '#fafafa',
                        border: '1px solid #d9d9d9',
                        borderRadius: '6px'
                      }}
                    />
                  )}
                </ReactFlow>
              </Card>
            </div>

            {/* 右侧属性面板和小地图 */}
            <div className="w-80 flex-shrink-0 space-y-4">
              {/* 属性面板 - 使用********-临时版的PropertyPanel组件 */}
              {selectedNode ? (
                <PropertyPanel
                  selectedNode={selectedNode}
                  onNodeUpdate={onNodeUpdate}
                  onNodeDelete={onNodeDelete}
                />
              ) : (
                <Card title="属性面板" styles={{ body: { padding: '16px' } }}>
                  <div style={{
                    textAlign: 'center',
                    color: '#999',
                    padding: '40px 20px',
                    fontSize: '14px'
                  }}>
                    <InfoCircleOutlined style={{ fontSize: '32px', marginBottom: '12px', display: 'block' }} />
                    <div>请选择一个节点</div>
                    <div style={{ fontSize: '12px', marginTop: '8px' }}>
                      点击画布中的节点来查看和编辑其属性
                    </div>
                  </div>
                </Card>
              )}

              {/* 快捷键说明 */}
              <Card title="快捷键" size="small" styles={{ body: { padding: '12px' } }}>
                <div className="text-xs text-gray-600 space-y-1">
                  <div><kbd>Ctrl+S</kbd>: 保存工作流</div>
                  <div><kbd>Ctrl+C</kbd>: 复制节点</div>
                  <div><kbd>Ctrl+V</kbd>: 粘贴节点</div>
                  <div><kbd>Ctrl+Z</kbd>: 撤销操作</div>
                  <div><kbd>Ctrl+Y</kbd>: 重做操作</div>
                  <div><kbd>Delete</kbd>: 删除选中节点</div>
                  <div><kbd>Space+拖动</kbd>: 平移画布</div>
                  <div><kbd>滚轮</kbd>: 缩放画布</div>
                </div>
              </Card>

              {/* 工作流统计 */}
              <Card title="工作流统计" size="small" styles={{ body: { padding: '12px' } }}>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>节点总数:</span>
                    <Badge count={nodes.length} style={{ backgroundColor: '#52c41a' }} />
                  </div>
                  <div className="flex justify-between">
                    <span>连接总数:</span>
                    <Badge count={edges.length} style={{ backgroundColor: '#1890ff' }} />
                  </div>
                  <div className="flex justify-between">
                    <span>当前选中:</span>
                    <span style={{ color: selectedNode ? '#1890ff' : '#999' }}>
                      {selectedNode ? selectedNode.data.label : '无'}
                    </span>
                  </div>
                </div>
              </Card>
            </div>
          </div>
        )}

        {/* 其他标签页内容 */}
        {activeTab === 'preview' && renderOtherContent(
          <EyeOutlined style={{ fontSize: '48px', color: '#1890ff' }} />,
          '工作流预览',
          '在这里可以预览工作流的执行流程'
        )}
        {activeTab === 'debug' && renderOtherContent(
          <BugOutlined style={{ fontSize: '48px', color: '#fa8c16' }} />,
          '调试模式',
          '在这里可以调试工作流的执行过程'
        )}
        {activeTab === 'history' && renderOtherContent(
          <HistoryOutlined style={{ fontSize: '48px', color: '#52c41a' }} />,
          '历史记录',
          '查看工作流的修改历史和版本记录'
        )}
        {activeTab === 'share' && renderOtherContent(
          <ShareAltOutlined style={{ fontSize: '48px', color: '#722ed1' }} />,
          '分享工作流',
          '生成分享链接或导出工作流配置'
        )}
      </div>
    </PageLayout>
    </ReactFlowProvider>
  );
};

export default WorkflowDesignerReactFlow;
