using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace FlowCustomV1.Core.Models.Designer;

/// <summary>
/// 协作者信息
/// </summary>
public class CollaboratorInfo
{
    /// <summary>
    /// 协作者唯一标识符
    /// </summary>
    [Required]
    [JsonPropertyName("collaboratorId")]
    public string CollaboratorId { get; set; } = string.Empty;

    /// <summary>
    /// 协作者名称
    /// </summary>
    [Required]
    [JsonPropertyName("name")]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 协作者邮箱
    /// </summary>
    [JsonPropertyName("email")]
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// 协作者头像URL
    /// </summary>
    [JsonPropertyName("avatarUrl")]
    public string AvatarUrl { get; set; } = string.Empty;

    /// <summary>
    /// 加入协作时间
    /// </summary>
    [JsonPropertyName("joinedAt")]
    public DateTime JoinedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 最后活跃时间
    /// </summary>
    [JsonPropertyName("lastActiveAt")]
    public DateTime LastActiveAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 协作者角色
    /// </summary>
    [JsonPropertyName("role")]
    public CollaboratorRole Role { get; set; } = CollaboratorRole.Editor;

    /// <summary>
    /// 当前选中的对象ID
    /// </summary>
    [JsonPropertyName("currentSelection")]
    public string CurrentSelection { get; set; } = string.Empty;

    /// <summary>
    /// 当前光标位置
    /// </summary>
    [JsonPropertyName("cursorPosition")]
    public CursorPosition? CursorPosition { get; set; }

    /// <summary>
    /// 协作者状态
    /// </summary>
    [JsonPropertyName("status")]
    public CollaboratorStatus Status { get; set; } = CollaboratorStatus.Online;

    /// <summary>
    /// 权限列表
    /// </summary>
    [JsonPropertyName("permissions")]
    public HashSet<string> Permissions { get; set; } = new();

    /// <summary>
    /// 协作者颜色（用于UI标识）
    /// </summary>
    [JsonPropertyName("color")]
    public string Color { get; set; } = "#007bff";
}

/// <summary>
/// 协作者角色枚举
/// </summary>
public enum CollaboratorRole
{
    /// <summary>
    /// 观察者（只读）
    /// </summary>
    Viewer,

    /// <summary>
    /// 编辑者
    /// </summary>
    Editor,

    /// <summary>
    /// 管理员
    /// </summary>
    Admin,

    /// <summary>
    /// 所有者
    /// </summary>
    Owner
}

/// <summary>
/// 协作者状态枚举
/// </summary>
public enum CollaboratorStatus
{
    /// <summary>
    /// 在线
    /// </summary>
    Online,

    /// <summary>
    /// 离线
    /// </summary>
    Offline,

    /// <summary>
    /// 忙碌
    /// </summary>
    Busy,

    /// <summary>
    /// 离开
    /// </summary>
    Away
}

/// <summary>
/// 光标位置信息
/// </summary>
public class CursorPosition
{
    /// <summary>
    /// X坐标
    /// </summary>
    [JsonPropertyName("x")]
    public double X { get; set; }

    /// <summary>
    /// Y坐标
    /// </summary>
    [JsonPropertyName("y")]
    public double Y { get; set; }

    /// <summary>
    /// 时间戳
    /// </summary>
    [JsonPropertyName("timestamp")]
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 设计操作
/// </summary>
public class DesignOperation
{
    /// <summary>
    /// 操作唯一标识符
    /// </summary>
    [JsonPropertyName("operationId")]
    public string OperationId { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 工作流ID
    /// </summary>
    [Required]
    [JsonPropertyName("workflowId")]
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 协作者ID
    /// </summary>
    [Required]
    [JsonPropertyName("collaboratorId")]
    public string CollaboratorId { get; set; } = string.Empty;

    /// <summary>
    /// 操作类型
    /// </summary>
    [JsonPropertyName("type")]
    public DesignOperationType Type { get; set; }

    /// <summary>
    /// 操作目标类型
    /// </summary>
    [JsonPropertyName("targetType")]
    public string TargetType { get; set; } = string.Empty;

    /// <summary>
    /// 操作目标ID
    /// </summary>
    [JsonPropertyName("targetId")]
    public string TargetId { get; set; } = string.Empty;

    /// <summary>
    /// 操作数据
    /// </summary>
    [JsonPropertyName("data")]
    public Dictionary<string, object> Data { get; set; } = new();

    /// <summary>
    /// 操作前的状态（用于撤销）
    /// </summary>
    [JsonPropertyName("previousState")]
    public Dictionary<string, object>? PreviousState { get; set; }

    /// <summary>
    /// 操作时间戳
    /// </summary>
    [JsonPropertyName("timestamp")]
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 操作序列号（用于排序）
    /// </summary>
    [JsonPropertyName("sequenceNumber")]
    public long SequenceNumber { get; set; }

    /// <summary>
    /// 是否需要同步到其他协作者
    /// </summary>
    [JsonPropertyName("requiresSync")]
    public bool RequiresSync { get; set; } = true;
}

/// <summary>
/// 设计操作类型枚举
/// </summary>
public enum DesignOperationType
{
    /// <summary>
    /// 添加节点
    /// </summary>
    AddNode,

    /// <summary>
    /// 删除节点
    /// </summary>
    DeleteNode,

    /// <summary>
    /// 移动节点
    /// </summary>
    MoveNode,

    /// <summary>
    /// 更新节点
    /// </summary>
    UpdateNode,

    /// <summary>
    /// 添加连接
    /// </summary>
    AddConnection,

    /// <summary>
    /// 删除连接
    /// </summary>
    DeleteConnection,

    /// <summary>
    /// 更新连接
    /// </summary>
    UpdateConnection,

    /// <summary>
    /// 选择对象
    /// </summary>
    SelectObject,

    /// <summary>
    /// 取消选择
    /// </summary>
    DeselectObject,

    /// <summary>
    /// 移动光标
    /// </summary>
    MoveCursor,

    /// <summary>
    /// 开始编辑
    /// </summary>
    StartEdit,

    /// <summary>
    /// 结束编辑
    /// </summary>
    EndEdit
}

/// <summary>
/// 冲突解决方案
/// </summary>
public class ConflictResolution
{
    /// <summary>
    /// 冲突ID
    /// </summary>
    [Required]
    [JsonPropertyName("conflictId")]
    public string ConflictId { get; set; } = string.Empty;

    /// <summary>
    /// 工作流ID
    /// </summary>
    [Required]
    [JsonPropertyName("workflowId")]
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 解决策略
    /// </summary>
    [JsonPropertyName("strategy")]
    public ConflictResolutionStrategy Strategy { get; set; }

    /// <summary>
    /// 选择的版本（当策略为选择版本时）
    /// </summary>
    [JsonPropertyName("selectedVersion")]
    public string? SelectedVersion { get; set; }

    /// <summary>
    /// 合并后的数据（当策略为手动合并时）
    /// </summary>
    [JsonPropertyName("mergedData")]
    public Dictionary<string, object>? MergedData { get; set; }

    /// <summary>
    /// 解决者ID
    /// </summary>
    [JsonPropertyName("resolvedBy")]
    public string ResolvedBy { get; set; } = string.Empty;

    /// <summary>
    /// 解决时间
    /// </summary>
    [JsonPropertyName("resolvedAt")]
    public DateTime ResolvedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 解决说明
    /// </summary>
    [JsonPropertyName("description")]
    public string Description { get; set; } = string.Empty;
}

/// <summary>
/// 冲突解决策略枚举
/// </summary>
public enum ConflictResolutionStrategy
{
    /// <summary>
    /// 接受当前版本
    /// </summary>
    AcceptCurrent,

    /// <summary>
    /// 接受传入版本
    /// </summary>
    AcceptIncoming,

    /// <summary>
    /// 手动合并
    /// </summary>
    ManualMerge,

    /// <summary>
    /// 创建新分支
    /// </summary>
    CreateBranch
}

/// <summary>
/// 协作动作枚举
/// </summary>
public enum CollaborationAction
{
    /// <summary>
    /// 加入协作
    /// </summary>
    Join,

    /// <summary>
    /// 离开协作
    /// </summary>
    Leave,

    /// <summary>
    /// 设计变更
    /// </summary>
    DesignChange,

    /// <summary>
    /// 状态更新
    /// </summary>
    StatusUpdate,

    /// <summary>
    /// 权限变更
    /// </summary>
    PermissionChange
}
