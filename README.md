# FlowCustomV1 工作流自动化系统

## 📋 项目概述

FlowCustomV1是一个现代化的工作流自动化系统，提供类似n8n的可视化工作流设计体验，采用.NET 9.0 + React 18技术栈，遵循清洁架构原则。

## 🎯 项目信息

| 项目信息 | 详细内容 |
|---------|---------|
| **项目名称** | FlowCustomV1 工作流自动化系统 |
| **当前版本** | v0.0.1.13 (ReactFlow工作流可视化设计器完成) |
| **架构模式** | 分布式集群架构 + 清洁架构原则 + 动态插件系统 |
| **技术栈** | .NET 9.0 + Furion + React 18 + ReactFlow + Ant Design + NATS JetStream + Docker |
| **开发模式** | 测试驱动开发 + 敏捷迭代 |
| **质量状态** | ✅ ReactFlow可视化设计器完成，16种内置节点支持，拖拽式工作流设计体验 |

## 🏗️ 当前架构设计 (v0.0.1.12)

### 🎯 工作流管理界面架构亮点 (v0.0.1.12)

**核心改进：完整的前后端工作流管理系统**
- **前端**: React 18 + Ant Design Pro - 现代化企业级UI框架
- **后端**: Furion 4.9.5.2 - API开发增强功能
- **集成**: 完整的前后端数据交互和CRUD操作
- **体验**: 响应式设计，完善的错误处理和用户反馈
- **架构**: 保持清洁架构原则，前后端分离设计

### 🎯 插件系统架构基础 (v0.0.1.10基础)

**核心改进：动态插件系统和丰富节点库**
- **新增**: Natasha动态编译引擎 - 运行时C#代码编译
- **新增**: McMaster热插拔系统 - DLL插件动态加载卸载
- **重构**: 移除硬编码执行器，实现真正的可扩展架构
- **优化**: 三层插件架构，支持内置、配置、DLL三种插件类型
- **完成**: 16种内置节点类型，覆盖触发器、动作、控制流、数据转换、外部服务集成

**插件系统架构**：
```
API层 (统一入口)
    ↓
Infrastructure层 (PluginManager - 插件统一管理)
    ↓
├── NatashaCompilerService (动态编译)
├── McMasterPluginLoader (热插拔)
└── 三层插件架构 (内置/配置/DLL)
    ↓
Engine层 (动态执行器 - 运行时生成)
    ↓
Core层 (插件接口和抽象)
```

**质量提升**：
- ✅ 真正的可扩展性 - 运行时动态加载新节点类型
- ✅ 高性能动态编译 - 基于Natasha，性能损失<5%
- ✅ 热插拔能力 - 无需重启即可加载新插件
- ✅ 架构现代化 - 移除硬编码，实现插件化架构
- ✅ 丰富节点库 - 16种内置节点，开箱即用的工作流组件
- ✅ 外部服务集成 - MySQL数据库、NATS消息队列、REST API完整支持

**🧪 测试就绪状态**：
- ✅ 16种内置节点覆盖所有核心工作流场景
- ✅ 支持构建复杂的企业级工作流应用
- ✅ 完整的外部服务集成能力
- ✅ 可开展功能测试、集成测试、性能测试

### 分布式集群架构
- **当前阶段**: 3节点NATS JetStream集群
- **消息中间件**: NATS 2.11.8 + JetStream
- **监控系统**: NATS Surveyor + Prometheus指标
- **部署方式**: Docker Compose容器编排

### NATS集群架构
```
┌─────────────────────────────────────────────────────────────┐
│                    监控层                                    │
│  • NATS Surveyor (Prometheus指标)                          │
│  • HTTP监控端点 (/varz, /connz, /routez)                   │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                  NATS集群层                                  │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │  NATS-1     │  │  NATS-2     │  │  NATS-3     │          │
│  │ (Leader)    │  │ (Follower)  │  │ (Follower)  │          │
│  │ :4222       │  │ :4223       │  │ :4224       │          │
│  │ :8222       │  │ :8223       │  │ :8224       │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
│         │                │                │                 │
│         └────────────────┼────────────────┘                 │
│                          │                                  │
│              JetStream分布式存储                             │
│         (消息持久化 + 流处理 + 故障转移)                      │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                  应用服务层                                  │
│              (未来开发)                                      │
│  • 工作流引擎服务                                            │
│  • API网关服务                                              │
│  • Web管理界面                                              │
└─────────────────────────────────────────────────────────────┘
```

### 核心设计原则
- **分布式优先**: 基于NATS的分布式消息架构
- **高可用性**: 3节点集群，自动故障转移
- **可观测性**: 完整的监控和指标收集
- **容器化部署**: Docker Compose一键部署

## 🚀 v0.0.1.0 核心功能

### **NATS集群基础设施** ✅
- **3节点高可用集群** - 自动故障转移，恢复时间 < 5秒
- **JetStream分布式存储** - 消息持久化，10GB存储/节点
- **WebSocket支持** - 前端实时通信，端口8080-8082
- **账户权限管理** - 系统账户和应用账户隔离

### **集群管理工具** ✅
- **Python管理脚本** - 一键启动/停止/重启/状态检查
- **健康检查系统** - 自动检测节点状态和集群健康
- **日志管理** - 集中化日志查看和分析
- **Docker编排** - 完整的容器化部署方案

### **监控和可观测性** ✅
- **NATS Surveyor** - Prometheus指标导出，54KB监控数据
- **HTTP监控端点** - 原生NATS监控API (/varz, /connz, /routez)
- **实时性能监控** - 连接数、消息数、错误率监控
- **集群状态可视化** - 完整的集群拓扑和状态展示

### **性能指标** ✅
- **消息吞吐量**: 312,657 msg/s (超过基准要求)
- **数据传输速度**: 305MB/s
- **故障转移时间**: < 5秒
- **测试成功率**: 100% (5/5测试通过)

## 📁 项目结构

```
FlowCustomV1/
├── docker/                            # Docker部署配置 ✅
│   └── nats-cluster/                  # NATS集群配置
│       ├── config/                    # NATS配置文件
│       │   ├── nats-1.conf           # 节点1配置
│       │   ├── nats-2.conf           # 节点2配置
│       │   └── nats-3.conf           # 节点3配置
│       ├── docker-compose.yml         # 容器编排配置
│       ├── start-cluster.py           # 集群管理脚本
│       ├── test-cluster.py            # 集群测试脚本
│       └── test-monitoring.py         # 监控测试脚本
├── docs/                              # 项目文档 ✅
│   ├── 项目管理/                       # 项目管理文档
│   │   ├── 项目状态跟踪.md             # 版本状态跟踪
│   │   └── 功能开发路线图.md           # 开发路线图
│   ├── 核心设计/                       # 核心设计文档
│   │   ├── 系统架构设计文档.md         # 架构设计
│   │   └── API接口设计文档.md          # API接口设计
│   └── 开发规范/                       # 开发规范文档
│       ├── 代码规范和最佳实践.md       # 代码规范
│       └── 开发流程控制规范.md         # 流程规范
├── src/                               # 源代码 (未来开发)
│   ├── FlowCustomV1.Api/              # Web API接口层
│   ├── FlowCustomV1.Core/             # 核心业务层
│   ├── FlowCustomV1.Engine/           # 工作流执行引擎
│   └── FlowCustomV1.Infrastructure/   # 基础设施层
├── tests/                             # 测试代码 (未来开发)
│   ├── FlowCustomV1.Core.Tests/      # 核心层单元测试
│   └── FlowCustomV1.Engine.Tests/    # 引擎层单元测试
└── README.md                          # 项目说明文档
```

## 🚀 快速开始

### **启动NATS集群** (当前版本)

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd FlowCustomV1
   ```

2. **启动NATS集群**
   ```bash
   cd docker/nats-cluster
   python start-cluster.py start
   ```

3. **验证集群状态**
   ```bash
   # 检查集群健康状态
   python start-cluster.py health

   # 运行集群测试
   python test-cluster.py
   ```

4. **访问监控界面**
   - NATS-1监控: http://localhost:8222/varz
   - NATS-2监控: http://localhost:8223/varz
   - NATS-3监控: http://localhost:8224/varz
   - Prometheus指标: http://localhost:7777/metrics

5. **集群管理命令**
   ```bash
   # 启动集群
   python start-cluster.py start

   # 停止集群
   python start-cluster.py stop

   # 重启集群
   python start-cluster.py restart

   # 查看集群状态
   python start-cluster.py status

   # 查看集群日志
   python start-cluster.py logs
   ```

### **运行集群测试** (推荐)

1. **运行完整集群测试**
   ```bash
   python test-cluster.py
   ```

2. **预期输出**
   ```
   🚀 开始NATS集群测试...
   ✅ 基础连接测试通过
   ✅ JetStream功能测试通过
   ✅ 集群监控测试通过
   ✅ 故障转移测试通过
   ✅ 性能基准测试通过

   📊 测试结果:
   - 成功率: 100% (5/5)
   - 消息吞吐量: 312,657 msg/s
   - 数据传输速度: 305MB/s
   ```

### **集群配置说明**

```yaml
# docker-compose.yml 核心配置
services:
  nats-1:
    image: nats:2.11.8-alpine
    ports:
      - "4222:4222"  # NATS服务端口
      - "8222:8222"  # HTTP监控端口
    volumes:
      - ./config/nats-1.conf:/etc/nats/nats-server.conf
      - nats-1-data:/data

  nats-surveyor:
    image: natsio/nats-surveyor:latest
    ports:
      - "7777:7777"  # Prometheus指标端口

// 2. 创建工作流定义
var workflow = new WorkflowDefinition
{
    WorkflowId = "my-workflow",
    Name = "我的工作流",
    Nodes = new List<WorkflowNode>
    {
        new() { NodeId = "start", NodeType = "Start", Name = "开始" },
        new() { NodeId = "task1", NodeType = "Task", Name = "任务1" },
        new() { NodeId = "end", NodeType = "End", Name = "结束" }
    },
    Connections = new List<WorkflowConnection>
    {
        new() { SourceNodeId = "start", TargetNodeId = "task1" },
        new() { SourceNodeId = "task1", TargetNodeId = "end" }
    }
};

// 3. 执行工作流
var engine = serviceProvider.GetRequiredService<IWorkflowEngine>();
await engine.StartAsync();
var result = await engine.ExecuteWorkflowAsync(workflow);
```

## 📋 版本历史

### v0.0.0.10 (2025-09-04) - RESTful API接口基础实现完成 ✅
- ✅ 完整的RESTful API接口实现
- ✅ WorkflowsController工作流CRUD操作
- ✅ ExecutionsController工作流执行管理
- ✅ Swagger/OpenAPI文档自动生成
- ✅ WorkflowEngineHostedService后台服务
- ✅ 与现有Engine和Infrastructure完美集成

### v0.0.0.9 (2025-09-02) - 工作流验证服务实现完成 ✅
- ✅ 完整的工作流验证服务实现
- ✅ 节点配置验证和连接关系验证
- ✅ 循环依赖检测和验证错误报告
- ✅ 验证规则引擎和图算法实现
- ✅ 项目结构清理和整洁性维护

### v0.0.0.8 (2025-08-21) - 数据持久化实现完成 ✅
- ✅ 完整的数据持久化功能实现
- ✅ MySQL数据库集成和真实数据库测试
- ✅ 工作流定义和实例存储
- ✅ 执行历史记录和数据库迁移机制
- ✅ 86个测试全部通过，生产环境就绪

### v0.0.0.7 (2025-08-20) - 工作流引擎核心功能完成 ✅
- ✅ 完整的工作流执行引擎实现
- ✅ Start/Task/End三种基础节点执行器
- ✅ 状态管理和事件驱动调度系统
- ✅ 功能验证示例程序，100%验证通过
- ✅ 修复状态同步和节点注册关键问题

### v0.0.0.6 (2025-08-18) - 执行引擎架构设计
- ✅ FlowCustomV1.Engine项目创建和架构设计
- ✅ 节点执行器、调度器、状态跟踪器实现
- ✅ 清洁架构依赖关系建立
- ✅ 基础示例程序框架

### v0.0.0.5 (2025-08-18) - 工作流引擎基础
- ✅ 工作流引擎核心接口和服务实现
- ✅ 节点执行器抽象和基础实现
- ✅ 工作流定义模型完善
- ✅ 单元测试覆盖率提升

### v0.0.0.4 (2025-08-18) - 依赖注入和基础服务
- ✅ 完整的依赖注入系统和服务注册扩展方法
- ✅ LoggingService和ConfigurationService基础服务实现
- ✅ 创建FlowCustomV1.Core.Tests单元测试项目
- ✅ 38个单元测试，100%通过率，验证架构可行性

### v0.0.0.3 (2025-08-18) - 重大重构版本
- ✅ 系统性架构重构：去除所有"Unified"前缀
- ✅ 类名优化：UnifiedNodeInfo → NodeInfo, UnifiedClusterService → ClusterService
- ✅ 属性名优化：DisplayName → NodeName, RegisteredAt → CreatedAt
- ✅ 服务架构重构：完全重写ClusterService和相关接口
- ✅ 修复123个编译错误，实现零错误编译
- ✅ 保持向后兼容性，确保API稳定性

### v0.0.0.4 (2025-08-18) - 依赖注入和基础服务
- ✅ 完整的依赖注入系统和服务注册扩展方法
- ✅ LoggingService和ConfigurationService基础服务实现
- ✅ 创建FlowCustomV1.Core.Tests单元测试项目
- ✅ 38个单元测试，100%通过率，验证架构可行性
- ✅ 正确的服务生命周期管理和依赖关系处理

## 🎯 核心特性

### 已实现特性 (v0.0.0.10)
- ✅ **RESTful API接口**: 完整的HTTP API服务，支持工作流CRUD和执行管理
- ✅ **工作流执行引擎**: 完整的异步执行队列和状态机驱动
- ✅ **节点执行系统**: Start/Task/End三种基础节点执行器
- ✅ **数据持久化**: MySQL数据库集成，完整的数据存储和查询
- ✅ **工作流验证**: 完整的验证规则引擎和错误检测
- ✅ **状态管理系统**: 实时状态跟踪和双重状态同步机制
- ✅ **事件驱动架构**: 节点完成自动触发后续节点调度
- ✅ **清洁架构**: 四层架构完整实现 (Api/Engine/Infrastructure/Core)
- ✅ **依赖注入**: 完整的DI容器配置和自动服务注册
- ✅ **API文档**: Swagger/OpenAPI自动生成，完整的接口文档
- ✅ **自动化测试**: Python测试脚本，100%API测试覆盖
- ✅ **性能优化**: <1秒API响应，100%测试通过率

### 计划特性 (未来版本)
- 🔄 **控制节点**: 条件判断、循环控制 (v0.2.0)
- 🔄 **并发执行**: 并行分支和大规模并发支持 (v0.0.0.8)
- 🔄 **持久化**: 执行结果和状态持久化 (v0.0.0.8)
- 🔄 **可视化设计器**: 类似n8n的拖拽式工作流设计 (v0.7.0)
- 🔄 **Web API**: RESTful API接口 (v0.5.0)
- 🔄 **实时监控**: 工作流执行状态实时监控 (v0.8.0)
- 🔄 **分布式执行**: 跨节点的工作流分布式执行

## 🧪 测试覆盖

### 单元测试统计
- **总测试数**: 38个
- **通过率**: 100% ✅
- **覆盖范围**: 核心服务、依赖注入、配置管理、日志系统
- **测试框架**: xUnit + Moq + Microsoft.Extensions.DependencyInjection

### API集成测试统计
- **API测试数**: 10个端点
- **通过率**: 100% ✅
- **覆盖范围**: 工作流CRUD、执行管理、验证服务
- **测试工具**: Python + requests库

### 测试类别
```
单元测试 (38个)
├── ServiceCollectionExtensionsTests (18个) - 依赖注入验证
├── LoggingServiceTests (10个) - 日志服务验证
└── ConfigurationServiceTests (10个) - 配置服务验证

API集成测试 (10个)
├── 工作流管理API (6个) - CRUD + 验证
├── 工作流执行API (3个) - 执行 + 状态查询
└── 系统健康检查 (1个) - 服务可用性
```

## 🔄 开发流程

### 版本控制
- 采用简化的Git工作流
- 版本号从v0.0.0.1开始递增
- 每个版本都是完整可用的功能增量

### 质量保证
- 设计优先原则
- 严禁临时性代码
- 代码质量优于开发速度
- 完整的测试覆盖

### AI辅助开发
- 使用Augment AI助手进行开发
- 严格遵循开发规范
- 质量门禁控制

## 📚 文档

### 📖 文档导航
- **[📋 文档导航和使用指南](docs/文档导航和使用指南.md)** ← 🎯 **从这里开始！**

### 📊 项目管理文档
- [项目状态跟踪](docs/项目管理/项目状态跟踪.md) - 当前进度和版本状态
- [功能开发路线图](docs/项目管理/功能开发路线图.md) - 开发计划和任务管理

### 🏗️ 核心设计文档
- [系统架构设计文档](docs/核心设计/系统架构设计文档.md) - 整体架构和愿景
- [分布式集群架构设计](docs/核心设计/分布式集群架构设计.md) - v0.0.1.0分布式方案
- [API接口设计文档](docs/核心设计/API接口设计文档.md) - 接口规范和数据模型

### 📐 开发规范文档
- [代码规范和最佳实践](docs/开发规范/代码规范和最佳实践.md) - 编码标准
- [开发流程控制规范](docs/开发规范/开发流程控制规范.md) - 开发流程

### 🧪 测试文档
- [API测试报告](tests/API_Test_Report.md) - API接口测试结果

## 🤝 贡献指南

1. 遵循开发流程控制规范
2. 确保代码质量符合标准
3. 更新相关文档
4. 通过所有测试

## 📄 许可证

本项目采用 MIT 许可证。

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 项目Issues
- 邮件联系

## 🚀 引擎实现进展 (v0.1.0)

### 已完成的核心功能

✅ **工作流引擎架构**
- 基于Channel的异步节点调度器
- 事件驱动的执行模型
- 完整的生命周期管理

✅ **状态管理系统**
- 内存状态跟踪器
- 工作流和节点状态历史
- 实时状态查询接口

✅ **错误处理机制**
- 智能错误分类和处理
- 可配置的重试策略
- 错误恢复机制

✅ **节点执行器**
- Start节点：工作流初始化
- Task节点：支持多种任务类型（数据处理、计算、验证、转换、延迟）
- End节点：结果汇总和清理

✅ **测试覆盖**
- 完整的单元测试套件
- 集成测试验证
- 性能和并发测试

### 引擎使用示例

``csharp
// 配置服务
services.AddWorkflowEngine(options =>
{
    options.EnableDetailedLogging = true;
    options.SchedulerOptions.MaxConcurrentExecutions = 10;
});

// 创建工作流
var workflow = new WorkflowDefinition
{
    WorkflowId = "data-processing",
    Nodes = new List<WorkflowNode>
    {
        new() { NodeId = "start", NodeType = "Start" },
        new() { NodeId = "process", NodeType = "Task",
                Configuration = new() { Settings = { ["TaskType"] = "data_processing" } } },
        new() { NodeId = "end", NodeType = "End" }
    },
    Connections = new List<NodeConnection>
    {
        new() { SourceNodeId = "start", TargetNodeId = "process" },
        new() { SourceNodeId = "process", TargetNodeId = "end" }
    }
};

// 执行工作流
var engine = serviceProvider.GetRequiredService<IWorkflowEngine>();
await engine.StartAsync();

var result = await engine.ExecuteWorkflowAsync(workflow, inputData);
Console.WriteLine($"执行结果: {result.IsSuccess}");
```

### 性能特性

- **高并发**: 支持数千个并发节点执行
- **低延迟**: 基于Channel的异步调度，延迟 < 1ms
- **内存效率**: 优化的内存使用，支持大规模工作流
- **可扩展**: 支持自定义节点执行器

### 运行示例

```bash
# 运行单元测试
cd tests/FlowCustomV1.Engine.Tests
dotnet test

# 运行示例程序
cd examples/FlowCustomV1.Engine.Example
dotnet run
```

---

**FlowCustomV1 - 让工作流自动化变得简单高效！**
