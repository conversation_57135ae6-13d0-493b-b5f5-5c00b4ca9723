using System.Collections.Concurrent;
using System.Text.RegularExpressions;
using FlowCustomV1.Core.Interfaces.Messaging;
using FlowCustomV1.Core.Models.Messaging;
using Microsoft.Extensions.Logging;

namespace FlowCustomV1.Infrastructure.Services.Messaging;

/// <summary>
/// NATS消息路由服务实现
/// </summary>
public class NatsMessageRouter : INatsMessageRouter
{
    private readonly INatsService _natsService;
    private readonly IMessageTopicService _topicService;
    private readonly ILogger<NatsMessageRouter> _logger;
    private readonly ConcurrentDictionary<string, RouteRule> _routes;
    private readonly ConcurrentDictionary<string, NodeInfo> _nodes;
    private readonly ConcurrentDictionary<string, int> _roundRobinCounters;
    private readonly Random _random;

    /// <summary>
    /// 构造函数
    /// </summary>
    public NatsMessageRouter(INatsService natsService, IMessageTopicService topicService, ILogger<NatsMessageRouter> logger)
    {
        _natsService = natsService ?? throw new ArgumentNullException(nameof(natsService));
        _topicService = topicService ?? throw new ArgumentNullException(nameof(topicService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _routes = new ConcurrentDictionary<string, RouteRule>();
        _nodes = new ConcurrentDictionary<string, NodeInfo>();
        _roundRobinCounters = new ConcurrentDictionary<string, int>();
        _random = new Random();

        InitializeDefaultRoutes();
    }

    #region 路由规则管理

    /// <inheritdoc />
    public void RegisterRoute(string pattern, string targetRole, RoutingStrategy routingStrategy = RoutingStrategy.RoundRobin)
    {
        ArgumentException.ThrowIfNullOrEmpty(pattern);
        ArgumentException.ThrowIfNullOrEmpty(targetRole);

        var route = new RouteRule
        {
            Pattern = pattern,
            TargetRole = targetRole,
            Strategy = routingStrategy
        };

        _routes.AddOrUpdate(pattern, route, (key, oldValue) => route);
        _logger.LogDebug("Registered route: {Pattern} -> {TargetRole} ({Strategy})", pattern, targetRole, routingStrategy);
    }

    /// <inheritdoc />
    public void UnregisterRoute(string pattern)
    {
        if (_routes.TryRemove(pattern, out var route))
        {
            _logger.LogDebug("Unregistered route: {Pattern} -> {TargetRole}", pattern, route.TargetRole);
        }
    }

    /// <inheritdoc />
    public IReadOnlyList<RouteRule> GetRoutes()
    {
        return _routes.Values.Where(r => r.Enabled).ToList();
    }

    #endregion

    #region 消息路由

    /// <inheritdoc />
    public async Task RouteMessageAsync(IMessage message, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(message);

        try
        {
            // 从元数据中获取主题信息
            var subject = GetSubjectFromMessage(message);

            // 查找匹配的路由规则
            var matchedRoute = FindMatchingRoute(subject);
            if (matchedRoute != null)
            {
                await RouteToRoleAsync(message, matchedRoute.TargetRole, matchedRoute.Strategy, cancellationToken);
                return;
            }

            // 如果没有匹配的路由规则，直接发布到原主题
            await _natsService.PublishAsync(message, cancellationToken);
            _logger.LogDebug("Message routed to original subject: {Subject}", subject);
        }
        catch (Exception ex)
        {
            var subject = GetSubjectFromMessage(message);
            _logger.LogError(ex, "Failed to route message {MessageId} to subject {Subject}", message.MessageId, subject);
            OnRoutingFailed(message.MessageId, subject, null, ex.Message);
            throw;
        }
    }

    /// <inheritdoc />
    public async Task RouteToRoleAsync(IMessage message, string targetRole, RoutingStrategy strategy = RoutingStrategy.RoundRobin, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(message);
        ArgumentException.ThrowIfNullOrEmpty(targetRole);

        try
        {
            var availableNodes = GetAvailableNodes(targetRole);
            if (!availableNodes.Any())
            {
                var error = $"No available nodes found for role: {targetRole}";
                var subject = GetSubjectFromMessage(message);
                _logger.LogWarning(error);
                OnRoutingFailed(message.MessageId, subject, targetRole, error);
                throw new InvalidOperationException(error);
            }

            var selectedNode = SelectNode(availableNodes, strategy);
            var targetSubject = _topicService.GetNodeTasksTopic(selectedNode.NodeId);

            // 注意：IMessage的TargetId是只读的，目标节点通过主题确定

            await _natsService.PublishAsync(targetSubject, message, cancellationToken);
            _logger.LogDebug("Message {MessageId} routed to node {NodeId} ({Role}) via subject {Subject}", 
                message.MessageId, selectedNode.NodeId, targetRole, targetSubject);
        }
        catch (Exception ex)
        {
            var subject = GetSubjectFromMessage(message);
            _logger.LogError(ex, "Failed to route message {MessageId} to role {TargetRole}", message.MessageId, targetRole);
            OnRoutingFailed(message.MessageId, subject, targetRole, ex.Message);
            throw;
        }
    }

    /// <inheritdoc />
    public async Task BroadcastAsync(IMessage message, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(message);

        try
        {
            var allNodes = GetAllNodes();
            var tasks = allNodes.Select(async node =>
            {
                var targetSubject = _topicService.GetNodeTasksTopic(node.NodeId);
                // 直接使用原始消息进行发布，目标节点通过主题确定
                await _natsService.PublishAsync(targetSubject, message, cancellationToken);
            });

            await Task.WhenAll(tasks);
            _logger.LogDebug("Message {MessageId} broadcasted to {NodeCount} nodes", message.MessageId, allNodes.Count);
        }
        catch (Exception ex)
        {
            var subject = GetSubjectFromMessage(message);
            _logger.LogError(ex, "Failed to broadcast message {MessageId}", message.MessageId);
            OnRoutingFailed(message.MessageId, subject, "ALL", ex.Message);
            throw;
        }
    }

    /// <inheritdoc />
    public async Task BroadcastToRoleAsync(IMessage message, string targetRole, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(message);
        ArgumentException.ThrowIfNullOrEmpty(targetRole);

        try
        {
            var roleNodes = GetAvailableNodes(targetRole);
            if (!roleNodes.Any())
            {
                _logger.LogWarning("No available nodes found for role: {TargetRole}", targetRole);
                return;
            }

            var tasks = roleNodes.Select(async node =>
            {
                var targetSubject = _topicService.GetNodeTasksTopic(node.NodeId);
                // 直接使用原始消息进行发布，目标节点通过主题确定
                await _natsService.PublishAsync(targetSubject, message, cancellationToken);
            });

            await Task.WhenAll(tasks);
            _logger.LogDebug("Message {MessageId} broadcasted to {NodeCount} nodes with role {TargetRole}", 
                message.MessageId, roleNodes.Count, targetRole);
        }
        catch (Exception ex)
        {
            var subject = GetSubjectFromMessage(message);
            _logger.LogError(ex, "Failed to broadcast message {MessageId} to role {TargetRole}", message.MessageId, targetRole);
            OnRoutingFailed(message.MessageId, subject, targetRole, ex.Message);
            throw;
        }
    }

    /// <inheritdoc />
    public async Task PublishAsync<T>(string subject, T message, CancellationToken cancellationToken = default) where T : class
    {
        ArgumentException.ThrowIfNullOrEmpty(subject);
        ArgumentNullException.ThrowIfNull(message);

        try
        {
            await _natsService.PublishAsync(subject, message, cancellationToken);
            _logger.LogDebug("Message published to subject: {Subject}", subject);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to publish message to subject: {Subject}", subject);
            throw;
        }
    }

    #endregion

    #region 节点管理

    /// <inheritdoc />
    public void RegisterNode(NodeInfo nodeInfo)
    {
        ArgumentNullException.ThrowIfNull(nodeInfo);
        ArgumentException.ThrowIfNullOrEmpty(nodeInfo.NodeId);

        var oldStatus = _nodes.TryGetValue(nodeInfo.NodeId, out var existingNode) ? existingNode.Status : "Unknown";
        
        _nodes.AddOrUpdate(nodeInfo.NodeId, nodeInfo, (key, oldValue) =>
        {
            // 保留注册时间
            nodeInfo.RegisteredAt = oldValue.RegisteredAt;
            return nodeInfo;
        });

        _logger.LogInformation("Node registered: {NodeId} ({Role}) - {Status}", 
            nodeInfo.NodeId, nodeInfo.Role, nodeInfo.Status);

        if (oldStatus != nodeInfo.Status)
        {
            OnNodeStatusChanged(nodeInfo.NodeId, oldStatus, nodeInfo.Status);
        }
    }

    /// <inheritdoc />
    public void UnregisterNode(string nodeId)
    {
        ArgumentException.ThrowIfNullOrEmpty(nodeId);

        if (_nodes.TryRemove(nodeId, out var node))
        {
            _logger.LogInformation("Node unregistered: {NodeId} ({Role})", nodeId, node.Role);
            OnNodeStatusChanged(nodeId, node.Status, "Offline");
        }
    }

    /// <inheritdoc />
    public void UpdateNodeLoad(string nodeId, NodeLoadInfo loadInfo)
    {
        ArgumentException.ThrowIfNullOrEmpty(nodeId);
        ArgumentNullException.ThrowIfNull(loadInfo);

        if (_nodes.TryGetValue(nodeId, out var node))
        {
            node.LoadInfo = loadInfo;
            node.LastHeartbeat = DateTime.UtcNow;
            _logger.LogDebug("Updated load info for node {NodeId}: CPU={CpuUsage}%, Memory={MemoryUsage}%, Tasks={ActiveTasks}/{MaxTasks}", 
                nodeId, loadInfo.CpuUsage, loadInfo.MemoryUsage, loadInfo.ActiveTasks, loadInfo.MaxTasks);
        }
    }

    /// <inheritdoc />
    public IReadOnlyList<NodeInfo> GetAvailableNodes(string role)
    {
        return _nodes.Values
            .Where(n => n.Role.Equals(role, StringComparison.OrdinalIgnoreCase) && n.IsOnline)
            .OrderBy(n => n.LoadInfo.ActiveTasks)
            .ToList();
    }

    /// <inheritdoc />
    public IReadOnlyList<NodeInfo> GetAllNodes()
    {
        return _nodes.Values.ToList();
    }

    #endregion

    #region 事件

    /// <inheritdoc />
    public event EventHandler<NodeStatusChangedEventArgs>? NodeStatusChanged;

    /// <inheritdoc />
    public event EventHandler<RoutingFailedEventArgs>? RoutingFailed;

    #endregion

    #region 私有方法

    private void InitializeDefaultRoutes()
    {
        // 注册默认路由规则
        RegisterRoute("flowcustom.designer.*", "Designer", RoutingStrategy.LeastLoad);
        RegisterRoute("flowcustom.validator.*", "Validator", RoutingStrategy.RoundRobin);
        RegisterRoute("flowcustom.executor.*", "Executor", RoutingStrategy.LeastLoad);
        RegisterRoute("flowcustom.tasks.high", "Executor", RoutingStrategy.FastestResponse);
        RegisterRoute("flowcustom.tasks.normal", "Executor", RoutingStrategy.LeastLoad);
        RegisterRoute("flowcustom.tasks.low", "Executor", RoutingStrategy.RoundRobin);

        _logger.LogDebug("Default routing rules initialized");
    }

    private RouteRule? FindMatchingRoute(string subject)
    {
        return _routes.Values
            .Where(r => r.Enabled && IsPatternMatch(r.Pattern, subject))
            .OrderByDescending(r => r.Pattern.Length) // 优先匹配更具体的模式
            .FirstOrDefault();
    }

    private static bool IsPatternMatch(string pattern, string subject)
    {
        // 将NATS通配符模式转换为正则表达式
        var regexPattern = pattern
            .Replace(".", "\\.")
            .Replace("*", "[^.]*")
            .Replace(">", ".*");
        
        return Regex.IsMatch(subject, $"^{regexPattern}$", RegexOptions.IgnoreCase);
    }

    private NodeInfo SelectNode(IReadOnlyList<NodeInfo> nodes, RoutingStrategy strategy)
    {
        return strategy switch
        {
            RoutingStrategy.RoundRobin => SelectRoundRobin(nodes),
            RoutingStrategy.LeastConnections => SelectLeastConnections(nodes),
            RoutingStrategy.LeastLoad => SelectLeastLoad(nodes),
            RoutingStrategy.Random => SelectRandom(nodes),
            RoutingStrategy.WeightedRoundRobin => SelectWeightedRoundRobin(nodes),
            RoutingStrategy.FastestResponse => SelectFastestResponse(nodes),
            _ => nodes.First()
        };
    }

    private NodeInfo SelectRoundRobin(IReadOnlyList<NodeInfo> nodes)
    {
        var key = string.Join(",", nodes.Select(n => n.NodeId).OrderBy(id => id));
        var counter = _roundRobinCounters.AddOrUpdate(key, 0, (k, v) => (v + 1) % nodes.Count);
        return nodes[counter];
    }

    private NodeInfo SelectLeastConnections(IReadOnlyList<NodeInfo> nodes)
    {
        return nodes.OrderBy(n => n.LoadInfo.ActiveTasks).First();
    }

    private NodeInfo SelectLeastLoad(IReadOnlyList<NodeInfo> nodes)
    {
        return nodes.OrderBy(n => n.LoadInfo.CpuUsage + n.LoadInfo.MemoryUsage).First();
    }

    private NodeInfo SelectRandom(IReadOnlyList<NodeInfo> nodes)
    {
        return nodes[_random.Next(nodes.Count)];
    }

    private NodeInfo SelectWeightedRoundRobin(IReadOnlyList<NodeInfo> nodes)
    {
        var totalWeight = nodes.Sum(n => n.Weight);
        var randomWeight = _random.Next(totalWeight);
        var currentWeight = 0;

        foreach (var node in nodes)
        {
            currentWeight += node.Weight;
            if (randomWeight < currentWeight)
                return node;
        }

        return nodes.First();
    }

    private NodeInfo SelectFastestResponse(IReadOnlyList<NodeInfo> nodes)
    {
        return nodes.OrderBy(n => n.LoadInfo.NetworkLatency).First();
    }

    /// <summary>
    /// 从消息元数据中获取主题信息
    /// </summary>
    /// <param name="message">消息对象</param>
    /// <returns>主题字符串</returns>
    private static string GetSubjectFromMessage(IMessage message)
    {
        if (message.Metadata.TryGetValue("Subject", out var subjectObj) && subjectObj is string subject)
        {
            return subject;
        }

        // 如果没有主题信息，使用消息类型作为默认主题
        return $"default.{message.MessageType.ToLowerInvariant()}";
    }

    private void OnNodeStatusChanged(string nodeId, string oldStatus, string newStatus)
    {
        NodeStatusChanged?.Invoke(this, new NodeStatusChangedEventArgs
        {
            NodeId = nodeId,
            OldStatus = oldStatus,
            NewStatus = newStatus
        });
    }

    private void OnRoutingFailed(string messageId, string subject, string? targetRole, string error)
    {
        RoutingFailed?.Invoke(this, new RoutingFailedEventArgs
        {
            MessageId = messageId,
            Subject = subject,
            TargetRole = targetRole,
            Error = error
        });
    }

    #endregion
}
