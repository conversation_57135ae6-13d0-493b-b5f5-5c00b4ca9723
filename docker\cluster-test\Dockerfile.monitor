# FlowCustomV1 Monitor节点 Dockerfile
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
WORKDIR /app

FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

# 复制项目文件
COPY ["src/FlowCustomV1.Core/FlowCustomV1.Core.csproj", "src/FlowCustomV1.Core/"]
COPY ["src/FlowCustomV1.Engine/FlowCustomV1.Engine.csproj", "src/FlowCustomV1.Engine/"]
COPY ["src/FlowCustomV1.Infrastructure/FlowCustomV1.Infrastructure.csproj", "src/FlowCustomV1.Infrastructure/"]
COPY ["src/FlowCustomV1.Api/FlowCustomV1.Api.csproj", "src/FlowCustomV1.Api/"]

# 还原依赖
RUN dotnet restore "src/FlowCustomV1.Api/FlowCustomV1.Api.csproj"

# 复制所有源代码
COPY . .

# 构建项目
WORKDIR "/src/src/FlowCustomV1.Api"
RUN dotnet build "FlowCustomV1.Api.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "FlowCustomV1.Api.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .

# 创建Monitor节点特定配置
RUN echo '{ \
  "System": { \
    "Node": { \
      "NodeId": "flowcustom-test-monitor-001", \
      "NodeName": "FlowCustom Testing Monitor Node", \
      "IpAddress": "0.0.0.0", \
      "Roles": ["Monitor"], \
      "Labels": { \
        "environment": "testing", \
        "version": "v0.0.1.8", \
        "cluster": "docker", \
        "role": "monitor-only" \
      } \
    } \
  }, \
  "NodeDiscovery": { \
    "NodeRole": "Monitor", \
    "NodeTags": [ \
      "monitor", \
      "testing", \
      "docker", \
      "single-role" \
    ], \
    "CapabilityTags": [ \
      "system-monitoring", \
      "performance-tracking", \
      "health-check", \
      "metrics-collection" \
    ] \
  } \
}' > appsettings.Testing.override.json

ENTRYPOINT ["dotnet", "FlowCustomV1.Api.dll", "--environment", "Testing"]
