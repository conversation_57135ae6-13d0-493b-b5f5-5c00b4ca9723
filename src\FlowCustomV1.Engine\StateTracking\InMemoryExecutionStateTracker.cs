using System.Collections.Concurrent;
using Microsoft.Extensions.Logging;
using FlowCustomV1.Core.Models.Workflow;

namespace FlowCustomV1.Engine.StateTracking;

/// <summary>
/// 内存版本的执行状态跟踪器
/// 在内存中跟踪工作流和节点的执行状态，适用于单机部署
/// </summary>
public class InMemoryExecutionStateTracker : IExecutionStateTracker
{
    private readonly ILogger<InMemoryExecutionStateTracker> _logger;
    
    // 当前状态存储
    private readonly ConcurrentDictionary<string, WorkflowExecutionState> _workflowStates = new();
    private readonly ConcurrentDictionary<string, NodeExecutionState> _nodeStates = new();
    
    // 状态历史存储
    private readonly ConcurrentDictionary<string, List<WorkflowStateHistoryEntry>> _workflowStateHistory = new();
    private readonly ConcurrentDictionary<string, List<NodeStateHistoryEntry>> _nodeStateHistory = new();
    
    // 工作流到节点的映射
    private readonly ConcurrentDictionary<string, HashSet<string>> _workflowNodes = new();
    
    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public InMemoryExecutionStateTracker(ILogger<InMemoryExecutionStateTracker> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 跟踪工作流状态变更
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="state">新状态</param>
    /// <param name="reason">状态变更原因</param>
    /// <param name="metadata">状态元数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>跟踪任务</returns>
    public async Task TrackWorkflowStateAsync(
        string executionId, 
        WorkflowExecutionState state, 
        string? reason = null,
        Dictionary<string, object>? metadata = null,
        CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(executionId))
            throw new ArgumentException("ExecutionId cannot be null or empty", nameof(executionId));

        var oldState = _workflowStates.GetValueOrDefault(executionId, WorkflowExecutionState.NotStarted);
        
        // 更新当前状态
        _workflowStates.AddOrUpdate(executionId, state, (key, oldValue) => state);
        
        // 添加到历史记录
        var historyEntry = new WorkflowStateHistoryEntry
        {
            ExecutionId = executionId,
            WorkflowId = GetWorkflowIdFromExecutionId(executionId),
            State = state,
            ChangedAt = DateTime.UtcNow,
            Reason = reason,
            Metadata = metadata ?? new Dictionary<string, object>()
        };
        
        _workflowStateHistory.AddOrUpdate(
            executionId,
            new List<WorkflowStateHistoryEntry> { historyEntry },
            (key, existingHistory) =>
            {
                existingHistory.Add(historyEntry);
                return existingHistory;
            });

        _logger.LogDebug("Workflow state tracked: {ExecutionId} {OldState} → {NewState} ({Reason})",
            executionId, oldState, state, reason ?? "No reason");

        // 触发状态变更事件
        WorkflowStateChanged?.Invoke(this, new WorkflowStateChangedEventArgs
        {
            ExecutionId = executionId,
            WorkflowId = historyEntry.WorkflowId,
            OldState = oldState,
            NewState = state,
            ChangedAt = historyEntry.ChangedAt,
            Reason = reason,
            Metadata = historyEntry.Metadata
        });

        await Task.CompletedTask;
    }

    /// <summary>
    /// 跟踪节点状态变更
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="nodeId">节点ID</param>
    /// <param name="state">新状态</param>
    /// <param name="reason">状态变更原因</param>
    /// <param name="metadata">状态元数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>跟踪任务</returns>
    public async Task TrackNodeStateAsync(
        string executionId, 
        string nodeId, 
        NodeExecutionState state, 
        string? reason = null,
        Dictionary<string, object>? metadata = null,
        CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(executionId))
            throw new ArgumentException("ExecutionId cannot be null or empty", nameof(executionId));
        if (string.IsNullOrEmpty(nodeId))
            throw new ArgumentException("NodeId cannot be null or empty", nameof(nodeId));

        var nodeKey = $"{executionId}_{nodeId}";
        var oldState = _nodeStates.GetValueOrDefault(nodeKey, NodeExecutionState.NotStarted);
        
        // 更新当前状态
        _nodeStates.AddOrUpdate(nodeKey, state, (key, oldValue) => state);
        
        // 维护工作流到节点的映射
        _workflowNodes.AddOrUpdate(
            executionId,
            new HashSet<string> { nodeId },
            (key, existingNodes) =>
            {
                existingNodes.Add(nodeId);
                return existingNodes;
            });
        
        // 添加到历史记录
        var historyEntry = new NodeStateHistoryEntry
        {
            ExecutionId = executionId,
            WorkflowId = GetWorkflowIdFromExecutionId(executionId),
            NodeId = nodeId,
            State = state,
            ChangedAt = DateTime.UtcNow,
            Reason = reason,
            Metadata = metadata ?? new Dictionary<string, object>()
        };
        
        _nodeStateHistory.AddOrUpdate(
            nodeKey,
            new List<NodeStateHistoryEntry> { historyEntry },
            (key, existingHistory) =>
            {
                existingHistory.Add(historyEntry);
                return existingHistory;
            });

        _logger.LogDebug("Node state tracked: {ExecutionId}/{NodeId} {OldState} → {NewState} ({Reason})",
            executionId, nodeId, oldState, state, reason ?? "No reason");

        // 触发状态变更事件
        NodeStateChanged?.Invoke(this, new NodeStateChangedEventArgs
        {
            ExecutionId = executionId,
            WorkflowId = historyEntry.WorkflowId,
            NodeId = nodeId,
            OldState = oldState,
            NewState = state,
            ChangedAt = historyEntry.ChangedAt,
            Reason = reason,
            Metadata = historyEntry.Metadata
        });

        await Task.CompletedTask;
    }

    /// <summary>
    /// 获取工作流当前状态
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>工作流状态</returns>
    public async Task<WorkflowExecutionState> GetWorkflowStateAsync(
        string executionId, 
        CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(executionId))
            throw new ArgumentException("ExecutionId cannot be null or empty", nameof(executionId));

        var state = _workflowStates.GetValueOrDefault(executionId, WorkflowExecutionState.NotStarted);
        return await Task.FromResult(state);
    }

    /// <summary>
    /// 获取节点当前状态
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="nodeId">节点ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>节点状态</returns>
    public async Task<NodeExecutionState> GetNodeStateAsync(
        string executionId, 
        string nodeId, 
        CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(executionId))
            throw new ArgumentException("ExecutionId cannot be null or empty", nameof(executionId));
        if (string.IsNullOrEmpty(nodeId))
            throw new ArgumentException("NodeId cannot be null or empty", nameof(nodeId));

        var nodeKey = $"{executionId}_{nodeId}";
        var state = _nodeStates.GetValueOrDefault(nodeKey, NodeExecutionState.NotStarted);
        return await Task.FromResult(state);
    }

    /// <summary>
    /// 获取工作流的所有节点状态
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>节点状态映射</returns>
    public async Task<Dictionary<string, NodeExecutionState>> GetAllNodeStatesAsync(
        string executionId, 
        CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(executionId))
            throw new ArgumentException("ExecutionId cannot be null or empty", nameof(executionId));

        var result = new Dictionary<string, NodeExecutionState>();
        
        if (_workflowNodes.TryGetValue(executionId, out var nodeIds))
        {
            foreach (var nodeId in nodeIds)
            {
                var nodeKey = $"{executionId}_{nodeId}";
                var state = _nodeStates.GetValueOrDefault(nodeKey, NodeExecutionState.NotStarted);
                result[nodeId] = state;
            }
        }
        
        return await Task.FromResult(result);
    }

    /// <summary>
    /// 从执行ID提取工作流ID（简化实现）
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <returns>工作流ID</returns>
    private string GetWorkflowIdFromExecutionId(string executionId)
    {
        // 简化实现：假设执行ID包含工作流ID信息
        // 在实际实现中，这应该从执行上下文或其他地方获取
        return executionId.Split('_').FirstOrDefault() ?? executionId;
    }

    /// <summary>
    /// 获取工作流状态历史
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>状态历史列表</returns>
    public async Task<List<WorkflowStateHistoryEntry>> GetWorkflowStateHistoryAsync(
        string executionId,
        CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(executionId))
            throw new ArgumentException("ExecutionId cannot be null or empty", nameof(executionId));

        var history = _workflowStateHistory.GetValueOrDefault(executionId, new List<WorkflowStateHistoryEntry>());
        return await Task.FromResult(new List<WorkflowStateHistoryEntry>(history.OrderBy(h => h.ChangedAt)));
    }

    /// <summary>
    /// 获取工作流历史（兼容性方法）
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>状态历史列表</returns>
    public async Task<List<WorkflowStateHistoryEntry>> GetWorkflowHistoryAsync(
        string executionId,
        CancellationToken cancellationToken = default)
    {
        // 委托给GetWorkflowStateHistoryAsync方法
        return await GetWorkflowStateHistoryAsync(executionId, cancellationToken);
    }

    /// <summary>
    /// 获取节点状态历史
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="nodeId">节点ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>状态历史列表</returns>
    public async Task<List<NodeStateHistoryEntry>> GetNodeStateHistoryAsync(
        string executionId,
        string nodeId,
        CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(executionId))
            throw new ArgumentException("ExecutionId cannot be null or empty", nameof(executionId));
        if (string.IsNullOrEmpty(nodeId))
            throw new ArgumentException("NodeId cannot be null or empty", nameof(nodeId));

        var nodeKey = $"{executionId}_{nodeId}";
        var history = _nodeStateHistory.GetValueOrDefault(nodeKey, new List<NodeStateHistoryEntry>());
        return await Task.FromResult(new List<NodeStateHistoryEntry>(history.OrderBy(h => h.ChangedAt)));
    }

    /// <summary>
    /// 清理指定工作流的状态数据
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>清理任务</returns>
    public async Task CleanupWorkflowStateAsync(
        string executionId,
        CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(executionId))
            throw new ArgumentException("ExecutionId cannot be null or empty", nameof(executionId));

        var cleanedItems = 0;

        // 清理工作流状态
        if (_workflowStates.TryRemove(executionId, out _))
            cleanedItems++;

        // 清理工作流状态历史
        if (_workflowStateHistory.TryRemove(executionId, out _))
            cleanedItems++;

        // 清理节点状态和历史
        if (_workflowNodes.TryGetValue(executionId, out var nodeIds))
        {
            foreach (var nodeId in nodeIds)
            {
                var nodeKey = $"{executionId}_{nodeId}";
                if (_nodeStates.TryRemove(nodeKey, out _))
                    cleanedItems++;
                if (_nodeStateHistory.TryRemove(nodeKey, out _))
                    cleanedItems++;
            }
        }

        // 清理工作流节点映射
        if (_workflowNodes.TryRemove(executionId, out _))
            cleanedItems++;

        _logger.LogInformation("Cleaned up {Count} state items for workflow {ExecutionId}", cleanedItems, executionId);
        await Task.CompletedTask;
    }

    /// <summary>
    /// 清理过期的状态数据
    /// </summary>
    /// <param name="olderThan">清理早于此时间的数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>清理任务</returns>
    public async Task CleanupExpiredStatesAsync(
        DateTime olderThan,
        CancellationToken cancellationToken = default)
    {
        var cleanedWorkflows = 0;
        var cleanedNodes = 0;

        // 清理过期的工作流状态历史
        var expiredWorkflows = new List<string>();
        foreach (var kvp in _workflowStateHistory)
        {
            var latestEntry = kvp.Value.OrderByDescending(h => h.ChangedAt).FirstOrDefault();
            if (latestEntry != null && latestEntry.ChangedAt < olderThan)
            {
                expiredWorkflows.Add(kvp.Key);
            }
        }

        foreach (var executionId in expiredWorkflows)
        {
            await CleanupWorkflowStateAsync(executionId, cancellationToken);
            cleanedWorkflows++;
        }

        // 清理孤立的节点状态（没有对应工作流的节点）
        var orphanedNodes = new List<string>();
        foreach (var kvp in _nodeStateHistory)
        {
            var nodeKey = kvp.Key;
            var executionId = nodeKey.Split('_')[0];

            if (!_workflowStateHistory.ContainsKey(executionId))
            {
                var latestEntry = kvp.Value.OrderByDescending(h => h.ChangedAt).FirstOrDefault();
                if (latestEntry != null && latestEntry.ChangedAt < olderThan)
                {
                    orphanedNodes.Add(nodeKey);
                }
            }
        }

        foreach (var nodeKey in orphanedNodes)
        {
            _nodeStates.TryRemove(nodeKey, out _);
            _nodeStateHistory.TryRemove(nodeKey, out _);
            cleanedNodes++;
        }

        _logger.LogInformation("Cleaned up {WorkflowCount} expired workflows and {NodeCount} orphaned nodes older than {OlderThan}",
            cleanedWorkflows, cleanedNodes, olderThan);

        await Task.CompletedTask;
    }

    /// <summary>
    /// 获取状态跟踪器统计信息
    /// </summary>
    /// <returns>统计信息</returns>
    public StateTrackerStatistics GetStatistics()
    {
        return new StateTrackerStatistics
        {
            TrackedWorkflows = _workflowStates.Count,
            TrackedNodes = _nodeStates.Count,
            WorkflowHistoryEntries = _workflowStateHistory.Values.Sum(h => h.Count),
            NodeHistoryEntries = _nodeStateHistory.Values.Sum(h => h.Count),
            MemoryUsageEstimate = EstimateMemoryUsage()
        };
    }

    /// <summary>
    /// 估算内存使用量（字节）
    /// </summary>
    /// <returns>内存使用量估算</returns>
    private long EstimateMemoryUsage()
    {
        long estimate = 0;

        // 估算工作流状态存储
        estimate += _workflowStates.Count * 100; // 粗略估算每个条目100字节

        // 估算节点状态存储
        estimate += _nodeStates.Count * 100;

        // 估算历史记录存储
        estimate += _workflowStateHistory.Values.Sum(h => h.Count) * 200; // 每个历史条目200字节
        estimate += _nodeStateHistory.Values.Sum(h => h.Count) * 200;

        return estimate;
    }

    /// <summary>
    /// 工作流状态变更事件
    /// </summary>
    public event EventHandler<WorkflowStateChangedEventArgs>? WorkflowStateChanged;

    /// <summary>
    /// 节点状态变更事件
    /// </summary>
    public event EventHandler<NodeStateChangedEventArgs>? NodeStateChanged;
}

/// <summary>
/// 状态跟踪器统计信息
/// </summary>
public class StateTrackerStatistics
{
    /// <summary>
    /// 跟踪的工作流数量
    /// </summary>
    public int TrackedWorkflows { get; set; }

    /// <summary>
    /// 跟踪的节点数量
    /// </summary>
    public int TrackedNodes { get; set; }

    /// <summary>
    /// 工作流历史条目数量
    /// </summary>
    public int WorkflowHistoryEntries { get; set; }

    /// <summary>
    /// 节点历史条目数量
    /// </summary>
    public int NodeHistoryEntries { get; set; }

    /// <summary>
    /// 内存使用量估算（字节）
    /// </summary>
    public long MemoryUsageEstimate { get; set; }

    /// <summary>
    /// 获取统计信息的字符串表示
    /// </summary>
    /// <returns>统计信息字符串</returns>
    public override string ToString()
    {
        return $"StateTracker: Workflows={TrackedWorkflows}, Nodes={TrackedNodes}, " +
               $"History={WorkflowHistoryEntries + NodeHistoryEntries}, Memory={MemoryUsageEstimate / 1024}KB";
    }
}
