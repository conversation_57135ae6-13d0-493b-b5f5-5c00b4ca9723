using FlowCustomV1.Core.Models.Workflow;
using System.Text.Json.Serialization;

namespace FlowCustomV1.Core.Models.Executor;

/// <summary>
/// 执行信息
/// </summary>
public class ExecutionInfo
{
    /// <summary>
    /// 执行ID
    /// </summary>
    public string ExecutionId { get; set; } = string.Empty;

    /// <summary>
    /// 工作流ID
    /// </summary>
    public string WorkflowId { get; set; } = string.Empty;

    /// <summary>
    /// 工作流名称
    /// </summary>
    public string WorkflowName { get; set; } = string.Empty;

    /// <summary>
    /// 执行状态
    /// </summary>
    public WorkflowExecutionState State { get; set; }

    /// <summary>
    /// 执行节点ID
    /// </summary>
    public string ExecutorNodeId { get; set; } = string.Empty;

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime StartedAt { get; set; }

    /// <summary>
    /// 完成时间
    /// </summary>
    public DateTime? CompletedAt { get; set; }

    /// <summary>
    /// 执行时长
    /// </summary>
    public TimeSpan? Duration => CompletedAt?.Subtract(StartedAt);

    /// <summary>
    /// 输入数据大小（字节）
    /// </summary>
    public long InputDataSize { get; set; }

    /// <summary>
    /// 输出数据大小（字节）
    /// </summary>
    public long OutputDataSize { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 执行优先级
    /// </summary>
    public ExecutionPriority Priority { get; set; } = ExecutionPriority.Normal;

    /// <summary>
    /// 执行标签
    /// </summary>
    public List<string> Tags { get; set; } = new();

    /// <summary>
    /// 执行元数据
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// 执行容量信息
/// </summary>
public class ExecutionCapacity
{
    /// <summary>
    /// 节点ID
    /// </summary>
    public string NodeId { get; set; } = string.Empty;

    /// <summary>
    /// 最大并发执行数
    /// </summary>
    public int MaxConcurrentExecutions { get; set; } = 10;

    /// <summary>
    /// 当前运行的执行数
    /// </summary>
    public int CurrentExecutions { get; set; }

    /// <summary>
    /// 可用执行槽位
    /// </summary>
    public int AvailableSlots => Math.Max(0, MaxConcurrentExecutions - CurrentExecutions);

    /// <summary>
    /// CPU使用率（0-100）
    /// </summary>
    public double CpuUsagePercent { get; set; }

    /// <summary>
    /// 内存使用率（0-100）
    /// </summary>
    public double MemoryUsagePercent { get; set; }

    /// <summary>
    /// 磁盘使用率（0-100）
    /// </summary>
    public double DiskUsagePercent { get; set; }

    /// <summary>
    /// 网络使用率（0-100）
    /// </summary>
    public double NetworkUsagePercent { get; set; }

    /// <summary>
    /// 负载评分（0-100，越低越好）
    /// </summary>
    public double LoadScore { get; set; }

    /// <summary>
    /// 是否可以接受新执行
    /// </summary>
    public bool CanAcceptExecution => AvailableSlots > 0 && LoadScore < 90;

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 节点负载信息
/// </summary>
public class NodeLoadInfo
{
    /// <summary>
    /// 节点ID
    /// </summary>
    public string NodeId { get; set; } = string.Empty;

    /// <summary>
    /// 节点名称
    /// </summary>
    public string NodeName { get; set; } = string.Empty;

    /// <summary>
    /// 节点角色
    /// </summary>
    public string NodeRole { get; set; } = "Executor";

    /// <summary>
    /// CPU核心数
    /// </summary>
    public int CpuCores { get; set; }

    /// <summary>
    /// 总内存（MB）
    /// </summary>
    public long TotalMemoryMb { get; set; }

    /// <summary>
    /// 可用内存（MB）
    /// </summary>
    public long AvailableMemoryMb { get; set; }

    /// <summary>
    /// 总磁盘空间（MB）
    /// </summary>
    public long TotalDiskMb { get; set; }

    /// <summary>
    /// 可用磁盘空间（MB）
    /// </summary>
    public long AvailableDiskMb { get; set; }

    /// <summary>
    /// 网络带宽（Mbps）
    /// </summary>
    public double NetworkBandwidthMbps { get; set; }

    /// <summary>
    /// 当前执行数
    /// </summary>
    public int CurrentExecutions { get; set; }

    /// <summary>
    /// 最大执行数
    /// </summary>
    public int MaxExecutions { get; set; }

    /// <summary>
    /// 平均执行时间（毫秒）
    /// </summary>
    public double AverageExecutionTimeMs { get; set; }

    /// <summary>
    /// 成功执行数
    /// </summary>
    public long SuccessfulExecutions { get; set; }

    /// <summary>
    /// 失败执行数
    /// </summary>
    public long FailedExecutions { get; set; }

    /// <summary>
    /// 节点健康状态
    /// </summary>
    public NodeHealthStatus HealthStatus { get; set; } = NodeHealthStatus.Healthy;

    /// <summary>
    /// 最后心跳时间
    /// </summary>
    public DateTime LastHeartbeat { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 节点标签
    /// </summary>
    public List<string> Tags { get; set; } = new();

    /// <summary>
    /// 节点能力
    /// </summary>
    public List<string> Capabilities { get; set; } = new();
}

/// <summary>
/// 资源使用量
/// </summary>
public class ResourceUsage
{
    /// <summary>
    /// CPU使用量（核心数）
    /// </summary>
    public double CpuCores { get; set; }

    /// <summary>
    /// 内存使用量（MB）
    /// </summary>
    public long MemoryMB { get; set; }

    /// <summary>
    /// 磁盘使用量（MB）
    /// </summary>
    public long DiskMB { get; set; }

    /// <summary>
    /// 网络带宽使用量（Mbps）
    /// </summary>
    public double NetworkMbps { get; set; }

    /// <summary>
    /// 预估执行时间（毫秒）
    /// </summary>
    public long EstimatedDurationMs { get; set; }

    /// <summary>
    /// 资源权重（用于负载均衡）
    /// </summary>
    public double Weight { get; set; } = 1.0;
}

/// <summary>
/// 执行统计信息
/// </summary>
public class ExecutionStatistics
{
    /// <summary>
    /// 节点ID
    /// </summary>
    public string NodeId { get; set; } = string.Empty;

    /// <summary>
    /// 统计时间范围开始
    /// </summary>
    public DateTime StartTime { get; set; }

    /// <summary>
    /// 统计时间范围结束
    /// </summary>
    public DateTime EndTime { get; set; }

    /// <summary>
    /// 开始日期（别名属性，兼容性）
    /// </summary>
    public DateTime? StartDate
    {
        get => StartTime;
        set => StartTime = value ?? DateTime.MinValue;
    }

    /// <summary>
    /// 结束日期（别名属性，兼容性）
    /// </summary>
    public DateTime? EndDate
    {
        get => EndTime;
        set => EndTime = value ?? DateTime.MaxValue;
    }

    /// <summary>
    /// 总执行数
    /// </summary>
    public long TotalExecutions { get; set; }

    /// <summary>
    /// 成功执行数
    /// </summary>
    public long SuccessfulExecutions { get; set; }

    /// <summary>
    /// 失败执行数
    /// </summary>
    public long FailedExecutions { get; set; }

    /// <summary>
    /// 取消执行数
    /// </summary>
    public long CancelledExecutions { get; set; }

    /// <summary>
    /// 平均执行时间（毫秒）
    /// </summary>
    public double AverageExecutionTimeMs { get; set; }

    /// <summary>
    /// 最短执行时间（毫秒）
    /// </summary>
    public long MinExecutionTimeMs { get; set; }

    /// <summary>
    /// 最长执行时间（毫秒）
    /// </summary>
    public long MaxExecutionTimeMs { get; set; }

    /// <summary>
    /// 吞吐量（执行数/小时）
    /// </summary>
    public double ThroughputPerHour { get; set; }

    /// <summary>
    /// 成功率（0-100）
    /// </summary>
    public double SuccessRate => TotalExecutions > 0 ? (double)SuccessfulExecutions / TotalExecutions * 100 : 0;

    /// <summary>
    /// 错误率（0-100）
    /// </summary>
    public double ErrorRate => TotalExecutions > 0 ? (double)FailedExecutions / TotalExecutions * 100 : 0;
}

/// <summary>
/// 执行优先级
/// </summary>
[JsonConverter(typeof(JsonStringEnumConverter))]
public enum ExecutionPriority
{
    /// <summary>
    /// 低优先级
    /// </summary>
    Low = 1,

    /// <summary>
    /// 普通优先级
    /// </summary>
    Normal = 2,

    /// <summary>
    /// 高优先级
    /// </summary>
    High = 3,

    /// <summary>
    /// 紧急优先级
    /// </summary>
    Critical = 4
}

/// <summary>
/// 节点健康状态
/// </summary>
[JsonConverter(typeof(JsonStringEnumConverter))]
public enum NodeHealthStatus
{
    /// <summary>
    /// 健康
    /// </summary>
    Healthy,

    /// <summary>
    /// 警告
    /// </summary>
    Warning,

    /// <summary>
    /// 不健康
    /// </summary>
    Unhealthy,

    /// <summary>
    /// 离线
    /// </summary>
    Offline
}

/// <summary>
/// 执行器服务状态
/// </summary>
[JsonConverter(typeof(JsonStringEnumConverter))]
public enum ExecutorServiceStatus
{
    /// <summary>
    /// 未启动
    /// </summary>
    NotStarted,

    /// <summary>
    /// 启动中
    /// </summary>
    Starting,

    /// <summary>
    /// 运行中
    /// </summary>
    Running,

    /// <summary>
    /// 暂停中
    /// </summary>
    Pausing,

    /// <summary>
    /// 已暂停
    /// </summary>
    Paused,

    /// <summary>
    /// 停止中
    /// </summary>
    Stopping,

    /// <summary>
    /// 已停止
    /// </summary>
    Stopped,

    /// <summary>
    /// 错误状态
    /// </summary>
    Error
}

/// <summary>
/// 容量限制
/// </summary>
public class CapacityLimits
{
    /// <summary>
    /// 最大并发执行数
    /// </summary>
    public int MaxConcurrentExecutions { get; set; } = 10;

    /// <summary>
    /// 最大CPU使用率（0-100）
    /// </summary>
    public double MaxCpuUsagePercent { get; set; } = 80;

    /// <summary>
    /// 最大内存使用率（0-100）
    /// </summary>
    public double MaxMemoryUsagePercent { get; set; } = 80;

    /// <summary>
    /// 最大磁盘使用率（0-100）
    /// </summary>
    public double MaxDiskUsagePercent { get; set; } = 90;

    /// <summary>
    /// 最大网络使用率（0-100）
    /// </summary>
    public double MaxNetworkUsagePercent { get; set; } = 80;

    /// <summary>
    /// 最大负载评分
    /// </summary>
    public double MaxLoadScore { get; set; } = 90;
}

/// <summary>
/// 资源使用统计
/// </summary>
public class ResourceUsageStatistics
{
    /// <summary>
    /// 统计时间范围开始
    /// </summary>
    public DateTime StartTime { get; set; }

    /// <summary>
    /// 统计时间范围结束
    /// </summary>
    public DateTime EndTime { get; set; }

    /// <summary>
    /// 平均CPU使用率
    /// </summary>
    public double AverageCpuUsage { get; set; }

    /// <summary>
    /// 峰值CPU使用率
    /// </summary>
    public double PeakCpuUsage { get; set; }

    /// <summary>
    /// 平均内存使用率
    /// </summary>
    public double AverageMemoryUsage { get; set; }

    /// <summary>
    /// 峰值内存使用率
    /// </summary>
    public double PeakMemoryUsage { get; set; }

    /// <summary>
    /// 平均磁盘使用率
    /// </summary>
    public double AverageDiskUsage { get; set; }

    /// <summary>
    /// 峰值磁盘使用率
    /// </summary>
    public double PeakDiskUsage { get; set; }

    /// <summary>
    /// 平均网络使用率
    /// </summary>
    public double AverageNetworkUsage { get; set; }

    /// <summary>
    /// 峰值网络使用率
    /// </summary>
    public double PeakNetworkUsage { get; set; }

    /// <summary>
    /// 资源使用历史记录
    /// </summary>
    public List<ResourceUsageSnapshot> UsageHistory { get; set; } = new();
}

/// <summary>
/// 资源使用快照
/// </summary>
public class ResourceUsageSnapshot
{
    /// <summary>
    /// 快照时间
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// CPU使用率
    /// </summary>
    public double CpuUsage { get; set; }

    /// <summary>
    /// 内存使用率
    /// </summary>
    public double MemoryUsage { get; set; }

    /// <summary>
    /// 磁盘使用率
    /// </summary>
    public double DiskUsage { get; set; }

    /// <summary>
    /// 网络使用率
    /// </summary>
    public double NetworkUsage { get; set; }

    /// <summary>
    /// 当前执行数
    /// </summary>
    public int CurrentExecutions { get; set; }
}
