# FlowCustomV1 Web Frontend 构建脚本
# PowerShell 脚本用于 Windows 环境

param(
    [string]$Mode = "production",
    [switch]$Docker = $false,
    [switch]$Clean = $false
)

Write-Host "=== FlowCustomV1 Web Frontend 构建脚本 ===" -ForegroundColor Green
Write-Host "版本: v0.0.1.11" -ForegroundColor Cyan
Write-Host "构建模式: $Mode" -ForegroundColor Yellow
Write-Host ""

# 清理构建目录
if ($Clean) {
    Write-Host "清理构建目录..." -ForegroundColor Yellow
    if (Test-Path "dist") {
        Remove-Item -Recurse -Force "dist"
        Write-Host "已清理 dist 目录" -ForegroundColor Green
    }
    if (Test-Path "node_modules") {
        Remove-Item -Recurse -Force "node_modules"
        Write-Host "已清理 node_modules 目录" -ForegroundColor Green
    }
}

# 检查 Node.js 环境
Write-Host "检查构建环境..." -ForegroundColor Yellow
try {
    $nodeVersion = node --version
    Write-Host "Node.js 版本: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "错误: 未找到 Node.js" -ForegroundColor Red
    exit 1
}

# 安装依赖
if (!(Test-Path "node_modules") -or $Clean) {
    Write-Host "安装依赖..." -ForegroundColor Yellow
    npm ci
    if ($LASTEXITCODE -ne 0) {
        Write-Host "依赖安装失败！" -ForegroundColor Red
        exit 1
    }
}

# 类型检查
Write-Host "执行类型检查..." -ForegroundColor Yellow
npm run type-check
if ($LASTEXITCODE -ne 0) {
    Write-Host "类型检查失败！" -ForegroundColor Red
    exit 1
}

# 代码检查
Write-Host "执行代码检查..." -ForegroundColor Yellow
npm run lint
if ($LASTEXITCODE -ne 0) {
    Write-Host "代码检查失败！" -ForegroundColor Red
    exit 1
}

# 构建应用
Write-Host "构建应用..." -ForegroundColor Yellow
if ($Mode -eq "development") {
    $env:NODE_ENV = "development"
} else {
    $env:NODE_ENV = "production"
}

npm run build
if ($LASTEXITCODE -ne 0) {
    Write-Host "构建失败！" -ForegroundColor Red
    exit 1
}

Write-Host "构建完成！" -ForegroundColor Green

# 显示构建结果
if (Test-Path "dist") {
    $distSize = (Get-ChildItem -Recurse "dist" | Measure-Object -Property Length -Sum).Sum
    $distSizeMB = [math]::Round($distSize / 1MB, 2)
    Write-Host "构建产物大小: $distSizeMB MB" -ForegroundColor Cyan
    
    Write-Host "构建产物目录结构:" -ForegroundColor Cyan
    Get-ChildItem -Recurse "dist" | Select-Object Name, Length | Format-Table -AutoSize
}

# Docker 构建
if ($Docker) {
    Write-Host "构建 Docker 镜像..." -ForegroundColor Yellow
    
    $imageName = "flowcustomv1-web:v0.0.1.11"
    docker build -t $imageName .
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Docker 镜像构建成功: $imageName" -ForegroundColor Green
        
        # 显示镜像信息
        docker images $imageName
    } else {
        Write-Host "Docker 镜像构建失败！" -ForegroundColor Red
        exit 1
    }
}

Write-Host ""
Write-Host "构建完成！可以使用以下命令预览:" -ForegroundColor Green
Write-Host "npm run preview" -ForegroundColor Cyan
Write-Host ""
