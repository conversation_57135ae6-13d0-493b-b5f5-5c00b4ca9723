import React, { useEffect, useState } from 'react';
import { Tag, Progress, Button, Space, Input, Select, DatePicker, Statistic, Row, Col } from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  ReloadOutlined,
  EyeOutlined,
  SearchOutlined,
  FilterOutlined,
  MonitorOutlined
} from '@ant-design/icons';
import { ProCard, ProColumns, ProTable } from '@ant-design/pro-components';
import { executionApi } from '@/services/execution';
import type { WorkflowExecution } from '@/types/api';
import { ExecutionStatus } from '@/types/api';
import PageLayout from '@/components/Layout/PageLayout';

// ========== 页面布局配置 ==========
const PAGE_LAYOUT_CONFIG = {
  // 🎯 页面类型标识
  pageType: 'complex-with-stats',

  // 🟠 统计卡片容器配置
  hasStatsContainer: true,
  statsContainerHeight: 'var(--layout-stats-container-height)',
  statsContainerMargin: 'var(--layout-stats-container-margin)', // 下边距（与工具栏间距）
  statsContainerPadding: 'var(--layout-stats-container-padding)', // 内边距

  // 🟠 统计容器四周间距配置（移除蓝色容器后简化）
  statsContainerSpacing: {
    top: '0px',    // 上间距（无需间距）
    right: '0px',  // 右间距（无需间距）
    bottom: 'var(--layout-stats-container-margin)', // 下间距（与工具栏间距）
    left: '0px'    // 左间距（无需间距）
  },

  // 🟠 工具栏配置
  hasToolbar: true,
  toolbarHeight: 'var(--layout-toolbar-height)',
  toolbarMargin: 'var(--layout-toolbar-margin)',

  // 🟣 表格配置
  tablePaginationHeight: '20px', // 分页栏高度
  tableHasSelection: false, // 执行监控不需要行选择

  // 表格滚动高度 - 简化计算（移除容器后）
  tableScrollY: 'calc(100vh - var(--layout-header-height) - var(--layout-toolbar-height) - 150px)', // 简化计算：只考虑头部、工具栏和统计卡片

  // 📐 页面特定间距调整
  customSpacing: {
    extraOffset: 0, // 使用动态计算，不需要额外偏移
    compactMode: false
  }
};

const { Search } = Input;
const { Option } = Select;
const { RangePicker } = DatePicker;



const ExecutionMonitor: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [executions, setExecutions] = useState<WorkflowExecution[]>([]);
  const [stats, setStats] = useState({
    total: 0,
    running: 0,
    success: 0,
    failed: 0,
  });

  // 应用页面特定的布局配置
  useEffect(() => {
    // 应用自定义的分页栏高度
    document.documentElement.style.setProperty(
      '--layout-table-pagination-height',
      PAGE_LAYOUT_CONFIG.tablePaginationHeight
    );


    // 清理函数：组件卸载时恢复默认值
    return () => {
      document.documentElement.style.setProperty('--layout-table-pagination-height', '20px');
    };
  }, []);

  // 加载执行数据
  const loadExecutions = async () => {
    try {
      setLoading(true);
      const [executionData, statsData] = await Promise.all([
        executionApi.getAllExecutions({ pageIndex: 0, pageSize: 100 }),
        executionApi.getExecutionStats(),
      ]);

      setExecutions(Array.isArray(executionData) ? executionData : executionData.data || []);
      setStats({
        total: statsData.total,
        running: statsData.running || 0,
        success: statsData.success,
        failed: statsData.failed,
      });
    } catch (error) {
      console.error('加载执行数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadExecutions();
    // 设置定时刷新
    const interval = setInterval(loadExecutions, 5000);
    return () => clearInterval(interval);
  }, []);

  // 获取状态标签
  const getStatusTag = (status: ExecutionStatus) => {
    const statusMap = {
      [ExecutionStatus.Running]: { color: 'processing', text: '运行中' },
      [ExecutionStatus.Completed]: { color: 'success', text: '已完成' },
      [ExecutionStatus.Failed]: { color: 'error', text: '失败' },
      [ExecutionStatus.Pending]: { color: 'default', text: '等待中' },
      [ExecutionStatus.Cancelled]: { color: 'warning', text: '已取消' },
      [ExecutionStatus.Paused]: { color: 'warning', text: '已暂停' },
    };
    const config = statusMap[status] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 操作处理
  const handlePause = async (executionId: string) => {
    try {
      await executionApi.pauseExecution(executionId);
      loadExecutions();
    } catch (error) {
      console.error('暂停执行失败:', error);
    }
  };

  const handleResume = async (executionId: string) => {
    try {
      await executionApi.resumeExecution(executionId);
      loadExecutions();
    } catch (error) {
      console.error('恢复执行失败:', error);
    }
  };

  const handleStop = async (executionId: string) => {
    try {
      await executionApi.stopExecution(executionId);
      loadExecutions();
    } catch (error) {
      console.error('停止执行失败:', error);
    }
  };

  // 表格列定义
  const columns: ProColumns<WorkflowExecution>[] = [
    {
      title: '执行ID',
      dataIndex: 'executionId',
      key: 'executionId',
      width: 120,
      ellipsis: true,
      render: (_, record) => (
        <code className="text-xs bg-gray-100 px-1 rounded">
          {record.executionId.substring(0, 8)}...
        </code>
      ),
    },
    {
      title: '工作流名称',
      dataIndex: 'workflowName',
      key: 'workflowName',
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (_, record) => getStatusTag(record.status),
    },
    {
      title: '进度',
      dataIndex: 'progress',
      key: 'progress',
      width: 120,
      render: (_, record) => (
        <Progress
          percent={record.progress}
          size="small"
          status={record.progress === 100 ? 'success' : 'active'}
        />
      ),
    },
    {
      title: '当前节点',
      dataIndex: 'currentNodeId',
      key: 'currentNodeId',
      width: 120,
      ellipsis: true,
      render: (nodeId) => nodeId ? (
        <code className="text-xs bg-blue-100 px-1 rounded">
          {nodeId}
        </code>
      ) : '-',
    },
    {
      title: '开始时间',
      dataIndex: 'startedAt',
      key: 'startedAt',
      width: 160,
      render: (_, record) => new Date(record.startedAt).toLocaleString(),
    },
    {
      title: '持续时间',
      dataIndex: 'duration',
      key: 'duration',
      width: 100,
      render: (_, record) => {
        if (record.duration) {
          return `${Math.round(record.duration / 1000)}s`;
        }
        if (record.status === ExecutionStatus.Running) {
          const elapsed = Date.now() - new Date(record.startedAt).getTime();
          return `${Math.round(elapsed / 1000)}s`;
        }
        return '-';
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Button 
            type="text" 
            size="small" 
            icon={<EyeOutlined />}
            onClick={() => window.open(`/execution/detail/${record.executionId}`, '_blank')}
          >
            详情
          </Button>
          {record.status === ExecutionStatus.Running && (
            <>
              <Button 
                type="text" 
                size="small" 
                icon={<PauseCircleOutlined />}
                onClick={() => handlePause(record.executionId)}
              >
                暂停
              </Button>
              <Button 
                type="text" 
                size="small" 
                danger
                icon={<StopOutlined />}
                onClick={() => handleStop(record.executionId)}
              >
                停止
              </Button>
            </>
          )}
          {record.status === ExecutionStatus.Paused && (
            <Button 
              type="text" 
              size="small" 
              icon={<PlayCircleOutlined />}
              onClick={() => handleResume(record.executionId)}
            >
              恢复
            </Button>
          )}
        </Space>
      ),
    },
  ];

  return (
    <PageLayout
      title="执行监控"
      description="实时监控工作流执行状态"
      icon={<MonitorOutlined />}
      actions={
        <Button
          type="primary"
          icon={<ReloadOutlined />}
          loading={loading}
          onClick={loadExecutions}
        >
          刷新
        </Button>
      }
    >
      {/* 统计卡片 - 直接显示，无容器 */}
      {PAGE_LAYOUT_CONFIG.hasStatsContainer && (
        <Row gutter={[16, 16]} style={{ marginBottom: '16px' }}>
          <Col xs={24} sm={6}>
            <ProCard>
              <Statistic
                title="总执行数"
                value={stats.total}
                valueStyle={{ color: '#1890ff' }}
              />
            </ProCard>
          </Col>
          <Col xs={24} sm={6}>
            <ProCard>
              <Statistic
                title="运行中"
                value={stats.running}
                valueStyle={{ color: '#fa8c16' }}
              />
            </ProCard>
          </Col>
          <Col xs={24} sm={6}>
            <ProCard>
              <Statistic
                title="已完成"
                value={stats.success}
                valueStyle={{ color: '#52c41a' }}
              />
            </ProCard>
          </Col>
          <Col xs={24} sm={6}>
            <ProCard>
              <Statistic
                title="失败"
                value={stats.failed}
                valueStyle={{ color: '#ff4d4f' }}
              />
            </ProCard>
          </Col>
        </Row>
      )}

      {/* 筛选工具栏 - 直接显示，无容器 */}
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
        <Space>
          <Search
            placeholder="搜索工作流名称或执行ID"
            allowClear
            style={{ width: 300 }}
            prefix={<SearchOutlined />}
          />
          <Select
            placeholder="状态筛选"
            allowClear
            style={{ width: 120 }}
            suffixIcon={<FilterOutlined />}
          >
            <Option value="Running">运行中</Option>
            <Option value="Completed">已完成</Option>
            <Option value="Failed">失败</Option>
            <Option value="Pending">等待中</Option>
            <Option value="Cancelled">已取消</Option>
            <Option value="Paused">已暂停</Option>
          </Select>
          <RangePicker placeholder={['开始时间', '结束时间']} />
        </Space>
        <Space>
          <Button
            type="primary"
            icon={<ReloadOutlined />}
            onClick={loadExecutions}
            loading={loading}
          >
            刷新
          </Button>
        </Space>
      </div>

      {/* 执行列表 - 直接显示，无容器 */}
      <ProTable<WorkflowExecution>
          columns={columns}
          dataSource={executions}
          rowKey="executionId"
          loading={loading}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`,
          }}
          scroll={{
            x: 1200,
            y: PAGE_LAYOUT_CONFIG.tableScrollY
          }}
          search={false}
          toolBarRender={false}
          options={false}
          rowClassName={(record) => {
            if (record.status === ExecutionStatus.Running) return 'bg-blue-50';
            if (record.status === ExecutionStatus.Failed) return 'bg-red-50';
            return '';
          }}
        />
    </PageLayout>
  );
};

export default ExecutionMonitor;
