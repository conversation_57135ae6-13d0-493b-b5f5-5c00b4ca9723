using FlowCustomV1.Core.Models.Cluster;
using FlowCustomV1.Core.Models.Scheduling;

namespace FlowCustomV1.Core.Interfaces.Scheduling;

/// <summary>
/// 负载均衡策略接口
/// 定义不同负载均衡算法的统一接口
/// </summary>
public interface ILoadBalancingStrategy
{
    /// <summary>
    /// 策略名称
    /// </summary>
    string StrategyName { get; }

    /// <summary>
    /// 策略描述
    /// </summary>
    string Description { get; }

    /// <summary>
    /// 策略权重（用于加权选择）
    /// </summary>
    int Weight { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    bool IsEnabled { get; set; }

    /// <summary>
    /// 从候选节点中选择最佳节点
    /// </summary>
    /// <param name="candidateNodes">候选节点列表</param>
    /// <param name="taskRequirements">任务要求</param>
    /// <param name="context">选择上下文</param>
    /// <returns>选择的节点</returns>
    NodeInfo? SelectNode(
        IReadOnlyList<NodeInfo> candidateNodes,
        TaskRequirements taskRequirements,
        LoadBalancingContext context);

    /// <summary>
    /// 计算节点适合度评分
    /// </summary>
    /// <param name="node">节点信息</param>
    /// <param name="taskRequirements">任务要求</param>
    /// <param name="context">选择上下文</param>
    /// <returns>适合度评分（0-100）</returns>
    double CalculateNodeFitness(
        NodeInfo node,
        TaskRequirements taskRequirements,
        LoadBalancingContext context);

    /// <summary>
    /// 更新策略状态
    /// </summary>
    /// <param name="selectedNode">被选择的节点</param>
    /// <param name="taskResult">任务执行结果</param>
    void UpdateState(NodeInfo selectedNode, TaskExecutionResult? taskResult = null);

    /// <summary>
    /// 重置策略状态
    /// </summary>
    void ResetState();

    /// <summary>
    /// 获取策略统计信息
    /// </summary>
    /// <returns>策略统计</returns>
    LoadBalancingStrategyStats GetStatistics();
}

/// <summary>
/// 负载均衡上下文
/// 提供选择节点时需要的上下文信息
/// </summary>
public class LoadBalancingContext
{
    /// <summary>
    /// 任务ID
    /// </summary>
    public string TaskId { get; set; } = string.Empty;

    /// <summary>
    /// 任务类型
    /// </summary>
    public string TaskType { get; set; } = string.Empty;

    /// <summary>
    /// 任务优先级
    /// </summary>
    public int Priority { get; set; } = 5;

    /// <summary>
    /// 预期执行时间（毫秒）
    /// </summary>
    public long EstimatedExecutionTimeMs { get; set; }

    /// <summary>
    /// 历史选择记录
    /// </summary>
    public List<NodeSelectionRecord> SelectionHistory { get; set; } = new();

    /// <summary>
    /// 当前集群状态
    /// </summary>
    public ClusterState? ClusterState { get; set; }

    /// <summary>
    /// 负载均衡配置
    /// </summary>
    public Dictionary<string, object> Configuration { get; set; } = new();

    /// <summary>
    /// 上下文元数据
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// 节点选择记录
/// </summary>
public class NodeSelectionRecord
{
    /// <summary>
    /// 选择时间
    /// </summary>
    public DateTime SelectedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 选择的节点ID
    /// </summary>
    public string NodeId { get; set; } = string.Empty;

    /// <summary>
    /// 使用的策略
    /// </summary>
    public string Strategy { get; set; } = string.Empty;

    /// <summary>
    /// 选择评分
    /// </summary>
    public double Score { get; set; }

    /// <summary>
    /// 任务执行结果
    /// </summary>
    public TaskExecutionResult? ExecutionResult { get; set; }
}

/// <summary>
/// 任务执行结果
/// </summary>
public class TaskExecutionResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 执行时间（毫秒）
    /// </summary>
    public long ExecutionTimeMs { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 执行开始时间
    /// </summary>
    public DateTime StartTime { get; set; }

    /// <summary>
    /// 执行结束时间
    /// </summary>
    public DateTime EndTime { get; set; }

    /// <summary>
    /// 资源使用情况
    /// </summary>
    public ResourceUsage? ResourceUsage { get; set; }
}

/// <summary>
/// 资源使用情况
/// </summary>
public class ResourceUsage
{
    /// <summary>
    /// CPU使用率峰值
    /// </summary>
    public double PeakCpuUsage { get; set; }

    /// <summary>
    /// 内存使用量峰值（MB）
    /// </summary>
    public long PeakMemoryUsageMB { get; set; }

    /// <summary>
    /// 磁盘I/O总量（MB）
    /// </summary>
    public long TotalDiskIOMB { get; set; }

    /// <summary>
    /// 网络I/O总量（MB）
    /// </summary>
    public long TotalNetworkIOMB { get; set; }
}

/// <summary>
/// 集群状态
/// </summary>
public class ClusterState
{
    /// <summary>
    /// 总节点数
    /// </summary>
    public int TotalNodes { get; set; }

    /// <summary>
    /// 活跃节点数
    /// </summary>
    public int ActiveNodes { get; set; }

    /// <summary>
    /// 平均负载
    /// </summary>
    public double AverageLoad { get; set; }

    /// <summary>
    /// 负载标准差
    /// </summary>
    public double LoadStandardDeviation { get; set; }

    /// <summary>
    /// 总任务数
    /// </summary>
    public int TotalTasks { get; set; }

    /// <summary>
    /// 队列中的任务数
    /// </summary>
    public int QueuedTasks { get; set; }

    /// <summary>
    /// 集群健康状态
    /// </summary>
    public string HealthStatus { get; set; } = "Unknown";

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 负载均衡策略统计
/// </summary>
public class LoadBalancingStrategyStats
{
    /// <summary>
    /// 策略名称
    /// </summary>
    public string StrategyName { get; set; } = string.Empty;

    /// <summary>
    /// 使用次数
    /// </summary>
    public long UsageCount { get; set; }

    /// <summary>
    /// 成功次数
    /// </summary>
    public long SuccessCount { get; set; }

    /// <summary>
    /// 失败次数
    /// </summary>
    public long FailureCount { get; set; }

    /// <summary>
    /// 成功率
    /// </summary>
    public double SuccessRate => UsageCount > 0 ? (double)SuccessCount / UsageCount : 0;

    /// <summary>
    /// 平均选择时间（毫秒）
    /// </summary>
    public double AverageSelectionTimeMs { get; set; }

    /// <summary>
    /// 平均适合度评分
    /// </summary>
    public double AverageFitnessScore { get; set; }

    /// <summary>
    /// 最后使用时间
    /// </summary>
    public DateTime LastUsedAt { get; set; }

    /// <summary>
    /// 统计开始时间
    /// </summary>
    public DateTime StatsSince { get; set; } = DateTime.UtcNow;
}
