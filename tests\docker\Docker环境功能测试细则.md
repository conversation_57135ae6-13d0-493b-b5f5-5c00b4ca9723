# FlowCustomV1 Docker环境功能测试细则

## 📋 测试信息

| 测试信息 | 详细内容 |
|---------|---------|
| **测试环境** | Docker Compose 简单测试环境 |
| **测试版本** | v0.0.1.8 |
| **测试日期** | 2025-09-07 |
| **测试类型** | 功能测试、集成测试、配置验证 |
| **测试范围** | 基础设施、应用节点、节点通信、API功能 |

---

## 🎯 测试目标

1. **验证Docker环境稳定性**：确保所有容器正常启动和运行
2. **验证配置体系**：确保配置参数正确加载和生效
3. **验证节点通信**：确保Master-Worker节点发现和消息传递
4. **验证API功能**：确保所有API端点正常工作
5. **验证故障恢复**：确保容器重启和故障恢复机制

---

## 🏗️ 测试环境准备

### 前置条件检查

**系统要求**：
- [ ] Docker Desktop 已安装并运行
- [ ] Docker Compose 版本 ≥ 2.0
- [ ] 可用内存 ≥ 4GB
- [ ] 可用磁盘空间 ≥ 2GB

**端口可用性检查**：
```powershell
# 检查端口是否被占用
netstat -an | findstr ":4222"    # NATS端口
netstat -an | findstr ":8222"    # NATS监控端口
netstat -an | findstr ":3306"    # MySQL端口
netstat -an | findstr ":5001"    # Master节点端口
netstat -an | findstr ":5011"    # Worker节点端口
```

**环境清理**：
```bash
# 清理旧环境
cd tests/docker
docker-compose -f docker-compose.simple.yml down -v
docker system prune -f
```

### 测试环境启动

**步骤1：构建镜像**
```bash
cd tests/docker
docker-compose -f docker-compose.simple.yml build
```

**验证点**：
- [ ] 构建过程无错误
- [ ] 镜像构建成功
- [ ] 构建时间合理（< 5分钟）

**步骤2：启动基础设施**
```bash
docker-compose -f docker-compose.simple.yml up -d nats mysql
```

**验证点**：
- [ ] NATS容器启动成功
- [ ] MySQL容器启动成功
- [ ] 容器健康检查通过

**步骤3：启动应用节点**
```bash
# 等待基础设施就绪
Start-Sleep -Seconds 30

# 启动应用节点
docker-compose -f docker-compose.simple.yml up -d master-node worker-node
```

**验证点**：
- [ ] Master节点容器启动成功
- [ ] Worker节点容器启动成功
- [ ] 应用启动日志正常

---

## 🧪 功能测试细则

### 测试1：基础设施服务验证

**测试目标**：验证NATS和MySQL服务正常运行

**测试步骤**：

1. **NATS服务验证**
```bash
# 检查NATS容器状态
docker ps | grep nats

# 检查NATS健康状态
curl -s http://localhost:8222/healthz

# 检查NATS连接信息
curl -s http://localhost:8222/connz
```

**预期结果**：
- [ ] NATS容器状态为 "Up (healthy)"
- [ ] 健康检查返回 `{"status":"ok"}`
- [ ] 连接信息显示正常

2. **MySQL服务验证**
```bash
# 检查MySQL容器状态
docker ps | grep mysql

# 测试MySQL连接
docker exec flowcustom-test-mysql mysql -uflowcustom -pTestPassword123! -e "SELECT 1;"
```

**预期结果**：
- [ ] MySQL容器状态为 "Up (healthy)"
- [ ] 数据库连接成功
- [ ] 查询返回结果 "1"

### 测试2：应用节点启动验证

**测试目标**：验证Master和Worker节点正常启动

**测试步骤**：

1. **容器状态检查**
```bash
# 检查所有容器状态
docker ps

# 检查应用容器日志
docker logs flowcustom-test-master --tail 20
docker logs flowcustom-test-worker --tail 20
```

**预期结果**：
- [ ] Master节点容器状态为 "Up (healthy)"
- [ ] Worker节点容器状态为 "Up (healthy)"
- [ ] 日志显示 "Application started"
- [ ] 无错误或异常日志

2. **应用启动验证**
```bash
# 检查应用进程
docker exec flowcustom-test-master ps aux | grep dotnet
docker exec flowcustom-test-worker ps aux | grep dotnet
```

**预期结果**：
- [ ] dotnet进程正在运行
- [ ] 进程ID存在且稳定
- [ ] 内存使用合理

### 测试3：配置体系验证

**测试目标**：验证配置参数正确加载和生效

**测试步骤**：

1. **环境变量验证**
```bash
# 检查Master节点环境变量
docker exec flowcustom-test-master env | grep -E "(Nats|Database|NodeDiscovery)"

# 检查Worker节点环境变量
docker exec flowcustom-test-worker env | grep -E "(Nats|Database|NodeDiscovery)"
```

**预期结果**：
- [ ] `Nats__Servers__0=nats://nats:4222`
- [ ] `Database__ConnectionString` 包含正确的连接信息
- [ ] `NodeDiscovery__NodeId` 设置正确
- [ ] `NodeDiscovery__NodeRole` 设置正确

2. **配置文件验证**
```bash
# 检查配置文件存在
docker exec flowcustom-test-master ls -la /app/*.json

# 检查配置文件内容
docker exec flowcustom-test-master cat /app/appsettings.Test.json | head -20
```

**预期结果**：
- [ ] `appsettings.Test.json` 文件存在
- [ ] 配置文件格式正确
- [ ] NATS服务器配置为 `nats://nats:4222`
- [ ] 无硬编码的localhost地址

### 测试4：节点发现和通信验证

**测试目标**：验证Master-Worker节点发现和心跳机制

**测试步骤**：

1. **节点发现验证**
```bash
# 检查节点发现日志
docker logs flowcustom-test-master | grep -i "node.*register"
docker logs flowcustom-test-worker | grep -i "node.*register"
```

**预期结果**：
- [ ] Master节点成功注册
- [ ] Worker节点成功注册
- [ ] 节点ID和角色信息正确

2. **心跳机制验证**
```bash
# 检查心跳日志
docker logs flowcustom-test-master | grep -i "heartbeat" | tail -5
docker logs flowcustom-test-worker | grep -i "heartbeat" | tail -5
```

**预期结果**：
- [ ] Master节点发送心跳
- [ ] Worker节点发送心跳
- [ ] 节点间互相接收心跳
- [ ] 心跳间隔符合配置（10秒）

3. **消息传递验证**
```bash
# 检查消息发布日志
docker logs flowcustom-test-master | grep -i "message.*publish" | tail -5
docker logs flowcustom-test-worker | grep -i "message.*publish" | tail -5
```

**预期结果**：
- [ ] 消息发布成功
- [ ] 消息订阅正常
- [ ] 无消息传递错误

### 测试5：API功能验证

**测试目标**：验证API端点正常工作

**测试步骤**：

1. **健康检查API**
```bash
# 测试Master节点健康检查
curl -s http://localhost:5001/health || echo "Health check failed"

# 测试Worker节点健康检查
curl -s http://localhost:5011/health || echo "Health check failed"
```

**预期结果**：
- [ ] 返回HTTP 200状态码
- [ ] 响应包含健康状态信息
- [ ] 响应时间 < 1秒

2. **节点信息API**
```bash
# 获取节点列表
curl -s http://localhost:5001/api/nodes || echo "Nodes API failed"

# 获取集群状态
curl -s http://localhost:5001/api/cluster/status || echo "Cluster API failed"
```

**预期结果**：
- [ ] 返回节点列表信息
- [ ] 包含Master和Worker节点
- [ ] 节点状态为活跃

3. **工作流API**
```bash
# 测试工作流创建
curl -X POST http://localhost:5001/api/workflows \
  -H "Content-Type: application/json" \
  -d '{"name":"test-workflow","description":"Test workflow"}' || echo "Workflow creation failed"
```

**预期结果**：
- [ ] 工作流创建成功
- [ ] 返回工作流ID
- [ ] 数据库记录正确

### 测试6：故障恢复验证

**测试目标**：验证容器重启和故障恢复机制

**测试步骤**：

1. **容器重启测试**
```bash
# 重启Worker节点
docker restart flowcustom-test-worker

# 等待重启完成
Start-Sleep -Seconds 30

# 检查重启后状态
docker ps
docker logs flowcustom-test-worker --tail 10
```

**预期结果**：
- [ ] 容器成功重启
- [ ] 应用重新连接到NATS和MySQL
- [ ] 节点重新注册到集群
- [ ] 心跳机制恢复正常

2. **网络中断恢复测试**
```bash
# 模拟网络中断（断开NATS连接）
docker network disconnect docker_flowcustom-test flowcustom-test-worker

# 等待一段时间
Start-Sleep -Seconds 15

# 恢复网络连接
docker network connect docker_flowcustom-test flowcustom-test-worker

# 检查恢复状态
docker logs flowcustom-test-worker --tail 10
```

**预期结果**：
- [ ] 检测到连接中断
- [ ] 自动重连机制启动
- [ ] 连接恢复后正常工作
- [ ] 节点状态同步正常

### 测试7：性能和资源验证

**测试目标**：验证系统性能和资源使用

**测试步骤**：

1. **资源使用检查**
```bash
# 检查容器资源使用
docker stats --no-stream

# 检查内存使用
docker exec flowcustom-test-master free -h
docker exec flowcustom-test-worker free -h
```

**预期结果**：
- [ ] CPU使用率 < 50%
- [ ] 内存使用 < 1GB per container
- [ ] 无内存泄漏迹象

2. **响应时间测试**
```bash
# 测试API响应时间
for i in {1..10}; do
  time curl -s http://localhost:5001/health > /dev/null
done
```

**预期结果**：
- [ ] 平均响应时间 < 100ms
- [ ] 99%请求响应时间 < 500ms
- [ ] 无超时错误

---

## 📊 测试报告模板

### 测试执行记录

**测试环境信息**：
- 测试日期：[填写日期]
- 测试人员：[填写姓名]
- Docker版本：[填写版本]
- 系统环境：[填写系统信息]

**测试结果汇总**：

| 测试项目 | 测试结果 | 备注 |
|---------|---------|------|
| 基础设施服务 | ✅/❌ | |
| 应用节点启动 | ✅/❌ | |
| 配置体系 | ✅/❌ | |
| 节点通信 | ✅/❌ | |
| API功能 | ✅/❌ | |
| 故障恢复 | ✅/❌ | |
| 性能资源 | ✅/❌ | |

**问题记录**：
1. [问题描述] - [解决方案]
2. [问题描述] - [解决方案]

**改进建议**：
1. [建议内容]
2. [建议内容]

---

## 🔧 故障排查指南

### 常见问题及解决方案

**问题1：容器启动失败**
```bash
# 检查容器日志
docker logs [container_name]

# 检查端口占用
netstat -an | findstr ":[port]"

# 重新构建镜像
docker-compose build --no-cache
```

**问题2：节点无法通信**
```bash
# 检查网络连接
docker network ls
docker network inspect docker_flowcustom-test

# 测试服务连通性
docker exec flowcustom-test-master nc -zv nats 4222
docker exec flowcustom-test-master nc -zv mysql 3306
```

**问题3：配置不生效**
```bash
# 检查环境变量
docker exec [container_name] env | grep -E "(Nats|Database)"

# 检查配置文件
docker exec [container_name] cat /app/appsettings.Test.json
```

---

**此测试细则确保FlowCustomV1 Docker环境的全面功能验证，为系统稳定性和可靠性提供保障。**
