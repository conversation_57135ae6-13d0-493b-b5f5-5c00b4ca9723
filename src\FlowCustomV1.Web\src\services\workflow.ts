import { request, requestWithPagination } from './api';
import type {
  WorkflowDefinition,
  WorkflowExecution,
  ValidationResult,
  PaginationParams,
  PagedResponse,
  PublishWorkflowRequest,
  PublishWorkflowResponse
} from '@/types/api';

// 工作流管理 API
export const workflowApi = {
  // 获取所有工作流
  getWorkflows: async (params?: {
    search?: string;
    status?: string;
    createdAfter?: string;
    createdBefore?: string;
    page?: number;
    pageSize?: number;
  }): Promise<PagedResponse<WorkflowDefinition>> => {
    const response = await request.get('/workflows', { params });
    // 处理不同的响应格式
    console.log('workflow.ts - 原始响应:', response);
    console.log('workflow.ts - 检查条件:', {
      hasResponseData: !!response,
      hasTotalCount: response?.totalCount !== undefined,
      totalCountValue: response?.totalCount,
      hasDataField: 'data' in (response || {}),
      dataFieldIsArray: Array.isArray(response?.data),
      dataFieldValue: response?.data
    });

    // 直接检查API返回的分页格式
    if (response &&
        typeof response === 'object' &&
        'totalCount' in response &&
        'data' in response &&
        Array.isArray(response.data)) {

      console.log('workflow.ts - 使用分页格式处理，totalCount:', response.totalCount);
      const result = {
        data: response.data,
        totalCount: response.totalCount,
        page: response.page || 1,
        pageSize: response.pageSize || 10,
        totalPages: response.totalPages || 1
      };
      console.log('workflow.ts - 返回结果:', result);
      return result;
    } else if (Array.isArray(response.data)) {
      // 如果直接返回数组
      console.log('workflow.ts - 使用数组格式处理');
      return {
        data: response,
        totalCount: response.length,
        page: params?.page || 1,
        pageSize: params?.pageSize || response.length,
        totalPages: 1
      };
    } else {
      // 默认空数据
      return {
        data: [],
        totalCount: 0,
        page: 1,
        pageSize: 10,
        totalPages: 0
      };
    }
  },

  // 获取工作流详情
  getWorkflow: async (id: string): Promise<WorkflowDefinition> => {
    const response = await request.get(`/workflows/${id}`);
    // API直接返回工作流对象，不是包装在data中
    return response;
  },

  // 创建工作流
  createWorkflow: async (workflow: Partial<WorkflowDefinition>): Promise<WorkflowDefinition> => {
    const response = await request.post('/workflows', workflow);
    // API直接返回工作流对象，不是包装在data中
    return response;
  },

  // 更新工作流
  updateWorkflow: (id: string, workflow: Partial<WorkflowDefinition>): Promise<void> =>
    request.put(`/workflows/${id}`, workflow),

  // 删除工作流
  deleteWorkflow: (id: string): Promise<void> =>
    request.delete(`/workflows/${id}`),

  // 验证工作流
  validateWorkflow: async (workflow: WorkflowDefinition): Promise<ValidationResult> => {
    try {
      const response = await request.post('/workflows/validate', workflow);
      return response;
    } catch (error: any) {
      // 如果验证失败，返回错误信息
      if (error?.response?.data) {
        const errorData = error.response.data;
        return {
          isValid: false,
          errors: errorData.Errors || [{ errorCode: 'VALIDATION_FAILED', message: errorData.Message || '验证失败', severity: 'Error' }],
          warnings: errorData.Warnings || [],
          validatedAt: new Date().toISOString(),
          validationDuration: 0
        };
      }
      throw error;
    }
  },

  // 复制工作流
  copyWorkflow: async (id: string, copyRequest?: { name?: string; description?: string }): Promise<WorkflowDefinition> => {
    const response = await request.post(`/workflows/${id}/copy`, copyRequest || {});
    return response.data;
  },

  // 导出工作流
  exportWorkflow: (id: string): Promise<Blob> =>
    request.get(`/workflows/${id}/export`, { responseType: 'blob' }),

  // 导入工作流
  importWorkflow: (file: File): Promise<WorkflowDefinition> => {
    const formData = new FormData();
    formData.append('file', file);
    return request.post('/workflows/import', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
  },

  // 发布工作流
  publishWorkflow: (publishRequest: PublishWorkflowRequest): Promise<PublishWorkflowResponse> =>
    request.post(`/workflows/${publishRequest.workflowId}/publish`, publishRequest),

  // 取消发布工作流
  unpublishWorkflow: (workflowId: string): Promise<void> =>
    request.post(`/workflows/${workflowId}/unpublish`),

  // 获取发布历史
  getPublishHistory: (workflowId: string): Promise<Array<{
    version: string;
    publishedAt: string;
    publishedBy: string;
    releaseNotes?: string;
    environments: string[];
  }>> =>
    request.get(`/workflows/${workflowId}/publish-history`),

  // 回滚到指定版本
  rollbackToVersion: (workflowId: string, version: string): Promise<void> =>
    request.post(`/workflows/${workflowId}/rollback`, { version }),
};

// 工作流执行 API
export const executionApi = {
  // 启动工作流执行
  startExecution: (workflowId: string, inputData?: Record<string, any>): Promise<WorkflowExecution> =>
    request.post(`/executions/start/${workflowId}`, { inputData }),

  // 获取执行详情
  getExecution: (executionId: string): Promise<WorkflowExecution> =>
    request.get(`/executions/${executionId}`),

  // 获取工作流执行历史
  getExecutionHistory: (
    workflowId: string, 
    params?: PaginationParams
  ): Promise<PagedResponse<WorkflowExecution>> =>
    requestWithPagination.get(`/executions/workflow/${workflowId}`, params),

  // 获取所有执行记录
  getAllExecutions: (params?: PaginationParams): Promise<PagedResponse<WorkflowExecution>> =>
    requestWithPagination.get('/executions', params),

  // 停止执行
  stopExecution: (executionId: string): Promise<void> =>
    request.post(`/executions/${executionId}/stop`),

  // 暂停执行
  pauseExecution: (executionId: string): Promise<void> =>
    request.post(`/executions/${executionId}/pause`),

  // 恢复执行
  resumeExecution: (executionId: string): Promise<void> =>
    request.post(`/executions/${executionId}/resume`),

  // 重试执行
  retryExecution: (executionId: string): Promise<WorkflowExecution> =>
    request.post(`/executions/${executionId}/retry`),

  // 获取执行日志
  getExecutionLogs: (executionId: string): Promise<string[]> =>
    request.get(`/executions/${executionId}/logs`),

  // 获取执行统计
  getExecutionStats: (workflowId?: string): Promise<{
    total: number;
    running: number;
    completed: number;
    failed: number;
    averageDuration: number;
  }> =>
    request.get('/executions/stats', { params: { workflowId } }),

};

// 工作流模板 API
export const templateApi = {
  // 获取模板列表
  getTemplates: (): Promise<WorkflowDefinition[]> =>
    request.get('/templates'),

  // 从模板创建工作流
  createFromTemplate: (templateId: string, name: string): Promise<WorkflowDefinition> =>
    request.post(`/templates/${templateId}/create`, { name }),

  // 保存为模板
  saveAsTemplate: (workflowId: string, templateName: string): Promise<void> =>
    request.post(`/workflows/${workflowId}/save-as-template`, { name: templateName }),
};
