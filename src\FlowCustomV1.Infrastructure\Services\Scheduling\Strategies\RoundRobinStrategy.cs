using System.Collections.Concurrent;
using FlowCustomV1.Core.Interfaces.Scheduling;
using FlowCustomV1.Core.Models.Cluster;
using FlowCustomV1.Core.Models.Scheduling;

namespace FlowCustomV1.Infrastructure.Services.Scheduling.Strategies;

/// <summary>
/// 轮询负载均衡策略
/// 按顺序轮流选择节点，确保负载均匀分布
/// </summary>
public class RoundRobinStrategy : ILoadBalancingStrategy
{
    private readonly ConcurrentDictionary<string, int> _counters = new();
    private readonly LoadBalancingStrategyStats _stats = new() { StrategyName = "RoundRobin" };
    private readonly object _lockObject = new();

    /// <inheritdoc />
    public string StrategyName => "RoundRobin";

    /// <inheritdoc />
    public string Description => "按顺序轮流选择节点，确保负载均匀分布";

    /// <inheritdoc />
    public int Weight { get; set; } = 20;

    /// <inheritdoc />
    public bool IsEnabled { get; set; } = true;

    /// <inheritdoc />
    public NodeInfo? SelectNode(
        IReadOnlyList<NodeInfo> candidateNodes,
        TaskRequirements taskRequirements,
        LoadBalancingContext context)
    {
        if (candidateNodes.Count == 0)
            return null;

        var startTime = DateTime.UtcNow;

        try
        {
            // 为了确保轮询的一致性，使用节点ID排序
            var sortedNodes = candidateNodes.OrderBy(n => n.NodeId).ToList();
            var key = string.Join(",", sortedNodes.Select(n => n.NodeId));

            NodeInfo selectedNode;
            lock (_lockObject)
            {
                var counter = _counters.AddOrUpdate(key, 0, (k, v) => (v + 1) % sortedNodes.Count);
                selectedNode = sortedNodes[counter];
            }

            // 更新统计
            _stats.UsageCount++;
            _stats.LastUsedAt = DateTime.UtcNow;
            _stats.AverageSelectionTimeMs = UpdateAverage(_stats.AverageSelectionTimeMs, 
                (DateTime.UtcNow - startTime).TotalMilliseconds, _stats.UsageCount);

            return selectedNode;
        }
        catch (Exception)
        {
            // 发生异常时返回第一个节点
            return candidateNodes.First();
        }
    }

    /// <inheritdoc />
    public double CalculateNodeFitness(
        NodeInfo node,
        TaskRequirements taskRequirements,
        LoadBalancingContext context)
    {
        // 轮询策略不基于节点适合度，所有节点评分相等
        return 50.0;
    }

    /// <inheritdoc />
    public void UpdateState(NodeInfo selectedNode, TaskExecutionResult? taskResult = null)
    {
        if (taskResult != null)
        {
            if (taskResult.IsSuccess)
            {
                _stats.SuccessCount++;
            }
            else
            {
                _stats.FailureCount++;
            }
        }
    }

    /// <inheritdoc />
    public void ResetState()
    {
        lock (_lockObject)
        {
            _counters.Clear();
            _stats.UsageCount = 0;
            _stats.SuccessCount = 0;
            _stats.FailureCount = 0;
            _stats.AverageSelectionTimeMs = 0;
            _stats.AverageFitnessScore = 0;
            _stats.StatsSince = DateTime.UtcNow;
        }
    }

    /// <inheritdoc />
    public LoadBalancingStrategyStats GetStatistics()
    {
        return new LoadBalancingStrategyStats
        {
            StrategyName = _stats.StrategyName,
            UsageCount = _stats.UsageCount,
            SuccessCount = _stats.SuccessCount,
            FailureCount = _stats.FailureCount,
            AverageSelectionTimeMs = _stats.AverageSelectionTimeMs,
            AverageFitnessScore = _stats.AverageFitnessScore,
            LastUsedAt = _stats.LastUsedAt,
            StatsSince = _stats.StatsSince
        };
    }

    /// <summary>
    /// 更新平均值
    /// </summary>
    private double UpdateAverage(double currentAverage, double newValue, long count)
    {
        if (count <= 1) return newValue;
        return (currentAverage * (count - 1) + newValue) / count;
    }
}

/// <summary>
/// 最少连接负载均衡策略
/// 选择当前活跃任务数最少的节点
/// </summary>
public class LeastConnectionsStrategy : ILoadBalancingStrategy
{
    private readonly LoadBalancingStrategyStats _stats = new() { StrategyName = "LeastConnections" };

    /// <inheritdoc />
    public string StrategyName => "LeastConnections";

    /// <inheritdoc />
    public string Description => "选择当前活跃任务数最少的节点";

    /// <inheritdoc />
    public int Weight { get; set; } = 30;

    /// <inheritdoc />
    public bool IsEnabled { get; set; } = true;

    /// <inheritdoc />
    public NodeInfo? SelectNode(
        IReadOnlyList<NodeInfo> candidateNodes,
        TaskRequirements taskRequirements,
        LoadBalancingContext context)
    {
        if (candidateNodes.Count == 0)
            return null;

        var startTime = DateTime.UtcNow;

        try
        {
            // 选择活跃任务数最少的节点
            var selectedNode = candidateNodes
                .OrderBy(n => n.Load.ActiveTaskCount)
                .ThenBy(n => n.Load.LoadScore) // 次要排序条件
                .First();

            // 更新统计
            _stats.UsageCount++;
            _stats.LastUsedAt = DateTime.UtcNow;
            _stats.AverageSelectionTimeMs = UpdateAverage(_stats.AverageSelectionTimeMs,
                (DateTime.UtcNow - startTime).TotalMilliseconds, _stats.UsageCount);

            var fitness = CalculateNodeFitness(selectedNode, taskRequirements, context);
            _stats.AverageFitnessScore = UpdateAverage(_stats.AverageFitnessScore, fitness, _stats.UsageCount);

            return selectedNode;
        }
        catch (Exception)
        {
            return candidateNodes.First();
        }
    }

    /// <inheritdoc />
    public double CalculateNodeFitness(
        NodeInfo node,
        TaskRequirements taskRequirements,
        LoadBalancingContext context)
    {
        // 基于活跃任务数和最大容量计算适合度
        if (node.Load.MaxTaskCapacity <= 0)
            return 0;

        var utilizationRate = (double)node.Load.ActiveTaskCount / node.Load.MaxTaskCapacity;
        return Math.Max(0, 100 - utilizationRate * 100);
    }

    /// <inheritdoc />
    public void UpdateState(NodeInfo selectedNode, TaskExecutionResult? taskResult = null)
    {
        if (taskResult != null)
        {
            if (taskResult.IsSuccess)
            {
                _stats.SuccessCount++;
            }
            else
            {
                _stats.FailureCount++;
            }
        }
    }

    /// <inheritdoc />
    public void ResetState()
    {
        _stats.UsageCount = 0;
        _stats.SuccessCount = 0;
        _stats.FailureCount = 0;
        _stats.AverageSelectionTimeMs = 0;
        _stats.AverageFitnessScore = 0;
        _stats.StatsSince = DateTime.UtcNow;
    }

    /// <inheritdoc />
    public LoadBalancingStrategyStats GetStatistics()
    {
        return new LoadBalancingStrategyStats
        {
            StrategyName = _stats.StrategyName,
            UsageCount = _stats.UsageCount,
            SuccessCount = _stats.SuccessCount,
            FailureCount = _stats.FailureCount,
            AverageSelectionTimeMs = _stats.AverageSelectionTimeMs,
            AverageFitnessScore = _stats.AverageFitnessScore,
            LastUsedAt = _stats.LastUsedAt,
            StatsSince = _stats.StatsSince
        };
    }

    /// <summary>
    /// 更新平均值
    /// </summary>
    private double UpdateAverage(double currentAverage, double newValue, long count)
    {
        if (count <= 1) return newValue;
        return (currentAverage * (count - 1) + newValue) / count;
    }
}

/// <summary>
/// 最低负载策略
/// 选择负载评分最低的节点
/// </summary>
public class LeastLoadStrategy : ILoadBalancingStrategy
{
    private readonly LoadBalancingStrategyStats _stats = new() { StrategyName = "LeastLoad" };

    /// <inheritdoc />
    public string StrategyName => "LeastLoad";

    /// <inheritdoc />
    public string Description => "选择负载评分最低的节点";

    /// <inheritdoc />
    public int Weight { get; set; } = 40;

    /// <inheritdoc />
    public bool IsEnabled { get; set; } = true;

    /// <inheritdoc />
    public NodeInfo? SelectNode(
        IReadOnlyList<NodeInfo> candidateNodes,
        TaskRequirements taskRequirements,
        LoadBalancingContext context)
    {
        if (candidateNodes.Count == 0)
            return null;

        var startTime = DateTime.UtcNow;

        try
        {
            // 选择负载评分最低的节点
            var selectedNode = candidateNodes
                .OrderBy(n => n.Load.LoadScore)
                .ThenBy(n => n.Load.CpuUsagePercentage) // 次要排序条件
                .First();

            // 更新统计
            _stats.UsageCount++;
            _stats.LastUsedAt = DateTime.UtcNow;
            _stats.AverageSelectionTimeMs = UpdateAverage(_stats.AverageSelectionTimeMs,
                (DateTime.UtcNow - startTime).TotalMilliseconds, _stats.UsageCount);

            var fitness = CalculateNodeFitness(selectedNode, taskRequirements, context);
            _stats.AverageFitnessScore = UpdateAverage(_stats.AverageFitnessScore, fitness, _stats.UsageCount);

            return selectedNode;
        }
        catch (Exception)
        {
            return candidateNodes.First();
        }
    }

    /// <inheritdoc />
    public double CalculateNodeFitness(
        NodeInfo node,
        TaskRequirements taskRequirements,
        LoadBalancingContext context)
    {
        // 负载评分越低，适合度越高
        return Math.Max(0, 100 - node.Load.LoadScore);
    }

    /// <inheritdoc />
    public void UpdateState(NodeInfo selectedNode, TaskExecutionResult? taskResult = null)
    {
        if (taskResult != null)
        {
            if (taskResult.IsSuccess)
            {
                _stats.SuccessCount++;
            }
            else
            {
                _stats.FailureCount++;
            }
        }
    }

    /// <inheritdoc />
    public void ResetState()
    {
        _stats.UsageCount = 0;
        _stats.SuccessCount = 0;
        _stats.FailureCount = 0;
        _stats.AverageSelectionTimeMs = 0;
        _stats.AverageFitnessScore = 0;
        _stats.StatsSince = DateTime.UtcNow;
    }

    /// <inheritdoc />
    public LoadBalancingStrategyStats GetStatistics()
    {
        return new LoadBalancingStrategyStats
        {
            StrategyName = _stats.StrategyName,
            UsageCount = _stats.UsageCount,
            SuccessCount = _stats.SuccessCount,
            FailureCount = _stats.FailureCount,
            AverageSelectionTimeMs = _stats.AverageSelectionTimeMs,
            AverageFitnessScore = _stats.AverageFitnessScore,
            LastUsedAt = _stats.LastUsedAt,
            StatsSince = _stats.StatsSince
        };
    }

    /// <summary>
    /// 更新平均值
    /// </summary>
    private double UpdateAverage(double currentAverage, double newValue, long count)
    {
        if (count <= 1) return newValue;
        return (currentAverage * (count - 1) + newValue) / count;
    }
}
