#!/usr/bin/env python3
"""
工作流测试数据生成脚本（修复版）
清空数据库并生成50个测试工作流，覆盖所有16种节点类型
使用正确的后端API数据格式
"""

import json
import requests
import time
import random
from typing import List, Dict, Any
from datetime import datetime

# API基础URL
API_BASE_URL = "http://localhost:5000/api"

# 16种节点类型定义
NODE_TYPES = [
    {"type": "start", "name": "开始", "category": "基础控制"},
    {"type": "end", "name": "结束", "category": "基础控制"},
    {"type": "task", "name": "任务", "category": "基础控制"},
    {"type": "decision", "name": "决策", "category": "流程控制"},
    {"type": "parallel", "name": "并行", "category": "流程控制"},
    {"type": "merge", "name": "合并", "category": "流程控制"},
    {"type": "loop", "name": "循环", "category": "流程控制"},
    {"type": "delay", "name": "延时", "category": "流程控制"},
    {"type": "http", "name": "HTTP请求", "category": "外部集成"},
    {"type": "database", "name": "数据库", "category": "数据处理"},
    {"type": "email", "name": "邮件", "category": "外部集成"},
    {"type": "file", "name": "文件", "category": "数据处理"},
    {"type": "script", "name": "脚本", "category": "数据处理"},
    {"type": "webhook", "name": "Webhook", "category": "外部集成"},
    {"type": "timer", "name": "定时器", "category": "触发器"},
    {"type": "condition", "name": "条件", "category": "流程控制"}
]

def clear_database():
    """清空数据库中的测试数据"""
    print("🧹 清空数据库测试数据...")
    
    try:
        # 删除所有工作流定义
        response = requests.get(f"{API_BASE_URL}/workflows")
        if response.status_code == 200:
            workflows = response.json()
            # 处理不同的响应格式
            if isinstance(workflows, dict) and 'data' in workflows:
                workflows = workflows['data']
            elif not isinstance(workflows, list):
                workflows = []
                
            for workflow in workflows:
                workflow_id = workflow.get('workflowId') or workflow.get('id')
                workflow_name = workflow.get('name', 'Unknown')
                if workflow_id:
                    delete_response = requests.delete(f"{API_BASE_URL}/workflows/{workflow_id}")
                    if delete_response.status_code in [200, 204]:
                        print(f"✅ 删除工作流: {workflow_name}")
                    else:
                        print(f"⚠️ 删除工作流失败: {workflow_name}")
        
        print("✅ 数据库清理完成")
        return True
    except Exception as e:
        print(f"❌ 清理数据库失败: {e}")
        return False

def create_workflow_node(node_id: str, node_type: str, name: str, description: str, position: Dict[str, int], config: Dict = None) -> Dict[str, Any]:
    """创建工作流节点（后端格式）"""
    return {
        "nodeId": node_id,
        "name": name,
        "description": description,
        "nodeType": node_type,
        "category": "Process",
        "version": "1.0.0",
        "configuration": {
            "parameters": config or {},
            "inputMappings": {},
            "outputMappings": {},
            "validationRules": [],
            "customProperties": {}
        },
        "position": {
            "x": position["x"],
            "y": position["y"],
            "z": 0
        },
        "isEnabled": True,
        "isCritical": False,
        "timeoutMinutes": 30,
        "retryStrategy": {
            "maxRetries": 3,
            "retryDelay": 1000,
            "backoffMultiplier": 2.0,
            "maxRetryDelay": 30000,
            "retryOnExceptions": []
        },
        "tags": [],
        "metadata": {},
        "priority": 0
    }

def create_workflow_connection(connection_id: str, source_node_id: str, target_node_id: str) -> Dict[str, Any]:
    """创建工作流连接（后端格式）"""
    return {
        "connectionId": connection_id,
        "sourceNodeId": source_node_id,
        "targetNodeId": target_node_id,
        "sourcePort": "default",
        "targetPort": "default",
        "name": f"连接_{source_node_id}_到_{target_node_id}",
        "description": "",
        "conditionType": "Always",
        "conditionExpression": "",
        "priority": 0,
        "isEnabled": True,
        "dataTransform": {
            "enableTransform": False,
            "fieldMappings": {},
            "dataFilters": [],
            "transformScript": "",
            "transformType": "None"
        },
        "tags": [],
        "metadata": {}
    }

def generate_node_config(node_type: str) -> Dict[str, Any]:
    """根据节点类型生成配置"""
    configs = {
        "http": {
            "url": "https://api.example.com/data",
            "method": "GET",
            "headers": {"Content-Type": "application/json"},
            "timeout": 30
        },
        "database": {
            "connectionString": "Server=localhost;Database=TestDB;",
            "query": "SELECT * FROM users WHERE active = 1",
            "timeout": 60
        },
        "email": {
            "to": "<EMAIL>",
            "subject": "测试邮件",
            "template": "这是一封测试邮件",
            "smtpServer": "smtp.example.com"
        },
        "file": {
            "filePath": "/data/input.txt",
            "operation": "read",
            "encoding": "utf-8"
        },
        "script": {
            "language": "python",
            "code": "print('Hello World')",
            "timeout": 300
        },
        "webhook": {
            "url": "https://webhook.example.com/notify",
            "method": "POST",
            "headers": {"Authorization": "Bearer token123"}
        },
        "timer": {
            "interval": "0 0 * * *",
            "timezone": "Asia/Shanghai"
        },
        "delay": {
            "duration": 5000,
            "unit": "milliseconds"
        },
        "condition": {
            "expression": "data.value > 100",
            "trueAction": "continue",
            "falseAction": "skip"
        },
        "decision": {
            "conditions": [
                {"expression": "data.status == 'approved'", "target": "approve_path"},
                {"expression": "data.status == 'rejected'", "target": "reject_path"}
            ]
        },
        "parallel": {
            "maxConcurrency": 3,
            "waitForAll": True
        },
        "loop": {
            "condition": "data.count < 10",
            "maxIterations": 100
        }
    }
    return configs.get(node_type, {})

def create_workflow_definition(name: str, description: str, nodes: List[Dict], connections: List[Dict]) -> Dict[str, Any]:
    """创建工作流定义"""
    return {
        "workflowId": f"wf_{random.randint(100000, 999999)}",
        "name": name,
        "description": description,
        "version": "1.0.0",
        "author": "测试脚本",
        "createdAt": datetime.utcnow().isoformat() + "Z",
        "lastModifiedAt": datetime.utcnow().isoformat() + "Z",
        "isActive": True,
        "isPublished": False,
        "publishStatus": "Draft",
        "createdBy": "测试脚本",
        "lastModifiedBy": "测试脚本",
        "nodes": nodes,
        "connections": connections,
        "inputParameters": [],
        "outputParameters": [],
        "configuration": {},
        "tags": ["测试", "自动生成"],
        "metadata": {
            "createdBy": "测试脚本",
            "category": "测试工作流",
            "nodeCount": len(nodes),
            "connectionCount": len(connections)
        }
    }

def create_simple_workflow(name: str, description: str, node_types: List[str]) -> Dict[str, Any]:
    """创建简单工作流"""
    nodes = []
    connections = []
    
    # 添加开始节点
    start_node = create_workflow_node(
        "start_node", 
        "start", 
        "开始", 
        "工作流开始节点", 
        {"x": 100, "y": 100}
    )
    nodes.append(start_node)
    
    # 添加指定类型的节点
    prev_node_id = "start_node"
    x_pos = 300
    
    for i, node_type in enumerate(node_types):
        node_id = f"{node_type}_{i}"
        node_info = next((n for n in NODE_TYPES if n["type"] == node_type), None)
        
        if node_info:
            node = create_workflow_node(
                node_id,
                node_type,
                node_info["name"],
                f"测试{node_info['name']}节点",
                {"x": x_pos, "y": 100 + (i * 150)},
                generate_node_config(node_type)
            )
            nodes.append(node)
            
            # 添加连接
            connection = create_workflow_connection(
                f"conn_{prev_node_id}_{node_id}",
                prev_node_id,
                node_id
            )
            connections.append(connection)
            
            prev_node_id = node_id
            if i % 3 == 0:  # 每3个节点换行
                x_pos += 200
    
    # 添加结束节点
    end_node = create_workflow_node(
        "end_node",
        "end", 
        "结束", 
        "工作流结束节点", 
        {"x": x_pos + 200, "y": 100}
    )
    nodes.append(end_node)
    
    # 连接到结束节点
    final_connection = create_workflow_connection(
        f"conn_{prev_node_id}_end_node",
        prev_node_id,
        "end_node"
    )
    connections.append(final_connection)
    
    return create_workflow_definition(name, description, nodes, connections)

def generate_test_workflows():
    """生成100个测试工作流"""
    workflows = []
    single_node_types = [n["type"] for n in NODE_TYPES if n["type"] not in ["start", "end"]]

    # 1. 单节点测试工作流（14个，排除start和end）
    for node_type in single_node_types:
        node_info = next(n for n in NODE_TYPES if n["type"] == node_type)
        workflow = create_simple_workflow(
            f"单节点测试-{node_info['name']}",
            f"测试{node_info['name']}节点的基本功能",
            [node_type]
        )
        workflows.append(workflow)

    # 2. 双节点组合测试（16个）
    combinations_2 = [
        (["task", "decision"], "任务决策流程"),
        (["database", "email"], "数据处理通知流程"),
        (["timer", "condition"], "定时条件触发流程"),
        (["parallel", "merge"], "并行合并流程"),
        (["loop", "script"], "循环脚本处理流程"),
        (["delay", "http"], "延时HTTP请求流程"),
        (["file", "database"], "文件数据库存储流程"),
        (["webhook", "condition"], "Webhook条件流程"),
        (["email", "task"], "邮件任务流程"),
        (["script", "file"], "脚本文件处理流程"),
        (["http", "database"], "HTTP数据库流程"),
        (["timer", "email"], "定时邮件流程"),
        (["condition", "delay"], "条件延时流程"),
        (["webhook", "script"], "Webhook脚本流程"),
        (["parallel", "task"], "并行任务流程"),
        (["decision", "email"], "决策邮件流程")
    ]

    for node_types, desc in combinations_2:
        workflow = create_simple_workflow(
            f"双节点组合-{desc}",
            f"测试{desc}的双节点组合功能",
            node_types
        )
        workflows.append(workflow)

    # 3. 三节点组合测试（20个）
    combinations_3 = [
        (["task", "decision", "email"], "任务决策通知流程"),
        (["timer", "http", "database"], "定时HTTP数据流程"),
        (["file", "script", "email"], "文件脚本邮件流程"),
        (["webhook", "condition", "task"], "Webhook条件任务流程"),
        (["parallel", "task", "merge"], "并行任务合并流程"),
        (["delay", "http", "decision"], "延时HTTP决策流程"),
        (["database", "script", "file"], "数据库脚本文件流程"),
        (["timer", "condition", "email"], "定时条件邮件流程"),
        (["loop", "task", "database"], "循环任务数据流程"),
        (["http", "script", "webhook"], "HTTP脚本Webhook流程")
    ]

    for node_types, desc in combinations_3:
        workflow = create_simple_workflow(
            f"三节点组合-{desc}",
            f"测试{desc}的三节点组合功能",
            node_types
        )
        workflows.append(workflow)

    # 生成更多三节点组合
    for i in range(10):
        random_types = random.sample(single_node_types, 3)
        workflow = create_simple_workflow(
            f"随机三节点组合-{i+1}",
            f"随机三节点组合测试工作流{i+1}",
            random_types
        )
        workflows.append(workflow)

    # 4. 复杂流程测试（30个）
    business_scenarios = [
        "用户注册流程", "订单处理流程", "支付验证流程", "库存管理流程", "客户服务流程",
        "数据备份流程", "系统监控流程", "报告生成流程", "审批流程", "通知推送流程",
        "文件处理流程", "数据同步流程", "错误处理流程", "性能监控流程", "安全检查流程",
        "日志分析流程", "缓存更新流程", "消息队列流程", "API调用流程", "数据清理流程",
        "用户认证流程", "权限验证流程", "配置更新流程", "健康检查流程", "负载均衡流程",
        "故障恢复流程", "数据迁移流程", "版本发布流程", "测试执行流程", "部署流程"
    ]

    for i, scenario in enumerate(business_scenarios):
        # 创建包含4-8个节点的复杂流程
        complex_types = random.sample(single_node_types, random.randint(4, 8))
        workflow = create_simple_workflow(
            f"业务流程-{scenario}",
            f"模拟{scenario}的完整业务处理流程，包含{len(complex_types)}个处理节点",
            complex_types
        )
        workflows.append(workflow)

    # 5. 超复杂流程测试（20个）
    for i in range(20):
        # 创建包含8-12个节点的超复杂流程
        super_complex_types = random.sample(single_node_types, random.randint(8, min(12, len(single_node_types))))
        workflow = create_simple_workflow(
            f"超复杂流程-企业级业务流程{i+1}",
            f"企业级复杂业务流程，包含{len(super_complex_types)}个节点的完整处理链路",
            super_complex_types
        )
        workflows.append(workflow)

    print(f"✅ 生成了 {len(workflows)} 个测试工作流")
    return workflows

def upload_workflows(workflows):
    """上传工作流到API"""
    print(f"📤 开始上传 {len(workflows)} 个测试工作流...")

    success_count = 0
    for i, workflow in enumerate(workflows, 1):
        try:
            response = requests.post(
                f"{API_BASE_URL}/workflows",
                json=workflow,
                headers={"Content-Type": "application/json"}
            )

            if response.status_code in [200, 201]:
                print(f"✅ [{i:3d}/{len(workflows)}] 上传成功: {workflow['name']}")
                success_count += 1
            else:
                print(f"❌ [{i:3d}/{len(workflows)}] 上传失败: {workflow['name']} - {response.status_code}")
                if response.text:
                    error_info = response.text[:200] + "..." if len(response.text) > 200 else response.text
                    print(f"    错误信息: {error_info}")
        except Exception as e:
            print(f"❌ [{i:3d}/{len(workflows)}] 上传异常: {workflow['name']} - {e}")

        # 避免请求过快
        time.sleep(0.05)  # 减少延时，加快上传速度

    print(f"\n📊 上传完成: {success_count}/{len(workflows)} 个工作流上传成功")
    return success_count

def main():
    """主函数"""
    print("🚀 开始生成工作流测试数据...")
    print("=" * 50)

    # 1. 清空数据库
    if not clear_database():
        print("❌ 数据库清理失败，退出程序")
        return

    print()

    # 2. 生成测试工作流
    print("🏗️ 生成测试工作流...")
    workflows = generate_test_workflows()
    print(f"✅ 生成了 {len(workflows)} 个测试工作流")

    print()

    # 3. 上传工作流
    success_count = upload_workflows(workflows)

    print()
    print("=" * 50)
    if success_count == len(workflows):
        print("🎉 所有测试工作流生成成功！")
    elif success_count > 0:
        print(f"⚠️ 部分工作流生成成功: {success_count}/{len(workflows)}")
    else:
        print("❌ 工作流生成失败")

    print()
    print("🌐 可以访问 http://localhost:3001/workflow/list 查看生成的工作流")

if __name__ == "__main__":
    main()
