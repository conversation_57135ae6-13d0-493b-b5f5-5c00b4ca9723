using FlowCustomV1.Core.Models.Designer;

namespace FlowCustomV1.Core.Interfaces.Services;

/// <summary>
/// 工作流模板管理服务接口
/// 提供工作流模板的创建、管理和版本控制功能
/// </summary>
public interface ITemplateManagementService
{
    #region 模板基础操作

    /// <summary>
    /// 创建工作流模板
    /// </summary>
    /// <param name="template">模板定义</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>创建结果</returns>
    Task<bool> CreateTemplateAsync(WorkflowTemplate template, CancellationToken cancellationToken = default);

    /// <summary>
    /// 更新工作流模板
    /// </summary>
    /// <param name="templateId">模板ID</param>
    /// <param name="template">模板定义</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>更新结果</returns>
    Task<bool> UpdateTemplateAsync(string templateId, WorkflowTemplate template, CancellationToken cancellationToken = default);

    /// <summary>
    /// 删除工作流模板
    /// </summary>
    /// <param name="templateId">模板ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>删除结果</returns>
    Task<bool> DeleteTemplateAsync(string templateId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取工作流模板
    /// </summary>
    /// <param name="templateId">模板ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>模板定义</returns>
    Task<WorkflowTemplate?> GetTemplateAsync(string templateId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取工作流模板列表
    /// </summary>
    /// <param name="query">查询条件</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>模板列表</returns>
    Task<IReadOnlyList<WorkflowTemplate>> GetTemplatesAsync(TemplateQuery? query = null, CancellationToken cancellationToken = default);

    #endregion

    #region 模板分类管理

    /// <summary>
    /// 获取模板分类列表
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>分类列表</returns>
    Task<IReadOnlyList<string>> GetCategoriesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 按分类获取模板
    /// </summary>
    /// <param name="category">分类名称</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>模板列表</returns>
    Task<IReadOnlyList<WorkflowTemplate>> GetTemplatesByCategoryAsync(string category, CancellationToken cancellationToken = default);

    #endregion

    #region 模板版本管理

    /// <summary>
    /// 创建模板版本
    /// </summary>
    /// <param name="templateId">模板ID</param>
    /// <param name="versionInfo">版本信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>版本ID</returns>
    Task<string> CreateTemplateVersionAsync(string templateId, TemplateVersionInfo versionInfo, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取模板版本历史
    /// </summary>
    /// <param name="templateId">模板ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>版本历史</returns>
    Task<IReadOnlyList<TemplateVersion>> GetTemplateVersionsAsync(string templateId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取指定版本的模板
    /// </summary>
    /// <param name="templateId">模板ID</param>
    /// <param name="version">版本号</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>模板定义</returns>
    Task<WorkflowTemplate?> GetTemplateVersionAsync(string templateId, string version, CancellationToken cancellationToken = default);

    /// <summary>
    /// 激活模板版本
    /// </summary>
    /// <param name="templateId">模板ID</param>
    /// <param name="version">版本号</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>激活结果</returns>
    Task<bool> ActivateTemplateVersionAsync(string templateId, string version, CancellationToken cancellationToken = default);

    #endregion

    #region 模板共享和发布

    /// <summary>
    /// 发布模板（设为公共）
    /// </summary>
    /// <param name="templateId">模板ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发布结果</returns>
    Task<bool> PublishTemplateAsync(string templateId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 取消发布模板
    /// </summary>
    /// <param name="templateId">模板ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>取消发布结果</returns>
    Task<bool> UnpublishTemplateAsync(string templateId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取公共模板列表
    /// </summary>
    /// <param name="query">查询条件</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>公共模板列表</returns>
    Task<IReadOnlyList<WorkflowTemplate>> GetPublicTemplatesAsync(TemplateQuery? query = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 复制模板
    /// </summary>
    /// <param name="sourceTemplateId">源模板ID</param>
    /// <param name="newTemplateName">新模板名称</param>
    /// <param name="newTemplateDescription">新模板描述</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>新模板ID</returns>
    Task<string> CloneTemplateAsync(string sourceTemplateId, string newTemplateName, string newTemplateDescription = "", CancellationToken cancellationToken = default);

    #endregion

    #region 模板统计和分析

    /// <summary>
    /// 获取模板使用统计
    /// </summary>
    /// <param name="templateId">模板ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>使用统计</returns>
    Task<TemplateUsageStatistics> GetTemplateUsageStatisticsAsync(string templateId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 更新模板使用次数
    /// </summary>
    /// <param name="templateId">模板ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>更新结果</returns>
    Task<bool> IncrementTemplateUsageAsync(string templateId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取热门模板列表
    /// </summary>
    /// <param name="count">返回数量</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>热门模板列表</returns>
    Task<IReadOnlyList<WorkflowTemplate>> GetPopularTemplatesAsync(int count = 10, CancellationToken cancellationToken = default);

    #endregion

    #region 模板验证

    /// <summary>
    /// 验证模板定义
    /// </summary>
    /// <param name="template">模板定义</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    Task<TemplateValidationResult> ValidateTemplateAsync(WorkflowTemplate template, CancellationToken cancellationToken = default);

    /// <summary>
    /// 检查模板兼容性
    /// </summary>
    /// <param name="templateId">模板ID</param>
    /// <param name="targetVersion">目标版本</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>兼容性检查结果</returns>
    Task<TemplateCompatibilityResult> CheckTemplateCompatibilityAsync(string templateId, string targetVersion, CancellationToken cancellationToken = default);

    #endregion

    #region 事件

    /// <summary>
    /// 模板创建事件
    /// </summary>
    event EventHandler<TemplateCreatedEventArgs>? TemplateCreated;

    /// <summary>
    /// 模板更新事件
    /// </summary>
    event EventHandler<TemplateUpdatedEventArgs>? TemplateUpdated;

    /// <summary>
    /// 模板删除事件
    /// </summary>
    event EventHandler<TemplateDeletedEventArgs>? TemplateDeleted;

    /// <summary>
    /// 模板发布事件
    /// </summary>
    event EventHandler<TemplatePublishedEventArgs>? TemplatePublished;

    /// <summary>
    /// 模板使用事件
    /// </summary>
    event EventHandler<TemplateUsedEventArgs>? TemplateUsed;

    #endregion
}
