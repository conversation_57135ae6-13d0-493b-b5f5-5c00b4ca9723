#!/usr/bin/env python3
"""
生成各环境和角色的Dockerfile文件
"""

import os
from pathlib import Path

def create_dockerfile(environment, role, expose_port=None):
    """创建指定环境和角色的Dockerfile"""
    
    # 基础模板
    template = f"""# FlowCustomV1 {role.title()}节点 - {environment.title()}环境
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
WORKDIR /app"""
    
    if expose_port:
        template += f"\nEXPOSE {expose_port}"
    
    template += f"""

# {environment.title()}环境标签
LABEL environment="{environment}"
LABEL component="{role.lower()}"
LABEL version="v0.0.1.8"

FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

# 复制项目文件
COPY ["src/FlowCustomV1.Api/FlowCustomV1.Api.csproj", "src/FlowCustomV1.Api/"]
COPY ["src/FlowCustomV1.Core/FlowCustomV1.Core.csproj", "src/FlowCustomV1.Core/"]
COPY ["src/FlowCustomV1.Infrastructure/FlowCustomV1.Infrastructure.csproj", "src/FlowCustomV1.Infrastructure/"]

# 还原依赖
RUN dotnet restore "src/FlowCustomV1.Api/FlowCustomV1.Api.csproj"

# 复制所有源代码
COPY . .

# 构建项目"""
    
    if environment == "production":
        template += " (生产优化)"
    
    template += f"""
WORKDIR "/src/src/FlowCustomV1.Api"
RUN dotnet build "FlowCustomV1.Api.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "FlowCustomV1.Api.csproj" -c Release -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .

# {environment.title()}环境特定配置
ENV ASPNETCORE_ENVIRONMENT={environment.title()}"""

    if role.lower() != "api":
        template += f"""

# {role.title()}节点特定配置
ENV NODE_ROLES={role.title()}"""

    if environment == "production":
        template += """

# 生产环境安全设置
ENV DOTNET_EnableDiagnostics=0
ENV DOTNET_USE_POLLING_FILE_WATCHER=false
ENV ASPNETCORE_LOGGING__CONSOLE__DISABLECOLORS=true

# 创建非root用户 (生产安全)
RUN adduser --disabled-password --gecos '' appuser && chown -R appuser /app
USER appuser"""
    
    elif environment == "development":
        template += """

# 开发环境设置
ENV DOTNET_EnableDiagnostics=1
ENV ASPNETCORE_LOGGING__LOGLEVEL__DEFAULT=Debug"""

    # 健康检查
    if role.lower() == "api":
        health_check = f"curl -f http://localhost:{expose_port or 5000}/health || exit 1"
    else:
        health_check = "dotnet FlowCustomV1.Api.dll --health-check || exit 1"
    
    template += f"""

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \\
    CMD {health_check}"""

    # 入口点
    if role.lower() == "api":
        if expose_port:
            template += f"""

ENV ASPNETCORE_URLS=http://+:{expose_port}
ENTRYPOINT ["dotnet", "FlowCustomV1.Api.dll", "--environment", "{environment.title()}"]"""
        else:
            template += f"""

ENTRYPOINT ["dotnet", "FlowCustomV1.Api.dll", "--environment", "{environment.title()}"]"""
    else:
        template += f"""

ENTRYPOINT ["dotnet", "FlowCustomV1.Api.dll", "--environment", "{environment.title()}", "--roles", "{role.title()}"]"""

    return template

def main():
    """主函数"""
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    
    # 定义环境和角色
    environments = {
        "development": {"port_offset": 0, "api_port": 5000},
        "production": {"port_offset": 10000, "api_port": 15000},
        "testing": {"port_offset": 20000, "api_port": 25000}
    }
    
    roles = ["worker", "designer", "validator", "executor", "monitor", "scheduler"]
    
    # 为每个环境生成Dockerfile
    for env_name, env_config in environments.items():
        env_dir = project_root / "docker" / env_name
        env_dir.mkdir(exist_ok=True)
        
        # 生成各角色的Dockerfile
        for role in roles:
            dockerfile_path = env_dir / f"Dockerfile.{role}"
            dockerfile_content = create_dockerfile(env_name, role)
            
            with open(dockerfile_path, 'w', encoding='utf-8') as f:
                f.write(dockerfile_content)
            
            print(f"✅ 创建: {dockerfile_path}")
    
    print(f"\n🎉 所有Dockerfile生成完成!")
    print(f"📁 生成位置:")
    for env_name in environments.keys():
        print(f"   - docker/{env_name}/Dockerfile.*")

if __name__ == "__main__":
    main()
