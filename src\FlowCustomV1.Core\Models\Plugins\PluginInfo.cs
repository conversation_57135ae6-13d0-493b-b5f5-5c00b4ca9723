namespace FlowCustomV1.Core.Models.Plugins;

/// <summary>
/// 插件信息
/// </summary>
public class PluginInfo
{
    /// <summary>
    /// 节点类型标识符
    /// </summary>
    public string NodeType { get; set; } = string.Empty;

    /// <summary>
    /// 插件名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 插件显示名称
    /// </summary>
    public string DisplayName { get; set; } = string.Empty;

    /// <summary>
    /// 插件描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 插件版本
    /// </summary>
    public string Version { get; set; } = string.Empty;

    /// <summary>
    /// 插件作者
    /// </summary>
    public string Author { get; set; } = string.Empty;

    /// <summary>
    /// 插件类型
    /// </summary>
    public PluginType PluginType { get; set; }

    /// <summary>
    /// 插件路径
    /// </summary>
    public string PluginPath { get; set; } = string.Empty;

    /// <summary>
    /// 插件配置
    /// </summary>
    public Dictionary<string, object> Configuration { get; set; } = new();

    /// <summary>
    /// 插件依赖
    /// </summary>
    public List<string> Dependencies { get; set; } = new();

    /// <summary>
    /// 插件标签
    /// </summary>
    public List<string> Tags { get; set; } = new();

    /// <summary>
    /// 是否已加载
    /// </summary>
    public bool IsLoaded { get; set; }

    /// <summary>
    /// 加载时间
    /// </summary>
    public DateTime? LoadedAt { get; set; }

    /// <summary>
    /// 最后使用时间
    /// </summary>
    public DateTime? LastUsedAt { get; set; }

    /// <summary>
    /// 使用次数
    /// </summary>
    public long UsageCount { get; set; }

    /// <summary>
    /// 插件元数据
    /// </summary>
    public PluginMetadata? Metadata { get; set; }
}

/// <summary>
/// 插件类型枚举
/// </summary>
public enum PluginType
{
    /// <summary>
    /// 内置插件
    /// </summary>
    Builtin,

    /// <summary>
    /// JSON配置插件
    /// </summary>
    JsonConfig,

    /// <summary>
    /// DLL预编译插件
    /// </summary>
    DllPrecompiled
}

/// <summary>
/// 插件元数据
/// </summary>
public class PluginMetadata
{
    /// <summary>
    /// 节点类型标识符
    /// </summary>
    public string NodeType { get; set; } = string.Empty;

    /// <summary>
    /// 插件名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 插件显示名称
    /// </summary>
    public string DisplayName { get; set; } = string.Empty;

    /// <summary>
    /// 插件描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 插件版本
    /// </summary>
    public string Version { get; set; } = string.Empty;

    /// <summary>
    /// 插件作者
    /// </summary>
    public string Author { get; set; } = string.Empty;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// 修改时间
    /// </summary>
    public DateTime ModifiedAt { get; set; }

    /// <summary>
    /// 文件大小（字节）
    /// </summary>
    public long FileSize { get; set; }

    /// <summary>
    /// 文件哈希值
    /// </summary>
    public string FileHash { get; set; } = string.Empty;

    /// <summary>
    /// 支持的框架版本
    /// </summary>
    public List<string> SupportedFrameworks { get; set; } = new();

    /// <summary>
    /// 最低系统要求
    /// </summary>
    public Dictionary<string, string> SystemRequirements { get; set; } = new();

    /// <summary>
    /// 许可证信息
    /// </summary>
    public string License { get; set; } = string.Empty;

    /// <summary>
    /// 主页URL
    /// </summary>
    public string Homepage { get; set; } = string.Empty;

    /// <summary>
    /// 文档URL
    /// </summary>
    public string Documentation { get; set; } = string.Empty;
}
