using System.Collections.Concurrent;
using FlowCustomV1.Core.Interfaces.Messaging;
using FlowCustomV1.Infrastructure.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NATS.Client.Core;
using NATS.Client.JetStream;
using NATS.Client.JetStream.Models;
using NATS.Net;

namespace FlowCustomV1.Infrastructure.Services.Messaging;

/// <summary>
/// NATS消息服务实现
/// </summary>
public class NatsService : INatsService
{
    private readonly ILogger<NatsService> _logger;
    private readonly NatsConfiguration _config;
    private readonly ConcurrentDictionary<string, MessageSubscription> _subscriptions;
    
    private NatsClient? _connection;
    private INatsJSContext? _jetStreamContext;
    private bool _disposed;

    /// <summary>
    /// 构造函数
    /// </summary>
    public NatsService(IOptions<NatsConfiguration> config, ConnectionStateManager connectionStateManager, ILogger<NatsService> logger)
    {
        _config = config.Value ?? throw new ArgumentNullException(nameof(config));
        _ = connectionStateManager ?? throw new ArgumentNullException(nameof(connectionStateManager)); // 参数验证但不存储
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _subscriptions = new ConcurrentDictionary<string, MessageSubscription>();
    }

    #region 连接管理

    /// <inheritdoc />
    public bool IsConnected => _connection != null;

    /// <inheritdoc />
    public event EventHandler<ConnectionStatusChangedEventArgs>? ConnectionStatusChanged;

    /// <inheritdoc />
    public async Task ConnectAsync(CancellationToken cancellationToken = default)
    {
        ObjectDisposedException.ThrowIf(_disposed, this);

        if (IsConnected)
        {
            _logger.LogDebug("Already connected to NATS");
            return;
        }

        try
        {
            _logger.LogInformation("Connecting to NATS servers: {Servers}", string.Join(", ", _config.Servers));

            var options = new NatsOpts
            {
                Url = _config.Servers.First(), // 主服务器
                Name = _config.ConnectionName,
                ConnectTimeout = TimeSpan.FromSeconds(_config.ConnectionTimeoutSeconds),
                ReconnectWaitMin = TimeSpan.FromSeconds(_config.ReconnectIntervalSeconds),
                MaxReconnectRetry = _config.MaxReconnectAttempts,
                PingInterval = TimeSpan.FromSeconds(_config.PingIntervalSeconds),
                MaxPingOut = _config.MaxPingsOutstanding,
                Verbose = _config.EnableVerboseLogging
            };

            // 添加认证信息
            if (!string.IsNullOrEmpty(_config.Username) && !string.IsNullOrEmpty(_config.Password))
            {
                options = options with
                {
                    AuthOpts = new NatsAuthOpts
                    {
                        Username = _config.Username,
                        Password = _config.Password
                    }
                };
            }

            _connection = new NatsClient(options);

            // 订阅连接状态变更事件
            // 注意：这里简化了事件处理，在v0.0.1.2中会完善
            // _connection.ConnectionDisconnected += OnConnectionDisconnected;
            // _connection.ConnectionOpened += OnConnectionOpened;
            // _connection.ReconnectFailed += OnReconnectFailed;

            // NatsClient 自动连接，无需显式调用 ConnectAsync
            // await _connection.ConnectAsync();

            // 初始化JetStream上下文
            if (_config.JetStream.Enabled)
            {
                await InitializeJetStreamAsync(cancellationToken);
            }

            _logger.LogInformation("Successfully connected to NATS");
            OnConnectionStatusChanged(true, _config.Servers.First());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to connect to NATS");
            OnConnectionStatusChanged(false, null, ex.Message);
            throw;
        }
    }

    /// <inheritdoc />
    public async Task DisconnectAsync(CancellationToken cancellationToken = default)
    {
        if (_connection == null || !IsConnected)
            return;

        try
        {
            _logger.LogInformation("Disconnecting from NATS");

            // 取消所有订阅
            await UnsubscribeAllAsync();

            // 断开连接
            await _connection.DisposeAsync();
            _connection = null;
            _jetStreamContext = null;

            _logger.LogInformation("Successfully disconnected from NATS");
            OnConnectionStatusChanged(false, null);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during NATS disconnection");
            throw;
        }
    }

    #endregion

    #region 消息发布

    /// <inheritdoc />
    public async Task PublishAsync<T>(string subject, T message, CancellationToken cancellationToken = default) where T : class
    {
        EnsureConnected();
        
        try
        {
            _logger.LogDebug("Publishing message to subject: {Subject}", subject);
            
            await _connection!.PublishAsync(subject, message, cancellationToken: cancellationToken);
            
            _logger.LogDebug("Message published successfully to subject: {Subject}", subject);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to publish message to subject: {Subject}", subject);
            throw;
        }
    }

    /// <inheritdoc />
    public async Task PublishAsync(IMessage message, CancellationToken cancellationToken = default)
    {
        // 对于抽象的IMessage，我们需要从元数据中获取主题信息
        if (!message.Metadata.TryGetValue("Subject", out var subjectObj) || subjectObj is not string subject)
        {
            throw new ArgumentException("Message must contain 'Subject' in metadata for NATS publishing", nameof(message));
        }

        await PublishAsync(subject, message, cancellationToken);
    }

    /// <inheritdoc />
    public async Task<TResponse?> RequestAsync<TRequest, TResponse>(
        string subject, 
        TRequest request, 
        TimeSpan? timeout = null,
        CancellationToken cancellationToken = default) 
        where TRequest : class 
        where TResponse : class
    {
        EnsureConnected();

        try
        {
            _logger.LogDebug("Sending request to subject: {Subject}", subject);

            var requestTimeout = timeout ?? TimeSpan.FromSeconds(30);
            var response = await _connection!.RequestAsync<TRequest, TResponse>(
                subject,
                request,
                cancellationToken: cancellationToken);

            _logger.LogDebug("Received response from subject: {Subject}", subject);
            return response.Data;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send request to subject: {Subject}", subject);
            throw;
        }
    }

    #endregion

    #region 消息订阅

    /// <inheritdoc />
    public Task<IMessageSubscription> SubscribeAsync<T>(
        string subject,
        Func<T, Task> handler,
        string? queueGroup = null,
        CancellationToken cancellationToken = default) where T : class
    {
        EnsureConnected();

        try
        {
            _logger.LogDebug("Subscribing to subject: {Subject}, QueueGroup: {QueueGroup}", subject, queueGroup);

            var subscriptionId = Guid.NewGuid().ToString();
            
            var subscription = _connection!.SubscribeAsync<T>(subject, queueGroup, cancellationToken: cancellationToken);

            // 启动后台任务处理消息
            _ = Task.Run(async () =>
            {
                try
                {
                    await foreach (var msg in subscription.WithCancellation(cancellationToken))
                    {
                        try
                        {
                            if (msg.Data != null)
                                await handler(msg.Data);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "Error handling message from subject: {Subject}", subject);
                        }
                    }
                }
                catch (OperationCanceledException)
                {
                    // 正常取消，忽略
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error in subscription loop for subject: {Subject}", subject);
                }
            }, cancellationToken);

            var messageSubscription = new MessageSubscription(subscriptionId, subject, queueGroup, cancellationToken);
            _subscriptions.TryAdd(subscriptionId, messageSubscription);

            _logger.LogDebug("Successfully subscribed to subject: {Subject}", subject);
            return Task.FromResult<IMessageSubscription>(messageSubscription);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to subscribe to subject: {Subject}", subject);
            throw;
        }
    }

    /// <inheritdoc />
    public async Task<IMessageSubscription> SubscribeAsync(
        string subject,
        Func<IMessage, Task> handler,
        string? queueGroup = null,
        CancellationToken cancellationToken = default)
    {
        return await SubscribeAsync<IMessage>(subject, handler, queueGroup, cancellationToken);
    }

    /// <inheritdoc />
    public Task<IMessageSubscription> SubscribeRequestAsync<TRequest, TResponse>(
        string subject,
        Func<TRequest, Task<TResponse>> handler,
        string? queueGroup = null,
        CancellationToken cancellationToken = default)
        where TRequest : class
        where TResponse : class
    {
        EnsureConnected();

        try
        {
            _logger.LogDebug("Subscribing to request subject: {Subject}, QueueGroup: {QueueGroup}", subject, queueGroup);

            var subscriptionId = Guid.NewGuid().ToString();
            
            var subscription = _connection!.SubscribeAsync<TRequest>(subject, queueGroup, cancellationToken: cancellationToken);

            // 启动后台任务处理请求
            _ = Task.Run(async () =>
            {
                try
                {
                    await foreach (var msg in subscription.WithCancellation(cancellationToken))
                    {
                        try
                        {
                            if (msg.Data != null)
                            {
                                var response = await handler(msg.Data);
                                await msg.ReplyAsync(response, cancellationToken: cancellationToken);
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "Error handling request from subject: {Subject}", subject);
                        }
                    }
                }
                catch (OperationCanceledException)
                {
                    // 正常取消，忽略
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error in request subscription loop for subject: {Subject}", subject);
                }
            }, cancellationToken);

            var messageSubscription = new MessageSubscription(subscriptionId, subject, queueGroup, cancellationToken);
            _subscriptions.TryAdd(subscriptionId, messageSubscription);

            _logger.LogDebug("Successfully subscribed to request subject: {Subject}", subject);
            return Task.FromResult<IMessageSubscription>(messageSubscription);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to subscribe to request subject: {Subject}", subject);
            throw;
        }
    }

    #endregion

    #region JetStream支持

    /// <inheritdoc />
    public bool IsJetStreamEnabled => _jetStreamContext != null;

    /// <inheritdoc />
    public async Task<string> PublishToStreamAsync<T>(string subject, T message, CancellationToken cancellationToken = default) where T : class
    {
        if (_jetStreamContext == null)
            throw new InvalidOperationException("JetStream is not enabled or initialized");

        try
        {
            _logger.LogDebug("Publishing message to JetStream subject: {Subject}", subject);

            var ack = await _jetStreamContext.PublishAsync(subject, message, cancellationToken: cancellationToken);

            _logger.LogDebug("Message published to JetStream successfully. Sequence: {Sequence}", ack.Seq);
            return ack.Seq.ToString();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to publish message to JetStream subject: {Subject}", subject);
            throw;
        }
    }

    /// <inheritdoc />
    public async Task CreateOrUpdateStreamAsync(string streamName, string[] subjects, CancellationToken cancellationToken = default)
    {
        if (_jetStreamContext == null)
            throw new InvalidOperationException("JetStream is not enabled or initialized");

        try
        {
            _logger.LogInformation("Creating or updating JetStream stream: {StreamName}", streamName);

            var streamConfig = new StreamConfig(streamName, subjects)
            {
                Storage = _config.JetStream.DefaultStream.Storage == "memory" ? StreamConfigStorage.Memory : StreamConfigStorage.File,
                MaxMsgs = _config.JetStream.DefaultStream.MaxMessages,
                MaxBytes = _config.JetStream.DefaultStream.MaxBytes,
                MaxAge = TimeSpan.FromSeconds(_config.JetStream.DefaultStream.MaxAgeSeconds),
                NumReplicas = _config.JetStream.DefaultStream.Replicas
            };

            try
            {
                await _jetStreamContext.CreateStreamAsync(streamConfig, cancellationToken);
                _logger.LogInformation("JetStream stream created successfully: {StreamName}", streamName);
            }
            catch (NatsJSApiException ex) when (ex.Error?.Code == 10058) // Stream name already in use
            {
                _logger.LogInformation("JetStream stream already exists: {StreamName}, attempting to update", streamName);
                try
                {
                    await _jetStreamContext.UpdateStreamAsync(streamConfig, cancellationToken);
                    _logger.LogInformation("JetStream stream updated successfully: {StreamName}", streamName);
                }
                catch (Exception updateEx)
                {
                    _logger.LogWarning(updateEx, "Failed to update existing stream {StreamName}, continuing with existing stream", streamName);
                    // Continue with existing stream - this is acceptable for most use cases
                }
            }
            catch (NatsJSApiException ex) when (ex.Error?.Description?.Contains("duplicate subjects") == true ||
                                                   ex.Error?.Description?.Contains("subjects overlap") == true)
            {
                _logger.LogWarning("JetStream stream has overlapping subjects: {StreamName}, continuing with existing stream", streamName);
                // Continue with existing stream - this is acceptable for most use cases
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create or update JetStream stream: {StreamName}", streamName);
            throw;
        }
    }

    #endregion

    #region 私有方法

    private void EnsureConnected()
    {
        ObjectDisposedException.ThrowIf(_disposed, this);

        if (!IsConnected)
            throw new InvalidOperationException("Not connected to NATS. Call ConnectAsync first.");
    }

    private async Task InitializeJetStreamAsync(CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Initializing JetStream context");

            _jetStreamContext = _connection!.CreateJetStreamContext();

            // 创建默认流（如果配置了）
            if (!string.IsNullOrEmpty(_config.JetStream.DefaultStream.Name) &&
                _config.JetStream.DefaultStream.Subjects.Count > 0)
            {
                await CreateOrUpdateStreamAsync(
                    _config.JetStream.DefaultStream.Name,
                    [.. _config.JetStream.DefaultStream.Subjects],
                    cancellationToken);
            }

            _logger.LogDebug("JetStream context initialized successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize JetStream context");
            throw;
        }
    }

    private async Task UnsubscribeAllAsync()
    {
        var subscriptions = _subscriptions.Values.ToList();
        foreach (var subscription in subscriptions)
        {
            try
            {
                await subscription.UnsubscribeAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error unsubscribing from {Subject}", subscription.Subject);
            }
        }
        _subscriptions.Clear();
    }

    private void OnConnectionStatusChanged(bool isConnected, string? serverUrl, string? error = null)
    {
        ConnectionStatusChanged?.Invoke(this, new ConnectionStatusChangedEventArgs
        {
            IsConnected = isConnected,
            ServerUrl = serverUrl,
            Error = error
        });
    }

    // 移除未使用的事件处理方法

    #endregion

    #region IDisposable

    /// <inheritdoc />
    public void Dispose()
    {
        if (_disposed)
            return;

        try
        {
            DisconnectAsync().GetAwaiter().GetResult();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during disposal");
        }

        _disposed = true;
        GC.SuppressFinalize(this);
    }

    #endregion
}

/// <summary>
/// 消息订阅实现
/// </summary>
internal class MessageSubscription : IMessageSubscription
{
    private readonly CancellationTokenSource _cancellationTokenSource;
    private bool _disposed;

    /// <summary>
    /// 构造函数
    /// </summary>
    public MessageSubscription(string subscriptionId, string subject, string? queueGroup, CancellationToken parentCancellationToken)
    {
        SubscriptionId = subscriptionId;
        Subject = subject;
        QueueGroup = queueGroup;
        _cancellationTokenSource = CancellationTokenSource.CreateLinkedTokenSource(parentCancellationToken);
    }

    /// <inheritdoc />
    public string SubscriptionId { get; }

    /// <inheritdoc />
    public string Subject { get; }

    /// <inheritdoc />
    public string? QueueGroup { get; }

    /// <inheritdoc />
    public bool IsActive => !_disposed && !_cancellationTokenSource.Token.IsCancellationRequested;

    /// <inheritdoc />
    public Task UnsubscribeAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed)
            return Task.CompletedTask;

        try
        {
            _cancellationTokenSource.Cancel();
        }
        finally
        {
            _disposed = true;
        }

        return Task.CompletedTask;
    }

    /// <inheritdoc />
    public void Dispose()
    {
        if (_disposed)
            return;

        try
        {
            _cancellationTokenSource.Cancel();
            _cancellationTokenSource.Dispose();
        }
        catch
        {
            // 忽略异常
        }

        _disposed = true;
    }
}
