# FlowCustomV1 Web Frontend

FlowCustomV1 工作流自动化系统的前端管理界面，基于 React + TypeScript + Ant Design Pro 构建。

## 技术栈

- **框架**: React 18 + TypeScript
- **UI 组件**: Ant Design 5 + Ant Design Pro Components
- **构建工具**: Vite
- **路由**: React Router v6
- **状态管理**: Zustand
- **HTTP 客户端**: Axios
- **样式**: WindiCSS + Ant Design
- **代码规范**: ESLint + TypeScript

## 功能特性

### 🎯 核心功能
- **仪表盘**: 系统概览和实时监控
- **工作流管理**: 可视化设计器、模板库、导入导出
- **执行管理**: 实时监控、历史记录、调度管理
- **集群管理**: 节点管理、拓扑视图、健康检查
- **节点服务**: Designer、Validator、Executor 等服务管理
- **监控中心**: 性能指标、告警管理、日志中心
- **插件管理**: 插件市场、Natasha 编译器集成
- **系统管理**: 配置管理、用户权限、安全设置

### 🚀 技术特性
- **响应式设计**: 支持桌面端和移动端
- **国际化**: 支持中英文切换
- **主题定制**: 支持亮色/暗色主题
- **权限控制**: 基于角色的访问控制
- **实时更新**: WebSocket 实时数据推送
- **离线支持**: PWA 离线缓存

## 项目结构

```
src/
├── components/          # 通用组件
│   └── Layout/         # 布局组件
├── pages/              # 页面组件
│   ├── Dashboard/      # 仪表盘
│   ├── Workflow/       # 工作流管理
│   ├── Execution/      # 执行管理
│   ├── Cluster/        # 集群管理
│   ├── Nodes/          # 节点服务
│   ├── Monitoring/     # 监控中心
│   ├── Plugins/        # 插件管理
│   └── System/         # 系统管理
├── services/           # API 服务
├── types/              # TypeScript 类型定义
├── constants/          # 常量定义
├── utils/              # 工具函数
├── hooks/              # 自定义 Hooks
├── stores/             # 状态管理
└── styles/             # 样式文件
```

## 开发指南

### 环境要求

- Node.js >= 16.0.0
- npm >= 8.0.0

### 安装依赖

```bash
npm install
```

### 开发模式

```bash
npm run dev
```

访问 http://localhost:3000

### 构建生产版本

```bash
npm run build
```

### 代码检查

```bash
npm run lint
npm run lint:fix
```

### 类型检查

```bash
npm run type-check
```

## API 集成

前端通过 Axios 与后端 API 进行通信，支持：

- **自动错误处理**: 统一的错误提示和处理
- **请求拦截**: 自动添加认证头和请求日志
- **响应拦截**: 统一的响应格式处理
- **代理配置**: 开发环境 API 代理到后端服务

### API 服务结构

```typescript
// 工作流 API
workflowApi.getWorkflows()
workflowApi.createWorkflow(workflow)
workflowApi.updateWorkflow(id, workflow)

// 执行 API
executionApi.startExecution(workflowId, inputData)
executionApi.getExecution(executionId)
executionApi.stopExecution(executionId)

// 集群 API
clusterApi.getClusterStats()
clusterApi.getAllNodes()
clusterApi.getClusterHealth()
```

## 组件开发规范

### 页面组件

```typescript
import React from 'react';
import { ProCard } from '@ant-design/pro-components';

const MyPage: React.FC = () => {
  return (
    <div>
      <div className="mb-6">
        <h1 className="text-2xl font-semibold text-gray-900 mb-2">
          页面标题
        </h1>
        <p className="text-gray-600">页面描述</p>
      </div>

      <ProCard title="内容区域">
        {/* 页面内容 */}
      </ProCard>
    </div>
  );
};

export default MyPage;
```

### 样式规范

- 使用 WindiCSS 工具类优先
- 遵循 Ant Design 设计规范
- 响应式设计考虑
- 语义化 CSS 类名

### 状态管理

使用 Zustand 进行状态管理：

```typescript
import { create } from 'zustand';

interface AppState {
  user: User | null;
  setUser: (user: User) => void;
}

export const useAppStore = create<AppState>((set) => ({
  user: null,
  setUser: (user) => set({ user }),
}));
```

## 部署说明

### Docker 部署

```dockerfile
FROM node:18-alpine as builder
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

### 环境变量

```bash
# API 基础地址
VITE_API_BASE_URL=http://localhost:5000/api

# 应用标题
VITE_APP_TITLE=FlowCustomV1

# 启用调试模式
VITE_DEBUG=false
```

## 版本信息

- **当前版本**: v0.0.1.11
- **发布日期**: 2025-01-13
- **兼容性**: 支持现代浏览器 (Chrome 90+, Firefox 88+, Safari 14+)

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。
