using FlowCustomV1.Core.Models.Workflow;

namespace FlowCustomV1.Core.Interfaces.Validator;

/// <summary>
/// 分布式验证结果缓存接口
/// 支持跨节点的验证结果缓存和同步
/// </summary>
public interface IDistributedValidationCache
{
    #region 缓存操作

    /// <summary>
    /// 获取缓存的验证结果
    /// </summary>
    /// <param name="cacheKey">缓存键</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>缓存的验证结果</returns>
    Task<WorkflowValidationResult?> GetAsync(string cacheKey, CancellationToken cancellationToken = default);

    /// <summary>
    /// 设置验证结果缓存
    /// </summary>
    /// <param name="cacheKey">缓存键</param>
    /// <param name="validationResult">验证结果</param>
    /// <param name="expiration">过期时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>设置任务</returns>
    Task SetAsync(string cacheKey, WorkflowValidationResult validationResult, TimeSpan? expiration = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 删除缓存项
    /// </summary>
    /// <param name="cacheKey">缓存键</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>删除任务</returns>
    Task<bool> RemoveAsync(string cacheKey, CancellationToken cancellationToken = default);

    /// <summary>
    /// 检查缓存项是否存在
    /// </summary>
    /// <param name="cacheKey">缓存键</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否存在</returns>
    Task<bool> ExistsAsync(string cacheKey, CancellationToken cancellationToken = default);

    /// <summary>
    /// 批量获取缓存项
    /// </summary>
    /// <param name="cacheKeys">缓存键列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>缓存结果字典</returns>
    Task<Dictionary<string, WorkflowValidationResult?>> GetBatchAsync(IEnumerable<string> cacheKeys, CancellationToken cancellationToken = default);

    /// <summary>
    /// 批量设置缓存项
    /// </summary>
    /// <param name="items">缓存项字典</param>
    /// <param name="expiration">过期时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>设置任务</returns>
    Task SetBatchAsync(Dictionary<string, WorkflowValidationResult> items, TimeSpan? expiration = null, CancellationToken cancellationToken = default);

    #endregion

    #region 缓存管理

    /// <summary>
    /// 清空所有缓存
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>清空任务</returns>
    Task ClearAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 清空指定模式的缓存
    /// </summary>
    /// <param name="pattern">缓存键模式</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>清空任务</returns>
    Task ClearByPatternAsync(string pattern, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取缓存统计信息
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>缓存统计</returns>
    Task<CacheStatistics> GetStatisticsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 重置缓存统计信息
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>重置任务</returns>
    Task ResetStatisticsAsync(CancellationToken cancellationToken = default);

    #endregion

    #region 分布式同步

    /// <summary>
    /// 同步缓存到其他节点
    /// </summary>
    /// <param name="cacheKey">缓存键</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>同步任务</returns>
    Task SyncToNodesAsync(string cacheKey, CancellationToken cancellationToken = default);

    /// <summary>
    /// 从其他节点同步缓存
    /// </summary>
    /// <param name="cacheKey">缓存键</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>同步任务</returns>
    Task SyncFromNodesAsync(string cacheKey, CancellationToken cancellationToken = default);

    /// <summary>
    /// 广播缓存失效通知
    /// </summary>
    /// <param name="cacheKey">缓存键</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>广播任务</returns>
    Task BroadcastInvalidationAsync(string cacheKey, CancellationToken cancellationToken = default);

    /// <summary>
    /// 处理缓存失效通知
    /// </summary>
    /// <param name="cacheKey">缓存键</param>
    /// <param name="sourceNodeId">源节点ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理任务</returns>
    Task HandleInvalidationAsync(string cacheKey, string sourceNodeId, CancellationToken cancellationToken = default);

    #endregion

    #region 缓存策略

    /// <summary>
    /// 设置缓存策略
    /// </summary>
    /// <param name="strategy">缓存策略</param>
    void SetCacheStrategy(CacheStrategy strategy);

    /// <summary>
    /// 获取当前缓存策略
    /// </summary>
    /// <returns>缓存策略</returns>
    CacheStrategy GetCacheStrategy();

    /// <summary>
    /// 设置缓存容量限制
    /// </summary>
    /// <param name="maxItems">最大缓存项数</param>
    /// <param name="maxMemoryMb">最大内存使用（MB）</param>
    void SetCapacityLimits(int maxItems, int maxMemoryMb);

    #endregion

    #region 事件

    /// <summary>
    /// 缓存命中事件
    /// </summary>
    event EventHandler<CacheHitEventArgs>? CacheHit;

    /// <summary>
    /// 缓存未命中事件
    /// </summary>
    event EventHandler<CacheMissEventArgs>? CacheMiss;

    /// <summary>
    /// 缓存失效事件
    /// </summary>
    event EventHandler<CacheInvalidatedEventArgs>? CacheInvalidated;

    /// <summary>
    /// 缓存同步事件
    /// </summary>
    event EventHandler<CacheSyncEventArgs>? CacheSynced;

    #endregion
}

/// <summary>
/// 缓存统计信息
/// </summary>
public class CacheStatistics
{
    /// <summary>
    /// 总缓存项数
    /// </summary>
    public long TotalItems { get; set; }

    /// <summary>
    /// 缓存命中次数
    /// </summary>
    public long HitCount { get; set; }

    /// <summary>
    /// 缓存未命中次数
    /// </summary>
    public long MissCount { get; set; }

    /// <summary>
    /// 缓存命中率
    /// </summary>
    public double HitRate => TotalRequests > 0 ? (double)HitCount / TotalRequests : 0;

    /// <summary>
    /// 总请求次数
    /// </summary>
    public long TotalRequests => HitCount + MissCount;

    /// <summary>
    /// 缓存大小（字节）
    /// </summary>
    public long CacheSizeBytes { get; set; }

    /// <summary>
    /// 过期项数量
    /// </summary>
    public long ExpiredItems { get; set; }

    /// <summary>
    /// 被驱逐项数量
    /// </summary>
    public long EvictedItems { get; set; }

    /// <summary>
    /// 平均访问时间（毫秒）
    /// </summary>
    public double AverageAccessTimeMs { get; set; }

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 缓存策略
/// </summary>
public enum CacheStrategy
{
    /// <summary>
    /// 最近最少使用
    /// </summary>
    LRU,

    /// <summary>
    /// 最近最不常用
    /// </summary>
    LFU,

    /// <summary>
    /// 先进先出
    /// </summary>
    FIFO,

    /// <summary>
    /// 基于时间的过期
    /// </summary>
    TimeBasedExpiration,

    /// <summary>
    /// 自适应策略
    /// </summary>
    Adaptive
}

/// <summary>
/// 缓存命中事件参数
/// </summary>
public class CacheHitEventArgs : EventArgs
{
    /// <summary>
    /// 缓存键
    /// </summary>
    public string CacheKey { get; set; } = string.Empty;

    /// <summary>
    /// 访问时间
    /// </summary>
    public DateTime AccessTime { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 访问耗时（毫秒）
    /// </summary>
    public long AccessTimeMs { get; set; }
}

/// <summary>
/// 缓存未命中事件参数
/// </summary>
public class CacheMissEventArgs : EventArgs
{
    /// <summary>
    /// 缓存键
    /// </summary>
    public string CacheKey { get; set; } = string.Empty;

    /// <summary>
    /// 访问时间
    /// </summary>
    public DateTime AccessTime { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 未命中原因
    /// </summary>
    public string Reason { get; set; } = string.Empty;
}

/// <summary>
/// 缓存失效事件参数
/// </summary>
public class CacheInvalidatedEventArgs : EventArgs
{
    /// <summary>
    /// 缓存键
    /// </summary>
    public string CacheKey { get; set; } = string.Empty;

    /// <summary>
    /// 失效时间
    /// </summary>
    public DateTime InvalidatedTime { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 失效原因
    /// </summary>
    public string Reason { get; set; } = string.Empty;

    /// <summary>
    /// 源节点ID
    /// </summary>
    public string? SourceNodeId { get; set; }
}

/// <summary>
/// 缓存同步事件参数
/// </summary>
public class CacheSyncEventArgs : EventArgs
{
    /// <summary>
    /// 缓存键
    /// </summary>
    public string CacheKey { get; set; } = string.Empty;

    /// <summary>
    /// 同步时间
    /// </summary>
    public DateTime SyncTime { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 同步方向
    /// </summary>
    public SyncDirection Direction { get; set; }

    /// <summary>
    /// 目标节点ID
    /// </summary>
    public string? TargetNodeId { get; set; }
}

/// <summary>
/// 同步方向
/// </summary>
public enum SyncDirection
{
    /// <summary>
    /// 推送到其他节点
    /// </summary>
    Push,

    /// <summary>
    /// 从其他节点拉取
    /// </summary>
    Pull
}
