namespace FlowCustomV1.Core.Models.Workflow;

/// <summary>
/// 验证规则
/// </summary>
public class ValidationRule
{
    /// <summary>
    /// 规则ID
    /// </summary>
    public string RuleId { get; set; } = string.Empty;

    /// <summary>
    /// 规则名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 规则描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 规则类型
    /// </summary>
    public ValidationRuleType RuleType { get; set; }

    /// <summary>
    /// 严重程度
    /// </summary>
    public ValidationSeverity Severity { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// 规则表达式
    /// </summary>
    public string Expression { get; set; } = string.Empty;

    /// <summary>
    /// 错误消息
    /// </summary>
    public string ErrorMessage { get; set; } = string.Empty;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 创建者
    /// </summary>
    public string CreatedBy { get; set; } = string.Empty;

    /// <summary>
    /// 规则配置
    /// </summary>
    public Dictionary<string, object> Configuration { get; set; } = new();

    /// <summary>
    /// 支持的节点类型
    /// </summary>
    public HashSet<string> SupportedNodeTypes { get; set; } = new();

    /// <summary>
    /// 规则优先级
    /// </summary>
    public int Priority { get; set; } = 0;

    /// <summary>
    /// 规则版本
    /// </summary>
    public string Version { get; set; } = "1.0.0";

    /// <summary>
    /// 规则标签
    /// </summary>
    public HashSet<string> Tags { get; set; } = new();

    /// <summary>
    /// 规则元数据
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// 验证规则类型
/// </summary>
public enum ValidationRuleType
{
    /// <summary>
    /// 结构验证
    /// </summary>
    Structural,

    /// <summary>
    /// 语法验证
    /// </summary>
    Syntax,

    /// <summary>
    /// 语义验证
    /// </summary>
    Semantic,

    /// <summary>
    /// 性能验证
    /// </summary>
    Performance,

    /// <summary>
    /// 安全验证
    /// </summary>
    Security,

    /// <summary>
    /// 业务规则验证
    /// </summary>
    Business,

    /// <summary>
    /// 自定义验证
    /// </summary>
    Custom
}


