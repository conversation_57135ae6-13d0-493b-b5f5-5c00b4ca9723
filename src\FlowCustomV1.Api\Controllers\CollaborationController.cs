using Microsoft.AspNetCore.Mvc;
using FlowCustomV1.Core.Interfaces.Services;
using FlowCustomV1.Core.Models.Designer;

namespace FlowCustomV1.Api.Controllers;

/// <summary>
/// 协作控制器
/// 提供实时协作会话管理功能
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public class CollaborationController : ControllerBase
{
    private readonly ICollaborationService _collaborationService;
    private readonly ILogger<CollaborationController> _logger;

    /// <summary>
    /// 构造函数
    /// </summary>
    public CollaborationController(
        ICollaborationService collaborationService,
        ILogger<CollaborationController> logger)
    {
        _collaborationService = collaborationService ?? throw new ArgumentNullException(nameof(collaborationService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 创建协作会话
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="sessionInfo">会话信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>会话ID</returns>
    [HttpPost("sessions")]
    [ProducesResponseType(typeof(string), 201)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    public async Task<ActionResult<string>> CreateSessionAsync(
        [FromQuery] string workflowId,
        [FromBody] CollaborationSessionInfo sessionInfo,
        CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(workflowId))
            {
                return BadRequest("Workflow ID is required");
            }

            if (sessionInfo == null)
            {
                return BadRequest("Session info is required");
            }

            var sessionId = await _collaborationService.CreateCollaborationSessionAsync(workflowId, sessionInfo, cancellationToken);
            if (string.IsNullOrEmpty(sessionId))
            {
                return StatusCode(500, "Failed to create collaboration session");
            }

            return Created($"/api/collaboration/sessions/{sessionId}", sessionId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create collaboration session for workflow {WorkflowId}", workflowId);
            return StatusCode(500, $"Failed to create session: {ex.Message}");
        }
    }

    /// <summary>
    /// 获取协作会话信息
    /// </summary>
    /// <param name="sessionId">会话ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>会话信息</returns>
    [HttpGet("sessions/{sessionId}")]
    [ProducesResponseType(typeof(CollaborationSession), 200)]
    [ProducesResponseType(404)]
    [ProducesResponseType(500)]
    public async Task<ActionResult<CollaborationSession>> GetSessionAsync(
        string sessionId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var session = await _collaborationService.GetSessionAsync(sessionId, cancellationToken);
            if (session == null)
            {
                return NotFound($"Session {sessionId} not found");
            }

            return Ok(session);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get session {SessionId}", sessionId);
            return StatusCode(500, $"Failed to get session: {ex.Message}");
        }
    }

    /// <summary>
    /// 加入协作会话
    /// </summary>
    /// <param name="sessionId">会话ID</param>
    /// <param name="collaborator">协作者信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>加入结果</returns>
    [HttpPost("sessions/{sessionId}/join")]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(404)]
    [ProducesResponseType(500)]
    public async Task<ActionResult> JoinSessionAsync(
        string sessionId,
        [FromBody] CollaboratorInfo collaborator,
        CancellationToken cancellationToken = default)
    {
        try
        {
            if (collaborator == null)
            {
                return BadRequest("Collaborator info is required");
            }

            var success = await _collaborationService.JoinSessionAsync(sessionId, collaborator, cancellationToken);
            if (!success)
            {
                return NotFound($"Session {sessionId} not found or join failed");
            }

            return Ok(new { message = "Joined session successfully", sessionId, collaboratorId = collaborator.CollaboratorId });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to join session {SessionId}", sessionId);
            return StatusCode(500, $"Failed to join session: {ex.Message}");
        }
    }

    /// <summary>
    /// 离开协作会话
    /// </summary>
    /// <param name="sessionId">会话ID</param>
    /// <param name="collaboratorId">协作者ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>离开结果</returns>
    [HttpPost("sessions/{sessionId}/leave")]
    [ProducesResponseType(200)]
    [ProducesResponseType(404)]
    [ProducesResponseType(500)]
    public async Task<ActionResult> LeaveSessionAsync(
        string sessionId,
        [FromQuery] string collaboratorId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(collaboratorId))
            {
                return BadRequest("Collaborator ID is required");
            }

            var success = await _collaborationService.LeaveSessionAsync(sessionId, collaboratorId, cancellationToken);
            if (!success)
            {
                return NotFound($"Session {sessionId} not found or leave failed");
            }

            return Ok(new { message = "Left session successfully", sessionId, collaboratorId });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to leave session {SessionId}", sessionId);
            return StatusCode(500, $"Failed to leave session: {ex.Message}");
        }
    }

    /// <summary>
    /// 结束协作会话
    /// </summary>
    /// <param name="sessionId">会话ID</param>
    /// <param name="reason">结束原因</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>结束结果</returns>
    [HttpPost("sessions/{sessionId}/end")]
    [ProducesResponseType(200)]
    [ProducesResponseType(404)]
    [ProducesResponseType(500)]
    public async Task<ActionResult> EndSessionAsync(
        string sessionId,
        [FromQuery] string? reason = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var success = await _collaborationService.EndSessionAsync(sessionId, reason ?? "Manually ended", cancellationToken);
            if (!success)
            {
                return NotFound($"Session {sessionId} not found or end failed");
            }

            return Ok(new { message = "Session ended successfully", sessionId, reason });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to end session {SessionId}", sessionId);
            return StatusCode(500, $"Failed to end session: {ex.Message}");
        }
    }

    /// <summary>
    /// 获取工作流的活跃协作会话
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>活跃会话列表</returns>
    [HttpGet("workflows/{workflowId}/sessions")]
    [ProducesResponseType(typeof(IReadOnlyList<CollaborationSession>), 200)]
    [ProducesResponseType(500)]
    public async Task<ActionResult<IReadOnlyList<CollaborationSession>>> GetActiveSessionsAsync(
        string workflowId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var sessions = await _collaborationService.GetActiveSessionsAsync(workflowId, cancellationToken);
            return Ok(sessions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get active sessions for workflow {WorkflowId}", workflowId);
            return StatusCode(500, $"Failed to get active sessions: {ex.Message}");
        }
    }

    /// <summary>
    /// 广播设计操作
    /// </summary>
    /// <param name="sessionId">会话ID</param>
    /// <param name="operation">设计操作</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>广播结果</returns>
    [HttpPost("sessions/{sessionId}/operations")]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(404)]
    [ProducesResponseType(500)]
    public async Task<ActionResult> BroadcastOperationAsync(
        string sessionId,
        [FromBody] DesignOperation operation,
        CancellationToken cancellationToken = default)
    {
        try
        {
            if (operation == null)
            {
                return BadRequest("Operation is required");
            }

            await _collaborationService.BroadcastOperationAsync(sessionId, operation, cancellationToken);
            return Ok(new { message = "Operation broadcasted successfully", sessionId, operationId = operation.OperationId });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to broadcast operation in session {SessionId}", sessionId);
            return StatusCode(500, $"Failed to broadcast operation: {ex.Message}");
        }
    }

    /// <summary>
    /// 获取协作统计信息
    /// </summary>
    /// <param name="sessionId">会话ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>统计信息</returns>
    [HttpGet("sessions/{sessionId}/statistics")]
    [ProducesResponseType(typeof(CollaborationStatistics), 200)]
    [ProducesResponseType(404)]
    [ProducesResponseType(500)]
    public async Task<ActionResult<CollaborationStatistics>> GetStatisticsAsync(
        string sessionId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var statistics = await _collaborationService.GetStatisticsAsync(sessionId, cancellationToken);
            return Ok(statistics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get statistics for session {SessionId}", sessionId);
            return StatusCode(500, $"Failed to get statistics: {ex.Message}");
        }
    }

    /// <summary>
    /// 获取协作历史
    /// </summary>
    /// <param name="sessionId">会话ID</param>
    /// <param name="collaboratorId">协作者ID过滤</param>
    /// <param name="pageSize">分页大小</param>
    /// <param name="pageNumber">页码</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>协作历史</returns>
    [HttpGet("sessions/{sessionId}/history")]
    [ProducesResponseType(typeof(IReadOnlyList<CollaborationHistoryEntry>), 200)]
    [ProducesResponseType(404)]
    [ProducesResponseType(500)]
    public async Task<ActionResult<IReadOnlyList<CollaborationHistoryEntry>>> GetHistoryAsync(
        string sessionId,
        [FromQuery] string? collaboratorId = null,
        [FromQuery] int pageSize = 50,
        [FromQuery] int pageNumber = 1,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new CollaborationHistoryQuery
            {
                CollaboratorId = collaboratorId,
                PageSize = pageSize,
                PageNumber = pageNumber
            };

            var history = await _collaborationService.GetCollaborationHistoryAsync(sessionId, query, cancellationToken);
            return Ok(history);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get history for session {SessionId}", sessionId);
            return StatusCode(500, $"Failed to get history: {ex.Message}");
        }
    }
}
