using System.Text.Json.Serialization;
using FlowCustomV1.Core.Models.Cluster;

namespace FlowCustomV1.Core.Models.Messages;

/// <summary>
/// 节点注册消息
/// 用于节点加入或离开集群时的通知
/// </summary>
public class NodeRegistrationMessage : ClusterMessage
{
    /// <summary>
    /// 消息类型标识符
    /// </summary>
    public override string MessageType => "node.registration";

    /// <summary>
    /// 节点信息
    /// </summary>
    [JsonPropertyName("nodeInfo")]
    public NodeInfo? NodeInfo { get; set; }

    /// <summary>
    /// 注册类型
    /// </summary>
    [JsonPropertyName("registrationType")]
    public RegistrationType RegistrationType { get; set; } = RegistrationType.Join;

    /// <summary>
    /// 注册原因或描述
    /// </summary>
    [JsonPropertyName("reason")]
    public string Reason { get; set; } = string.Empty;

    /// <summary>
    /// 注册时间戳
    /// </summary>
    [JsonPropertyName("registrationTime")]
    public DateTime RegistrationTime { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 创建节点加入消息
    /// </summary>
    /// <param name="nodeInfo">节点信息</param>
    /// <param name="reason">加入原因</param>
    /// <returns>注册消息</returns>
    public static NodeRegistrationMessage CreateJoinMessage(NodeInfo nodeInfo, string reason = "Node startup")
    {
        return new NodeRegistrationMessage
        {
            MessageId = Guid.NewGuid().ToString(),
            SenderId = nodeInfo.NodeId,
            CreatedAt = DateTime.UtcNow,
            NodeInfo = nodeInfo,
            RegistrationType = RegistrationType.Join,
            Reason = reason,
            RegistrationTime = DateTime.UtcNow
        };
    }

    /// <summary>
    /// 创建节点离开消息
    /// </summary>
    /// <param name="nodeInfo">节点信息</param>
    /// <param name="reason">离开原因</param>
    /// <returns>注册消息</returns>
    public static NodeRegistrationMessage CreateLeaveMessage(NodeInfo nodeInfo, string reason = "Node shutdown")
    {
        return new NodeRegistrationMessage
        {
            MessageId = Guid.NewGuid().ToString(),
            SenderId = nodeInfo.NodeId,
            CreatedAt = DateTime.UtcNow,
            NodeInfo = nodeInfo,
            RegistrationType = RegistrationType.Leave,
            Reason = reason,
            RegistrationTime = DateTime.UtcNow
        };
    }

    /// <summary>
    /// 验证消息有效性
    /// </summary>
    /// <returns>是否有效</returns>
    public override bool IsValid()
    {
        return base.IsValid() &&
               NodeInfo != null &&
               NodeInfo.IsValid() &&
               !string.IsNullOrWhiteSpace(NodeInfo.NodeId) &&
               NodeInfo.NodeId == SenderId;
    }

    /// <summary>
    /// 获取消息摘要
    /// </summary>
    /// <returns>消息摘要</returns>
    public override string ToString()
    {
        return $"NodeRegistration[{RegistrationType}] from {SenderId} ({NodeInfo?.NodeName}) - {Reason}";
    }
}

/// <summary>
/// 节点发现消息
/// 用于节点发现请求和响应
/// </summary>
public class NodeDiscoveryMessage : ClusterMessage
{
    /// <summary>
    /// 消息类型标识符
    /// </summary>
    public override string MessageType => "node.discovery";

    /// <summary>
    /// 发现类型
    /// </summary>
    [JsonPropertyName("discoveryType")]
    public DiscoveryType DiscoveryType { get; set; } = DiscoveryType.Request;

    /// <summary>
    /// 发现查询条件（仅用于请求）
    /// </summary>
    [JsonPropertyName("query")]
    public NodeDiscoveryQuery? Query { get; set; }

    /// <summary>
    /// 发现的节点列表（仅用于响应）
    /// </summary>
    [JsonPropertyName("discoveredNodes")]
    public List<NodeInfo>? DiscoveredNodes { get; set; }

    /// <summary>
    /// 请求ID（用于匹配请求和响应）
    /// </summary>
    [JsonPropertyName("requestId")]
    public string? RequestId { get; set; }

    /// <summary>
    /// 发现范围
    /// </summary>
    [JsonPropertyName("scope")]
    public DiscoveryScope Scope { get; set; } = DiscoveryScope.Cluster;

    /// <summary>
    /// 创建发现请求消息
    /// </summary>
    /// <param name="senderId">发送者ID</param>
    /// <param name="query">查询条件</param>
    /// <param name="scope">发现范围</param>
    /// <returns>发现消息</returns>
    public static NodeDiscoveryMessage CreateRequest(string senderId, NodeDiscoveryQuery? query = null, DiscoveryScope scope = DiscoveryScope.Cluster)
    {
        var requestId = Guid.NewGuid().ToString();
        return new NodeDiscoveryMessage
        {
            MessageId = Guid.NewGuid().ToString(),
            SenderId = senderId,
            CreatedAt = DateTime.UtcNow,
            DiscoveryType = DiscoveryType.Request,
            Query = query,
            RequestId = requestId,
            Scope = scope
        };
    }

    /// <summary>
    /// 创建发现响应消息
    /// </summary>
    /// <param name="senderId">发送者ID</param>
    /// <param name="requestId">请求ID</param>
    /// <param name="discoveredNodes">发现的节点</param>
    /// <returns>发现消息</returns>
    public static NodeDiscoveryMessage CreateResponse(string senderId, string requestId, List<NodeInfo> discoveredNodes)
    {
        return new NodeDiscoveryMessage
        {
            MessageId = Guid.NewGuid().ToString(),
            SenderId = senderId,
            CreatedAt = DateTime.UtcNow,
            DiscoveryType = DiscoveryType.Response,
            RequestId = requestId,
            DiscoveredNodes = discoveredNodes,
            Scope = DiscoveryScope.Cluster
        };
    }

    /// <summary>
    /// 验证消息有效性
    /// </summary>
    /// <returns>是否有效</returns>
    public override bool IsValid()
    {
        return base.IsValid() &&
               (DiscoveryType == DiscoveryType.Request || 
                (DiscoveryType == DiscoveryType.Response && DiscoveredNodes != null));
    }

    /// <summary>
    /// 获取消息摘要
    /// </summary>
    /// <returns>消息摘要</returns>
    public override string ToString()
    {
        var nodeCount = DiscoveredNodes?.Count ?? 0;
        return $"NodeDiscovery[{DiscoveryType}] from {SenderId} - {nodeCount} nodes, Scope: {Scope}";
    }
}

/// <summary>
/// 注册类型
/// </summary>
public enum RegistrationType
{
    /// <summary>
    /// 加入集群
    /// </summary>
    Join = 1,

    /// <summary>
    /// 离开集群
    /// </summary>
    Leave = 2,

    /// <summary>
    /// 更新信息
    /// </summary>
    Update = 3
}

/// <summary>
/// 发现类型
/// </summary>
public enum DiscoveryType
{
    /// <summary>
    /// 发现请求
    /// </summary>
    Request = 1,

    /// <summary>
    /// 发现响应
    /// </summary>
    Response = 2,

    /// <summary>
    /// 广播通知
    /// </summary>
    Broadcast = 3
}

/// <summary>
/// 发现范围
/// </summary>
public enum DiscoveryScope
{
    /// <summary>
    /// 整个集群
    /// </summary>
    Cluster = 1,

    /// <summary>
    /// 本地网络
    /// </summary>
    Local = 2,

    /// <summary>
    /// 指定角色
    /// </summary>
    Role = 3,

    /// <summary>
    /// 指定标签
    /// </summary>
    Tag = 4
}
